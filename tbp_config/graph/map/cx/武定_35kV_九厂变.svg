<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-170" aopId="3949070" id="thSvg" product="E8000V2" version="1.0" viewBox="3082 -1199 2168 1442">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape11">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.974359" x1="16" x2="92" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="68" x2="37" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="68" x2="36" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="107" x2="76" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="107" x2="76" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="22" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="52" y1="22" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="92" x2="92" y1="22" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="load:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="15"/>
    <polyline DF8003:Layer="PUBLIC" points="1,15 10,15 5,25 0,15 1,15 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="15" y2="25"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape33_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape33_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="9" x2="9" y1="33" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape33-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="1" x2="9" y1="31" y2="14"/>
   </symbol>
   <symbol id="switch2:shape33-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="10" stroke-width="0.416609" width="28" x="23" y="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="23" x2="49" y1="30" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="48" x2="18" y1="5" y2="34"/>
    <rect height="9" stroke-width="0.416609" width="29" x="21" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape51_0">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="39" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="97" y1="54" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="100" x2="94" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="98" x2="96" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="103" x2="91" y1="54" y2="54"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
   </symbol>
   <symbol id="transformer2:shape51_1">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="52"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
   </symbol>
   <symbol id="transformer2:shape78_0">
    <circle cx="31" cy="90" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="23" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="65" y2="60"/>
    <circle cx="31" cy="68" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="65" y2="70"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="31" y1="60" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="36" y2="36"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,65 6,65 6,36 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="31,22 25,35 37,35 31,22 31,23 31,22 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="53" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="65" y2="70"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="26" y1="94" y2="94"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="31" y1="94" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="31" y1="94" y2="87"/>
   </symbol>
   <symbol id="transformer2:shape78_1"/>
   <symbol id="voltageTransformer:shape64">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="29" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="6" y1="69" y2="69"/>
    <ellipse cx="8" cy="59" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="67" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="38" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="8" y1="16" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="17" y1="24" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="37" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="10" y1="17" y2="30"/>
   </symbol>
   <symbol id="voltageTransformer:shape90">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="25" y1="18" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="12" x2="9" y1="26" y2="23"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <ellipse cx="21" cy="16" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="9" x2="6" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="12" x2="9" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="9" x2="9" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="9" x2="6" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="9" x2="9" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="21" y1="14" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape89">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.992459" x1="27" x2="28" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.989747" x1="22" x2="21" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.960801" x1="21" x2="28" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="8" y2="11"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="23" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="7" y2="11"/>
    <circle cx="23" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="23" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="27" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="7" y2="11"/>
   </symbol>
   <symbol id="voltageTransformer:shape2">
    <circle cx="7" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="7" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_33d5f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a19d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33d7de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33d8d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33d9cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33da8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33db350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_33dbe10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_33d6f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_33d6f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33df1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33df1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e0f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e0f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_33e1f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e3b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33e4780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33e5540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33e5e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e74a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e81a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e8a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33e9220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33ea300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33eac80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33eb770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33ec130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33ed5b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33ee150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33ef180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33efdc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33fe200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33f1330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_33f2910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_33f3e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1452" width="2178" x="3077" y="-1204"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="4806" x2="4806" y1="-951" y2="-951"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4699" x2="4693" y1="-918" y2="-918"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4699" x2="4693" y1="-909" y2="-909"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5241" x2="5250" y1="-257" y2="-257"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3190" y="-1067"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="24" stroke="rgb(255,255,0)" stroke-width="0.379884" width="14" x="4312" y="-607"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-118809">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.065789 -654.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22130" ObjectName="SW-WD_JIUC.WD_JIUC_3011SW"/>
     <cge:Meas_Ref ObjectId="118809"/>
    <cge:TPSR_Ref TObjectID="22130"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118808">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4141.065789 -351.000000)" xlink:href="#switch2:shape33_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22129" ObjectName="SW-WD_JIUC.WD_JIUC_00160SW"/>
     <cge:Meas_Ref ObjectId="118808"/>
    <cge:TPSR_Ref TObjectID="22129"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118807">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.065789 -245.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22128" ObjectName="SW-WD_JIUC.WD_JIUC_0011SW"/>
     <cge:Meas_Ref ObjectId="118807"/>
    <cge:TPSR_Ref TObjectID="22128"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118826">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4603.597368 -653.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22136" ObjectName="SW-WD_JIUC.WD_JIUC_3021SW"/>
     <cge:Meas_Ref ObjectId="118826"/>
    <cge:TPSR_Ref TObjectID="22136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118825">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4621.597368 -352.000000)" xlink:href="#switch2:shape33_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22135" ObjectName="SW-WD_JIUC.WD_JIUC_0026SW"/>
     <cge:Meas_Ref ObjectId="118825"/>
    <cge:TPSR_Ref TObjectID="22135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118824">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.142857 -0.000000 0.000000 -1.000000 4602.597368 -244.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22134" ObjectName="SW-WD_JIUC.WD_JIUC_0021SW"/>
     <cge:Meas_Ref ObjectId="118824"/>
    <cge:TPSR_Ref TObjectID="22134"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118920">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4310.728684 -650.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22142" ObjectName="SW-WD_JIUC.WD_JIUC_3901SW"/>
     <cge:Meas_Ref ObjectId="118920"/>
    <cge:TPSR_Ref TObjectID="22142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118914">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.065789 -718.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22138" ObjectName="SW-WD_JIUC.WD_JIUC_3411SW"/>
     <cge:Meas_Ref ObjectId="118914"/>
    <cge:TPSR_Ref TObjectID="22138"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118916">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.065789 -880.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22140" ObjectName="SW-WD_JIUC.WD_JIUC_3416SW"/>
     <cge:Meas_Ref ObjectId="118916"/>
    <cge:TPSR_Ref TObjectID="22140"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118917">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4069.065789 -947.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22141" ObjectName="SW-WD_JIUC.WD_JIUC_34167SW"/>
     <cge:Meas_Ref ObjectId="118917"/>
    <cge:TPSR_Ref TObjectID="22141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118941">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4603.597368 -727.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22144" ObjectName="SW-WD_JIUC.WD_JIUC_3421SW"/>
     <cge:Meas_Ref ObjectId="118941"/>
    <cge:TPSR_Ref TObjectID="22144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118942">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4603.597368 -882.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22145" ObjectName="SW-WD_JIUC.WD_JIUC_3426SW"/>
     <cge:Meas_Ref ObjectId="118942"/>
    <cge:TPSR_Ref TObjectID="22145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118943">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4557.597368 -948.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22146" ObjectName="SW-WD_JIUC.WD_JIUC_34267SW"/>
     <cge:Meas_Ref ObjectId="118943"/>
    <cge:TPSR_Ref TObjectID="22146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119036">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4124.000000 -167.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22160" ObjectName="SW-WD_JIUC.WD_JIUC_0441SW"/>
     <cge:Meas_Ref ObjectId="119036"/>
    <cge:TPSR_Ref TObjectID="22160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119037">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4124.000000 -63.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22161" ObjectName="SW-WD_JIUC.WD_JIUC_0442SW"/>
     <cge:Meas_Ref ObjectId="119037"/>
    <cge:TPSR_Ref TObjectID="22161"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118966">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3960.000000 -63.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22149" ObjectName="SW-WD_JIUC.WD_JIUC_0432SW"/>
     <cge:Meas_Ref ObjectId="118966"/>
    <cge:TPSR_Ref TObjectID="22149"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118965">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3960.000000 -165.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22148" ObjectName="SW-WD_JIUC.WD_JIUC_0431SW"/>
     <cge:Meas_Ref ObjectId="118965"/>
    <cge:TPSR_Ref TObjectID="22148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119013">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3790.000000 -62.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22157" ObjectName="SW-WD_JIUC.WD_JIUC_0422SW"/>
     <cge:Meas_Ref ObjectId="119013"/>
    <cge:TPSR_Ref TObjectID="22157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119012">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3790.000000 -165.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22156" ObjectName="SW-WD_JIUC.WD_JIUC_0421SW"/>
     <cge:Meas_Ref ObjectId="119012"/>
    <cge:TPSR_Ref TObjectID="22156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118990">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3621.000000 -165.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22152" ObjectName="SW-WD_JIUC.WD_JIUC_0411SW"/>
     <cge:Meas_Ref ObjectId="118990"/>
    <cge:TPSR_Ref TObjectID="22152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118967">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3960.000000 70.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22150" ObjectName="SW-WD_JIUC.WD_JIUC_0436SW"/>
     <cge:Meas_Ref ObjectId="118967"/>
    <cge:TPSR_Ref TObjectID="22150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119015">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3790.000000 73.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22158" ObjectName="SW-WD_JIUC.WD_JIUC_0426SW"/>
     <cge:Meas_Ref ObjectId="119015"/>
    <cge:TPSR_Ref TObjectID="22158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118991">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3621.000000 -35.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22153" ObjectName="SW-WD_JIUC.WD_JIUC_0416SW"/>
     <cge:Meas_Ref ObjectId="118991"/>
    <cge:TPSR_Ref TObjectID="22153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119062">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -64.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22166" ObjectName="SW-WD_JIUC.WD_JIUC_0452SW"/>
     <cge:Meas_Ref ObjectId="119062"/>
    <cge:TPSR_Ref TObjectID="22166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119061">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -166.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22165" ObjectName="SW-WD_JIUC.WD_JIUC_0451SW"/>
     <cge:Meas_Ref ObjectId="119061"/>
    <cge:TPSR_Ref TObjectID="22165"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119064">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 72.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22167" ObjectName="SW-WD_JIUC.WD_JIUC_0456SW"/>
     <cge:Meas_Ref ObjectId="119064"/>
    <cge:TPSR_Ref TObjectID="22167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119086">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4440.000000 -65.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22170" ObjectName="SW-WD_JIUC.WD_JIUC_0462SW"/>
     <cge:Meas_Ref ObjectId="119086"/>
    <cge:TPSR_Ref TObjectID="22170"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119085">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4440.000000 -165.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22169" ObjectName="SW-WD_JIUC.WD_JIUC_0461SW"/>
     <cge:Meas_Ref ObjectId="119085"/>
    <cge:TPSR_Ref TObjectID="22169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119110">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4612.000000 -65.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22174" ObjectName="SW-WD_JIUC.WD_JIUC_0472SW"/>
     <cge:Meas_Ref ObjectId="119110"/>
    <cge:TPSR_Ref TObjectID="22174"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119109">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4612.000000 -167.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22173" ObjectName="SW-WD_JIUC.WD_JIUC_0471SW"/>
     <cge:Meas_Ref ObjectId="119109"/>
    <cge:TPSR_Ref TObjectID="22173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119135">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4757.000000 -65.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22178" ObjectName="SW-WD_JIUC.WD_JIUC_0482SW"/>
     <cge:Meas_Ref ObjectId="119135"/>
    <cge:TPSR_Ref TObjectID="22178"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119134">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4757.000000 -171.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22177" ObjectName="SW-WD_JIUC.WD_JIUC_0481SW"/>
     <cge:Meas_Ref ObjectId="119134"/>
    <cge:TPSR_Ref TObjectID="22177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119088">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4440.000000 71.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22171" ObjectName="SW-WD_JIUC.WD_JIUC_0466SW"/>
     <cge:Meas_Ref ObjectId="119088"/>
    <cge:TPSR_Ref TObjectID="22171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119111">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4612.000000 71.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22175" ObjectName="SW-WD_JIUC.WD_JIUC_0476SW"/>
     <cge:Meas_Ref ObjectId="119111"/>
    <cge:TPSR_Ref TObjectID="22175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119038">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4942.000000 -167.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22162" ObjectName="SW-WD_JIUC.WD_JIUC_0901SW"/>
     <cge:Meas_Ref ObjectId="119038"/>
    <cge:TPSR_Ref TObjectID="22162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119138">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4757.000000 70.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22179" ObjectName="SW-WD_JIUC.WD_JIUC_0486SW"/>
     <cge:Meas_Ref ObjectId="119138"/>
    <cge:TPSR_Ref TObjectID="22179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119039">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4124.000000 68.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22163" ObjectName="SW-WD_JIUC.WD_JIUC_0446SW"/>
     <cge:Meas_Ref ObjectId="119039"/>
    <cge:TPSR_Ref TObjectID="22163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118915">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4338.000000 -624.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22139" ObjectName="SW-WD_JIUC.WD_JIUC_39017SW"/>
     <cge:Meas_Ref ObjectId="118915"/>
    <cge:TPSR_Ref TObjectID="22139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118993">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5114.000000 -168.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22154" ObjectName="SW-WD_JIUC.WD_JIUC_0902SW"/>
     <cge:Meas_Ref ObjectId="118993"/>
    <cge:TPSR_Ref TObjectID="22154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4649.000000 -999.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235587">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.000000 -628.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39254" ObjectName="SW-WD_JIUC.WD_JIUC_30117SW"/>
     <cge:Meas_Ref ObjectId="235587"/>
    <cge:TPSR_Ref TObjectID="39254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235588">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4633.000000 -624.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39255" ObjectName="SW-WD_JIUC.WD_JIUC_30217SW"/>
     <cge:Meas_Ref ObjectId="235588"/>
    <cge:TPSR_Ref TObjectID="39255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235590">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4151.000000 -853.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39257" ObjectName="SW-WD_JIUC.WD_JIUC_34160SW"/>
     <cge:Meas_Ref ObjectId="235590"/>
    <cge:TPSR_Ref TObjectID="39257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235589">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.000000 -781.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39256" ObjectName="SW-WD_JIUC.WD_JIUC_34117SW"/>
     <cge:Meas_Ref ObjectId="235589"/>
    <cge:TPSR_Ref TObjectID="39256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235591">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4633.000000 -859.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39258" ObjectName="SW-WD_JIUC.WD_JIUC_34260SW"/>
     <cge:Meas_Ref ObjectId="235591"/>
    <cge:TPSR_Ref TObjectID="39258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235592">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4632.000000 -789.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39259" ObjectName="SW-WD_JIUC.WD_JIUC_34217SW"/>
     <cge:Meas_Ref ObjectId="235592"/>
    <cge:TPSR_Ref TObjectID="39259"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-WD_JIUC.WD_JIUC_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4057,-712 4848,-712 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22124" ObjectName="BS-WD_JIUC.WD_JIUC_3IM"/>
    <cge:TPSR_Ref TObjectID="22124"/></metadata>
   <polyline fill="none" opacity="0" points="4057,-712 4848,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-WD_JIUC.WD_JIUC_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3430,-228 5222,-228 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22125" ObjectName="BS-WD_JIUC.WD_JIUC_9IM"/>
    <cge:TPSR_Ref TObjectID="22125"/></metadata>
   <polyline fill="none" opacity="0" points="3430,-228 5222,-228 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-WD_JIUC.WD_JIUC_1C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3578.000000 175.000000)" xlink:href="#capacitor:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22571" ObjectName="CB-WD_JIUC.WD_JIUC_1C"/>
    <cge:TPSR_Ref TObjectID="22571"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-WD_JIUC.WD_JIUC_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="31119"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4093.065789 -470.000000)" xlink:href="#transformer2:shape51_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4093.065789 -470.000000)" xlink:href="#transformer2:shape51_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="22180" ObjectName="TF-WD_JIUC.WD_JIUC_1T"/>
    <cge:TPSR_Ref TObjectID="22180"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-WD_JIUC.WD_JIUC_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="31123"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4574.597368 -470.000000)" xlink:href="#transformer2:shape51_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4574.597368 -470.000000)" xlink:href="#transformer2:shape51_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="22181" ObjectName="TF-WD_JIUC.WD_JIUC_2T"/>
    <cge:TPSR_Ref TObjectID="22181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4761.000000 -826.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4761.000000 -826.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 5155.000000 49.000000)" xlink:href="#transformer2:shape78_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 5155.000000 49.000000)" xlink:href="#transformer2:shape78_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2a60ca0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4127.065789 -402.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37fd7e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3625.000000 66.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3385be0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4285.000000 -559.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_338f410">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 -67.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_339fcc0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4065.000000 -1003.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33a4940">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4777.000000 -958.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33a56b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.000000 -1005.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33a8de0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4133.000000 -339.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33aab00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4614.000000 -340.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33ac820">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3764.000000 22.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33ad7b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3789.000000 22.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33af1e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3764.000000 147.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33b1120">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3934.000000 147.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33b1e50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3934.000000 22.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33b2de0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3959.000000 22.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_342f2e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.000000 144.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_342fef0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.000000 19.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3430e80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.000000 19.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3433860">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4267.000000 27.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34347f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4293.000000 27.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34354d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4267.000000 152.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3438160">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4414.000000 148.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3438e90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4414.000000 23.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3439e20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4439.000000 23.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_343c5a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4586.000000 147.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_343d2d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4586.000000 22.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_343e260">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4611.000000 22.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3440c40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4731.000000 20.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3441bd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4756.000000 20.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34428b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4731.000000 145.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3445540">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4916.000000 -54.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3446fc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3655.000000 -38.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3449c50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4607.597368 -403.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3246.000000 -1064.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-145368" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3200.538462 -841.966362) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145368" ObjectName="WD_JIUC:WD_JIUC_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-145369" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3200.538462 -808.966362) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145369" ObjectName="WD_JIUC:WD_JIUC_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-121140" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4252.000000 -481.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121140" ObjectName="WD_JIUC:WD_JIUC_1T_Tp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-118662" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4251.000000 -454.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118662" ObjectName="WD_JIUC:WD_JIUC_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-121142" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4777.000000 -477.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121142" ObjectName="WD_JIUC:WD_JIUC_2T_Tp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-118661" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4778.000000 -451.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118661" ObjectName="WD_JIUC:WD_JIUC_2T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-118613" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3179.538462 -972.966362) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118613" ObjectName="WD_JIUC:WD_JIUC_3IM_F"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-145368" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3197.538462 -926.966362) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145368" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-145368" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3198.538462 -884.966362) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145368" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118673" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.781083 -0.000000 -0.000000 0.750000 4046.393492 -847.500000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118673" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22137"/>
     <cge:Term_Ref ObjectID="31031"/>
    <cge:TPSR_Ref TObjectID="22137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118674" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.781083 -0.000000 -0.000000 0.750000 4046.393492 -847.500000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118674" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22137"/>
     <cge:Term_Ref ObjectID="31031"/>
    <cge:TPSR_Ref TObjectID="22137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118670" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.781083 -0.000000 -0.000000 0.750000 4046.393492 -847.500000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118670" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22137"/>
     <cge:Term_Ref ObjectID="31031"/>
    <cge:TPSR_Ref TObjectID="22137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118686" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.766763 -0.000000 -0.000000 0.750000 4523.981402 -854.500000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118686" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22143"/>
     <cge:Term_Ref ObjectID="31043"/>
    <cge:TPSR_Ref TObjectID="22143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118687" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.766763 -0.000000 -0.000000 0.750000 4523.981402 -854.500000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118687" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22143"/>
     <cge:Term_Ref ObjectID="31043"/>
    <cge:TPSR_Ref TObjectID="22143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118683" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.766763 -0.000000 -0.000000 0.750000 4523.981402 -854.500000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118683" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22143"/>
     <cge:Term_Ref ObjectID="31043"/>
    <cge:TPSR_Ref TObjectID="22143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118712" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.750000 3615.500000 197.500000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118712" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22151"/>
     <cge:Term_Ref ObjectID="31059"/>
    <cge:TPSR_Ref TObjectID="22151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118713" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.750000 3615.500000 197.500000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118713" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22151"/>
     <cge:Term_Ref ObjectID="31059"/>
    <cge:TPSR_Ref TObjectID="22151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118709" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.750000 3615.500000 197.500000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118709" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22151"/>
     <cge:Term_Ref ObjectID="31059"/>
    <cge:TPSR_Ref TObjectID="22151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118726" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.754308 3785.500000 197.370763) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118726" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22155"/>
     <cge:Term_Ref ObjectID="31067"/>
    <cge:TPSR_Ref TObjectID="22155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118727" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.754308 3785.500000 197.370763) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118727" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22155"/>
     <cge:Term_Ref ObjectID="31067"/>
    <cge:TPSR_Ref TObjectID="22155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118723" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.754308 3785.500000 197.370763) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118723" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22155"/>
     <cge:Term_Ref ObjectID="31067"/>
    <cge:TPSR_Ref TObjectID="22155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118699" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.754308 3957.500000 197.370763) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118699" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22147"/>
     <cge:Term_Ref ObjectID="31051"/>
    <cge:TPSR_Ref TObjectID="22147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118700" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.754308 3957.500000 197.370763) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118700" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22147"/>
     <cge:Term_Ref ObjectID="31051"/>
    <cge:TPSR_Ref TObjectID="22147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118696" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.754308 3957.500000 197.370763) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118696" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22147"/>
     <cge:Term_Ref ObjectID="31051"/>
    <cge:TPSR_Ref TObjectID="22147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118738" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.754308 4120.500000 197.370763) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118738" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22159"/>
     <cge:Term_Ref ObjectID="31075"/>
    <cge:TPSR_Ref TObjectID="22159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118739" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.754308 4120.500000 197.370763) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118739" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22159"/>
     <cge:Term_Ref ObjectID="31075"/>
    <cge:TPSR_Ref TObjectID="22159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118735" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.754308 4120.500000 197.370763) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118735" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22159"/>
     <cge:Term_Ref ObjectID="31075"/>
    <cge:TPSR_Ref TObjectID="22159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118751" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.754308 4292.500000 197.370763) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118751" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22164"/>
     <cge:Term_Ref ObjectID="31085"/>
    <cge:TPSR_Ref TObjectID="22164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118752" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.754308 4292.500000 197.370763) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118752" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22164"/>
     <cge:Term_Ref ObjectID="31085"/>
    <cge:TPSR_Ref TObjectID="22164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118748" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.754308 4292.500000 197.370763) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118748" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22164"/>
     <cge:Term_Ref ObjectID="31085"/>
    <cge:TPSR_Ref TObjectID="22164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118764" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.754308 4435.500000 197.370763) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118764" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22168"/>
     <cge:Term_Ref ObjectID="31093"/>
    <cge:TPSR_Ref TObjectID="22168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118765" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.754308 4435.500000 197.370763) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118765" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22168"/>
     <cge:Term_Ref ObjectID="31093"/>
    <cge:TPSR_Ref TObjectID="22168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118761" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.754308 4435.500000 197.370763) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118761" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22168"/>
     <cge:Term_Ref ObjectID="31093"/>
    <cge:TPSR_Ref TObjectID="22168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118777" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.754308 4611.500000 197.370763) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118777" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22172"/>
     <cge:Term_Ref ObjectID="31101"/>
    <cge:TPSR_Ref TObjectID="22172"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118778" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.754308 4611.500000 197.370763) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118778" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22172"/>
     <cge:Term_Ref ObjectID="31101"/>
    <cge:TPSR_Ref TObjectID="22172"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118774" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.754308 4611.500000 197.370763) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118774" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22172"/>
     <cge:Term_Ref ObjectID="31101"/>
    <cge:TPSR_Ref TObjectID="22172"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118789" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.754308 4755.500000 197.370763) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118789" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22176"/>
     <cge:Term_Ref ObjectID="31109"/>
    <cge:TPSR_Ref TObjectID="22176"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118790" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.754308 4755.500000 197.370763) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118790" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22176"/>
     <cge:Term_Ref ObjectID="31109"/>
    <cge:TPSR_Ref TObjectID="22176"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118787" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.820513 -0.000000 -0.000000 0.754308 4755.500000 197.370763) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118787" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22176"/>
     <cge:Term_Ref ObjectID="31109"/>
    <cge:TPSR_Ref TObjectID="22176"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118627" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.800000 -0.000000 -0.000000 0.740260 4068.879162 -337.203947) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118627" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22127"/>
     <cge:Term_Ref ObjectID="31011"/>
    <cge:TPSR_Ref TObjectID="22127"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118628" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.800000 -0.000000 -0.000000 0.740260 4068.879162 -337.203947) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118628" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22127"/>
     <cge:Term_Ref ObjectID="31011"/>
    <cge:TPSR_Ref TObjectID="22127"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118624" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.800000 -0.000000 -0.000000 0.740260 4068.879162 -337.203947) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118624" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22127"/>
     <cge:Term_Ref ObjectID="31011"/>
    <cge:TPSR_Ref TObjectID="22127"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119167" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.816327 -0.000000 -0.000000 0.740132 4552.579484 -336.203947) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119167" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22133"/>
     <cge:Term_Ref ObjectID="31023"/>
    <cge:TPSR_Ref TObjectID="22133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119168" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.816327 -0.000000 -0.000000 0.740132 4552.579484 -336.203947) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119168" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22133"/>
     <cge:Term_Ref ObjectID="31023"/>
    <cge:TPSR_Ref TObjectID="22133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118644" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(0.816327 -0.000000 -0.000000 0.740132 4552.579484 -336.203947) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118644" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22133"/>
     <cge:Term_Ref ObjectID="31023"/>
    <cge:TPSR_Ref TObjectID="22133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-120774" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3525.000000 -362.500000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120774" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22125"/>
     <cge:Term_Ref ObjectID="31008"/>
    <cge:TPSR_Ref TObjectID="22125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-120775" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3525.000000 -362.500000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120775" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22125"/>
     <cge:Term_Ref ObjectID="31008"/>
    <cge:TPSR_Ref TObjectID="22125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-120776" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3525.000000 -362.500000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120776" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22125"/>
     <cge:Term_Ref ObjectID="31008"/>
    <cge:TPSR_Ref TObjectID="22125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-118630" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3525.000000 -362.500000) translate(0,76)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118630" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22125"/>
     <cge:Term_Ref ObjectID="31008"/>
    <cge:TPSR_Ref TObjectID="22125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-120772" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3525.000000 -362.500000) translate(0,96)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120772" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22125"/>
     <cge:Term_Ref ObjectID="31008"/>
    <cge:TPSR_Ref TObjectID="22125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-118623" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3525.000000 -362.500000) translate(0,116)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118623" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22125"/>
     <cge:Term_Ref ObjectID="31008"/>
    <cge:TPSR_Ref TObjectID="22125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118617" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.901761 -0.000000 -0.000000 0.833333 4068.719178 -622.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118617" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22126"/>
     <cge:Term_Ref ObjectID="31009"/>
    <cge:TPSR_Ref TObjectID="22126"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118618" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.901761 -0.000000 -0.000000 0.833333 4068.719178 -622.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118618" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22126"/>
     <cge:Term_Ref ObjectID="31009"/>
    <cge:TPSR_Ref TObjectID="22126"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118614" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.901761 -0.000000 -0.000000 0.833333 4068.719178 -622.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118614" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22126"/>
     <cge:Term_Ref ObjectID="31009"/>
    <cge:TPSR_Ref TObjectID="22126"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-118619" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.901761 -0.000000 -0.000000 0.833333 4068.719178 -622.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118619" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22126"/>
     <cge:Term_Ref ObjectID="31009"/>
    <cge:TPSR_Ref TObjectID="22126"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118637" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.914286 -0.000000 -0.000000 0.833333 4552.500000 -622.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118637" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22131"/>
     <cge:Term_Ref ObjectID="31019"/>
    <cge:TPSR_Ref TObjectID="22131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118638" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.914286 -0.000000 -0.000000 0.833333 4552.500000 -622.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118638" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22131"/>
     <cge:Term_Ref ObjectID="31019"/>
    <cge:TPSR_Ref TObjectID="22131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118634" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.914286 -0.000000 -0.000000 0.833333 4552.500000 -622.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118634" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22131"/>
     <cge:Term_Ref ObjectID="31019"/>
    <cge:TPSR_Ref TObjectID="22131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-118639" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.914286 -0.000000 -0.000000 0.833333 4552.500000 -622.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118639" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22131"/>
     <cge:Term_Ref ObjectID="31019"/>
    <cge:TPSR_Ref TObjectID="22131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-120794" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.114286 -0.000000 -0.000000 1.120370 4984.000000 -843.500000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120794" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22124"/>
     <cge:Term_Ref ObjectID="31007"/>
    <cge:TPSR_Ref TObjectID="22124"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-120795" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.114286 -0.000000 -0.000000 1.120370 4984.000000 -843.500000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120795" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22124"/>
     <cge:Term_Ref ObjectID="31007"/>
    <cge:TPSR_Ref TObjectID="22124"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-120796" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.114286 -0.000000 -0.000000 1.120370 4984.000000 -843.500000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120796" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22124"/>
     <cge:Term_Ref ObjectID="31007"/>
    <cge:TPSR_Ref TObjectID="22124"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-118622" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.114286 -0.000000 -0.000000 1.120370 4984.000000 -843.500000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118622" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22124"/>
     <cge:Term_Ref ObjectID="31007"/>
    <cge:TPSR_Ref TObjectID="22124"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-118620" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.114286 -0.000000 -0.000000 1.120370 4984.000000 -843.500000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118620" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22124"/>
     <cge:Term_Ref ObjectID="31007"/>
    <cge:TPSR_Ref TObjectID="22124"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-118613" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.114286 -0.000000 -0.000000 1.120370 4984.000000 -843.500000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118613" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22124"/>
     <cge:Term_Ref ObjectID="31007"/>
    <cge:TPSR_Ref TObjectID="22124"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="64" qtmmishow="hidden" width="222" x="3235" y="-1153"/>
    </a>
   <metadata/><rect fill="white" height="64" opacity="0" stroke="white" transform="" width="222" x="3235" y="-1153"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="109" qtmmishow="hidden" width="132" x="3130" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="109" opacity="0" stroke="white" transform="" width="132" x="3130" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="30" x="3579" y="-143"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="30" x="3579" y="-143"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="3749" y="-153"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="3749" y="-153"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="3915" y="-156"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="3915" y="-156"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="4082" y="-158"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="4082" y="-158"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="34" x="4255" y="-157"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="34" x="4255" y="-157"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="4396" y="-151"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="4396" y="-151"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="34" x="4567" y="-155"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="34" x="4567" y="-155"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="4715" y="-161"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="4715" y="-161"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="71" x="4202" y="-514"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="71" x="4202" y="-514"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="4697" y="-543"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="4697" y="-543"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4091" y="-832"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4091" y="-832"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4571" y="-840"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4571" y="-840"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="28" qtmmishow="hidden" width="95" x="3122" y="-707"/>
    </a>
   <metadata/><rect fill="white" height="28" opacity="0" stroke="white" transform="" width="95" x="3122" y="-707"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3485" y="-1125"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3485" y="-1125"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3485" y="-1160"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3485" y="-1160"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="64" qtmmishow="hidden" width="222" x="3235" y="-1153"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="109" qtmmishow="hidden" width="132" x="3130" y="-1194"/></g>
   <g href="35kV九厂变WD_JIUC_041间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="30" x="3579" y="-143"/></g>
   <g href="35kV九厂变WD_JIUC_042间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="3749" y="-153"/></g>
   <g href="35kV九厂变WD_JIUC_043间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="3915" y="-156"/></g>
   <g href="35kV九厂变WD_JIUC_044间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="4082" y="-158"/></g>
   <g href="35kV九厂变WD_JIUC_045间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="34" x="4255" y="-157"/></g>
   <g href="35kV九厂变WD_JIUC_046间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="4396" y="-151"/></g>
   <g href="35kV九厂变WD_JIUC_047间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="34" x="4567" y="-155"/></g>
   <g href="35kV九厂变WD_JIUC_048间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="4715" y="-161"/></g>
   <g href="35kV九厂变九厂变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="71" x="4202" y="-514"/></g>
   <g href="35kV九厂变九厂变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="4697" y="-543"/></g>
   <g href="35kV九厂变WD_JIUC_341间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4091" y="-832"/></g>
   <g href="35kV九厂变WD_JIUC_342间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4571" y="-840"/></g>
   <g href="35kV九厂变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="28" qtmmishow="hidden" width="95" x="3122" y="-707"/></g>
   <g href="cx_配调_配网接线图35_武定.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3485" y="-1125"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3485" y="-1160"/></g>
  </g><g id="Rectangle_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="0.848485" width="14" x="4944" y="-89"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="0.848485" width="14" x="5116" y="-153"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3820c90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4167.065789 -987.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37fe460">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4942.000000 -14.000000)" xlink:href="#voltageTransformer:shape90"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3806af0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4310.728684 -532.000000)" xlink:href="#voltageTransformer:shape89"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33a83c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4689.000000 -904.000000)" xlink:href="#voltageTransformer:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(-0.707107 0.707107 0.707107 0.707107 4190.989592 -963.874369)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_JIUC.044Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4128.000000 184.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33605" ObjectName="EC-WD_JIUC.044Ld"/>
    <cge:TPSR_Ref TObjectID="33605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_JIUC.043Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3964.000000 182.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33604" ObjectName="EC-WD_JIUC.043Ld"/>
    <cge:TPSR_Ref TObjectID="33604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_JIUC.042Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3794.000000 184.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33603" ObjectName="EC-WD_JIUC.042Ld"/>
    <cge:TPSR_Ref TObjectID="33603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_JIUC.045Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4298.000000 155.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33606" ObjectName="EC-WD_JIUC.045Ld"/>
    <cge:TPSR_Ref TObjectID="33606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_JIUC.047Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4616.000000 155.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33608" ObjectName="EC-WD_JIUC.047Ld"/>
    <cge:TPSR_Ref TObjectID="33608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_JIUC.046Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4444.000000 155.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33607" ObjectName="EC-WD_JIUC.046Ld"/>
    <cge:TPSR_Ref TObjectID="33607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_JIUC.048Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4761.000000 156.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33609" ObjectName="EC-WD_JIUC.048Ld"/>
    <cge:TPSR_Ref TObjectID="33609"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_32f3050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-712 4132,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22124@0" ObjectIDZND0="22130@1" Pin0InfoVect0LinkObjId="SW-118809_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-712 4132,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37e7ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-475 4132,-460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="22180@1" ObjectIDZND0="g_2a60ca0@1" Pin0InfoVect0LinkObjId="g_2a60ca0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37fb9e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-475 4132,-460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37e9340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-407 4132,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2a60ca0@0" ObjectIDZND0="22129@0" Pin0InfoVect0LinkObjId="SW-118808_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a60ca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-407 4132,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32ec2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-302 4132,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22127@0" ObjectIDZND0="22128@1" Pin0InfoVect0LinkObjId="SW-118807_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118805_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-302 4132,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32ebda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-250 4132,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22128@0" ObjectIDZND0="22125@0" Pin0InfoVect0LinkObjId="g_32fa620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118807_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-250 4132,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a4a7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-579 4613,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="22131@0" ObjectIDZND0="22181@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118819_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-579 4613,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32fa3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-303 4613,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22133@0" ObjectIDZND0="22134@1" Pin0InfoVect0LinkObjId="SW-118824_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118822_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-303 4613,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32fa620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-249 4613,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22134@0" ObjectIDZND0="22125@0" Pin0InfoVect0LinkObjId="g_32ebda0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118824_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-249 4613,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29b9a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4319,-712 4319,-691 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22124@0" ObjectIDZND0="22142@1" Pin0InfoVect0LinkObjId="SW-118920_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4319,-712 4319,-691 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c54f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-712 4132,-723 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22124@0" ObjectIDZND0="22138@0" Pin0InfoVect0LinkObjId="SW-118914_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-712 4132,-723 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33bde10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-943 4599,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="22145@x" ObjectIDND1="0@x" ObjectIDND2="g_33a56b0@0" ObjectIDZND0="22146@1" Pin0InfoVect0LinkObjId="SW-118943_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-118942_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_33a56b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-943 4599,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33be070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4563,-943 4544,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22146@0" ObjectIDZND0="g_33be2d0@0" Pin0InfoVect0LinkObjId="g_33be2d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118943_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4563,-943 4544,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33bed20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-923 4613,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="22145@1" ObjectIDZND0="22146@x" ObjectIDZND1="0@x" ObjectIDZND2="g_33a56b0@0" Pin0InfoVect0LinkObjId="SW-118943_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_33a56b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118942_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-923 4613,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33bef80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4612,-962 4696,-962 4696,-924 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="22146@x" ObjectIDND1="22145@x" ObjectIDND2="0@x" ObjectIDZND0="g_33a83c0@0" Pin0InfoVect0LinkObjId="g_33a83c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-118943_0" Pin1InfoVect1LinkObjId="SW-118942_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4612,-962 4696,-962 4696,-924 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_382d960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-228 4133,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22125@0" ObjectIDZND0="22160@1" Pin0InfoVect0LinkObjId="SW-119036_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32ebda0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-228 4133,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_382fc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-172 4133,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22160@0" ObjectIDZND0="22159@1" Pin0InfoVect0LinkObjId="SW-119034_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119036_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-172 4133,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3832660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-131 4133,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22159@0" ObjectIDZND0="22161@1" Pin0InfoVect0LinkObjId="SW-119037_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119034_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-131 4133,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3311470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3969,-170 3969,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22148@0" ObjectIDZND0="22147@1" Pin0InfoVect0LinkObjId="SW-118963_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118965_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3969,-170 3969,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3313ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3969,-129 3969,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22147@0" ObjectIDZND0="22149@1" Pin0InfoVect0LinkObjId="SW-118966_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118963_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3969,-129 3969,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3314130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3969,-228 3969,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22125@0" ObjectIDZND0="22148@1" Pin0InfoVect0LinkObjId="SW-118965_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32ebda0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3969,-228 3969,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3317800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,-170 3799,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22156@0" ObjectIDZND0="22155@1" Pin0InfoVect0LinkObjId="SW-119010_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119012_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3799,-170 3799,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3302400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,-129 3799,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22155@0" ObjectIDZND0="22157@1" Pin0InfoVect0LinkObjId="SW-119013_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3799,-129 3799,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3304e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,-228 3799,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22125@0" ObjectIDZND0="22156@1" Pin0InfoVect0LinkObjId="SW-119012_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32ebda0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3799,-228 3799,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_330a600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3630,-228 3630,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22125@0" ObjectIDZND0="22152@1" Pin0InfoVect0LinkObjId="SW-118990_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32ebda0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3630,-228 3630,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_330c930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3630,-170 3630,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22152@0" ObjectIDZND0="22151@1" Pin0InfoVect0LinkObjId="SW-118988_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3630,-170 3630,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34085f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,-228 4303,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22125@0" ObjectIDZND0="22165@1" Pin0InfoVect0LinkObjId="SW-119061_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32ebda0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4303,-228 4303,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3408850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,-171 4303,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22165@0" ObjectIDZND0="22164@1" Pin0InfoVect0LinkObjId="SW-119059_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119061_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4303,-171 4303,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_340b2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,-130 4303,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22164@0" ObjectIDZND0="22166@1" Pin0InfoVect0LinkObjId="SW-119062_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119059_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4303,-130 4303,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3413210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,-170 4449,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22169@0" ObjectIDZND0="22168@1" Pin0InfoVect0LinkObjId="SW-119083_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119085_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4449,-170 4449,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3415c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,-128 4449,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22168@0" ObjectIDZND0="22170@1" Pin0InfoVect0LinkObjId="SW-119086_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119083_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4449,-128 4449,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34186d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,-228 4449,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22125@0" ObjectIDZND0="22169@1" Pin0InfoVect0LinkObjId="SW-119085_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32ebda0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4449,-228 4449,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3418930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-172 4621,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22173@0" ObjectIDZND0="22172@1" Pin0InfoVect0LinkObjId="SW-119107_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119109_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-172 4621,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_341b390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-131 4621,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22172@0" ObjectIDZND0="22174@1" Pin0InfoVect0LinkObjId="SW-119110_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119107_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-131 4621,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_341ddf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-228 4621,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22125@0" ObjectIDZND0="22173@1" Pin0InfoVect0LinkObjId="SW-119109_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32ebda0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-228 4621,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3422920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4766,-135 4766,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22176@0" ObjectIDZND0="22178@1" Pin0InfoVect0LinkObjId="SW-119135_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119132_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4766,-135 4766,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3425380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4766,-228 4766,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22125@0" ObjectIDZND0="22177@1" Pin0InfoVect0LinkObjId="SW-119134_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32ebda0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4766,-228 4766,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34276b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4766,-176 4766,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22177@0" ObjectIDZND0="22176@1" Pin0InfoVect0LinkObjId="SW-119132_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119134_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4766,-176 4766,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37f0850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4951,-228 4951,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22125@0" ObjectIDZND0="22162@1" Pin0InfoVect0LinkObjId="SW-119038_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32ebda0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4951,-228 4951,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37fb9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-579 4132,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="22126@0" ObjectIDZND0="22180@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119632_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-579 4132,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37fbbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4175,-982 4175,-997 4132,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3820c90@0" ObjectIDZND0="22141@x" ObjectIDZND1="22140@x" ObjectIDZND2="g_339fcc0@0" Pin0InfoVect0LinkObjId="SW-118917_0" Pin0InfoVect1LinkObjId="SW-118916_0" Pin0InfoVect2LinkObjId="g_339fcc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3820c90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4175,-982 4175,-997 4132,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37fe200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3630,60 3630,138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_37fd7e0@0" ObjectIDZND0="22571@0" Pin0InfoVect0LinkObjId="CB-WD_JIUC.WD_JIUC_1C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37fd7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3630,60 3630,138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38107f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4059,-942 4074,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2989cb0@0" ObjectIDZND0="22141@0" Pin0InfoVect0LinkObjId="SW-118917_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2989cb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4059,-942 4074,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38109e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-942 4132,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="22141@1" ObjectIDZND0="g_3820c90@0" ObjectIDZND1="g_339fcc0@0" ObjectIDZND2="37807@1" Pin0InfoVect0LinkObjId="g_3820c90_0" Pin0InfoVect1LinkObjId="g_339fcc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118917_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-942 4132,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3810bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-942 4132,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="22141@x" ObjectIDND1="22140@x" ObjectIDZND0="g_3820c90@0" ObjectIDZND1="g_339fcc0@0" ObjectIDZND2="37807@1" Pin0InfoVect0LinkObjId="g_3820c90_0" Pin0InfoVect1LinkObjId="g_339fcc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118917_0" Pin1InfoVect1LinkObjId="SW-118916_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-942 4132,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3810e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-921 4132,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="22140@1" ObjectIDZND0="g_3820c90@0" ObjectIDZND1="g_339fcc0@0" ObjectIDZND2="37807@1" Pin0InfoVect0LinkObjId="g_3820c90_0" Pin0InfoVect1LinkObjId="g_339fcc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118916_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-921 4132,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_337ee00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3630,-40 3630,8 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="22153@0" ObjectIDZND0="g_37fd7e0@1" Pin0InfoVect0LinkObjId="g_37fd7e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118991_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3630,-40 3630,8 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33868f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4292,-613 4292,-629 4319,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="g_3385be0@0" ObjectIDZND0="22142@x" ObjectIDZND1="g_3806af0@0" ObjectIDZND2="22139@x" Pin0InfoVect0LinkObjId="SW-118920_0" Pin0InfoVect1LinkObjId="g_3806af0_0" Pin0InfoVect2LinkObjId="SW-118915_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3385be0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4292,-613 4292,-629 4319,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3387600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4319,-655 4319,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="22142@0" ObjectIDZND0="g_3385be0@0" ObjectIDZND1="g_3806af0@0" ObjectIDZND2="22139@x" Pin0InfoVect0LinkObjId="g_3385be0_0" Pin0InfoVect1LinkObjId="g_3806af0_0" Pin0InfoVect2LinkObjId="SW-118915_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4319,-655 4319,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3387860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4319,-629 4319,-563 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_3385be0@0" ObjectIDND1="22142@x" ObjectIDND2="22139@x" ObjectIDZND0="g_3806af0@0" Pin0InfoVect0LinkObjId="g_3806af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3385be0_0" Pin1InfoVect1LinkObjId="SW-118920_0" Pin1InfoVect2LinkObjId="SW-118915_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4319,-629 4319,-563 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_338aa80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4319,-629 4343,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_3385be0@0" ObjectIDND1="22142@x" ObjectIDND2="g_3806af0@0" ObjectIDZND0="22139@0" Pin0InfoVect0LinkObjId="SW-118915_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3385be0_0" Pin1InfoVect1LinkObjId="SW-118920_0" Pin1InfoVect2LinkObjId="g_3806af0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4319,-629 4343,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_338ace0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4379,-629 4401,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22139@1" ObjectIDZND0="g_3387ac0@0" Pin0InfoVect0LinkObjId="g_3387ac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118915_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4379,-629 4401,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_338dd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-228 5123,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22125@0" ObjectIDZND0="22154@1" Pin0InfoVect0LinkObjId="SW-118993_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32ebda0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-228 5123,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_338fcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-173 5123,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="22154@0" ObjectIDZND0="g_338f410@1" Pin0InfoVect0LinkObjId="g_338f410_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118993_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-173 5123,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_338ff10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-72 5123,-55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_338f410@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_338f410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-72 5123,-55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_339cd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-962 4613,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_33a56b0@0" ObjectIDND2="19220@1" ObjectIDZND0="22146@x" ObjectIDZND1="22145@x" Pin0InfoVect0LinkObjId="SW-118943_0" Pin0InfoVect1LinkObjId="SW-118942_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_33a56b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-962 4613,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_339d810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-1004 4613,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="0@x" ObjectIDND1="g_33a56b0@0" ObjectIDND2="19220@1" ObjectIDZND0="22146@x" ObjectIDZND1="22145@x" ObjectIDZND2="g_33a83c0@0" Pin0InfoVect0LinkObjId="SW-118943_0" Pin0InfoVect1LinkObjId="SW-118942_0" Pin0InfoVect2LinkObjId="g_33a83c0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_33a56b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-1004 4613,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a0660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4122,-1010 4132,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_339fcc0@0" ObjectIDZND0="g_3820c90@0" ObjectIDZND1="22141@x" ObjectIDZND2="22140@x" Pin0InfoVect0LinkObjId="g_3820c90_0" Pin0InfoVect1LinkObjId="SW-118917_0" Pin0InfoVect2LinkObjId="SW-118916_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_339fcc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4122,-1010 4132,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a1150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-1098 4132,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="37807@1" ObjectIDZND0="g_339fcc0@0" ObjectIDZND1="g_3820c90@0" ObjectIDZND2="22141@x" Pin0InfoVect0LinkObjId="g_339fcc0_0" Pin0InfoVect1LinkObjId="g_3820c90_0" Pin0InfoVect2LinkObjId="SW-118917_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-1098 4132,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a13b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-1010 4132,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_339fcc0@0" ObjectIDND1="37807@1" ObjectIDZND0="g_3820c90@0" ObjectIDZND1="22141@x" ObjectIDZND2="22140@x" Pin0InfoVect0LinkObjId="g_3820c90_0" Pin0InfoVect1LinkObjId="SW-118917_0" Pin0InfoVect2LinkObjId="SW-118916_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_339fcc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-1010 4132,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a46e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4654,-1004 4613,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="22146@x" ObjectIDZND1="22145@x" ObjectIDZND2="g_33a83c0@0" Pin0InfoVect0LinkObjId="SW-118943_0" Pin0InfoVect1LinkObjId="SW-118942_0" Pin0InfoVect2LinkObjId="g_33a83c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4654,-1004 4613,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a6460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4603,-1012 4613,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_33a56b0@0" ObjectIDZND0="22146@x" ObjectIDZND1="22145@x" ObjectIDZND2="g_33a83c0@0" Pin0InfoVect0LinkObjId="SW-118943_0" Pin0InfoVect1LinkObjId="SW-118942_0" Pin0InfoVect2LinkObjId="g_33a83c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33a56b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4603,-1012 4613,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a6f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-1100 4613,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="19220@1" ObjectIDZND0="g_33a56b0@0" ObjectIDZND1="22146@x" ObjectIDZND2="22145@x" Pin0InfoVect0LinkObjId="g_33a56b0_0" Pin0InfoVect1LinkObjId="SW-118943_0" Pin0InfoVect2LinkObjId="SW-118942_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-1100 4613,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a71b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-1012 4613,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_33a56b0@0" ObjectIDND1="19220@1" ObjectIDZND0="22146@x" ObjectIDZND1="22145@x" ObjectIDZND2="g_33a83c0@0" Pin0InfoVect0LinkObjId="SW-118943_0" Pin0InfoVect1LinkObjId="SW-118942_0" Pin0InfoVect2LinkObjId="g_33a83c0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33a56b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-1012 4613,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33a7410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-966 4781,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_33a4940@0" Pin0InfoVect0LinkObjId="g_33a4940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-966 4781,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33a7f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-919 4776,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="g_33a4940@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_33a4940_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-919 4776,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33a8160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-966 4776,-1004 4706,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_33a4940@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33a4940_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-966 4776,-1004 4706,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33a9b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-347 4137,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="22129@x" ObjectIDND1="22127@x" ObjectIDZND0="g_33a8de0@0" Pin0InfoVect0LinkObjId="g_33a8de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118808_0" Pin1InfoVect1LinkObjId="SW-118805_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-347 4137,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33aa640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-356 4132,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="22129@1" ObjectIDZND0="g_33a8de0@0" ObjectIDZND1="22127@x" Pin0InfoVect0LinkObjId="g_33a8de0_0" Pin0InfoVect1LinkObjId="SW-118805_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118808_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-356 4132,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33aa8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-347 4132,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_33a8de0@0" ObjectIDND1="22129@x" ObjectIDZND0="22127@1" Pin0InfoVect0LinkObjId="SW-118805_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33a8de0_0" Pin1InfoVect1LinkObjId="SW-118808_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-347 4132,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ab870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-348 4618,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="22135@x" ObjectIDND1="22133@x" ObjectIDZND0="g_33aab00@0" Pin0InfoVect0LinkObjId="g_33aab00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118825_0" Pin1InfoVect1LinkObjId="SW-118822_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-348 4618,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ac360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-357 4613,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="22135@1" ObjectIDZND0="g_33aab00@0" ObjectIDZND1="22133@x" Pin0InfoVect0LinkObjId="g_33aab00_0" Pin0InfoVect1LinkObjId="SW-118822_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118825_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-357 4613,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ac5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-348 4613,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_33aab00@0" ObjectIDND1="22135@x" ObjectIDZND0="22133@1" Pin0InfoVect0LinkObjId="SW-118822_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33aab00_0" Pin1InfoVect1LinkObjId="SW-118825_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-348 4613,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ad550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-33 3799,-33 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_33ac820@0" ObjectIDZND0="22157@x" ObjectIDZND1="g_33ad7b0@0" Pin0InfoVect0LinkObjId="SW-119013_0" Pin0InfoVect1LinkObjId="g_33ad7b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33ac820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-33 3799,-33 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33aeac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,-67 3799,-33 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="22157@0" ObjectIDZND0="g_33ac820@0" ObjectIDZND1="g_33ad7b0@0" Pin0InfoVect0LinkObjId="g_33ac820_0" Pin0InfoVect1LinkObjId="g_33ad7b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119013_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3799,-67 3799,-33 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33aed20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,-33 3799,-17 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_33ac820@0" ObjectIDND1="22157@x" ObjectIDZND0="g_33ad7b0@1" Pin0InfoVect0LinkObjId="g_33ad7b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33ac820_0" Pin1InfoVect1LinkObjId="SW-119013_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3799,-33 3799,-17 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33aef80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,17 3799,32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_33ad7b0@0" ObjectIDZND0="22158@1" Pin0InfoVect0LinkObjId="SW-119015_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33ad7b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3799,17 3799,32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33aff10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,92 3799,92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_33af1e0@0" ObjectIDZND0="33603@x" ObjectIDZND1="22158@x" Pin0InfoVect0LinkObjId="EC-WD_JIUC.042Ld_0" Pin0InfoVect1LinkObjId="SW-119015_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33af1e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3771,92 3799,92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b0a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,163 3799,92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33603@0" ObjectIDZND0="g_33af1e0@0" ObjectIDZND1="22158@x" Pin0InfoVect0LinkObjId="g_33af1e0_0" Pin0InfoVect1LinkObjId="SW-119015_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_JIUC.042Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3799,163 3799,92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b0c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,92 3799,68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_33af1e0@0" ObjectIDND1="33603@x" ObjectIDZND0="22158@0" Pin0InfoVect0LinkObjId="SW-119015_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33af1e0_0" Pin1InfoVect1LinkObjId="EC-WD_JIUC.042Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3799,92 3799,68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b0ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3941,92 3969,92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_33b1120@0" ObjectIDZND0="22150@x" ObjectIDZND1="33604@x" Pin0InfoVect0LinkObjId="SW-118967_0" Pin0InfoVect1LinkObjId="EC-WD_JIUC.043Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33b1120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3941,92 3969,92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b2b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3941,-33 3969,-33 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_33b1e50@0" ObjectIDZND0="22149@x" ObjectIDZND1="g_33b2de0@0" Pin0InfoVect0LinkObjId="SW-118966_0" Pin0InfoVect1LinkObjId="g_33b2de0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33b1e50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3941,-33 3969,-33 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b40f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3969,-33 3969,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_33b1e50@0" ObjectIDND1="g_33b2de0@0" ObjectIDZND0="22149@0" Pin0InfoVect0LinkObjId="SW-118966_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33b1e50_0" Pin1InfoVect1LinkObjId="g_33b2de0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3969,-33 3969,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b4350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3969,29 3969,17 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="22150@1" ObjectIDZND0="g_33b2de0@0" Pin0InfoVect0LinkObjId="g_33b2de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118967_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3969,29 3969,17 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b45b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3969,-17 3969,-33 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_33b2de0@1" ObjectIDZND0="g_33b1e50@0" ObjectIDZND1="22149@x" Pin0InfoVect0LinkObjId="g_33b1e50_0" Pin0InfoVect1LinkObjId="SW-118966_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33b2de0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3969,-17 3969,-33 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_342ee20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3969,65 3969,92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="22150@0" ObjectIDZND0="g_33b1120@0" ObjectIDZND1="33604@x" Pin0InfoVect0LinkObjId="g_33b1120_0" Pin0InfoVect1LinkObjId="EC-WD_JIUC.043Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118967_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3969,65 3969,92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_342f080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3969,92 3969,161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_33b1120@0" ObjectIDND1="22150@x" ObjectIDZND0="33604@0" Pin0InfoVect0LinkObjId="EC-WD_JIUC.043Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33b1120_0" Pin1InfoVect1LinkObjId="SW-118967_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3969,92 3969,161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3430c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-36 4133,-36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_342fef0@0" ObjectIDZND0="22161@x" ObjectIDZND1="g_3430e80@0" Pin0InfoVect0LinkObjId="SW-119037_0" Pin0InfoVect1LinkObjId="g_3430e80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_342fef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-36 4133,-36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3431900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,89 4133,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_342f2e0@0" ObjectIDZND0="22163@x" ObjectIDZND1="33605@x" Pin0InfoVect0LinkObjId="SW-119039_0" Pin0InfoVect1LinkObjId="EC-WD_JIUC.044Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_342f2e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4105,89 4133,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34323f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-68 4133,-36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="22161@0" ObjectIDZND0="g_342fef0@0" ObjectIDZND1="g_3430e80@0" Pin0InfoVect0LinkObjId="g_342fef0_0" Pin0InfoVect1LinkObjId="g_3430e80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119037_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-68 4133,-36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3432650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-36 4133,-20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_342fef0@0" ObjectIDND1="22161@x" ObjectIDZND0="g_3430e80@1" Pin0InfoVect0LinkObjId="g_3430e80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_342fef0_0" Pin1InfoVect1LinkObjId="SW-119037_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-36 4133,-20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34328b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,14 4133,27 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3430e80@0" ObjectIDZND0="22163@1" Pin0InfoVect0LinkObjId="SW-119039_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3430e80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4133,14 4133,27 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34333a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,63 4133,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="22163@0" ObjectIDZND0="g_342f2e0@0" ObjectIDZND1="33605@x" Pin0InfoVect0LinkObjId="g_342f2e0_0" Pin0InfoVect1LinkObjId="EC-WD_JIUC.044Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119039_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4133,63 4133,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3433600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,89 4133,163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_342f2e0@0" ObjectIDND1="22163@x" ObjectIDZND0="33605@0" Pin0InfoVect0LinkObjId="EC-WD_JIUC.044Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_342f2e0_0" Pin1InfoVect1LinkObjId="SW-119039_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4133,89 4133,163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3434590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4274,-28 4303,-28 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3433860@0" ObjectIDZND0="22166@x" ObjectIDZND1="g_34347f0@0" Pin0InfoVect0LinkObjId="SW-119062_0" Pin0InfoVect1LinkObjId="g_34347f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3433860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4274,-28 4303,-28 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3435270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4274,97 4303,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_34354d0@0" ObjectIDZND0="22167@x" ObjectIDZND1="33606@x" Pin0InfoVect0LinkObjId="SW-119064_0" Pin0InfoVect1LinkObjId="EC-WD_JIUC.045Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34354d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4274,97 4303,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3436a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,-69 4303,-28 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="22166@0" ObjectIDZND0="g_34347f0@0" ObjectIDZND1="g_3433860@0" Pin0InfoVect0LinkObjId="g_34347f0_0" Pin0InfoVect1LinkObjId="g_3433860_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119062_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4303,-69 4303,-28 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3436cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,-28 4303,-12 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="22166@x" ObjectIDND1="g_3433860@0" ObjectIDZND0="g_34347f0@1" Pin0InfoVect0LinkObjId="g_34347f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119062_0" Pin1InfoVect1LinkObjId="g_3433860_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4303,-28 4303,-12 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3436f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,22 4303,31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_34347f0@0" ObjectIDZND0="22167@1" Pin0InfoVect0LinkObjId="SW-119064_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34347f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4303,22 4303,31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3437a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,67 4303,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="22167@0" ObjectIDZND0="33606@x" ObjectIDZND1="g_34354d0@0" Pin0InfoVect0LinkObjId="EC-WD_JIUC.045Ld_0" Pin0InfoVect1LinkObjId="g_34354d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119064_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4303,67 4303,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3437ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,97 4303,160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="22167@x" ObjectIDND1="g_34354d0@0" ObjectIDZND0="33606@0" Pin0InfoVect0LinkObjId="EC-WD_JIUC.045Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119064_0" Pin1InfoVect1LinkObjId="g_34354d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4303,97 4303,160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3437f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4421,93 4449,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_3438160@0" ObjectIDZND0="22171@x" ObjectIDZND1="33607@x" Pin0InfoVect0LinkObjId="SW-119088_0" Pin0InfoVect1LinkObjId="EC-WD_JIUC.046Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3438160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4421,93 4449,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3439bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4421,-32 4449,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3438e90@0" ObjectIDZND0="22170@x" ObjectIDZND1="g_3439e20@0" Pin0InfoVect0LinkObjId="SW-119086_0" Pin0InfoVect1LinkObjId="g_3439e20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3438e90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4421,-32 4449,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343b130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,-70 4449,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="22170@0" ObjectIDZND0="g_3438e90@0" ObjectIDZND1="g_3439e20@0" Pin0InfoVect0LinkObjId="g_3438e90_0" Pin0InfoVect1LinkObjId="g_3439e20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119086_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4449,-70 4449,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343b390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,-32 4449,-16 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3438e90@0" ObjectIDND1="22170@x" ObjectIDZND0="g_3439e20@1" Pin0InfoVect0LinkObjId="g_3439e20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3438e90_0" Pin1InfoVect1LinkObjId="SW-119086_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4449,-32 4449,-16 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343b5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,18 4449,30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3439e20@0" ObjectIDZND0="22171@1" Pin0InfoVect0LinkObjId="SW-119088_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3439e20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4449,18 4449,30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343c0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,66 4449,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="22171@0" ObjectIDZND0="g_3438160@0" ObjectIDZND1="33607@x" Pin0InfoVect0LinkObjId="g_3438160_0" Pin0InfoVect1LinkObjId="EC-WD_JIUC.046Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119088_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4449,66 4449,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343c340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,93 4449,160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3438160@0" ObjectIDND1="22171@x" ObjectIDZND0="33607@0" Pin0InfoVect0LinkObjId="EC-WD_JIUC.046Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3438160_0" Pin1InfoVect1LinkObjId="SW-119088_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4449,93 4449,160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343e000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-33 4621,-33 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_343d2d0@0" ObjectIDZND0="22174@x" ObjectIDZND1="g_343e260@0" Pin0InfoVect0LinkObjId="SW-119110_0" Pin0InfoVect1LinkObjId="g_343e260_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_343d2d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-33 4621,-33 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343ece0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,92 4621,92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_343c5a0@0" ObjectIDZND0="22175@x" ObjectIDZND1="33608@x" Pin0InfoVect0LinkObjId="SW-119111_0" Pin0InfoVect1LinkObjId="EC-WD_JIUC.047Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_343c5a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4593,92 4621,92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343f7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-70 4621,-33 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="22174@0" ObjectIDZND0="g_343d2d0@0" ObjectIDZND1="g_343e260@0" Pin0InfoVect0LinkObjId="g_343d2d0_0" Pin0InfoVect1LinkObjId="g_343e260_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-70 4621,-33 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343fa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-33 4621,-17 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_343d2d0@0" ObjectIDND1="22174@x" ObjectIDZND0="g_343e260@1" Pin0InfoVect0LinkObjId="g_343e260_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_343d2d0_0" Pin1InfoVect1LinkObjId="SW-119110_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-33 4621,-17 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343fc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,17 4621,30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_343e260@0" ObjectIDZND0="22175@1" Pin0InfoVect0LinkObjId="SW-119111_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_343e260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,17 4621,30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3440780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,66 4621,92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="22175@0" ObjectIDZND0="g_343c5a0@0" ObjectIDZND1="33608@x" Pin0InfoVect0LinkObjId="g_343c5a0_0" Pin0InfoVect1LinkObjId="EC-WD_JIUC.047Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119111_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4621,66 4621,92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34409e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,92 4621,160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_343c5a0@0" ObjectIDND1="22175@x" ObjectIDZND0="33608@0" Pin0InfoVect0LinkObjId="EC-WD_JIUC.047Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_343c5a0_0" Pin1InfoVect1LinkObjId="SW-119111_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,92 4621,160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3441970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-35 4766,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3440c40@0" ObjectIDZND0="22178@x" ObjectIDZND1="g_3441bd0@0" Pin0InfoVect0LinkObjId="SW-119135_0" Pin0InfoVect1LinkObjId="g_3441bd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3440c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-35 4766,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3442650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,90 4766,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_34428b0@0" ObjectIDZND0="22179@x" ObjectIDZND1="33609@x" Pin0InfoVect0LinkObjId="SW-119138_0" Pin0InfoVect1LinkObjId="EC-WD_JIUC.048Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34428b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4738,90 4766,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3443e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4766,-70 4766,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="22178@0" ObjectIDZND0="g_3440c40@0" ObjectIDZND1="g_3441bd0@0" Pin0InfoVect0LinkObjId="g_3440c40_0" Pin0InfoVect1LinkObjId="g_3441bd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119135_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4766,-70 4766,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34440d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4766,-35 4766,-19 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3440c40@0" ObjectIDND1="22178@x" ObjectIDZND0="g_3441bd0@1" Pin0InfoVect0LinkObjId="g_3441bd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3440c40_0" Pin1InfoVect1LinkObjId="SW-119135_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4766,-35 4766,-19 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3444330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4766,15 4766,30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3441bd0@0" ObjectIDZND0="22179@1" Pin0InfoVect0LinkObjId="SW-119138_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3441bd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4766,15 4766,30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3444e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4766,65 4766,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="22179@0" ObjectIDZND0="g_34428b0@0" ObjectIDZND1="33609@x" Pin0InfoVect0LinkObjId="g_34428b0_0" Pin0InfoVect1LinkObjId="EC-WD_JIUC.048Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119138_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4766,65 4766,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3445080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4766,90 4766,161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_34428b0@0" ObjectIDND1="22179@x" ObjectIDZND0="33609@0" Pin0InfoVect0LinkObjId="EC-WD_JIUC.048Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_34428b0_0" Pin1InfoVect1LinkObjId="SW-119138_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4766,90 4766,161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34452e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4923,-108 4951,-108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="g_3445540@0" ObjectIDZND0="22162@x" ObjectIDZND1="g_37fe460@0" Pin0InfoVect0LinkObjId="SW-119038_0" Pin0InfoVect1LinkObjId="g_37fe460_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3445540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4923,-108 4951,-108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3446b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4951,-172 4951,-108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="22162@0" ObjectIDZND0="g_3445540@0" ObjectIDZND1="g_37fe460@0" Pin0InfoVect0LinkObjId="g_3445540_0" Pin0InfoVect1LinkObjId="g_37fe460_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119038_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4951,-172 4951,-108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3446d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4951,-108 4951,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_3445540@0" ObjectIDND1="22162@x" ObjectIDZND0="g_37fe460@0" Pin0InfoVect0LinkObjId="g_37fe460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3445540_0" Pin1InfoVect1LinkObjId="SW-119038_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4951,-108 4951,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3447cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3662,-93 3630,-93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_3446fc0@0" ObjectIDZND0="22151@x" ObjectIDZND1="22153@x" Pin0InfoVect0LinkObjId="SW-118988_0" Pin0InfoVect1LinkObjId="SW-118991_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3446fc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3662,-93 3630,-93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34487e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3630,-117 3630,-93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="22151@0" ObjectIDZND0="22153@x" ObjectIDZND1="g_3446fc0@0" Pin0InfoVect0LinkObjId="SW-118991_0" Pin0InfoVect1LinkObjId="g_3446fc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118988_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3630,-117 3630,-93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3448a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3630,-93 3630,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="22151@x" ObjectIDND1="g_3446fc0@0" ObjectIDZND0="22153@1" Pin0InfoVect0LinkObjId="SW-118991_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118988_0" Pin1InfoVect1LinkObjId="g_3446fc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3630,-93 3630,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3449a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-475 4613,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="22181@1" ObjectIDZND0="g_3449c50@1" Pin0InfoVect0LinkObjId="g_3449c50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a4a7e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-475 4613,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_344a400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-408 4613,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3449c50@0" ObjectIDZND0="22135@0" Pin0InfoVect0LinkObjId="SW-118825_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3449c50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-408 4613,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3452720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-633 4157,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22130@x" ObjectIDND1="22126@x" ObjectIDZND0="39254@0" Pin0InfoVect0LinkObjId="SW-235587_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118809_0" Pin1InfoVect1LinkObjId="SW-119632_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-633 4157,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3452980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4193,-633 4215,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39254@1" ObjectIDZND0="g_344fb80@0" Pin0InfoVect0LinkObjId="g_344fb80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235587_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4193,-633 4215,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3453470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-659 4132,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22130@0" ObjectIDZND0="39254@x" ObjectIDZND1="22126@x" Pin0InfoVect0LinkObjId="SW-235587_0" Pin0InfoVect1LinkObjId="SW-119632_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118809_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-659 4132,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34536d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-633 4132,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="39254@x" ObjectIDND1="22130@x" ObjectIDZND0="22126@1" Pin0InfoVect0LinkObjId="SW-119632_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235587_0" Pin1InfoVect1LinkObjId="SW-118809_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-633 4132,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3455e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4614,-629 4638,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22131@x" ObjectIDND1="22136@x" ObjectIDZND0="39255@0" Pin0InfoVect0LinkObjId="SW-235588_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118819_0" Pin1InfoVect1LinkObjId="SW-118826_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4614,-629 4638,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34560c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-629 4696,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39255@1" ObjectIDZND0="g_3456320@0" Pin0InfoVect0LinkObjId="g_3456320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235588_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-629 4696,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3457640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-629 4613,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="39255@x" ObjectIDND1="22136@x" ObjectIDZND0="22131@1" Pin0InfoVect0LinkObjId="SW-118819_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235588_0" Pin1InfoVect1LinkObjId="SW-118826_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-629 4613,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3459dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-858 4156,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22137@x" ObjectIDND1="22140@x" ObjectIDZND0="39257@0" Pin0InfoVect0LinkObjId="SW-235590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118912_0" Pin1InfoVect1LinkObjId="SW-118916_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-858 4156,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_345a030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-858 4214,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39257@1" ObjectIDZND0="g_345a290@0" Pin0InfoVect0LinkObjId="g_345a290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235590_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-858 4214,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_345b5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-835 4132,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22137@1" ObjectIDZND0="39257@x" ObjectIDZND1="22140@x" Pin0InfoVect0LinkObjId="SW-235590_0" Pin0InfoVect1LinkObjId="SW-118916_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118912_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-835 4132,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_345b810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-858 4132,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="39257@x" ObjectIDND1="22137@x" ObjectIDZND0="22140@0" Pin0InfoVect0LinkObjId="SW-118916_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235590_0" Pin1InfoVect1LinkObjId="SW-118912_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-858 4132,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_345ba70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4193,-786 4215,-786 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39256@1" ObjectIDZND0="g_345bcd0@0" Pin0InfoVect0LinkObjId="g_345bcd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235589_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4193,-786 4215,-786 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_345ec90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-786 4157,-786 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22138@x" ObjectIDND1="22137@x" ObjectIDZND0="39256@0" Pin0InfoVect0LinkObjId="SW-235589_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118914_0" Pin1InfoVect1LinkObjId="SW-118912_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-786 4157,-786 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_345f780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-759 4132,-786 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22138@1" ObjectIDZND0="39256@x" ObjectIDZND1="22137@x" Pin0InfoVect0LinkObjId="SW-235589_0" Pin0InfoVect1LinkObjId="SW-118912_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118914_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-759 4132,-786 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_345f9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-786 4132,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="39256@x" ObjectIDND1="22138@x" ObjectIDZND0="22137@0" Pin0InfoVect0LinkObjId="SW-118912_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235589_0" Pin1InfoVect1LinkObjId="SW-118914_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-786 4132,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3462170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4614,-864 4638,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22143@x" ObjectIDND1="22145@x" ObjectIDZND0="39258@0" Pin0InfoVect0LinkObjId="SW-235591_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118939_0" Pin1InfoVect1LinkObjId="SW-118942_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4614,-864 4638,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34623d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-864 4696,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39258@1" ObjectIDZND0="g_3462630@0" Pin0InfoVect0LinkObjId="g_3462630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235591_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-864 4696,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3463950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-843 4613,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22143@1" ObjectIDZND0="39258@x" ObjectIDZND1="22145@x" Pin0InfoVect0LinkObjId="SW-235591_0" Pin0InfoVect1LinkObjId="SW-118942_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118939_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-843 4613,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3463bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-864 4613,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="39258@x" ObjectIDND1="22143@x" ObjectIDZND0="22145@0" Pin0InfoVect0LinkObjId="SW-118942_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235591_0" Pin1InfoVect1LinkObjId="SW-118939_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-864 4613,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3463e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4673,-794 4695,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39259@1" ObjectIDZND0="g_3464070@0" Pin0InfoVect0LinkObjId="g_3464070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235592_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4673,-794 4695,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3467030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-794 4637,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22143@x" ObjectIDND1="22144@x" ObjectIDZND0="39259@0" Pin0InfoVect0LinkObjId="SW-235592_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-118939_0" Pin1InfoVect1LinkObjId="SW-118941_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-794 4637,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3467b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-794 4613,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="39259@x" ObjectIDND1="22144@x" ObjectIDZND0="22143@0" Pin0InfoVect0LinkObjId="SW-118939_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235592_0" Pin1InfoVect1LinkObjId="SW-118941_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-794 4613,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3468ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-712 4613,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22124@0" ObjectIDZND0="22144@0" Pin0InfoVect0LinkObjId="SW-118941_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-712 4613,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34690e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-768 4613,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22144@1" ObjectIDZND0="39259@x" ObjectIDZND1="22143@x" Pin0InfoVect0LinkObjId="SW-235592_0" Pin0InfoVect1LinkObjId="SW-118939_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118941_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-768 4613,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34697c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-712 4613,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22124@0" ObjectIDZND0="22136@1" Pin0InfoVect0LinkObjId="SW-118826_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-712 4613,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34699d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-658 4613,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22136@0" ObjectIDZND0="39255@x" ObjectIDZND1="22131@x" Pin0InfoVect0LinkObjId="SW-235588_0" Pin0InfoVect1LinkObjId="SW-118819_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-118826_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-658 4613,-629 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-118604" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3507.500000 -1051.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22116" ObjectName="DYN-WD_JIUC"/>
     <cge:Meas_Ref ObjectId="118604"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1.578947 -0.000000 0.000000 -1.340909 -2087.736842 207.545455)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3811160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3488.000000 409.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3811eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3488.000000 423.500000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3812460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3488.000000 394.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38129c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3494.000000 379.500000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3812c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3480.000000 365.500000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3812e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3496.000000 351.500000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.194030 -0.000000 0.000000 -1.900000 -690.910448 606.800000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3814930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4504.000000 557.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3815500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4504.000000 572.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.985075 -0.000000 0.000000 -1.966667 62.328358 666.466667)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3815eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4176.000000 586.000000) translate(0,12)">档位(档):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3816150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4176.000000 571.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3817540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3999.000000 608.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38183a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4024.000000 593.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3818c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4029.000000 577.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38197c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4010.000000 623.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.578947 -0.000000 0.000000 -1.340909 -620.736842 -270.454545)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_337fb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3488.000000 409.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_337fd90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3488.000000 423.500000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_337ffd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3488.000000 394.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3380210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3494.000000 379.500000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3380450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3480.000000 365.500000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3380690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3496.000000 351.500000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 227.000000 330.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_346a3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 1177.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_346a8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 1162.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_346ab30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3776.000000 1147.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_346ae60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4483.000000 608.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_346b0d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4508.000000 593.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_346b310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4513.000000 577.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_346b550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4494.000000 623.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 705.000000 323.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_346b970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 1177.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_346bc70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 1162.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_346beb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3776.000000 1147.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 249.000000 839.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33208d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 1177.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3320bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 1162.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3320e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3776.000000 1147.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 733.000000 842.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3321230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 1177.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3321530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 1162.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3321770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3776.000000 1147.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -202.000000 1374.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3321b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 1177.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3321e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 1162.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33220d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3776.000000 1147.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -32.000000 1374.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33224f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 1177.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33227f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 1162.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3322a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3776.000000 1147.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 139.000000 1374.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3322e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 1177.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3323150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 1162.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3323390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3776.000000 1147.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 300.000000 1374.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33237b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 1177.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3323ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 1162.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3323cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3776.000000 1147.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 474.000000 1374.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3324110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 1177.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3324410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 1162.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3324650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3776.000000 1147.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 615.000000 1374.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3324a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 1177.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3324d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 1162.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3324fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3776.000000 1147.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 791.000000 1374.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33253d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 1177.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33256d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 1162.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3325910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3776.000000 1147.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 934.000000 1374.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3325d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 1177.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3326030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 1162.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3326270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3776.000000 1147.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_GY" endPointId="0" endStationName="WD_JIUC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_GuoJiuT" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4613,-1098 4613,-1118 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19220" ObjectName="AC-35kV.LN_GuoJiuT"/>
    <cge:TPSR_Ref TObjectID="19220_SS-170"/></metadata>
   <polyline fill="none" opacity="0" points="4613,-1098 4613,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_WD" endPointId="0" endStationName="WD_JIUC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_WuJiu" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4132,-1094 4132,-1125 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37807" ObjectName="AC-35kV.LN_WuJiu"/>
    <cge:TPSR_Ref TObjectID="37807_SS-170"/></metadata>
   <polyline fill="none" opacity="0" points="4132,-1094 4132,-1125 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="22124" cx="4133" cy="-712" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22124" cx="4319" cy="-712" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22124" cx="4133" cy="-712" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22125" cx="4132" cy="-228" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22125" cx="4133" cy="-228" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22125" cx="3969" cy="-228" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22125" cx="3799" cy="-228" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22125" cx="3630" cy="-228" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22125" cx="4303" cy="-228" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22125" cx="4766" cy="-228" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22125" cx="4951" cy="-228" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22125" cx="4613" cy="-228" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22125" cx="5123" cy="-228" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22124" cx="4613" cy="-712" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22124" cx="4613" cy="-712" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-119632">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.065789 -571.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22126" ObjectName="SW-WD_JIUC.WD_JIUC_301BK"/>
     <cge:Meas_Ref ObjectId="119632"/>
    <cge:TPSR_Ref TObjectID="22126"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118805">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.065789 -294.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22127" ObjectName="SW-WD_JIUC.WD_JIUC_001BK"/>
     <cge:Meas_Ref ObjectId="118805"/>
    <cge:TPSR_Ref TObjectID="22127"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118819">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4603.597368 -571.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22131" ObjectName="SW-WD_JIUC.WD_JIUC_302BK"/>
     <cge:Meas_Ref ObjectId="118819"/>
    <cge:TPSR_Ref TObjectID="22131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118822">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4603.597368 -295.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22133" ObjectName="SW-WD_JIUC.WD_JIUC_002BK"/>
     <cge:Meas_Ref ObjectId="118822"/>
    <cge:TPSR_Ref TObjectID="22133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118912">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.065789 -800.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22137" ObjectName="SW-WD_JIUC.WD_JIUC_341BK"/>
     <cge:Meas_Ref ObjectId="118912"/>
    <cge:TPSR_Ref TObjectID="22137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119034">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4124.000000 -122.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22159" ObjectName="SW-WD_JIUC.WD_JIUC_044BK"/>
     <cge:Meas_Ref ObjectId="119034"/>
    <cge:TPSR_Ref TObjectID="22159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118963">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3960.000000 -120.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22147" ObjectName="SW-WD_JIUC.WD_JIUC_043BK"/>
     <cge:Meas_Ref ObjectId="118963"/>
    <cge:TPSR_Ref TObjectID="22147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119010">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3790.000000 -120.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22155" ObjectName="SW-WD_JIUC.WD_JIUC_042BK"/>
     <cge:Meas_Ref ObjectId="119010"/>
    <cge:TPSR_Ref TObjectID="22155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118988">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3621.000000 -109.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22151" ObjectName="SW-WD_JIUC.WD_JIUC_041BK"/>
     <cge:Meas_Ref ObjectId="118988"/>
    <cge:TPSR_Ref TObjectID="22151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119083">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4440.000000 -119.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22168" ObjectName="SW-WD_JIUC.WD_JIUC_046BK"/>
     <cge:Meas_Ref ObjectId="119083"/>
    <cge:TPSR_Ref TObjectID="22168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119107">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4612.000000 -122.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22172" ObjectName="SW-WD_JIUC.WD_JIUC_047BK"/>
     <cge:Meas_Ref ObjectId="119107"/>
    <cge:TPSR_Ref TObjectID="22172"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119132">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4757.000000 -126.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22176" ObjectName="SW-WD_JIUC.WD_JIUC_048BK"/>
     <cge:Meas_Ref ObjectId="119132"/>
    <cge:TPSR_Ref TObjectID="22176"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119059">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -121.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22164" ObjectName="SW-WD_JIUC.WD_JIUC_045BK"/>
     <cge:Meas_Ref ObjectId="119059"/>
    <cge:TPSR_Ref TObjectID="22164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-118939">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4604.000000 -808.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22143" ObjectName="SW-WD_JIUC.WD_JIUC_342BK"/>
     <cge:Meas_Ref ObjectId="118939"/>
    <cge:TPSR_Ref TObjectID="22143"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2976cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3115.000000 -964.000000) translate(0,12)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2976cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3115.000000 -964.000000) translate(0,27)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2976cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3115.000000 -964.000000) translate(0,42)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2976cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3115.000000 -964.000000) translate(0,57)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2976cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3115.000000 -964.000000) translate(0,72)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2976cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3115.000000 -964.000000) translate(0,87)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2976cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3115.000000 -964.000000) translate(0,102)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2976cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3115.000000 -964.000000) translate(0,117)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2976cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3115.000000 -964.000000) translate(0,132)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2976cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3115.000000 -964.000000) translate(0,147)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2976cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3115.000000 -964.000000) translate(0,162)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2976cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3115.000000 -964.000000) translate(0,177)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2976cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3115.000000 -964.000000) translate(0,192)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2976cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3115.000000 -964.000000) translate(0,207)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2976cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3115.000000 -964.000000) translate(0,222)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2976cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3115.000000 -964.000000) translate(0,237)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3113.000000 -590.000000) translate(0,12)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3113.000000 -590.000000) translate(0,27)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3113.000000 -590.000000) translate(0,42)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3113.000000 -590.000000) translate(0,57)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3113.000000 -590.000000) translate(0,72)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3113.000000 -590.000000) translate(0,87)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3113.000000 -590.000000) translate(0,102)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3113.000000 -590.000000) translate(0,117)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3113.000000 -590.000000) translate(0,132)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3113.000000 -590.000000) translate(0,147)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3113.000000 -590.000000) translate(0,162)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3113.000000 -590.000000) translate(0,177)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3113.000000 -590.000000) translate(0,192)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3113.000000 -590.000000) translate(0,207)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3113.000000 -590.000000) translate(0,222)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3113.000000 -590.000000) translate(0,237)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3113.000000 -590.000000) translate(0,252)">联系方式：8823447</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3113.000000 -590.000000) translate(0,267)">                4793</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(21,40,56)" font-family="SimSun" font-size="30" graphid="g_33babf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3291.000000 -1139.500000) translate(0,24)">九厂变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f0c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4094.000000 -1148.000000) translate(0,12)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f0c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4094.000000 -1148.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f0c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4094.000000 -1148.000000) translate(0,42)">武</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f0c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4094.000000 -1148.000000) translate(0,57)">九</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f0c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4094.000000 -1148.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f23d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4156.000000 -904.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f2dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4355.000000 -481.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f2dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4355.000000 -481.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f3b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4674.000000 -902.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f3fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4748.000000 -762.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f4960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4647.000000 -1105.000000) translate(0,12)">35kV果九T线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f5200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3568.000000 111.000000) translate(0,12)">电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f5750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4908.000000 10.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f5750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4908.000000 10.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f5d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5093.000000 67.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f5f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4816.000000 -558.000000) translate(0,12)">2号主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f5f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4816.000000 -558.000000) translate(0,27)">SZ7-4000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f5f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4816.000000 -558.000000) translate(0,42)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f5f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4816.000000 -558.000000) translate(0,57)">Y/△-11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f5f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4816.000000 -558.000000) translate(0,72)">Ud=5.70%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37faeb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -491.000000) translate(0,12)">1号主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37faeb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -491.000000) translate(0,27)">SFZ-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37faeb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -491.000000) translate(0,42)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37faeb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -491.000000) translate(0,57)">Y/△-11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37faeb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -491.000000) translate(0,72)">Ud=7.40%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_37ffb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4373.000000 -743.000000) translate(0,16)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3801480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5116.000000 -261.000000) translate(0,16)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_338e1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3121.000000 -705.000000) translate(0,20)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3394010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4091.000000 -832.000000) translate(0,12)">341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3394640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4081.000000 -749.000000) translate(0,12)">3411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3394880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4077.000000 -908.000000) translate(0,12)">3416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3394ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4072.000000 -964.000000) translate(0,12)">34167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33950c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4571.000000 -840.000000) translate(0,12)">342</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3395300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4567.000000 -755.000000) translate(0,12)">3421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3395540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4564.000000 -915.000000) translate(0,12)">3426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3395780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4561.000000 -965.000000) translate(0,12)">34267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33959c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4346.000000 -654.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3395c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4273.000000 -682.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3395e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4139.000000 -684.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3396080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4141.000000 -600.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33962c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4206.000000 -513.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3396500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4145.000000 -324.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3396740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4143.000000 -381.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3396980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4139.000000 -275.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3396bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4620.000000 -683.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3396e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4622.000000 -600.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3397040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4697.000000 -543.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3397280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4622.000000 -324.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33974c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4623.000000 -382.000000) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3397700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4621.000000 -274.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3397940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3583.000000 -195.000000) translate(0,12)">0411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3397b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 -142.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3397dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3578.000000 -65.000000) translate(0,12)">0416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3398000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3752.000000 -197.000000) translate(0,12)">0421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3398240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3756.000000 -149.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3398480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3747.000000 -90.000000) translate(0,12)">0422</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33986c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3745.000000 45.000000) translate(0,12)">0426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3398900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3919.000000 -200.000000) translate(0,12)">0431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3398b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3922.000000 -152.000000) translate(0,12)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3398d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3919.000000 -91.000000) translate(0,12)">0432</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3398fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3917.000000 43.000000) translate(0,12)">0436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3399200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4084.000000 -201.000000) translate(0,12)">0441</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3399440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4090.000000 -155.000000) translate(0,12)">044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3399680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4082.000000 -92.000000) translate(0,12)">0442</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33998c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4082.000000 40.000000) translate(0,12)">0446</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3399b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4256.000000 -198.000000) translate(0,12)">0451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3399d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4259.000000 -153.000000) translate(0,12)">045</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3399f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4253.000000 43.000000) translate(0,12)">0456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339a1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4253.000000 -92.000000) translate(0,12)">0452</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339a400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4401.000000 -200.000000) translate(0,12)">0461</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339a640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4403.000000 -92.000000) translate(0,12)">0462</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339a880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4402.000000 41.000000) translate(0,12)">0466</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339aac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4403.000000 -150.000000) translate(0,12)">046</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339ad00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4568.000000 42.000000) translate(0,12)">0476</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339af40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4574.000000 -152.000000) translate(0,12)">047</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339b180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4572.000000 -197.000000) translate(0,12)">0471</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339b3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4572.000000 -95.000000) translate(0,12)">0472</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339b600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -157.000000) translate(0,12)">048</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339b840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4719.000000 -200.000000) translate(0,12)">0481</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339ba80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4718.000000 -97.000000) translate(0,12)">0482</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339bcc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4718.000000 36.000000) translate(0,12)">0486</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339bf00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4902.000000 -195.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339c140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5071.000000 -200.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_339e5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3496.000000 -1117.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_339f610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3496.000000 -1152.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344b520" transform="matrix(1.000000 0.000000 0.000000 1.000000 3814.000000 125.000000) translate(0,12)">滑</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344b520" transform="matrix(1.000000 0.000000 0.000000 1.000000 3814.000000 125.000000) translate(0,27)">坡</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344b520" transform="matrix(1.000000 0.000000 0.000000 1.000000 3814.000000 125.000000) translate(0,42)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344c1c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3986.000000 122.000000) translate(0,12)">九</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344c1c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3986.000000 122.000000) translate(0,27)">近</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344c1c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3986.000000 122.000000) translate(0,42)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344c730" transform="matrix(1.000000 0.000000 0.000000 1.000000 4148.000000 114.000000) translate(0,12)">武</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344c730" transform="matrix(1.000000 0.000000 0.000000 1.000000 4148.000000 114.000000) translate(0,27)">化</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344c730" transform="matrix(1.000000 0.000000 0.000000 1.000000 4148.000000 114.000000) translate(0,42)">专</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344c730" transform="matrix(1.000000 0.000000 0.000000 1.000000 4148.000000 114.000000) translate(0,57)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344cfe0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4312.000000 122.000000) translate(0,12)">禄</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344cfe0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4312.000000 122.000000) translate(0,27)">金</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344cfe0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4312.000000 122.000000) translate(0,42)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344d860" transform="matrix(1.000000 0.000000 0.000000 1.000000 4467.000000 125.000000) translate(0,12)">姚</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344d860" transform="matrix(1.000000 0.000000 0.000000 1.000000 4467.000000 125.000000) translate(0,27)">铭</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344d860" transform="matrix(1.000000 0.000000 0.000000 1.000000 4467.000000 125.000000) translate(0,42)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344e100" transform="matrix(1.000000 0.000000 0.000000 1.000000 4636.000000 124.000000) translate(0,12)">机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344e100" transform="matrix(1.000000 0.000000 0.000000 1.000000 4636.000000 124.000000) translate(0,27)">关</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344e100" transform="matrix(1.000000 0.000000 0.000000 1.000000 4636.000000 124.000000) translate(0,42)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344e9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4781.000000 119.000000) translate(0,12)">恒通</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344e9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4781.000000 119.000000) translate(0,27)">冶炼</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_344e9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4781.000000 119.000000) translate(0,42)">厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3467d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4155.000000 -847.000000) translate(0,12)">34160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34683b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4157.000000 -775.000000) translate(0,12)">34117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34685f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4637.000000 -783.000000) translate(0,12)">34217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3468830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4637.000000 -853.000000) translate(0,12)">34260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3468a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4158.000000 -660.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3468cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4637.000000 -657.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33264b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3082.000000 -301.500000) translate(0,12)">武定巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33276d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3188.000000 -316.500000) translate(0,12)">18787878990</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33276d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3188.000000 -316.500000) translate(0,27)">18787842893</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33276d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3188.000000 -316.500000) translate(0,42)">13987880311</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2989cb0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4041.065789 -948.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33be2d0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4525.597368 -949.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3387ac0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4397.000000 -623.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_344fb80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4211.000000 -627.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3456320" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4692.000000 -623.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_345a290" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4210.000000 -852.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_345bcd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4211.000000 -780.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3462630" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4692.000000 -858.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3464070" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4691.000000 -788.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="WD_JIUC"/>
</svg>