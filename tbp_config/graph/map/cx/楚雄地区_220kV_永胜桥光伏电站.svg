<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-304" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-270 -957 4951 2457">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="19" y2="19"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape126">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="46" y2="29"/>
   </symbol>
   <symbol id="lightningRod:shape139">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27195" x1="7" x2="7" y1="6" y2="15"/>
    <rect height="25" stroke-width="1" width="12" x="1" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="48" y2="23"/>
   </symbol>
   <symbol id="lightningRod:shape206">
    <circle cx="7" cy="21" fillStyle="0" r="6" stroke-width="0.431185"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="3,17 11,25 " stroke-width="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="2,24 11,17 " stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="27" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape174">
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="14" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="39" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="7" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape37">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,63 0,73 10,73 5,63 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,28 0,18 10,18 5,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="86" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape194">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="10" y1="8" y2="8"/>
    <polyline DF8003:Layer="PUBLIC" points="22,1 22,16 10,8 22,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="36" x2="22" y1="9" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_2063b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="load:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="15"/>
    <polyline DF8003:Layer="PUBLIC" points="1,15 10,15 5,25 0,15 1,15 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="15" y2="25"/>
   </symbol>
   <symbol id="load:shape6">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="19,1 31,9 19,17 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="5" x2="29" y1="9" y2="9"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape93_0">
    <ellipse cx="62" cy="27" fillStyle="0" rx="24.5" ry="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="74" x2="59" y1="25" y2="25"/>
   </symbol>
   <symbol id="transformer2:shape93_1">
    <circle cx="29" cy="25" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="16" y1="24" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape98_0">
    <circle cx="16" cy="61" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="8" y2="8"/>
    <polyline DF8003:Layer="PUBLIC" points="12,58 19,58 16,65 12,58 "/>
   </symbol>
   <symbol id="transformer2:shape98_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,39 40,39 40,10 " stroke-width="1"/>
    <circle cx="16" cy="39" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="40" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="39" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="39" y2="34"/>
   </symbol>
   <symbol id="transformer2:shape99_0">
    <circle cx="40" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <circle cx="76" cy="48" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="70" x2="88" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="79" x2="88" y1="58" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="79" x2="70" y1="58" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="69" y1="47" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="64" x2="69" y1="86" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="69" x2="67" y1="86" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="69" x2="69" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="39" y1="31" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="32" y1="24" y2="31"/>
   </symbol>
   <symbol id="transformer2:shape99_1">
    <circle cx="40" cy="64" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="78" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="39" y1="62" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="32" y1="69" y2="62"/>
   </symbol>
   <symbol id="voltageTransformer:shape71">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="12" y2="12"/>
    <ellipse cx="39" cy="19" fillStyle="0" rx="15" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.36031" x1="30" x2="34" y1="55" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.39762" x1="25" x2="21" y1="55" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.77579" x1="21" x2="35" y1="62" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="33" x2="41" y1="16" y2="20"/>
    <ellipse cx="16" cy="20" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="40" x2="41" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="47" x2="41" y1="16" y2="20"/>
    <ellipse cx="28" cy="56" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="32" y2="32"/>
    <ellipse cx="40" cy="39" fillStyle="0" rx="15" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="32" y2="32"/>
    <ellipse cx="15" cy="40" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="15" x2="16" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="22" x2="16" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="16" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="33" x2="41" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="40" x2="41" y1="40" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="47" x2="41" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="15" x2="16" y1="40" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="21" x2="15" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="16" y1="36" y2="40"/>
   </symbol>
   <symbol id="voltageTransformer:shape14">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="3" x2="7" y1="3" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="6,19 26,19 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="2" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="30" y1="6" y2="4"/>
    <circle cx="30" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="39" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="30" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="22" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="37" x2="39" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="40" x2="40" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="42" x2="39" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="30" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="31" x2="31" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="33" x2="30" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="21" x2="24" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="21" x2="18" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="18" x2="24" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="31" x2="31" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="33" x2="30" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251787" x1="6" x2="6" y1="19" y2="9"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a7e460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a7f410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aac510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aad4f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aae510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aaeff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aaf930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ab0230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2156fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2156fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cd2880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cd2880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cd4310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2cd4310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2cd5150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a56960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a575d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a58380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a58ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2af2490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a5a080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a5a8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aed930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aeea10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aef390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aefe80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2af0840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2af1440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a1c210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a1d3b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a1dfd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a90e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a6a9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2a6ba60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2a6d020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="2467" width="4961" x="-275" y="-962"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2164330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 332.000000 396.666667) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e01cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 327.000000 383.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a26a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 335.000000 438.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2157ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 335.000000 410.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2106430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 335.000000 424.333333) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_208f490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1671.000000 520.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_208f600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1660.000000 505.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_208f770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1685.000000 490.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_200b920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 599.000000 -396.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_200bc50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 588.000000 -411.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_200be00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 613.000000 -426.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203b8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -45.000000 -1193.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203bac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -56.000000 -1208.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203bcc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -31.000000 -1223.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203c5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 247.000000 -1065.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203c7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 236.000000 -1080.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203ca30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 261.000000 -1095.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203d300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 773.000000 -1076.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203d560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 762.000000 -1091.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203d7a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 787.000000 -1106.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203e070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1021.000000 -1075.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203e2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1010.000000 -1090.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203e510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1035.000000 -1105.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203ee20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1254.000000 -1075.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203f040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1243.000000 -1090.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203f280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1268.000000 -1105.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203fb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1507.000000 -1078.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203fdb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1496.000000 -1093.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203fff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1521.000000 -1108.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20408a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1729.000000 -1076.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2040ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1718.000000 -1091.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2040d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1743.000000 -1106.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2041610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1986.000000 -1073.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2041830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1975.000000 -1088.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2041a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2000.000000 -1103.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20429c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -243.000000 -509.333333) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2042c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -248.000000 -523.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2042e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -240.000000 -468.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2043090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -240.000000 -495.333333) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20432d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -240.000000 -481.666667) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d77e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3086.000000 -404.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d7cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3075.000000 -419.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d7f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3100.000000 -434.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22d82e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2483.000000 -1078.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22d8730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2472.000000 -1093.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22d8970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2497.000000 -1108.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fd600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2740.000000 -1082.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fdc00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2729.000000 -1097.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fde40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2754.000000 -1112.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fff70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3004.000000 -1077.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23001d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2993.000000 -1092.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2300410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3018.000000 -1107.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2300740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3279.000000 -1083.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23009a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3268.000000 -1098.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2300be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3293.000000 -1113.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2300f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3747.000000 -1081.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2301170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3736.000000 -1096.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23013b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3761.000000 -1111.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23016e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.000000 -1081.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2301940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.000000 -1096.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2301b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3997.000000 -1111.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2301eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4213.000000 -1087.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2302110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4202.000000 -1102.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2302350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4227.000000 -1117.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2302680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4448.000000 -1084.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23028e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4437.000000 -1099.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2302b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4462.000000 -1114.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2309990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3121.000000 190.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2309bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3110.000000 175.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2309e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3135.000000 160.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2311060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 642.000000 183.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2311670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 631.000000 168.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23118b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 656.000000 153.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-267256">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1600.063218 -519.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43148" ObjectName="SW-CX_YSQ.CX_YSQ_281BK"/>
     <cge:Meas_Ref ObjectId="267256"/>
    <cge:TPSR_Ref TObjectID="43148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287673">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 515.333972 -166.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44936" ObjectName="SW-CX_YSQ.CX_YSQ_201BK"/>
     <cge:Meas_Ref ObjectId="287673"/>
    <cge:TPSR_Ref TObjectID="44936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267270">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 515.333972 442.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43158" ObjectName="SW-CX_YSQ.CX_YSQ_301BK"/>
     <cge:Meas_Ref ObjectId="267270"/>
    <cge:TPSR_Ref TObjectID="43158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267303">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 17.012133 684.650000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43183" ObjectName="SW-CX_YSQ.CX_YSQ_311BK"/>
     <cge:Meas_Ref ObjectId="267303"/>
    <cge:TPSR_Ref TObjectID="43183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267308">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 305.012133 684.650000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43187" ObjectName="SW-CX_YSQ.CX_YSQ_312BK"/>
     <cge:Meas_Ref ObjectId="267308"/>
    <cge:TPSR_Ref TObjectID="43187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 515.012133 899.650000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267278">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 829.012133 685.650000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43163" ObjectName="SW-CX_YSQ.CX_YSQ_313BK"/>
     <cge:Meas_Ref ObjectId="267278"/>
    <cge:TPSR_Ref TObjectID="43163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267283">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1065.012133 684.650000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43167" ObjectName="SW-CX_YSQ.CX_YSQ_314BK"/>
     <cge:Meas_Ref ObjectId="267283"/>
    <cge:TPSR_Ref TObjectID="43167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267288">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1318.012133 683.650000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43171" ObjectName="SW-CX_YSQ.CX_YSQ_315BK"/>
     <cge:Meas_Ref ObjectId="267288"/>
    <cge:TPSR_Ref TObjectID="43171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267293">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1547.012133 684.650000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43175" ObjectName="SW-CX_YSQ.CX_YSQ_316BK"/>
     <cge:Meas_Ref ObjectId="267293"/>
    <cge:TPSR_Ref TObjectID="43175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267298">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1773.012133 684.650000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43179" ObjectName="SW-CX_YSQ.CX_YSQ_317BK"/>
     <cge:Meas_Ref ObjectId="267298"/>
    <cge:TPSR_Ref TObjectID="43179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267313">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2004.012133 683.650000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43191" ObjectName="SW-CX_YSQ.CX_YSQ_318BK"/>
     <cge:Meas_Ref ObjectId="267313"/>
    <cge:TPSR_Ref TObjectID="43191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287689">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3002.012133 -158.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44952" ObjectName="SW-CX_YSQ.CX_YSQ_202BK"/>
     <cge:Meas_Ref ObjectId="287689"/>
    <cge:TPSR_Ref TObjectID="44952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287696">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3002.012133 450.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44959" ObjectName="SW-CX_YSQ.CX_YSQ_302BK"/>
     <cge:Meas_Ref ObjectId="287696"/>
    <cge:TPSR_Ref TObjectID="44959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287703">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2505.012133 692.650000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44962" ObjectName="SW-CX_YSQ.CX_YSQ_321BK"/>
     <cge:Meas_Ref ObjectId="287703"/>
    <cge:TPSR_Ref TObjectID="44962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287708">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2792.012133 692.650000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44966" ObjectName="SW-CX_YSQ.CX_YSQ_322BK"/>
     <cge:Meas_Ref ObjectId="287708"/>
    <cge:TPSR_Ref TObjectID="44966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287718">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3316.012133 693.650000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44974" ObjectName="SW-CX_YSQ.CX_YSQ_324BK"/>
     <cge:Meas_Ref ObjectId="287718"/>
    <cge:TPSR_Ref TObjectID="44974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287724">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3805.012133 691.650000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44980" ObjectName="SW-CX_YSQ.CX_YSQ_325BK"/>
     <cge:Meas_Ref ObjectId="287724"/>
    <cge:TPSR_Ref TObjectID="44980"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287729">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4034.012133 692.650000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44984" ObjectName="SW-CX_YSQ.CX_YSQ_326BK"/>
     <cge:Meas_Ref ObjectId="287729"/>
    <cge:TPSR_Ref TObjectID="44984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287734">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4260.012133 692.650000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44988" ObjectName="SW-CX_YSQ.CX_YSQ_327BK"/>
     <cge:Meas_Ref ObjectId="287734"/>
    <cge:TPSR_Ref TObjectID="44988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287739">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4491.012133 691.650000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44992" ObjectName="SW-CX_YSQ.CX_YSQ_328BK"/>
     <cge:Meas_Ref ObjectId="287739"/>
    <cge:TPSR_Ref TObjectID="44992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287713">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3040.012133 693.650000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44970" ObjectName="SW-CX_YSQ.CX_YSQ_323BK"/>
     <cge:Meas_Ref ObjectId="287713"/>
    <cge:TPSR_Ref TObjectID="44970"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3516.012133 910.650000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_20e5050">
    <use class="BV-220KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1498.063218 -703.000000)" xlink:href="#voltageTransformer:shape71"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_202a760">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 554.000000 960.000000)" xlink:href="#voltageTransformer:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22f7c10">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3555.000000 971.000000)" xlink:href="#voltageTransformer:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YSQ.CX_YSQ_2ⅠM">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-354 3315,-354 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="43197" ObjectName="BS-CX_YSQ.CX_YSQ_2ⅠM"/>
    <cge:TPSR_Ref TObjectID="43197"/></metadata>
   <polyline fill="none" opacity="0" points="416,-354 3315,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YSQ.CX_YSQ_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-190,556 2194,556 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="43198" ObjectName="BS-CX_YSQ.CX_YSQ_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="43198"/></metadata>
   <polyline fill="none" opacity="0" points="-190,556 2194,556 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YSQ.CX_YSQ_3ⅡM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2297,564 4681,564 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="44997" ObjectName="BS-CX_YSQ.CX_YSQ_3ⅡM"/>
    <cge:TPSR_Ref TObjectID="44997"/></metadata>
   <polyline fill="none" opacity="0" points="2297,564 4681,564 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 566.321839 299.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 255.000000 770.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 -32.000000 771.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 569.000000 623.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 619.000000 783.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 780.000000 771.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 1016.000000 770.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 1269.000000 769.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 1498.000000 770.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 1724.000000 770.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 1955.000000 769.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 3053.000000 307.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 2743.000000 778.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 2456.000000 779.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 3267.000000 779.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 3756.000000 777.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 3985.000000 778.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 4211.000000 778.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 4442.000000 777.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 2991.000000 779.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 3570.000000 634.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 3620.000000 794.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(0.000000 -0.344444 -0.326923 -0.000000 510.000000 215.000000)" xlink:href="#transformer2:shape93_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -0.344444 -0.326923 -0.000000 510.000000 215.000000)" xlink:href="#transformer2:shape93_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 12.000000 1025.000000)" xlink:href="#transformer2:shape98_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 12.000000 1025.000000)" xlink:href="#transformer2:shape98_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_YSQ.CX_YSQ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="19264"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 563.500000 118.500000)" xlink:href="#transformer2:shape99_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 563.500000 118.500000)" xlink:href="#transformer2:shape99_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="43195" ObjectName="TF-CX_YSQ.CX_YSQ_1T"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_YSQ.CX_YSQ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="22785"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3050.500000 143.500000)" xlink:href="#transformer2:shape99_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3050.500000 143.500000)" xlink:href="#transformer2:shape99_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="44996" ObjectName="TF-CX_YSQ.CX_YSQ_2T"/>
    <cge:TPSR_Ref TObjectID="44996"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_20fa6c0">
    <use class="BV-220KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1687.063218 -735.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_200fbc0">
    <use class="BV-220KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 643.000000 107.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20101b0">
    <use class="BV-220KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 611.000000 107.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fcdd20">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 406.833972 196.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fced50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 567.321839 346.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fcfcb0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 495.000000 170.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20f0cf0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 480.821839 351.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20d73f0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 69.500000 817.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20b4600">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 -16.000000 817.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20b7420">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 908.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20b8830">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 35.000000 1064.000000)" xlink:href="#lightningRod:shape194"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20ba0a0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 357.500000 817.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20bacf0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 271.000000 817.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20bd2a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 309.000000 908.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2063530">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 297.000000 980.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20ac4a0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 583.000000 675.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20b15b0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 567.500000 829.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20b2e50">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 634.000000 835.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2073980">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 881.500000 818.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20750b0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 795.000000 818.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2079980">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 833.000000 909.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2021760">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1117.500000 817.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2022e90">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1031.000000 817.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20933e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1069.000000 908.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_209e730">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1370.500000 816.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20c1460">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1284.000000 816.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20c5d30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1322.000000 907.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_207e3b0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1599.500000 817.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_207fae0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1513.000000 817.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20843b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1551.000000 908.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ff2770">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1825.500000 817.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ff3ea0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1739.000000 817.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ff8770">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1777.000000 908.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_204df50">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 2056.500000 816.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_204f580">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1970.000000 816.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2053b50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2008.000000 907.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fd4070">
    <use class="BV-220KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3130.000000 115.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fd48e0">
    <use class="BV-220KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3098.000000 115.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fd9da0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2893.512133 204.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fdb010">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3054.000000 354.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fdc200">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2967.500000 359.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb3ab0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 2557.500000 825.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb56a0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2471.000000 825.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb9f70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2509.000000 916.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fc31d0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 2844.500000 825.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fc4900">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2758.000000 825.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fc91d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2796.000000 916.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_205d5c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3032.000000 962.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f5eb40">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 3368.500000 826.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f60270">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3282.000000 826.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f64b40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3320.000000 917.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f6eb50">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 3857.500000 824.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f70280">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3771.000000 824.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f74b50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3809.000000 915.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2293b50">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4086.500000 825.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2295280">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4000.000000 825.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2299b50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4038.000000 916.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22a3b60">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4312.500000 825.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22a5290">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4226.000000 825.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22a9b60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4264.000000 916.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22b42b0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4543.500000 824.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22b59e0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4457.000000 824.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22ba2b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4495.000000 915.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22d4ad0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2955.512133 193.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22e0850">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 3092.500000 826.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22e1f80">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3006.000000 826.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22e6850">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3044.000000 917.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22f0530">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3584.000000 686.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22f2270">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 3568.500000 840.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22f3280">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3635.000000 846.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -179.000000 -881.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-267362" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 672.000000 167.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267362" ObjectName="CX_YSQ:CX_YSQ_1T_34"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-267338" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1725.063218 -521.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267338" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43148"/>
     <cge:Term_Ref ObjectID="19168"/>
    <cge:TPSR_Ref TObjectID="43148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-267339" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1725.063218 -521.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267339" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43148"/>
     <cge:Term_Ref ObjectID="19168"/>
    <cge:TPSR_Ref TObjectID="43148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-267335" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1725.063218 -521.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267335" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43148"/>
     <cge:Term_Ref ObjectID="19168"/>
    <cge:TPSR_Ref TObjectID="43148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-267372" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 652.321839 396.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267372" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43158"/>
     <cge:Term_Ref ObjectID="19188"/>
    <cge:TPSR_Ref TObjectID="43158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-267373" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 652.321839 396.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267373" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43158"/>
     <cge:Term_Ref ObjectID="19188"/>
    <cge:TPSR_Ref TObjectID="43158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-267369" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 652.321839 396.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267369" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43158"/>
     <cge:Term_Ref ObjectID="19188"/>
    <cge:TPSR_Ref TObjectID="43158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-267416" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 10.000000 1191.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267416" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43183"/>
     <cge:Term_Ref ObjectID="19238"/>
    <cge:TPSR_Ref TObjectID="43183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-267417" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 10.000000 1191.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267417" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43183"/>
     <cge:Term_Ref ObjectID="19238"/>
    <cge:TPSR_Ref TObjectID="43183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-267413" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 10.000000 1191.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267413" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43183"/>
     <cge:Term_Ref ObjectID="19238"/>
    <cge:TPSR_Ref TObjectID="43183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-267422" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 301.000000 1063.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267422" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43187"/>
     <cge:Term_Ref ObjectID="19246"/>
    <cge:TPSR_Ref TObjectID="43187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-267423" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 301.000000 1063.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267423" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43187"/>
     <cge:Term_Ref ObjectID="19246"/>
    <cge:TPSR_Ref TObjectID="43187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-267419" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 301.000000 1063.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267419" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43187"/>
     <cge:Term_Ref ObjectID="19246"/>
    <cge:TPSR_Ref TObjectID="43187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-267386" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 827.000000 1075.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267386" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43163"/>
     <cge:Term_Ref ObjectID="19198"/>
    <cge:TPSR_Ref TObjectID="43163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-267387" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 827.000000 1075.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267387" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43163"/>
     <cge:Term_Ref ObjectID="19198"/>
    <cge:TPSR_Ref TObjectID="43163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-267383" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 827.000000 1075.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267383" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43163"/>
     <cge:Term_Ref ObjectID="19198"/>
    <cge:TPSR_Ref TObjectID="43163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-267392" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1077.000000 1073.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267392" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43167"/>
     <cge:Term_Ref ObjectID="19206"/>
    <cge:TPSR_Ref TObjectID="43167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-267393" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1077.000000 1073.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267393" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43167"/>
     <cge:Term_Ref ObjectID="19206"/>
    <cge:TPSR_Ref TObjectID="43167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-267389" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1077.000000 1073.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267389" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43167"/>
     <cge:Term_Ref ObjectID="19206"/>
    <cge:TPSR_Ref TObjectID="43167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-267398" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1308.000000 1073.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267398" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43171"/>
     <cge:Term_Ref ObjectID="19214"/>
    <cge:TPSR_Ref TObjectID="43171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-267399" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1308.000000 1073.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267399" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43171"/>
     <cge:Term_Ref ObjectID="19214"/>
    <cge:TPSR_Ref TObjectID="43171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-267395" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1308.000000 1073.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267395" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43171"/>
     <cge:Term_Ref ObjectID="19214"/>
    <cge:TPSR_Ref TObjectID="43171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-267404" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1557.000000 1077.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267404" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43175"/>
     <cge:Term_Ref ObjectID="19222"/>
    <cge:TPSR_Ref TObjectID="43175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-267405" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1557.000000 1077.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267405" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43175"/>
     <cge:Term_Ref ObjectID="19222"/>
    <cge:TPSR_Ref TObjectID="43175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-267401" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1557.000000 1077.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267401" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43175"/>
     <cge:Term_Ref ObjectID="19222"/>
    <cge:TPSR_Ref TObjectID="43175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-267410" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1781.000000 1074.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267410" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43179"/>
     <cge:Term_Ref ObjectID="19230"/>
    <cge:TPSR_Ref TObjectID="43179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-267411" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1781.000000 1074.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267411" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43179"/>
     <cge:Term_Ref ObjectID="19230"/>
    <cge:TPSR_Ref TObjectID="43179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-267407" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1781.000000 1074.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267407" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43179"/>
     <cge:Term_Ref ObjectID="19230"/>
    <cge:TPSR_Ref TObjectID="43179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-267428" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2040.000000 1071.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267428" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43191"/>
     <cge:Term_Ref ObjectID="19254"/>
    <cge:TPSR_Ref TObjectID="43191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-267429" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2040.000000 1071.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267429" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43191"/>
     <cge:Term_Ref ObjectID="19254"/>
    <cge:TPSR_Ref TObjectID="43191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-267425" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2040.000000 1071.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267425" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43191"/>
     <cge:Term_Ref ObjectID="19254"/>
    <cge:TPSR_Ref TObjectID="43191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-267342" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 388.000000 -441.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267342" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-267343" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 388.000000 -441.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267343" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-267344" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 388.000000 -441.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267344" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-267348" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 388.000000 -441.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267348" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-267345" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 388.000000 -441.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267345" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-267375" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -187.000000 466.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267375" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-267376" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -187.000000 466.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267376" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-267377" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -187.000000 466.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267377" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-267381" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -187.000000 466.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267381" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-267378" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -187.000000 466.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267378" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="0"/>
     <cge:Term_Ref ObjectID="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-287765" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 404.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287765" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44959"/>
     <cge:Term_Ref ObjectID="22665"/>
    <cge:TPSR_Ref TObjectID="44959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-287766" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 404.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287766" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44959"/>
     <cge:Term_Ref ObjectID="22665"/>
    <cge:TPSR_Ref TObjectID="44959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-287762" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 404.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287762" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44959"/>
     <cge:Term_Ref ObjectID="22665"/>
    <cge:TPSR_Ref TObjectID="44959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-287777" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2540.000000 1082.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287777" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44962"/>
     <cge:Term_Ref ObjectID="22671"/>
    <cge:TPSR_Ref TObjectID="44962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-287778" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2540.000000 1082.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287778" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44962"/>
     <cge:Term_Ref ObjectID="22671"/>
    <cge:TPSR_Ref TObjectID="44962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-287776" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2540.000000 1082.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287776" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44962"/>
     <cge:Term_Ref ObjectID="22671"/>
    <cge:TPSR_Ref TObjectID="44962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-287781" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2800.000000 1081.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287781" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44966"/>
     <cge:Term_Ref ObjectID="22679"/>
    <cge:TPSR_Ref TObjectID="44966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-287782" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2800.000000 1081.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287782" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44966"/>
     <cge:Term_Ref ObjectID="22679"/>
    <cge:TPSR_Ref TObjectID="44966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-287780" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2800.000000 1081.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287780" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44966"/>
     <cge:Term_Ref ObjectID="22679"/>
    <cge:TPSR_Ref TObjectID="44966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-287785" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3064.000000 1079.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287785" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44970"/>
     <cge:Term_Ref ObjectID="22687"/>
    <cge:TPSR_Ref TObjectID="44970"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-287786" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3064.000000 1079.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287786" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44970"/>
     <cge:Term_Ref ObjectID="22687"/>
    <cge:TPSR_Ref TObjectID="44970"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-287784" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3064.000000 1079.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287784" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44970"/>
     <cge:Term_Ref ObjectID="22687"/>
    <cge:TPSR_Ref TObjectID="44970"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-287789" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3333.000000 1085.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287789" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44974"/>
     <cge:Term_Ref ObjectID="22701"/>
    <cge:TPSR_Ref TObjectID="44974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-287790" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3333.000000 1085.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287790" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44974"/>
     <cge:Term_Ref ObjectID="22701"/>
    <cge:TPSR_Ref TObjectID="44974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-287788" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3333.000000 1085.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287788" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44974"/>
     <cge:Term_Ref ObjectID="22701"/>
    <cge:TPSR_Ref TObjectID="44974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-287793" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3804.000000 1081.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287793" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44980"/>
     <cge:Term_Ref ObjectID="22721"/>
    <cge:TPSR_Ref TObjectID="44980"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-287794" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3804.000000 1081.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287794" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44980"/>
     <cge:Term_Ref ObjectID="22721"/>
    <cge:TPSR_Ref TObjectID="44980"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-287792" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3804.000000 1081.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287792" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44980"/>
     <cge:Term_Ref ObjectID="22721"/>
    <cge:TPSR_Ref TObjectID="44980"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-287797" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 1082.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287797" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44984"/>
     <cge:Term_Ref ObjectID="22759"/>
    <cge:TPSR_Ref TObjectID="44984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-287798" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 1082.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287798" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44984"/>
     <cge:Term_Ref ObjectID="22759"/>
    <cge:TPSR_Ref TObjectID="44984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-287796" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 1082.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287796" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44984"/>
     <cge:Term_Ref ObjectID="22759"/>
    <cge:TPSR_Ref TObjectID="44984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-287801" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 1087.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287801" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44988"/>
     <cge:Term_Ref ObjectID="22767"/>
    <cge:TPSR_Ref TObjectID="44988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-287802" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 1087.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287802" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44988"/>
     <cge:Term_Ref ObjectID="22767"/>
    <cge:TPSR_Ref TObjectID="44988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-287800" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 1087.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287800" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44988"/>
     <cge:Term_Ref ObjectID="22767"/>
    <cge:TPSR_Ref TObjectID="44988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-287805" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4501.000000 1085.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287805" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44992"/>
     <cge:Term_Ref ObjectID="22775"/>
    <cge:TPSR_Ref TObjectID="44992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-287806" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4501.000000 1085.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287806" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44992"/>
     <cge:Term_Ref ObjectID="22775"/>
    <cge:TPSR_Ref TObjectID="44992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-287804" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4501.000000 1085.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287804" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44992"/>
     <cge:Term_Ref ObjectID="22775"/>
    <cge:TPSR_Ref TObjectID="44992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3155.000000 179.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44996"/>
     <cge:Term_Ref ObjectID="22783"/>
    <cge:TPSR_Ref TObjectID="44996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-287753" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3180.000000 -189.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287753" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44952"/>
     <cge:Term_Ref ObjectID="22649"/>
    <cge:TPSR_Ref TObjectID="44952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-287754" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3180.000000 -189.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287754" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44952"/>
     <cge:Term_Ref ObjectID="22649"/>
    <cge:TPSR_Ref TObjectID="44952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-287750" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3180.000000 -189.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287750" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44952"/>
     <cge:Term_Ref ObjectID="22649"/>
    <cge:TPSR_Ref TObjectID="44952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-267359" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 701.000000 -183.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267359" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44936"/>
     <cge:Term_Ref ObjectID="22613"/>
    <cge:TPSR_Ref TObjectID="44936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-267360" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 701.000000 -183.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267360" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44936"/>
     <cge:Term_Ref ObjectID="22613"/>
    <cge:TPSR_Ref TObjectID="44936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-267356" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 701.000000 -183.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267356" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44936"/>
     <cge:Term_Ref ObjectID="22613"/>
    <cge:TPSR_Ref TObjectID="44936"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="182" x="-175" y="-940"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="182" x="-175" y="-940"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-224" y="-957"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-224" y="-957"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_省调直调电厂_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="182" x="-175" y="-940"/></g>
   <g href="cx_索引_接线图_省地共调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-224" y="-957"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1035" x2="1035" y1="-368" y2="-368"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="747" x2="1404" y1="1499" y2="1499"/>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_YSQ.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 833.000000 972.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43407" ObjectName="SM-CX_YSQ.P1"/>
    <cge:TPSR_Ref TObjectID="43407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YSQ.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1069.000000 971.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43408" ObjectName="SM-CX_YSQ.P2"/>
    <cge:TPSR_Ref TObjectID="43408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YSQ.P3">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1322.000000 970.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43409" ObjectName="SM-CX_YSQ.P3"/>
    <cge:TPSR_Ref TObjectID="43409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YSQ.P4">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1551.000000 971.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43410" ObjectName="SM-CX_YSQ.P4"/>
    <cge:TPSR_Ref TObjectID="43410"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2796.000000 952.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3320.000000 953.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YSQ.CX_YSQ_325_P5">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3809.000000 978.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48820" ObjectName="SM-CX_YSQ.CX_YSQ_325_P5"/>
    <cge:TPSR_Ref TObjectID="48820"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YSQ.CX_YSQ_326">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4038.000000 979.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48821" ObjectName="SM-CX_YSQ.CX_YSQ_326"/>
    <cge:TPSR_Ref TObjectID="48821"/></metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1604.063218 -773.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 18.000000 1077.000000)" xlink:href="#load:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1773.000000 945.000000)" xlink:href="#load:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4260.000000 953.000000)" xlink:href="#load:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4491.000000 939.000000)" xlink:href="#load:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-220KV" id="g_2091150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1531,-456 1555,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_20a41d0@0" ObjectIDZND0="43150@1" Pin0InfoVect0LinkObjId="SW-267258_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20a41d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1531,-456 1555,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2091340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1591,-456 1609,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="43150@0" ObjectIDZND0="43148@x" ObjectIDZND1="43149@x" Pin0InfoVect0LinkObjId="SW-267256_0" Pin0InfoVect1LinkObjId="SW-267257_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267258_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1591,-456 1609,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2091530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1609,-483 1609,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="43148@1" ObjectIDZND0="43150@x" ObjectIDZND1="43149@x" Pin0InfoVect0LinkObjId="SW-267258_0" Pin0InfoVect1LinkObjId="SW-267257_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267256_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1609,-483 1609,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2091720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1609,-456 1609,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="43150@x" ObjectIDND1="43148@x" ObjectIDZND0="43149@0" Pin0InfoVect0LinkObjId="SW-267257_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-267258_0" Pin1InfoVect1LinkObjId="SW-267256_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1609,-456 1609,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_210bfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1530,-535 1554,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_210b970@0" ObjectIDZND0="43152@1" Pin0InfoVect0LinkObjId="SW-267260_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_210b970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1530,-535 1554,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_210c190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1590,-535 1608,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="43152@0" ObjectIDZND0="43151@x" ObjectIDZND1="43148@x" Pin0InfoVect0LinkObjId="SW-267259_0" Pin0InfoVect1LinkObjId="SW-267256_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1590,-535 1608,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_210c380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1609,-558 1609,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="43151@1" ObjectIDZND0="43152@x" ObjectIDZND1="43148@x" Pin0InfoVect0LinkObjId="SW-267260_0" Pin0InfoVect1LinkObjId="SW-267256_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267259_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1609,-558 1609,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_210c570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1609,-535 1609,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="43152@x" ObjectIDND1="43151@x" ObjectIDZND0="43148@0" Pin0InfoVect0LinkObjId="SW-267256_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-267260_0" Pin1InfoVect1LinkObjId="SW-267259_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1609,-535 1609,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_206cd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1531,-619 1555,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_210c760@0" ObjectIDZND0="43153@1" Pin0InfoVect0LinkObjId="SW-267261_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_210c760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1531,-619 1555,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_206cf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1591,-619 1609,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43153@0" ObjectIDZND0="43151@x" ObjectIDZND1="43154@x" ObjectIDZND2="g_20fa6c0@0" Pin0InfoVect0LinkObjId="SW-267259_0" Pin0InfoVect1LinkObjId="SW-267262_0" Pin0InfoVect2LinkObjId="g_20fa6c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267261_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1591,-619 1609,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_206d120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1609,-619 1609,-594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="43153@x" ObjectIDND1="43154@x" ObjectIDND2="g_20fa6c0@0" ObjectIDZND0="43151@0" Pin0InfoVect0LinkObjId="SW-267259_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-267261_0" Pin1InfoVect1LinkObjId="SW-267262_0" Pin1InfoVect2LinkObjId="g_20fa6c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1609,-619 1609,-594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20fae90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1630,-728 1609,-728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_20fa6c0@0" ObjectIDZND0="0@x" ObjectIDZND1="43153@x" ObjectIDZND2="43151@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-267261_0" Pin0InfoVect2LinkObjId="SW-267259_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20fa6c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1630,-728 1609,-728 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20fb080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1609,-619 1609,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="43153@x" ObjectIDND1="43151@x" ObjectIDZND0="43154@x" ObjectIDZND1="g_20fa6c0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-267262_0" Pin0InfoVect1LinkObjId="g_20fa6c0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-267261_0" Pin1InfoVect1LinkObjId="SW-267259_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1609,-619 1609,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20fb270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1522,-709 1609,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43154@0" ObjectIDZND0="43153@x" ObjectIDZND1="43151@x" ObjectIDZND2="g_20fa6c0@0" Pin0InfoVect0LinkObjId="SW-267261_0" Pin0InfoVect1LinkObjId="SW-267259_0" Pin0InfoVect2LinkObjId="g_20fa6c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267262_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1522,-709 1609,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20e6790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1486,-709 1457,-709 1457,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="43154@1" ObjectIDZND0="g_20e5050@0" Pin0InfoVect0LinkObjId="g_20e5050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267262_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1486,-709 1457,-709 1457,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2000110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1609,-778 1609,-728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_20fa6c0@0" ObjectIDZND1="43153@x" ObjectIDZND2="43151@x" Pin0InfoVect0LinkObjId="g_20fa6c0_0" Pin0InfoVect1LinkObjId="SW-267261_0" Pin0InfoVect2LinkObjId="SW-267259_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1609,-778 1609,-728 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2000300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1609,-728 1609,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_20fa6c0@0" ObjectIDND1="0@x" ObjectIDZND0="43153@x" ObjectIDZND1="43151@x" ObjectIDZND2="43154@x" Pin0InfoVect0LinkObjId="SW-267261_0" Pin0InfoVect1LinkObjId="SW-267259_0" Pin0InfoVect2LinkObjId="SW-267262_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20fa6c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1609,-728 1609,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2114de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="446,-149 470,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2114630@0" ObjectIDZND0="44947@0" Pin0InfoVect0LinkObjId="SW-287684_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2114630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="446,-149 470,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2114fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="506,-149 524,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44947@1" ObjectIDZND0="44946@x" ObjectIDZND1="44936@x" Pin0InfoVect0LinkObjId="SW-287683_0" Pin0InfoVect1LinkObjId="SW-287673_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287684_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="506,-149 524,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_21151c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,-126 524,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44946@1" ObjectIDZND0="44947@x" ObjectIDZND1="44936@x" Pin0InfoVect0LinkObjId="SW-287684_0" Pin0InfoVect1LinkObjId="SW-287673_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287683_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="524,-126 524,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_21153b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,-149 524,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="44947@x" ObjectIDND1="44946@x" ObjectIDZND0="44936@0" Pin0InfoVect0LinkObjId="SW-287673_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-287684_0" Pin1InfoVect1LinkObjId="SW-287683_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="524,-149 524,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_200b260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-65 471,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_200aab0@0" ObjectIDZND0="44948@0" Pin0InfoVect0LinkObjId="SW-287685_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_200aab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="447,-65 471,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_200b450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="507,-65 524,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="44948@1" ObjectIDZND0="44946@x" ObjectIDZND1="43195@x" Pin0InfoVect0LinkObjId="SW-287683_0" Pin0InfoVect1LinkObjId="g_230c630_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287685_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="507,-65 524,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_200b640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,-65 524,-90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="44948@x" ObjectIDND1="43195@x" ObjectIDZND0="44946@0" Pin0InfoVect0LinkObjId="SW-287683_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-287685_0" Pin1InfoVect1LinkObjId="g_230c630_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="524,-65 524,-90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_200c720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-311 471,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_200bfb0@0" ObjectIDZND0="43156@0" Pin0InfoVect0LinkObjId="SW-267267_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_200bfb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="447,-311 471,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_200c910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="507,-311 524,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="43156@1" ObjectIDZND0="43155@x" ObjectIDZND1="43197@0" Pin0InfoVect0LinkObjId="SW-267266_0" Pin0InfoVect1LinkObjId="g_200f9d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267267_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="507,-311 524,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_200f7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,-292 524,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="43155@1" ObjectIDZND0="43156@x" ObjectIDZND1="43197@0" Pin0InfoVect0LinkObjId="SW-267267_0" Pin0InfoVect1LinkObjId="g_200f9d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267266_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="524,-292 524,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_200f9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,-311 524,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="43156@x" ObjectIDND1="43155@x" ObjectIDZND0="43197@0" Pin0InfoVect0LinkObjId="g_1fd3e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-267267_0" Pin1InfoVect1LinkObjId="SW-267266_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="524,-311 524,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2010b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="617,47 527,47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" ObjectIDND0="43157@x" ObjectIDND1="g_200fbc0@0" ObjectIDND2="g_20101b0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-267269_0" Pin1InfoVect1LinkObjId="g_200fbc0_0" Pin1InfoVect2LinkObjId="g_20101b0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="617,47 527,47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fcb0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="672,98 672,114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="43157@0" ObjectIDZND0="g_1fca030@0" ObjectIDZND1="g_200fbc0@0" ObjectIDZND2="g_20101b0@0" Pin0InfoVect0LinkObjId="g_1fca030_0" Pin0InfoVect1LinkObjId="g_200fbc0_0" Pin0InfoVect2LinkObjId="g_20101b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267269_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="672,98 672,114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fcb2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="672,114 672,132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="earth" ObjectIDND0="43157@x" ObjectIDND1="g_200fbc0@0" ObjectIDND2="g_20101b0@0" ObjectIDZND0="g_1fca030@0" Pin0InfoVect0LinkObjId="g_1fca030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-267269_0" Pin1InfoVect1LinkObjId="g_200fbc0_0" Pin1InfoVect2LinkObjId="g_20101b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="672,114 672,132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fcbd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="672,62 672,47 648,47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="43157@1" ObjectIDZND0="g_200fbc0@0" ObjectIDZND1="g_20101b0@0" Pin0InfoVect0LinkObjId="g_200fbc0_0" Pin0InfoVect1LinkObjId="g_20101b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267269_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="672,62 672,47 648,47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fcbf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="648,47 648,62 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="43157@x" ObjectIDND1="g_20101b0@0" ObjectIDZND0="g_200fbc0@1" Pin0InfoVect0LinkObjId="g_200fbc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-267269_0" Pin1InfoVect1LinkObjId="g_20101b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="648,47 648,62 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fcca10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="648,102 648,114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="earth" EndDevType2="lightningRod" ObjectIDND0="g_200fbc0@0" ObjectIDZND0="43157@x" ObjectIDZND1="g_1fca030@0" ObjectIDZND2="g_20101b0@0" Pin0InfoVect0LinkObjId="SW-267269_0" Pin0InfoVect1LinkObjId="g_1fca030_0" Pin0InfoVect2LinkObjId="g_20101b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_200fbc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="648,102 648,114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fccc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="648,114 672,114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="earth" ObjectIDND0="g_200fbc0@0" ObjectIDND1="g_20101b0@0" ObjectIDZND0="43157@x" ObjectIDZND1="g_1fca030@0" Pin0InfoVect0LinkObjId="SW-267269_0" Pin0InfoVect1LinkObjId="g_1fca030_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_200fbc0_0" Pin1InfoVect1LinkObjId="g_20101b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="648,114 672,114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fcce50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="617,102 617,114 648,114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="earth" ObjectIDND0="g_20101b0@1" ObjectIDZND0="g_200fbc0@0" ObjectIDZND1="43157@x" ObjectIDZND2="g_1fca030@0" Pin0InfoVect0LinkObjId="g_200fbc0_0" Pin0InfoVect1LinkObjId="SW-267269_0" Pin0InfoVect2LinkObjId="g_1fca030_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20101b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="617,102 617,114 648,114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fcd8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="648,47 617,47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="43157@x" ObjectIDND1="g_200fbc0@0" ObjectIDZND0="g_20101b0@0" Pin0InfoVect0LinkObjId="g_20101b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-267269_0" Pin1InfoVect1LinkObjId="g_200fbc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="648,47 617,47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fcdb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="617,47 617,59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="43157@x" ObjectIDND1="g_200fbc0@0" ObjectIDZND0="g_20101b0@0" Pin0InfoVect0LinkObjId="g_20101b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-267269_0" Pin1InfoVect1LinkObjId="g_200fbc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="617,47 617,59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fce910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="400,139 400,70 468,70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1fcdd20@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fcdd20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="400,139 400,70 468,70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fceb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="564,95 502,95 502,131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1fcfcb0@0" Pin0InfoVect0LinkObjId="g_1fcfcb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="564,95 502,95 502,131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20eff80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="502,165 502,185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1fcfcb0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fcfcb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="502,165 502,185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20f01a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="501,213 501,232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_20f03c0@0" Pin0InfoVect0LinkObjId="g_20f03c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="501,213 501,232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20f8760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,393 524,406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43160@1" ObjectIDZND0="43158@1" Pin0InfoVect0LinkObjId="SW-267270_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267271_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="524,393 524,406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20d71d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="27,676 27,694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43183@0" ObjectIDZND0="43185@1" Pin0InfoVect0LinkObjId="SW-267304_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267303_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="27,676 27,694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20d7e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="77,760 77,740 27,740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_20d73f0@0" ObjectIDZND0="43185@x" ObjectIDZND1="43186@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-267304_0" Pin0InfoVect1LinkObjId="SW-267305_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20d73f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="77,760 77,740 27,740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20d8080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="27,556 27,617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43198@0" ObjectIDZND0="43184@0" Pin0InfoVect0LinkObjId="SW-267304_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_230a640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="27,556 27,617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20d82e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="27,634 27,649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43184@1" ObjectIDZND0="43183@1" Pin0InfoVect0LinkObjId="SW-267303_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267304_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="27,634 27,649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20d8540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,458 524,433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43159@1" ObjectIDZND0="43158@0" Pin0InfoVect0LinkObjId="SW-267270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267271_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="524,458 524,433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20d9230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="27,711 27,740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="43185@0" ObjectIDZND0="g_20d73f0@0" ObjectIDZND1="43186@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_20d73f0_0" Pin0InfoVect1LinkObjId="SW-267305_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267304_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="27,711 27,740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b6490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-69,805 -69,790 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_20b5a60@0" ObjectIDZND0="43186@0" Pin0InfoVect0LinkObjId="SW-267305_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20b5a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-69,805 -69,790 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b66f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-23,740 -69,740 -69,754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_20d73f0@0" ObjectIDND1="43185@x" ObjectIDND2="g_20b7420@0" ObjectIDZND0="43186@1" Pin0InfoVect0LinkObjId="SW-267305_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20d73f0_0" Pin1InfoVect1LinkObjId="SW-267304_0" Pin1InfoVect2LinkObjId="g_20b7420_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-23,740 -69,740 -69,754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b71c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-23,740 27,740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43186@x" ObjectIDND1="0@x" ObjectIDZND0="g_20d73f0@0" ObjectIDZND1="43185@x" ObjectIDZND2="g_20b7420@0" Pin0InfoVect0LinkObjId="g_20d73f0_0" Pin0InfoVect1LinkObjId="SW-267304_0" Pin0InfoVect2LinkObjId="g_20b7420_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-267305_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-23,740 27,740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b77f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="27,740 27,823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_20d73f0@0" ObjectIDND1="43185@x" ObjectIDND2="43186@x" ObjectIDZND0="g_20b7420@1" Pin0InfoVect0LinkObjId="g_20b7420_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20d73f0_0" Pin1InfoVect1LinkObjId="SW-267304_0" Pin1InfoVect2LinkObjId="SW-267305_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="27,740 27,823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b7f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="27,903 27,950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_20b7420@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20b7420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="27,903 27,950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20b8d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="27,1001 27,1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_20b8830@0" Pin0InfoVect0LinkObjId="g_20b8830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="27,1001 27,1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20b8ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="27,1059 27,1082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_20b8830@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20b8830_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="27,1059 27,1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b9e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="314,676 314,694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43187@0" ObjectIDZND0="43189@1" Pin0InfoVect0LinkObjId="SW-267309_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267308_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="314,676 314,694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ba3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="364,760 364,740 314,740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_20ba0a0@0" ObjectIDZND0="43189@x" ObjectIDZND1="43190@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-267309_0" Pin0InfoVect1LinkObjId="SW-267310_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20ba0a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="364,760 364,740 314,740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ba5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="314,556 314,617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43198@0" ObjectIDZND0="43188@0" Pin0InfoVect0LinkObjId="SW-267309_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_230a640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="314,556 314,617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ba830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="314,634 314,649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43188@1" ObjectIDZND0="43187@1" Pin0InfoVect0LinkObjId="SW-267308_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267309_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="314,634 314,649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20baa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="314,711 314,740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="43189@0" ObjectIDZND0="g_20ba0a0@0" ObjectIDZND1="43190@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_20ba0a0_0" Pin0InfoVect1LinkObjId="SW-267310_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267309_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="314,711 314,740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20bcb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="218,805 218,790 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_20bc150@0" ObjectIDZND0="43190@0" Pin0InfoVect0LinkObjId="SW-267310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20bc150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="218,805 218,790 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20bcde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="264,740 218,740 218,754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_20ba0a0@0" ObjectIDND1="43189@x" ObjectIDND2="g_20bd2a0@0" ObjectIDZND0="43190@1" Pin0InfoVect0LinkObjId="SW-267310_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20ba0a0_0" Pin1InfoVect1LinkObjId="SW-267309_0" Pin1InfoVect2LinkObjId="g_20bd2a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="264,740 218,740 218,754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20bd040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="264,740 314,740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43190@x" ObjectIDND1="0@x" ObjectIDZND0="g_20ba0a0@0" ObjectIDZND1="43189@x" ObjectIDZND2="g_20bd2a0@0" Pin0InfoVect0LinkObjId="g_20ba0a0_0" Pin0InfoVect1LinkObjId="SW-267309_0" Pin0InfoVect2LinkObjId="g_20bd2a0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-267310_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="264,740 314,740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20bd670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="314,740 314,823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_20ba0a0@0" ObjectIDND1="43189@x" ObjectIDND2="43190@x" ObjectIDZND0="g_20bd2a0@1" Pin0InfoVect0LinkObjId="g_20bd2a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20ba0a0_0" Pin1InfoVect1LinkObjId="SW-267309_0" Pin1InfoVect2LinkObjId="SW-267310_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="314,740 314,823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20bda00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="314,903 314,950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_20bd2a0@0" ObjectIDZND0="g_2063530@0" Pin0InfoVect0LinkObjId="g_2063530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20bd2a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="314,903 314,950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ac2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,694 524,634 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="43162@1" ObjectIDZND0="43161@1" Pin0InfoVect0LinkObjId="SW-267277_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267277_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="524,694 524,634 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20ae5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="264,779 264,767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_20bacf0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20bacf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="264,779 264,767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ae840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="264,757 264,740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="43190@x" ObjectIDZND1="g_20ba0a0@0" ObjectIDZND2="43189@x" Pin0InfoVect0LinkObjId="SW-267310_0" Pin0InfoVect1LinkObjId="g_20ba0a0_0" Pin0InfoVect2LinkObjId="SW-267309_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="264,757 264,740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20af390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-23,779 -23,768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_20b4600@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20b4600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-23,779 -23,768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20af5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-23,758 -23,740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="43186@x" ObjectIDZND1="g_20d73f0@0" ObjectIDZND2="43185@x" Pin0InfoVect0LinkObjId="SW-267305_0" Pin0InfoVect1LinkObjId="g_20d73f0_0" Pin0InfoVect2LinkObjId="SW-267304_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-23,758 -23,740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20af850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,308 574,296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_1fced50@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fced50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="574,308 574,296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20b03a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,637 576,621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_20ac4a0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20ac4a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,637 576,621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b0600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,586 577,586 577,610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="43198@0" ObjectIDND1="43161@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_230a640_0" Pin1InfoVect1LinkObjId="SW-267277_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="524,586 577,586 577,610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b10f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,556 524,586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="43198@0" ObjectIDZND0="43161@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-267277_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_230a640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="524,556 524,586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b1350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,586 524,617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="43198@0" ObjectIDND1="0@x" ObjectIDZND0="43161@0" Pin0InfoVect0LinkObjId="SW-267277_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_230a640_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="524,586 524,617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b2bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,711 524,752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="capacitor" EndDevType2="breaker" ObjectIDND0="43162@0" ObjectIDZND0="g_20b15b0@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_20b15b0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267277_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="524,711 524,752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2026970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="627,797 627,780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_20b2e50@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20b2e50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="627,797 627,780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2026bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,752 627,752 627,770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="capacitor" ObjectIDND0="g_20b15b0@0" ObjectIDND1="43162@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20b15b0_0" Pin1InfoVect1LinkObjId="SW-267277_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="574,752 627,752 627,770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20276c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,772 574,752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="capacitor" ObjectIDND0="g_20b15b0@0" ObjectIDZND0="43162@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-267277_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20b15b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="574,772 574,752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2027920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,752 524,752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_20b15b0@0" ObjectIDND1="0@x" ObjectIDZND0="43162@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-267277_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20b15b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="574,752 524,752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2029bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,752 524,864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="breaker" ObjectIDND0="43162@x" ObjectIDND1="g_20b15b0@0" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-267277_0" Pin1InfoVect1LinkObjId="g_20b15b0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="524,752 524,864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2029e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,891 524,938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_202a760@0" Pin0InfoVect0LinkObjId="g_202a760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="524,891 524,938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2073720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="838,677 838,695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43163@0" ObjectIDZND0="43165@1" Pin0InfoVect0LinkObjId="SW-267279_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267278_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="838,677 838,695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2074730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="888,761 888,741 838,741 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2073980@0" ObjectIDZND0="43165@x" ObjectIDZND1="g_2079980@0" ObjectIDZND2="43166@x" Pin0InfoVect0LinkObjId="SW-267279_0" Pin0InfoVect1LinkObjId="g_2079980_0" Pin0InfoVect2LinkObjId="SW-267280_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2073980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="888,761 888,741 838,741 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2074990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="838,556 838,618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43198@0" ObjectIDZND0="43164@0" Pin0InfoVect0LinkObjId="SW-267279_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_230a640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="838,556 838,618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2074bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="838,635 838,650 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43164@1" ObjectIDZND0="43163@1" Pin0InfoVect0LinkObjId="SW-267278_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267279_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="838,635 838,650 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2074e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="838,712 838,741 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="43165@0" ObjectIDZND0="g_2073980@0" ObjectIDZND1="g_2079980@0" ObjectIDZND2="43166@x" Pin0InfoVect0LinkObjId="g_2073980_0" Pin0InfoVect1LinkObjId="g_2079980_0" Pin0InfoVect2LinkObjId="SW-267280_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267279_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="838,712 838,741 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2079260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="742,806 742,791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_20787d0@0" ObjectIDZND0="43166@0" Pin0InfoVect0LinkObjId="SW-267280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20787d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="742,806 742,791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20794c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="788,741 742,741 742,755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="43165@x" ObjectIDND1="g_2073980@0" ObjectIDND2="g_2079980@0" ObjectIDZND0="43166@1" Pin0InfoVect0LinkObjId="SW-267280_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-267279_0" Pin1InfoVect1LinkObjId="g_2073980_0" Pin1InfoVect2LinkObjId="g_2079980_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="788,741 742,741 742,755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2079720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="788,741 838,741 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="43166@x" ObjectIDND1="0@x" ObjectIDZND0="43165@x" ObjectIDZND1="g_2073980@0" ObjectIDZND2="g_2079980@0" Pin0InfoVect0LinkObjId="SW-267279_0" Pin0InfoVect1LinkObjId="g_2073980_0" Pin0InfoVect2LinkObjId="g_2079980_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-267280_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="788,741 838,741 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_207a3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="838,741 838,824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="43165@x" ObjectIDND1="g_2073980@0" ObjectIDND2="43166@x" ObjectIDZND0="g_2079980@1" Pin0InfoVect0LinkObjId="g_2079980_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-267279_0" Pin1InfoVect1LinkObjId="g_2073980_0" Pin1InfoVect2LinkObjId="SW-267280_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="838,741 838,824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_207a750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="838,904 838,951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_2079980@0" ObjectIDZND0="43407@0" Pin0InfoVect0LinkObjId="SM-CX_YSQ.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2079980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="838,904 838,951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_207b2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="788,780 788,768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_20750b0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20750b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="788,780 788,768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_207b500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="788,758 788,741 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="43165@x" ObjectIDZND1="g_2073980@0" ObjectIDZND2="g_2079980@0" Pin0InfoVect0LinkObjId="SW-267279_0" Pin0InfoVect1LinkObjId="g_2073980_0" Pin0InfoVect2LinkObjId="g_2079980_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="788,758 788,741 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2021500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1074,676 1074,694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43167@0" ObjectIDZND0="43169@1" Pin0InfoVect0LinkObjId="SW-267284_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267283_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1074,676 1074,694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2022510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1124,760 1124,740 1074,740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2021760@0" ObjectIDZND0="43169@x" ObjectIDZND1="g_20933e0@0" ObjectIDZND2="43170@x" Pin0InfoVect0LinkObjId="SW-267284_0" Pin0InfoVect1LinkObjId="g_20933e0_0" Pin0InfoVect2LinkObjId="SW-267285_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2021760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1124,760 1124,740 1074,740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2022770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1074,556 1074,617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43198@0" ObjectIDZND0="43168@0" Pin0InfoVect0LinkObjId="SW-267284_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_230a640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1074,556 1074,617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20229d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1074,634 1074,649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43168@1" ObjectIDZND0="43167@1" Pin0InfoVect0LinkObjId="SW-267283_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267284_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1074,634 1074,649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2022c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1074,711 1074,740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="43169@0" ObjectIDZND0="g_2021760@0" ObjectIDZND1="g_20933e0@0" ObjectIDZND2="43170@x" Pin0InfoVect0LinkObjId="g_2021760_0" Pin0InfoVect1LinkObjId="g_20933e0_0" Pin0InfoVect2LinkObjId="SW-267285_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267284_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1074,711 1074,740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2092cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="978,805 978,790 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2092230@0" ObjectIDZND0="43170@0" Pin0InfoVect0LinkObjId="SW-267285_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2092230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="978,805 978,790 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2092f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1024,740 978,740 978,754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="43169@x" ObjectIDND1="g_2021760@0" ObjectIDND2="g_20933e0@0" ObjectIDZND0="43170@1" Pin0InfoVect0LinkObjId="SW-267285_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-267284_0" Pin1InfoVect1LinkObjId="g_2021760_0" Pin1InfoVect2LinkObjId="g_20933e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1024,740 978,740 978,754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2093180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1024,740 1074,740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="43170@x" ObjectIDND1="0@x" ObjectIDZND0="43169@x" ObjectIDZND1="g_2021760@0" ObjectIDZND2="g_20933e0@0" Pin0InfoVect0LinkObjId="SW-267284_0" Pin0InfoVect1LinkObjId="g_2021760_0" Pin0InfoVect2LinkObjId="g_20933e0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-267285_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1024,740 1074,740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2093e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1074,740 1074,823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="43169@x" ObjectIDND1="g_2021760@0" ObjectIDND2="43170@x" ObjectIDZND0="g_20933e0@1" Pin0InfoVect0LinkObjId="g_20933e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-267284_0" Pin1InfoVect1LinkObjId="g_2021760_0" Pin1InfoVect2LinkObjId="SW-267285_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1074,740 1074,823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20941b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1074,903 1074,950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_20933e0@0" ObjectIDZND0="43408@0" Pin0InfoVect0LinkObjId="SM-CX_YSQ.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20933e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1074,903 1074,950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2094d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1024,779 1024,767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_2022e90@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2022e90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1024,779 1024,767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2094f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1024,757 1024,740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="43169@x" ObjectIDZND1="g_2021760@0" ObjectIDZND2="g_20933e0@0" Pin0InfoVect0LinkObjId="SW-267284_0" Pin0InfoVect1LinkObjId="g_2021760_0" Pin0InfoVect2LinkObjId="g_20933e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1024,757 1024,740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_209e4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1327,675 1327,693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43171@0" ObjectIDZND0="43173@1" Pin0InfoVect0LinkObjId="SW-267289_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267288_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1327,675 1327,693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_209f4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1377,759 1377,739 1327,739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_209e730@0" ObjectIDZND0="43173@x" ObjectIDZND1="g_20c5d30@0" ObjectIDZND2="43174@x" Pin0InfoVect0LinkObjId="SW-267289_0" Pin0InfoVect1LinkObjId="g_20c5d30_0" Pin0InfoVect2LinkObjId="SW-267290_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_209e730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1377,759 1377,739 1327,739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_209f740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1327,556 1327,616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43198@0" ObjectIDZND0="43172@0" Pin0InfoVect0LinkObjId="SW-267289_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_230a640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1327,556 1327,616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c0fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1327,633 1327,648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43172@1" ObjectIDZND0="43171@1" Pin0InfoVect0LinkObjId="SW-267288_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267289_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1327,633 1327,648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c1200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1327,710 1327,739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="43173@0" ObjectIDZND0="g_209e730@0" ObjectIDZND1="g_20c5d30@0" ObjectIDZND2="43174@x" Pin0InfoVect0LinkObjId="g_209e730_0" Pin0InfoVect1LinkObjId="g_20c5d30_0" Pin0InfoVect2LinkObjId="SW-267290_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267289_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1327,710 1327,739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c5610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1231,804 1231,789 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_20c4b80@0" ObjectIDZND0="43174@0" Pin0InfoVect0LinkObjId="SW-267290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20c4b80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1231,804 1231,789 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c5870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1277,739 1231,739 1231,753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="43173@x" ObjectIDND1="g_209e730@0" ObjectIDND2="g_20c5d30@0" ObjectIDZND0="43174@1" Pin0InfoVect0LinkObjId="SW-267290_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-267289_0" Pin1InfoVect1LinkObjId="g_209e730_0" Pin1InfoVect2LinkObjId="g_20c5d30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1277,739 1231,739 1231,753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c5ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1277,739 1327,739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="43174@x" ObjectIDND1="0@x" ObjectIDZND0="43173@x" ObjectIDZND1="g_209e730@0" ObjectIDZND2="g_20c5d30@0" Pin0InfoVect0LinkObjId="SW-267289_0" Pin0InfoVect1LinkObjId="g_209e730_0" Pin0InfoVect2LinkObjId="g_20c5d30_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-267290_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1277,739 1327,739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c6750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1327,739 1327,822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="43173@x" ObjectIDND1="g_209e730@0" ObjectIDND2="43174@x" ObjectIDZND0="g_20c5d30@1" Pin0InfoVect0LinkObjId="g_20c5d30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-267289_0" Pin1InfoVect1LinkObjId="g_209e730_0" Pin1InfoVect2LinkObjId="SW-267290_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1327,739 1327,822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c6b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1327,902 1327,949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_20c5d30@0" ObjectIDZND0="43409@0" Pin0InfoVect0LinkObjId="SM-CX_YSQ.P3_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20c5d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1327,902 1327,949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20c7650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1277,778 1277,766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_20c1460@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20c1460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1277,778 1277,766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c78b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1277,756 1277,739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="43173@x" ObjectIDZND1="g_209e730@0" ObjectIDZND2="g_20c5d30@0" Pin0InfoVect0LinkObjId="SW-267289_0" Pin0InfoVect1LinkObjId="g_209e730_0" Pin0InfoVect2LinkObjId="g_20c5d30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1277,756 1277,739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_207e150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1556,676 1556,694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43175@0" ObjectIDZND0="43177@1" Pin0InfoVect0LinkObjId="SW-267294_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267293_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1556,676 1556,694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_207f160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1606,760 1606,740 1556,740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_207e3b0@0" ObjectIDZND0="43177@x" ObjectIDZND1="g_20843b0@0" ObjectIDZND2="43178@x" Pin0InfoVect0LinkObjId="SW-267294_0" Pin0InfoVect1LinkObjId="g_20843b0_0" Pin0InfoVect2LinkObjId="SW-267295_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_207e3b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1606,760 1606,740 1556,740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_207f3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1556,556 1556,617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43198@0" ObjectIDZND0="43176@0" Pin0InfoVect0LinkObjId="SW-267294_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_230a640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1556,556 1556,617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_207f620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1556,634 1556,649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43176@1" ObjectIDZND0="43175@1" Pin0InfoVect0LinkObjId="SW-267293_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267294_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1556,634 1556,649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_207f880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1556,711 1556,740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="43177@0" ObjectIDZND0="g_207e3b0@0" ObjectIDZND1="g_20843b0@0" ObjectIDZND2="43178@x" Pin0InfoVect0LinkObjId="g_207e3b0_0" Pin0InfoVect1LinkObjId="g_20843b0_0" Pin0InfoVect2LinkObjId="SW-267295_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267294_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1556,711 1556,740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2083c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1460,805 1460,790 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2083200@0" ObjectIDZND0="43178@0" Pin0InfoVect0LinkObjId="SW-267295_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2083200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1460,805 1460,790 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2083ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1506,740 1460,740 1460,754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="43177@x" ObjectIDND1="g_207e3b0@0" ObjectIDND2="g_20843b0@0" ObjectIDZND0="43178@1" Pin0InfoVect0LinkObjId="SW-267295_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-267294_0" Pin1InfoVect1LinkObjId="g_207e3b0_0" Pin1InfoVect2LinkObjId="g_20843b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1506,740 1460,740 1460,754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2084150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1506,740 1556,740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="43178@x" ObjectIDND1="0@x" ObjectIDZND0="43177@x" ObjectIDZND1="g_207e3b0@0" ObjectIDZND2="g_20843b0@0" Pin0InfoVect0LinkObjId="SW-267294_0" Pin0InfoVect1LinkObjId="g_207e3b0_0" Pin0InfoVect2LinkObjId="g_20843b0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-267295_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1506,740 1556,740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2084dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1556,740 1556,823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="43177@x" ObjectIDND1="g_207e3b0@0" ObjectIDND2="43178@x" ObjectIDZND0="g_20843b0@1" Pin0InfoVect0LinkObjId="g_20843b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-267294_0" Pin1InfoVect1LinkObjId="g_207e3b0_0" Pin1InfoVect2LinkObjId="SW-267295_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1556,740 1556,823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2085180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1556,903 1556,950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_20843b0@0" ObjectIDZND0="43410@0" Pin0InfoVect0LinkObjId="SM-CX_YSQ.P4_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20843b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1556,903 1556,950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2085cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1506,779 1506,767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_207fae0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_207fae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1506,779 1506,767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2085f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1506,757 1506,740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="43177@x" ObjectIDZND1="g_207e3b0@0" ObjectIDZND2="g_20843b0@0" Pin0InfoVect0LinkObjId="SW-267294_0" Pin0InfoVect1LinkObjId="g_207e3b0_0" Pin0InfoVect2LinkObjId="g_20843b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1506,757 1506,740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff2510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1782,676 1782,694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43179@0" ObjectIDZND0="43181@1" Pin0InfoVect0LinkObjId="SW-267299_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267298_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1782,676 1782,694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff3520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1832,760 1832,740 1782,740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1ff2770@0" ObjectIDZND0="43181@x" ObjectIDZND1="g_1ff8770@0" ObjectIDZND2="43182@x" Pin0InfoVect0LinkObjId="SW-267299_0" Pin0InfoVect1LinkObjId="g_1ff8770_0" Pin0InfoVect2LinkObjId="SW-267300_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ff2770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1832,760 1832,740 1782,740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff3780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1782,556 1782,617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43198@0" ObjectIDZND0="43180@0" Pin0InfoVect0LinkObjId="SW-267299_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_230a640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1782,556 1782,617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff39e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1782,634 1782,649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43180@1" ObjectIDZND0="43179@1" Pin0InfoVect0LinkObjId="SW-267298_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267299_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1782,634 1782,649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff3c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1782,711 1782,740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="43181@0" ObjectIDZND0="g_1ff2770@0" ObjectIDZND1="g_1ff8770@0" ObjectIDZND2="43182@x" Pin0InfoVect0LinkObjId="g_1ff2770_0" Pin0InfoVect1LinkObjId="g_1ff8770_0" Pin0InfoVect2LinkObjId="SW-267300_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267299_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1782,711 1782,740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff8050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1686,805 1686,790 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1ff75c0@0" ObjectIDZND0="43182@0" Pin0InfoVect0LinkObjId="SW-267300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ff75c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1686,805 1686,790 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff82b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1732,740 1686,740 1686,754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="43181@x" ObjectIDND1="g_1ff2770@0" ObjectIDND2="g_1ff8770@0" ObjectIDZND0="43182@1" Pin0InfoVect0LinkObjId="SW-267300_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-267299_0" Pin1InfoVect1LinkObjId="g_1ff2770_0" Pin1InfoVect2LinkObjId="g_1ff8770_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1732,740 1686,740 1686,754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff8510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1732,740 1782,740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="43182@x" ObjectIDND1="0@x" ObjectIDZND0="43181@x" ObjectIDZND1="g_1ff2770@0" ObjectIDZND2="g_1ff8770@0" Pin0InfoVect0LinkObjId="SW-267299_0" Pin0InfoVect1LinkObjId="g_1ff2770_0" Pin0InfoVect2LinkObjId="g_1ff8770_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-267300_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1732,740 1782,740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff9190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1782,740 1782,823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="43181@x" ObjectIDND1="g_1ff2770@0" ObjectIDND2="43182@x" ObjectIDZND0="g_1ff8770@1" Pin0InfoVect0LinkObjId="g_1ff8770_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-267299_0" Pin1InfoVect1LinkObjId="g_1ff2770_0" Pin1InfoVect2LinkObjId="SW-267300_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1782,740 1782,823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff9540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1782,903 1782,950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_1ff8770@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ff8770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1782,903 1782,950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ffa090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1732,779 1732,767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_1ff3ea0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ff3ea0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1732,779 1732,767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ffa2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1732,757 1732,740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="43181@x" ObjectIDZND1="g_1ff2770@0" ObjectIDZND2="g_1ff8770@0" Pin0InfoVect0LinkObjId="SW-267299_0" Pin0InfoVect1LinkObjId="g_1ff2770_0" Pin0InfoVect2LinkObjId="g_1ff8770_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1732,757 1732,740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_204dcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2013,675 2013,693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43191@0" ObjectIDZND0="43193@1" Pin0InfoVect0LinkObjId="SW-267314_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267313_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2013,675 2013,693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_204ec00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2063,759 2063,739 2013,739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_204df50@0" ObjectIDZND0="43193@x" ObjectIDZND1="g_2053b50@0" ObjectIDZND2="43194@x" Pin0InfoVect0LinkObjId="SW-267314_0" Pin0InfoVect1LinkObjId="g_2053b50_0" Pin0InfoVect2LinkObjId="SW-267315_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_204df50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2063,759 2063,739 2013,739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_204ee60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2013,556 2013,616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43198@0" ObjectIDZND0="43192@0" Pin0InfoVect0LinkObjId="SW-267314_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_230a640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2013,556 2013,616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_204f0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2013,633 2013,648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43192@1" ObjectIDZND0="43191@1" Pin0InfoVect0LinkObjId="SW-267313_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267314_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2013,633 2013,648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_204f320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2013,710 2013,739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="43193@0" ObjectIDZND0="g_204df50@0" ObjectIDZND1="g_2053b50@0" ObjectIDZND2="43194@x" Pin0InfoVect0LinkObjId="g_204df50_0" Pin0InfoVect1LinkObjId="g_2053b50_0" Pin0InfoVect2LinkObjId="SW-267315_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267314_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2013,710 2013,739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2053430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1917,804 1917,789 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2052a00@0" ObjectIDZND0="43194@0" Pin0InfoVect0LinkObjId="SW-267315_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2052a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1917,804 1917,789 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2053690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1963,739 1917,739 1917,753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="43193@x" ObjectIDND1="g_204df50@0" ObjectIDND2="g_2053b50@0" ObjectIDZND0="43194@1" Pin0InfoVect0LinkObjId="SW-267315_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-267314_0" Pin1InfoVect1LinkObjId="g_204df50_0" Pin1InfoVect2LinkObjId="g_2053b50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1963,739 1917,739 1917,753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20538f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1963,739 2013,739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="43194@x" ObjectIDND1="0@x" ObjectIDZND0="43193@x" ObjectIDZND1="g_204df50@0" ObjectIDZND2="g_2053b50@0" Pin0InfoVect0LinkObjId="SW-267314_0" Pin0InfoVect1LinkObjId="g_204df50_0" Pin0InfoVect2LinkObjId="g_2053b50_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-267315_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1963,739 2013,739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2054430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2013,739 2013,822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="43193@x" ObjectIDND1="g_204df50@0" ObjectIDND2="43194@x" ObjectIDZND0="g_2053b50@1" Pin0InfoVect0LinkObjId="g_2053b50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-267314_0" Pin1InfoVect1LinkObjId="g_204df50_0" Pin1InfoVect2LinkObjId="SW-267315_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2013,739 2013,822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2055230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1963,778 1963,766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_204f580@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_204f580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1963,778 1963,766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2055490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1963,756 1963,739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="43193@x" ObjectIDZND1="g_204df50@0" ObjectIDZND2="g_2053b50@0" Pin0InfoVect0LinkObjId="SW-267314_0" Pin0InfoVect1LinkObjId="g_204df50_0" Pin0InfoVect2LinkObjId="g_2053b50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1963,756 1963,739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_21d3690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2933,-141 2957,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_21d2c00@0" ObjectIDZND0="44956@0" Pin0InfoVect0LinkObjId="SW-287693_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21d2c00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2933,-141 2957,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_21d38f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2993,-141 3011,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44956@1" ObjectIDZND0="44955@x" ObjectIDZND1="44952@x" Pin0InfoVect0LinkObjId="SW-287692_0" Pin0InfoVect1LinkObjId="SW-287689_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287693_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2993,-141 3011,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_21d3b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3011,-118 3011,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44955@1" ObjectIDZND0="44956@x" ObjectIDZND1="44952@x" Pin0InfoVect0LinkObjId="SW-287693_0" Pin0InfoVect1LinkObjId="SW-287689_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287692_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3011,-118 3011,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_21d3db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3011,-141 3011,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="44955@x" ObjectIDND1="44956@x" ObjectIDZND0="44952@0" Pin0InfoVect0LinkObjId="SW-287689_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-287692_0" Pin1InfoVect1LinkObjId="SW-287693_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3011,-141 3011,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_21d6fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2934,-57 2958,-57 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_21d6540@0" ObjectIDZND0="44957@0" Pin0InfoVect0LinkObjId="SW-287694_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21d6540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2934,-57 2958,-57 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_21d7230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2994,-57 3011,-57 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="44957@1" ObjectIDZND0="44955@x" ObjectIDZND1="44996@x" Pin0InfoVect0LinkObjId="SW-287692_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287694_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2994,-57 3011,-57 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_21d7490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3011,-57 3011,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="44957@x" ObjectIDND1="44996@x" ObjectIDZND0="44955@0" Pin0InfoVect0LinkObjId="SW-287692_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-287694_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3011,-57 3011,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fd13f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2934,-303 2958,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1fd0c10@0" ObjectIDZND0="44954@0" Pin0InfoVect0LinkObjId="SW-287691_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fd0c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2934,-303 2958,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fd1620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2994,-303 3011,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="44954@1" ObjectIDZND0="44953@x" ObjectIDZND1="43197@0" Pin0InfoVect0LinkObjId="SW-287690_0" Pin0InfoVect1LinkObjId="g_200f9d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287691_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2994,-303 3011,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fd3bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3011,-284 3011,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="44953@1" ObjectIDZND0="44954@x" ObjectIDZND1="43197@0" Pin0InfoVect0LinkObjId="SW-287691_0" Pin0InfoVect1LinkObjId="g_200f9d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287690_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3011,-284 3011,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fd3e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3011,-303 3011,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="44953@x" ObjectIDND1="44954@x" ObjectIDZND0="43197@0" Pin0InfoVect0LinkObjId="g_200f9d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-287690_0" Pin1InfoVect1LinkObjId="SW-287691_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3011,-303 3011,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fd5370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3104,55 3014,55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="g_1fd4070@0" ObjectIDND1="0@x" ObjectIDND2="g_1fd48e0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1fd4070_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1fd48e0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3104,55 3014,55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fd8840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3159,106 3159,122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1fd7db0@0" ObjectIDZND1="g_1fd4070@0" ObjectIDZND2="g_1fd48e0@0" Pin0InfoVect0LinkObjId="g_1fd7db0_0" Pin0InfoVect1LinkObjId="g_1fd4070_0" Pin0InfoVect2LinkObjId="g_1fd48e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3159,106 3159,122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fd8aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3159,122 3159,140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDND1="g_1fd4070@0" ObjectIDND2="g_1fd48e0@0" ObjectIDZND0="g_1fd7db0@0" Pin0InfoVect0LinkObjId="g_1fd7db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1fd4070_0" Pin1InfoVect2LinkObjId="g_1fd48e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3159,122 3159,140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fd8d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3159,70 3159,55 3135,55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1fd4070@0" ObjectIDZND1="g_1fd48e0@0" Pin0InfoVect0LinkObjId="g_1fd4070_0" Pin0InfoVect1LinkObjId="g_1fd48e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3159,70 3159,55 3135,55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fd8f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3135,55 3135,70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_1fd48e0@0" ObjectIDZND0="g_1fd4070@1" Pin0InfoVect0LinkObjId="g_1fd4070_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1fd48e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3135,55 3135,70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fd91c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3135,110 3135,122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1fd4070@0" ObjectIDZND0="g_1fd7db0@0" ObjectIDZND1="0@x" ObjectIDZND2="g_1fd48e0@0" Pin0InfoVect0LinkObjId="g_1fd7db0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1fd48e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fd4070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3135,110 3135,122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fd9420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3135,122 3159,122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="earth" EndDevType1="switch" ObjectIDND0="g_1fd4070@0" ObjectIDND1="g_1fd48e0@0" ObjectIDZND0="g_1fd7db0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1fd7db0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fd4070_0" Pin1InfoVect1LinkObjId="g_1fd48e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3135,122 3159,122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fd9680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3104,110 3104,122 3135,122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1fd48e0@1" ObjectIDZND0="g_1fd7db0@0" ObjectIDZND1="0@x" ObjectIDZND2="g_1fd4070@0" Pin0InfoVect0LinkObjId="g_1fd7db0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1fd4070_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fd48e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3104,110 3104,122 3135,122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fd98e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3135,55 3104,55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1fd4070@0" ObjectIDND1="0@x" ObjectIDZND0="g_1fd48e0@0" Pin0InfoVect0LinkObjId="g_1fd48e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fd4070_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3135,55 3104,55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fd9b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3104,55 3104,67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1fd4070@0" ObjectIDND1="0@x" ObjectIDZND0="g_1fd48e0@0" Pin0InfoVect0LinkObjId="g_1fd48e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fd4070_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3104,55 3104,67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fdab50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2887,147 2887,78 2956,78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1fd9da0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fd9da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2887,147 2887,78 2956,78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fdadb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3011,100 2949,100 2949,136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_22d4ad0@0" Pin0InfoVect0LinkObjId="g_22d4ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3011,100 2949,100 2949,136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fe4f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3011,401 3011,414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44961@1" ObjectIDZND0="44959@1" Pin0InfoVect0LinkObjId="SW-287696_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287697_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3011,401 3011,414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb3130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2514,684 2514,702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="44962@0" ObjectIDZND0="44964@1" Pin0InfoVect0LinkObjId="SW-287704_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287703_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2514,684 2514,702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb3390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2960,302 2960,282 3011,282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="g_1fdc200@0" ObjectIDZND0="0@x" ObjectIDZND1="44961@x" ObjectIDZND2="44996@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-287697_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fdc200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2960,302 2960,282 3011,282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb35f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3010,139 3011,282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="capacitor" EndDevType2="switch" ObjectIDND0="44996@0" ObjectIDZND0="g_1fdc200@0" ObjectIDZND1="0@x" ObjectIDZND2="44961@x" Pin0InfoVect0LinkObjId="g_1fdc200_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-287697_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3010,139 3011,282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb3850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3011,282 3011,384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" BeginDevType2="transformer2" EndDevType0="switch" ObjectIDND0="g_1fdc200@0" ObjectIDND1="0@x" ObjectIDND2="44996@x" ObjectIDZND0="44961@0" Pin0InfoVect0LinkObjId="SW-287697_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1fdc200_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3011,282 3011,384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb4860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2564,768 2564,748 2514,748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1fb3ab0@0" ObjectIDZND0="44964@x" ObjectIDZND1="g_1fb9f70@0" ObjectIDZND2="44965@x" Pin0InfoVect0LinkObjId="SW-287704_0" Pin0InfoVect1LinkObjId="g_1fb9f70_0" Pin0InfoVect2LinkObjId="SW-287705_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fb3ab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2564,768 2564,748 2514,748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb4ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2514,564 2514,625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="44997@0" ObjectIDZND0="44963@0" Pin0InfoVect0LinkObjId="SW-287704_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22fc850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2514,564 2514,625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb4d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2514,642 2514,657 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44963@1" ObjectIDZND0="44962@1" Pin0InfoVect0LinkObjId="SW-287703_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287704_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2514,642 2514,657 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb4f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3011,564 3011,483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="44997@0" ObjectIDZND0="44960@0" Pin0InfoVect0LinkObjId="SW-287697_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22fc850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3011,564 3011,483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb51e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3011,466 3011,441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44960@1" ObjectIDZND0="44959@0" Pin0InfoVect0LinkObjId="SW-287696_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287697_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3011,466 3011,441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb5440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2514,719 2514,748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="44964@0" ObjectIDZND0="g_1fb3ab0@0" ObjectIDZND1="g_1fb9f70@0" ObjectIDZND2="44965@x" Pin0InfoVect0LinkObjId="g_1fb3ab0_0" Pin0InfoVect1LinkObjId="g_1fb9f70_0" Pin0InfoVect2LinkObjId="SW-287705_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287704_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2514,719 2514,748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb9850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2418,813 2418,798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1fb8dc0@0" ObjectIDZND0="44965@0" Pin0InfoVect0LinkObjId="SW-287705_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fb8dc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2418,813 2418,798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb9ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2464,748 2418,748 2418,762 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="44964@x" ObjectIDND1="g_1fb3ab0@0" ObjectIDND2="g_1fb9f70@0" ObjectIDZND0="44965@1" Pin0InfoVect0LinkObjId="SW-287705_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-287704_0" Pin1InfoVect1LinkObjId="g_1fb3ab0_0" Pin1InfoVect2LinkObjId="g_1fb9f70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2464,748 2418,748 2418,762 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb9d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2464,748 2514,748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="44965@x" ObjectIDND1="0@x" ObjectIDZND0="44964@x" ObjectIDZND1="g_1fb3ab0@0" ObjectIDZND2="g_1fb9f70@0" Pin0InfoVect0LinkObjId="SW-287704_0" Pin0InfoVect1LinkObjId="g_1fb3ab0_0" Pin0InfoVect2LinkObjId="g_1fb9f70_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-287705_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2464,748 2514,748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fba990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2514,748 2514,831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="44964@x" ObjectIDND1="g_1fb3ab0@0" ObjectIDND2="44965@x" ObjectIDZND0="g_1fb9f70@1" Pin0InfoVect0LinkObjId="g_1fb9f70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-287704_0" Pin1InfoVect1LinkObjId="g_1fb3ab0_0" Pin1InfoVect2LinkObjId="SW-287705_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2514,748 2514,831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fbad40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2514,911 2514,958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1fb9f70@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fb9f70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2514,911 2514,958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc2f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2801,684 2801,702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="44966@0" ObjectIDZND0="44968@1" Pin0InfoVect0LinkObjId="SW-287709_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287708_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2801,684 2801,702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc3f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2851,768 2851,748 2801,748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1fc31d0@0" ObjectIDZND0="44968@x" ObjectIDZND1="g_1fc91d0@0" ObjectIDZND2="44969@x" Pin0InfoVect0LinkObjId="SW-287709_0" Pin0InfoVect1LinkObjId="g_1fc91d0_0" Pin0InfoVect2LinkObjId="SW-287710_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fc31d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2851,768 2851,748 2801,748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc41e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2801,564 2801,625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="44997@0" ObjectIDZND0="44967@0" Pin0InfoVect0LinkObjId="SW-287709_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22fc850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2801,564 2801,625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc4440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2801,642 2801,657 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44967@1" ObjectIDZND0="44966@1" Pin0InfoVect0LinkObjId="SW-287708_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287709_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2801,642 2801,657 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc46a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2801,719 2801,748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="44968@0" ObjectIDZND0="g_1fc31d0@0" ObjectIDZND1="g_1fc91d0@0" ObjectIDZND2="44969@x" Pin0InfoVect0LinkObjId="g_1fc31d0_0" Pin0InfoVect1LinkObjId="g_1fc91d0_0" Pin0InfoVect2LinkObjId="SW-287710_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287709_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2801,719 2801,748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc8ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2705,813 2705,798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1fc8020@0" ObjectIDZND0="44969@0" Pin0InfoVect0LinkObjId="SW-287710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fc8020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2705,813 2705,798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc8d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2751,748 2705,748 2705,762 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="44968@x" ObjectIDND1="g_1fc31d0@0" ObjectIDND2="g_1fc91d0@0" ObjectIDZND0="44969@1" Pin0InfoVect0LinkObjId="SW-287710_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-287709_0" Pin1InfoVect1LinkObjId="g_1fc31d0_0" Pin1InfoVect2LinkObjId="g_1fc91d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2751,748 2705,748 2705,762 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc8f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2751,748 2801,748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="44969@x" ObjectIDND1="0@x" ObjectIDZND0="44968@x" ObjectIDZND1="g_1fc31d0@0" ObjectIDZND2="g_1fc91d0@0" Pin0InfoVect0LinkObjId="SW-287709_0" Pin0InfoVect1LinkObjId="g_1fc31d0_0" Pin0InfoVect2LinkObjId="g_1fc91d0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-287710_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2751,748 2801,748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc9bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2801,748 2801,831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="44968@x" ObjectIDND1="g_1fc31d0@0" ObjectIDND2="44969@x" ObjectIDZND0="g_1fc91d0@1" Pin0InfoVect0LinkObjId="g_1fc91d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-287709_0" Pin1InfoVect1LinkObjId="g_1fc31d0_0" Pin1InfoVect2LinkObjId="SW-287710_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2801,748 2801,831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f52550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2801,911 2801,931 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_1fc91d0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fc91d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2801,911 2801,931 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f54f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2751,787 2751,775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_1fc4900@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fc4900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2751,787 2751,775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f551f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2751,765 2751,748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="44968@x" ObjectIDZND1="g_1fc31d0@0" ObjectIDZND2="g_1fc91d0@0" Pin0InfoVect0LinkObjId="SW-287709_0" Pin0InfoVect1LinkObjId="g_1fc31d0_0" Pin0InfoVect2LinkObjId="g_1fc91d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2751,765 2751,748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f55d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2464,787 2464,776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_1fb56a0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fb56a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2464,787 2464,776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f55fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2464,766 2464,748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="44964@x" ObjectIDZND1="g_1fb3ab0@0" ObjectIDZND2="g_1fb9f70@0" Pin0InfoVect0LinkObjId="SW-287704_0" Pin0InfoVect1LinkObjId="g_1fb3ab0_0" Pin0InfoVect2LinkObjId="g_1fb9f70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2464,766 2464,748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f56200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3061,316 3061,304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_1fdb010@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fdb010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3061,316 3061,304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f56460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3061,294 3061,282 3011,282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="0@0" ObjectIDZND0="g_1fdc200@0" ObjectIDZND1="44961@x" ObjectIDZND2="44996@x" Pin0InfoVect0LinkObjId="g_1fdc200_0" Pin0InfoVect1LinkObjId="SW-287697_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3061,294 3061,282 3011,282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f5e8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3325,685 3325,703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="44974@0" ObjectIDZND0="44976@1" Pin0InfoVect0LinkObjId="SW-287719_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287718_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3325,685 3325,703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f5f8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3375,769 3375,749 3325,749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1f5eb40@0" ObjectIDZND0="44976@x" ObjectIDZND1="g_1f64b40@0" ObjectIDZND2="44977@x" Pin0InfoVect0LinkObjId="SW-287719_0" Pin0InfoVect1LinkObjId="g_1f64b40_0" Pin0InfoVect2LinkObjId="SW-287720_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f5eb40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3375,769 3375,749 3325,749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f5fb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3325,564 3325,626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="44997@0" ObjectIDZND0="44975@0" Pin0InfoVect0LinkObjId="SW-287719_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22fc850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3325,564 3325,626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f5fdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3325,643 3325,658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44975@1" ObjectIDZND0="44974@1" Pin0InfoVect0LinkObjId="SW-287718_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287719_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3325,643 3325,658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f60010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3325,720 3325,749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="44976@0" ObjectIDZND0="g_1f5eb40@0" ObjectIDZND1="g_1f64b40@0" ObjectIDZND2="44977@x" Pin0InfoVect0LinkObjId="g_1f5eb40_0" Pin0InfoVect1LinkObjId="g_1f64b40_0" Pin0InfoVect2LinkObjId="SW-287720_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287719_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3325,720 3325,749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f64420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3229,814 3229,799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1f63990@0" ObjectIDZND0="44977@0" Pin0InfoVect0LinkObjId="SW-287720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f63990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3229,814 3229,799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f64680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3275,749 3229,749 3229,763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="44976@x" ObjectIDND1="g_1f5eb40@0" ObjectIDND2="g_1f64b40@0" ObjectIDZND0="44977@1" Pin0InfoVect0LinkObjId="SW-287720_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-287719_0" Pin1InfoVect1LinkObjId="g_1f5eb40_0" Pin1InfoVect2LinkObjId="g_1f64b40_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3275,749 3229,749 3229,763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f648e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3275,749 3325,749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="44977@x" ObjectIDND1="0@x" ObjectIDZND0="44976@x" ObjectIDZND1="g_1f5eb40@0" ObjectIDZND2="g_1f64b40@0" Pin0InfoVect0LinkObjId="SW-287719_0" Pin0InfoVect1LinkObjId="g_1f5eb40_0" Pin0InfoVect2LinkObjId="g_1f64b40_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-287720_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3275,749 3325,749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f65560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3325,749 3325,832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="44976@x" ObjectIDND1="g_1f5eb40@0" ObjectIDND2="44977@x" ObjectIDZND0="g_1f64b40@1" Pin0InfoVect0LinkObjId="g_1f64b40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-287719_0" Pin1InfoVect1LinkObjId="g_1f5eb40_0" Pin1InfoVect2LinkObjId="SW-287720_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3325,749 3325,832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f65910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3325,912 3325,932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_1f64b40@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f64b40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3325,912 3325,932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f66460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3275,788 3275,776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_1f60270@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f60270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3275,788 3275,776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f666c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3275,766 3275,749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="44976@x" ObjectIDZND1="g_1f5eb40@0" ObjectIDZND2="g_1f64b40@0" Pin0InfoVect0LinkObjId="SW-287719_0" Pin0InfoVect1LinkObjId="g_1f5eb40_0" Pin0InfoVect2LinkObjId="g_1f64b40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3275,766 3275,749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f6e8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,683 3814,701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="44980@0" ObjectIDZND0="44982@1" Pin0InfoVect0LinkObjId="SW-287725_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287724_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,683 3814,701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f6f900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3864,767 3864,747 3814,747 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1f6eb50@0" ObjectIDZND0="44982@x" ObjectIDZND1="g_1f74b50@0" ObjectIDZND2="44983@x" Pin0InfoVect0LinkObjId="SW-287725_0" Pin0InfoVect1LinkObjId="g_1f74b50_0" Pin0InfoVect2LinkObjId="SW-287726_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f6eb50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3864,767 3864,747 3814,747 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f6fb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,564 3814,624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="44997@0" ObjectIDZND0="44981@0" Pin0InfoVect0LinkObjId="SW-287725_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22fc850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,564 3814,624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f6fdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,641 3814,656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44981@1" ObjectIDZND0="44980@1" Pin0InfoVect0LinkObjId="SW-287724_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287725_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,641 3814,656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f70020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,718 3814,747 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="44982@0" ObjectIDZND0="g_1f6eb50@0" ObjectIDZND1="g_1f74b50@0" ObjectIDZND2="44983@x" Pin0InfoVect0LinkObjId="g_1f6eb50_0" Pin0InfoVect1LinkObjId="g_1f74b50_0" Pin0InfoVect2LinkObjId="SW-287726_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287725_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3814,718 3814,747 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f74430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3718,812 3718,797 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1f739a0@0" ObjectIDZND0="44983@0" Pin0InfoVect0LinkObjId="SW-287726_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f739a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3718,812 3718,797 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f74690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3764,747 3718,747 3718,761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="44982@x" ObjectIDND1="g_1f6eb50@0" ObjectIDND2="g_1f74b50@0" ObjectIDZND0="44983@1" Pin0InfoVect0LinkObjId="SW-287726_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-287725_0" Pin1InfoVect1LinkObjId="g_1f6eb50_0" Pin1InfoVect2LinkObjId="g_1f74b50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3764,747 3718,747 3718,761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f748f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3764,747 3814,747 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="44983@x" ObjectIDND1="0@x" ObjectIDZND0="44982@x" ObjectIDZND1="g_1f6eb50@0" ObjectIDZND2="g_1f74b50@0" Pin0InfoVect0LinkObjId="SW-287725_0" Pin0InfoVect1LinkObjId="g_1f6eb50_0" Pin0InfoVect2LinkObjId="g_1f74b50_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-287726_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3764,747 3814,747 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f75570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,747 3814,830 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="44982@x" ObjectIDND1="g_1f6eb50@0" ObjectIDND2="44983@x" ObjectIDZND0="g_1f74b50@1" Pin0InfoVect0LinkObjId="g_1f74b50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-287725_0" Pin1InfoVect1LinkObjId="g_1f6eb50_0" Pin1InfoVect2LinkObjId="SW-287726_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,747 3814,830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f75920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,910 3814,957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_1f74b50@0" ObjectIDZND0="48820@0" Pin0InfoVect0LinkObjId="SM-CX_YSQ.CX_YSQ_325_P5_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f74b50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,910 3814,957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f76470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3764,786 3764,774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_1f70280@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f70280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3764,786 3764,774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f766d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3764,764 3764,747 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="44982@x" ObjectIDZND1="g_1f6eb50@0" ObjectIDZND2="g_1f74b50@0" Pin0InfoVect0LinkObjId="SW-287725_0" Pin0InfoVect1LinkObjId="g_1f6eb50_0" Pin0InfoVect2LinkObjId="g_1f74b50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3764,764 3764,747 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22938f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4043,684 4043,702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="44984@0" ObjectIDZND0="44986@1" Pin0InfoVect0LinkObjId="SW-287730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287729_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4043,684 4043,702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2294900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,768 4093,748 4043,748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2293b50@0" ObjectIDZND0="44986@x" ObjectIDZND1="g_2299b50@0" ObjectIDZND2="44987@x" Pin0InfoVect0LinkObjId="SW-287730_0" Pin0InfoVect1LinkObjId="g_2299b50_0" Pin0InfoVect2LinkObjId="SW-287731_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2293b50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4093,768 4093,748 4043,748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2294b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4043,564 4043,625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="44997@0" ObjectIDZND0="44985@0" Pin0InfoVect0LinkObjId="SW-287730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22fc850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4043,564 4043,625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2294dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4043,642 4043,657 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44985@1" ObjectIDZND0="44984@1" Pin0InfoVect0LinkObjId="SW-287729_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287730_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4043,642 4043,657 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2295020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4043,719 4043,748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="44986@0" ObjectIDZND0="g_2293b50@0" ObjectIDZND1="g_2299b50@0" ObjectIDZND2="44987@x" Pin0InfoVect0LinkObjId="g_2293b50_0" Pin0InfoVect1LinkObjId="g_2299b50_0" Pin0InfoVect2LinkObjId="SW-287731_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4043,719 4043,748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2299430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3947,813 3947,798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22989a0@0" ObjectIDZND0="44987@0" Pin0InfoVect0LinkObjId="SW-287731_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22989a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3947,813 3947,798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2299690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3993,748 3947,748 3947,762 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="44986@x" ObjectIDND1="g_2293b50@0" ObjectIDND2="g_2299b50@0" ObjectIDZND0="44987@1" Pin0InfoVect0LinkObjId="SW-287731_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-287730_0" Pin1InfoVect1LinkObjId="g_2293b50_0" Pin1InfoVect2LinkObjId="g_2299b50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3993,748 3947,748 3947,762 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22998f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3993,748 4043,748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="44987@x" ObjectIDND1="0@x" ObjectIDZND0="44986@x" ObjectIDZND1="g_2293b50@0" ObjectIDZND2="g_2299b50@0" Pin0InfoVect0LinkObjId="SW-287730_0" Pin0InfoVect1LinkObjId="g_2293b50_0" Pin0InfoVect2LinkObjId="g_2299b50_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-287731_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3993,748 4043,748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_229a570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4043,748 4043,831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="44986@x" ObjectIDND1="g_2293b50@0" ObjectIDND2="44987@x" ObjectIDZND0="g_2299b50@1" Pin0InfoVect0LinkObjId="g_2299b50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-287730_0" Pin1InfoVect1LinkObjId="g_2293b50_0" Pin1InfoVect2LinkObjId="SW-287731_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4043,748 4043,831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_229a920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4043,911 4043,958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_2299b50@0" ObjectIDZND0="48821@0" Pin0InfoVect0LinkObjId="SM-CX_YSQ.CX_YSQ_326_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2299b50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4043,911 4043,958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_229b470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3993,787 3993,775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_2295280@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2295280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3993,787 3993,775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_229b6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3993,765 3993,748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="44986@x" ObjectIDZND1="g_2293b50@0" ObjectIDZND2="g_2299b50@0" Pin0InfoVect0LinkObjId="SW-287730_0" Pin0InfoVect1LinkObjId="g_2293b50_0" Pin0InfoVect2LinkObjId="g_2299b50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3993,765 3993,748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a3900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4269,684 4269,702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="44988@0" ObjectIDZND0="44990@1" Pin0InfoVect0LinkObjId="SW-287735_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287734_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4269,684 4269,702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a4910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4319,768 4319,748 4269,748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_22a3b60@0" ObjectIDZND0="44990@x" ObjectIDZND1="g_22a9b60@0" ObjectIDZND2="44991@x" Pin0InfoVect0LinkObjId="SW-287735_0" Pin0InfoVect1LinkObjId="g_22a9b60_0" Pin0InfoVect2LinkObjId="SW-287736_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22a3b60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4319,768 4319,748 4269,748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a4b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4269,564 4269,625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="44997@0" ObjectIDZND0="44989@0" Pin0InfoVect0LinkObjId="SW-287735_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22fc850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4269,564 4269,625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a4dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4269,642 4269,657 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44989@1" ObjectIDZND0="44988@1" Pin0InfoVect0LinkObjId="SW-287734_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287735_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4269,642 4269,657 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a5030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4269,719 4269,748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="44990@0" ObjectIDZND0="g_22a3b60@0" ObjectIDZND1="g_22a9b60@0" ObjectIDZND2="44991@x" Pin0InfoVect0LinkObjId="g_22a3b60_0" Pin0InfoVect1LinkObjId="g_22a9b60_0" Pin0InfoVect2LinkObjId="SW-287736_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287735_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4269,719 4269,748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a9440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4173,813 4173,798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22a89b0@0" ObjectIDZND0="44991@0" Pin0InfoVect0LinkObjId="SW-287736_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22a89b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4173,813 4173,798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a96a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4219,748 4173,748 4173,762 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="44990@x" ObjectIDND1="g_22a3b60@0" ObjectIDND2="g_22a9b60@0" ObjectIDZND0="44991@1" Pin0InfoVect0LinkObjId="SW-287736_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-287735_0" Pin1InfoVect1LinkObjId="g_22a3b60_0" Pin1InfoVect2LinkObjId="g_22a9b60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4219,748 4173,748 4173,762 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a9900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4219,748 4269,748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="44991@x" ObjectIDND1="0@x" ObjectIDZND0="44990@x" ObjectIDZND1="g_22a3b60@0" ObjectIDZND2="g_22a9b60@0" Pin0InfoVect0LinkObjId="SW-287735_0" Pin0InfoVect1LinkObjId="g_22a3b60_0" Pin0InfoVect2LinkObjId="g_22a9b60_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-287736_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4219,748 4269,748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22aa580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4269,748 4269,831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="44990@x" ObjectIDND1="g_22a3b60@0" ObjectIDND2="44991@x" ObjectIDZND0="g_22a9b60@1" Pin0InfoVect0LinkObjId="g_22a9b60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-287735_0" Pin1InfoVect1LinkObjId="g_22a3b60_0" Pin1InfoVect2LinkObjId="SW-287736_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4269,748 4269,831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22aa930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4269,911 4269,958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_22a9b60@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22a9b60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4269,911 4269,958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22ab480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4219,787 4219,775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_22a5290@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22a5290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4219,787 4219,775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22ab6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4219,765 4219,748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="44990@x" ObjectIDZND1="g_22a3b60@0" ObjectIDZND2="g_22a9b60@0" Pin0InfoVect0LinkObjId="SW-287735_0" Pin0InfoVect1LinkObjId="g_22a3b60_0" Pin0InfoVect2LinkObjId="g_22a9b60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4219,765 4219,748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22b4050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,683 4500,701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="44992@0" ObjectIDZND0="44994@1" Pin0InfoVect0LinkObjId="SW-287740_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287739_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,683 4500,701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22b5060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4550,767 4550,747 4500,747 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_22b42b0@0" ObjectIDZND0="44994@x" ObjectIDZND1="g_22ba2b0@0" ObjectIDZND2="44995@x" Pin0InfoVect0LinkObjId="SW-287740_0" Pin0InfoVect1LinkObjId="g_22ba2b0_0" Pin0InfoVect2LinkObjId="SW-287741_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22b42b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4550,767 4550,747 4500,747 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22b52c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,564 4500,624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="44997@0" ObjectIDZND0="44993@0" Pin0InfoVect0LinkObjId="SW-287740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22fc850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,564 4500,624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22b5520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,641 4500,656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44993@1" ObjectIDZND0="44992@1" Pin0InfoVect0LinkObjId="SW-287739_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287740_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,641 4500,656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22b5780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,718 4500,747 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="44994@0" ObjectIDZND0="g_22b42b0@0" ObjectIDZND1="g_22ba2b0@0" ObjectIDZND2="44995@x" Pin0InfoVect0LinkObjId="g_22b42b0_0" Pin0InfoVect1LinkObjId="g_22ba2b0_0" Pin0InfoVect2LinkObjId="SW-287741_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4500,718 4500,747 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22b9b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4404,812 4404,797 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22b9100@0" ObjectIDZND0="44995@0" Pin0InfoVect0LinkObjId="SW-287741_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22b9100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4404,812 4404,797 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22b9df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4450,747 4404,747 4404,761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="44994@x" ObjectIDND1="g_22b42b0@0" ObjectIDND2="g_22ba2b0@0" ObjectIDZND0="44995@1" Pin0InfoVect0LinkObjId="SW-287741_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-287740_0" Pin1InfoVect1LinkObjId="g_22b42b0_0" Pin1InfoVect2LinkObjId="g_22ba2b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4450,747 4404,747 4404,761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22ba050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4450,747 4500,747 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="44995@x" ObjectIDND1="0@x" ObjectIDZND0="44994@x" ObjectIDZND1="g_22b42b0@0" ObjectIDZND2="g_22ba2b0@0" Pin0InfoVect0LinkObjId="SW-287740_0" Pin0InfoVect1LinkObjId="g_22b42b0_0" Pin0InfoVect2LinkObjId="g_22ba2b0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-287741_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4450,747 4500,747 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22bacd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,747 4500,830 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="44994@x" ObjectIDND1="g_22b42b0@0" ObjectIDND2="44995@x" ObjectIDZND0="g_22ba2b0@1" Pin0InfoVect0LinkObjId="g_22ba2b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-287740_0" Pin1InfoVect1LinkObjId="g_22b42b0_0" Pin1InfoVect2LinkObjId="SW-287741_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,747 4500,830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22bb080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,910 4500,944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_22ba2b0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22ba2b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,910 4500,944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22bbbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4450,786 4450,774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_22b59e0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22b59e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4450,786 4450,774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22bbe30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4450,764 4450,747 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="44994@x" ObjectIDZND1="g_22b42b0@0" ObjectIDZND2="g_22ba2b0@0" Pin0InfoVect0LinkObjId="SW-287740_0" Pin0InfoVect1LinkObjId="g_22b42b0_0" Pin0InfoVect2LinkObjId="g_22ba2b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4450,764 4450,747 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22e05f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3049,685 3049,703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="44970@0" ObjectIDZND0="44972@1" Pin0InfoVect0LinkObjId="SW-287714_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287713_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3049,685 3049,703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22e1600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3099,769 3099,749 3049,749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_22e0850@0" ObjectIDZND0="44972@x" ObjectIDZND1="g_22e6850@0" ObjectIDZND2="44973@x" Pin0InfoVect0LinkObjId="SW-287714_0" Pin0InfoVect1LinkObjId="g_22e6850_0" Pin0InfoVect2LinkObjId="SW-287715_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22e0850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3099,769 3099,749 3049,749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22e1860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3049,564 3049,626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="44997@0" ObjectIDZND0="44971@0" Pin0InfoVect0LinkObjId="SW-287714_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22fc850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3049,564 3049,626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22e1ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3049,643 3049,658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44971@1" ObjectIDZND0="44970@1" Pin0InfoVect0LinkObjId="SW-287713_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287714_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3049,643 3049,658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22e1d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3049,720 3049,749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="44972@0" ObjectIDZND0="g_22e0850@0" ObjectIDZND1="g_22e6850@0" ObjectIDZND2="44973@x" Pin0InfoVect0LinkObjId="g_22e0850_0" Pin0InfoVect1LinkObjId="g_22e6850_0" Pin0InfoVect2LinkObjId="SW-287715_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287714_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3049,720 3049,749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22e6130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2953,814 2953,799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22e56a0@0" ObjectIDZND0="44973@0" Pin0InfoVect0LinkObjId="SW-287715_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22e56a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2953,814 2953,799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22e6390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2999,749 2953,749 2953,763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="44972@x" ObjectIDND1="g_22e0850@0" ObjectIDND2="g_22e6850@0" ObjectIDZND0="44973@1" Pin0InfoVect0LinkObjId="SW-287715_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-287714_0" Pin1InfoVect1LinkObjId="g_22e0850_0" Pin1InfoVect2LinkObjId="g_22e6850_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2999,749 2953,749 2953,763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22e65f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2999,749 3049,749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="44973@x" ObjectIDND1="0@x" ObjectIDZND0="44972@x" ObjectIDZND1="g_22e0850@0" ObjectIDZND2="g_22e6850@0" Pin0InfoVect0LinkObjId="SW-287714_0" Pin0InfoVect1LinkObjId="g_22e0850_0" Pin0InfoVect2LinkObjId="g_22e6850_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-287715_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2999,749 3049,749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22e7270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3049,749 3049,832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="44972@x" ObjectIDND1="g_22e0850@0" ObjectIDND2="44973@x" ObjectIDZND0="g_22e6850@1" Pin0InfoVect0LinkObjId="g_22e6850_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-287714_0" Pin1InfoVect1LinkObjId="g_22e0850_0" Pin1InfoVect2LinkObjId="SW-287715_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3049,749 3049,832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22e7620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3049,912 3049,932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_22e6850@0" ObjectIDZND0="g_205d5c0@0" Pin0InfoVect0LinkObjId="g_205d5c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22e6850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3049,912 3049,932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22e8170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2999,788 2999,776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_22e1f80@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22e1f80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2999,788 2999,776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22e83d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2999,766 2999,749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="44972@x" ObjectIDZND1="g_22e0850@0" ObjectIDZND2="g_22e6850@0" Pin0InfoVect0LinkObjId="SW-287714_0" Pin0InfoVect1LinkObjId="g_22e0850_0" Pin0InfoVect2LinkObjId="g_22e6850_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2999,766 2999,749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22f02d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3525,705 3525,645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="44979@1" ObjectIDZND0="44978@1" Pin0InfoVect0LinkObjId="SW-287723_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287723_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3525,705 3525,645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22f2010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3577,648 3577,632 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_22f0530@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22f0530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3577,648 3577,632 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22f3020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3525,722 3525,763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="capacitor" EndDevType2="breaker" ObjectIDND0="44979@0" ObjectIDZND0="g_22f2270@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_22f2270_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287723_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3525,722 3525,763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22f4d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3628,808 3628,791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_22f3280@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22f3280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3628,808 3628,791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22f4fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3575,763 3628,763 3628,781 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="capacitor" ObjectIDND0="g_22f2270@0" ObjectIDND1="44979@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22f2270_0" Pin1InfoVect1LinkObjId="SW-287723_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3575,763 3628,763 3628,781 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22f5220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3575,783 3575,763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_22f2270@0" ObjectIDZND0="0@x" ObjectIDZND1="44979@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-287723_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22f2270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3575,783 3575,763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22f5480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3575,763 3525,763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_22f2270@0" ObjectIDND1="0@x" ObjectIDZND0="44979@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-287723_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22f2270_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3575,763 3525,763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22f7750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3525,763 3525,875 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_22f2270@0" ObjectIDND1="0@x" ObjectIDND2="44979@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22f2270_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-287723_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3525,763 3525,875 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22f79b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3525,902 3525,949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_22f7c10@0" Pin0InfoVect0LinkObjId="g_22f7c10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3525,902 3525,949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22fbb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3578,621 3578,602 3525,602 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="0@0" ObjectIDZND0="44978@x" ObjectIDZND1="44997@0" Pin0InfoVect0LinkObjId="SW-287723_0" Pin0InfoVect1LinkObjId="g_22fc850_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3578,621 3578,602 3525,602 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22fc5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3525,628 3525,602 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="busSection" ObjectIDND0="44978@0" ObjectIDZND0="0@x" ObjectIDZND1="44997@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_22fc850_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287723_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3525,628 3525,602 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22fc850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3525,602 3525,564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="0@x" ObjectIDND1="44978@x" ObjectIDZND0="44997@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-287723_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3525,602 3525,564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2305a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1609,-392 1609,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43149@1" ObjectIDZND0="43197@0" Pin0InfoVect0LinkObjId="g_200f9d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267257_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1609,-392 1609,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_230a640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,475 524,556 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43159@0" ObjectIDZND0="43198@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267271_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="524,475 524,556 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_230ad20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="474,294 524,294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="transformer2" ObjectIDND0="g_20f0cf0@0" ObjectIDZND0="43160@x" ObjectIDZND1="0@x" ObjectIDZND2="43195@x" Pin0InfoVect0LinkObjId="SW-267271_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_230c630_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20f0cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="474,294 524,294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_230b6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,376 524,294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="capacitor" EndDevType2="transformer2" ObjectIDND0="43160@0" ObjectIDZND0="g_20f0cf0@0" ObjectIDZND1="0@x" ObjectIDZND2="43195@x" Pin0InfoVect0LinkObjId="g_20f0cf0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_230c630_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267271_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="524,376 524,294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_230b920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="575,286 524,286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="0@0" ObjectIDZND0="g_20f0cf0@0" ObjectIDZND1="43160@x" ObjectIDZND2="43195@x" Pin0InfoVect0LinkObjId="g_20f0cf0_0" Pin0InfoVect1LinkObjId="SW-267271_0" Pin0InfoVect2LinkObjId="g_230c630_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="575,286 524,286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_230c3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,294 524,286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="capacitor" EndDevType1="transformer2" ObjectIDND0="g_20f0cf0@0" ObjectIDND1="43160@x" ObjectIDZND0="0@x" ObjectIDZND1="43195@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_230c630_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20f0cf0_0" Pin1InfoVect1LinkObjId="SW-267271_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="524,294 524,286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_230c630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,286 523,113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="transformer2" ObjectIDND0="0@x" ObjectIDND1="g_20f0cf0@0" ObjectIDND2="43160@x" ObjectIDZND0="43195@0" Pin0InfoVect0LinkObjId="g_230caf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_20f0cf0_0" Pin1InfoVect2LinkObjId="SW-267271_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="524,286 523,113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_230c890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,-256 524,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43155@0" ObjectIDZND0="44936@1" Pin0InfoVect0LinkObjId="SW-287673_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-267266_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="524,-256 524,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_230caf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,-65 523,30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="44948@x" ObjectIDND1="44946@x" ObjectIDZND0="43195@1" Pin0InfoVect0LinkObjId="g_230c630_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-287685_0" Pin1InfoVect1LinkObjId="SW-287683_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="524,-65 523,30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_230cd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3011,-248 3011,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44953@0" ObjectIDZND0="44952@1" Pin0InfoVect0LinkObjId="SW-287689_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3011,-248 3011,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_230cfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3009,56 3011,-57 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="44996@1" ObjectIDZND0="44955@x" ObjectIDZND1="44957@x" Pin0InfoVect0LinkObjId="SW-287692_0" Pin0InfoVect1LinkObjId="SW-287694_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3009,56 3011,-57 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2809860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2013,902 2013,923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_2053b50@0" ObjectIDZND0="43719@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2053b50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2013,902 2013,923 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-267140" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 50.500000 -859.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43128" ObjectName="DYN-CX_YSQ"/>
     <cge:Meas_Ref ObjectId="267140"/>
    </metadata>
   </g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="26" cy="863" fill="none" fillStyle="0" r="8.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="48" cy="986" fill="none" fillStyle="0" r="3.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="313" cy="863" fill="none" fillStyle="0" r="8.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="837" cy="864" fill="none" fillStyle="0" r="8.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1073" cy="863" fill="none" fillStyle="0" r="8.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1326" cy="862" fill="none" fillStyle="0" r="8.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1555" cy="863" fill="none" fillStyle="0" r="8.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1781" cy="863" fill="none" fillStyle="0" r="8.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="2012" cy="862" fill="none" fillStyle="0" r="8.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="2513" cy="871" fill="none" fillStyle="0" r="8.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="2800" cy="871" fill="none" fillStyle="0" r="8.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3324" cy="872" fill="none" fillStyle="0" r="8.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3813" cy="870" fill="none" fillStyle="0" r="8.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4042" cy="871" fill="none" fillStyle="0" r="8.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4268" cy="871" fill="none" fillStyle="0" r="8.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4499" cy="870" fill="none" fillStyle="0" r="8.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3048" cy="872" fill="none" fillStyle="0" r="8.5" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_LJ" endPointId="0" endStationName="CX_YSQ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_shenglong" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2013,955 2013,919 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="43719" ObjectName="AC-35kV.LN_shenglong"/>
    <cge:TPSR_Ref TObjectID="43719_SS-304"/></metadata>
   <polyline fill="none" opacity="0" points="2013,955 2013,919 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="43198" cx="27" cy="556" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43198" cx="314" cy="556" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43198" cx="524" cy="556" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43198" cx="838" cy="556" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43198" cx="1074" cy="556" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43198" cx="1327" cy="556" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43198" cx="1556" cy="556" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43198" cx="1782" cy="556" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43198" cx="2013" cy="556" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44997" cx="2514" cy="564" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44997" cx="2801" cy="564" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44997" cx="3325" cy="564" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44997" cx="3814" cy="564" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44997" cx="4043" cy="564" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44997" cx="4269" cy="564" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44997" cx="4500" cy="564" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43197" cx="524" cy="-354" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43197" cx="3011" cy="-354" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44997" cx="3049" cy="564" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44997" cx="3525" cy="564" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43197" cx="1609" cy="-354" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43198" cx="524" cy="556" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-267257">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1600.063218 -433.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43149" ObjectName="SW-CX_YSQ.CX_YSQ_2811SW"/>
     <cge:Meas_Ref ObjectId="267257"/>
    <cge:TPSR_Ref TObjectID="43149"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267259">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1600.063218 -599.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43151" ObjectName="SW-CX_YSQ.CX_YSQ_2816SW"/>
     <cge:Meas_Ref ObjectId="267259"/>
    <cge:TPSR_Ref TObjectID="43151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267261">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1596.063218 -614.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43153" ObjectName="SW-CX_YSQ.CX_YSQ_28167SW"/>
     <cge:Meas_Ref ObjectId="267261"/>
    <cge:TPSR_Ref TObjectID="43153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267260">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1595.063218 -530.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43152" ObjectName="SW-CX_YSQ.CX_YSQ_28160SW"/>
     <cge:Meas_Ref ObjectId="267260"/>
    <cge:TPSR_Ref TObjectID="43152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267258">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1596.063218 -451.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43150" ObjectName="SW-CX_YSQ.CX_YSQ_28117SW"/>
     <cge:Meas_Ref ObjectId="267258"/>
    <cge:TPSR_Ref TObjectID="43150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267262">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1527.063218 -704.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43154" ObjectName="SW-CX_YSQ.CX_YSQ_2819SW"/>
     <cge:Meas_Ref ObjectId="267262"/>
    <cge:TPSR_Ref TObjectID="43154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267266">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 515.333972 -251.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43155" ObjectName="SW-CX_YSQ.CX_YSQ_2011SW"/>
     <cge:Meas_Ref ObjectId="267266"/>
    <cge:TPSR_Ref TObjectID="43155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287683">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 515.333972 -85.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44946" ObjectName="SW-CX_YSQ.CX_YSQ_2016SW"/>
     <cge:Meas_Ref ObjectId="287683"/>
    <cge:TPSR_Ref TObjectID="44946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287684">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 465.333972 -144.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44947" ObjectName="SW-CX_YSQ.CX_YSQ_20160SW"/>
     <cge:Meas_Ref ObjectId="287684"/>
    <cge:TPSR_Ref TObjectID="44947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287685">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 466.333972 -60.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44948" ObjectName="SW-CX_YSQ.CX_YSQ_20167SW"/>
     <cge:Meas_Ref ObjectId="287685"/>
    <cge:TPSR_Ref TObjectID="44948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267267">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 466.333972 -306.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43156" ObjectName="SW-CX_YSQ.CX_YSQ_20117SW"/>
     <cge:Meas_Ref ObjectId="267267"/>
    <cge:TPSR_Ref TObjectID="43156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267269">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 663.000000 103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43157" ObjectName="SW-CX_YSQ.CX_YSQ_2010SW"/>
     <cge:Meas_Ref ObjectId="267269"/>
    <cge:TPSR_Ref TObjectID="43157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267271">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 514.333972 400.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43160" ObjectName="SW-CX_YSQ.CX_YSQ_301XC1"/>
     <cge:Meas_Ref ObjectId="267271"/>
    <cge:TPSR_Ref TObjectID="43160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267271">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 514.333972 482.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43159" ObjectName="SW-CX_YSQ.CX_YSQ_301XC"/>
     <cge:Meas_Ref ObjectId="267271"/>
    <cge:TPSR_Ref TObjectID="43159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267304">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 17.012133 641.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43184" ObjectName="SW-CX_YSQ.CX_YSQ_311XC"/>
     <cge:Meas_Ref ObjectId="267304"/>
    <cge:TPSR_Ref TObjectID="43184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267304">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 17.012133 718.394118)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43185" ObjectName="SW-CX_YSQ.CX_YSQ_311XC1"/>
     <cge:Meas_Ref ObjectId="267304"/>
    <cge:TPSR_Ref TObjectID="43185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267305">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 -64.000000 794.987867)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43186" ObjectName="SW-CX_YSQ.CX_YSQ_3117SW"/>
     <cge:Meas_Ref ObjectId="267305"/>
    <cge:TPSR_Ref TObjectID="43186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267309">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 304.012133 641.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43188" ObjectName="SW-CX_YSQ.CX_YSQ_312XC"/>
     <cge:Meas_Ref ObjectId="267309"/>
    <cge:TPSR_Ref TObjectID="43188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267309">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 304.012133 718.394118)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43189" ObjectName="SW-CX_YSQ.CX_YSQ_312XC1"/>
     <cge:Meas_Ref ObjectId="267309"/>
    <cge:TPSR_Ref TObjectID="43189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267310">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 223.000000 794.987867)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43190" ObjectName="SW-CX_YSQ.CX_YSQ_31267SW"/>
     <cge:Meas_Ref ObjectId="267310"/>
    <cge:TPSR_Ref TObjectID="43190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267277">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 514.012133 641.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43161" ObjectName="SW-CX_YSQ.CX_YSQ_3901XC"/>
     <cge:Meas_Ref ObjectId="267277"/>
    <cge:TPSR_Ref TObjectID="43161"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267277">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 514.012133 718.394118)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43162" ObjectName="SW-CX_YSQ.CX_YSQ_3901XC1"/>
     <cge:Meas_Ref ObjectId="267277"/>
    <cge:TPSR_Ref TObjectID="43162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267279">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 828.012133 642.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43164" ObjectName="SW-CX_YSQ.CX_YSQ_313XC"/>
     <cge:Meas_Ref ObjectId="267279"/>
    <cge:TPSR_Ref TObjectID="43164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267279">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 828.012133 719.394118)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43165" ObjectName="SW-CX_YSQ.CX_YSQ_313XC1"/>
     <cge:Meas_Ref ObjectId="267279"/>
    <cge:TPSR_Ref TObjectID="43165"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267280">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 747.000000 795.987867)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43166" ObjectName="SW-CX_YSQ.CX_YSQ_31367SW"/>
     <cge:Meas_Ref ObjectId="267280"/>
    <cge:TPSR_Ref TObjectID="43166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267284">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1064.012133 641.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43168" ObjectName="SW-CX_YSQ.CX_YSQ_314XC"/>
     <cge:Meas_Ref ObjectId="267284"/>
    <cge:TPSR_Ref TObjectID="43168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267284">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1064.012133 718.394118)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43169" ObjectName="SW-CX_YSQ.CX_YSQ_314XC1"/>
     <cge:Meas_Ref ObjectId="267284"/>
    <cge:TPSR_Ref TObjectID="43169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267285">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 983.000000 794.987867)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43170" ObjectName="SW-CX_YSQ.CX_YSQ_31467SW"/>
     <cge:Meas_Ref ObjectId="267285"/>
    <cge:TPSR_Ref TObjectID="43170"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267289">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1317.012133 640.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43172" ObjectName="SW-CX_YSQ.CX_YSQ_315XC"/>
     <cge:Meas_Ref ObjectId="267289"/>
    <cge:TPSR_Ref TObjectID="43172"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267289">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1317.012133 717.394118)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43173" ObjectName="SW-CX_YSQ.CX_YSQ_315XC1"/>
     <cge:Meas_Ref ObjectId="267289"/>
    <cge:TPSR_Ref TObjectID="43173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267290">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1236.000000 793.987867)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43174" ObjectName="SW-CX_YSQ.CX_YSQ_31567SW"/>
     <cge:Meas_Ref ObjectId="267290"/>
    <cge:TPSR_Ref TObjectID="43174"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267294">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1546.012133 641.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43176" ObjectName="SW-CX_YSQ.CX_YSQ_316XC"/>
     <cge:Meas_Ref ObjectId="267294"/>
    <cge:TPSR_Ref TObjectID="43176"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267294">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1546.012133 718.394118)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43177" ObjectName="SW-CX_YSQ.CX_YSQ_316XC1"/>
     <cge:Meas_Ref ObjectId="267294"/>
    <cge:TPSR_Ref TObjectID="43177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267295">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1465.000000 794.987867)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43178" ObjectName="SW-CX_YSQ.CX_YSQ_31667SW"/>
     <cge:Meas_Ref ObjectId="267295"/>
    <cge:TPSR_Ref TObjectID="43178"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267299">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1772.012133 641.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43180" ObjectName="SW-CX_YSQ.CX_YSQ_317XC"/>
     <cge:Meas_Ref ObjectId="267299"/>
    <cge:TPSR_Ref TObjectID="43180"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267299">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1772.012133 718.394118)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43181" ObjectName="SW-CX_YSQ.CX_YSQ_317XC1"/>
     <cge:Meas_Ref ObjectId="267299"/>
    <cge:TPSR_Ref TObjectID="43181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267300">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1691.000000 794.987867)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43182" ObjectName="SW-CX_YSQ.CX_YSQ_31767SW"/>
     <cge:Meas_Ref ObjectId="267300"/>
    <cge:TPSR_Ref TObjectID="43182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267314">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2003.012133 640.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43192" ObjectName="SW-CX_YSQ.CX_YSQ_318XC"/>
     <cge:Meas_Ref ObjectId="267314"/>
    <cge:TPSR_Ref TObjectID="43192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267314">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2003.012133 717.394118)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43193" ObjectName="SW-CX_YSQ.CX_YSQ_318XC1"/>
     <cge:Meas_Ref ObjectId="267314"/>
    <cge:TPSR_Ref TObjectID="43193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-267315">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1922.000000 793.987867)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43194" ObjectName="SW-CX_YSQ.CX_YSQ_31867SW"/>
     <cge:Meas_Ref ObjectId="267315"/>
    <cge:TPSR_Ref TObjectID="43194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287690">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3002.012133 -243.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44953" ObjectName="SW-CX_YSQ.CX_YSQ_2021SW"/>
     <cge:Meas_Ref ObjectId="287690"/>
    <cge:TPSR_Ref TObjectID="44953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287692">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3002.012133 -77.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44955" ObjectName="SW-CX_YSQ.CX_YSQ_2026SW"/>
     <cge:Meas_Ref ObjectId="287692"/>
    <cge:TPSR_Ref TObjectID="44955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287693">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2952.012133 -136.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44956" ObjectName="SW-CX_YSQ.CX_YSQ_20260SW"/>
     <cge:Meas_Ref ObjectId="287693"/>
    <cge:TPSR_Ref TObjectID="44956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287694">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2953.012133 -52.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44957" ObjectName="SW-CX_YSQ.CX_YSQ_20267SW"/>
     <cge:Meas_Ref ObjectId="287694"/>
    <cge:TPSR_Ref TObjectID="44957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287691">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2953.012133 -298.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44954" ObjectName="SW-CX_YSQ.CX_YSQ_20217SW"/>
     <cge:Meas_Ref ObjectId="287691"/>
    <cge:TPSR_Ref TObjectID="44954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3150.000000 111.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287697">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3001.012133 408.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44961" ObjectName="SW-CX_YSQ.CX_YSQ_302XC1"/>
     <cge:Meas_Ref ObjectId="287697"/>
    <cge:TPSR_Ref TObjectID="44961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287697">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3001.012133 490.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44960" ObjectName="SW-CX_YSQ.CX_YSQ_302XC"/>
     <cge:Meas_Ref ObjectId="287697"/>
    <cge:TPSR_Ref TObjectID="44960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287704">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2504.012133 649.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44963" ObjectName="SW-CX_YSQ.CX_YSQ_321XC"/>
     <cge:Meas_Ref ObjectId="287704"/>
    <cge:TPSR_Ref TObjectID="44963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287704">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2504.012133 726.394118)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44964" ObjectName="SW-CX_YSQ.CX_YSQ_321XC1"/>
     <cge:Meas_Ref ObjectId="287704"/>
    <cge:TPSR_Ref TObjectID="44964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287705">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2423.000000 802.987867)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44965" ObjectName="SW-CX_YSQ.CX_YSQ_32167SW"/>
     <cge:Meas_Ref ObjectId="287705"/>
    <cge:TPSR_Ref TObjectID="44965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287709">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2791.012133 649.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44967" ObjectName="SW-CX_YSQ.CX_YSQ_322XC"/>
     <cge:Meas_Ref ObjectId="287709"/>
    <cge:TPSR_Ref TObjectID="44967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287709">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2791.012133 726.394118)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44968" ObjectName="SW-CX_YSQ.CX_YSQ_322XC1"/>
     <cge:Meas_Ref ObjectId="287709"/>
    <cge:TPSR_Ref TObjectID="44968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287710">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2710.000000 802.987867)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44969" ObjectName="SW-CX_YSQ.CX_YSQ_32267SW"/>
     <cge:Meas_Ref ObjectId="287710"/>
    <cge:TPSR_Ref TObjectID="44969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287719">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3315.012133 650.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44975" ObjectName="SW-CX_YSQ.CX_YSQ_324XC"/>
     <cge:Meas_Ref ObjectId="287719"/>
    <cge:TPSR_Ref TObjectID="44975"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287719">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3315.012133 727.394118)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44976" ObjectName="SW-CX_YSQ.CX_YSQ_324XC1"/>
     <cge:Meas_Ref ObjectId="287719"/>
    <cge:TPSR_Ref TObjectID="44976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287720">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3234.000000 803.987867)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44977" ObjectName="SW-CX_YSQ.CX_YSQ_32467SW"/>
     <cge:Meas_Ref ObjectId="287720"/>
    <cge:TPSR_Ref TObjectID="44977"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287725">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3804.012133 648.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44981" ObjectName="SW-CX_YSQ.CX_YSQ_325XC"/>
     <cge:Meas_Ref ObjectId="287725"/>
    <cge:TPSR_Ref TObjectID="44981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287725">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3804.012133 725.394118)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44982" ObjectName="SW-CX_YSQ.CX_YSQ_325XC1"/>
     <cge:Meas_Ref ObjectId="287725"/>
    <cge:TPSR_Ref TObjectID="44982"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287726">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3723.000000 801.987867)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44983" ObjectName="SW-CX_YSQ.CX_YSQ_32567SW"/>
     <cge:Meas_Ref ObjectId="287726"/>
    <cge:TPSR_Ref TObjectID="44983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287730">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4033.012133 649.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44985" ObjectName="SW-CX_YSQ.CX_YSQ_326XC"/>
     <cge:Meas_Ref ObjectId="287730"/>
    <cge:TPSR_Ref TObjectID="44985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287730">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4033.012133 726.394118)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44986" ObjectName="SW-CX_YSQ.CX_YSQ_326XC1"/>
     <cge:Meas_Ref ObjectId="287730"/>
    <cge:TPSR_Ref TObjectID="44986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287731">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3952.000000 802.987867)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44987" ObjectName="SW-CX_YSQ.CX_YSQ_32667SW"/>
     <cge:Meas_Ref ObjectId="287731"/>
    <cge:TPSR_Ref TObjectID="44987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287735">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4259.012133 649.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44989" ObjectName="SW-CX_YSQ.CX_YSQ_327XC"/>
     <cge:Meas_Ref ObjectId="287735"/>
    <cge:TPSR_Ref TObjectID="44989"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287735">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4259.012133 726.394118)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44990" ObjectName="SW-CX_YSQ.CX_YSQ_327XC1"/>
     <cge:Meas_Ref ObjectId="287735"/>
    <cge:TPSR_Ref TObjectID="44990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287736">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4178.000000 802.987867)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44991" ObjectName="SW-CX_YSQ.CX_YSQ_32767SW"/>
     <cge:Meas_Ref ObjectId="287736"/>
    <cge:TPSR_Ref TObjectID="44991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287740">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4490.012133 648.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44993" ObjectName="SW-CX_YSQ.CX_YSQ_328XC"/>
     <cge:Meas_Ref ObjectId="287740"/>
    <cge:TPSR_Ref TObjectID="44993"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287740">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4490.012133 725.394118)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44994" ObjectName="SW-CX_YSQ.CX_YSQ_328XC1"/>
     <cge:Meas_Ref ObjectId="287740"/>
    <cge:TPSR_Ref TObjectID="44994"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287741">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4409.000000 801.987867)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44995" ObjectName="SW-CX_YSQ.CX_YSQ_32867SW"/>
     <cge:Meas_Ref ObjectId="287741"/>
    <cge:TPSR_Ref TObjectID="44995"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287714">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3039.012133 650.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44971" ObjectName="SW-CX_YSQ.CX_YSQ_323XC"/>
     <cge:Meas_Ref ObjectId="287714"/>
    <cge:TPSR_Ref TObjectID="44971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287714">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3039.012133 727.394118)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44972" ObjectName="SW-CX_YSQ.CX_YSQ_323XC1"/>
     <cge:Meas_Ref ObjectId="287714"/>
    <cge:TPSR_Ref TObjectID="44972"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287715">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2958.000000 803.987867)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44973" ObjectName="SW-CX_YSQ.CX_YSQ_32367SW"/>
     <cge:Meas_Ref ObjectId="287715"/>
    <cge:TPSR_Ref TObjectID="44973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287723">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3515.012133 652.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44978" ObjectName="SW-CX_YSQ.CX_YSQ_3902XC"/>
     <cge:Meas_Ref ObjectId="287723"/>
    <cge:TPSR_Ref TObjectID="44978"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287723">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3515.012133 729.394118)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44979" ObjectName="SW-CX_YSQ.CX_YSQ_3902XC1"/>
     <cge:Meas_Ref ObjectId="287723"/>
    <cge:TPSR_Ref TObjectID="44979"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ead960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 708.000000 -378.000000) translate(0,15)">220kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2183d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -350.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_211bce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_211bce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_211bce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_211bce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_211bce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_211bce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_211bce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -788.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1df81a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -148.000000 -929.500000) translate(0,16)">永胜桥光伏电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_20fb460" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1544.000000 -836.000000) translate(0,16)">220kV胜启线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20fbc40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1548.063218 -647.000000) translate(0,12)">28167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20fbf70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1616.063218 -583.000000) translate(0,12)">2816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2105e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1548.063218 -559.000000) translate(0,12)">28160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2105f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1554.063218 -444.000000) translate(0,12)">28117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2106100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1616.063218 -417.000000) translate(0,12)">2811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_208f8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 616.000000 168.000000) translate(0,17)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e6980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1442.000000 -626.000000) translate(0,12)">2819PT</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f89f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -41.000000 529.000000) translate(0,15)">35kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_20bee40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -31.000000 1137.000000) translate(0,16)">至站用电系统</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_20e3cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 50.000000 953.000000) translate(0,14)">#1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_20e41d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -156.000000 960.000000) translate(0,14)">SCB13-315/37</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_20e41d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -156.000000 960.000000) translate(0,31)">37±2×2.5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_20e41d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -156.000000 960.000000) translate(0,48)">Ud=6%  Dyn11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_20e41d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -156.000000 960.000000) translate(0,65)">LMZJ-0.66-300/1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2064300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 273.000000 996.000000) translate(0,16)">#1 SVG</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20abc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 474.000000 654.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_202a0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 460.000000 865.000000) translate(0,12)">3901F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_202cd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 486.000000 992.000000) translate(0,16)">0901PT</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2059e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 786.000000 1003.000000) translate(0,16)">1回集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2059e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 786.000000 1003.000000) translate(0,36)">1-8号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2035450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1024.000000 1001.000000) translate(0,16)">2回集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2035450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1024.000000 1001.000000) translate(0,36)">9-16号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2035be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1281.000000 1000.000000) translate(0,16)">3回集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2035be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1281.000000 1000.000000) translate(0,36)">17-27号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2036460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1500.000000 1000.000000) translate(0,16)">4回集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2036460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1500.000000 1000.000000) translate(0,36)">28-37号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_20369c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1764.000000 1007.000000) translate(0,16)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2036ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1567.063218 -504.000000) translate(0,12)">281</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2037150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1499.063218 -735.000000) translate(0,12)">2819</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2037390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 478.321839 -278.000000) translate(0,12)">2011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20375d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 469.321839 -337.000000) translate(0,12)">20117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2037810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 679.000000 72.000000) translate(0,12)">2010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2037a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 556.321839 11.000000) translate(0,16)">#1主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2038210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 539.321839 412.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2038670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -14.000000 657.000000) translate(0,12)">311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20388b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -120.000000 769.000000) translate(0,12)">31167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2038af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 277.000000 655.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2038d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 164.000000 763.000000) translate(0,12)">31267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2038f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 792.000000 656.000000) translate(0,12)">313</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20391b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 682.000000 766.000000) translate(0,12)">31367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20393f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1036.000000 658.000000) translate(0,12)">314</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2039630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 926.000000 764.000000) translate(0,12)">31467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2039870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1285.000000 654.000000) translate(0,12)">315</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2039ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1179.000000 765.000000) translate(0,12)">31567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2039cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1514.000000 655.000000) translate(0,12)">316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2039f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1409.000000 762.000000) translate(0,12)">31667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203a170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1740.000000 657.000000) translate(0,12)">317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203a3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1635.000000 761.000000) translate(0,12)">31767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203a5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1976.000000 654.000000) translate(0,12)">318</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_203a830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1861.000000 761.000000) translate(0,12)">31867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21c7f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1974.000000 1001.000000) translate(0,15)">35kV胜龙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_21c9630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3103.000000 176.000000) translate(0,17)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fe5440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2446.000000 537.000000) translate(0,15)">35kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_1f527b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2767.000000 981.000000) translate(0,14)">#2接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_205dfb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3002.000000 983.000000) translate(0,16)">#2 SVG</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1f566c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3495.000000 993.000000) translate(0,16)">3902PT</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22bc090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2760.000000 45.000000) translate(0,16)">#2主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bc6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3026.000000 420.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bc900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2473.000000 665.000000) translate(0,12)">321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bcb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2367.000000 777.000000) translate(0,12)">32167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bcd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2764.000000 663.000000) translate(0,12)">322</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bcfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2651.000000 771.000000) translate(0,12)">32267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bd200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3279.000000 664.000000) translate(0,12)">324</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bd440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3169.000000 774.000000) translate(0,12)">32467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bd680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3772.000000 662.000000) translate(0,12)">325</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bd8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3666.000000 773.000000) translate(0,12)">32567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bdb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4001.000000 663.000000) translate(0,12)">326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bdd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3891.000000 769.000000) translate(0,12)">32667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bdf80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4227.000000 665.000000) translate(0,12)">327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22be1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4115.000000 768.000000) translate(0,12)">32767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22be400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4463.000000 662.000000) translate(0,12)">328</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22be640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4348.000000 769.000000) translate(0,12)">32867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22d3dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2729.321839 89.000000) translate(0,16)">2号主变:100MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_22d60b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2451.000000 977.000000) translate(0,14)">备用（滤波装置）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22e8630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3012.000000 664.000000) translate(0,12)">323</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22e8c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2899.000000 772.000000) translate(0,12)">32367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fcab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3479.000000 668.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22fe080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3267.000000 989.000000) translate(0,16)">#2-1储能装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22fee80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3771.000000 1005.000000) translate(0,16)">5回集电线路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22ff6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3988.000000 1013.000000) translate(0,16)">6回集电线路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22ffaf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4208.000000 1016.000000) translate(0,16)">7回集电线路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_22ffd30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4452.000000 1014.000000) translate(0,16)">8回集电线路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23067e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 534.000000 -195.000000) translate(0,12)">201</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23069f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 531.000000 -115.000000) translate(0,12)">2016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2306c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 468.000000 -175.000000) translate(0,12)">20160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2306e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 469.000000 -91.000000) translate(0,12)">20167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23070b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3020.000000 -187.000000) translate(0,12)">202</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23072f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3018.000000 -273.000000) translate(0,12)">2021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2307530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2956.000000 -329.000000) translate(0,12)">20217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2307770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3018.000000 -107.000000) translate(0,12)">2026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23079b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2954.000000 -167.000000) translate(0,12)">20260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2307bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2955.000000 -83.000000) translate(0,12)">20267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2307e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -13.000000) translate(0,15)">0878-6013192</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2311af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -152.000000 14.000000) translate(0,15)">93503501</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_20a41d0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 1535.063218 -450.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_210b970" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 1534.063218 -529.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_210c760" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 1535.063218 -613.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2114630" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 451.333972 -155.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_200aab0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 452.333972 -71.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_200bfb0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 452.333972 -317.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fca030" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 666.500000 127.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20f03c0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 495.500000 227.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20b5a60" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 -75.000000 800.987867)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20bc150" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 212.000000 800.987867)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20787d0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 736.000000 801.987867)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2092230" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 972.000000 800.987867)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20c4b80" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1225.000000 799.987867)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2083200" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1454.000000 800.987867)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ff75c0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1680.000000 800.987867)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2052a00" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1911.000000 799.987867)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21d2c00" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2937.012133 -147.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21d6540" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2938.012133 -63.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fd0c10" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2938.012133 -309.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fd7db0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3153.500000 135.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb8dc0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2412.000000 808.987867)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fc8020" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2699.000000 808.987867)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f63990" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3223.000000 809.987867)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f739a0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3712.000000 807.987867)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22989a0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3941.000000 808.987867)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22a89b0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4167.000000 808.987867)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22b9100" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4398.000000 807.987867)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22e56a0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2947.000000 809.987867)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_YSQ"/>
</svg>