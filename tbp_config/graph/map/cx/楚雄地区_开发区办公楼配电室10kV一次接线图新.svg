<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-248" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="97087 -3560 3273 1461">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="transformer2:shape17_0">
    <circle cx="13" cy="34" fillStyle="0" r="13" stroke-width="0.265306"/>
   </symbol>
   <symbol id="transformer2:shape17_1">
    <ellipse cx="13" cy="17" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
   </symbol>
   <symbol id="voltageTransformer:shape18">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="48" x2="48" y1="4" y2="4"/>
    <circle cx="41" cy="55" fillStyle="0" r="25" stroke-width="0.520408"/>
    <circle cx="57" cy="26" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="40" y1="50" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="48" x2="40" y1="66" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="32" y1="59" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="22" x2="22" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="30" x2="22" y1="29" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="22" x2="14" y1="22" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="57" x2="57" y1="33" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="73" x2="57" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="73" x2="57" y1="24" y2="33"/>
    <circle cx="25" cy="25" fillStyle="0" r="25" stroke-width="0.520408"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aefd60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a88d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1aafcd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1828740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1581e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_171c600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_171d1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b08670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a8bf90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a88060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aed8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1a9f570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa4f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1815a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1599930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_159f740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9d970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15520b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a8c2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_154acc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_180c0f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_160c320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a95c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1aa8dc0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aec1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ab4b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a8a960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b246a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1582e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1ab14b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1471" width="3283" x="97082" y="-3565"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="0" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2600d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98696.000000 3316.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e15090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98721.000000 3301.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e6460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98726.000000 3285.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f7c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98707.000000 3331.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="0" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_261a2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99206.000000 3303.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24d2320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99231.000000 3288.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24d2560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99236.000000 3272.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2de2b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99217.000000 3318.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="0" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e4dbb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98706.000000 3137.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e4de20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98731.000000 3122.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24ffa70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98736.000000 3106.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24ffc90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98717.000000 3152.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="0" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2db87f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99216.000000 3133.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e12e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99241.000000 3118.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e1520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99246.000000 3102.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33a8390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99227.000000 3148.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="0" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15e1910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98942.000000 3297.000000) translate(0,12)">3U0（kV）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ddd760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98950.000000 3313.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3380860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98950.000000 3326.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_165f320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98950.000000 3344.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_165f5a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98942.000000 3280.000000) translate(0,12)">Uab（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="0" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcaf10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98226.000000 2860.000000) translate(0,12)">Uc（V）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a94ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98226.000000 2873.000000) translate(0,12)">Ub（V）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a94cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98226.000000 2891.000000) translate(0,12)">Ua（V）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_260d7c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98218.000000 2827.000000) translate(0,12)">Uab（V）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_260da00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98218.000000 2844.000000) translate(0,12)">3U0（V）：</text>
   <metadata/></g>
   <g DF8003:Layer="0" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bad50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99725.000000 2828.000000) translate(0,12)">Uc（V）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24baf90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99725.000000 2841.000000) translate(0,12)">Ub（V）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1570700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99725.000000 2859.000000) translate(0,12)">Ua（V）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1570950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99717.000000 2795.000000) translate(0,12)">Uab（V）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ab2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99717.000000 2812.000000) translate(0,12)">3U0（V）：</text>
   <metadata/></g>
   <g DF8003:Layer="0" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dca9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98872.000000 2911.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33a5260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98886.000000 2864.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e8bbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98885.000000 2847.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e8bde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98861.000000 2896.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e8c020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98886.000000 2881.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="0" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b8e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99221.000000 2918.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b8ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99235.000000 2871.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b9230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99234.000000 2854.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e59b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99210.000000 2903.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e59d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99235.000000 2888.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="0" transform="matrix(0.888889 -0.000000 0.000000 -0.711538 10960.000000 -705.846154)">
    <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98921" x2="98927" y1="2336" y2="2352"/>
    <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98926" x2="98926" y1="2375" y2="2349"/>
    <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98926" x2="98919" y1="2375" y2="2388"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="98921" x2="98927" y1="2336" y2="2352"/></g>
   <g DF8003:Layer="0" transform="matrix(0.888889 -0.000000 0.000000 -0.711538 11183.000000 -705.846154)">
    <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98921" x2="98927" y1="2336" y2="2352"/>
    <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98926" x2="98926" y1="2375" y2="2349"/>
    <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98926" x2="98919" y1="2375" y2="2388"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="98921" x2="98927" y1="2336" y2="2352"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="0" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="97099" y="-3559"/>
   <rect DF8003:Layer="0" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="97098" y="-3439"/>
   <rect DF8003:Layer="0" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="97088" y="-2968"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="97511" y="-2626"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="97570" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="97625" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="97697" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="97747" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="97797" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="97908" y="-2626"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="97854" y="-2626"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98612" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98355" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98549" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98485" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98073" y="-2626"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98018" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="97963" y="-2626"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98128" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98182" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98774" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98299" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="16" x="98241" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98420" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98682" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="100318" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="100258" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="16" x="100194" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="100138" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="100075" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="100008" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99943" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99879" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99816" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99761" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99701" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99638" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99580" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99524" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99461" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99393" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99323" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99260" y="-2627"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99199" y="-2626"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(0,255,0)" stroke-width="1" width="21" x="99013" y="-3125"/>
   <rect DF8003:Layer="0" fill="none" height="13" stroke="rgb(0,255,0)" stroke-width="1" width="42" x="98763" y="-3383"/>
   <rect DF8003:Layer="0" fill="none" height="13" stroke="rgb(0,255,0)" stroke-width="1" width="42" x="99205" y="-3380"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98818" y="-2931"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99181" y="-2906"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98882" y="-2628"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98882" y="-2493"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98883" y="-2408"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99105" y="-2628"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99105" y="-2493"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99106" y="-2408"/>
   <rect DF8003:Layer="0" fill="none" height="15" stroke="rgb(255,0,0)" stroke-width="1" width="42" x="97934" y="-2219"/>
   <rect DF8003:Layer="0" fill="none" height="15" stroke="rgb(255,0,0)" stroke-width="1" width="42" x="98112" y="-2280"/>
   <rect DF8003:Layer="0" fill="none" height="15" stroke="rgb(255,0,0)" stroke-width="1" width="42" x="98111" y="-2151"/>
   <rect DF8003:Layer="0" fill="none" height="176" stroke="rgb(227,86,255)" stroke-width="1" width="517" x="97833" y="-2299"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="0" id="g_34b8920">
    <use class="BV-0KV" transform="matrix(0.524390 -0.000000 0.000000 -0.523810 99003.000000 -2974.000000)" xlink:href="#voltageTransformer:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="0" id="TF-CX_PDS.CX_PDS_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="41026"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98811.000000 -2996.000000)" xlink:href="#transformer2:shape17_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98811.000000 -2996.000000)" xlink:href="#transformer2:shape17_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="28795" ObjectName="TF-CX_PDS.CX_PDS_2T"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99176.000000 -2994.000000)" xlink:href="#transformer2:shape17_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99176.000000 -2994.000000)" xlink:href="#transformer2:shape17_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_32a7ab0">
     <polyline DF8003:Layer="0" fill="none" points="98824,-3231 98824,-3173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="28786@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_34b8920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24e92f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="98824,-3231 98824,-3173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_251d1b0">
     <polyline DF8003:Layer="0" fill="none" points="98824,-3081 98824,-3043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="28795@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34b8920_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="98824,-3081 98824,-3043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_251bcf0">
     <polyline DF8003:Layer="0" fill="none" points="99189,-3359 99189,-3343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_34b8920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="99189,-3359 99189,-3343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3380c50">
     <polyline DF8003:Layer="0" fill="none" points="99189,-3080 99189,-3041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_34b8920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34b8920_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="99189,-3080 99189,-3041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24e92f0">
     <polyline DF8003:Layer="0" fill="none" points="99189,-3251 99189,-3231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="28786@0" Pin0InfoVect0LinkObjId="g_15df790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34b8920_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="99189,-3251 99189,-3231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15df790">
     <polyline DF8003:Layer="0" fill="none" points="99189,-3172 99189,-3231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="28786@0" Pin0InfoVect0LinkObjId="g_24e92f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34b8920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="99189,-3172 99189,-3231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15df160">
     <polyline DF8003:Layer="0" fill="none" points="98824,-3375 98824,-3343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_34b8920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="98824,-3375 98824,-3343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2de0de0">
     <polyline DF8003:Layer="0" fill="none" points="98824,-3251 98824,-3231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="28786@0" Pin0InfoVect0LinkObjId="g_24e92f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34b8920_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="98824,-3251 98824,-3231 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="0" busDevId="28786" cx="98822" cy="-3231" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="28786" cx="98824" cy="-3231" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="28786" cx="99189" cy="-3231" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="28786" cx="99189" cy="-3231" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="0" freshType="0" id="DYN-188552" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 97450.000000 -3450.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28725" ObjectName="DYN-CX_PDS"/>
     <cge:Meas_Ref ObjectId="188552"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dcd2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -3387.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dcd2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -3387.000000) translate(0,38)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dcd2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -3387.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dcd2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -3387.000000) translate(0,80)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dcd2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -3387.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dcd2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -3387.000000) translate(0,122)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dcd2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -3387.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32ae230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -2938.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32ae230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -2938.000000) translate(0,38)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32ae230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -2938.000000) translate(0,59)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32ae230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -2938.000000) translate(0,80)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32ae230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -2938.000000) translate(0,101)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32ae230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -2938.000000) translate(0,122)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32ae230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -2938.000000) translate(0,143)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32ae230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -2938.000000) translate(0,164)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32ae230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -2938.000000) translate(0,185)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32ae230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -2938.000000) translate(0,206)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32ae230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -2938.000000) translate(0,227)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32ae230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -2938.000000) translate(0,248)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32ae230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -2938.000000) translate(0,269)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32ae230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -2938.000000) translate(0,290)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32ae230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -2938.000000) translate(0,311)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32ae230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -2938.000000) translate(0,332)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32ae230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -2938.000000) translate(0,353)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32ae230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97117.000000 -2938.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="0" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_33ea730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97210.500000 -3525.500000) translate(0,16)">10kV供电办公楼配电室</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="74" transform="matrix(0.581873 -0.000000 -0.000000 0.520147 98806.587388 -3552.963931) translate(0,60)">10kV供电局办公楼配电室</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="54" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98743.622831 -3031.649723) translate(0,44)">2号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="54" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99222.198671 -3031.487465) translate(0,44)">1号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99327.356838 -3465.031520) translate(0,40)">10kV开发区Ⅱ回线37号杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99327.356838 -3465.031520) translate(0,90)">育才路联络线4号杆供电局办公楼Ⅰ回支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="54" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98936.364343 -2970.010737) translate(0,44)">10kV母线电压互感器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="54" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99719.560102 -2773.244155) translate(0,44)">0.4kVI段母线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97505.981641 -2486.856837) translate(0,24)">应</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97505.981641 -2486.856837) translate(0,54)">急</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97505.981641 -2486.856837) translate(0,84)">发</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97505.981641 -2486.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97505.981641 -2486.856837) translate(0,144)">机</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97505.981641 -2486.856837) translate(0,174)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97505.981641 -2486.856837) translate(0,204)">源</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97505.981641 -2486.856837) translate(0,234)">接</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97505.981641 -2486.856837) translate(0,264)">入</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97505.981641 -2486.856837) translate(0,294)">口</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97569.907766 -2484.856837) translate(0,24)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97569.907766 -2484.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97569.907766 -2484.856837) translate(0,84)">信</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97569.907766 -2484.856837) translate(0,114)">息</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97569.907766 -2484.856837) translate(0,144)">机</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97569.907766 -2484.856837) translate(0,174)">房</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97569.907766 -2484.856837) translate(0,204)">配</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97569.907766 -2484.856837) translate(0,234)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97569.907766 -2484.856837) translate(0,264)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97569.907766 -2484.856837) translate(0,294)">回</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97569.907766 -2484.856837) translate(0,324)">路</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97627.509762 -2488.856837) translate(0,24)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97627.509762 -2488.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97627.509762 -2488.856837) translate(0,84)">信</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97627.509762 -2488.856837) translate(0,114)">息</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97627.509762 -2488.856837) translate(0,144)">机</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97627.509762 -2488.856837) translate(0,174)">房</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97627.509762 -2488.856837) translate(0,204)">配</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97627.509762 -2488.856837) translate(0,234)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97627.509762 -2488.856837) translate(0,264)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97627.509762 -2488.856837) translate(0,294)">回</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97627.509762 -2488.856837) translate(0,324)">路</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98858.123632 -2241.856837) translate(0,24)">2号电容器组</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98132.725337 -2488.856837) translate(0,24)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98132.725337 -2488.856837) translate(0,54)">号</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98132.725337 -2488.856837) translate(0,84)">风</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98132.725337 -2488.856837) translate(0,114)">机</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98132.725337 -2488.856837) translate(0,144)">有</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98132.725337 -2488.856837) translate(0,174)">载</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98132.725337 -2488.856837) translate(0,204)">调</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98132.725337 -2488.856837) translate(0,234)">压</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98132.725337 -2488.856837) translate(0,264)">过</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98132.725337 -2488.856837) translate(0,294)">流</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98132.725337 -2488.856837) translate(0,324)">箱</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98185.928892 -2489.856837) translate(0,24)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98185.928892 -2489.856837) translate(0,54)">号</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98185.928892 -2489.856837) translate(0,84)">直</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98185.928892 -2489.856837) translate(0,114)">流</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98185.928892 -2489.856837) translate(0,144)">充</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98185.928892 -2489.856837) translate(0,174)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98185.928892 -2489.856837) translate(0,204)">10</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98185.928892 -2489.856837) translate(0,234)">kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98185.928892 -2489.856837) translate(0,264)">高</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98185.928892 -2489.856837) translate(0,294)">压</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98185.928892 -2489.856837) translate(0,324)">照</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98185.928892 -2489.856837) translate(0,354)">明</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98303.289740 -2486.856837) translate(0,24)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98303.289740 -2486.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98303.289740 -2486.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98303.289740 -2486.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98303.289740 -2486.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98244.678492 -2486.856837) translate(0,24)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98244.678492 -2486.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98244.678492 -2486.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98244.678492 -2486.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98244.678492 -2486.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98422.280197 -2486.856837) translate(0,24)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98422.280197 -2486.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98422.280197 -2486.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98422.280197 -2486.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98422.280197 -2486.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98490.465247 -2491.856837) translate(0,24)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98490.465247 -2491.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98490.465247 -2491.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98490.465247 -2491.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98490.465247 -2491.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98556.048447 -2490.856837) translate(0,24)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98556.048447 -2490.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98556.048447 -2490.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98556.048447 -2490.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98556.048447 -2490.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98358.427946 -2486.856837) translate(0,24)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98358.427946 -2486.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98358.427946 -2486.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98358.427946 -2486.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98358.427946 -2486.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98687.807445 -2490.856837) translate(0,24)">10</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98687.807445 -2490.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98687.807445 -2490.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98687.807445 -2490.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98687.807445 -2490.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98615.011000 -2490.856837) translate(0,24)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98615.011000 -2490.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98615.011000 -2490.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98615.011000 -2490.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98615.011000 -2490.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97701.186798 -2487.856837) translate(0,24)">9</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97701.186798 -2487.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97701.186798 -2487.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97701.186798 -2487.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97701.186798 -2487.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98020.992204 -2486.856837) translate(0,24)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98020.992204 -2486.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98020.992204 -2486.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98020.992204 -2486.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98020.992204 -2486.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97801.186507 -2495.856837) translate(0,24)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97801.186507 -2495.856837) translate(0,54)">梯</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97801.186507 -2495.856837) translate(0,84)">机</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97801.186507 -2495.856837) translate(0,114)">房</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97855.584510 -2485.856837) translate(0,24)">地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97855.584510 -2485.856837) translate(0,54)">下</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97855.584510 -2485.856837) translate(0,84)">室</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97855.584510 -2485.856837) translate(0,114)">消</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97855.584510 -2485.856837) translate(0,144)">防</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97909.167710 -2485.856837) translate(0,24)">夜</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97909.167710 -2485.856837) translate(0,54)">景</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97909.167710 -2485.856837) translate(0,84)">灯  </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97909.167710 -2485.856837) translate(0,114)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97909.167710 -2485.856837) translate(0,144)">地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97909.167710 -2485.856837) translate(0,174)">埋</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97909.167710 -2485.856837) translate(0,204)">灯</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97967.556462 -2490.856837) translate(0,24)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97967.556462 -2490.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97967.556462 -2490.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97967.556462 -2490.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97967.556462 -2490.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97750.139661 -2488.856837) translate(0,24)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97750.139661 -2488.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97750.139661 -2488.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97750.139661 -2488.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97750.139661 -2488.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99821.014495 -2492.433863) translate(0,24)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99821.014495 -2492.433863) translate(0,54)">号</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99821.014495 -2492.433863) translate(0,84)">风</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99821.014495 -2492.433863) translate(0,114)">机</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99821.014495 -2492.433863) translate(0,144)">有</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99821.014495 -2492.433863) translate(0,174)">载</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99821.014495 -2492.433863) translate(0,204)">调</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99821.014495 -2492.433863) translate(0,234)">压</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99821.014495 -2492.433863) translate(0,264)">过</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99821.014495 -2492.433863) translate(0,294)">流</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99821.014495 -2492.433863) translate(0,324)">箱</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100196.885688 -2492.433863) translate(0,24)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100196.885688 -2492.433863) translate(0,54)">梯</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100196.885688 -2492.433863) translate(0,84)">机</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100196.885688 -2492.433863) translate(0,114)">房</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100140.672588 -2492.433863) translate(0,24)">地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100140.672588 -2492.433863) translate(0,54)">下</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100140.672588 -2492.433863) translate(0,84)">室</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100140.672588 -2492.433863) translate(0,114)">消</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100140.672588 -2492.433863) translate(0,144)">防</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100077.459489 -2492.433863) translate(0,24)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100077.459489 -2492.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100077.459489 -2492.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100077.459489 -2492.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100077.459489 -2492.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99945.449946 -2492.433863) translate(0,24)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99945.449946 -2492.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99945.449946 -2492.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99945.449946 -2492.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99945.449946 -2492.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99880.449800 -2492.433863) translate(0,24)">半</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99880.449800 -2492.433863) translate(0,54)">层</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99880.449800 -2492.433863) translate(0,84)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99880.449800 -2492.433863) translate(0,114)">暖</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99880.449800 -2492.433863) translate(0,144)">通</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99704.986446 -2492.433863) translate(0,24)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99704.986446 -2492.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99704.986446 -2492.433863) translate(0,84)">楚</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99704.986446 -2492.433863) translate(0,114)">雄</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99704.986446 -2492.433863) translate(0,144)">监</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99704.986446 -2492.433863) translate(0,174)">控</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99704.986446 -2492.433863) translate(0,204)">中</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99704.986446 -2492.433863) translate(0,234)">心</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99704.986446 -2492.433863) translate(0,264)">UPS</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99704.986446 -2492.433863) translate(0,294)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99704.986446 -2492.433863) translate(0,324)">源</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99581.375052 -2492.433863) translate(0,24)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99581.375052 -2492.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99581.375052 -2492.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99581.375052 -2492.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99581.375052 -2492.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99326.355964 -2492.433863) translate(0,24)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99326.355964 -2492.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99326.355964 -2492.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99326.355964 -2492.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99326.355964 -2492.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99263.513257 -2494.433863) translate(0,24)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99263.513257 -2494.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99263.513257 -2494.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99263.513257 -2494.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99263.513257 -2494.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99200.503859 -2491.433863) translate(0,24)">副</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99200.503859 -2491.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99200.503859 -2491.433863) translate(0,84)">餐</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99200.503859 -2491.433863) translate(0,114)">厅</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99395.541306 -2492.433863) translate(0,24)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99395.541306 -2492.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99395.541306 -2492.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99395.541306 -2492.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99395.541306 -2492.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99464.356255 -2492.433863) translate(0,24)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99464.356255 -2492.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99464.356255 -2492.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99464.356255 -2492.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99464.356255 -2492.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99525.161953 -2492.433863) translate(0,24)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99525.161953 -2492.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99525.161953 -2492.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99525.161953 -2492.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99525.161953 -2492.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99639.967795 -2492.433863) translate(0,24)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99639.967795 -2492.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99639.967795 -2492.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99639.967795 -2492.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99639.967795 -2492.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99764.792143 -2492.433863) translate(0,24)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99764.792143 -2492.433863) translate(0,54)">号</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99764.792143 -2492.433863) translate(0,84)">直</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99764.792143 -2492.433863) translate(0,114)">流</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99764.792143 -2492.433863) translate(0,144)">充</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99764.792143 -2492.433863) translate(0,174)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99764.792143 -2492.433863) translate(0,204)">10</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99764.792143 -2492.433863) translate(0,234)">kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99764.792143 -2492.433863) translate(0,264)">高</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99764.792143 -2492.433863) translate(0,294)">压</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99764.792143 -2492.433863) translate(0,324)">照</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99764.792143 -2492.433863) translate(0,354)">明</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100010.459344 -2492.433863) translate(0,24)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100010.459344 -2492.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100010.459344 -2492.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100010.459344 -2492.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100010.459344 -2492.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100260.496936 -2492.433863) translate(0,24)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100260.496936 -2492.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100260.496936 -2492.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100260.496936 -2492.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100260.496936 -2492.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100320.080427 -2492.433863) translate(0,24)">副</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100320.080427 -2492.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100320.080427 -2492.433863) translate(0,84)">食</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100320.080427 -2492.433863) translate(0,114)">堂</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98263.398149 -3452.795419) translate(0,40)">10kV开发区Ⅲ回线6号环网柜</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98263.398149 -3452.795419) translate(0,90)">育才路联络线1号电缆分接箱供电局办公楼Ⅱ回支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="54" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97960.622394 -2768.658923) translate(0,44)">0.4kVII段母线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99351.291488 -3382.353351) translate(0,40)">10kV线路电压互感器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98483.316915 -3374.117249) translate(0,40)">10kV线路电压互感器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3380eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98900.000000 -3218.000000) translate(0,16)">10kV母线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33bc630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98842.000000 -3306.000000) translate(0,16)">020</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32aed70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99130.000000 -3304.000000) translate(0,16)">010</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2521860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98838.000000 -3132.000000) translate(0,16)">002</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2521aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99130.000000 -2886.000000) translate(0,16)">410</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2600b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98771.000000 -2915.000000) translate(0,16)">420</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2de0fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99139.000000 -3131.000000) translate(0,16)">001</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2dcac10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98994.000000 -2724.000000) translate(0,16)">412</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_15ab520" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97749.139661 -2424.856837) translate(0,24)">(11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_15ab520" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97749.139661 -2424.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_15ab520" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97749.139661 -2424.856837) translate(0,84)">6,</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_15ab520" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97749.139661 -2424.856837) translate(0,114)">机</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_15ab520" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97749.139661 -2424.856837) translate(0,144)">房</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_15ab520" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97749.139661 -2424.856837) translate(0,174)">空</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_15ab520" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97749.139661 -2424.856837) translate(0,204)">调</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_15ab520" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97749.139661 -2424.856837) translate(0,234)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_15ab520" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97749.139661 -2424.856837) translate(0,264)">源)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_25190e0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97967.556462 -2428.856837) translate(0,24)">(11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_25190e0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97967.556462 -2428.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_25190e0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97967.556462 -2428.856837) translate(0,84)">2，</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_25190e0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97967.556462 -2428.856837) translate(0,114)">调</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_25190e0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97967.556462 -2428.856837) translate(0,144)">度</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_25190e0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97967.556462 -2428.856837) translate(0,174)">自</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_25190e0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97967.556462 -2428.856837) translate(0,204)">动</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_25190e0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97967.556462 -2428.856837) translate(0,234)">化)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2de2530" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97853.584510 -2418.856837) translate(0,24)">(-1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2de2530" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97853.584510 -2418.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2de2530" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97853.584510 -2418.856837) translate(0,84)">2)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_251f380" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97798.556462 -2440.856837) translate(0,24)">(13</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_251f380" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97798.556462 -2440.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_251f380" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97798.556462 -2440.856837) translate(0,84)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_251f380" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97798.556462 -2440.856837) translate(0,114)">，</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_251f380" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97798.556462 -2440.856837) translate(0,144)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_251f380" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97798.556462 -2440.856837) translate(0,174)">梯)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33a63c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98018.556462 -2423.856837) translate(0,24)">(11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33a63c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98018.556462 -2423.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33a63c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98018.556462 -2423.856837) translate(0,84)">4,</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33a63c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98018.556462 -2423.856837) translate(0,114)">通</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33a63c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98018.556462 -2423.856837) translate(0,144)">讯</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33a63c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98018.556462 -2423.856837) translate(0,174)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33a63c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98018.556462 -2423.856837) translate(0,204)">源</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33a63c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98018.556462 -2423.856837) translate(0,234)">室)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2619650" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97700.056462 -2423.856837) translate(0,24)">(9</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2619650" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97700.056462 -2423.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2619650" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97700.056462 -2423.856837) translate(0,84)">1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_26198e0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98614.056462 -2427.856837) translate(0,24)">(2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_26198e0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98614.056462 -2427.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_26198e0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98614.056462 -2427.856837) translate(0,84)">3)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1647d70" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98686.556462 -2427.856837) translate(0,24)">(10</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1647d70" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98686.556462 -2427.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1647d70" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98686.556462 -2427.856837) translate(0,84)">1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1647fb0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98357.556462 -2424.856837) translate(0,24)">(2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1647fb0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98357.556462 -2424.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1647fb0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98357.556462 -2424.856837) translate(0,84)">1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_26bd7b0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98556.056462 -2427.856837) translate(0,24)">(3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_26bd7b0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98556.056462 -2427.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_26bd7b0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98556.056462 -2427.856837) translate(0,84)">2)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_26bd9f0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98489.056462 -2426.856837) translate(0,24)">(8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_26bd9f0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98489.056462 -2426.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_26bd9f0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98489.056462 -2426.856837) translate(0,84)">1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24e07c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98423.056462 -2422.856837) translate(0,24)">(6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24e07c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98423.056462 -2422.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24e07c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98423.056462 -2422.856837) translate(0,84)">1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24e0a00" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98244.556462 -2423.856837) translate(0,24)">(1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24e0a00" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98244.556462 -2423.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24e0a00" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98244.556462 -2423.856837) translate(0,84)">1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33378f0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98303.556462 -2425.856837) translate(0,24)">(4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33378f0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98303.556462 -2425.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33378f0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98303.556462 -2425.856837) translate(0,84)">1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_3337b30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99201.556462 -2439.856837) translate(0,24)">(1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_3337b30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99201.556462 -2439.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_3337b30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99201.556462 -2439.856837) translate(0,84)">2)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_260a110" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99263.556462 -2430.856837) translate(0,24)">(2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_260a110" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99263.556462 -2430.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_260a110" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99263.556462 -2430.856837) translate(0,84)">4)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_260a350" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99326.556462 -2427.856837) translate(0,24)">(2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_260a350" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99326.556462 -2427.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_260a350" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99326.556462 -2427.856837) translate(0,84)">2)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33e2010" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99395.556462 -2426.856837) translate(0,24)">(3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33e2010" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99395.556462 -2426.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33e2010" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99395.556462 -2426.856837) translate(0,84)">1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33e2250" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99465.556462 -2427.856837) translate(0,24)">(1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33e2250" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99465.556462 -2427.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33e2250" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99465.556462 -2427.856837) translate(0,84)">1，</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33e2250" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99465.556462 -2427.856837) translate(0,114)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33e2250" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99465.556462 -2427.856837) translate(0,144)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33e2250" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99465.556462 -2427.856837) translate(0,174)">营</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33e2250" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99465.556462 -2427.856837) translate(0,204)">业</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33e2250" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99465.556462 -2427.856837) translate(0,234)">厅</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33e2250" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99465.556462 -2427.856837) translate(0,264)">照</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33e2250" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99465.556462 -2427.856837) translate(0,294)">明</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33e2250" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99465.556462 -2427.856837) translate(0,324)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33e2250" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99465.556462 -2427.856837) translate(0,354)">源)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2e1fb40" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99525.556462 -2427.856837) translate(0,24)">(5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2e1fb40" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99525.556462 -2427.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2e1fb40" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99525.556462 -2427.856837) translate(0,84)">1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2e1fdd0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99578.556462 -2428.856837) translate(0,24)">(11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2e1fdd0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99578.556462 -2428.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2e1fdd0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99578.556462 -2428.856837) translate(0,84)">7)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_165ecf0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99641.556462 -2427.856837) translate(0,24)">(3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_165ecf0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99641.556462 -2427.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_165ecf0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99641.556462 -2427.856837) translate(0,84)">3)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_165ef30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99880.556462 -2426.856837) translate(0,24)">(14</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_165ef30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99880.556462 -2426.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_165ef30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99880.556462 -2426.856837) translate(0,84)">1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_165f170" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99943.556462 -2426.856837) translate(0,24)">(11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_165f170" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99943.556462 -2426.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_165f170" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99943.556462 -2426.856837) translate(0,84)">3，</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_165f170" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99943.556462 -2426.856837) translate(0,114)">通</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_165f170" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99943.556462 -2426.856837) translate(0,144)">讯</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_165f170" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99943.556462 -2426.856837) translate(0,174)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_165f170" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99943.556462 -2426.856837) translate(0,204)">源</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_165f170" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99943.556462 -2426.856837) translate(0,234)">室)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2760590" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100008.556462 -2427.856837) translate(0,24)">(11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2760590" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100008.556462 -2427.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2760590" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100008.556462 -2427.856837) translate(0,84)">1，</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2760590" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100008.556462 -2427.856837) translate(0,114)">调</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2760590" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100008.556462 -2427.856837) translate(0,144)">度</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2760590" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100008.556462 -2427.856837) translate(0,174)">自</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2760590" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100008.556462 -2427.856837) translate(0,204)">动</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2760590" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100008.556462 -2427.856837) translate(0,234)">化)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_27607c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100078.056462 -2425.856837) translate(0,24)">(7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_27607c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100078.056462 -2425.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_27607c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100078.056462 -2425.856837) translate(0,84)">3)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2760a10" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100138.556462 -2426.856837) translate(0,24)">(-1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2760a10" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100138.556462 -2426.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_2760a10" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100138.556462 -2426.856837) translate(0,84)">1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24ff440" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100195.556462 -2435.856837) translate(0,24)">(13</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24ff440" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100195.556462 -2435.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24ff440" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100195.556462 -2435.856837) translate(0,84)">1，</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24ff440" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100195.556462 -2435.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24ff440" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100195.556462 -2435.856837) translate(0,144)">梯)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24ff690" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100321.556462 -2436.856837) translate(0,24)">(1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24ff690" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100321.556462 -2436.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24ff690" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100321.556462 -2436.856837) translate(0,84)">4)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24ff8c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100260.556462 -2427.856837) translate(0,24)">(11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24ff8c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100260.556462 -2427.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24ff8c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100260.556462 -2427.856837) translate(0,84)">5，</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24ff8c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100260.556462 -2427.856837) translate(0,114)">机</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24ff8c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100260.556462 -2427.856837) translate(0,144)">房</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24ff8c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100260.556462 -2427.856837) translate(0,174)">空</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24ff8c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100260.556462 -2427.856837) translate(0,204)">调</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24ff8c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100260.556462 -2427.856837) translate(0,234)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_24ff8c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100260.556462 -2427.856837) translate(0,264)">源)</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e59f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97111.000000 -2893.000000) translate(0,12)">注：801、802以及0.4kV开关位置信号均未采集</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1660a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98987.000000 -3260.000000) translate(0,16)">备自投</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="54" graphid="g_2dcc830" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98678.622831 -3003.649723) translate(0,44)">SCZ9-400/10</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="54" graphid="g_34b86a0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99216.622831 -3000.649723) translate(0,44)">SCZ9-400/10</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="54" graphid="g_33b3040" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98964.622831 -3111.649723) translate(0,44)">0901</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_33b34f0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97483.622394 -2611.658923) translate(0,28)">42X</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_26a52a0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97542.622394 -2612.658923) translate(0,28)">42W</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_26a5830" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97519.622394 -2540.658923) translate(0,28)">630A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_33a5d00" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97597.622394 -2615.658923) translate(0,28)">41W</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2dcb0e0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97575.622394 -2540.658923) translate(0,28)">400A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2dcb3b0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97669.622394 -2614.658923) translate(0,28)">42V</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2dcb810" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97633.622394 -2539.658923) translate(0,28)">400A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2f4e360" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97704.622394 -2539.658923) translate(0,28)">400A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2f4e570" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97724.622394 -2613.658923) translate(0,28)">42V</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2f4e7b0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97754.622394 -2540.658923) translate(0,28)">150A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2e73c50" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97774.622394 -2613.658923) translate(0,28)">42T</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2e740d0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97805.622394 -2539.658923) translate(0,28)">100A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2e27580" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97829.622394 -2613.658923) translate(0,28)">42S</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2e27940" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97882.622394 -2611.658923) translate(0,28)">42R</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_26e34c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97861.622394 -2539.658923) translate(0,28)">200A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_26e38c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97936.622394 -2611.658923) translate(0,28)">42Q</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_26e3c80" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97916.622394 -2539.658923) translate(0,28)">250A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_165f8c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97990.622394 -2613.658923) translate(0,28)">42P</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_165fcf0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97971.622394 -2539.658923) translate(0,28)">200A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_15f7130" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98025.622394 -2539.658923) translate(0,28)">200A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_15f7370" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98077.992204 -2485.856837) translate(0,24)">半</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_15f7370" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98077.992204 -2485.856837) translate(0,54)">层</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_15f7370" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98077.992204 -2485.856837) translate(0,84)">暖</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_15f7370" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98077.992204 -2485.856837) translate(0,114)">通</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_15f75c0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98047.622394 -2612.658923) translate(0,28)">42N</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_15f78f0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98077.556462 -2434.856837) translate(0,24)">(14</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_15f78f0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98077.556462 -2434.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_15f78f0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98077.556462 -2434.856837) translate(0,84)">2)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_26049f0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98082.622394 -2538.658923) translate(0,28)">300A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2604c30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98104.622394 -2611.658923) translate(0,28)">42M</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2605090" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98137.622394 -2539.658923) translate(0,28)">30A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_260c820" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98155.622394 -2612.658923) translate(0,28)">42L</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_260cc10" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98217.622394 -2611.658923) translate(0,28)">42K</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_260d170" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98190.622394 -2540.658923) translate(0,28)">75A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2e6f430" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98253.622394 -2542.658923) translate(0,28)">50A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2e6f670" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98276.622394 -2609.658923) translate(0,28)">42J</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2e6fb10" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98308.622394 -2542.658923) translate(0,28)">75A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_1685970" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98330.622394 -2610.658923) translate(0,28)">42H</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_1685e40" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98366.622394 -2543.658923) translate(0,28)">75A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_1686260" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98392.622394 -2611.658923) translate(0,28)">42G</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_161c5f0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98433.622394 -2543.658923) translate(0,28)">50A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_161ca50" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98460.622394 -2610.658923) translate(0,28)">42F</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_161cd90" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98525.622394 -2608.658923) translate(0,28)">42E</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2db9470" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98495.622394 -2542.658923) translate(0,28)">100A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2db9890" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98560.622394 -2542.658923) translate(0,28)">100A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2db9ad0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98586.622394 -2609.658923) translate(0,28)">42D</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_15affd0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98622.622394 -2543.658923) translate(0,28)">100A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_15b0430" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98657.622394 -2611.658923) translate(0,28)">42C</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_15b0740" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98692.622394 -2544.658923) translate(0,28)">100A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_164de30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98745.622394 -2611.658923) translate(0,28)">42B</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_164e270" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98772.556462 -2429.856837) translate(0,24)">(12</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_164e270" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98772.556462 -2429.856837) translate(0,54)">至</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_164e270" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98772.556462 -2429.856837) translate(0,84)">1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_164e690" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98773.807445 -2492.856837) translate(0,24)">12</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_164e690" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98773.807445 -2492.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_164e690" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98773.807445 -2492.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_164e690" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98773.807445 -2492.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_164e690" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98773.807445 -2492.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_1680e60" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98783.622394 -2542.658923) translate(0,28)">100A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_15b4430" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98853.622394 -2612.658923) translate(0,28)">42A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_15b4750" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98899.622394 -2539.658923) translate(0,28)">800A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_260fc80" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99081.123632 -2241.856837) translate(0,24)">2号电容器组</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_156fe40" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99076.622394 -2612.658923) translate(0,28)">41A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_1570400" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99122.622394 -2539.658923) translate(0,28)">800A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_26e2640" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99208.622394 -2536.658923) translate(0,28)">100A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_26e2b30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99176.622394 -2610.658923) translate(0,28)">41B</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_26e2d70" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99236.622394 -2611.658923) translate(0,28)">41C</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_26e2fb0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99268.622394 -2539.658923) translate(0,28)">100A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_26e31f0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99298.622394 -2612.658923) translate(0,28)">41D</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_1627470" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99330.622394 -2540.658923) translate(0,28)">100A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_16276b0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99401.622394 -2541.658923) translate(0,28)">100A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_16278f0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99369.622394 -2613.658923) translate(0,28)">41E</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_1627b30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99468.622394 -2540.658923) translate(0,28)">100A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_1627d70" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99436.622394 -2612.658923) translate(0,28)">41F</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_1627fb0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99533.622394 -2541.658923) translate(0,28)">50A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_16281f0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99500.622394 -2611.658923) translate(0,28)">41G</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2ddfb30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99590.622394 -2542.658923) translate(0,28)">75A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2ddfd70" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99555.622394 -2611.658923) translate(0,28)">41H</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2ddffb0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99646.622394 -2544.658923) translate(0,28)">75A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2de01f0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99614.622394 -2611.658923) translate(0,28)">41J</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2de0430" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99710.622394 -2545.658923) translate(0,28)">50A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2de0670" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99677.622394 -2611.658923) translate(0,28)">41K</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_2de08b0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99770.622394 -2547.658923) translate(0,28)">75A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_33a3360" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99737.622394 -2613.658923) translate(0,28)">41L</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_33a35a0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99827.622394 -2544.658923) translate(0,28)">30A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_33a37e0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99791.622394 -2611.658923) translate(0,28)">41M</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_33a3a20" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99889.622394 -2544.658923) translate(0,28)">300A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_33a3c60" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99855.622394 -2610.658923) translate(0,28)">41N</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_33a3ea0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99951.622394 -2543.658923) translate(0,28)">200A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_33a40e0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99917.622394 -2609.658923) translate(0,28)">41P</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_33a4320" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100017.622394 -2544.658923) translate(0,28)">200A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_15e7ef0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99983.622394 -2610.658923) translate(0,28)">41Q</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_15e8130" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100084.622394 -2546.658923) translate(0,28)">150A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_15e8370" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100050.622394 -2612.658923) translate(0,28)">41R</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_15e85b0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100148.622394 -2545.658923) translate(0,28)">200A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_15e87f0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100112.622394 -2610.658923) translate(0,28)">41S</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_15e8a30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100205.622394 -2548.658923) translate(0,28)">100A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_15e8c70" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100169.622394 -2613.658923) translate(0,28)">41T</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_15e8eb0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100267.622394 -2548.658923) translate(0,28)">150A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_33ef510" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100231.622394 -2613.658923) translate(0,28)">41U</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_33ef980" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100327.622394 -2546.658923) translate(0,28)">300A</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_33efda0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 100291.622394 -2611.658923) translate(0,28)">41V</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_24fe6d0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97947.622394 -2233.658923) translate(0,28)">QF</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_24febc0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98129.622394 -2295.658923) translate(0,28)">QF1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_3495610" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98122.622394 -2166.658923) translate(0,28)">QF2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_3495850" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98208.622394 -2278.658923) translate(0,28)">发电机接口</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" graphid="g_34963f0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98202.622394 -2150.658923) translate(0,28)">充电区低压配电箱</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="51" graphid="g_15b0d50" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97958.622394 -2119.658923) translate(0,41)">楚雄供电局户外电缆分接相</text>
  </g><g id="Line_Layer">
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99189" x2="99439" y1="-3424" y2="-3424"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98824" x2="98824" y1="-3435" y2="-3368"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99189" x2="99189" y1="-3424" y2="-3357"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99024" x2="99072" y1="-3181" y2="-3181"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99072" x2="99072" y1="-3181" y2="-3145"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99024" x2="99024" y1="-3015" y2="-3045"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99024" x2="99024" y1="-3057" y2="-3084"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99024" x2="99024" y1="-3126" y2="-3151"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99024" x2="99024" y1="-3163" y2="-3229"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99024" x2="99013" y1="-3163" y2="-3144"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99024" x2="99035" y1="-3163" y2="-3144"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99024" x2="99013" y1="-3151" y2="-3132"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99024" x2="99035" y1="-3151" y2="-3132"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99024" x2="99035" y1="-3045" y2="-3064"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99024" x2="99013" y1="-3045" y2="-3064"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99024" x2="99035" y1="-3057" y2="-3076"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99024" x2="99013" y1="-3057" y2="-3076"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98825" x2="98825" y1="-2858" y2="-2743"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99188" x2="99188" y1="-2826" y2="-2743"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97706" x2="98950" y1="-2743" y2="-2743"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98963" x2="98997" y1="-2743" y2="-2743"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99023" x2="99056" y1="-2743" y2="-2743"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99069" x2="99049" y1="-2743" y2="-2754"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99069" x2="99049" y1="-2743" y2="-2732"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99057" x2="99037" y1="-2743" y2="-2754"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99057" x2="99037" y1="-2743" y2="-2732"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98950" x2="98969" y1="-2743" y2="-2732"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98950" x2="98969" y1="-2743" y2="-2754"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98962" x2="98981" y1="-2743" y2="-2732"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98962" x2="98981" y1="-2743" y2="-2754"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98824" x2="98835" y1="-3407" y2="-3389"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98824" x2="98813" y1="-3407" y2="-3389"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98835" x2="98813" y1="-3389" y2="-3389"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99189" x2="99200" y1="-3406" y2="-3387"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99189" x2="99178" y1="-3406" y2="-3387"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99200" x2="99178" y1="-3387" y2="-3387"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97518" x2="97518" y1="-2651" y2="-2626"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97518" x2="97518" y1="-2584" y2="-2557"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97518" x2="97508" y1="-2663" y2="-2644"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97518" x2="97530" y1="-2663" y2="-2644"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97518" x2="97508" y1="-2651" y2="-2632"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97518" x2="97530" y1="-2651" y2="-2632"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97518" x2="97530" y1="-2545" y2="-2564"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97518" x2="97508" y1="-2545" y2="-2564"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97518" x2="97530" y1="-2557" y2="-2576"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97518" x2="97508" y1="-2557" y2="-2576"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97518" x2="97518" y1="-2677" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97518" x2="97518" y1="-2800" y2="-2694"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97518" x2="97514" y1="-2695" y2="-2683"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97513" x2="97524" y1="-2677" y2="-2677"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97518" x2="97518" y1="-2546" y2="-2212"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97518" x2="97507" y1="-2498" y2="-2518"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97518" x2="97529" y1="-2498" y2="-2518"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99068" x2="100326" y1="-2743" y2="-2743"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97578" x2="97578" y1="-2799" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97578" x2="97578" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97577" x2="97577" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97578" x2="97567" y1="-2665" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97578" x2="97589" y1="-2665" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97578" x2="97567" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97578" x2="97589" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97577" x2="97588" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97577" x2="97566" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97577" x2="97588" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97577" x2="97566" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97577" x2="97577" y1="-2546" y2="-2477"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97577" x2="97566" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97577" x2="97588" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97566" x2="97589" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97633" x2="97633" y1="-2788" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97633" x2="97633" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97632" x2="97632" y1="-2586" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97633" x2="97622" y1="-2665" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97633" x2="97644" y1="-2665" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97633" x2="97622" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97633" x2="97644" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97632" x2="97643" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97632" x2="97621" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97632" x2="97643" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97632" x2="97621" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97632" x2="97632" y1="-2546" y2="-2501"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97632" x2="97622" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97632" x2="97643" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97622" x2="97643" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97705" x2="97705" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97705" x2="97705" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97705" x2="97705" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97705" x2="97694" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97705" x2="97716" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97705" x2="97694" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97705" x2="97716" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97705" x2="97716" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97705" x2="97694" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97705" x2="97716" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97705" x2="97694" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97705" x2="97705" y1="-2546" y2="-2500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97705" x2="97695" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97705" x2="97716" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97695" x2="97717" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97755" x2="97755" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97755" x2="97755" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97755" x2="97755" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97755" x2="97744" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97755" x2="97766" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97755" x2="97744" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97755" x2="97766" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97755" x2="97766" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97755" x2="97744" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97755" x2="97766" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97755" x2="97744" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97755" x2="97755" y1="-2546" y2="-2500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97755" x2="97744" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97755" x2="97765" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97744" x2="97766" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97805" x2="97805" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97805" x2="97805" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97805" x2="97805" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97805" x2="97794" y1="-2665" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97805" x2="97816" y1="-2665" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97805" x2="97794" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97805" x2="97816" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97805" x2="97816" y1="-2545" y2="-2564"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97805" x2="97794" y1="-2545" y2="-2564"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97805" x2="97816" y1="-2557" y2="-2576"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97805" x2="97794" y1="-2557" y2="-2576"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97805" x2="97805" y1="-2545" y2="-2500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97805" x2="97794" y1="-2499" y2="-2518"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97805" x2="97815" y1="-2499" y2="-2518"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97794" x2="97816" y1="-2518" y2="-2518"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97916" x2="97916" y1="-2742" y2="-2662"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97916" x2="97916" y1="-2651" y2="-2626"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97916" x2="97916" y1="-2585" y2="-2558"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97916" x2="97905" y1="-2664" y2="-2644"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97916" x2="97927" y1="-2664" y2="-2644"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97916" x2="97905" y1="-2652" y2="-2632"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97916" x2="97927" y1="-2652" y2="-2632"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97916" x2="97927" y1="-2545" y2="-2564"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97916" x2="97905" y1="-2545" y2="-2564"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97916" x2="97927" y1="-2557" y2="-2576"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97916" x2="97905" y1="-2557" y2="-2576"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97916" x2="97916" y1="-2545" y2="-2500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97916" x2="97906" y1="-2499" y2="-2518"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97916" x2="97927" y1="-2499" y2="-2518"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97906" x2="97928" y1="-2518" y2="-2518"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97862" x2="97862" y1="-2742" y2="-2662"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97862" x2="97862" y1="-2651" y2="-2626"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97862" x2="97862" y1="-2584" y2="-2558"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97862" x2="97851" y1="-2663" y2="-2644"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97862" x2="97873" y1="-2663" y2="-2644"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97862" x2="97851" y1="-2651" y2="-2632"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97862" x2="97873" y1="-2651" y2="-2632"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97862" x2="97873" y1="-2545" y2="-2564"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97862" x2="97851" y1="-2545" y2="-2564"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97862" x2="97873" y1="-2557" y2="-2576"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97862" x2="97851" y1="-2557" y2="-2576"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97862" x2="97862" y1="-2545" y2="-2499"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97862" x2="97851" y1="-2499" y2="-2518"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97862" x2="97873" y1="-2499" y2="-2518"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97851" x2="97873" y1="-2518" y2="-2518"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97970" x2="97970" y1="-2742" y2="-2662"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97970" x2="97970" y1="-2651" y2="-2626"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97970" x2="97970" y1="-2584" y2="-2558"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97970" x2="97959" y1="-2663" y2="-2644"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97970" x2="97981" y1="-2663" y2="-2644"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97970" x2="97959" y1="-2651" y2="-2632"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97970" x2="97981" y1="-2651" y2="-2632"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97970" x2="97981" y1="-2545" y2="-2564"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97970" x2="97959" y1="-2545" y2="-2564"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97970" x2="97981" y1="-2557" y2="-2576"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97970" x2="97959" y1="-2557" y2="-2576"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97970" x2="97970" y1="-2545" y2="-2499"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97970" x2="97959" y1="-2499" y2="-2518"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97970" x2="97980" y1="-2499" y2="-2518"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97959" x2="97981" y1="-2518" y2="-2518"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98026" x2="98026" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98026" x2="98026" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98025" x2="98025" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98026" x2="98015" y1="-2665" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98026" x2="98036" y1="-2665" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98026" x2="98015" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98026" x2="98036" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98025" x2="98035" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98025" x2="98014" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98025" x2="98035" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98025" x2="98013" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98025" x2="98025" y1="-2546" y2="-2501"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98025" x2="98014" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98025" x2="98036" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98014" x2="98036" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98081" x2="98081" y1="-2742" y2="-2662"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98081" x2="98081" y1="-2651" y2="-2626"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98081" x2="98081" y1="-2584" y2="-2558"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98081" x2="98071" y1="-2664" y2="-2644"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98081" x2="98093" y1="-2664" y2="-2644"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98082" x2="98071" y1="-2652" y2="-2632"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98082" x2="98093" y1="-2652" y2="-2632"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98081" x2="98093" y1="-2544" y2="-2563"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98081" x2="98071" y1="-2544" y2="-2563"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98081" x2="98093" y1="-2556" y2="-2575"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98081" x2="98071" y1="-2556" y2="-2575"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98081" x2="98081" y1="-2544" y2="-2499"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98081" x2="98070" y1="-2498" y2="-2517"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98081" x2="98092" y1="-2498" y2="-2517"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98070" x2="98092" y1="-2517" y2="-2517"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98493" x2="98493" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98493" x2="98493" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98492" x2="98492" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98493" x2="98482" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98493" x2="98504" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98493" x2="98482" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98493" x2="98504" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98493" x2="98504" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98493" x2="98482" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98493" x2="98504" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98493" x2="98482" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98493" x2="98493" y1="-2546" y2="-2500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98493" x2="98482" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98493" x2="98504" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98482" x2="98504" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98557" x2="98557" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98557" x2="98557" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98556" x2="98556" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98557" x2="98546" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98557" x2="98567" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98557" x2="98546" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98557" x2="98568" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98557" x2="98568" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98557" x2="98546" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98557" x2="98567" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98557" x2="98546" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98557" x2="98557" y1="-2546" y2="-2500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98557" x2="98546" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98557" x2="98568" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98546" x2="98569" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98363" x2="98363" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98363" x2="98363" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98362" x2="98362" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98363" x2="98352" y1="-2665" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98363" x2="98373" y1="-2665" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98363" x2="98352" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98363" x2="98373" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98363" x2="98373" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98363" x2="98352" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98363" x2="98373" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98363" x2="98352" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98363" x2="98363" y1="-2546" y2="-2501"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98363" x2="98352" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98363" x2="98374" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98352" x2="98375" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98620" x2="98620" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98620" x2="98620" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98619" x2="98619" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98620" x2="98609" y1="-2665" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98620" x2="98631" y1="-2665" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98620" x2="98609" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98620" x2="98631" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98620" x2="98631" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98620" x2="98609" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98620" x2="98631" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98620" x2="98609" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98620" x2="98620" y1="-2546" y2="-2501"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98620" x2="98609" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98620" x2="98631" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98609" x2="98631" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98690" x2="98690" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98690" x2="98690" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98689" x2="98689" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98690" x2="98679" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98690" x2="98701" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98690" x2="98679" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98690" x2="98701" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98690" x2="98701" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98690" x2="98679" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98690" x2="98701" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98690" x2="98679" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98690" x2="98690" y1="-2546" y2="-2500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98690" x2="98679" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98690" x2="98701" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98679" x2="98701" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98428" x2="98428" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98428" x2="98428" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98427" x2="98427" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98428" x2="98417" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98428" x2="98439" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98428" x2="98417" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98428" x2="98439" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98428" x2="98439" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98428" x2="98417" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98428" x2="98439" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98428" x2="98417" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98428" x2="98428" y1="-2546" y2="-2500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98428" x2="98417" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98428" x2="98439" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98417" x2="98440" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98249" x2="98249" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98249" x2="98249" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98249" x2="98249" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98249" x2="98238" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98249" x2="98260" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98249" x2="98238" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98249" x2="98260" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98249" x2="98260" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98249" x2="98238" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98249" x2="98260" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98249" x2="98238" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98249" x2="98249" y1="-2546" y2="-2500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98249" x2="98239" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98249" x2="98260" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98239" x2="98261" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98306" x2="98306" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98306" x2="98306" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98306" x2="98306" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98306" x2="98295" y1="-2665" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98306" x2="98317" y1="-2665" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98306" x2="98295" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98306" x2="98317" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98306" x2="98317" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98306" x2="98295" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98306" x2="98317" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98306" x2="98295" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98306" x2="98306" y1="-2546" y2="-2501"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98306" x2="98296" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98306" x2="98317" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98296" x2="98318" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98782" x2="98782" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98782" x2="98782" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98781" x2="98781" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98782" x2="98771" y1="-2665" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98782" x2="98793" y1="-2665" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98782" x2="98771" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98782" x2="98793" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98782" x2="98793" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98782" x2="98771" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98782" x2="98793" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98782" x2="98771" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98782" x2="98782" y1="-2546" y2="-2501"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98783" x2="98772" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98783" x2="98793" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98772" x2="98794" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98189" x2="98189" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98189" x2="98189" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98189" x2="98189" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98189" x2="98178" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98189" x2="98200" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98189" x2="98178" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98189" x2="98200" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98189" x2="98200" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98189" x2="98178" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98189" x2="98200" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98189" x2="98178" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98189" x2="98189" y1="-2546" y2="-2500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98190" x2="98179" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98190" x2="98200" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98179" x2="98201" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98136" x2="98136" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98136" x2="98136" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98136" x2="98136" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98136" x2="98125" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98136" x2="98147" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98136" x2="98125" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98136" x2="98147" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98136" x2="98147" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98136" x2="98125" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98136" x2="98147" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98136" x2="98125" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98137" x2="98137" y1="-2546" y2="-2500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98137" x2="98126" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98137" x2="98148" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98126" x2="98148" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98890" x2="98890" y1="-2547" y2="-2396"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98890" x2="98890" y1="-2742" y2="-2665"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97633" x2="99188" y1="-2788" y2="-2788"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97519" x2="98825" y1="-2800" y2="-2800"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99206" x2="99206" y1="-2742" y2="-2662"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99206" x2="99206" y1="-2651" y2="-2626"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99206" x2="99206" y1="-2584" y2="-2558"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99206" x2="99195" y1="-2663" y2="-2644"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99206" x2="99217" y1="-2663" y2="-2644"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99206" x2="99195" y1="-2651" y2="-2632"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99206" x2="99217" y1="-2651" y2="-2632"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99206" x2="99217" y1="-2545" y2="-2564"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99206" x2="99195" y1="-2545" y2="-2564"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99206" x2="99217" y1="-2557" y2="-2576"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99206" x2="99195" y1="-2557" y2="-2576"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99206" x2="99206" y1="-2545" y2="-2499"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99206" x2="99195" y1="-2499" y2="-2518"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99206" x2="99217" y1="-2499" y2="-2518"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99195" x2="99217" y1="-2518" y2="-2518"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99268" x2="99268" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99268" x2="99268" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99268" x2="99268" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99268" x2="99258" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99268" x2="99279" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99268" x2="99258" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99268" x2="99279" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99268" x2="99279" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99268" x2="99258" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99268" x2="99279" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99268" x2="99258" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99269" x2="99269" y1="-2546" y2="-2500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99269" x2="99258" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99269" x2="99280" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99258" x2="99280" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99331" x2="99331" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99331" x2="99331" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99331" x2="99331" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99331" x2="99321" y1="-2665" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99331" x2="99342" y1="-2665" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99331" x2="99321" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99331" x2="99342" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99331" x2="99342" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99331" x2="99321" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99331" x2="99342" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99331" x2="99321" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99331" x2="99331" y1="-2546" y2="-2501"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99331" x2="99320" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99331" x2="99342" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99320" x2="99342" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99402" x2="99402" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99402" x2="99402" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99401" x2="99401" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99402" x2="99390" y1="-2665" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99402" x2="99412" y1="-2665" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99402" x2="99390" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99402" x2="99412" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99402" x2="99412" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99402" x2="99390" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99402" x2="99412" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99402" x2="99390" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99402" x2="99402" y1="-2546" y2="-2501"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99402" x2="99391" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99402" x2="99413" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99391" x2="99413" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99470" x2="99470" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99470" x2="99470" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99469" x2="99469" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99470" x2="99458" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99470" x2="99480" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99470" x2="99459" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99470" x2="99480" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99469" x2="99479" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99469" x2="99458" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99469" x2="99479" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99469" x2="99457" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99469" x2="99469" y1="-2546" y2="-2500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99469" x2="99458" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99469" x2="99480" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99458" x2="99480" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99532" x2="99532" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99532" x2="99532" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99532" x2="99532" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99532" x2="99522" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99532" x2="99543" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99532" x2="99522" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99532" x2="99543" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99532" x2="99543" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99532" x2="99522" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99532" x2="99543" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99532" x2="99522" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99533" x2="99533" y1="-2546" y2="-2500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99533" x2="99522" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99533" x2="99544" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99522" x2="99544" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99588" x2="99588" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99588" x2="99588" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99587" x2="99587" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99588" x2="99577" y1="-2665" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99588" x2="99599" y1="-2665" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99588" x2="99577" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99588" x2="99599" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99588" x2="99599" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99588" x2="99577" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99588" x2="99598" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99588" x2="99577" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99588" x2="99588" y1="-2546" y2="-2501"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99588" x2="99577" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99588" x2="99599" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99577" x2="99600" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99646" x2="99646" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99646" x2="99646" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99646" x2="99646" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99646" x2="99635" y1="-2665" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99646" x2="99657" y1="-2665" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99646" x2="99635" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99646" x2="99657" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99646" x2="99657" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99646" x2="99635" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99646" x2="99657" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99646" x2="99635" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99646" x2="99646" y1="-2546" y2="-2501"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99646" x2="99636" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99646" x2="99657" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99636" x2="99657" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99709" x2="99709" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99709" x2="99709" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99709" x2="99709" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99709" x2="99698" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99709" x2="99720" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99709" x2="99698" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99709" x2="99720" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99709" x2="99720" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99709" x2="99698" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99709" x2="99720" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99709" x2="99698" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99709" x2="99709" y1="-2546" y2="-2500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99709" x2="99699" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99709" x2="99720" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99699" x2="99721" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99769" x2="99769" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99769" x2="99769" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99769" x2="99769" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99769" x2="99758" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99769" x2="99780" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99769" x2="99758" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99769" x2="99780" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99769" x2="99780" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99769" x2="99758" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99769" x2="99780" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99769" x2="99758" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99769" x2="99769" y1="-2546" y2="-2500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99770" x2="99758" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99770" x2="99780" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99758" x2="99781" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99824" x2="99824" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99824" x2="99824" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99823" x2="99823" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99824" x2="99813" y1="-2665" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99824" x2="99835" y1="-2665" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99824" x2="99813" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99824" x2="99835" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99824" x2="99835" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99824" x2="99813" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99824" x2="99835" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99824" x2="99813" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99824" x2="99824" y1="-2546" y2="-2501"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99825" x2="99813" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99825" x2="99835" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99813" x2="99836" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99887" x2="99887" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99887" x2="99887" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99887" x2="99887" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99887" x2="99876" y1="-2665" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99887" x2="99898" y1="-2665" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99887" x2="99876" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99887" x2="99898" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99887" x2="99898" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99887" x2="99876" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99887" x2="99898" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99887" x2="99876" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99887" x2="99887" y1="-2546" y2="-2501"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99887" x2="99877" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99887" x2="99898" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99877" x2="99899" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99951" x2="99951" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99951" x2="99951" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99951" x2="99951" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99951" x2="99940" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99951" x2="99962" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99951" x2="99940" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99951" x2="99962" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99951" x2="99962" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99951" x2="99940" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99951" x2="99962" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99951" x2="99940" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99951" x2="99951" y1="-2546" y2="-2500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99951" x2="99941" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99951" x2="99963" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99941" x2="99963" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100015" x2="100015" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100015" x2="100015" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100015" x2="100015" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100015" x2="100004" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100015" x2="100026" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100015" x2="100004" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100015" x2="100026" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100015" x2="100026" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100015" x2="100004" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100015" x2="100026" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100015" x2="100004" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100015" x2="100015" y1="-2546" y2="-2500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100015" x2="100004" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100015" x2="100026" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100004" x2="100026" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100083" x2="100083" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100083" x2="100083" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100083" x2="100083" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100083" x2="100072" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100083" x2="100094" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100083" x2="100072" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100083" x2="100094" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100083" x2="100094" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100083" x2="100072" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100083" x2="100094" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100083" x2="100072" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100084" x2="100084" y1="-2546" y2="-2500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100084" x2="100073" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100084" x2="100095" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100073" x2="100095" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100146" x2="100146" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100146" x2="100146" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100146" x2="100146" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100146" x2="100136" y1="-2665" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100146" x2="100157" y1="-2665" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100146" x2="100136" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100146" x2="100157" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100146" x2="100157" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100146" x2="100136" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100146" x2="100157" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100146" x2="100136" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100147" x2="100147" y1="-2546" y2="-2501"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100147" x2="100136" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100147" x2="100158" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100136" x2="100158" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100202" x2="100202" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100202" x2="100202" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100202" x2="100202" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100202" x2="100191" y1="-2665" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100202" x2="100213" y1="-2665" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100202" x2="100192" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100202" x2="100213" y1="-2653" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100202" x2="100213" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100202" x2="100191" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100202" x2="100213" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100202" x2="100191" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100202" x2="100202" y1="-2546" y2="-2501"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100202" x2="100191" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100202" x2="100213" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100191" x2="100213" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100265" x2="100265" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100265" x2="100265" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100265" x2="100265" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100265" x2="100255" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100265" x2="100276" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100265" x2="100255" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100265" x2="100276" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100265" x2="100276" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100265" x2="100255" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100265" x2="100276" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100265" x2="100255" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100266" x2="100266" y1="-2546" y2="-2500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100266" x2="100255" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100266" x2="100277" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100255" x2="100277" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100326" x2="100326" y1="-2743" y2="-2663"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100326" x2="100326" y1="-2652" y2="-2627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100325" x2="100325" y1="-2585" y2="-2559"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100326" x2="100315" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100326" x2="100336" y1="-2664" y2="-2645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100326" x2="100315" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100326" x2="100336" y1="-2652" y2="-2633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100326" x2="100336" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100326" x2="100315" y1="-2546" y2="-2565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100325" x2="100336" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100325" x2="100315" y1="-2558" y2="-2577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100326" x2="100326" y1="-2546" y2="-2500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100326" x2="100315" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100326" x2="100337" y1="-2500" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="100315" x2="100337" y1="-2519" y2="-2519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99024" x2="99024" y1="-3127" y2="-3083"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98696" x2="98696" y1="-3364" y2="-3392"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98729" x2="98729" y1="-3364" y2="-3392"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99320" x2="99320" y1="-3387" y2="-3359"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99287" x2="99287" y1="-3387" y2="-3359"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98435" x2="98824" y1="-3435" y2="-3435"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98750" x2="98824" y1="-3377" y2="-3377"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99189" x2="99266" y1="-3374" y2="-3374"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98825" x2="98825" y1="-2956" y2="-2931"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98825" x2="98825" y1="-2889" y2="-2862"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98825" x2="98815" y1="-2968" y2="-2949"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98825" x2="98837" y1="-2968" y2="-2949"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98825" x2="98815" y1="-2956" y2="-2937"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98825" x2="98837" y1="-2956" y2="-2937"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98825" x2="98837" y1="-2850" y2="-2869"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98825" x2="98815" y1="-2850" y2="-2869"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98825" x2="98837" y1="-2862" y2="-2881"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98825" x2="98815" y1="-2862" y2="-2881"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98825" x2="98825" y1="-3003" y2="-2968"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99188" x2="99188" y1="-2931" y2="-2906"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99188" x2="99188" y1="-2864" y2="-2837"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99188" x2="99178" y1="-2943" y2="-2924"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99188" x2="99200" y1="-2943" y2="-2924"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99188" x2="99178" y1="-2931" y2="-2912"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99188" x2="99200" y1="-2931" y2="-2912"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99188" x2="99200" y1="-2825" y2="-2844"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99188" x2="99178" y1="-2825" y2="-2844"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99188" x2="99200" y1="-2837" y2="-2856"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99188" x2="99178" y1="-2837" y2="-2856"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99188" x2="99188" y1="-3001" y2="-2943"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97506" x2="97529" y1="-2518" y2="-2518"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98889" x2="98889" y1="-2586" y2="-2560"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98890" x2="98879" y1="-2666" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98890" x2="98901" y1="-2666" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98890" x2="98879" y1="-2654" y2="-2634"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98890" x2="98901" y1="-2654" y2="-2634"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98890" x2="98901" y1="-2547" y2="-2566"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98890" x2="98879" y1="-2547" y2="-2566"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98890" x2="98901" y1="-2559" y2="-2578"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98890" x2="98879" y1="-2559" y2="-2578"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98890" x2="98890" y1="-2653" y2="-2628"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98898" x2="98880" y1="-2431" y2="-2442"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98899" x2="98881" y1="-2414" y2="-2425"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98882" x2="98898" y1="-2425" y2="-2430"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98891" x2="98891" y1="-2373" y2="-2347"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98864" x2="98836" y1="-2303" y2="-2321"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98856" x2="98837" y1="-2299" y2="-2311"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98852" x2="98891" y1="-2312" y2="-2346"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98806" x2="98845" y1="-2271" y2="-2305"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98926" x2="98945" y1="-2299" y2="-2311"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98930" x2="98891" y1="-2312" y2="-2346"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98976" x2="98937" y1="-2271" y2="-2305"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98918" x2="98946" y1="-2303" y2="-2321"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98887" x2="98806" y1="-2269" y2="-2269"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98976" x2="98898" y1="-2269" y2="-2269"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98888" x2="98888" y1="-2257" y2="-2280"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98897" x2="98897" y1="-2249" y2="-2287"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99112" x2="99112" y1="-2586" y2="-2560"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99113" x2="99102" y1="-2666" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99113" x2="99124" y1="-2666" y2="-2646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99113" x2="99102" y1="-2654" y2="-2634"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99113" x2="99124" y1="-2654" y2="-2634"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99113" x2="99124" y1="-2547" y2="-2566"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99113" x2="99102" y1="-2547" y2="-2566"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99113" x2="99124" y1="-2559" y2="-2578"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99113" x2="99102" y1="-2559" y2="-2578"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99113" x2="99113" y1="-2653" y2="-2628"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99121" x2="99103" y1="-2431" y2="-2442"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99122" x2="99104" y1="-2414" y2="-2425"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99105" x2="99121" y1="-2425" y2="-2430"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99114" x2="99114" y1="-2373" y2="-2347"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99087" x2="99059" y1="-2303" y2="-2321"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99079" x2="99060" y1="-2299" y2="-2311"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99075" x2="99114" y1="-2312" y2="-2346"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99029" x2="99068" y1="-2271" y2="-2305"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99149" x2="99168" y1="-2299" y2="-2311"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99153" x2="99114" y1="-2312" y2="-2346"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99199" x2="99160" y1="-2271" y2="-2305"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99141" x2="99169" y1="-2303" y2="-2321"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99110" x2="99029" y1="-2269" y2="-2269"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99199" x2="99121" y1="-2269" y2="-2269"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99111" x2="99111" y1="-2257" y2="-2280"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99120" x2="99120" y1="-2249" y2="-2287"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99113" x2="99113" y1="-2742" y2="-2665"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99113" x2="99113" y1="-2547" y2="-2396"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97854" x2="97872" y1="-2212" y2="-2201"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97872" x2="97872" y1="-2223" y2="-2201"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97854" x2="97872" y1="-2212" y2="-2223"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97933" x2="97519" y1="-2212" y2="-2212"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98022" x2="98022" y1="-2273" y2="-2144"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98022" x2="97977" y1="-2212" y2="-2212"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98200" x2="98155" y1="-2273" y2="-2273"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98111" x2="98023" y1="-2273" y2="-2273"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98199" x2="98154" y1="-2144" y2="-2144"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98110" x2="98022" y1="-2144" y2="-2144"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="0" id="BS-CX_PDS.CX_PDS_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="0" fill="none" points="98815,-3231 99198,-3231 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28786" ObjectName="BS-CX_PDS.CX_PDS_9IM"/>
    <cge:TPSR_Ref TObjectID="28786"/></metadata>
   <polyline fill="none" opacity="0" points="98815,-3231 99198,-3231 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="0" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 97208.500000 -3479.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="0" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-188939" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99807.000000 -2860.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188939" ObjectName="CX_PDS:CX_PDS_GG_Ua1_43"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="0" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-188940" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99807.000000 -2843.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188940" ObjectName="CX_PDS:CX_PDS_GG_Ub1_44"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="0" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-188941" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99807.000000 -2827.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188941" ObjectName="CX_PDS:CX_PDS_GG_Uc1_45"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="0" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-188945" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99807.000000 -2811.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188945" ObjectName="CX_PDS:CX_PDS_GG_U01_49"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="0" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-188942" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99807.000000 -2796.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188942" ObjectName="CX_PDS:CX_PDS_GG_Uab1_46"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="0" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-188947" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98296.000000 -2874.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188947" ObjectName="CX_PDS:CX_PDS_GG_Ub2_51"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="0" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-188948" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98296.000000 -2858.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188948" ObjectName="CX_PDS:CX_PDS_GG_Uc2_52"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="0" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-188952" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98296.000000 -2842.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188952" ObjectName="CX_PDS:CX_PDS_GG_U02_56"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="0" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-188949" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98296.000000 -2827.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188949" ObjectName="CX_PDS:CX_PDS_GG_Uab2_53"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="0" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-188946" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98296.000000 -2891.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188946" ObjectName="CX_PDS:CX_PDS_GG_Ua2_50"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="0" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="230" x="97196" y="-3538"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="230" x="97196" y="-3538"/></g>
   <g DF8003:Layer="0" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="97137" y="-3555"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="97137" y="-3555"/></g>
  </g><g id="Circle_Layer">
   <ellipse DF8003:Layer="0" cx="98729" cy="-3378" fill="none" rx="20.5" ry="21" stroke="rgb(0,255,0)" stroke-width="0.398149"/>
   <circle DF8003:Layer="0" cx="98695" cy="-3377" fill="none" r="21.5" stroke="rgb(0,255,0)" stroke-width="0.398149"/>
   <ellipse DF8003:Layer="0" cx="99286" cy="-3373" fill="none" rx="20.5" ry="21" stroke="rgb(0,255,0)" stroke-width="0.398149"/>
   <circle DF8003:Layer="0" cx="99320" cy="-3373" fill="none" r="22" stroke="rgb(0,255,0)" stroke-width="0.398149"/>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="0" id="g_2620660">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99065.000000 -3091.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_34b94c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98886.000000 -2504.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_26c4c00">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99109.000000 -2504.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="230" x="97196" y="-3538"/></g>
   <g href="cx_配调_配网接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="97137" y="-3555"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98988.000000 -2733.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98814.000000 -3244.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98814.000000 -3074.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99179.000000 -3244.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99179.000000 -3073.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-188905" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98762.000000 -3330.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188905" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28791"/>
     <cge:Term_Ref ObjectID="41014"/>
    <cge:TPSR_Ref TObjectID="28791"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188906" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98762.000000 -3330.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188906" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28791"/>
     <cge:Term_Ref ObjectID="41014"/>
    <cge:TPSR_Ref TObjectID="28791"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188902" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98762.000000 -3330.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188902" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28791"/>
     <cge:Term_Ref ObjectID="41014"/>
    <cge:TPSR_Ref TObjectID="28791"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-188907" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98762.000000 -3330.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188907" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28791"/>
     <cge:Term_Ref ObjectID="41014"/>
    <cge:TPSR_Ref TObjectID="28791"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-188899" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99273.000000 -3317.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188899" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28787"/>
     <cge:Term_Ref ObjectID="41006"/>
    <cge:TPSR_Ref TObjectID="28787"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188900" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99273.000000 -3317.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188900" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28787"/>
     <cge:Term_Ref ObjectID="41006"/>
    <cge:TPSR_Ref TObjectID="28787"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99273.000000 -3317.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28787"/>
     <cge:Term_Ref ObjectID="41006"/>
    <cge:TPSR_Ref TObjectID="28787"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-188901" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99273.000000 -3317.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188901" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28787"/>
     <cge:Term_Ref ObjectID="41006"/>
    <cge:TPSR_Ref TObjectID="28787"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-188917" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98773.000000 -3151.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188917" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28789"/>
     <cge:Term_Ref ObjectID="41010"/>
    <cge:TPSR_Ref TObjectID="28789"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188918" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98773.000000 -3151.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188918" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28789"/>
     <cge:Term_Ref ObjectID="41010"/>
    <cge:TPSR_Ref TObjectID="28789"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188914" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98773.000000 -3151.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188914" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28789"/>
     <cge:Term_Ref ObjectID="41010"/>
    <cge:TPSR_Ref TObjectID="28789"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-188919" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98773.000000 -3151.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188919" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28789"/>
     <cge:Term_Ref ObjectID="41010"/>
    <cge:TPSR_Ref TObjectID="28789"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-188911" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99282.000000 -3149.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188911" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28788"/>
     <cge:Term_Ref ObjectID="41008"/>
    <cge:TPSR_Ref TObjectID="28788"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188912" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99282.000000 -3149.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188912" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28788"/>
     <cge:Term_Ref ObjectID="41008"/>
    <cge:TPSR_Ref TObjectID="28788"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99282.000000 -3149.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28788"/>
     <cge:Term_Ref ObjectID="41008"/>
    <cge:TPSR_Ref TObjectID="28788"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-188913" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99282.000000 -3149.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188913" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28788"/>
     <cge:Term_Ref ObjectID="41008"/>
    <cge:TPSR_Ref TObjectID="28788"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-188932" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99031.000000 -3341.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28786"/>
     <cge:Term_Ref ObjectID="41005"/>
    <cge:TPSR_Ref TObjectID="28786"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-188933" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99031.000000 -3341.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188933" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28786"/>
     <cge:Term_Ref ObjectID="41005"/>
    <cge:TPSR_Ref TObjectID="28786"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-188934" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99031.000000 -3341.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188934" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28786"/>
     <cge:Term_Ref ObjectID="41005"/>
    <cge:TPSR_Ref TObjectID="28786"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-188938" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99031.000000 -3341.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188938" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28786"/>
     <cge:Term_Ref ObjectID="41005"/>
    <cge:TPSR_Ref TObjectID="28786"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-188935" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99031.000000 -3341.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188935" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28786"/>
     <cge:Term_Ref ObjectID="41005"/>
    <cge:TPSR_Ref TObjectID="28786"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-188929" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98933.000000 -2919.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188929" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28793"/>
     <cge:Term_Ref ObjectID="41018"/>
    <cge:TPSR_Ref TObjectID="28793"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188930" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98933.000000 -2919.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188930" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28793"/>
     <cge:Term_Ref ObjectID="41018"/>
    <cge:TPSR_Ref TObjectID="28793"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188926" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98933.000000 -2919.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188926" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28793"/>
     <cge:Term_Ref ObjectID="41018"/>
    <cge:TPSR_Ref TObjectID="28793"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-188927" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98933.000000 -2919.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188927" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28793"/>
     <cge:Term_Ref ObjectID="41018"/>
    <cge:TPSR_Ref TObjectID="28793"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-188928" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98933.000000 -2919.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188928" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28793"/>
     <cge:Term_Ref ObjectID="41018"/>
    <cge:TPSR_Ref TObjectID="28793"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-188923" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99281.000000 -2917.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188923" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28792"/>
     <cge:Term_Ref ObjectID="41016"/>
    <cge:TPSR_Ref TObjectID="28792"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188924" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99281.000000 -2917.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188924" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28792"/>
     <cge:Term_Ref ObjectID="41016"/>
    <cge:TPSR_Ref TObjectID="28792"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188920" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99281.000000 -2917.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188920" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28792"/>
     <cge:Term_Ref ObjectID="41016"/>
    <cge:TPSR_Ref TObjectID="28792"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-188921" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99281.000000 -2917.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188921" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28792"/>
     <cge:Term_Ref ObjectID="41016"/>
    <cge:TPSR_Ref TObjectID="28792"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-188922" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99281.000000 -2917.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188922" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28792"/>
     <cge:Term_Ref ObjectID="41016"/>
    <cge:TPSR_Ref TObjectID="28792"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer10="本期工程:0.000000 0.000000" layer11="4:0.000000 0.000000" layer12="GDXT:0.000000 0.000000" layer13="Defpoints:0.000000 0.000000" layer14="主干线:0.000000 0.000000" layer15="设备（实线）:0.000000 0.000000" layer16="标注线层:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layer4="0:0.000000 0.000000" layer5="$AUDIT-BAD-LAYER:0.000000 0.000000" layer6="图层2:0.000000 0.000000" layer7="SX:0.000000 0.000000" layer8="CSX:0.000000 0.000000" layer9="文字:0.000000 0.000000" layerN="17" moveAndZoomFlag="1"/>
</svg>