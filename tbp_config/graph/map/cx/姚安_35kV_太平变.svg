<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-216" aopId="4063750" id="thSvg" product="E8000V2" version="1.0" viewBox="-198 -1064 2070 1127">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape28">
    <polyline arcFlag="1" points="19,105 17,105 15,104 14,104 12,103 11,102 9,101 8,99 7,97 7,96 6,94 6,92 6,90 7,88 7,87 8,85 9,84 11,82 12,81 14,80 15,80 17,79 19,79 21,79 23,80 24,80 26,81 27,82 29,84 30,85 31,87 31,88 32,90 32,92 " stroke-width="0.0972"/>
    <polyline arcFlag="1" points="36,30 37,30 38,30 38,30 39,31 39,31 40,31 40,32 41,32 41,33 41,34 41,34 42,35 42,36 42,36 41,37 41,38 41,38 41,39 40,39 40,40 39,40 39,40 38,41 38,41 37,41 36,41 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="11" x2="26" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="10" x2="26" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="19" x2="19" y1="19" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="35" x2="35" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="2" x2="2" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="19" x2="36" y1="60" y2="60"/>
    <rect height="23" stroke-width="0.369608" width="12" x="13" y="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="19" x2="36" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="2" x2="35" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="19" x2="19" y1="92" y2="26"/>
    <polyline arcFlag="1" points="36,41 37,41 38,41 38,42 39,42 39,42 40,43 40,43 41,44 41,44 41,45 41,45 42,46 42,47 42,47 41,48 41,49 41,49 41,50 40,50 40,51 39,51 39,52 38,52 38,52 37,52 36,52 " stroke-width="1"/>
    <polyline arcFlag="1" points="36,19 37,19 38,19 38,19 39,19 39,20 40,20 40,21 41,21 41,22 41,22 41,23 42,24 42,24 42,25 41,26 41,26 41,27 41,27 40,28 40,28 39,29 39,29 38,29 38,30 37,30 36,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="60" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="19" x2="19" y1="105" y2="116"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="32" x2="19" y1="92" y2="92"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape6_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="38" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="38" y2="13"/>
   </symbol>
   <symbol id="switch2:shape6_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
   </symbol>
   <symbol id="switch2:shape6-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="13" y2="14"/>
   </symbol>
   <symbol id="switch2:shape6-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="11" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape25_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
   </symbol>
   <symbol id="switch2:shape25_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape55_0">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,44 6,44 6,73 " stroke-width="1"/>
    <circle cx="31" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="56" y2="98"/>
    <polyline DF8003:Layer="PUBLIC" points="31,87 25,74 37,74 31,87 31,86 31,87 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="79" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="31" y1="49" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="44" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="44" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="44" y2="39"/>
   </symbol>
   <symbol id="transformer2:shape55_1">
    <circle cx="31" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="20" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="20" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="20" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape42_0">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="73" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="79" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="81" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape42_1">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="49" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="55" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="57" y2="55"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1.1087"/>
    <polyline points="58,100 64,100 " stroke-width="1.1087"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1.1087"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape33">
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="67" y2="23"/>
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="6" y2="6"/>
   </symbol>
   <symbol id="voltageTransformer:shape104">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="41" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="36" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="38" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="37" y1="43" y2="43"/>
    <ellipse cx="8" cy="12" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="8" cy="24" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="4" y1="10" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="10" y2="14"/>
    <ellipse cx="19" cy="12" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <ellipse cx="19" cy="24" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="44" y1="19" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="28" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="39" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="12" y2="18"/>
    <rect height="13" stroke-width="1" width="5" x="37" y="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape74">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="32" y2="32"/>
    <circle cx="59" cy="64" fillStyle="0" r="25" stroke-width="0.520408"/>
    <circle cx="51" cy="32" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="58" x2="58" y1="59" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="66" x2="58" y1="75" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="58" x2="50" y1="68" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="54" x2="54" y1="18" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="62" x2="54" y1="34" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="54" x2="46" y1="27" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="28" x2="28" y1="65" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="12" x2="28" y1="48" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="12" x2="28" y1="55" y2="65"/>
    <circle cx="25" cy="58" fillStyle="0" r="25" stroke-width="0.520408"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_391fd30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_39207a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3934f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3935ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3936900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3937220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_39379a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3938130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3938740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3938dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3938dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_393a370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_393a370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_393b150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_393cd40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_393d950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_393e300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_393ec10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3940400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3940c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_39412f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3941d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3942ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3943870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3944360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3949620" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_394a2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_39460b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3948180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3947630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_39562e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_394c160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1137" width="2080" x="-203" y="-1069"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1070" x2="1070" y1="-531" y2="-537"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1058" x2="1070" y1="-531" y2="-531"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1066" x2="1064" y1="-540" y2="-540"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1066" x2="1076" y1="-540" y2="-549"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1072" x2="1068" y1="-562" y2="-562"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1071" x2="1069" y1="-564" y2="-564"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1073" x2="1067" y1="-560" y2="-560"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1070" x2="1070" y1="-560" y2="-550"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-145630">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 696.000000 -640.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25561" ObjectName="SW-YA_TP.YA_TP_301BK"/>
     <cge:Meas_Ref ObjectId="145630"/>
    <cge:TPSR_Ref TObjectID="25561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145703">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 696.000000 -462.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25563" ObjectName="SW-YA_TP.YA_TP_001BK"/>
     <cge:Meas_Ref ObjectId="145703"/>
    <cge:TPSR_Ref TObjectID="25563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145562">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1565.000000 -843.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25551" ObjectName="SW-YA_TP.YA_TP_312BK"/>
     <cge:Meas_Ref ObjectId="145562"/>
    <cge:TPSR_Ref TObjectID="25551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145747">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1390.000000 -640.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25565" ObjectName="SW-YA_TP.YA_TP_302BK"/>
     <cge:Meas_Ref ObjectId="145747"/>
    <cge:TPSR_Ref TObjectID="25565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145821">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1390.000000 -462.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25568" ObjectName="SW-YA_TP.YA_TP_002BK"/>
     <cge:Meas_Ref ObjectId="145821"/>
    <cge:TPSR_Ref TObjectID="25568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145519">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 514.000000 -851.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25546" ObjectName="SW-YA_TP.YA_TP_311BK"/>
     <cge:Meas_Ref ObjectId="145519"/>
    <cge:TPSR_Ref TObjectID="25546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145867">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 592.457143 -234.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25571" ObjectName="SW-YA_TP.YA_TP_052BK"/>
     <cge:Meas_Ref ObjectId="145867"/>
    <cge:TPSR_Ref TObjectID="25571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 787.514286 -230.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 973.971429 -233.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145913">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1154.028571 -235.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25574" ObjectName="SW-YA_TP.YA_TP_055BK"/>
     <cge:Meas_Ref ObjectId="145913"/>
    <cge:TPSR_Ref TObjectID="25574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1347.685714 -235.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145959">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1527.742857 -235.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25577" ObjectName="SW-YA_TP.YA_TP_057BK"/>
     <cge:Meas_Ref ObjectId="145959"/>
    <cge:TPSR_Ref TObjectID="25577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-146005">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 406.000000 -234.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25580" ObjectName="SW-YA_TP.YA_TP_051BK"/>
     <cge:Meas_Ref ObjectId="146005"/>
    <cge:TPSR_Ref TObjectID="25580"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3c59f70">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 409.000000 -989.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_48642e0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1719.000000 -973.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_406f780">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 904.000000 -983.000000)" xlink:href="#voltageTransformer:shape104"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40738e0">
    <use class="BV-10KV" transform="matrix(0.341176 -0.000000 0.000000 -0.355556 1032.000000 -520.000000)" xlink:href="#voltageTransformer:shape74"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YA" endPointId="0" endStationName="YA_TP" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_taiping" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="523,-1031 523,-999 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37767" ObjectName="AC-35kV.LN_taiping"/>
    <cge:TPSR_Ref TObjectID="37767_SS-216"/></metadata>
   <polyline fill="none" opacity="0" points="523,-1031 523,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YA_TP" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_huangxitaiTP" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1574,-994 1574,-1026 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38084" ObjectName="AC-35kV.LN_huangxitaiTP"/>
    <cge:TPSR_Ref TObjectID="38084_SS-216"/></metadata>
   <polyline fill="none" opacity="0" points="1574,-994 1574,-1026 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YA_TP.052Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 596.457143 -28.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34275" ObjectName="EC-YA_TP.052Ld"/>
    <cge:TPSR_Ref TObjectID="34275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 791.514286 -24.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 977.971429 -27.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_TP.055Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1158.028571 -29.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34276" ObjectName="EC-YA_TP.055Ld"/>
    <cge:TPSR_Ref TObjectID="34276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1351.685714 -29.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_TP.057Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.742857 -29.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34277" ObjectName="EC-YA_TP.057Ld"/>
    <cge:TPSR_Ref TObjectID="34277"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_36e0d00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 565.000000 -973.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d79c70">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1259.000000 -861.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c4d410">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1508.000000 -990.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ec9f90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 638.457143 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d9a500">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 833.514286 -25.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e7c7a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1019.971429 -28.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4309b60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1200.028571 -30.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_43113d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1393.685714 -30.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ecdba0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1573.742857 -30.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_430e9e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 351.800000 -102.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4071c60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 945.000000 -923.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4078870">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1091.000000 -445.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -69.000000 -994.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217888" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -41.000000 -815.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217888" ObjectName="YA_TP:YA_TP_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-219741" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -41.000000 -772.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219741" ObjectName="YA_TP:YA_TP_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217888" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -894.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217888" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217888" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -41.000000 -855.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217888" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-145404" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 640.000000 -891.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145404" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25546"/>
     <cge:Term_Ref ObjectID="35993"/>
    <cge:TPSR_Ref TObjectID="25546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-145405" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 640.000000 -891.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145405" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25546"/>
     <cge:Term_Ref ObjectID="35993"/>
    <cge:TPSR_Ref TObjectID="25546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-145401" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 640.000000 -891.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145401" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25546"/>
     <cge:Term_Ref ObjectID="35993"/>
    <cge:TPSR_Ref TObjectID="25546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-145410" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1735.000000 -892.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145410" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25551"/>
     <cge:Term_Ref ObjectID="36003"/>
    <cge:TPSR_Ref TObjectID="25551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-145411" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1735.000000 -892.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145411" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25551"/>
     <cge:Term_Ref ObjectID="36003"/>
    <cge:TPSR_Ref TObjectID="25551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-145407" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1735.000000 -892.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145407" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25551"/>
     <cge:Term_Ref ObjectID="36003"/>
    <cge:TPSR_Ref TObjectID="25551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-145478" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 406.000000 18.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145478" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25580"/>
     <cge:Term_Ref ObjectID="36061"/>
    <cge:TPSR_Ref TObjectID="25580"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-145479" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 406.000000 18.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145479" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25580"/>
     <cge:Term_Ref ObjectID="36061"/>
    <cge:TPSR_Ref TObjectID="25580"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-145475" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 406.000000 18.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145475" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25580"/>
     <cge:Term_Ref ObjectID="36061"/>
    <cge:TPSR_Ref TObjectID="25580"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-145460" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 594.000000 17.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145460" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25571"/>
     <cge:Term_Ref ObjectID="36043"/>
    <cge:TPSR_Ref TObjectID="25571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-145461" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 594.000000 17.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145461" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25571"/>
     <cge:Term_Ref ObjectID="36043"/>
    <cge:TPSR_Ref TObjectID="25571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-145457" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 594.000000 17.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145457" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25571"/>
     <cge:Term_Ref ObjectID="36043"/>
    <cge:TPSR_Ref TObjectID="25571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-145466" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1155.000000 16.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145466" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25574"/>
     <cge:Term_Ref ObjectID="36049"/>
    <cge:TPSR_Ref TObjectID="25574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-145467" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1155.000000 16.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145467" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25574"/>
     <cge:Term_Ref ObjectID="36049"/>
    <cge:TPSR_Ref TObjectID="25574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-145463" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1155.000000 16.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145463" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25574"/>
     <cge:Term_Ref ObjectID="36049"/>
    <cge:TPSR_Ref TObjectID="25574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-145472" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1529.000000 16.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145472" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25577"/>
     <cge:Term_Ref ObjectID="36055"/>
    <cge:TPSR_Ref TObjectID="25577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-145473" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1529.000000 16.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145473" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25577"/>
     <cge:Term_Ref ObjectID="36055"/>
    <cge:TPSR_Ref TObjectID="25577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-145469" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1529.000000 16.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145469" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25577"/>
     <cge:Term_Ref ObjectID="36055"/>
    <cge:TPSR_Ref TObjectID="25577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-145416" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 650.000000 -682.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145416" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25561"/>
     <cge:Term_Ref ObjectID="36023"/>
    <cge:TPSR_Ref TObjectID="25561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-145417" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 650.000000 -682.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145417" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25561"/>
     <cge:Term_Ref ObjectID="36023"/>
    <cge:TPSR_Ref TObjectID="25561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-145413" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 650.000000 -682.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145413" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25561"/>
     <cge:Term_Ref ObjectID="36023"/>
    <cge:TPSR_Ref TObjectID="25561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-145422" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 -678.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145422" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25565"/>
     <cge:Term_Ref ObjectID="36031"/>
    <cge:TPSR_Ref TObjectID="25565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-145423" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 -678.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145423" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25565"/>
     <cge:Term_Ref ObjectID="36031"/>
    <cge:TPSR_Ref TObjectID="25565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-145419" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 -678.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145419" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25565"/>
     <cge:Term_Ref ObjectID="36031"/>
    <cge:TPSR_Ref TObjectID="25565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-145428" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 657.000000 -505.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145428" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25563"/>
     <cge:Term_Ref ObjectID="36027"/>
    <cge:TPSR_Ref TObjectID="25563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-145429" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 657.000000 -505.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145429" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25563"/>
     <cge:Term_Ref ObjectID="36027"/>
    <cge:TPSR_Ref TObjectID="25563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-145425" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 657.000000 -505.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145425" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25563"/>
     <cge:Term_Ref ObjectID="36027"/>
    <cge:TPSR_Ref TObjectID="25563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-145436" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1349.000000 -504.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145436" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25568"/>
     <cge:Term_Ref ObjectID="36037"/>
    <cge:TPSR_Ref TObjectID="25568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-145437" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1349.000000 -504.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145437" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25568"/>
     <cge:Term_Ref ObjectID="36037"/>
    <cge:TPSR_Ref TObjectID="25568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-145433" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1349.000000 -504.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145433" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25568"/>
     <cge:Term_Ref ObjectID="36037"/>
    <cge:TPSR_Ref TObjectID="25568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-145440" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1535.000000 -582.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145440" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25586"/>
     <cge:Term_Ref ObjectID="36075"/>
    <cge:TPSR_Ref TObjectID="25586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-145439" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1535.000000 -582.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145439" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25586"/>
     <cge:Term_Ref ObjectID="36075"/>
    <cge:TPSR_Ref TObjectID="25586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-145441" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 375.000000 -766.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145441" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25544"/>
     <cge:Term_Ref ObjectID="35991"/>
    <cge:TPSR_Ref TObjectID="25544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-145442" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 375.000000 -766.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145442" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25544"/>
     <cge:Term_Ref ObjectID="35991"/>
    <cge:TPSR_Ref TObjectID="25544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-145443" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 375.000000 -766.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145443" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25544"/>
     <cge:Term_Ref ObjectID="35991"/>
    <cge:TPSR_Ref TObjectID="25544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-145444" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 375.000000 -766.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145444" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25544"/>
     <cge:Term_Ref ObjectID="35991"/>
    <cge:TPSR_Ref TObjectID="25544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-145449" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 378.000000 -437.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145449" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25545"/>
     <cge:Term_Ref ObjectID="35992"/>
    <cge:TPSR_Ref TObjectID="25545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-145450" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 378.000000 -437.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145450" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25545"/>
     <cge:Term_Ref ObjectID="35992"/>
    <cge:TPSR_Ref TObjectID="25545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-145451" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 378.000000 -437.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145451" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25545"/>
     <cge:Term_Ref ObjectID="35992"/>
    <cge:TPSR_Ref TObjectID="25545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-145452" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 378.000000 -437.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145452" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25545"/>
     <cge:Term_Ref ObjectID="35992"/>
    <cge:TPSR_Ref TObjectID="25545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-145432" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 836.000000 -587.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145432" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25585"/>
     <cge:Term_Ref ObjectID="36074"/>
    <cge:TPSR_Ref TObjectID="25585"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-145431" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 836.000000 -587.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145431" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25585"/>
     <cge:Term_Ref ObjectID="36074"/>
    <cge:TPSR_Ref TObjectID="25585"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="167" y="-1019"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="167" y="-1019"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-54" y="-1044"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-54" y="-1044"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-102" y="-1064"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-102" y="-1064"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="167" y="-1058"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="167" y="-1058"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="532" y="-880"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="532" y="-880"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="1583" y="-872"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="1583" y="-872"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="424" y="-263"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="424" y="-263"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="611" y="-263"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="611" y="-263"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1172" y="-264"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1172" y="-264"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1546" y="-264"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1546" y="-264"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="60" x="-148" y="-697"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="60" x="-148" y="-697"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="539" y="-614"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="539" y="-614"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1226" y="-616"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1226" y="-616"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="292,-1045 289,-1048 289,-991 292,-994 292,-1045" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="292,-1045 289,-1048 352,-1048 349,-1045 292,-1045" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="292,-994 289,-991 352,-991 349,-994 292,-994" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="349,-1045 352,-1048 352,-991 349,-994 349,-1045" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="51" stroke="rgb(255,255,255)" width="57" x="292" y="-1045"/>
     <rect fill="none" height="51" qtmmishow="hidden" stroke="rgb(0,0,0)" width="57" x="292" y="-1045"/>
    </a>
   <metadata/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图35_姚安.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="167" y="-1019"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-54" y="-1044"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-102" y="-1064"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="167" y="-1058"/></g>
   <g href="35kV太平变35kV太平线311间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="532" y="-880"/></g>
   <g href="35kV太平变35kV黄西太线312间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="1583" y="-872"/></g>
   <g href="35kV太平变10kV1号电容器051间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="424" y="-263"/></g>
   <g href="35kV太平变10kV陈家线052间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="611" y="-263"/></g>
   <g href="35kV太平变10kV各苴线055间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1172" y="-264"/></g>
   <g href="35kV太平变10kV者乐线057间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1546" y="-264"/></g>
   <g href="35kV太平变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="60" x="-148" y="-697"/></g>
   <g href="35kV太平变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="539" y="-614"/></g>
   <g href="35kV太平变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1226" y="-616"/></g>
   <g href="AVC太平站.svg" style="fill-opacity:0"><rect height="51" qtmmishow="hidden" stroke="rgb(0,0,0)" width="57" x="292" y="-1045"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(255,255,0)" stroke-width="0.416609" width="14" x="916" y="-972"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(0,255,0)" stroke-width="1" width="5" x="1068" y="-550"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(0,255,0)" stroke-width="0.416609" width="13" x="1726" y="-247"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YA_TP.YA_TP_3M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="352,-779 1792,-779 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25544" ObjectName="BS-YA_TP.YA_TP_3M"/>
    <cge:TPSR_Ref TObjectID="25544"/></metadata>
   <polyline fill="none" opacity="0" points="352,-779 1792,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YA_TP.YA_TP_9M">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="276,-363 1872,-363 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25545" ObjectName="BS-YA_TP.YA_TP_9M"/>
    <cge:TPSR_Ref TObjectID="25545"/></metadata>
   <polyline fill="none" opacity="0" points="276,-363 1872,-363 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YA_TP.YA_TP_Zyb1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="36081"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1222.000000 -874.000000)" xlink:href="#transformer2:shape55_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1222.000000 -874.000000)" xlink:href="#transformer2:shape55_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25587" ObjectName="TF-YA_TP.YA_TP_Zyb1"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YA_TP.YA_TP_Zyb2">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="36085"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1702.000000 -31.000000)" xlink:href="#transformer2:shape42_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1702.000000 -31.000000)" xlink:href="#transformer2:shape42_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25588" ObjectName="TF-YA_TP.YA_TP_Zyb2"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YA_TP.YA_TP_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="36077"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 1364.000000 -528.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 1364.000000 -528.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25586" ObjectName="TF-YA_TP.YA_TP_2T"/>
    <cge:TPSR_Ref TObjectID="25586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YA_TP.YA_TP_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="36073"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 670.000000 -523.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 670.000000 -523.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25585" ObjectName="TF-YA_TP.YA_TP_1T"/>
    <cge:TPSR_Ref TObjectID="25585"/></metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="25544" cx="705" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25545" cx="705" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25544" cx="1399" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25545" cx="1399" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25544" cx="523" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25544" cx="923" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25544" cx="1254" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25544" cx="1574" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25545" cx="601" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25545" cx="797" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25545" cx="983" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25545" cx="1163" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25545" cx="1357" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25545" cx="1537" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25545" cx="1733" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25545" cx="415" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25545" cx="1050" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e52ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 412.000000 -928.000000) translate(0,12)">31167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e04f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 530.000000 -926.000000) translate(0,12)">3116</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e20320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 532.000000 -880.000000) translate(0,12)">311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fe4050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 412.000000 -831.000000) translate(0,12)">31117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42ca970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 535.000000 -821.000000) translate(0,12)">3111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d4da20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 278.000000 -787.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d9b080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 812.000000 -862.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d9aef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 930.000000 -844.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d41840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1011.000000 -835.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e46cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1197.000000 -1048.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38f97e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1512.000000 -929.000000) translate(0,12)">31267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d8c820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1581.000000 -923.000000) translate(0,12)">3126</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e16420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1658.000000 -919.000000) translate(0,12)">31260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d99850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1583.000000 -872.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e4260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1581.000000 -817.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e16cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1659.000000 -823.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b591f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 712.000000 -738.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40c2a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 714.000000 -669.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d55680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 714.000000 -491.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f14a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 712.000000 -425.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dff2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1057.000000 -415.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40c0410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1406.000000 -738.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2da3900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -669.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d1caa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -491.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40becd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1406.000000 -425.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dedbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1740.000000 -321.000000) translate(0,12)">0581</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40bfe40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1751.000000 -87.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cdacc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1544.000000 -322.000000) translate(0,12)">0571</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e46990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1546.000000 -264.000000) translate(0,12)">057</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d4e8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1544.000000 -122.000000) translate(0,12)">0576</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e32ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1170.000000 -322.000000) translate(0,12)">0551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_447c610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1172.000000 -264.000000) translate(0,12)">055</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d98740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1170.000000 -122.000000) translate(0,12)">0556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e32a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 611.000000 -263.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e20100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 608.000000 -321.000000) translate(0,12)">0521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e20760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 608.000000 -121.000000) translate(0,12)">0526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e65740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 422.000000 -210.000000) translate(0,12)">0516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0b3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 424.000000 -263.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1c8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 422.000000 -321.000000) translate(0,12)">0511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d99660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 275.000000 -353.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2da1740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -16.000000 -1033.500000) translate(0,16)">太平变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e087b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 886.000000 -1049.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40cd840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1528.000000 -1048.000000) translate(0,12)">35kV黄西太线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e7370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 485.000000 -1048.000000) translate(0,12)">35kV太平线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d4cf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 528.000000 -595.000000) translate(0,12)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d4cf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 528.000000 -595.000000) translate(0,27)">35±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d4cf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 528.000000 -595.000000) translate(0,42)">5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d4cf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 528.000000 -595.000000) translate(0,57)">yNd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d4cf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 528.000000 -595.000000) translate(0,72)">Ud=7.29%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1f410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1f410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1f410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1f410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1f410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1f410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1f410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1f410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1f410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1f410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1f410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1f410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1f410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1f410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1f410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1f410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1f410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e1f410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d9aba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d9aba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d9aba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d9aba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d9aba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d9aba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d9aba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d9aba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d9aba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42d7ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1264.666667 -725.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42d7d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1219.000000 -595.000000) translate(0,12)">SZ-2500/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42d7d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1219.000000 -595.000000) translate(0,27)">35±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42d7d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1219.000000 -595.000000) translate(0,42)">2500kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42d7d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1219.000000 -595.000000) translate(0,57)">y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42d7d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1219.000000 -595.000000) translate(0,72)">Ud=6.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4311bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1012.000000 -580.000000) translate(0,12)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0c3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 396.000000 -971.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d1ba40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1666.000000 -1010.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4305de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 578.457143 -12.400000) translate(0,12)">陈家线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4312750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 766.514286 -12.400000) translate(0,12)">预留间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42c85a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 952.971429 -12.400000) translate(0,12)">预留间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c967f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1141.028571 -12.400000) translate(0,12)">各苴线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4874a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1327.685714 -12.400000) translate(0,12)">预留间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42e6220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1514.742857 -12.400000) translate(0,12)">者乐线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ebdea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 381.000000 -12.400000) translate(0,12)">1号电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3f1d890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 178.000000 -1012.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3f1dc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 178.000000 -1049.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f1e980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -148.000000 -697.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_407e120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -198.000000 -56.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_407e5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -44.000000 -66.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_407e5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -44.000000 -66.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_407e890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -44.000000 -96.000000) translate(0,17)">5832115</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40801e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 539.000000 -614.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4080b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1226.000000 -616.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_40825f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 298.000000 -1028.000000) translate(0,16)">AVC</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42d5280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 287.000000 750.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42d5770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 287.000000 765.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42d5b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.000000 722.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42d5de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 287.000000 736.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42d6200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 579.000000 893.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42d64e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 568.000000 878.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42d6710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 593.000000 863.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42d6b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1676.000000 893.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42d6e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1665.000000 878.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42d7070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1690.000000 863.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f17610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1440.000000 568.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49056a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1440.000000 583.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4905990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 287.000000 421.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4905c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 287.000000 436.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4905e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.000000 393.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49060a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 287.000000 407.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f17880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 347.000000 -15.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f17b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 336.000000 -30.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f17d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 361.000000 -45.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f1ab50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 598.000000 507.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f1ad80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 587.000000 492.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f1af90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 612.000000 477.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f1b380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 592.000000 683.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f1b610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 581.000000 668.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f1b820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 606.000000 653.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f1bc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1290.000000 679.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f1bf00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1279.000000 664.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f1c140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1304.000000 649.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f1c560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1289.000000 504.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f1c820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1278.000000 489.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f1ca60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1303.000000 474.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_407c160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 743.000000 572.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_407c380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 743.000000 587.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_407c7a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 536.000000 -14.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_407ca60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 525.000000 -29.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_407cca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 550.000000 -44.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_407d0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1099.000000 -13.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_407d380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1088.000000 -28.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_407d5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1113.000000 -43.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_407d9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1469.000000 -14.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_407dca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1458.000000 -29.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_407dee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1483.000000 -44.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-YA_TP.YA_TP_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 396.000000 -22.000000)" xlink:href="#capacitor:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41826" ObjectName="CB-YA_TP.YA_TP_Cb1"/>
    <cge:TPSR_Ref TObjectID="41826"/></metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-145384" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98.500000 -959.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25528" ObjectName="DYN-YA_TP"/>
     <cge:Meas_Ref ObjectId="145384"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-145521">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 514.000000 -791.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25547" ObjectName="SW-YA_TP.YA_TP_3111SW"/>
     <cge:Meas_Ref ObjectId="145521"/>
    <cge:TPSR_Ref TObjectID="25547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145632">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 696.000000 -708.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25562" ObjectName="SW-YA_TP.YA_TP_3011SW"/>
     <cge:Meas_Ref ObjectId="145632"/>
    <cge:TPSR_Ref TObjectID="25562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145705">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 696.000000 -395.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25564" ObjectName="SW-YA_TP.YA_TP_0011SW"/>
     <cge:Meas_Ref ObjectId="145705"/>
    <cge:TPSR_Ref TObjectID="25564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145627">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 914.000000 -814.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25558" ObjectName="SW-YA_TP.YA_TP_3901SW"/>
     <cge:Meas_Ref ObjectId="145627"/>
    <cge:TPSR_Ref TObjectID="25558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145628">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 993.000000 -800.000000)" xlink:href="#switch2:shape6_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25559" ObjectName="SW-YA_TP.YA_TP_39010SW"/>
     <cge:Meas_Ref ObjectId="145628"/>
    <cge:TPSR_Ref TObjectID="25559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145629">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 859.000000 -830.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25560" ObjectName="SW-YA_TP.YA_TP_39017SW"/>
     <cge:Meas_Ref ObjectId="145629"/>
    <cge:TPSR_Ref TObjectID="25560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145564">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1565.000000 -787.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25552" ObjectName="SW-YA_TP.YA_TP_3121SW"/>
     <cge:Meas_Ref ObjectId="145564"/>
    <cge:TPSR_Ref TObjectID="25552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145565">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1565.000000 -893.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25553" ObjectName="SW-YA_TP.YA_TP_3126SW"/>
     <cge:Meas_Ref ObjectId="145565"/>
    <cge:TPSR_Ref TObjectID="25553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145568">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1641.000000 -788.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25556" ObjectName="SW-YA_TP.YA_TP_31217SW"/>
     <cge:Meas_Ref ObjectId="145568"/>
    <cge:TPSR_Ref TObjectID="25556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145567">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1494.000000 -894.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25555" ObjectName="SW-YA_TP.YA_TP_31267SW"/>
     <cge:Meas_Ref ObjectId="145567"/>
    <cge:TPSR_Ref TObjectID="25555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145566">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1641.000000 -884.000000)" xlink:href="#switch2:shape6_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25554" ObjectName="SW-YA_TP.YA_TP_31260SW"/>
     <cge:Meas_Ref ObjectId="145566"/>
    <cge:TPSR_Ref TObjectID="25554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145749">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1390.000000 -708.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25566" ObjectName="SW-YA_TP.YA_TP_3021SW"/>
     <cge:Meas_Ref ObjectId="145749"/>
    <cge:TPSR_Ref TObjectID="25566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145750">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1390.000000 -395.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25567" ObjectName="SW-YA_TP.YA_TP_0021SW"/>
     <cge:Meas_Ref ObjectId="145750"/>
    <cge:TPSR_Ref TObjectID="25567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1320.000000 -692.000000)" xlink:href="#switch2:shape6_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145523">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 459.000000 -795.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25549" ObjectName="SW-YA_TP.YA_TP_31117SW"/>
     <cge:Meas_Ref ObjectId="145523"/>
    <cge:TPSR_Ref TObjectID="25549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145522">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 514.000000 -896.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25548" ObjectName="SW-YA_TP.YA_TP_3116SW"/>
     <cge:Meas_Ref ObjectId="145522"/>
    <cge:TPSR_Ref TObjectID="25548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145524">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 459.000000 -895.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25550" ObjectName="SW-YA_TP.YA_TP_31167SW"/>
     <cge:Meas_Ref ObjectId="145524"/>
    <cge:TPSR_Ref TObjectID="25550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145869">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 592.457143 -291.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25572" ObjectName="SW-YA_TP.YA_TP_0521SW"/>
     <cge:Meas_Ref ObjectId="145869"/>
    <cge:TPSR_Ref TObjectID="25572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145870">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 592.457143 -91.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25573" ObjectName="SW-YA_TP.YA_TP_0526SW"/>
     <cge:Meas_Ref ObjectId="145870"/>
    <cge:TPSR_Ref TObjectID="25573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 787.514286 -287.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 787.514286 -87.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 973.971429 -290.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 973.971429 -90.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145915">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1154.028571 -292.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25575" ObjectName="SW-YA_TP.YA_TP_0551SW"/>
     <cge:Meas_Ref ObjectId="145915"/>
    <cge:TPSR_Ref TObjectID="25575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145916">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1154.028571 -92.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25576" ObjectName="SW-YA_TP.YA_TP_0556SW"/>
     <cge:Meas_Ref ObjectId="145916"/>
    <cge:TPSR_Ref TObjectID="25576"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1347.685714 -292.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1347.685714 -92.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145961">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1527.742857 -292.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25578" ObjectName="SW-YA_TP.YA_TP_0571SW"/>
     <cge:Meas_Ref ObjectId="145961"/>
    <cge:TPSR_Ref TObjectID="25578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145962">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1527.742857 -92.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25579" ObjectName="SW-YA_TP.YA_TP_0576SW"/>
     <cge:Meas_Ref ObjectId="145962"/>
    <cge:TPSR_Ref TObjectID="25579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145864">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1724.028571 -291.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25569" ObjectName="SW-YA_TP.YA_TP_0581SW"/>
     <cge:Meas_Ref ObjectId="145864"/>
    <cge:TPSR_Ref TObjectID="25569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-146007">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 406.000000 -291.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25581" ObjectName="SW-YA_TP.YA_TP_0511SW"/>
     <cge:Meas_Ref ObjectId="146007"/>
    <cge:TPSR_Ref TObjectID="25581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-146008">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 406.000000 -180.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25582" ObjectName="SW-YA_TP.YA_TP_0516SW"/>
     <cge:Meas_Ref ObjectId="146008"/>
    <cge:TPSR_Ref TObjectID="25582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145865">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1040.971429 -385.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25570" ObjectName="SW-YA_TP.YA_TP_0901SW"/>
     <cge:Meas_Ref ObjectId="145865"/>
    <cge:TPSR_Ref TObjectID="25570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1043.000000 -452.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_3729860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-779 705,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25544@0" ObjectIDZND0="25562@1" Pin0InfoVect0LinkObjId="SW-145632_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38f9c50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-779 705,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ce0a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1002,-805 923,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="25559@0" ObjectIDZND0="25558@x" ObjectIDZND1="25544@0" Pin0InfoVect0LinkObjId="SW-145627_0" Pin0InfoVect1LinkObjId="g_38f9c50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145628_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1002,-805 923,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e7a960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="923,-819 923,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="25558@0" ObjectIDZND0="25559@x" ObjectIDZND1="25544@0" Pin0InfoVect0LinkObjId="SW-145628_0" Pin0InfoVect1LinkObjId="g_38f9c50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145627_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="923,-819 923,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38f9c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="923,-805 923,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="25559@x" ObjectIDND1="25558@x" ObjectIDZND0="25544@0" Pin0InfoVect0LinkObjId="g_3c9c9a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-145628_0" Pin1InfoVect1LinkObjId="SW-145627_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="923,-805 923,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_430c800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="868,-881 923,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="25560@0" ObjectIDZND0="25558@x" ObjectIDZND1="g_4071c60@0" ObjectIDZND2="g_406f780@0" Pin0InfoVect0LinkObjId="SW-145627_0" Pin0InfoVect1LinkObjId="g_4071c60_0" Pin0InfoVect2LinkObjId="g_406f780_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145629_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="868,-881 923,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d1adf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="923,-881 923,-855 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="25560@x" ObjectIDND1="g_4071c60@0" ObjectIDND2="g_406f780@0" ObjectIDZND0="25558@1" Pin0InfoVect0LinkObjId="SW-145627_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-145629_0" Pin1InfoVect1LinkObjId="g_4071c60_0" Pin1InfoVect2LinkObjId="g_406f780_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="923,-881 923,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2da8d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1650,-839 1574,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25556@0" ObjectIDZND0="25552@x" ObjectIDZND1="25551@x" Pin0InfoVect0LinkObjId="SW-145564_0" Pin0InfoVect1LinkObjId="SW-145562_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145568_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1650,-839 1574,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d9b590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1574,-828 1574,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25552@1" ObjectIDZND0="25556@x" ObjectIDZND1="25551@x" Pin0InfoVect0LinkObjId="SW-145568_0" Pin0InfoVect1LinkObjId="SW-145562_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145564_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1574,-828 1574,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cb6610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1574,-839 1574,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25556@x" ObjectIDND1="25552@x" ObjectIDZND0="25551@0" Pin0InfoVect0LinkObjId="SW-145562_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-145568_0" Pin1InfoVect1LinkObjId="SW-145564_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1574,-839 1574,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2daee20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1650,-889 1574,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="25554@0" ObjectIDZND0="25551@x" ObjectIDZND1="25553@x" Pin0InfoVect0LinkObjId="SW-145562_0" Pin0InfoVect1LinkObjId="SW-145565_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145566_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1650,-889 1574,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e321b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1574,-878 1574,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25551@1" ObjectIDZND0="25554@x" ObjectIDZND1="25553@x" Pin0InfoVect0LinkObjId="SW-145566_0" Pin0InfoVect1LinkObjId="SW-145565_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145562_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1574,-878 1574,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d4e5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1574,-889 1574,-898 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25554@x" ObjectIDND1="25551@x" ObjectIDZND0="25553@0" Pin0InfoVect0LinkObjId="SW-145565_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-145566_0" Pin1InfoVect1LinkObjId="SW-145562_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1574,-889 1574,-898 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e07ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1503,-945 1574,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="25555@0" ObjectIDZND0="25553@x" ObjectIDZND1="g_48642e0@0" ObjectIDZND2="g_3c4d410@0" Pin0InfoVect0LinkObjId="SW-145565_0" Pin0InfoVect1LinkObjId="g_48642e0_0" Pin0InfoVect2LinkObjId="g_3c4d410_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145567_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1503,-945 1574,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cbe590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1574,-934 1574,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="25553@1" ObjectIDZND0="25555@x" ObjectIDZND1="g_48642e0@0" ObjectIDZND2="g_3c4d410@0" Pin0InfoVect0LinkObjId="SW-145567_0" Pin0InfoVect1LinkObjId="g_48642e0_0" Pin0InfoVect2LinkObjId="g_3c4d410_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145565_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1574,-934 1574,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ec0f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-470 705,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25563@0" ObjectIDZND0="25564@1" Pin0InfoVect0LinkObjId="SW-145705_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145703_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-470 705,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_43111b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-400 705,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25564@0" ObjectIDZND0="25545@0" Pin0InfoVect0LinkObjId="g_40bde80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145705_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-400 705,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d9b8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1399,-470 1399,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25568@0" ObjectIDZND0="25567@1" Pin0InfoVect0LinkObjId="SW-145750_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145821_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1399,-470 1399,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d4de10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1329,-697 1399,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="25566@x" ObjectIDZND1="25565@x" Pin0InfoVect0LinkObjId="SW-145749_0" Pin0InfoVect1LinkObjId="SW-145747_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1329,-697 1399,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d765c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1399,-713 1399,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25566@0" ObjectIDZND0="0@x" ObjectIDZND1="25565@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-145747_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1399,-713 1399,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40bea20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1399,-697 1399,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25566@x" ObjectIDND1="0@x" ObjectIDZND0="25565@1" Pin0InfoVect0LinkObjId="SW-145747_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-145749_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1399,-697 1399,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dfed70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1399,-779 1399,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25544@0" ObjectIDZND0="25566@1" Pin0InfoVect0LinkObjId="SW-145749_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38f9c50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1399,-779 1399,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40bde80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1399,-400 1399,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25567@0" ObjectIDZND0="25545@0" Pin0InfoVect0LinkObjId="g_43111b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1399,-400 1399,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dbbbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1399,-648 1399,-611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25565@0" ObjectIDZND0="25586@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145747_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1399,-648 1399,-611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4310770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1399,-532 1399,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="25586@0" ObjectIDZND0="25568@1" Pin0InfoVect0LinkObjId="SW-145821_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dbbbb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1399,-532 1399,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c9c9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="523,-796 523,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25547@0" ObjectIDZND0="25544@0" Pin0InfoVect0LinkObjId="g_38f9c50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145521_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="523,-796 523,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ec4230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="468,-846 523,-846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25549@0" ObjectIDZND0="25547@x" ObjectIDZND1="25546@x" Pin0InfoVect0LinkObjId="SW-145521_0" Pin0InfoVect1LinkObjId="SW-145519_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145523_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="468,-846 523,-846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_343c690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="523,-846 523,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25549@x" ObjectIDND1="25546@x" ObjectIDZND0="25547@1" Pin0InfoVect0LinkObjId="SW-145521_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-145523_0" Pin1InfoVect1LinkObjId="SW-145519_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="523,-846 523,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_428b590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="523,-859 523,-846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25546@0" ObjectIDZND0="25549@x" ObjectIDZND1="25547@x" Pin0InfoVect0LinkObjId="SW-145523_0" Pin0InfoVect1LinkObjId="SW-145521_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145519_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="523,-859 523,-846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cd3230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="523,-901 523,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25548@0" ObjectIDZND0="25546@1" Pin0InfoVect0LinkObjId="SW-145519_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145522_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="523,-901 523,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_44631a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="468,-946 523,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="25550@0" ObjectIDZND0="25548@x" ObjectIDZND1="g_3c59f70@0" ObjectIDZND2="g_36e0d00@0" Pin0InfoVect0LinkObjId="SW-145522_0" Pin0InfoVect1LinkObjId="g_3c59f70_0" Pin0InfoVect2LinkObjId="g_36e0d00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145524_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="468,-946 523,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ed9c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="523,-946 523,-937 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="25550@x" ObjectIDND1="g_3c59f70@0" ObjectIDND2="g_36e0d00@0" ObjectIDZND0="25548@1" Pin0InfoVect0LinkObjId="SW-145522_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-145524_0" Pin1InfoVect1LinkObjId="g_3c59f70_0" Pin1InfoVect2LinkObjId="g_36e0d00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="523,-946 523,-937 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d59070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="475,-981 523,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3c59f70@0" ObjectIDZND0="25550@x" ObjectIDZND1="25548@x" ObjectIDZND2="g_36e0d00@0" Pin0InfoVect0LinkObjId="SW-145524_0" Pin0InfoVect1LinkObjId="SW-145522_0" Pin0InfoVect2LinkObjId="g_36e0d00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c59f70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="475,-981 523,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e4fae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="523,-998 523,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="37767@1" ObjectIDZND0="g_3c59f70@0" ObjectIDZND1="25550@x" ObjectIDZND2="25548@x" Pin0InfoVect0LinkObjId="g_3c59f70_0" Pin0InfoVect1LinkObjId="SW-145524_0" Pin0InfoVect2LinkObjId="SW-145522_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="523,-998 523,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e05a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="523,-981 523,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3c59f70@0" ObjectIDND1="g_36e0d00@0" ObjectIDND2="37767@1" ObjectIDZND0="25550@x" ObjectIDZND1="25548@x" Pin0InfoVect0LinkObjId="SW-145524_0" Pin0InfoVect1LinkObjId="SW-145522_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c59f70_0" Pin1InfoVect1LinkObjId="g_36e0d00_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="523,-981 523,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cb76f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="523,-981 567,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3c59f70@0" ObjectIDND1="25550@x" ObjectIDND2="25548@x" ObjectIDZND0="g_36e0d00@0" Pin0InfoVect0LinkObjId="g_36e0d00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c59f70_0" Pin1InfoVect1LinkObjId="SW-145524_0" Pin1InfoVect2LinkObjId="SW-145522_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="523,-981 567,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4296750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1254,-811 1254,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_3d79c70@1" ObjectIDZND0="25544@0" Pin0InfoVect0LinkObjId="g_38f9c50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d79c70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1254,-811 1254,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ac9b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1254,-879 1254,-856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="25587@1" ObjectIDZND0="g_3d79c70@0" Pin0InfoVect0LinkObjId="g_3d79c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1254,-879 1254,-856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e7dc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1575,-981 1652,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="25553@x" ObjectIDND1="25555@x" ObjectIDND2="g_3c4d410@0" ObjectIDZND0="g_48642e0@0" Pin0InfoVect0LinkObjId="g_48642e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-145565_0" Pin1InfoVect1LinkObjId="SW-145567_0" Pin1InfoVect2LinkObjId="g_3c4d410_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1575,-981 1652,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eca7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1574,-996 1574,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="38084@1" ObjectIDZND0="g_48642e0@0" ObjectIDZND1="25553@x" ObjectIDZND2="25555@x" Pin0InfoVect0LinkObjId="g_48642e0_0" Pin0InfoVect1LinkObjId="SW-145565_0" Pin0InfoVect2LinkObjId="SW-145567_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1574,-996 1574,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b94b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1574,-981 1574,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_48642e0@0" ObjectIDND1="g_3c4d410@0" ObjectIDND2="38084@1" ObjectIDZND0="25553@x" ObjectIDZND1="25555@x" Pin0InfoVect0LinkObjId="SW-145565_0" Pin0InfoVect1LinkObjId="SW-145567_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_48642e0_0" Pin1InfoVect1LinkObjId="g_3c4d410_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1574,-981 1574,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ecb8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1503,-981 1574,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3c4d410@0" ObjectIDZND0="g_48642e0@0" ObjectIDZND1="25553@x" ObjectIDZND2="25555@x" Pin0InfoVect0LinkObjId="g_48642e0_0" Pin0InfoVect1LinkObjId="SW-145565_0" Pin0InfoVect2LinkObjId="SW-145567_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c4d410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1503,-981 1574,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ec9990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1574,-792 1574,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25552@0" ObjectIDZND0="25544@0" Pin0InfoVect0LinkObjId="g_38f9c50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145564_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1574,-792 1574,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ecd130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-713 705,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25562@0" ObjectIDZND0="25561@1" Pin0InfoVect0LinkObjId="SW-145630_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145632_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-713 705,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d628a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="601,-269 601,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25571@1" ObjectIDZND0="25572@0" Pin0InfoVect0LinkObjId="SW-145869_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145867_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="601,-269 601,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4307de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="601,-134 601,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25573@1" ObjectIDZND0="25571@0" Pin0InfoVect0LinkObjId="SW-145867_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145870_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="601,-134 601,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e7b210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="645,-83 601,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_3ec9f90@0" ObjectIDZND0="34275@x" ObjectIDZND1="25573@x" Pin0InfoVect0LinkObjId="EC-YA_TP.052Ld_0" Pin0InfoVect1LinkObjId="SW-145870_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ec9f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="645,-83 601,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d28b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="601,-49 601,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34275@0" ObjectIDZND0="g_3ec9f90@0" ObjectIDZND1="25573@x" Pin0InfoVect0LinkObjId="g_3ec9f90_0" Pin0InfoVect1LinkObjId="SW-145870_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_TP.052Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="601,-49 601,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e4f400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="601,-83 601,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34275@x" ObjectIDND1="g_3ec9f90@0" ObjectIDZND0="25573@0" Pin0InfoVect0LinkObjId="SW-145870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_TP.052Ld_0" Pin1InfoVect1LinkObjId="g_3ec9f90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="601,-83 601,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dd7410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="601,-332 601,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25572@1" ObjectIDZND0="25545@0" Pin0InfoVect0LinkObjId="g_43111b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145869_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="601,-332 601,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a82790">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="797,-265 797,-292 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="797,-265 797,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_42d82c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="797,-130 797,-238 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="797,-130 797,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3edb900">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="841,-79 797,-79 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2d9a500@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d9a500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="841,-79 797,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3aaad30">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="797,-45 797,-79 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2d9a500@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2d9a500_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="797,-45 797,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fe5340">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="797,-79 797,-92 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2d9a500@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2d9a500_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="797,-79 797,-92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d9a2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="797,-328 797,-363 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="25545@0" Pin0InfoVect0LinkObjId="g_43111b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="797,-328 797,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_40b88e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="983,-268 983,-295 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="983,-268 983,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3d3e710">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="983,-133 983,-241 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="983,-133 983,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4873bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1027,-82 983,-82 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_3e7c7a0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e7c7a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1027,-82 983,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3bf3470">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="983,-48 983,-82 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_3e7c7a0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_3e7c7a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="983,-48 983,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3bf36d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="983,-82 983,-95 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_3e7c7a0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3e7c7a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="983,-82 983,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4309970">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="983,-331 983,-363 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="25545@0" Pin0InfoVect0LinkObjId="g_43111b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="983,-331 983,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cb6c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1163,-271 1163,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25574@1" ObjectIDZND0="25575@0" Pin0InfoVect0LinkObjId="SW-145915_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145913_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1163,-271 1163,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ca95a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1163,-135 1163,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25576@1" ObjectIDZND0="25574@0" Pin0InfoVect0LinkObjId="SW-145913_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145916_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1163,-135 1163,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3dd17f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1207,-84 1163,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_4309b60@0" ObjectIDZND0="34276@x" ObjectIDZND1="25576@x" Pin0InfoVect0LinkObjId="EC-YA_TP.055Ld_0" Pin0InfoVect1LinkObjId="SW-145916_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4309b60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1207,-84 1163,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3eda410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1163,-50 1163,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34276@0" ObjectIDZND0="g_4309b60@0" ObjectIDZND1="25576@x" Pin0InfoVect0LinkObjId="g_4309b60_0" Pin0InfoVect1LinkObjId="SW-145916_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_TP.055Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1163,-50 1163,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3eda650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1163,-84 1163,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34276@x" ObjectIDND1="g_4309b60@0" ObjectIDZND0="25576@0" Pin0InfoVect0LinkObjId="SW-145916_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_TP.055Ld_0" Pin1InfoVect1LinkObjId="g_4309b60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1163,-84 1163,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_430ca10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1163,-333 1163,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25575@1" ObjectIDZND0="25545@0" Pin0InfoVect0LinkObjId="g_43111b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145915_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1163,-333 1163,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3eddf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1357,-270 1357,-297 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-270 1357,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ede1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1357,-135 1357,-243 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-135 1357,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b82570">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1401,-84 1357,-84 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_43113d0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_43113d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-84 1357,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b827d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1357,-50 1357,-84 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_43113d0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_43113d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-50 1357,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c808d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1357,-84 1357,-97 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_43113d0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_43113d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-84 1357,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4874f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1357,-333 1357,-363 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="25545@0" Pin0InfoVect0LinkObjId="g_43111b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-333 1357,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ebfb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1537,-271 1537,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25577@1" ObjectIDZND0="25578@0" Pin0InfoVect0LinkObjId="SW-145961_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145959_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1537,-271 1537,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ebfd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1537,-135 1537,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25579@1" ObjectIDZND0="25577@0" Pin0InfoVect0LinkObjId="SW-145959_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145962_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1537,-135 1537,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e61b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1581,-84 1537,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_3ecdba0@0" ObjectIDZND0="34277@x" ObjectIDZND1="25579@x" Pin0InfoVect0LinkObjId="EC-YA_TP.057Ld_0" Pin0InfoVect1LinkObjId="SW-145962_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ecdba0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1581,-84 1537,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e61df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1537,-50 1537,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34277@0" ObjectIDZND0="g_3ecdba0@0" ObjectIDZND1="25579@x" Pin0InfoVect0LinkObjId="g_3ecdba0_0" Pin0InfoVect1LinkObjId="SW-145962_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_TP.057Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1537,-50 1537,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e62050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1537,-84 1537,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34277@x" ObjectIDND1="g_3ecdba0@0" ObjectIDZND0="25579@0" Pin0InfoVect0LinkObjId="SW-145962_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_TP.057Ld_0" Pin1InfoVect1LinkObjId="g_3ecdba0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1537,-84 1537,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ecd940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1537,-333 1537,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25578@1" ObjectIDZND0="25545@0" Pin0InfoVect0LinkObjId="g_43111b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145961_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1537,-333 1537,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ebbc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1733,-332 1733,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25569@1" ObjectIDZND0="25545@0" Pin0InfoVect0LinkObjId="g_43111b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145864_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1733,-332 1733,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ed03e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1733,-296 1733,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="25569@0" ObjectIDZND0="25588@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145864_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1733,-296 1733,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ed0640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="415,-269 415,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25580@1" ObjectIDZND0="25581@0" Pin0InfoVect0LinkObjId="SW-146007_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-146005_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="415,-269 415,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ed08a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="415,-223 415,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25582@1" ObjectIDZND0="25580@0" Pin0InfoVect0LinkObjId="SW-146005_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-146008_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="415,-223 415,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ec2050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="415,-332 415,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25581@1" ObjectIDZND0="25545@0" Pin0InfoVect0LinkObjId="g_43111b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-146007_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="415,-332 415,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ed7760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="359,-156 415,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="g_430e9e0@0" ObjectIDZND0="25582@x" ObjectIDZND1="41826@x" Pin0InfoVect0LinkObjId="SW-146008_0" Pin0InfoVect1LinkObjId="CB-YA_TP.YA_TP_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_430e9e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="359,-156 415,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ed7990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="415,-185 415,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="25582@0" ObjectIDZND0="g_430e9e0@0" ObjectIDZND1="41826@x" Pin0InfoVect0LinkObjId="g_430e9e0_0" Pin0InfoVect1LinkObjId="CB-YA_TP.YA_TP_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-146008_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="415,-185 415,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ed7bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="415,-156 415,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="capacitor" ObjectIDND0="25582@x" ObjectIDND1="g_430e9e0@0" ObjectIDZND0="41826@0" Pin0InfoVect0LinkObjId="CB-YA_TP.YA_TP_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-146008_0" Pin1InfoVect1LinkObjId="g_430e9e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="415,-156 415,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42d4f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1050,-390 1050,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25570@0" ObjectIDZND0="25545@0" Pin0InfoVect0LinkObjId="g_43111b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145865_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1050,-390 1050,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4072950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="952,-927 952,-920 923,-920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_4071c60@0" ObjectIDZND0="g_406f780@0" ObjectIDZND1="25560@x" ObjectIDZND2="25558@x" Pin0InfoVect0LinkObjId="g_406f780_0" Pin0InfoVect1LinkObjId="SW-145629_0" Pin0InfoVect2LinkObjId="SW-145627_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4071c60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="952,-927 952,-920 923,-920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4073420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="923,-988 923,-920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_406f780@0" ObjectIDZND0="g_4071c60@0" ObjectIDZND1="25560@x" ObjectIDZND2="25558@x" Pin0InfoVect0LinkObjId="g_4071c60_0" Pin0InfoVect1LinkObjId="SW-145629_0" Pin0InfoVect2LinkObjId="SW-145627_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_406f780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="923,-988 923,-920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4073680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="923,-920 923,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_4071c60@0" ObjectIDND1="g_406f780@0" ObjectIDZND0="25560@x" ObjectIDZND1="25558@x" Pin0InfoVect0LinkObjId="SW-145629_0" Pin0InfoVect1LinkObjId="SW-145627_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4071c60_0" Pin1InfoVect1LinkObjId="g_406f780_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="923,-920 923,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4078610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1050,-522 1050,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_40738e0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40738e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1050,-522 1050,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4079520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1098,-449 1098,-443 1050,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_4078870@0" ObjectIDZND0="0@x" ObjectIDZND1="25570@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-145865_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4078870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1098,-449 1098,-443 1050,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4079ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1050,-457 1050,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="25570@x" ObjectIDZND1="g_4078870@0" Pin0InfoVect0LinkObjId="SW-145865_0" Pin0InfoVect1LinkObjId="g_4078870_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1050,-457 1050,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_407a250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1050,-443 1050,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_4078870@0" ObjectIDZND0="25570@1" Pin0InfoVect0LinkObjId="SW-145865_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_4078870_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1050,-443 1050,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_407b8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-648 705,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25561@0" ObjectIDZND0="25585@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-648 705,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_407baf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-527 705,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="25585@0" ObjectIDZND0="25563@1" Pin0InfoVect0LinkObjId="SW-145703_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_407b8c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-527 705,-497 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YA_TP"/>
</svg>