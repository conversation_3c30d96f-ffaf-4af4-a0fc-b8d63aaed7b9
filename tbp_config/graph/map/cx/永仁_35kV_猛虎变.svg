<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-193" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-13 -1103 1888 1161">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape132">
    <rect height="16" stroke-width="1" width="31" x="5" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="36" y1="9" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape42_0">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="73" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="79" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="81" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape42_1">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="49" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="55" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="57" y2="55"/>
   </symbol>
   <symbol id="transformer2:shape10_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape10_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="33" x2="33" y1="33" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="49" x2="33" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="49" x2="33" y1="24" y2="33"/>
   </symbol>
   <symbol id="transformer2:shape46_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="61" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="62" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="16" y1="56" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="31" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,62 40,62 40,31 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="47" y2="5"/>
    <circle cx="16" cy="62" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="61" y2="56"/>
   </symbol>
   <symbol id="transformer2:shape46_1">
    <circle cx="16" cy="84" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="16,93 10,81 22,81 16,93 16,92 16,93 "/>
   </symbol>
   <symbol id="voltageTransformer:shape1">
    <circle cx="13" cy="13" fillStyle="0" r="12" stroke-width="1"/>
    <circle cx="31" cy="13" fillStyle="0" r="12" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="6" y2="16"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="35" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="6" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="1" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="1" y1="28" y2="19"/>
    <ellipse cx="25" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="37" y1="24" y2="22"/>
    <ellipse cx="25" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="34" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="40" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="34" y1="33" y2="33"/>
    <ellipse cx="36" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="36" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="24" y2="22"/>
   </symbol>
   <symbol id="voltageTransformer:shape71">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="12" y2="12"/>
    <ellipse cx="39" cy="19" fillStyle="0" rx="15" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.36031" x1="30" x2="34" y1="55" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.39762" x1="25" x2="21" y1="55" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.77579" x1="21" x2="35" y1="62" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="33" x2="41" y1="16" y2="20"/>
    <ellipse cx="16" cy="20" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="40" x2="41" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="47" x2="41" y1="16" y2="20"/>
    <ellipse cx="28" cy="56" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="32" y2="32"/>
    <ellipse cx="40" cy="39" fillStyle="0" rx="15" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="32" y2="32"/>
    <ellipse cx="15" cy="40" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="15" x2="16" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="22" x2="16" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="16" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="33" x2="41" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="40" x2="41" y1="40" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="47" x2="41" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="15" x2="16" y1="40" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="21" x2="15" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="16" y1="36" y2="40"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d11c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d12db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d13890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d14550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d14cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d15630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d15e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d16840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d17e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d17e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d19680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d19680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d1b370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d1b370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1d1c300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d1dd10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d1e8b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d1f720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d1fe70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d211a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d21d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d225d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d22d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d23530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d23e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d24980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d25340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d26910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d27390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d28530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d291c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d37170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d2f320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1d30ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1d2aff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1171" width="1898" x="-18" y="-1108"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1030" x2="1046" y1="-555" y2="-555"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1038" x2="1046" y1="-543" y2="-555"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1038" x2="1030" y1="-543" y2="-555"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1030" x2="1046" y1="-528" y2="-528"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1038" x2="1046" y1="-540" y2="-528"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1038" x2="1030" y1="-540" y2="-528"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1444" x2="1460" y1="-557" y2="-557"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1452" x2="1460" y1="-545" y2="-557"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1452" x2="1444" y1="-545" y2="-557"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1444" x2="1460" y1="-530" y2="-530"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1452" x2="1460" y1="-542" y2="-530"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1452" x2="1444" y1="-542" y2="-530"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1072" x2="1080" y1="-133" y2="-145"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1072" x2="1064" y1="-133" y2="-145"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1064" x2="1080" y1="-108" y2="-108"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1072" x2="1080" y1="-120" y2="-108"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1072" x2="1064" y1="-120" y2="-108"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1064" x2="1080" y1="-145" y2="-145"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="912" x2="920" y1="-131" y2="-143"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="912" x2="904" y1="-131" y2="-143"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="904" x2="920" y1="-106" y2="-106"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="912" x2="920" y1="-118" y2="-106"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="912" x2="904" y1="-118" y2="-106"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="904" x2="920" y1="-143" y2="-143"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="608" x2="616" y1="-148" y2="-160"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="608" x2="600" y1="-148" y2="-160"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="600" x2="616" y1="-160" y2="-160"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1376" x2="1384" y1="-130" y2="-142"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1376" x2="1368" y1="-130" y2="-142"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1368" x2="1384" y1="-105" y2="-105"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1376" x2="1384" y1="-117" y2="-105"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1376" x2="1368" y1="-117" y2="-105"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1368" x2="1384" y1="-142" y2="-142"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1552" x2="1560" y1="-128" y2="-140"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1552" x2="1544" y1="-128" y2="-140"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1544" x2="1560" y1="-103" y2="-103"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1552" x2="1560" y1="-115" y2="-103"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1552" x2="1544" y1="-115" y2="-103"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1544" x2="1560" y1="-140" y2="-140"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="1866" x2="1875" y1="-439" y2="-439"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-139443">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1029.000000 -617.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25024" ObjectName="SW-YR_MH.YR_MH_301BK"/>
     <cge:Meas_Ref ObjectId="139443"/>
    <cge:TPSR_Ref TObjectID="25024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139450">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1028.000000 -383.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25026" ObjectName="SW-YR_MH.YR_MH_001BK"/>
     <cge:Meas_Ref ObjectId="139450"/>
    <cge:TPSR_Ref TObjectID="25026"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139599">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1367.000000 -203.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25038" ObjectName="SW-YR_MH.YR_MH_066BK"/>
     <cge:Meas_Ref ObjectId="139599"/>
    <cge:TPSR_Ref TObjectID="25038"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139527">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.000000 -209.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25032" ObjectName="SW-YR_MH.YR_MH_063BK"/>
     <cge:Meas_Ref ObjectId="139527"/>
    <cge:TPSR_Ref TObjectID="25032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139491">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 743.000000 -206.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25029" ObjectName="SW-YR_MH.YR_MH_062BK"/>
     <cge:Meas_Ref ObjectId="139491"/>
    <cge:TPSR_Ref TObjectID="25029"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226946">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1721.000000 -202.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37614" ObjectName="SW-YR_MH.YR_MH_068BK"/>
     <cge:Meas_Ref ObjectId="226946"/>
    <cge:TPSR_Ref TObjectID="37614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159151">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1543.000000 -200.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37611" ObjectName="SW-YR_MH.YR_MH_067BK"/>
     <cge:Meas_Ref ObjectId="159151"/>
    <cge:TPSR_Ref TObjectID="37611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139563">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1063.000000 -209.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25035" ObjectName="SW-YR_MH.YR_MH_064BK"/>
     <cge:Meas_Ref ObjectId="139563"/>
    <cge:TPSR_Ref TObjectID="25035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226821">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.000000 -787.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37595" ObjectName="SW-YR_MH.YR_MH_361BK"/>
     <cge:Meas_Ref ObjectId="226821"/>
    <cge:TPSR_Ref TObjectID="37595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226859">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1498.000000 -790.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37596" ObjectName="SW-YR_MH.YR_MH_364BK"/>
     <cge:Meas_Ref ObjectId="226859"/>
    <cge:TPSR_Ref TObjectID="37596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226742">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1443.000000 -619.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37605" ObjectName="SW-YR_MH.YR_MH_302BK"/>
     <cge:Meas_Ref ObjectId="226742"/>
    <cge:TPSR_Ref TObjectID="37605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226743">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1442.000000 -385.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37608" ObjectName="SW-YR_MH.YR_MH_002BK"/>
     <cge:Meas_Ref ObjectId="226743"/>
    <cge:TPSR_Ref TObjectID="37608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226981">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1196.000000 -239.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37617" ObjectName="SW-YR_MH.YR_MH_012BK"/>
     <cge:Meas_Ref ObjectId="226981"/>
    <cge:TPSR_Ref TObjectID="37617"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_35028d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 756.000000 -931.000000)" xlink:href="#voltageTransformer:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35134f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1347.000000 -934.000000)" xlink:href="#voltageTransformer:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35210c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1143.000000 -941.000000)" xlink:href="#voltageTransformer:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3561a90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 750.000000 -494.000000)" xlink:href="#voltageTransformer:shape71"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_356a900">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1638.000000 -458.000000)" xlink:href="#voltageTransformer:shape71"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YR_MH" endPointId="0" endStationName="YR_TPL" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_mengta" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="916,-993 916,-1024 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38085" ObjectName="AC-35kV.LN_mengta"/>
    <cge:TPSR_Ref TObjectID="38085_SS-193"/></metadata>
   <polyline fill="none" opacity="0" points="916,-993 916,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="YR_MH" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_yongmenglongTmh" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1507,-996 1507,-1028 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37840" ObjectName="AC-35kV.LN_yongmenglongTmh"/>
    <cge:TPSR_Ref TObjectID="37840_SS-193"/></metadata>
   <polyline fill="none" opacity="0" points="1507,-996 1507,-1028 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1263.000000 -937.000000)" xlink:href="#transformer2:shape42_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1263.000000 -937.000000)" xlink:href="#transformer2:shape42_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YR_MH.YR_MH_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="56313"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1414.000000 -431.000000)" xlink:href="#transformer2:shape10_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1414.000000 -431.000000)" xlink:href="#transformer2:shape10_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="37620" ObjectName="TF-YR_MH.YR_MH_2T"/>
    <cge:TPSR_Ref TObjectID="37620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 592.000000 -129.000000)" xlink:href="#transformer2:shape46_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 592.000000 -129.000000)" xlink:href="#transformer2:shape46_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YR_MH.YR_MH_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="35289"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1000.000000 -429.000000)" xlink:href="#transformer2:shape10_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1000.000000 -429.000000)" xlink:href="#transformer2:shape10_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25041" ObjectName="TF-YR_MH.YR_MH_1T"/>
    <cge:TPSR_Ref TObjectID="25041"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_34397a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1337.000000 -22.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34c1e10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1511.000000 -23.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34cacf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1031.000000 -22.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34d56f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 867.000000 -20.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3501b10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 805.000000 -935.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3503f00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 943.000000 -913.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3512730">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1396.000000 -938.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3514290">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1534.000000 -916.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_351e9e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1072.000000 -853.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3523cc0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1159.000000 -848.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3524a00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1285.000000 -780.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_352de30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1073.000000 -554.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_353e3f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1487.000000 -556.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_355cde0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 707.000000 -394.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3567af0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1594.000000 -392.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_356a080">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1670.000000 -413.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 107.000000 -953.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-250473" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 140.000000 -817.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250473" ObjectName="YR_MH:YR_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-250474" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 140.000000 -780.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250474" ObjectName="YR_MH:YR_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-227035" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1578.000000 -536.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227035" ObjectName="YR_MH:YR_MH_2T_TAP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-227034" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1580.000000 -516.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227034" ObjectName="YR_MH:YR_MH_2T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-139378" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1168.000000 -482.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139378" ObjectName="YR_MH:YR_MH_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-139379" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1167.000000 -499.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139379" ObjectName="YR_MH:YR_MH_1T_Tap"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-139369" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1165.000000 -683.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139369" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25024"/>
     <cge:Term_Ref ObjectID="35253"/>
    <cge:TPSR_Ref TObjectID="25024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-139370" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1165.000000 -683.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139370" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25024"/>
     <cge:Term_Ref ObjectID="35253"/>
    <cge:TPSR_Ref TObjectID="25024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-139366" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1165.000000 -683.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139366" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25024"/>
     <cge:Term_Ref ObjectID="35253"/>
    <cge:TPSR_Ref TObjectID="25024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-139375" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1167.000000 -412.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139375" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25026"/>
     <cge:Term_Ref ObjectID="35257"/>
    <cge:TPSR_Ref TObjectID="25026"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-139376" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1167.000000 -412.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139376" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25026"/>
     <cge:Term_Ref ObjectID="35257"/>
    <cge:TPSR_Ref TObjectID="25026"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-139372" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1167.000000 -412.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139372" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25026"/>
     <cge:Term_Ref ObjectID="35257"/>
    <cge:TPSR_Ref TObjectID="25026"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="1" id="ME-139380" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 791.000000 -813.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139380" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25019"/>
     <cge:Term_Ref ObjectID="35245"/>
    <cge:TPSR_Ref TObjectID="25019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="1" id="ME-139381" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 791.000000 -813.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139381" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25019"/>
     <cge:Term_Ref ObjectID="35245"/>
    <cge:TPSR_Ref TObjectID="25019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="1" id="ME-139382" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 791.000000 -813.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139382" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25019"/>
     <cge:Term_Ref ObjectID="35245"/>
    <cge:TPSR_Ref TObjectID="25019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="1" id="ME-139383" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 791.000000 -813.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139383" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25019"/>
     <cge:Term_Ref ObjectID="35245"/>
    <cge:TPSR_Ref TObjectID="25019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="1" id="ME-139387" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 791.000000 -813.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139387" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25019"/>
     <cge:Term_Ref ObjectID="35245"/>
    <cge:TPSR_Ref TObjectID="25019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-139388" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 569.000000 -419.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139388" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25020"/>
     <cge:Term_Ref ObjectID="35246"/>
    <cge:TPSR_Ref TObjectID="25020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-139389" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 569.000000 -419.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139389" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25020"/>
     <cge:Term_Ref ObjectID="35246"/>
    <cge:TPSR_Ref TObjectID="25020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-139390" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 569.000000 -419.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139390" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25020"/>
     <cge:Term_Ref ObjectID="35246"/>
    <cge:TPSR_Ref TObjectID="25020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-139391" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 569.000000 -419.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139391" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25020"/>
     <cge:Term_Ref ObjectID="35246"/>
    <cge:TPSR_Ref TObjectID="25020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-139395" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 569.000000 -419.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139395" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25020"/>
     <cge:Term_Ref ObjectID="35246"/>
    <cge:TPSR_Ref TObjectID="25020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-227068" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 877.000000 -1093.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227068" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37595"/>
     <cge:Term_Ref ObjectID="56261"/>
    <cge:TPSR_Ref TObjectID="37595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-227069" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 877.000000 -1093.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227069" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37595"/>
     <cge:Term_Ref ObjectID="56261"/>
    <cge:TPSR_Ref TObjectID="37595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-227065" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 877.000000 -1093.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227065" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37595"/>
     <cge:Term_Ref ObjectID="56261"/>
    <cge:TPSR_Ref TObjectID="37595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-227074" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1484.000000 -1102.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227074" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37596"/>
     <cge:Term_Ref ObjectID="56263"/>
    <cge:TPSR_Ref TObjectID="37596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-227075" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1484.000000 -1102.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227075" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37596"/>
     <cge:Term_Ref ObjectID="56263"/>
    <cge:TPSR_Ref TObjectID="37596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-227071" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1484.000000 -1102.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227071" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37596"/>
     <cge:Term_Ref ObjectID="56263"/>
    <cge:TPSR_Ref TObjectID="37596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-227025" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1563.000000 -682.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227025" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37605"/>
     <cge:Term_Ref ObjectID="56281"/>
    <cge:TPSR_Ref TObjectID="37605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-227026" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1563.000000 -682.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227026" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37605"/>
     <cge:Term_Ref ObjectID="56281"/>
    <cge:TPSR_Ref TObjectID="37605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-227022" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1563.000000 -682.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227022" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37605"/>
     <cge:Term_Ref ObjectID="56281"/>
    <cge:TPSR_Ref TObjectID="37605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-227031" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1562.000000 -425.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227031" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37608"/>
     <cge:Term_Ref ObjectID="56287"/>
    <cge:TPSR_Ref TObjectID="37608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-227032" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1562.000000 -425.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227032" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37608"/>
     <cge:Term_Ref ObjectID="56287"/>
    <cge:TPSR_Ref TObjectID="37608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-227028" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1562.000000 -425.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227028" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37608"/>
     <cge:Term_Ref ObjectID="56287"/>
    <cge:TPSR_Ref TObjectID="37608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-139399" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 725.000000 13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139399" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25029"/>
     <cge:Term_Ref ObjectID="35263"/>
    <cge:TPSR_Ref TObjectID="25029"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-139400" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 725.000000 13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139400" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25029"/>
     <cge:Term_Ref ObjectID="35263"/>
    <cge:TPSR_Ref TObjectID="25029"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-139396" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 725.000000 13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139396" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25029"/>
     <cge:Term_Ref ObjectID="35263"/>
    <cge:TPSR_Ref TObjectID="25029"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-139405" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 881.000000 6.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139405" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25032"/>
     <cge:Term_Ref ObjectID="35269"/>
    <cge:TPSR_Ref TObjectID="25032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-139406" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 881.000000 6.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139406" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25032"/>
     <cge:Term_Ref ObjectID="35269"/>
    <cge:TPSR_Ref TObjectID="25032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-139402" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 881.000000 6.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139402" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25032"/>
     <cge:Term_Ref ObjectID="35269"/>
    <cge:TPSR_Ref TObjectID="25032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-139411" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1046.000000 9.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139411" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25035"/>
     <cge:Term_Ref ObjectID="35275"/>
    <cge:TPSR_Ref TObjectID="25035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-139412" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1046.000000 9.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139412" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25035"/>
     <cge:Term_Ref ObjectID="35275"/>
    <cge:TPSR_Ref TObjectID="25035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-139408" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1046.000000 9.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139408" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25035"/>
     <cge:Term_Ref ObjectID="35275"/>
    <cge:TPSR_Ref TObjectID="25035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-139417" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 12.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139417" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25038"/>
     <cge:Term_Ref ObjectID="35281"/>
    <cge:TPSR_Ref TObjectID="25038"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-139418" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 12.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139418" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25038"/>
     <cge:Term_Ref ObjectID="35281"/>
    <cge:TPSR_Ref TObjectID="25038"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-139414" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 12.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139414" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25038"/>
     <cge:Term_Ref ObjectID="35281"/>
    <cge:TPSR_Ref TObjectID="25038"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-227048" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1193.000000 -237.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227048" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37617"/>
     <cge:Term_Ref ObjectID="56305"/>
    <cge:TPSR_Ref TObjectID="37617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-227049" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1193.000000 -237.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227049" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37617"/>
     <cge:Term_Ref ObjectID="56305"/>
    <cge:TPSR_Ref TObjectID="37617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-227045" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1193.000000 -237.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227045" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37617"/>
     <cge:Term_Ref ObjectID="56305"/>
    <cge:TPSR_Ref TObjectID="37617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-227054" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1528.000000 8.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227054" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37611"/>
     <cge:Term_Ref ObjectID="56293"/>
    <cge:TPSR_Ref TObjectID="37611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-227055" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1528.000000 8.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227055" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37611"/>
     <cge:Term_Ref ObjectID="56293"/>
    <cge:TPSR_Ref TObjectID="37611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-227051" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1528.000000 8.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227051" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37611"/>
     <cge:Term_Ref ObjectID="56293"/>
    <cge:TPSR_Ref TObjectID="37611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-227060" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1704.000000 7.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227060" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37614"/>
     <cge:Term_Ref ObjectID="56299"/>
    <cge:TPSR_Ref TObjectID="37614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-227061" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1704.000000 7.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227061" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37614"/>
     <cge:Term_Ref ObjectID="56299"/>
    <cge:TPSR_Ref TObjectID="37614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-227057" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1704.000000 7.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227057" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37614"/>
     <cge:Term_Ref ObjectID="56299"/>
    <cge:TPSR_Ref TObjectID="37614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-227037" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1820.000000 -406.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227037" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37621"/>
     <cge:Term_Ref ObjectID="56315"/>
    <cge:TPSR_Ref TObjectID="37621"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-227038" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1820.000000 -406.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227038" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37621"/>
     <cge:Term_Ref ObjectID="56315"/>
    <cge:TPSR_Ref TObjectID="37621"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-227039" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1820.000000 -406.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227039" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37621"/>
     <cge:Term_Ref ObjectID="56315"/>
    <cge:TPSR_Ref TObjectID="37621"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-227040" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1820.000000 -406.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227040" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37621"/>
     <cge:Term_Ref ObjectID="56315"/>
    <cge:TPSR_Ref TObjectID="37621"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-227044" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1820.000000 -406.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="227044" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="37621"/>
     <cge:Term_Ref ObjectID="56315"/>
    <cge:TPSR_Ref TObjectID="37621"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="119" y="-1012"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="70" y="-1029"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="391" y="-986"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="391" y="-986"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="391" y="-952"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="391" y="-952"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="925" y="-816"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="925" y="-816"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1516" y="-819"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1516" y="-819"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="934" y="-478"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="934" y="-478"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1488" y="-483"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1488" y="-483"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="761" y="-235"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="761" y="-235"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="923" y="-238"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="923" y="-238"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1084" y="-239"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1084" y="-239"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="1206" y="-273"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="1206" y="-273"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1396" y="-232"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1396" y="-232"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1572" y="-229"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1572" y="-229"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1739" y="-231"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1739" y="-231"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="60" x="27" y="-581"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="60" x="27" y="-581"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="283" y="-917"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="283" y="-917"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="391" y="-986"/></g>
   <g href="cx_配调_配网接线图35_永仁.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="391" y="-952"/></g>
   <g href="35kV猛虎变YR_MH_361间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="925" y="-816"/></g>
   <g href="35kV猛虎变YR_MH_364间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1516" y="-819"/></g>
   <g href="35kV猛虎变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="934" y="-478"/></g>
   <g href="35kV猛虎变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1488" y="-483"/></g>
   <g href="35kV猛虎变YR_MH_062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="761" y="-235"/></g>
   <g href="35kV猛虎变YR_MH_063间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="923" y="-238"/></g>
   <g href="35kV猛虎变YR_MH_064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1084" y="-239"/></g>
   <g href="35kV猛虎变YR_MH_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="1206" y="-273"/></g>
   <g href="35kV猛虎变YR_MH_066间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1396" y="-232"/></g>
   <g href="35kV猛虎变YR_MH_067间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1572" y="-229"/></g>
   <g href="35kV猛虎变YR_MH_068间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1739" y="-231"/></g>
   <g href="35kV猛虎变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="60" x="27" y="-581"/></g>
   <g href="AVC猛虎站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="283" y="-917"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="283" y="-916"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YR_MH.YR_MH_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="865,-721 1590,-721 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25019" ObjectName="BS-YR_MH.YR_MH_3IM"/>
    <cge:TPSR_Ref TObjectID="25019"/></metadata>
   <polyline fill="none" opacity="0" points="865,-721 1590,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YR_MH.YR_MH_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="520,-323 1185,-323 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25020" ObjectName="BS-YR_MH.YR_MH_9IM"/>
    <cge:TPSR_Ref TObjectID="25020"/></metadata>
   <polyline fill="none" opacity="0" points="520,-323 1185,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YR_MH.YR_MH_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1252,-321 1794,-321 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37621" ObjectName="BS-YR_MH.YR_MH_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="37621"/></metadata>
   <polyline fill="none" opacity="0" points="1252,-321 1794,-321 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_34fde00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 820.000000 -883.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_350f2b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1411.000000 -886.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_351c940" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1177.000000 -791.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="25019" cx="1507" cy="-721" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25019" cx="1122" cy="-721" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25019" cx="1294" cy="-721" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25019" cx="916" cy="-721" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25019" cx="1452" cy="-721" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25019" cx="1038" cy="-721" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25020" cx="912" cy="-323" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25020" cx="1072" cy="-323" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25020" cx="1037" cy="-323" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25020" cx="752" cy="-323" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25020" cx="608" cy="-323" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25020" cx="1154" cy="-323" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="37621" cx="1280" cy="-321" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="37621" cx="1376" cy="-321" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="37621" cx="1552" cy="-321" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="37621" cx="1730" cy="-321" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25020" cx="791" cy="-323" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="37621" cx="1451" cy="-321" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="37621" cx="1678" cy="-321" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34cf560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 866.500000 -1048.000000) translate(0,15)">35kV猛他线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34d1030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1548.500000 -742.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34d1d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 730.500000 -17.000000) translate(0,15)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34d2950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 885.500000 -17.000000) translate(0,15)">箐头线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34d3360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1037.500000 -17.000000) translate(0,15)">猛虎街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34d3c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1341.500000 -15.000000) translate(0,15)">么苴地线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34d4830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1528.500000 -15.000000) translate(0,15)">格租线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34d5130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1704.500000 -17.000000) translate(0,15)">备用二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34d6970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1439.500000 -1049.000000) translate(0,15)">35kV永猛龙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34daa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34daa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34daa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34daa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34daa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34daa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34daa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34daa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34daa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34daa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34daa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34daa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34daa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34daa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34daa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34daa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34daa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34daa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34dcfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34dcfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34dcfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34dcfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34dcfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34dcfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34dcfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_34e06b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 159.000000 -1001.500000) translate(0,16)">猛虎变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e11e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1047.000000 -646.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e1ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1045.000000 -691.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e2030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1046.000000 -412.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e2270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1044.000000 -367.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e24b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 923.000000 -238.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e2a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 921.000000 -182.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e2c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 921.000000 -293.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e2ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1084.000000 -239.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e3130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1082.000000 -183.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e3670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1082.000000 -294.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e38b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1396.000000 -232.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e3af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1394.000000 -176.000000) translate(0,12)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e3d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1394.000000 -287.000000) translate(0,12)">0662</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e3f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1572.000000 -229.000000) translate(0,12)">067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e41b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1570.000000 -173.000000) translate(0,12)">0676</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e43f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1570.000000 -284.000000) translate(0,12)">0672</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ece20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 928.000000 -769.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="18" graphid="g_34ed070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 18.500000 -386.000000) translate(0,15)">35kV母线电压值采集在35kV猛他线361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="18" graphid="g_34ed070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 18.500000 -386.000000) translate(0,33)">测控装置，该线路转检修时注意封锁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="18" graphid="g_34ed070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 18.500000 -386.000000) translate(0,51)">35kV母线电压值。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="18" graphid="g_34ed070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 18.500000 -386.000000) translate(0,69)">1、2号主变并列时2号主变档位在2档。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_34f4100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 402.000000 -978.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_34f4f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 402.000000 -946.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35078f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1519.000000 -772.000000) translate(0,12)">3641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3516f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 861.000000 -970.000000) translate(0,12)">3619</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_351d3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1115.000000 -961.000000) translate(0,12)">35kV电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353a5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1469.000000 -647.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353ac00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1459.000000 -695.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353ae40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1460.000000 -414.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_353b080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1458.000000 -369.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354bb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 559.000000 -283.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354c030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 576.000000 -122.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3554b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1205.000000 -289.000000) translate(0,12)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3564740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 802.000000 -370.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_356d350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 925.000000 -816.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_356d840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 923.000000 -865.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_356da80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 851.000000 -914.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_356dcc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 861.000000 -970.000000) translate(0,12)">3619</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_356df00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1129.000000 -764.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_356e140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1137.000000 -821.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_356e380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1301.000000 -762.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_356e5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1516.000000 -819.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_356e800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1514.000000 -868.000000) translate(0,12)">3646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_356ea40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1451.000000 -915.000000) translate(0,12)">36467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_356ec80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1452.000000 -973.000000) translate(0,12)">3649</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_356eec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1045.000000 -599.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_356f100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1459.000000 -601.000000) translate(0,12)">3026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_356f340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1685.000000 -368.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_356f580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1739.000000 -231.000000) translate(0,12)">068</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_356f910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1737.000000 -175.000000) translate(0,12)">0686</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_356fe00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1737.000000 -286.000000) translate(0,12)">0682</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3570040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1206.000000 -273.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3570280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1287.000000 -292.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35704c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1161.000000 -295.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3570700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 -235.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3570940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -179.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3570b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -290.000000) translate(0,12)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3570dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1252.000000 -341.000000) translate(0,12)">10kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35713c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 632.000000 -344.000000) translate(0,12)">10kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3571b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1488.000000 -483.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3573600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 27.000000 -581.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_357b9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 861.000000 -592.000000) translate(0,12)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_357b9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 861.000000 -592.000000) translate(0,27)">35±3×2.5%/10.5kV,</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_357b9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 861.000000 -592.000000) translate(0,42)">5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_357b9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 861.000000 -592.000000) translate(0,57)">Ud=7.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_357b9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 861.000000 -592.000000) translate(0,72)">ONAN,YN  D11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_357e3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1277.000000 -537.000000) translate(0,12)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_357e3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1277.000000 -537.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_357e3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1277.000000 -537.000000) translate(0,42)">Ud=7.2%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_357e3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1277.000000 -537.000000) translate(0,57)">Y/d11,ONAN</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_357ed40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 299.500000 -905.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_357f920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1258.000000 -955.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_357fe80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 690.000000 -545.000000) translate(0,12)">10kVⅠ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_357fe80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 690.000000 -545.000000) translate(0,27)">母线电压</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_357fe80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 690.000000 -545.000000) translate(0,42)">互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3587450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -13.000000 -22.000000) translate(0,17)">永仁巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3588b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 138.000000 -32.500000) translate(0,17)">13638777384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3588b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 138.000000 -32.500000) translate(0,38)">13987885824</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_358ad00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 136.000000 -62.000000) translate(0,17)">6821167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f8cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 934.000000 -476.000000) translate(0,12)">1号主变</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e4720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 726.000000 768.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e5f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 742.000000 752.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e6c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 734.000000 783.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e7190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 734.000000 798.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e7410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 734.000000 812.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e7740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 505.000000 375.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e79b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 521.000000 359.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e7bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 513.000000 390.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e7e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 513.000000 405.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e8070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 513.000000 419.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e8490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1109.000000 684.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e90e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1098.000000 669.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e99a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1123.000000 654.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ea3a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1110.000000 411.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ea6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1099.000000 396.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ea8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1124.000000 381.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ead00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 669.000000 -11.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34eafc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 658.000000 -26.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34eb200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 683.000000 -41.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3578900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1502.000000 519.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3579560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1502.000000 534.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3581550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 820.000000 1095.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3581990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 809.000000 1080.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3581bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 834.000000 1065.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3581ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1430.000000 1103.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35822b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1419.000000 1088.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35824f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1444.000000 1073.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3582910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 826.000000 -3.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3582bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 815.000000 -18.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3582e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 840.000000 -33.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3583230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 992.000000 -8.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35834f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 981.000000 -23.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3583730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1006.000000 -38.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3583b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1295.000000 -10.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3583e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1284.000000 -25.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3584050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1309.000000 -40.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3584470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1473.000000 -5.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3584730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1462.000000 -20.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3584970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1487.000000 -35.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3584d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1650.000000 -6.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3585050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1639.000000 -21.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3585290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1664.000000 -36.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35856b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1511.000000 684.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3585970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1500.000000 669.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3585bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1525.000000 654.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3585ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1755.000000 365.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3586150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1771.000000 349.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3586390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1763.000000 380.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35865d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1763.000000 395.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3586810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1763.000000 409.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3586c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1506.000000 428.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3586ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1495.000000 413.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3587130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1520.000000 398.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_358b040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1085.000000 482.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_358b2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1085.000000 497.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YR_MH.YR_MH_066Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1367.000000 -21.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33957" ObjectName="EC-YR_MH.YR_MH_066Ld"/>
    <cge:TPSR_Ref TObjectID="33957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_MH.YR_MH_063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.000000 -27.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37742" ObjectName="EC-YR_MH.YR_MH_063Ld"/>
    <cge:TPSR_Ref TObjectID="37742"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1721.000000 -20.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_MH.YR_MH_067Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1543.000000 -21.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33956" ObjectName="EC-YR_MH.YR_MH_067Ld"/>
    <cge:TPSR_Ref TObjectID="33956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_MH.YR_MH_064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1063.000000 -27.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33958" ObjectName="EC-YR_MH.YR_MH_064Ld"/>
    <cge:TPSR_Ref TObjectID="33958"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 743.000000 -27.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-130473" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 313.500000 -930.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23913" ObjectName="DYN-YR_MH"/>
     <cge:Meas_Ref ObjectId="130473"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-139447">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1029.000000 -665.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37604" ObjectName="SW-YR_MH.YR_MH_3011SW"/>
     <cge:Meas_Ref ObjectId="139447"/>
    <cge:TPSR_Ref TObjectID="37604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139439">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.000000 -738.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25022" ObjectName="SW-YR_MH.YR_MH_3611SW"/>
     <cge:Meas_Ref ObjectId="139439"/>
    <cge:TPSR_Ref TObjectID="25022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139602">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1367.000000 -257.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25039" ObjectName="SW-YR_MH.YR_MH_0662SW"/>
     <cge:Meas_Ref ObjectId="139602"/>
    <cge:TPSR_Ref TObjectID="25039"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139601">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1367.000000 -146.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25040" ObjectName="SW-YR_MH.YR_MH_0666SW"/>
     <cge:Meas_Ref ObjectId="139601"/>
    <cge:TPSR_Ref TObjectID="25040"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139529">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.000000 -263.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25033" ObjectName="SW-YR_MH.YR_MH_0631SW"/>
     <cge:Meas_Ref ObjectId="139529"/>
    <cge:TPSR_Ref TObjectID="25033"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139530">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.000000 -152.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25034" ObjectName="SW-YR_MH.YR_MH_0636SW"/>
     <cge:Meas_Ref ObjectId="139530"/>
    <cge:TPSR_Ref TObjectID="25034"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139493">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 743.000000 -260.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25030" ObjectName="SW-YR_MH.YR_MH_0621SW"/>
     <cge:Meas_Ref ObjectId="139493"/>
    <cge:TPSR_Ref TObjectID="25030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139494">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 743.000000 -149.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25031" ObjectName="SW-YR_MH.YR_MH_0626SW"/>
     <cge:Meas_Ref ObjectId="139494"/>
    <cge:TPSR_Ref TObjectID="25031"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226947">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1721.000000 -256.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37616" ObjectName="SW-YR_MH.YR_MH_0682SW"/>
     <cge:Meas_Ref ObjectId="226947"/>
    <cge:TPSR_Ref TObjectID="37616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226945">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1721.000000 -145.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37615" ObjectName="SW-YR_MH.YR_MH_0686SW"/>
     <cge:Meas_Ref ObjectId="226945"/>
    <cge:TPSR_Ref TObjectID="37615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159152">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1543.000000 -254.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37613" ObjectName="SW-YR_MH.YR_MH_0672SW"/>
     <cge:Meas_Ref ObjectId="159152"/>
    <cge:TPSR_Ref TObjectID="37613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159153">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1543.000000 -143.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37612" ObjectName="SW-YR_MH.YR_MH_0676SW"/>
     <cge:Meas_Ref ObjectId="159153"/>
    <cge:TPSR_Ref TObjectID="37612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139565">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1063.000000 -263.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25036" ObjectName="SW-YR_MH.YR_MH_0641SW"/>
     <cge:Meas_Ref ObjectId="139565"/>
    <cge:TPSR_Ref TObjectID="25036"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139566">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1063.000000 -152.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25037" ObjectName="SW-YR_MH.YR_MH_0646SW"/>
     <cge:Meas_Ref ObjectId="139566"/>
    <cge:TPSR_Ref TObjectID="25037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139453">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1028.000000 -333.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25027" ObjectName="SW-YR_MH.YR_MH_0011SW"/>
     <cge:Meas_Ref ObjectId="139453"/>
    <cge:TPSR_Ref TObjectID="25027"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226822">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.000000 -835.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37594" ObjectName="SW-YR_MH.YR_MH_3616SW"/>
     <cge:Meas_Ref ObjectId="226822"/>
    <cge:TPSR_Ref TObjectID="37594"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226823">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 853.000000 -884.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37593" ObjectName="SW-YR_MH.YR_MH_36167SW"/>
     <cge:Meas_Ref ObjectId="226823"/>
    <cge:TPSR_Ref TObjectID="37593"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226824">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 854.000000 -939.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37592" ObjectName="SW-YR_MH.YR_MH_3619SW"/>
     <cge:Meas_Ref ObjectId="226824"/>
    <cge:TPSR_Ref TObjectID="37592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226860">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1498.000000 -741.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37600" ObjectName="SW-YR_MH.YR_MH_3641SW"/>
     <cge:Meas_Ref ObjectId="226860"/>
    <cge:TPSR_Ref TObjectID="37600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226861">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1498.000000 -838.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37598" ObjectName="SW-YR_MH.YR_MH_3646SW"/>
     <cge:Meas_Ref ObjectId="226861"/>
    <cge:TPSR_Ref TObjectID="37598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226862">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1444.000000 -887.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37599" ObjectName="SW-YR_MH.YR_MH_36467SW"/>
     <cge:Meas_Ref ObjectId="226862"/>
    <cge:TPSR_Ref TObjectID="37599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226863">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1445.000000 -942.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37597" ObjectName="SW-YR_MH.YR_MH_3649SW"/>
     <cge:Meas_Ref ObjectId="226863"/>
    <cge:TPSR_Ref TObjectID="37597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226899">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1113.000000 -734.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37602" ObjectName="SW-YR_MH.YR_MH_3901SW"/>
     <cge:Meas_Ref ObjectId="226899"/>
    <cge:TPSR_Ref TObjectID="37602"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226900">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1128.000000 -792.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37603" ObjectName="SW-YR_MH.YR_MH_39017SW"/>
     <cge:Meas_Ref ObjectId="226900"/>
    <cge:TPSR_Ref TObjectID="37603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226898">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1285.000000 -732.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37601" ObjectName="SW-YR_MH.YR_MH_3621SW"/>
     <cge:Meas_Ref ObjectId="226898"/>
    <cge:TPSR_Ref TObjectID="37601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226727">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1029.000000 -569.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25025" ObjectName="SW-YR_MH.YR_MH_3016SW"/>
     <cge:Meas_Ref ObjectId="226727"/>
    <cge:TPSR_Ref TObjectID="25025"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226744">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1443.000000 -667.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37607" ObjectName="SW-YR_MH.YR_MH_3021SW"/>
     <cge:Meas_Ref ObjectId="226744"/>
    <cge:TPSR_Ref TObjectID="37607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226746">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1442.000000 -335.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37609" ObjectName="SW-YR_MH.YR_MH_0022SW"/>
     <cge:Meas_Ref ObjectId="226746"/>
    <cge:TPSR_Ref TObjectID="37609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226745">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1443.000000 -571.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37606" ObjectName="SW-YR_MH.YR_MH_3026SW"/>
     <cge:Meas_Ref ObjectId="226745"/>
    <cge:TPSR_Ref TObjectID="37606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 603.000000 -253.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226982">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1145.000000 -265.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37618" ObjectName="SW-YR_MH.YR_MH_0121SW"/>
     <cge:Meas_Ref ObjectId="226982"/>
    <cge:TPSR_Ref TObjectID="37618"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226983">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1271.000000 -262.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37619" ObjectName="SW-YR_MH.YR_MH_0122SW"/>
     <cge:Meas_Ref ObjectId="226983"/>
    <cge:TPSR_Ref TObjectID="37619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139489">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 782.000000 -340.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25028" ObjectName="SW-YR_MH.YR_MH_0901SW"/>
     <cge:Meas_Ref ObjectId="139489"/>
    <cge:TPSR_Ref TObjectID="25028"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 786.000000 -417.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-226915">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1669.000000 -338.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37610" ObjectName="SW-YR_MH.YR_MH_0902SW"/>
     <cge:Meas_Ref ObjectId="226915"/>
    <cge:TPSR_Ref TObjectID="37610"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_342f800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1037,-434 1037,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="25041@1" ObjectIDZND0="25026@1" Pin0InfoVect0LinkObjId="SW-139450_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_352dbd0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1037,-434 1037,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34392e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1376,-211 1376,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25038@0" ObjectIDZND0="25040@1" Pin0InfoVect0LinkObjId="SW-139601_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139599_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1376,-211 1376,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3439540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1376,-262 1376,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25039@0" ObjectIDZND0="25038@1" Pin0InfoVect0LinkObjId="SW-139599_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139602_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1376,-262 1376,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3441d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-217 912,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25032@0" ObjectIDZND0="25034@1" Pin0InfoVect0LinkObjId="SW-139530_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139527_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="912,-217 912,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3441f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-268 912,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25033@0" ObjectIDZND0="25032@1" Pin0InfoVect0LinkObjId="SW-139527_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139529_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="912,-268 912,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3442920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-304 912,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25033@1" ObjectIDZND0="25020@0" Pin0InfoVect0LinkObjId="g_344a110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139529_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="912,-304 912,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3449c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,-214 752,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25029@0" ObjectIDZND0="25031@1" Pin0InfoVect0LinkObjId="SW-139494_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139491_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="752,-214 752,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3449eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,-265 752,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25030@0" ObjectIDZND0="25029@1" Pin0InfoVect0LinkObjId="SW-139491_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139493_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="752,-265 752,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_344a110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,-301 752,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25030@1" ObjectIDZND0="25020@0" Pin0InfoVect0LinkObjId="g_3442920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139493_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="752,-301 752,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3451440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1730,-210 1730,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37614@0" ObjectIDZND0="37615@1" Pin0InfoVect0LinkObjId="SW-226945_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226946_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1730,-210 1730,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34516a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1730,-261 1730,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37616@0" ObjectIDZND0="37614@1" Pin0InfoVect0LinkObjId="SW-226946_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226947_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1730,-261 1730,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3452000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1730,-297 1730,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37616@1" ObjectIDZND0="37621@0" Pin0InfoVect0LinkObjId="g_34c3500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226947_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1730,-297 1730,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34c1950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1552,-208 1552,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37611@0" ObjectIDZND0="37612@1" Pin0InfoVect0LinkObjId="SW-159153_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159151_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1552,-208 1552,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34c1bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1552,-259 1552,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37613@0" ObjectIDZND0="37611@1" Pin0InfoVect0LinkObjId="SW-159151_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159152_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1552,-259 1552,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34c2b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1518,-77 1518,-95 1552,-95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_34c1e10@0" ObjectIDZND0="33956@x" ObjectIDZND1="37612@x" Pin0InfoVect0LinkObjId="EC-YR_MH.YR_MH_067Ld_0" Pin0InfoVect1LinkObjId="SW-159153_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34c1e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1518,-77 1518,-95 1552,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34c3500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1552,-295 1552,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37613@1" ObjectIDZND0="37621@0" Pin0InfoVect0LinkObjId="g_3452000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159152_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1552,-295 1552,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34ca830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1072,-217 1072,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25035@0" ObjectIDZND0="25037@1" Pin0InfoVect0LinkObjId="SW-139566_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139563_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1072,-217 1072,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34caa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1072,-268 1072,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25036@0" ObjectIDZND0="25035@1" Pin0InfoVect0LinkObjId="SW-139563_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139565_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1072,-268 1072,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34cba20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1038,-76 1038,-94 1072,-94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_34cacf0@0" ObjectIDZND0="33958@x" ObjectIDZND1="25037@x" Pin0InfoVect0LinkObjId="EC-YR_MH.YR_MH_064Ld_0" Pin0InfoVect1LinkObjId="SW-139566_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34cacf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1038,-76 1038,-94 1072,-94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34cc3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1072,-304 1072,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25036@1" ObjectIDZND0="25020@0" Pin0InfoVect0LinkObjId="g_3442920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139565_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1072,-304 1072,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34cc640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1376,-298 1376,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25039@1" ObjectIDZND0="37621@0" Pin0InfoVect0LinkObjId="g_3452000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139602_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1376,-298 1376,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34cf0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1037,-391 1037,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25026@0" ObjectIDZND0="25027@1" Pin0InfoVect0LinkObjId="SW-139453_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1037,-391 1037,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34cf300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1037,-338 1037,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25027@0" ObjectIDZND0="25020@0" Pin0InfoVect0LinkObjId="g_3442920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139453_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1037,-338 1037,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34d5500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1344,-76 1344,-94 1378,-94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_34397a0@0" ObjectIDZND0="33957@x" ObjectIDZND1="25040@x" Pin0InfoVect0LinkObjId="EC-YR_MH.YR_MH_066Ld_0" Pin0InfoVect1LinkObjId="SW-139601_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34397a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1344,-76 1344,-94 1378,-94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34d6250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="874,-74 874,-92 912,-92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_34d56f0@0" ObjectIDZND0="37742@x" ObjectIDZND1="25034@x" Pin0InfoVect0LinkObjId="EC-YR_MH.YR_MH_063Ld_0" Pin0InfoVect1LinkObjId="SW-139530_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34d56f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="874,-74 874,-92 912,-92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34d64b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-889 894,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="37594@x" ObjectIDND1="37592@x" ObjectIDND2="g_3503f00@0" ObjectIDZND0="37593@1" Pin0InfoVect0LinkObjId="SW-226823_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-226822_0" Pin1InfoVect1LinkObjId="SW-226824_0" Pin1InfoVect2LinkObjId="g_3503f00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="916,-889 894,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34d6710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-743 916,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25022@0" ObjectIDZND0="25019@0" Pin0InfoVect0LinkObjId="g_34d7610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139439_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="916,-743 916,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34d7610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1038,-706 1038,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37604@1" ObjectIDZND0="25019@0" Pin0InfoVect0LinkObjId="g_34d6710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139447_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1038,-706 1038,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34f7ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-795 916,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37595@0" ObjectIDZND0="25022@1" Pin0InfoVect0LinkObjId="SW-139439_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226821_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="916,-795 916,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34fa920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-840 916,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37594@0" ObjectIDZND0="37595@1" Pin0InfoVect0LinkObjId="SW-226821_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226822_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="916,-840 916,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34fb410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-889 916,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="37593@x" ObjectIDND1="37592@x" ObjectIDND2="g_3503f00@0" ObjectIDZND0="37594@1" Pin0InfoVect0LinkObjId="SW-226822_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-226823_0" Pin1InfoVect1LinkObjId="SW-226824_0" Pin1InfoVect2LinkObjId="g_3503f00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="916,-889 916,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34fdba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="858,-889 838,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="37593@0" ObjectIDZND0="g_34fde00@0" Pin0InfoVect0LinkObjId="g_34fde00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226823_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="858,-889 838,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34ff120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-889 916,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="37593@x" ObjectIDND1="37594@x" ObjectIDZND0="37592@x" ObjectIDZND1="g_3503f00@0" ObjectIDZND2="38085@1" Pin0InfoVect0LinkObjId="SW-226824_0" Pin0InfoVect1LinkObjId="g_3503f00_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-226823_0" Pin1InfoVect1LinkObjId="SW-226822_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="916,-889 916,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35018b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-944 895,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="37593@x" ObjectIDND1="37594@x" ObjectIDND2="g_3503f00@0" ObjectIDZND0="37592@1" Pin0InfoVect0LinkObjId="SW-226824_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-226823_0" Pin1InfoVect1LinkObjId="SW-226822_0" Pin1InfoVect2LinkObjId="g_3503f00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="916,-944 895,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3502410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="859,-944 841,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="37592@0" ObjectIDZND0="g_3501b10@1" Pin0InfoVect0LinkObjId="g_3501b10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226824_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="859,-944 841,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3502670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="810,-944 799,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_3501b10@0" ObjectIDZND0="g_35028d0@0" Pin0InfoVect0LinkObjId="g_35028d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3501b10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="810,-944 799,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3502f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-967 950,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="37593@x" ObjectIDND1="37594@x" ObjectIDND2="37592@x" ObjectIDZND0="g_3503f00@0" Pin0InfoVect0LinkObjId="g_3503f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-226823_0" Pin1InfoVect1LinkObjId="SW-226822_0" Pin1InfoVect2LinkObjId="SW-226824_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="916,-967 950,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3503a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-993 916,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="38085@1" ObjectIDZND0="g_3503f00@0" ObjectIDZND1="37593@x" ObjectIDZND2="37594@x" Pin0InfoVect0LinkObjId="g_3503f00_0" Pin0InfoVect1LinkObjId="SW-226823_0" Pin0InfoVect2LinkObjId="SW-226822_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="916,-993 916,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3503ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-967 916,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3503f00@0" ObjectIDND1="38085@1" ObjectIDZND0="37593@x" ObjectIDZND1="37594@x" ObjectIDZND2="37592@x" Pin0InfoVect0LinkObjId="SW-226823_0" Pin0InfoVect1LinkObjId="SW-226822_0" Pin0InfoVect2LinkObjId="SW-226824_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3503f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="916,-967 916,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3507430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1507,-892 1485,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="37598@x" ObjectIDND1="37597@x" ObjectIDND2="g_3514290@0" ObjectIDZND0="37599@1" Pin0InfoVect0LinkObjId="SW-226862_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-226861_0" Pin1InfoVect1LinkObjId="SW-226863_0" Pin1InfoVect2LinkObjId="g_3514290_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1507,-892 1485,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3507690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1507,-746 1507,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37600@0" ObjectIDZND0="25019@0" Pin0InfoVect0LinkObjId="g_34d6710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1507,-746 1507,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3509c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1507,-798 1507,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37596@0" ObjectIDZND0="37600@1" Pin0InfoVect0LinkObjId="SW-226860_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226859_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1507,-798 1507,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_350c660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1507,-843 1507,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37598@0" ObjectIDZND0="37596@1" Pin0InfoVect0LinkObjId="SW-226859_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226861_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1507,-843 1507,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_350c8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1507,-892 1507,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="37599@x" ObjectIDND1="37597@x" ObjectIDND2="g_3514290@0" ObjectIDZND0="37598@1" Pin0InfoVect0LinkObjId="SW-226861_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-226862_0" Pin1InfoVect1LinkObjId="SW-226863_0" Pin1InfoVect2LinkObjId="g_3514290_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1507,-892 1507,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_350f050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1449,-892 1429,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="37599@0" ObjectIDZND0="g_350f2b0@0" Pin0InfoVect0LinkObjId="g_350f2b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226862_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1449,-892 1429,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_350fd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1507,-892 1507,-947 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="37598@x" ObjectIDND1="37599@x" ObjectIDZND0="37597@x" ObjectIDZND1="g_3514290@0" ObjectIDZND2="37840@1" Pin0InfoVect0LinkObjId="SW-226863_0" Pin0InfoVect1LinkObjId="g_3514290_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-226861_0" Pin1InfoVect1LinkObjId="SW-226862_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1507,-892 1507,-947 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35124d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1507,-947 1486,-947 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="37598@x" ObjectIDND1="37599@x" ObjectIDND2="g_3514290@0" ObjectIDZND0="37597@1" Pin0InfoVect0LinkObjId="SW-226863_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-226861_0" Pin1InfoVect1LinkObjId="SW-226862_0" Pin1InfoVect2LinkObjId="g_3514290_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1507,-947 1486,-947 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3513030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1450,-947 1432,-947 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="37597@0" ObjectIDZND0="g_3512730@1" Pin0InfoVect0LinkObjId="g_3512730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226863_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1450,-947 1432,-947 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3513290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-947 1390,-947 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_3512730@0" ObjectIDZND0="g_35134f0@0" Pin0InfoVect0LinkObjId="g_35134f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3512730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-947 1390,-947 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3513b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1507,-970 1541,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="37597@x" ObjectIDND1="37598@x" ObjectIDND2="37599@x" ObjectIDZND0="g_3514290@0" Pin0InfoVect0LinkObjId="g_3514290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-226863_0" Pin1InfoVect1LinkObjId="SW-226861_0" Pin1InfoVect2LinkObjId="SW-226862_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1507,-970 1541,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3513dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1507,-996 1507,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="37840@1" ObjectIDZND0="g_3514290@0" ObjectIDZND1="37597@x" ObjectIDZND2="37598@x" Pin0InfoVect0LinkObjId="g_3514290_0" Pin0InfoVect1LinkObjId="SW-226863_0" Pin0InfoVect2LinkObjId="SW-226861_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1507,-996 1507,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3514030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1507,-970 1507,-947 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3514290@0" ObjectIDND1="37840@1" ObjectIDZND0="37597@x" ObjectIDZND1="37598@x" ObjectIDZND2="37599@x" Pin0InfoVect0LinkObjId="SW-226863_0" Pin0InfoVect1LinkObjId="SW-226861_0" Pin0InfoVect2LinkObjId="SW-226862_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3514290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1507,-970 1507,-947 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3519980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1122,-721 1122,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25019@0" ObjectIDZND0="37602@0" Pin0InfoVect0LinkObjId="SW-226899_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34d6710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-721 1122,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_351c6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1169,-797 1181,-797 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="37603@1" ObjectIDZND0="g_351c940@0" Pin0InfoVect0LinkObjId="g_351c940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226900_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1169,-797 1181,-797 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_351feb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1122,-775 1122,-797 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="37602@1" ObjectIDZND0="37603@x" ObjectIDZND1="g_351e9e0@0" ObjectIDZND2="g_3523cc0@0" Pin0InfoVect0LinkObjId="SW-226900_0" Pin0InfoVect1LinkObjId="g_351e9e0_0" Pin0InfoVect2LinkObjId="g_3523cc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226899_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-775 1122,-797 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3520110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1122,-797 1133,-797 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="37602@x" ObjectIDND1="g_351e9e0@0" ObjectIDND2="g_3523cc0@0" ObjectIDZND0="37603@0" Pin0InfoVect0LinkObjId="SW-226900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-226899_0" Pin1InfoVect1LinkObjId="g_351e9e0_0" Pin1InfoVect2LinkObjId="g_3523cc0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-797 1133,-797 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3520c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1122,-797 1122,-843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="37602@x" ObjectIDND1="37603@x" ObjectIDZND0="g_351e9e0@0" ObjectIDZND1="g_3523cc0@0" Pin0InfoVect0LinkObjId="g_351e9e0_0" Pin0InfoVect1LinkObjId="g_3523cc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-226899_0" Pin1InfoVect1LinkObjId="SW-226900_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-797 1122,-843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3520e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1122,-843 1079,-843 1079,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="37602@x" ObjectIDND1="37603@x" ObjectIDND2="g_3523cc0@0" ObjectIDZND0="g_351e9e0@0" Pin0InfoVect0LinkObjId="g_351e9e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-226899_0" Pin1InfoVect1LinkObjId="SW-226900_0" Pin1InfoVect2LinkObjId="g_3523cc0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-843 1079,-843 1079,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3524540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,-899 1168,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_35210c0@0" ObjectIDZND0="g_3523cc0@0" Pin0InfoVect0LinkObjId="g_3523cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35210c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1168,-899 1168,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35247a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,-853 1168,-843 1122,-843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3523cc0@1" ObjectIDZND0="37602@x" ObjectIDZND1="37603@x" ObjectIDZND2="g_351e9e0@0" Pin0InfoVect0LinkObjId="SW-226899_0" Pin0InfoVect1LinkObjId="SW-226900_0" Pin0InfoVect2LinkObjId="g_351e9e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3523cc0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1168,-853 1168,-843 1122,-843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3527a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1294,-721 1294,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25019@0" ObjectIDZND0="37601@0" Pin0InfoVect0LinkObjId="SW-226898_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34d6710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1294,-721 1294,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3527ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1294,-773 1294,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="37601@1" ObjectIDZND0="g_3524a00@1" Pin0InfoVect0LinkObjId="g_3524a00_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226898_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1294,-773 1294,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3527f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1294,-816 1294,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3524a00@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_35028d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3524a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1294,-816 1294,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352a480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1038,-652 1038,-670 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25024@1" ObjectIDZND0="37604@0" Pin0InfoVect0LinkObjId="SW-139447_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139443_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1038,-652 1038,-670 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352cee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1038,-625 1038,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25024@0" ObjectIDZND0="25025@1" Pin0InfoVect0LinkObjId="SW-226727_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139443_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1038,-625 1038,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352d710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1038,-562 1077,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="25025@x" ObjectIDND1="25041@x" ObjectIDZND0="g_352de30@0" Pin0InfoVect0LinkObjId="g_352de30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-226727_0" Pin1InfoVect1LinkObjId="g_352dbd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1038,-562 1077,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352d970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1038,-574 1038,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="25025@0" ObjectIDZND0="g_352de30@0" ObjectIDZND1="25041@x" Pin0InfoVect0LinkObjId="g_352de30_0" Pin0InfoVect1LinkObjId="g_352dbd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226727_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1038,-574 1038,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352dbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1038,-562 1038,-514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="25025@x" ObjectIDND1="g_352de30@0" ObjectIDZND0="25041@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-226727_0" Pin1InfoVect1LinkObjId="g_352de30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1038,-562 1038,-514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35376b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1451,-436 1451,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="37620@1" ObjectIDZND0="37608@1" Pin0InfoVect0LinkObjId="SW-226743_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353e190_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1451,-436 1451,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_353a110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1451,-393 1451,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37608@0" ObjectIDZND0="37609@1" Pin0InfoVect0LinkObjId="SW-226746_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226743_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1451,-393 1451,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_353a370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1452,-708 1452,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37607@1" ObjectIDZND0="25019@0" Pin0InfoVect0LinkObjId="g_34d6710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226744_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-708 1452,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_353b2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1452,-654 1452,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37605@1" ObjectIDZND0="37607@0" Pin0InfoVect0LinkObjId="SW-226744_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226742_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-654 1452,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_353da70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1452,-627 1452,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37605@0" ObjectIDZND0="37606@1" Pin0InfoVect0LinkObjId="SW-226745_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226742_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-627 1452,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_353dcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1452,-564 1491,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="37606@x" ObjectIDND1="37620@x" ObjectIDZND0="g_353e3f0@0" Pin0InfoVect0LinkObjId="g_353e3f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-226745_0" Pin1InfoVect1LinkObjId="g_353e190_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-564 1491,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_353df30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1452,-581 1452,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="37606@0" ObjectIDZND0="g_353e3f0@0" ObjectIDZND1="37620@x" Pin0InfoVect0LinkObjId="g_353e3f0_0" Pin0InfoVect1LinkObjId="g_353e190_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226745_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-581 1452,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_353e190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1452,-564 1452,-516 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="37606@x" ObjectIDND1="g_353e3f0@0" ObjectIDZND0="37620@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-226745_0" Pin1InfoVect1LinkObjId="g_353e3f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-564 1452,-516 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3540920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1451,-340 1451,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37609@0" ObjectIDZND0="37621@0" Pin0InfoVect0LinkObjId="g_3452000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226746_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1451,-340 1451,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3540b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,-154 752,-54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" ObjectIDND0="25031@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_35028d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139494_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="752,-154 752,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3541670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1072,-94 1072,-54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_34cacf0@0" ObjectIDND1="25037@x" ObjectIDZND0="33958@0" Pin0InfoVect0LinkObjId="EC-YR_MH.YR_MH_064Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_34cacf0_0" Pin1InfoVect1LinkObjId="SW-139566_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1072,-94 1072,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35418d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1072,-157 1072,-94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="25037@0" ObjectIDZND0="g_34cacf0@0" ObjectIDZND1="33958@x" Pin0InfoVect0LinkObjId="g_34cacf0_0" Pin0InfoVect1LinkObjId="EC-YR_MH.YR_MH_064Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139566_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1072,-157 1072,-94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3542d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-54 912,-92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="37742@0" ObjectIDZND0="g_34d56f0@0" ObjectIDZND1="25034@x" Pin0InfoVect0LinkObjId="g_34d56f0_0" Pin0InfoVect1LinkObjId="SW-139530_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YR_MH.YR_MH_063Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="912,-54 912,-92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3542f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-157 912,-92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="25034@0" ObjectIDZND0="g_34d56f0@0" ObjectIDZND1="37742@x" Pin0InfoVect0LinkObjId="g_34d56f0_0" Pin0InfoVect1LinkObjId="EC-YR_MH.YR_MH_063Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="912,-157 912,-92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35460e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,-323 608,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25020@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_35028d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3442920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="608,-323 608,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3549550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,-254 608,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_35028d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35028d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="608,-254 608,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3551a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1154,-323 1154,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25020@0" ObjectIDZND0="37618@1" Pin0InfoVect0LinkObjId="SW-226982_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3442920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1154,-323 1154,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3551c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1154,-270 1154,-249 1205,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37618@0" ObjectIDZND0="37617@1" Pin0InfoVect0LinkObjId="SW-226981_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226982_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1154,-270 1154,-249 1205,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35546d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1280,-321 1280,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="37621@0" ObjectIDZND0="37619@1" Pin0InfoVect0LinkObjId="SW-226983_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3452000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1280,-321 1280,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3554930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1280,-267 1280,-249 1232,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37619@0" ObjectIDZND0="37617@0" Pin0InfoVect0LinkObjId="SW-226981_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226983_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1280,-267 1280,-249 1232,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3555d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1376,-48 1376,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="33957@0" ObjectIDZND0="25040@x" ObjectIDZND1="g_34397a0@0" Pin0InfoVect0LinkObjId="SW-139601_0" Pin0InfoVect1LinkObjId="g_34397a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YR_MH.YR_MH_066Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1376,-48 1376,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3555f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1376,-151 1376,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="25040@0" ObjectIDZND0="33957@x" ObjectIDZND1="g_34397a0@0" Pin0InfoVect0LinkObjId="EC-YR_MH.YR_MH_066Ld_0" Pin0InfoVect1LinkObjId="g_34397a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139601_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1376,-151 1376,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35561a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1730,-150 1730,-47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" ObjectIDND0="37615@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_35028d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226945_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1730,-150 1730,-47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3557e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1552,-49 1552,-95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="33956@0" ObjectIDZND0="37612@x" ObjectIDZND1="g_34c1e10@0" Pin0InfoVect0LinkObjId="SW-159153_0" Pin0InfoVect1LinkObjId="g_34c1e10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YR_MH.YR_MH_067Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1552,-49 1552,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3558060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1552,-148 1552,-95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="37612@0" ObjectIDZND0="33956@x" ObjectIDZND1="g_34c1e10@0" Pin0InfoVect0LinkObjId="EC-YR_MH.YR_MH_067Ld_0" Pin0InfoVect1LinkObjId="g_34c1e10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159153_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1552,-148 1552,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_355c920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,-323 791,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25020@0" ObjectIDZND0="25028@0" Pin0InfoVect0LinkObjId="SW-139489_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3442920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="791,-323 791,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_355cb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,-401 764,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="25028@x" ObjectIDND1="0@x" ObjectIDZND0="g_355cde0@0" Pin0InfoVect0LinkObjId="g_355cde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-139489_0" Pin1InfoVect1LinkObjId="g_35028d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="791,-401 764,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_355e420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,-381 791,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="25028@1" ObjectIDZND0="g_355cde0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_355cde0_0" Pin0InfoVect1LinkObjId="g_35028d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139489_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="791,-381 791,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3561830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,-418 791,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="25028@x" ObjectIDZND1="g_355cde0@0" Pin0InfoVect0LinkObjId="SW-139489_0" Pin0InfoVect1LinkObjId="g_355cde0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35028d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="791,-418 791,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35644e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,-470 791,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_3561a90@0" Pin0InfoVect0LinkObjId="g_3561a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35028d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="791,-470 791,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3567890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1678,-399 1651,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="37610@x" ObjectIDND1="g_356a080@0" ObjectIDZND0="g_3567af0@0" Pin0InfoVect0LinkObjId="g_3567af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-226915_0" Pin1InfoVect1LinkObjId="g_356a080_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1678,-399 1651,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35688a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1678,-379 1678,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="37610@1" ObjectIDZND0="g_3567af0@0" ObjectIDZND1="g_356a080@0" Pin0InfoVect0LinkObjId="g_3567af0_0" Pin0InfoVect1LinkObjId="g_356a080_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226915_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1678,-379 1678,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3569390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1678,-343 1678,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37610@0" ObjectIDZND0="37621@0" Pin0InfoVect0LinkObjId="g_3452000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226915_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1678,-343 1678,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3569bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1679,-465 1679,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_356a900@0" ObjectIDZND0="g_356a080@0" Pin0InfoVect0LinkObjId="g_356a080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_356a900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1679,-465 1679,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3569e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1679,-418 1679,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_356a080@1" ObjectIDZND0="37610@x" ObjectIDZND1="g_3567af0@0" Pin0InfoVect0LinkObjId="SW-226915_0" Pin0InfoVect1LinkObjId="g_3567af0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_356a080_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1679,-418 1679,-399 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YR_MH"/>
</svg>