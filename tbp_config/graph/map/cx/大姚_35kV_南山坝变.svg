<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-229" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-752 -1271 2240 1203">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape50">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="42" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="11" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="36" x2="21" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="47" x2="28" y1="60" y2="60"/>
    <polyline points="29,89 31,89 33,88 34,88 36,87 37,86 39,85 40,83 41,81 41,80 42,78 42,76 42,74 41,72 41,71 40,69 39,68 37,66 36,65 34,64 33,64 31,63 29,63 27,63 25,64 24,64 22,65 21,66 19,68 18,69 17,71 17,72 16,74 16,76 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="16" x2="28" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="29" x2="29" y1="89" y2="97"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="55" y2="47"/>
    <polyline arcFlag="1" points="11,15 10,15 9,15 9,15 8,15 8,16 7,16 7,17 6,17 6,18 6,18 6,19 5,20 5,20 5,21 6,22 6,22 6,23 6,23 7,24 7,24 8,25 8,25 9,25 9,26 10,26 11,26 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,25 10,25 9,25 9,25 8,26 8,26 7,26 7,27 6,27 6,28 6,29 6,29 5,30 5,31 5,31 6,32 6,33 6,33 6,34 7,34 7,35 8,35 8,35 9,36 9,36 10,36 11,36 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,36 10,36 9,36 9,37 8,37 8,37 7,38 7,38 6,39 6,39 6,40 6,40 5,41 5,42 5,42 6,43 6,44 6,44 6,45 7,45 7,46 8,46 8,47 9,47 9,47 10,47 11,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="76" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="2" y2="2"/>
    <rect height="23" stroke-width="0.369608" width="12" x="41" y="29"/>
    <rect height="23" stroke-width="0.369608" width="12" x="22" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="28" x2="11" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="45" x2="45" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="12" x2="12" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="36" x2="21" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="48" x2="45" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="51" x2="43" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="41" x2="53" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="47" y1="21" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="47" x2="47" y1="60" y2="35"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape56_0">
    <circle cx="16" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,43 41,43 41,72 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="57" y2="99"/>
    <polyline DF8003:Layer="PUBLIC" points="16,84 22,71 9,71 16,84 16,83 16,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="35" x2="47" y1="72" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="45" x2="37" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="39" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="16" y1="54" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="43" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="43" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="43" y2="38"/>
   </symbol>
   <symbol id="transformer2:shape56_1">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="14" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="20" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="16" y1="20" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1.22798"/>
    <polyline points="58,100 64,100 " stroke-width="1.22798"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1.22798"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape77_0">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,61 6,61 6,32 " stroke-width="1"/>
    <circle cx="31" cy="64" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="19" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="49" y2="31"/>
    <polyline DF8003:Layer="PUBLIC" points="31,18 25,31 37,31 31,18 31,19 31,18 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="31" y1="56" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="61" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="61" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="61" y2="56"/>
   </symbol>
   <symbol id="transformer2:shape77_1">
    <circle cx="31" cy="86" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="31" y1="90" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="31" y1="90" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="26" y1="90" y2="90"/>
   </symbol>
   <symbol id="voltageTransformer:shape82">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="23" x2="23" y1="54" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="38" x2="50" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="45" x2="43" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="47" x2="41" y1="5" y2="5"/>
    <polyline points="44,17 44,9 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="33,28 44,28 44,17 32,17 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="29" x2="32" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="32" x2="32" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="34" x2="32" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="24" x2="18" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.755439" x1="22" x2="24" y1="31" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7538" x1="20" x2="18" y1="31" y2="28"/>
    <circle cx="22" cy="29" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="31" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="19" x2="22" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="22" x2="22" y1="17" y2="15"/>
    <ellipse cx="21" cy="19" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="24" x2="22" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="27" y1="31" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="29" x2="32" y1="30" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="32" x2="32" y1="28" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="34" x2="32" y1="30" y2="28"/>
    <ellipse cx="31" cy="19" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="32" cy="27" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="31" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="16" x2="16" y1="54" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="15" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="40" x2="40" y1="54" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="33" x2="33" y1="54" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="33" y1="48" y2="48"/>
   </symbol>
   <symbol id="voltageTransformer:shape65">
    <ellipse cx="19" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="46" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="9" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="8" x2="8" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="11" x2="8" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="5" x2="8" y1="18" y2="20"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="5" x2="9" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="5" x2="9" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="9" x2="9" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="41" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="30" y2="38"/>
    <rect height="13" stroke-width="1" width="7" x="32" y="17"/>
    <ellipse cx="8" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <polyline points="27,8 35,8 35,18 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="38" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="40" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="18" y2="20"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2139a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_213a450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_213ae00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_213bae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_213cce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_213d8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_213e150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_213eb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_213f3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_213fd70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_213fd70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2141800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2141800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2142810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21444a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_21450f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2145fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21466b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2147f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2148770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2148d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2149750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_214a8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_214b250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_214bd40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_214c700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_214dbf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_214e710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_214f760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21503b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_215e7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2151960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_21525a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1d84620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1213" width="2250" x="-757" y="-1276"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="1486" x2="1486" y1="-608" y2="-591"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-183568">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 734.241796 -921.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27903" ObjectName="SW-DY_NSB.DY_NSB_331BK"/>
     <cge:Meas_Ref ObjectId="183568"/>
    <cge:TPSR_Ref TObjectID="27903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183605">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 735.241796 -579.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27913" ObjectName="SW-DY_NSB.DY_NSB_001BK"/>
     <cge:Meas_Ref ObjectId="183605"/>
    <cge:TPSR_Ref TObjectID="27913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183777">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 280.241796 -441.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27942" ObjectName="SW-DY_NSB.DY_NSB_051BK"/>
     <cge:Meas_Ref ObjectId="183777"/>
    <cge:TPSR_Ref TObjectID="27942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183723">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 824.241796 -434.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27932" ObjectName="SW-DY_NSB.DY_NSB_033BK"/>
     <cge:Meas_Ref ObjectId="183723"/>
    <cge:TPSR_Ref TObjectID="27932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183696">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1031.241796 -436.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27927" ObjectName="SW-DY_NSB.DY_NSB_032BK"/>
     <cge:Meas_Ref ObjectId="183696"/>
    <cge:TPSR_Ref TObjectID="27927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183675">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1223.241796 -462.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27920" ObjectName="SW-DY_NSB.DY_NSB_031BK"/>
     <cge:Meas_Ref ObjectId="183675"/>
    <cge:TPSR_Ref TObjectID="27920"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183831">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -91.758204 -441.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27952" ObjectName="SW-DY_NSB.DY_NSB_053BK"/>
     <cge:Meas_Ref ObjectId="183831"/>
    <cge:TPSR_Ref TObjectID="27952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183804">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 88.241796 -443.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27947" ObjectName="SW-DY_NSB.DY_NSB_052BK"/>
     <cge:Meas_Ref ObjectId="183804"/>
    <cge:TPSR_Ref TObjectID="27947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183858">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -297.758204 -465.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27957" ObjectName="SW-DY_NSB.DY_NSB_054BK"/>
     <cge:Meas_Ref ObjectId="183858"/>
    <cge:TPSR_Ref TObjectID="27957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183750">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 605.241796 -438.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27937" ObjectName="SW-DY_NSB.DY_NSB_034BK"/>
     <cge:Meas_Ref ObjectId="183750"/>
    <cge:TPSR_Ref TObjectID="27937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183879">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 311.241796 -584.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27964" ObjectName="SW-DY_NSB.DY_NSB_012BK"/>
     <cge:Meas_Ref ObjectId="183879"/>
    <cge:TPSR_Ref TObjectID="27964"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_18f7820">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 762.000000 -1087.000000)" xlink:href="#voltageTransformer:shape82"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_180a0c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1020.000000 -692.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_175a0c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 78.000000 -690.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_NSB" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_dacangnanTnsb" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="743,-1195 743,-1224 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34315" ObjectName="AC-35kV.LN_dacangnanTnsb"/>
    <cge:TPSR_Ref TObjectID="34315_SS-229"/></metadata>
   <polyline fill="none" opacity="0" points="743,-1195 743,-1224 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-DY_NSB.051Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.761290 -212.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34229" ObjectName="EC-DY_NSB.051Ld"/>
    <cge:TPSR_Ref TObjectID="34229"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_NSB.033Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 823.761290 -205.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34233" ObjectName="EC-DY_NSB.033Ld"/>
    <cge:TPSR_Ref TObjectID="34233"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_NSB.032Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1030.761290 -207.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34232" ObjectName="EC-DY_NSB.032Ld"/>
    <cge:TPSR_Ref TObjectID="34232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_NSB.053Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -91.238710 -212.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34231" ObjectName="EC-DY_NSB.053Ld"/>
    <cge:TPSR_Ref TObjectID="34231"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_NSB.052Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 87.761290 -214.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34230" ObjectName="EC-DY_NSB.052Ld"/>
    <cge:TPSR_Ref TObjectID="34230"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_NSB.034Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 604.761290 -209.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34234" ObjectName="EC-DY_NSB.034Ld"/>
    <cge:TPSR_Ref TObjectID="34234"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1841290" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 624.000000 -1031.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18f5200" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 625.000000 -967.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_191ad20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 624.000000 -905.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17cdd10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 625.000000 -753.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18bb2f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 331.000000 -322.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_186b610" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 875.000000 -315.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18c65a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1082.000000 -317.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1805690" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1322.000000 -192.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1806320" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1322.000000 -427.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1806f70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1322.000000 -307.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_184ecc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -40.000000 -322.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_174aba0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 139.000000 -324.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17e9b40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -199.000000 -195.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17ea830" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -199.000000 -430.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17eb520" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -199.000000 -310.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17829c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 656.000000 -319.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1969260" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 366.000000 -781.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_183a340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="689,-1037 742,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="27906@1" ObjectIDZND0="27904@x" ObjectIDZND1="0@x" ObjectIDZND2="g_18f7820@0" Pin0InfoVect0LinkObjId="SW-183570_0" Pin0InfoVect1LinkObjId="g_18f7820_0" Pin0InfoVect2LinkObjId="g_18f7820_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183572_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="689,-1037 742,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18db260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="581,-1139 581,-1158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_18f7820_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18f7820_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="581,-1139 581,-1158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18f6d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-658 744,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="27971@0" ObjectIDZND0="27915@0" Pin0InfoVect0LinkObjId="SW-183607_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1963560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="744,-658 744,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18f7630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-1037 743,-1023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="27906@x" ObjectIDND1="0@x" ObjectIDND2="g_18f7820@0" ObjectIDZND0="27904@1" Pin0InfoVect0LinkObjId="SW-183570_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-183572_0" Pin1InfoVect1LinkObjId="g_18f7820_0" Pin1InfoVect2LinkObjId="g_18f7820_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="743,-1037 743,-1023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18881e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="767,-1134 743,-1134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_18f7820@0" ObjectIDZND0="g_18dc4c0@0" ObjectIDZND1="34315@1" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_18dc4c0_0" Pin0InfoVect1LinkObjId="g_18a2f50_1" Pin0InfoVect2LinkObjId="g_18f7820_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18f7820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="767,-1134 743,-1134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18a2480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-1171 712,-1161 743,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_18dc4c0@0" ObjectIDZND0="g_18f7820@0" ObjectIDZND1="0@x" ObjectIDZND2="27906@x" Pin0InfoVect0LinkObjId="g_18f7820_0" Pin0InfoVect1LinkObjId="g_18f7820_0" Pin0InfoVect2LinkObjId="SW-183572_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18dc4c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="712,-1171 712,-1161 743,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18a2d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-1134 743,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_18f7820@0" ObjectIDND1="0@x" ObjectIDND2="27906@x" ObjectIDZND0="g_18dc4c0@0" ObjectIDZND1="34315@1" Pin0InfoVect0LinkObjId="g_18dc4c0_0" Pin0InfoVect1LinkObjId="g_18a2f50_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_18f7820_0" Pin1InfoVect1LinkObjId="g_18f7820_0" Pin1InfoVect2LinkObjId="SW-183572_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="743,-1134 743,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18a2f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-1161 743,-1196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_18dc4c0@0" ObjectIDND1="g_18f7820@0" ObjectIDND2="0@x" ObjectIDZND0="34315@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_18dc4c0_0" Pin1InfoVect1LinkObjId="g_18f7820_0" Pin1InfoVect2LinkObjId="g_18f7820_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="743,-1161 743,-1196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18f5010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="654,-973 643,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27907@0" ObjectIDZND0="g_18f5200@0" Pin0InfoVect0LinkObjId="g_18f5200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183573_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="654,-973 643,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18f59b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="690,-973 744,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27907@1" ObjectIDZND0="27904@x" ObjectIDZND1="27903@x" Pin0InfoVect0LinkObjId="SW-183570_0" Pin0InfoVect1LinkObjId="SW-183568_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183573_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="690,-973 744,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18f5ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="653,-1037 642,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27906@0" ObjectIDZND0="g_1841290@0" Pin0InfoVect0LinkObjId="g_1841290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183572_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="653,-1037 642,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_191a750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-987 743,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27904@0" ObjectIDZND0="27907@x" ObjectIDZND1="27903@x" Pin0InfoVect0LinkObjId="SW-183573_0" Pin0InfoVect1LinkObjId="SW-183568_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="743,-987 743,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_191a940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-973 743,-956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="27907@x" ObjectIDND1="27904@x" ObjectIDZND0="27903@1" Pin0InfoVect0LinkObjId="SW-183568_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183573_0" Pin1InfoVect1LinkObjId="SW-183570_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="743,-973 743,-956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_191ab30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="653,-911 642,-911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27908@0" ObjectIDZND0="g_191ad20@0" Pin0InfoVect0LinkObjId="g_191ad20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183574_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="653,-911 642,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18e4d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="689,-911 743,-911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="27908@1" ObjectIDZND0="27903@x" ObjectIDZND1="27905@x" Pin0InfoVect0LinkObjId="SW-183568_0" Pin0InfoVect1LinkObjId="SW-183571_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183574_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="689,-911 743,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1914020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-929 743,-911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27903@0" ObjectIDZND0="27908@x" ObjectIDZND1="27905@x" Pin0InfoVect0LinkObjId="SW-183574_0" Pin0InfoVect1LinkObjId="SW-183571_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183568_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="743,-929 743,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1914210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-911 743,-898 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="27908@x" ObjectIDND1="27903@x" ObjectIDZND0="27905@1" Pin0InfoVect0LinkObjId="SW-183571_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183574_0" Pin1InfoVect1LinkObjId="SW-183568_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="743,-911 743,-898 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18e8e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-626 744,-614 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27915@1" ObjectIDZND0="27913@1" Pin0InfoVect0LinkObjId="SW-183605_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183607_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="744,-626 744,-614 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18ea640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-587 744,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27913@0" ObjectIDZND0="27914@1" Pin0InfoVect0LinkObjId="SW-183607_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183605_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="744,-587 744,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17cdb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="654,-759 643,-759 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27912@0" ObjectIDZND0="g_17cdd10@0" Pin0InfoVect0LinkObjId="g_17cdd10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183604_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="654,-759 643,-759 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17ce4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="690,-759 743,-759 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="27912@1" ObjectIDZND0="27911@x" ObjectIDZND1="27971@x" Pin0InfoVect0LinkObjId="SW-183603_0" Pin0InfoVect1LinkObjId="g_1963560_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183604_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="690,-759 743,-759 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17ce6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="581,-1094 581,-1081 744,-1081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="27906@x" ObjectIDZND1="27904@x" ObjectIDZND2="g_18f7820@0" Pin0InfoVect0LinkObjId="SW-183572_0" Pin0InfoVect1LinkObjId="SW-183570_0" Pin0InfoVect2LinkObjId="g_18f7820_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18f7820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="581,-1094 581,-1081 744,-1081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1846a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="289,-488 289,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27943@1" ObjectIDZND0="27942@1" Pin0InfoVect0LinkObjId="SW-183777_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183779_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="289,-488 289,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1848880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="289,-449 289,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27942@0" ObjectIDZND0="27944@1" Pin0InfoVect0LinkObjId="SW-183779_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183777_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="289,-449 289,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18796e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-263 289,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1878b70@0" ObjectIDZND0="27945@x" ObjectIDZND1="34229@x" Pin0InfoVect0LinkObjId="SW-183780_0" Pin0InfoVect1LinkObjId="EC-DY_NSB.051Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1878b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="320,-263 289,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1879900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="289,-277 289,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27945@0" ObjectIDZND0="g_1878b70@0" ObjectIDZND1="34229@x" Pin0InfoVect0LinkObjId="g_1878b70_0" Pin0InfoVect1LinkObjId="EC-DY_NSB.051Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="289,-277 289,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1879b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="289,-263 289,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1878b70@0" ObjectIDND1="27945@x" ObjectIDZND0="34229@0" Pin0InfoVect0LinkObjId="EC-DY_NSB.051Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1878b70_0" Pin1InfoVect1LinkObjId="SW-183780_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="289,-263 289,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1879d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="289,-401 337,-401 337,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="27944@x" ObjectIDND1="g_1848aa0@0" ObjectIDZND0="27946@1" Pin0InfoVect0LinkObjId="SW-183781_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183779_0" Pin1InfoVect1LinkObjId="g_1848aa0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="289,-401 337,-401 337,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1879f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="289,-420 289,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="27944@0" ObjectIDZND0="27946@x" ObjectIDZND1="g_1848aa0@0" Pin0InfoVect0LinkObjId="SW-183781_0" Pin0InfoVect1LinkObjId="g_1848aa0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183779_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="289,-420 289,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_187a180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="289,-401 289,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="27946@x" ObjectIDND1="27944@x" ObjectIDZND0="g_1848aa0@0" Pin0InfoVect0LinkObjId="g_1848aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183781_0" Pin1InfoVect1LinkObjId="SW-183779_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="289,-401 289,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18bb0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="337,-355 337,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27946@0" ObjectIDZND0="g_18bb2f0@0" Pin0InfoVect0LinkObjId="g_18bb2f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183781_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="337,-355 337,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18bbbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="289,-331 289,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1848aa0@1" ObjectIDZND0="27945@1" Pin0InfoVect0LinkObjId="SW-183780_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1848aa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="289,-331 289,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1794a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="289,-505 289,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27943@0" ObjectIDZND0="27901@0" Pin0InfoVect0LinkObjId="g_184f970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183779_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="289,-505 289,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18bf010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="833,-481 833,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27933@1" ObjectIDZND0="27932@1" Pin0InfoVect0LinkObjId="SW-183723_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183725_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="833,-481 833,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18b7cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="833,-442 833,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27932@0" ObjectIDZND0="27934@1" Pin0InfoVect0LinkObjId="SW-183725_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183723_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="833,-442 833,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1861820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="864,-256 833,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1860540@0" ObjectIDZND0="27935@x" ObjectIDZND1="34233@x" Pin0InfoVect0LinkObjId="SW-183726_0" Pin0InfoVect1LinkObjId="EC-DY_NSB.033Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1860540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="864,-256 833,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1861a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="833,-270 833,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27935@0" ObjectIDZND0="g_1860540@0" ObjectIDZND1="34233@x" Pin0InfoVect0LinkObjId="g_1860540_0" Pin0InfoVect1LinkObjId="EC-DY_NSB.033Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183726_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="833,-270 833,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1861c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="833,-256 833,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1860540@0" ObjectIDND1="27935@x" ObjectIDZND0="34233@0" Pin0InfoVect0LinkObjId="EC-DY_NSB.033Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1860540_0" Pin1InfoVect1LinkObjId="SW-183726_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="833,-256 833,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1861df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="833,-394 881,-394 881,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="27934@x" ObjectIDND1="g_18b7f30@0" ObjectIDZND0="27936@1" Pin0InfoVect0LinkObjId="SW-183727_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183725_0" Pin1InfoVect1LinkObjId="g_18b7f30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="833,-394 881,-394 881,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1868b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="833,-413 833,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="27934@0" ObjectIDZND0="27936@x" ObjectIDZND1="g_18b7f30@0" Pin0InfoVect0LinkObjId="SW-183727_0" Pin0InfoVect1LinkObjId="g_18b7f30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183725_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="833,-413 833,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1868d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="833,-394 833,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="27936@x" ObjectIDND1="27934@x" ObjectIDZND0="g_18b7f30@0" Pin0InfoVect0LinkObjId="g_18b7f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183727_0" Pin1InfoVect1LinkObjId="SW-183725_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="833,-394 833,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_186b3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="881,-348 881,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27936@0" ObjectIDZND0="g_186b610@0" Pin0InfoVect0LinkObjId="g_186b610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183727_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="881,-348 881,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18d0e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="833,-324 833,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_18b7f30@1" ObjectIDZND0="27935@1" Pin0InfoVect0LinkObjId="SW-183726_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18b7f30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="833,-324 833,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18d5980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-483 1040,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27928@1" ObjectIDZND0="27927@1" Pin0InfoVect0LinkObjId="SW-183696_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183698_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-483 1040,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18d7850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-444 1040,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27927@0" ObjectIDZND0="27929@1" Pin0InfoVect0LinkObjId="SW-183698_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183696_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-444 1040,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1876340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1071,-258 1040,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1875690@0" ObjectIDZND0="27930@x" ObjectIDZND1="34232@x" Pin0InfoVect0LinkObjId="SW-183699_0" Pin0InfoVect1LinkObjId="EC-DY_NSB.032Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1875690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1071,-258 1040,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18765a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-272 1040,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27930@0" ObjectIDZND0="g_1875690@0" ObjectIDZND1="34232@x" Pin0InfoVect0LinkObjId="g_1875690_0" Pin0InfoVect1LinkObjId="EC-DY_NSB.032Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183699_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-272 1040,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18c3630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-258 1040,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1875690@0" ObjectIDND1="27930@x" ObjectIDZND0="34232@0" Pin0InfoVect0LinkObjId="EC-DY_NSB.032Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1875690_0" Pin1InfoVect1LinkObjId="SW-183699_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-258 1040,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18c3890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-396 1088,-396 1088,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="27929@x" ObjectIDND1="g_18d7ab0@0" ObjectIDZND0="27931@1" Pin0InfoVect0LinkObjId="SW-183700_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183698_0" Pin1InfoVect1LinkObjId="g_18d7ab0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-396 1088,-396 1088,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18c3af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-415 1040,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="27929@0" ObjectIDZND0="27931@x" ObjectIDZND1="g_18d7ab0@0" Pin0InfoVect0LinkObjId="SW-183700_0" Pin0InfoVect1LinkObjId="g_18d7ab0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183698_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-415 1040,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18c3d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-396 1040,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="27931@x" ObjectIDND1="27929@x" ObjectIDZND0="g_18d7ab0@0" Pin0InfoVect0LinkObjId="g_18d7ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183700_0" Pin1InfoVect1LinkObjId="SW-183698_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-396 1040,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18c6340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1088,-350 1088,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27931@0" ObjectIDZND0="g_18c65a0@0" Pin0InfoVect0LinkObjId="g_18c65a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1088,-350 1088,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_187b2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-326 1040,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_18d7ab0@1" ObjectIDZND0="27930@1" Pin0InfoVect0LinkObjId="SW-183699_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18d7ab0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-326 1040,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18fba30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1232,-509 1232,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27921@1" ObjectIDZND0="27920@1" Pin0InfoVect0LinkObjId="SW-183675_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183677_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1232,-509 1232,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_189b610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1232,-470 1232,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27920@0" ObjectIDZND0="27922@1" Pin0InfoVect0LinkObjId="SW-183677_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183675_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1232,-470 1232,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17f2ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1232,-371 1232,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_189b890@1" ObjectIDZND0="27923@1" Pin0InfoVect0LinkObjId="SW-183678_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_189b890_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1232,-371 1232,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17f3120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1232,-433 1280,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="27922@x" ObjectIDND1="g_189b890@0" ObjectIDZND0="27924@0" Pin0InfoVect0LinkObjId="SW-183679_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183677_0" Pin1InfoVect1LinkObjId="g_189b890_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1232,-433 1280,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17f3bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1232,-441 1232,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="27922@0" ObjectIDZND0="27924@x" ObjectIDZND1="g_189b890@0" Pin0InfoVect0LinkObjId="SW-183679_0" Pin0InfoVect1LinkObjId="g_189b890_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183677_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1232,-441 1232,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17f3e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1232,-433 1232,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="27924@x" ObjectIDND1="27922@x" ObjectIDZND0="g_189b890@0" Pin0InfoVect0LinkObjId="g_189b890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183679_0" Pin1InfoVect1LinkObjId="SW-183677_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1232,-433 1232,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17f64c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1232,-313 1280,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="27923@x" ObjectIDND1="41628@x" ObjectIDZND0="27925@0" Pin0InfoVect0LinkObjId="SW-183680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183678_0" Pin1InfoVect1LinkObjId="CB-DY_NSB.DY_NSB_Cb1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1232,-313 1280,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17f9270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1232,-324 1232,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="27923@0" ObjectIDZND0="27925@x" ObjectIDZND1="41628@x" Pin0InfoVect0LinkObjId="SW-183680_0" Pin0InfoVect1LinkObjId="CB-DY_NSB.DY_NSB_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183678_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1232,-324 1232,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17f94d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1232,-313 1232,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="27925@x" ObjectIDND1="27923@x" ObjectIDZND0="41628@0" Pin0InfoVect0LinkObjId="CB-DY_NSB.DY_NSB_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183680_0" Pin1InfoVect1LinkObjId="SW-183678_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1232,-313 1232,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17f9730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1231,-206 1231,-198 1280,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="27926@0" Pin0InfoVect0LinkObjId="SW-183681_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1231,-206 1231,-198 1280,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18060c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1326,-198 1316,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1805690@0" ObjectIDZND0="27926@1" Pin0InfoVect0LinkObjId="SW-183681_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1805690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1326,-198 1316,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1806d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1326,-433 1316,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1806320@0" ObjectIDZND0="27924@1" Pin0InfoVect0LinkObjId="SW-183679_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1806320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1326,-433 1316,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18963f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1326,-313 1316,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1806f70@0" ObjectIDZND0="27925@1" Pin0InfoVect0LinkObjId="SW-183680_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1806f70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1326,-313 1316,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_184cf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-82,-488 -82,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27953@1" ObjectIDZND0="27952@1" Pin0InfoVect0LinkObjId="SW-183831_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183833_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-82,-488 -82,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17fc8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-82,-449 -82,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27952@0" ObjectIDZND0="27954@1" Pin0InfoVect0LinkObjId="SW-183833_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183831_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-82,-449 -82,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1817060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-51,-263 -82,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1815740@0" ObjectIDZND0="27955@x" ObjectIDZND1="34231@x" Pin0InfoVect0LinkObjId="SW-183834_0" Pin0InfoVect1LinkObjId="EC-DY_NSB.053Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1815740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-51,-263 -82,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1817250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-82,-277 -82,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27955@0" ObjectIDZND0="g_1815740@0" ObjectIDZND1="34231@x" Pin0InfoVect0LinkObjId="g_1815740_0" Pin0InfoVect1LinkObjId="EC-DY_NSB.053Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183834_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-82,-277 -82,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1817440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-82,-263 -82,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1815740@0" ObjectIDND1="27955@x" ObjectIDZND0="34231@0" Pin0InfoVect0LinkObjId="EC-DY_NSB.053Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1815740_0" Pin1InfoVect1LinkObjId="SW-183834_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-82,-263 -82,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1817630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-82,-401 -34,-401 -34,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="27954@x" ObjectIDND1="g_17fcb10@0" ObjectIDZND0="27956@1" Pin0InfoVect0LinkObjId="SW-183835_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183833_0" Pin1InfoVect1LinkObjId="g_17fcb10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-82,-401 -34,-401 -34,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1817860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-82,-420 -82,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="27954@0" ObjectIDZND0="27956@x" ObjectIDZND1="g_17fcb10@0" Pin0InfoVect0LinkObjId="SW-183835_0" Pin0InfoVect1LinkObjId="g_17fcb10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183833_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-82,-420 -82,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1817a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-82,-401 -82,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="27956@x" ObjectIDND1="27954@x" ObjectIDZND0="g_17fcb10@0" Pin0InfoVect0LinkObjId="g_17fcb10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183835_0" Pin1InfoVect1LinkObjId="SW-183833_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-82,-401 -82,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_184ea60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-34,-355 -34,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27956@0" ObjectIDZND0="g_184ecc0@0" Pin0InfoVect0LinkObjId="g_184ecc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183835_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-34,-355 -34,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_184f710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-82,-331 -82,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_17fcb10@1" ObjectIDZND0="27955@1" Pin0InfoVect0LinkObjId="SW-183834_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17fcb10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-82,-331 -82,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_184f970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-82,-505 -82,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27953@0" ObjectIDZND0="27901@0" Pin0InfoVect0LinkObjId="g_1794a60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183833_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-82,-505 -82,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17a8d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="97,-490 97,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27948@1" ObjectIDZND0="27947@1" Pin0InfoVect0LinkObjId="SW-183804_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183806_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="97,-490 97,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17ab050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="97,-451 97,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27947@0" ObjectIDZND0="27949@1" Pin0InfoVect0LinkObjId="SW-183806_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183804_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="97,-451 97,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b0560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="128,-265 97,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_17aef20@0" ObjectIDZND0="27950@x" ObjectIDZND1="34230@x" Pin0InfoVect0LinkObjId="SW-183807_0" Pin0InfoVect1LinkObjId="EC-DY_NSB.052Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17aef20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="128,-265 97,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b0750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="97,-279 97,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27950@0" ObjectIDZND0="g_17aef20@0" ObjectIDZND1="34230@x" Pin0InfoVect0LinkObjId="g_17aef20_0" Pin0InfoVect1LinkObjId="EC-DY_NSB.052Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183807_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="97,-279 97,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b0940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="97,-265 97,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_17aef20@0" ObjectIDND1="27950@x" ObjectIDZND0="34230@0" Pin0InfoVect0LinkObjId="EC-DY_NSB.052Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_17aef20_0" Pin1InfoVect1LinkObjId="SW-183807_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="97,-265 97,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b0b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="97,-403 145,-403 145,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="27949@x" ObjectIDND1="g_17ab2b0@0" ObjectIDZND0="27951@1" Pin0InfoVect0LinkObjId="SW-183808_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183806_0" Pin1InfoVect1LinkObjId="g_17ab2b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="97,-403 145,-403 145,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1747d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="97,-422 97,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="27949@0" ObjectIDZND0="27951@x" ObjectIDZND1="g_17ab2b0@0" Pin0InfoVect0LinkObjId="SW-183808_0" Pin0InfoVect1LinkObjId="g_17ab2b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183806_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="97,-422 97,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1747fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="97,-403 97,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="27951@x" ObjectIDND1="27949@x" ObjectIDZND0="g_17ab2b0@0" Pin0InfoVect0LinkObjId="g_17ab2b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183808_0" Pin1InfoVect1LinkObjId="SW-183806_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="97,-403 97,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_174a940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="145,-357 145,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27951@0" ObjectIDZND0="g_174aba0@0" Pin0InfoVect0LinkObjId="g_174aba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183808_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="145,-357 145,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_174b5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="97,-333 97,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_17ab2b0@1" ObjectIDZND0="27950@1" Pin0InfoVect0LinkObjId="SW-183807_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17ab2b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="97,-333 97,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_174b850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="97,-507 97,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27948@0" ObjectIDZND0="27901@0" Pin0InfoVect0LinkObjId="g_1794a60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183806_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="97,-507 97,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17dac40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-288,-512 -288,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27958@1" ObjectIDZND0="27957@1" Pin0InfoVect0LinkObjId="SW-183858_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183860_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-288,-512 -288,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17dcf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-288,-473 -288,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27957@0" ObjectIDZND0="27959@1" Pin0InfoVect0LinkObjId="SW-183860_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183858_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-288,-473 -288,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17e0720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-288,-374 -288,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_17dd1d0@1" ObjectIDZND0="27960@1" Pin0InfoVect0LinkObjId="SW-183861_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17dd1d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-288,-374 -288,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17e0980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-288,-436 -240,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="27959@x" ObjectIDND1="g_17dd1d0@0" ObjectIDZND0="27961@0" Pin0InfoVect0LinkObjId="SW-183862_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183860_0" Pin1InfoVect1LinkObjId="g_17dd1d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-288,-436 -240,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17e0be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-288,-444 -288,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="27959@0" ObjectIDZND0="27961@x" ObjectIDZND1="g_17dd1d0@0" Pin0InfoVect0LinkObjId="SW-183862_0" Pin0InfoVect1LinkObjId="g_17dd1d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-288,-444 -288,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17e0e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-288,-436 -288,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="27961@x" ObjectIDND1="27959@x" ObjectIDZND0="g_17dd1d0@0" Pin0InfoVect0LinkObjId="g_17dd1d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183862_0" Pin1InfoVect1LinkObjId="SW-183860_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-288,-436 -288,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17e35d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-288,-316 -240,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="27960@x" ObjectIDND1="41629@x" ObjectIDZND0="27962@0" Pin0InfoVect0LinkObjId="SW-183863_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183861_0" Pin1InfoVect1LinkObjId="CB-DY_NSB.DY_NSB_Cb2_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-288,-316 -240,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17e6ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-288,-327 -288,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="27960@0" ObjectIDZND0="27962@x" ObjectIDZND1="41629@x" Pin0InfoVect0LinkObjId="SW-183863_0" Pin0InfoVect1LinkObjId="CB-DY_NSB.DY_NSB_Cb2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183861_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-288,-327 -288,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17e7150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-288,-316 -288,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="27962@x" ObjectIDND1="27960@x" ObjectIDZND0="41629@0" Pin0InfoVect0LinkObjId="CB-DY_NSB.DY_NSB_Cb2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183863_0" Pin1InfoVect1LinkObjId="SW-183861_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-288,-316 -288,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17e73b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-289,-209 -289,-201 -240,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="27963@0" Pin0InfoVect0LinkObjId="SW-183864_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-289,-209 -289,-201 -240,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17ea5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-194,-201 -204,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_17e9b40@0" ObjectIDZND0="27963@1" Pin0InfoVect0LinkObjId="SW-183864_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17e9b40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-194,-201 -204,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17eb2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-194,-436 -204,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_17ea830@0" ObjectIDZND0="27961@1" Pin0InfoVect0LinkObjId="SW-183862_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17ea830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-194,-436 -204,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17ebfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-194,-316 -204,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_17eb520@0" ObjectIDZND0="27962@1" Pin0InfoVect0LinkObjId="SW-183863_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17eb520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-194,-316 -204,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17ec840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-288,-529 -288,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27958@0" ObjectIDZND0="27901@0" Pin0InfoVect0LinkObjId="g_1794a60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-288,-529 -288,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17781f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="614,-485 614,-473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27938@1" ObjectIDZND0="27937@1" Pin0InfoVect0LinkObjId="SW-183750_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183752_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="614,-485 614,-473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_177a520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="614,-446 614,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27937@0" ObjectIDZND0="27939@1" Pin0InfoVect0LinkObjId="SW-183752_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="614,-446 614,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_177f120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="645,-260 614,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_177e3f0@0" ObjectIDZND0="27940@x" ObjectIDZND1="34234@x" Pin0InfoVect0LinkObjId="SW-183753_0" Pin0InfoVect1LinkObjId="EC-DY_NSB.034Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_177e3f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="645,-260 614,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_177f380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="614,-274 614,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27940@0" ObjectIDZND0="g_177e3f0@0" ObjectIDZND1="34234@x" Pin0InfoVect0LinkObjId="g_177e3f0_0" Pin0InfoVect1LinkObjId="EC-DY_NSB.034Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183753_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="614,-274 614,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_177f5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="614,-260 614,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="27940@x" ObjectIDND1="g_177e3f0@0" ObjectIDZND0="34234@0" Pin0InfoVect0LinkObjId="EC-DY_NSB.034Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183753_0" Pin1InfoVect1LinkObjId="g_177e3f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="614,-260 614,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_177f840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="614,-398 662,-398 662,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="27939@x" ObjectIDND1="g_177a780@0" ObjectIDZND0="27941@1" Pin0InfoVect0LinkObjId="SW-183754_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183752_0" Pin1InfoVect1LinkObjId="g_177a780_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="614,-398 662,-398 662,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_177faa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="614,-417 614,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="27939@0" ObjectIDZND0="27941@x" ObjectIDZND1="g_177a780@0" Pin0InfoVect0LinkObjId="SW-183754_0" Pin0InfoVect1LinkObjId="g_177a780_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183752_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="614,-417 614,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_177fd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="614,-398 614,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="27939@x" ObjectIDND1="27941@x" ObjectIDZND0="g_177a780@0" Pin0InfoVect0LinkObjId="g_177a780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183752_0" Pin1InfoVect1LinkObjId="SW-183754_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="614,-398 614,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1782760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="662,-352 662,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27941@0" ObjectIDZND0="g_17829c0@0" Pin0InfoVect0LinkObjId="g_17829c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183754_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="662,-352 662,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1783410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="614,-328 614,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_177a780@1" ObjectIDZND0="27940@1" Pin0InfoVect0LinkObjId="SW-183753_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_177a780_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="614,-328 614,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_180d610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-694 1040,-685 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_180a0c0@0" ObjectIDZND0="g_180cd10@1" Pin0InfoVect0LinkObjId="g_180cd10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_180a0c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-694 1040,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1810930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-622 1072,-622 1072,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_180cd10@0" ObjectIDND1="27916@x" ObjectIDZND0="g_18118e0@0" Pin0InfoVect0LinkObjId="g_18118e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_180cd10_0" Pin1InfoVect1LinkObjId="SW-183667_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-622 1072,-622 1072,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1811420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-641 1040,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_180cd10@0" ObjectIDZND0="27916@x" ObjectIDZND1="g_18118e0@0" Pin0InfoVect0LinkObjId="SW-183667_0" Pin0InfoVect1LinkObjId="g_18118e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_180cd10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-641 1040,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1811680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-622 1040,-597 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_180cd10@0" ObjectIDND1="g_18118e0@0" ObjectIDZND0="27916@1" Pin0InfoVect0LinkObjId="SW-183667_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_180cd10_0" Pin1InfoVect1LinkObjId="g_18118e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-622 1040,-597 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1752ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1214,-581 1214,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="27969@1" ObjectIDZND0="g_1753130@0" Pin0InfoVect0LinkObjId="g_1753130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183949_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1214,-581 1214,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1753a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1214,-636 1214,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1753130@1" ObjectIDZND0="27970@1" Pin0InfoVect0LinkObjId="SW-183949_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1753130_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1214,-636 1214,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17577e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1214,-745 1214,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1756a90@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_18f7820_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1756a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1214,-745 1214,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1757a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1214,-677 1246,-677 1246,-689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="27970@x" ObjectIDND1="g_1756a90@0" ObjectIDZND0="g_17589f0@0" Pin0InfoVect0LinkObjId="g_17589f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183949_0" Pin1InfoVect1LinkObjId="g_1756a90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1214,-677 1246,-677 1246,-689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1758530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1214,-664 1214,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="27970@0" ObjectIDZND0="g_17589f0@0" ObjectIDZND1="g_1756a90@0" Pin0InfoVect0LinkObjId="g_17589f0_0" Pin0InfoVect1LinkObjId="g_1756a90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183949_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1214,-664 1214,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1758790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1214,-677 1214,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_17589f0@0" ObjectIDND1="27970@x" ObjectIDZND0="g_1756a90@1" Pin0InfoVect0LinkObjId="g_1756a90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_17589f0_0" Pin1InfoVect1LinkObjId="SW-183949_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1214,-677 1214,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_175d390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="98,-692 98,-683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_175a0c0@0" ObjectIDZND0="g_175ca90@1" Pin0InfoVect0LinkObjId="g_175ca90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_175a0c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="98,-692 98,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b0d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="98,-578 97,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27918@0" ObjectIDZND0="27901@0" Pin0InfoVect0LinkObjId="g_1794a60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183671_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="98,-578 97,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b0fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="98,-620 130,-620 130,-634 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_175ca90@0" ObjectIDND1="27918@x" ObjectIDZND0="g_17b1700@0" Pin0InfoVect0LinkObjId="g_17b1700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_175ca90_0" Pin1InfoVect1LinkObjId="SW-183671_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="98,-620 130,-620 130,-634 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b1240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="98,-639 98,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_175ca90@0" ObjectIDZND0="g_17b1700@0" ObjectIDZND1="27918@x" Pin0InfoVect0LinkObjId="g_17b1700_0" Pin0InfoVect1LinkObjId="SW-183671_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_175ca90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="98,-639 98,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b14a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="98,-620 98,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_17b1700@0" ObjectIDND1="g_175ca90@0" ObjectIDZND0="27918@1" Pin0InfoVect0LinkObjId="SW-183671_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_17b1700_0" Pin1InfoVect1LinkObjId="g_175ca90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="98,-620 98,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b8fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-631 320,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27965@1" ObjectIDZND0="27964@1" Pin0InfoVect0LinkObjId="SW-183879_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183880_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="320,-631 320,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17bb2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-592 320,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27964@0" ObjectIDZND0="27966@1" Pin0InfoVect0LinkObjId="SW-183880_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183879_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="320,-592 320,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17bb530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-563 320,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27966@0" ObjectIDZND0="27901@0" Pin0InfoVect0LinkObjId="g_1794a60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="320,-563 320,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17bbd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-648 320,-665 581,-665 581,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="27965@0" ObjectIDZND0="27967@0" Pin0InfoVect0LinkObjId="SW-183881_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="320,-648 320,-665 581,-665 581,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1962850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="581,-629 581,-600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="27967@1" ObjectIDZND0="27968@1" Pin0InfoVect0LinkObjId="SW-183881_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183881_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="581,-629 581,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1963320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-779 744,-759 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="27911@0" ObjectIDZND0="27912@x" ObjectIDZND1="27971@x" Pin0InfoVect0LinkObjId="SW-183604_0" Pin0InfoVect1LinkObjId="g_1963560_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183603_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="744,-779 744,-759 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1963560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-759 744,-738 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="27912@x" ObjectIDND1="27911@x" ObjectIDZND0="27971@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183604_0" Pin1InfoVect1LinkObjId="SW-183603_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="744,-759 744,-738 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1965b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="320,-829 302,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27909@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_18f7820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183601_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="320,-829 302,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19664e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="282,-829 282,-787 317,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="27910@0" Pin0InfoVect0LinkObjId="SW-183602_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18f7820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="282,-829 282,-787 317,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1969000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="353,-787 370,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27910@1" ObjectIDZND0="g_1969260@0" Pin0InfoVect0LinkObjId="g_1969260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183602_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="353,-787 370,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1769550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-558 744,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27914@0" ObjectIDZND0="27900@0" Pin0InfoVect0LinkObjId="g_1769c30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183607_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="744,-558 744,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1769c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="581,-583 581,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27968@0" ObjectIDZND0="27900@0" Pin0InfoVect0LinkObjId="g_1769550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183881_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="581,-583 581,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_176a340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="614,-502 614,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27938@0" ObjectIDZND0="27900@0" Pin0InfoVect0LinkObjId="g_1769550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183752_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="614,-502 614,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_176ab70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="833,-498 833,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27933@0" ObjectIDZND0="27900@0" Pin0InfoVect0LinkObjId="g_1769550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183725_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="833,-498 833,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_176b3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-580 1040,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27916@0" ObjectIDZND0="27900@0" Pin0InfoVect0LinkObjId="g_1769550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183667_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-580 1040,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_176bbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-500 1040,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27928@0" ObjectIDZND0="27900@0" Pin0InfoVect0LinkObjId="g_1769550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183698_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-500 1040,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_176c400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1214,-564 1214,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27969@0" ObjectIDZND0="27900@0" Pin0InfoVect0LinkObjId="g_1769550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183949_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1214,-564 1214,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_176cc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1232,-526 1232,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27921@0" ObjectIDZND0="27900@0" Pin0InfoVect0LinkObjId="g_1769550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183677_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1232,-526 1232,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_176d460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-862 743,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27905@0" ObjectIDZND0="27899@0" Pin0InfoVect0LinkObjId="g_176dc90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183571_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="743,-862 743,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_176dc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-815 744,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27911@1" ObjectIDZND0="27899@0" Pin0InfoVect0LinkObjId="g_176d460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183603_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="744,-815 744,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_176e4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="356,-829 380,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27909@1" ObjectIDZND0="27899@0" Pin0InfoVect0LinkObjId="g_176d460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183601_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="356,-829 380,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d7b8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-1037 743,-1081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="27906@x" ObjectIDND1="27904@x" ObjectIDZND0="0@x" ObjectIDZND1="g_18f7820@0" ObjectIDZND2="g_18dc4c0@0" Pin0InfoVect0LinkObjId="g_18f7820_0" Pin0InfoVect1LinkObjId="g_18f7820_0" Pin0InfoVect2LinkObjId="g_18dc4c0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-183572_0" Pin1InfoVect1LinkObjId="SW-183570_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="743,-1037 743,-1081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d7bb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-1081 743,-1134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="0@x" ObjectIDND1="27906@x" ObjectIDND2="27904@x" ObjectIDZND0="g_18f7820@0" ObjectIDZND1="g_18dc4c0@0" ObjectIDZND2="34315@1" Pin0InfoVect0LinkObjId="g_18f7820_0" Pin0InfoVect1LinkObjId="g_18dc4c0_0" Pin0InfoVect2LinkObjId="g_18a2f50_1" Pin0Num="3" Pin1InfoVect0LinkObjId="g_18f7820_0" Pin1InfoVect1LinkObjId="SW-183572_0" Pin1InfoVect2LinkObjId="SW-183570_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="743,-1081 743,-1134 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="27901" cx="289" cy="-542" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27901" cx="-82" cy="-542" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27901" cx="97" cy="-542" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27901" cx="-288" cy="-542" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27901" cx="97" cy="-542" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27901" cx="320" cy="-542" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="302" cy="-829" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="282" cy="-829" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27900" cx="744" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27900" cx="581" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27900" cx="614" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27900" cx="833" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27900" cx="1040" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27900" cx="1040" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27900" cx="1214" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27900" cx="1232" cy="-543" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27899" cx="743" cy="-829" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27899" cx="744" cy="-829" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27899" cx="380" cy="-829" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-153546" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -453.500000 -1166.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26036" ObjectName="DYN-DY_NSB"/>
     <cge:Meas_Ref ObjectId="153546"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimSun" font-size="20" graphid="g_193f050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -570.000000 -1243.500000) translate(0,16)">南山坝变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15a8b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15a8b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15a8b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,59)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15a8b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15a8b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,101)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15a8b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15a8b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,143)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15a8b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15a8b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,185)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15a8b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15a8b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,227)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16a3050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16a3050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16a3050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16a3050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16a3050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16a3050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16a3050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16a3050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16a3050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16a3050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16a3050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16a3050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16a3050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16a3050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16a3050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16a3050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16a3050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16a3050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,374)">联系方式：0878-6148331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_172e160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1004.000000 -751.000000) translate(0,12)">10kVⅠ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18664e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1169.096525 -874.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1575360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 453.096525 -1192.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18aea40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 691.096525 -1263.000000) translate(0,12)">35kV大仓南线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1887900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 817.096525 -1126.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18611f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 824.000000 -199.000000) translate(0,12)">南妙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1896650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1197.000000 -191.000000) translate(0,12)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1816430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -102.000000 -203.000000) translate(0,12)">南仓联络线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17afc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 84.000000 -201.000000) translate(0,12)">南七线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17ec210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -323.000000 -194.000000) translate(0,12)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17597a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 62.000000 -749.000000) translate(0,12)">10kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1969c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 254.000000 -199.000000) translate(0,12)">工业园区Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196a990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 564.000000 -200.000000) translate(0,12)">工业园区Ⅲ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196ae30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 993.000000 -196.000000) translate(0,12)">工业园区Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196b1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 376.000000 -849.000000) translate(0,12)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196b410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1072.000000 -564.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196b650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -128.000000 -564.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196b890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 758.000000 -948.000000) translate(0,12)">331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196bad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 750.000000 -1012.000000) translate(0,12)">3316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196be40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 750.000000 -887.000000) translate(0,12)">3311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196c220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 651.000000 -937.000000) translate(0,12)">33117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196c560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 652.000000 -999.000000) translate(0,12)">33160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196c9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 651.000000 -1063.000000) translate(0,12)">33167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196cc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 751.000000 -804.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196ce40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 652.000000 -785.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196d080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 322.000000 -855.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196d2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 315.000000 -813.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196d500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 754.000000 -608.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196d740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1242.000000 -491.000000) translate(0,12)">031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196d980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1239.000000 -349.000000) translate(0,12)">0316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196dbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1278.000000 -459.000000) translate(0,12)">03167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196de00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1278.000000 -339.000000) translate(0,12)">03160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196e040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1278.000000 -224.000000) translate(0,12)">03100</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196e280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1050.000000 -465.000000) translate(0,12)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196e4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1047.000000 -297.000000) translate(0,12)">0326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196e700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1095.000000 -375.000000) translate(0,12)">03267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196e940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 843.000000 -463.000000) translate(0,12)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196eb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 840.000000 -295.000000) translate(0,12)">0336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196edc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 888.000000 -373.000000) translate(0,12)">03367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196f000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 624.000000 -467.000000) translate(0,12)">034</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196f340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 621.000000 -299.000000) translate(0,12)">0346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196f7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 669.000000 -377.000000) translate(0,12)">03467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196f9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 592.000000 -620.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196fc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 283.000000 -613.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196fe60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 299.000000 -470.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19700a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 296.000000 -302.000000) translate(0,12)">0516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19702e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 344.000000 -380.000000) translate(0,12)">05167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1970520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 107.000000 -472.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1970760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 104.000000 -304.000000) translate(0,12)">0526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19709a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 152.000000 -382.000000) translate(0,12)">05267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1970be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -73.000000 -470.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1970e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -75.000000 -302.000000) translate(0,12)">0536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1971060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -27.000000 -380.000000) translate(0,12)">05367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19712a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -279.000000 -494.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19714e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -281.000000 -352.000000) translate(0,12)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1971720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -241.000000 -462.000000) translate(0,12)">05467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1971960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -241.000000 -342.000000) translate(0,12)">05460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1971ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -241.000000 -227.000000) translate(0,12)">05400</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d778b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 588.000000 -727.000000) translate(0,12)">SZ11-5000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d778b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 588.000000 -727.000000) translate(0,27)">35±3*2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d778b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 588.000000 -727.000000) translate(0,42)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d778b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 588.000000 -727.000000) translate(0,57)">7%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d79dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -703.000000 -845.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1d7cf20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -446.000000 -1227.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_189fa60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -446.000000 -1262.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d7f9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -728.903475 -601.000000) translate(0,20)">现场上送大0121手车位置为分位。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d7f9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -728.903475 -601.000000) translate(0,44)">临时措施：主站端已取反。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d85db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -752.000000 -261.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1d874c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -598.000000 -271.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1d874c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -598.000000 -271.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1d8b970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -388.500000 -1172.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d928a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1222.000000 -151.000000) translate(0,12)">Ia(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d92ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1222.000000 -134.000000) translate(0,12)">Ib(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d93110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1222.000000 -116.000000) translate(0,12)">Ic(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d93350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1196.500000 -169.000000) translate(0,12)">Q(KVar):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f226d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 775.000000 -764.000000) translate(0,12)">1号主变</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="-405" y="-1183"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-DY_NSB.DY_NSB_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="376,-829 1155,-829 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27899" ObjectName="BS-DY_NSB.DY_NSB_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="27899"/></metadata>
   <polyline fill="none" opacity="0" points="376,-829 1155,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_NSB.DY_NSB_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="340,-542 -337,-542 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27901" ObjectName="BS-DY_NSB.DY_NSB_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="27901"/></metadata>
   <polyline fill="none" opacity="0" points="340,-542 -337,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_NSB.DY_NSB_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="560,-543 1407,-543 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27900" ObjectName="BS-DY_NSB.DY_NSB_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="27900"/></metadata>
   <polyline fill="none" opacity="0" points="560,-543 1407,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="303,-829 255,-829 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="303,-829 255,-829 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 565.000000 -1153.000000)" xlink:href="#transformer2:shape56_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 565.000000 -1153.000000)" xlink:href="#transformer2:shape56_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-DY_NSB.DY_NSB_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="39622"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.814347 -0.000000 0.000000 -0.830245 713.000000 -658.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.814347 -0.000000 0.000000 -0.830245 713.000000 -658.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="27971" ObjectName="TF-DY_NSB.DY_NSB_1T"/>
    <cge:TPSR_Ref TObjectID="27971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.978723 0.000000 -0.000000 0.857143 1183.000000 -852.000000)" xlink:href="#transformer2:shape77_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.978723 0.000000 -0.000000 0.857143 1183.000000 -852.000000)" xlink:href="#transformer2:shape77_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -657.000000 -1196.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -623.000000 -1101.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-250475" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -595.000000 -976.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250475" ObjectName="DY_NSB:DY_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-250476" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -594.000000 -934.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250476" ObjectName="DY_NSB:DY_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-183443" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -595.000000 -1060.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183443" ObjectName="DY_NSB:DY_NSB_331BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-183443" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -595.000000 -1020.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183443" ObjectName="DY_NSB:DY_NSB_331BK_P"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-605" y="-1254"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-605" y="-1254"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-654" y="-1271"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-654" y="-1271"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="78" x="-702" y="-845"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="78" x="-702" y="-845"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1242" y="-491"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1242" y="-491"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="758" y="-948"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="758" y="-948"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="-279" y="-494"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="-279" y="-494"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="-73" y="-470"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="-73" y="-470"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="107" y="-472"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="107" y="-472"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="299" y="-470"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="299" y="-470"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="624" y="-467"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="624" y="-467"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="843" y="-463"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="843" y="-463"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1050" y="-465"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1050" y="-465"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="283" y="-613"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="283" y="-613"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-457" y="-1235"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-457" y="-1235"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-457" y="-1270"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-457" y="-1270"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="-405" y="-1184"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="-405" y="-1184"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="775" y="-764"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="775" y="-764"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1971ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1065.000000 920.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1972790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1059.000000 973.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1972d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1051.000000 885.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1973390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1051.000000 867.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1973610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1067.000000 850.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1974170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1059.000000 954.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19743f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1051.000000 903.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1974630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1059.000000 937.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1974960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1321.000000 632.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1974bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1315.000000 685.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1974e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1307.000000 597.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1975070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1307.000000 579.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19752b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1323.000000 562.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19754f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1315.000000 666.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1975730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1307.000000 615.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1975970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1315.000000 649.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1760920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -322.000000 635.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1760bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -328.000000 688.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1760df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -336.000000 600.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1761030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -336.000000 582.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1761270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -320.000000 565.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17614b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -328.000000 669.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17616f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -336.000000 618.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1761930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -328.000000 652.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1766750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -99.000000 138.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1767110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -99.000000 121.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1767330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -99.000000 103.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1767570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -95.000000 86.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1768130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -113.000000 175.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1768cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -124.500000 156.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_176ee20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 814.000000 947.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_176f310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 814.000000 930.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_176f550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 814.000000 912.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_176f790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 818.000000 895.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_176f9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 800.000000 984.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_176fc10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 788.500000 965.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_176ff40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 808.000000 622.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17701c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 808.000000 605.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1770400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 808.000000 587.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1770640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 812.000000 570.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1770880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 794.000000 659.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1770ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 782.500000 640.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1770df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 805.000000 731.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1771660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 775.500000 709.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1772330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 361.000000 618.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17725b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 361.000000 601.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17727f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 361.000000 583.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1772a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 365.000000 566.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1772c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 347.000000 655.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1772eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 335.500000 636.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17731e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 86.000000 139.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1773460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 86.000000 122.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17736a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 86.000000 104.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17738e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 90.000000 87.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1773b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 72.000000 176.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1773d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 60.500000 157.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1774090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 278.000000 138.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1774310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 278.000000 121.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1774550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 278.000000 103.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1774790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 282.000000 86.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17749d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 264.000000 175.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1774c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 252.500000 156.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1774f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 595.000000 139.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17751c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 595.000000 122.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1775400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 595.000000 104.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1775640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 599.000000 87.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1775880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 581.000000 176.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1775ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 569.500000 157.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1775df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 820.000000 138.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1776070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 820.000000 121.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17762b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 820.000000 103.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17764f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 824.000000 86.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1776730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 806.000000 175.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1776970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 794.500000 156.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d75320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1023.000000 138.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d755a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1023.000000 121.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d757e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1023.000000 103.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d75a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1027.000000 86.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d75c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1009.000000 175.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d75ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 997.500000 156.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d761d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -315.000000 152.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d76440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -315.000000 135.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d76680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -315.000000 117.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d768c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -340.500000 170.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-DY_NSB.DY_NSB_Cb2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -317.000000 -207.000000)" xlink:href="#capacitor:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41629" ObjectName="CB-DY_NSB.DY_NSB_Cb2"/>
    <cge:TPSR_Ref TObjectID="41629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-DY_NSB.DY_NSB_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1203.000000 -204.000000)" xlink:href="#capacitor:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41628" ObjectName="CB-DY_NSB.DY_NSB_Cb1"/>
    <cge:TPSR_Ref TObjectID="41628"/></metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-183468" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1374.000000 -688.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183468" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27900"/>
     <cge:Term_Ref ObjectID="20994"/>
    <cge:TPSR_Ref TObjectID="27900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-183469" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1374.000000 -688.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183469" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27900"/>
     <cge:Term_Ref ObjectID="20994"/>
    <cge:TPSR_Ref TObjectID="27900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-183470" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1374.000000 -688.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183470" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27900"/>
     <cge:Term_Ref ObjectID="20994"/>
    <cge:TPSR_Ref TObjectID="27900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-183474" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1374.000000 -688.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183474" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27900"/>
     <cge:Term_Ref ObjectID="20994"/>
    <cge:TPSR_Ref TObjectID="27900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-183471" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1374.000000 -688.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183471" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27900"/>
     <cge:Term_Ref ObjectID="20994"/>
    <cge:TPSR_Ref TObjectID="27900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-183472" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1374.000000 -688.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183472" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27900"/>
     <cge:Term_Ref ObjectID="20994"/>
    <cge:TPSR_Ref TObjectID="27900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-183473" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1374.000000 -688.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183473" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27900"/>
     <cge:Term_Ref ObjectID="20994"/>
    <cge:TPSR_Ref TObjectID="27900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-183475" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1374.000000 -688.000000) translate(0,141)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183475" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27900"/>
     <cge:Term_Ref ObjectID="20994"/>
    <cge:TPSR_Ref TObjectID="27900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-183476" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -692.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183476" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27901"/>
     <cge:Term_Ref ObjectID="39481"/>
    <cge:TPSR_Ref TObjectID="27901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-183477" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -692.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183477" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27901"/>
     <cge:Term_Ref ObjectID="39481"/>
    <cge:TPSR_Ref TObjectID="27901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-183478" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -692.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183478" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27901"/>
     <cge:Term_Ref ObjectID="39481"/>
    <cge:TPSR_Ref TObjectID="27901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-183482" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -692.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183482" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27901"/>
     <cge:Term_Ref ObjectID="39481"/>
    <cge:TPSR_Ref TObjectID="27901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-183479" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -692.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183479" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27901"/>
     <cge:Term_Ref ObjectID="39481"/>
    <cge:TPSR_Ref TObjectID="27901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-183480" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -692.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183480" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27901"/>
     <cge:Term_Ref ObjectID="39481"/>
    <cge:TPSR_Ref TObjectID="27901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-183481" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -692.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183481" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27901"/>
     <cge:Term_Ref ObjectID="39481"/>
    <cge:TPSR_Ref TObjectID="27901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-183483" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -270.000000 -692.000000) translate(0,141)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183483" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27901"/>
     <cge:Term_Ref ObjectID="39481"/>
    <cge:TPSR_Ref TObjectID="27901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-183460" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1116.000000 -975.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183460" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27899"/>
     <cge:Term_Ref ObjectID="20993"/>
    <cge:TPSR_Ref TObjectID="27899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-183461" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1116.000000 -975.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183461" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27899"/>
     <cge:Term_Ref ObjectID="20993"/>
    <cge:TPSR_Ref TObjectID="27899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-183462" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1116.000000 -975.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183462" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27899"/>
     <cge:Term_Ref ObjectID="20993"/>
    <cge:TPSR_Ref TObjectID="27899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-183466" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1116.000000 -975.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183466" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27899"/>
     <cge:Term_Ref ObjectID="20993"/>
    <cge:TPSR_Ref TObjectID="27899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-183463" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1116.000000 -975.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183463" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27899"/>
     <cge:Term_Ref ObjectID="20993"/>
    <cge:TPSR_Ref TObjectID="27899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-183464" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1116.000000 -975.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183464" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27899"/>
     <cge:Term_Ref ObjectID="20993"/>
    <cge:TPSR_Ref TObjectID="27899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-183465" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1116.000000 -975.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183465" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27899"/>
     <cge:Term_Ref ObjectID="20993"/>
    <cge:TPSR_Ref TObjectID="27899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-183467" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1116.000000 -975.000000) translate(0,141)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183467" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27899"/>
     <cge:Term_Ref ObjectID="20993"/>
    <cge:TPSR_Ref TObjectID="27899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-183521" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -55.000000 -177.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183521" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27952"/>
     <cge:Term_Ref ObjectID="39582"/>
    <cge:TPSR_Ref TObjectID="27952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-183522" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -55.000000 -177.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183522" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27952"/>
     <cge:Term_Ref ObjectID="39582"/>
    <cge:TPSR_Ref TObjectID="27952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-183518" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -55.000000 -177.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183518" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27952"/>
     <cge:Term_Ref ObjectID="39582"/>
    <cge:TPSR_Ref TObjectID="27952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-183519" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -55.000000 -177.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183519" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27952"/>
     <cge:Term_Ref ObjectID="39582"/>
    <cge:TPSR_Ref TObjectID="27952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-183520" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -55.000000 -177.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183520" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27952"/>
     <cge:Term_Ref ObjectID="39582"/>
    <cge:TPSR_Ref TObjectID="27952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-183523" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -55.000000 -177.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183523" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27952"/>
     <cge:Term_Ref ObjectID="39582"/>
    <cge:TPSR_Ref TObjectID="27952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-183515" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 130.000000 -178.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183515" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27947"/>
     <cge:Term_Ref ObjectID="39572"/>
    <cge:TPSR_Ref TObjectID="27947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-183516" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 130.000000 -178.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183516" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27947"/>
     <cge:Term_Ref ObjectID="39572"/>
    <cge:TPSR_Ref TObjectID="27947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-183512" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 130.000000 -178.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183512" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27947"/>
     <cge:Term_Ref ObjectID="39572"/>
    <cge:TPSR_Ref TObjectID="27947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-183513" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 130.000000 -178.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183513" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27947"/>
     <cge:Term_Ref ObjectID="39572"/>
    <cge:TPSR_Ref TObjectID="27947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-183514" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 130.000000 -178.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183514" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27947"/>
     <cge:Term_Ref ObjectID="39572"/>
    <cge:TPSR_Ref TObjectID="27947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-183517" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 130.000000 -178.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183517" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27947"/>
     <cge:Term_Ref ObjectID="39572"/>
    <cge:TPSR_Ref TObjectID="27947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-183509" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 322.000000 -178.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183509" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27942"/>
     <cge:Term_Ref ObjectID="39562"/>
    <cge:TPSR_Ref TObjectID="27942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-183510" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 322.000000 -178.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183510" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27942"/>
     <cge:Term_Ref ObjectID="39562"/>
    <cge:TPSR_Ref TObjectID="27942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-183506" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 322.000000 -178.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183506" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27942"/>
     <cge:Term_Ref ObjectID="39562"/>
    <cge:TPSR_Ref TObjectID="27942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-183507" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 322.000000 -178.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183507" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27942"/>
     <cge:Term_Ref ObjectID="39562"/>
    <cge:TPSR_Ref TObjectID="27942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-183508" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 322.000000 -178.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183508" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27942"/>
     <cge:Term_Ref ObjectID="39562"/>
    <cge:TPSR_Ref TObjectID="27942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-183511" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 322.000000 -178.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183511" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27942"/>
     <cge:Term_Ref ObjectID="39562"/>
    <cge:TPSR_Ref TObjectID="27942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-183503" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.000000 -177.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183503" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27937"/>
     <cge:Term_Ref ObjectID="39552"/>
    <cge:TPSR_Ref TObjectID="27937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-183504" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.000000 -177.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183504" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27937"/>
     <cge:Term_Ref ObjectID="39552"/>
    <cge:TPSR_Ref TObjectID="27937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-183500" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.000000 -177.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183500" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27937"/>
     <cge:Term_Ref ObjectID="39552"/>
    <cge:TPSR_Ref TObjectID="27937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-183501" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.000000 -177.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183501" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27937"/>
     <cge:Term_Ref ObjectID="39552"/>
    <cge:TPSR_Ref TObjectID="27937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-183502" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.000000 -177.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183502" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27937"/>
     <cge:Term_Ref ObjectID="39552"/>
    <cge:TPSR_Ref TObjectID="27937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-183505" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.000000 -177.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183505" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27937"/>
     <cge:Term_Ref ObjectID="39552"/>
    <cge:TPSR_Ref TObjectID="27937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-183497" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 866.000000 -180.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183497" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27932"/>
     <cge:Term_Ref ObjectID="39542"/>
    <cge:TPSR_Ref TObjectID="27932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-183498" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 866.000000 -180.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183498" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27932"/>
     <cge:Term_Ref ObjectID="39542"/>
    <cge:TPSR_Ref TObjectID="27932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-183494" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 866.000000 -180.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183494" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27932"/>
     <cge:Term_Ref ObjectID="39542"/>
    <cge:TPSR_Ref TObjectID="27932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-183495" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 866.000000 -180.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183495" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27932"/>
     <cge:Term_Ref ObjectID="39542"/>
    <cge:TPSR_Ref TObjectID="27932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-183496" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 866.000000 -180.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183496" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27932"/>
     <cge:Term_Ref ObjectID="39542"/>
    <cge:TPSR_Ref TObjectID="27932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-183499" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 866.000000 -180.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183499" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27932"/>
     <cge:Term_Ref ObjectID="39542"/>
    <cge:TPSR_Ref TObjectID="27932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-183491" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1068.000000 -176.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183491" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27927"/>
     <cge:Term_Ref ObjectID="39532"/>
    <cge:TPSR_Ref TObjectID="27927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-183492" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1068.000000 -176.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183492" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27927"/>
     <cge:Term_Ref ObjectID="39532"/>
    <cge:TPSR_Ref TObjectID="27927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-183488" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1068.000000 -176.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183488" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27927"/>
     <cge:Term_Ref ObjectID="39532"/>
    <cge:TPSR_Ref TObjectID="27927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-183489" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1068.000000 -176.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183489" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27927"/>
     <cge:Term_Ref ObjectID="39532"/>
    <cge:TPSR_Ref TObjectID="27927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-183490" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1068.000000 -176.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183490" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27927"/>
     <cge:Term_Ref ObjectID="39532"/>
    <cge:TPSR_Ref TObjectID="27927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-183493" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1068.000000 -176.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183493" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27927"/>
     <cge:Term_Ref ObjectID="39532"/>
    <cge:TPSR_Ref TObjectID="27927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-183455" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -661.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183455" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27913"/>
     <cge:Term_Ref ObjectID="39504"/>
    <cge:TPSR_Ref TObjectID="27913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-183456" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -661.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183456" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27913"/>
     <cge:Term_Ref ObjectID="39504"/>
    <cge:TPSR_Ref TObjectID="27913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-183452" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -661.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183452" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27913"/>
     <cge:Term_Ref ObjectID="39504"/>
    <cge:TPSR_Ref TObjectID="27913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-183453" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -661.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183453" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27913"/>
     <cge:Term_Ref ObjectID="39504"/>
    <cge:TPSR_Ref TObjectID="27913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-183454" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -661.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183454" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27913"/>
     <cge:Term_Ref ObjectID="39504"/>
    <cge:TPSR_Ref TObjectID="27913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-183457" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -661.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183457" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27913"/>
     <cge:Term_Ref ObjectID="39504"/>
    <cge:TPSR_Ref TObjectID="27913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-183531" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 405.000000 -657.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183531" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27964"/>
     <cge:Term_Ref ObjectID="39606"/>
    <cge:TPSR_Ref TObjectID="27964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-183532" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 405.000000 -657.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183532" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27964"/>
     <cge:Term_Ref ObjectID="39606"/>
    <cge:TPSR_Ref TObjectID="27964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-183528" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 405.000000 -657.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183528" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27964"/>
     <cge:Term_Ref ObjectID="39606"/>
    <cge:TPSR_Ref TObjectID="27964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-183529" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 405.000000 -657.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183529" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27964"/>
     <cge:Term_Ref ObjectID="39606"/>
    <cge:TPSR_Ref TObjectID="27964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-183530" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 405.000000 -657.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183530" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27964"/>
     <cge:Term_Ref ObjectID="39606"/>
    <cge:TPSR_Ref TObjectID="27964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-183533" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 405.000000 -657.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183533" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27964"/>
     <cge:Term_Ref ObjectID="39606"/>
    <cge:TPSR_Ref TObjectID="27964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-183443" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 857.000000 -987.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183443" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27903"/>
     <cge:Term_Ref ObjectID="39484"/>
    <cge:TPSR_Ref TObjectID="27903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-183444" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 857.000000 -987.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183444" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27903"/>
     <cge:Term_Ref ObjectID="39484"/>
    <cge:TPSR_Ref TObjectID="27903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-183440" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 857.000000 -987.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183440" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27903"/>
     <cge:Term_Ref ObjectID="39484"/>
    <cge:TPSR_Ref TObjectID="27903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-183441" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 857.000000 -987.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183441" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27903"/>
     <cge:Term_Ref ObjectID="39484"/>
    <cge:TPSR_Ref TObjectID="27903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-183442" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 857.000000 -987.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183442" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27903"/>
     <cge:Term_Ref ObjectID="39484"/>
    <cge:TPSR_Ref TObjectID="27903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-183445" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 857.000000 -987.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183445" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27903"/>
     <cge:Term_Ref ObjectID="39484"/>
    <cge:TPSR_Ref TObjectID="27903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-183527" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -268.000000 -172.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183527" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27957"/>
     <cge:Term_Ref ObjectID="39592"/>
    <cge:TPSR_Ref TObjectID="27957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-183524" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -268.000000 -172.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183524" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27957"/>
     <cge:Term_Ref ObjectID="39592"/>
    <cge:TPSR_Ref TObjectID="27957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-183525" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -268.000000 -172.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183525" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27957"/>
     <cge:Term_Ref ObjectID="39592"/>
    <cge:TPSR_Ref TObjectID="27957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-183526" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -268.000000 -172.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183526" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27957"/>
     <cge:Term_Ref ObjectID="39592"/>
    <cge:TPSR_Ref TObjectID="27957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-183487" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1265.000000 -171.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183487" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27920"/>
     <cge:Term_Ref ObjectID="39518"/>
    <cge:TPSR_Ref TObjectID="27920"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-183484" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1265.000000 -171.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183484" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27920"/>
     <cge:Term_Ref ObjectID="39518"/>
    <cge:TPSR_Ref TObjectID="27920"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-183485" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1265.000000 -171.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183485" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27920"/>
     <cge:Term_Ref ObjectID="39518"/>
    <cge:TPSR_Ref TObjectID="27920"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-183486" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1265.000000 -171.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27920"/>
     <cge:Term_Ref ObjectID="39518"/>
    <cge:TPSR_Ref TObjectID="27920"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-183458" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 851.000000 -732.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183458" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27971"/>
     <cge:Term_Ref ObjectID="39623"/>
    <cge:TPSR_Ref TObjectID="27971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-183459" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 850.000000 -711.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="183459" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27971"/>
     <cge:Term_Ref ObjectID="39620"/>
    <cge:TPSR_Ref TObjectID="27971"/></metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-183570">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 734.241796 -982.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27904" ObjectName="SW-DY_NSB.DY_NSB_3316SW"/>
     <cge:Meas_Ref ObjectId="183570"/>
    <cge:TPSR_Ref TObjectID="27904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183571">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 734.241796 -857.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27905" ObjectName="SW-DY_NSB.DY_NSB_3311SW"/>
     <cge:Meas_Ref ObjectId="183571"/>
    <cge:TPSR_Ref TObjectID="27905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 575.971879 -1089.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183603">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 735.241796 -774.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27911" ObjectName="SW-DY_NSB.DY_NSB_3011SW"/>
     <cge:Meas_Ref ObjectId="183603"/>
    <cge:TPSR_Ref TObjectID="27911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183573">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 649.152542 -967.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27907" ObjectName="SW-DY_NSB.DY_NSB_33160SW"/>
     <cge:Meas_Ref ObjectId="183573"/>
    <cge:TPSR_Ref TObjectID="27907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183572">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 648.152542 -1031.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27906" ObjectName="SW-DY_NSB.DY_NSB_33167SW"/>
     <cge:Meas_Ref ObjectId="183572"/>
    <cge:TPSR_Ref TObjectID="27906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183574">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 648.152542 -905.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27908" ObjectName="SW-DY_NSB.DY_NSB_33117SW"/>
     <cge:Meas_Ref ObjectId="183574"/>
    <cge:TPSR_Ref TObjectID="27908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183607">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 734.000000 -619.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27915" ObjectName="SW-DY_NSB.DY_NSB_001XC1"/>
     <cge:Meas_Ref ObjectId="183607"/>
    <cge:TPSR_Ref TObjectID="27915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183607">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 734.000000 -551.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27914" ObjectName="SW-DY_NSB.DY_NSB_001XC"/>
     <cge:Meas_Ref ObjectId="183607"/>
    <cge:TPSR_Ref TObjectID="27914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183604">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 649.152542 -753.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27912" ObjectName="SW-DY_NSB.DY_NSB_30117SW"/>
     <cge:Meas_Ref ObjectId="183604"/>
    <cge:TPSR_Ref TObjectID="27912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183779">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.000000 -481.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27943" ObjectName="SW-DY_NSB.DY_NSB_051XC"/>
     <cge:Meas_Ref ObjectId="183779"/>
    <cge:TPSR_Ref TObjectID="27943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183779">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.000000 -413.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27944" ObjectName="SW-DY_NSB.DY_NSB_051XC1"/>
     <cge:Meas_Ref ObjectId="183779"/>
    <cge:TPSR_Ref TObjectID="27944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183780">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.761290 -272.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27945" ObjectName="SW-DY_NSB.DY_NSB_0516SW"/>
     <cge:Meas_Ref ObjectId="183780"/>
    <cge:TPSR_Ref TObjectID="27945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183781">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 327.761290 -350.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27946" ObjectName="SW-DY_NSB.DY_NSB_05167SW"/>
     <cge:Meas_Ref ObjectId="183781"/>
    <cge:TPSR_Ref TObjectID="27946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183725">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 823.000000 -474.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27933" ObjectName="SW-DY_NSB.DY_NSB_033XC"/>
     <cge:Meas_Ref ObjectId="183725"/>
    <cge:TPSR_Ref TObjectID="27933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183725">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 823.000000 -406.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27934" ObjectName="SW-DY_NSB.DY_NSB_033XC1"/>
     <cge:Meas_Ref ObjectId="183725"/>
    <cge:TPSR_Ref TObjectID="27934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183726">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 823.761290 -265.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27935" ObjectName="SW-DY_NSB.DY_NSB_0336SW"/>
     <cge:Meas_Ref ObjectId="183726"/>
    <cge:TPSR_Ref TObjectID="27935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183727">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 871.761290 -343.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27936" ObjectName="SW-DY_NSB.DY_NSB_03367SW"/>
     <cge:Meas_Ref ObjectId="183727"/>
    <cge:TPSR_Ref TObjectID="27936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183698">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1030.000000 -476.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27928" ObjectName="SW-DY_NSB.DY_NSB_032XC"/>
     <cge:Meas_Ref ObjectId="183698"/>
    <cge:TPSR_Ref TObjectID="27928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183698">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1030.000000 -408.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27929" ObjectName="SW-DY_NSB.DY_NSB_032XC1"/>
     <cge:Meas_Ref ObjectId="183698"/>
    <cge:TPSR_Ref TObjectID="27929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183699">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1030.761290 -267.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27930" ObjectName="SW-DY_NSB.DY_NSB_0326SW"/>
     <cge:Meas_Ref ObjectId="183699"/>
    <cge:TPSR_Ref TObjectID="27930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183700">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1078.761290 -345.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27931" ObjectName="SW-DY_NSB.DY_NSB_03267SW"/>
     <cge:Meas_Ref ObjectId="183700"/>
    <cge:TPSR_Ref TObjectID="27931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183677">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1222.000000 -502.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27921" ObjectName="SW-DY_NSB.DY_NSB_031XC"/>
     <cge:Meas_Ref ObjectId="183677"/>
    <cge:TPSR_Ref TObjectID="27921"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183677">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1222.000000 -434.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27922" ObjectName="SW-DY_NSB.DY_NSB_031XC1"/>
     <cge:Meas_Ref ObjectId="183677"/>
    <cge:TPSR_Ref TObjectID="27922"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183678">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1222.761290 -319.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27923" ObjectName="SW-DY_NSB.DY_NSB_0316SW"/>
     <cge:Meas_Ref ObjectId="183678"/>
    <cge:TPSR_Ref TObjectID="27923"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183679">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1275.152542 -427.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27924" ObjectName="SW-DY_NSB.DY_NSB_03167SW"/>
     <cge:Meas_Ref ObjectId="183679"/>
    <cge:TPSR_Ref TObjectID="27924"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183680">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1275.152542 -307.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27925" ObjectName="SW-DY_NSB.DY_NSB_03160SW"/>
     <cge:Meas_Ref ObjectId="183680"/>
    <cge:TPSR_Ref TObjectID="27925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183681">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1275.152542 -192.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27926" ObjectName="SW-DY_NSB.DY_NSB_03100SW"/>
     <cge:Meas_Ref ObjectId="183681"/>
    <cge:TPSR_Ref TObjectID="27926"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183833">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -92.000000 -481.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27953" ObjectName="SW-DY_NSB.DY_NSB_053XC"/>
     <cge:Meas_Ref ObjectId="183833"/>
    <cge:TPSR_Ref TObjectID="27953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183833">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -92.000000 -413.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27954" ObjectName="SW-DY_NSB.DY_NSB_053XC1"/>
     <cge:Meas_Ref ObjectId="183833"/>
    <cge:TPSR_Ref TObjectID="27954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183834">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -91.238710 -272.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27955" ObjectName="SW-DY_NSB.DY_NSB_0536SW"/>
     <cge:Meas_Ref ObjectId="183834"/>
    <cge:TPSR_Ref TObjectID="27955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183835">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -43.238710 -350.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27956" ObjectName="SW-DY_NSB.DY_NSB_05367SW"/>
     <cge:Meas_Ref ObjectId="183835"/>
    <cge:TPSR_Ref TObjectID="27956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183806">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 87.000000 -483.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27948" ObjectName="SW-DY_NSB.DY_NSB_052XC"/>
     <cge:Meas_Ref ObjectId="183806"/>
    <cge:TPSR_Ref TObjectID="27948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183806">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 87.000000 -415.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27949" ObjectName="SW-DY_NSB.DY_NSB_052XC1"/>
     <cge:Meas_Ref ObjectId="183806"/>
    <cge:TPSR_Ref TObjectID="27949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183807">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 87.761290 -274.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27950" ObjectName="SW-DY_NSB.DY_NSB_0526SW"/>
     <cge:Meas_Ref ObjectId="183807"/>
    <cge:TPSR_Ref TObjectID="27950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183808">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 135.761290 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27951" ObjectName="SW-DY_NSB.DY_NSB_05267SW"/>
     <cge:Meas_Ref ObjectId="183808"/>
    <cge:TPSR_Ref TObjectID="27951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183860">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -298.000000 -505.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27958" ObjectName="SW-DY_NSB.DY_NSB_054XC"/>
     <cge:Meas_Ref ObjectId="183860"/>
    <cge:TPSR_Ref TObjectID="27958"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183860">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -298.000000 -437.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27959" ObjectName="SW-DY_NSB.DY_NSB_054XC1"/>
     <cge:Meas_Ref ObjectId="183860"/>
    <cge:TPSR_Ref TObjectID="27959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183861">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -297.238710 -322.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27960" ObjectName="SW-DY_NSB.DY_NSB_0546SW"/>
     <cge:Meas_Ref ObjectId="183861"/>
    <cge:TPSR_Ref TObjectID="27960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183862">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -244.847458 -430.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27961" ObjectName="SW-DY_NSB.DY_NSB_05467SW"/>
     <cge:Meas_Ref ObjectId="183862"/>
    <cge:TPSR_Ref TObjectID="27961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183863">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -244.847458 -310.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27962" ObjectName="SW-DY_NSB.DY_NSB_05460SW"/>
     <cge:Meas_Ref ObjectId="183863"/>
    <cge:TPSR_Ref TObjectID="27962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183864">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -244.847458 -195.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27963" ObjectName="SW-DY_NSB.DY_NSB_05400SW"/>
     <cge:Meas_Ref ObjectId="183864"/>
    <cge:TPSR_Ref TObjectID="27963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183752">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 604.000000 -478.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27938" ObjectName="SW-DY_NSB.DY_NSB_034XC"/>
     <cge:Meas_Ref ObjectId="183752"/>
    <cge:TPSR_Ref TObjectID="27938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183752">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 604.000000 -410.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27939" ObjectName="SW-DY_NSB.DY_NSB_034XC1"/>
     <cge:Meas_Ref ObjectId="183752"/>
    <cge:TPSR_Ref TObjectID="27939"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183753">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 604.761290 -269.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27940" ObjectName="SW-DY_NSB.DY_NSB_0346SW"/>
     <cge:Meas_Ref ObjectId="183753"/>
    <cge:TPSR_Ref TObjectID="27940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183754">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 652.761290 -347.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27941" ObjectName="SW-DY_NSB.DY_NSB_03467SW"/>
     <cge:Meas_Ref ObjectId="183754"/>
    <cge:TPSR_Ref TObjectID="27941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183667">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1030.000000 -573.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27916" ObjectName="SW-DY_NSB.DY_NSB_1XC"/>
     <cge:Meas_Ref ObjectId="183667"/>
    <cge:TPSR_Ref TObjectID="27916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183949">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1204.000000 -557.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27969" ObjectName="SW-DY_NSB.DY_NSB_3XC"/>
     <cge:Meas_Ref ObjectId="183949"/>
    <cge:TPSR_Ref TObjectID="27969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183949">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1204.000000 -640.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27970" ObjectName="SW-DY_NSB.DY_NSB_3XC1"/>
     <cge:Meas_Ref ObjectId="183949"/>
    <cge:TPSR_Ref TObjectID="27970"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183671">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 88.000000 -571.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27918" ObjectName="SW-DY_NSB.DY_NSB_2XC"/>
     <cge:Meas_Ref ObjectId="183671"/>
    <cge:TPSR_Ref TObjectID="27918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183880">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 310.000000 -624.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27965" ObjectName="SW-DY_NSB.DY_NSB_012XC"/>
     <cge:Meas_Ref ObjectId="183880"/>
    <cge:TPSR_Ref TObjectID="27965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183880">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 310.000000 -556.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27966" ObjectName="SW-DY_NSB.DY_NSB_012XC1"/>
     <cge:Meas_Ref ObjectId="183880"/>
    <cge:TPSR_Ref TObjectID="27966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183881">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 571.000000 -622.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27967" ObjectName="SW-DY_NSB.DY_NSB_0121XC"/>
     <cge:Meas_Ref ObjectId="183881"/>
    <cge:TPSR_Ref TObjectID="27967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183881">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 571.000000 -576.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27968" ObjectName="SW-DY_NSB.DY_NSB_0121XC1"/>
     <cge:Meas_Ref ObjectId="183881"/>
    <cge:TPSR_Ref TObjectID="27968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183601">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 315.152542 -823.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27909" ObjectName="SW-DY_NSB.DY_NSB_3121SW"/>
     <cge:Meas_Ref ObjectId="183601"/>
    <cge:TPSR_Ref TObjectID="27909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-183602">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 312.152542 -781.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27910" ObjectName="SW-DY_NSB.DY_NSB_31217SW"/>
     <cge:Meas_Ref ObjectId="183602"/>
    <cge:TPSR_Ref TObjectID="27910"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-605" y="-1254"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-654" y="-1271"/></g>
   <g href="35kV南山坝变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="78" x="-702" y="-845"/></g>
   <g href="35kV南山坝变DY_NSB_031间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1242" y="-491"/></g>
   <g href="35kV南山坝变DY_NSB_331间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="758" y="-948"/></g>
   <g href="35kV南山坝变DY_NSB_054间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="-279" y="-494"/></g>
   <g href="35kV南山坝变DY_NSB_053间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="-73" y="-470"/></g>
   <g href="35kV南山坝变DY_NSB_052间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="107" y="-472"/></g>
   <g href="35kV南山坝变DY_NSB_051间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="299" y="-470"/></g>
   <g href="35kV南山坝变DY_NSB_034间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="624" y="-467"/></g>
   <g href="35kV南山坝变DY_NSB_033间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="843" y="-463"/></g>
   <g href="35kV南山坝变DY_NSB_032间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1050" y="-465"/></g>
   <g href="35kV南山坝变DY_NSB_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="283" y="-613"/></g>
   <g href="cx_配调_配网接线图35_大姚.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-457" y="-1235"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-457" y="-1270"/></g>
   <g href="AVC南山坝站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="-405" y="-1184"/></g>
   <g href="35kV南山坝变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="775" y="-764"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_18dc4c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 705.000000 -1167.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1848aa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 284.000000 -326.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1878b70">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 312.676442 -209.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18b7f30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 828.000000 -319.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1860540">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 856.676442 -202.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18d7ab0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1035.000000 -321.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1875690">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1063.676442 -204.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_189b890">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1227.000000 -366.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17fcb10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -87.000000 -326.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1815740">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -58.323558 -209.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17ab2b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 92.000000 -328.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17aef20">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 120.676442 -211.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17dd1d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -293.000000 -369.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_177a780">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 609.000000 -323.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_177e3f0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 637.676442 -206.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_180cd10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1033.000000 -636.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18118e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1065.000000 -632.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1753130">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1207.000000 -587.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1756a90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1209.000000 -687.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17589f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1239.000000 -685.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_175ca90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 91.000000 -634.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17b1700">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 123.000000 -630.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="DY_NSB"/>
</svg>