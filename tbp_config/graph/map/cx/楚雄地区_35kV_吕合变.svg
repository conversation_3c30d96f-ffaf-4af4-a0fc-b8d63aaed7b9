<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-61" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3069 -1314 2492 1400">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="10" x2="10" y1="58" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="45" x2="45" y1="19" y2="53"/>
    <rect height="26" stroke-width="0.398039" width="12" x="4" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="34" x2="10" y1="61" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="102" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="34" x2="10" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="34" x2="34" y1="9" y2="18"/>
    <polyline arcFlag="1" points="34,29 35,29 35,29 36,29 37,29 37,28 38,28 39,27 39,27 39,26 40,25 40,25 40,24 40,23 40,22 40,22 39,21 39,20 39,20 38,19 37,19 37,18 36,18 35,18 35,18 34,18 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="34,40 35,40 35,40 36,40 37,40 37,39 38,39 39,38 39,38 39,37 40,36 40,36 40,35 40,34 40,33 40,33 39,32 39,31 39,31 38,30 37,30 37,29 36,29 35,29 35,29 34,29 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="34,51 35,51 35,51 36,51 37,51 37,50 38,50 39,49 39,49 39,48 40,47 40,47 40,46 40,45 40,44 40,44 39,43 39,42 39,42 38,41 37,41 37,40 36,40 35,40 35,40 34,40 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="34" x2="34" y1="51" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="20" x2="0" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="20" x2="0" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.529576" x1="10" x2="10" y1="2" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="56" x2="56" y1="57" y2="70"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="91" x2="91" y1="18" y2="52"/>
    <rect height="26" stroke-width="0.398039" width="12" x="50" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="80" x2="56" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="80" x2="56" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="80" x2="80" y1="8" y2="17"/>
    <polyline arcFlag="1" points="80,28 81,28 81,28 82,28 83,28 83,27 84,27 85,26 85,26 85,25 86,24 86,24 86,23 86,22 86,21 86,21 85,20 85,19 85,19 84,18 83,18 83,17 82,17 81,17 81,17 80,17 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="80,39 81,39 81,39 82,39 83,39 83,38 84,38 85,37 85,37 85,36 86,35 86,35 86,34 86,33 86,32 86,32 85,31 85,30 85,30 84,29 83,29 83,28 82,28 81,28 81,28 80,28 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="80,50 81,50 81,50 82,50 83,50 83,49 84,49 85,48 85,48 85,47 86,46 86,46 86,45 86,44 86,43 86,43 85,42 85,41 85,41 84,40 83,40 83,39 82,39 81,39 81,39 80,39 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="80" x2="80" y1="50" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="66" x2="46" y1="56" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="66" x2="46" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.518543" x1="56" x2="56" y1="2" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="102" x2="102" y1="56" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="137" x2="137" y1="17" y2="51"/>
    <rect height="26" stroke-width="0.398039" width="12" x="96" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="126" x2="102" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="126" x2="102" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="126" x2="126" y1="7" y2="16"/>
    <polyline arcFlag="1" points="126,27 127,27 127,27 128,27 129,27 129,26 130,26 131,25 131,25 131,24 132,23 132,23 132,22 132,21 132,20 132,20 131,19 131,18 131,18 130,17 129,17 129,16 128,16 127,16 127,16 126,16 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="126,38 127,38 127,38 128,38 129,38 129,37 130,37 131,36 131,36 131,35 132,34 132,34 132,33 132,32 132,31 132,31 131,30 131,29 131,29 130,28 129,28 129,27 128,27 127,27 127,27 126,27 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="126,49 127,49 127,49 128,49 129,49 129,48 130,48 131,47 131,47 131,46 132,45 132,45 132,44 132,43 132,42 132,42 131,41 131,40 131,40 130,39 129,39 129,38 128,38 127,38 127,38 126,38 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="126" x2="126" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="112" x2="92" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="112" x2="92" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.496477" x1="102" x2="102" y1="3" y2="47"/>
   </symbol>
   <symbol id="capacitor:shape15">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="45" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="47" y1="16" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="41" x2="53" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="51" x2="43" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="50" x2="47" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="11" x2="11" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="46" x2="46" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="47" x2="47" y1="54" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="46" x2="12" y1="55" y2="55"/>
    <rect height="23" stroke-width="0.398039" width="12" x="22" y="27"/>
    <rect height="24" stroke-width="0.398039" width="12" x="41" y="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="88" y2="21"/>
    <polyline arcFlag="1" points="11,36 10,36 9,36 9,37 8,37 8,37 7,38 7,38 6,39 6,39 6,40 6,40 5,41 5,42 5,42 6,43 6,44 6,44 6,45 7,45 7,46 8,46 8,47 9,47 9,47 10,47 11,47 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,25 10,25 9,25 9,25 8,26 8,26 7,26 7,27 6,27 6,28 6,29 6,29 5,30 5,31 5,31 6,32 6,33 6,33 6,34 7,34 7,35 8,35 8,35 9,36 9,36 10,36 11,36 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,15 10,15 9,15 9,15 8,15 8,16 7,16 7,17 6,17 6,18 6,18 6,19 5,20 5,20 5,21 6,22 6,22 6,23 6,23 7,24 7,24 8,25 8,25 9,25 9,26 10,26 11,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="27" x2="27" y1="99" y2="107"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="40" x2="28" y1="87" y2="87"/>
    <polyline arcFlag="1" points="27,100 25,100 23,99 22,99 20,98 19,97 17,96 16,94 15,92 15,91 14,89 14,87 14,85 15,83 15,82 16,80 17,79 19,77 20,76 22,75 23,75 25,74 27,74 29,74 31,75 32,75 34,76 35,77 37,79 38,80 39,82 39,83 40,85 40,87 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape127">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="29" x2="32" y1="2" y2="2"/>
    <circle cx="66" cy="38" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.141723" x1="68" x2="70" y1="37" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.163265" x1="71" x2="66" y1="39" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.141723" x1="66" x2="68" y1="39" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.141723" x1="43" x2="43" y1="34" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.141723" x1="41" x2="43" y1="39" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.141723" x1="43" x2="45" y1="37" y2="39"/>
    <ellipse cx="54" cy="42" fillStyle="0" rx="8" ry="7" stroke-width="0.141723"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.141723" x1="54" x2="54" y1="29" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.141723" x1="54" x2="56" y1="32" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.141723" x1="52" x2="54" y1="34" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.141723" x1="54" x2="54" y1="41" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.141723" x1="54" x2="56" y1="44" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.141723" x1="51" x2="54" y1="46" y2="44"/>
    <ellipse cx="54" cy="33" fillStyle="0" rx="8" ry="7" stroke-width="0.141723"/>
    <circle cx="42" cy="37" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="54" x2="54" y1="84" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="84" y2="94"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="42" y2="17"/>
    <rect height="27" stroke-width="0.416667" width="14" x="47" y="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="54" y1="84" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="84" y2="47"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="53" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="37" x2="25" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="31" x2="31" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="27" x2="35" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="54" x2="54" y1="26" y2="16"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape85">
    <circle cx="8" cy="17" fillStyle="0" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="20" y2="20"/>
    <rect height="27" stroke-width="0.416667" width="14" x="1" y="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="73" y2="25"/>
    <circle cx="8" cy="8" fillStyle="0" r="7.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape164">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape22">
    <polyline DF8003:Layer="PUBLIC" points="18,1 18,16 30,8 18,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="55" x2="60" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="46" x2="51" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="38" x2="43" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="30" x2="35" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="86" x2="72" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="72,1 72,16 60,8 72,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="4" x2="18" y1="9" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape99">
    <polyline DF8003:Layer="PUBLIC" points="25,29 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="8" y1="46" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="41" y1="46" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="46" y2="46"/>
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.5"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="7" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="24" y1="19" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="15" y1="24" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="24" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="17" y2="24"/>
    <circle cx="17" cy="17" fillStyle="0" r="16" stroke-width="1.0625"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="17" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape174">
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="14" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="39" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="7" y2="14"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape21_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="28" x2="36" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="25" x2="25" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="25" x2="9" y1="23" y2="23"/>
   </symbol>
   <symbol id="switch2:shape21_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="26" x2="26" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="6" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="10" x2="10" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="26" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="3" x2="3" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="36" x2="36" y1="34" y2="14"/>
   </symbol>
   <symbol id="switch2:shape21-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="28" x2="36" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="25" x2="25" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="25" x2="9" y1="23" y2="23"/>
   </symbol>
   <symbol id="switch2:shape21-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="26" x2="26" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="6" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="10" x2="10" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="26" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="3" x2="3" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="36" x2="36" y1="34" y2="14"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape45_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1.13333"/>
    <polyline points="58,100 64,100 " stroke-width="1.13333"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1.13333"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape55_0">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,44 6,44 6,73 " stroke-width="1"/>
    <circle cx="31" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="56" y2="98"/>
    <polyline DF8003:Layer="PUBLIC" points="31,87 25,74 37,74 31,87 31,86 31,87 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="79" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="31" y1="49" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="44" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="44" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="44" y2="39"/>
   </symbol>
   <symbol id="transformer2:shape55_1">
    <circle cx="31" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="20" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="20" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="20" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="voltageTransformer:shape65">
    <ellipse cx="19" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="46" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="9" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="8" x2="8" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="11" x2="8" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="5" x2="8" y1="18" y2="20"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="5" x2="9" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="5" x2="9" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="9" x2="9" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="41" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="30" y2="38"/>
    <rect height="13" stroke-width="1" width="7" x="32" y="17"/>
    <ellipse cx="8" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <polyline points="27,8 35,8 35,18 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="38" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="40" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="18" y2="20"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1895600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18966e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1897170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18980c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1899320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1899f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_189a960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_189b420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_189caa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_189caa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_189e390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_189e390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18a0120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18a0120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_18a1140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18a2db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c08fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c09d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c0a450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c0bc70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c0c5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c0cd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c0d4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c0e5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c0ef40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c0fa30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c103f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c119c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c12440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c13640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c142b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c1a7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c1b5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1c15c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1c17180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1410" width="2502" x="3064" y="-1319"/>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3682,-19 3683,-19 3683,-19 3684,-19 3684,-19 3685,-19 3685,-20 3685,-20 3686,-20 3686,-20 3686,-20 3686,-20 3686,-21 3686,-21 3686,-21 3686,-21 3686,-21 3686,-22 3685,-22 3685,-22 3685,-22 3684,-22 3684,-22 3683,-22 3683,-22 3682,-22 " stroke="rgb(60,120,255)" stroke-width="0.100565"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3682,-22 3683,-22 3683,-22 3684,-22 3684,-23 3685,-23 3685,-23 3685,-23 3686,-23 3686,-23 3686,-23 3686,-24 3686,-24 3686,-24 3686,-24 3686,-25 3686,-25 3686,-25 3685,-25 3685,-25 3685,-25 3684,-25 3684,-26 3683,-26 3683,-26 3682,-26 " stroke="rgb(60,120,255)" stroke-width="0.100565"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3682,-26 3683,-26 3683,-26 3684,-26 3684,-26 3685,-26 3685,-26 3685,-26 3686,-26 3686,-27 3686,-27 3686,-27 3686,-27 3686,-27 3686,-28 3686,-28 3686,-28 3686,-28 3685,-28 3685,-28 3685,-29 3684,-29 3684,-29 3683,-29 3683,-29 3682,-29 " stroke="rgb(60,120,255)" stroke-width="0.100565"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3696,-19 3695,-19 3695,-19 3695,-19 3694,-19 3694,-19 3693,-20 3693,-20 3693,-20 3692,-20 3692,-20 3692,-20 3692,-21 3692,-21 3692,-21 3692,-21 3692,-21 3693,-22 3693,-22 3693,-22 3694,-22 3694,-22 3695,-22 3695,-22 3695,-22 3696,-22 " stroke="rgb(60,120,255)" stroke-width="0.125706"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3696,-22 3695,-22 3695,-22 3695,-22 3694,-23 3694,-23 3693,-23 3693,-23 3693,-23 3692,-23 3692,-23 3692,-24 3692,-24 3692,-24 3692,-24 3692,-25 3692,-25 3693,-25 3693,-25 3693,-25 3694,-25 3694,-25 3695,-26 3695,-26 3695,-26 3696,-26 " stroke="rgb(60,120,255)" stroke-width="0.125706"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3696,-26 3695,-26 3695,-26 3695,-26 3694,-26 3694,-26 3693,-26 3693,-26 3693,-26 3692,-27 3692,-27 3692,-27 3692,-27 3692,-27 3692,-28 3692,-28 3692,-28 3693,-28 3693,-28 3693,-28 3694,-29 3694,-29 3695,-29 3695,-29 3695,-29 3696,-29 " stroke="rgb(60,120,255)" stroke-width="0.125706"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3682,2 3683,2 3683,2 3684,2 3684,2 3685,2 3685,1 3685,1 3686,1 3686,1 3686,1 3686,1 3686,0 3686,0 3686,0 3686,0 3686,0 3686,-1 3685,-1 3685,-1 3685,-1 3684,-1 3684,-1 3683,-1 3683,-1 3682,-1 " stroke="rgb(60,120,255)" stroke-width="0.100565"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3682,-1 3683,-1 3683,-1 3684,-1 3684,-2 3685,-2 3685,-2 3685,-2 3686,-2 3686,-2 3686,-2 3686,-3 3686,-3 3686,-3 3686,-3 3686,-4 3686,-4 3686,-4 3685,-4 3685,-4 3685,-4 3684,-4 3684,-5 3683,-5 3683,-5 3682,-5 " stroke="rgb(60,120,255)" stroke-width="0.100565"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3682,-5 3683,-5 3683,-5 3684,-5 3684,-5 3685,-5 3685,-5 3685,-5 3686,-5 3686,-6 3686,-6 3686,-6 3686,-6 3686,-6 3686,-7 3686,-7 3686,-7 3686,-7 3685,-7 3685,-7 3685,-8 3684,-8 3684,-8 3683,-8 3683,-8 3682,-8 " stroke="rgb(60,120,255)" stroke-width="0.100565"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3696,2 3695,2 3695,2 3695,2 3694,2 3694,2 3693,1 3693,1 3693,1 3692,1 3692,1 3692,1 3692,0 3692,0 3692,0 3692,0 3692,0 3693,-1 3693,-1 3693,-1 3694,-1 3694,-1 3695,-1 3695,-1 3695,-1 3696,-1 " stroke="rgb(60,120,255)" stroke-width="0.125706"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3696,-1 3695,-1 3695,-1 3695,-1 3694,-2 3694,-2 3693,-2 3693,-2 3693,-2 3692,-2 3692,-2 3692,-3 3692,-3 3692,-3 3692,-3 3692,-4 3692,-4 3693,-4 3693,-4 3693,-4 3694,-4 3694,-4 3695,-5 3695,-5 3695,-5 3696,-5 " stroke="rgb(60,120,255)" stroke-width="0.125706"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3696,-5 3695,-5 3695,-5 3695,-5 3694,-5 3694,-5 3693,-5 3693,-5 3693,-5 3692,-6 3692,-6 3692,-6 3692,-6 3692,-6 3692,-7 3692,-7 3692,-7 3693,-7 3693,-7 3693,-7 3694,-8 3694,-8 3695,-8 3695,-8 3695,-8 3696,-8 " stroke="rgb(60,120,255)" stroke-width="0.125706"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3821" x2="3821" y1="-532" y2="-499"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3821" x2="3888" y1="-499" y2="-499"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4270" x2="4270" y1="-538" y2="-500"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4270" x2="4333" y1="-500" y2="-500"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4574" x2="4574" y1="-532" y2="-498"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4574" x2="4642" y1="-498" y2="-498"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3696" x2="3696" y1="-416" y2="-382"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3696" x2="3760" y1="-416" y2="-416"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3835" x2="3899" y1="-415" y2="-415"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3835" x2="3835" y1="-415" y2="-383"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3954" x2="4018" y1="-416" y2="-416"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3954" x2="3954" y1="-416" y2="-384"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4074" x2="4138" y1="-415" y2="-415"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4074" x2="4074" y1="-415" y2="-383"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4194" x2="4258" y1="-415" y2="-415"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4194" x2="4194" y1="-415" y2="-383"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4748" x2="4748" y1="-417" y2="-385"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4748" x2="4813" y1="-417" y2="-417"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4868" x2="4868" y1="-416" y2="-384"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4868" x2="4933" y1="-416" y2="-416"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4989" x2="4989" y1="-416" y2="-384"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4989" x2="5054" y1="-416" y2="-416"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="5135" x2="5135" y1="-416" y2="-384"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="5135" x2="5200" y1="-416" y2="-416"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="5255" x2="5255" y1="-415" y2="-383"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="5255" x2="5320" y1="-415" y2="-415"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="5401" x2="5401" y1="-415" y2="-383"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="5401" x2="5466" y1="-415" y2="-415"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4333" x2="4381" y1="-391" y2="-391"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4333" x2="4333" y1="-391" y2="-351"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4572" x2="4623" y1="-393" y2="-393"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4572" x2="4572" y1="-393" y2="-353"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4349" x2="4360" y1="-546" y2="-546"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.348454" x1="4360" x2="4360" y1="-537" y2="-554"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.348454" x1="4368" x2="4368" y1="-538" y2="-554"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.35616" x1="4367" x2="4377" y1="-546" y2="-546"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.25" x1="4409" x2="4409" y1="-544" y2="-547"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.39375" x1="4406" x2="4406" y1="-542" y2="-549"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.611465" x1="4403" x2="4403" y1="-551" y2="-540"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.251748" x1="4402" x2="4394" y1="-546" y2="-546"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4478" x2="4467" y1="-543" y2="-543"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.348454" x1="4466" x2="4466" y1="-552" y2="-535"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.348454" x1="4458" x2="4458" y1="-551" y2="-535"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.35616" x1="4460" x2="4450" y1="-543" y2="-543"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.25" x1="4417" x2="4417" y1="-544" y2="-541"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.39375" x1="4422" x2="4422" y1="-547" y2="-540"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.611465" x1="4424" x2="4424" y1="-538" y2="-549"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.251748" x1="4425" x2="4433" y1="-544" y2="-544"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4640" x2="4651" y1="-330" y2="-330"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.348454" x1="4651" x2="4651" y1="-321" y2="-338"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.348454" x1="4659" x2="4659" y1="-322" y2="-338"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.35616" x1="4658" x2="4668" y1="-330" y2="-330"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.25" x1="4700" x2="4700" y1="-328" y2="-331"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.39375" x1="4697" x2="4697" y1="-326" y2="-333"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.611465" x1="4694" x2="4694" y1="-335" y2="-324"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.251748" x1="4693" x2="4685" y1="-330" y2="-330"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4399" x2="4410" y1="-332" y2="-332"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.348454" x1="4410" x2="4410" y1="-323" y2="-340"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.348454" x1="4418" x2="4418" y1="-324" y2="-340"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.35616" x1="4417" x2="4427" y1="-332" y2="-332"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.25" x1="4459" x2="4459" y1="-330" y2="-333"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.39375" x1="4456" x2="4456" y1="-328" y2="-335"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.611465" x1="4453" x2="4453" y1="-337" y2="-326"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.251748" x1="4452" x2="4444" y1="-332" y2="-332"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="3674" x2="3674" y1="-40" y2="19"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1.01266" x1="3689" x2="3689" y1="-29" y2="-19"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1.01266" x1="3689" x2="3689" y1="-8" y2="2"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.992537" x1="3503" x2="3503" y1="-144" y2="-11"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3503" x2="3547" y1="-11" y2="-11"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.0514649" x1="3547" x2="3547" y1="-18" y2="-11"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-42564">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3756.666667 -325.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7065" ObjectName="SW-CX_LH.CX_LH_069BK"/>
     <cge:Meas_Ref ObjectId="42564"/>
    <cge:TPSR_Ref TObjectID="7065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42384">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3894.066667 -325.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7017" ObjectName="SW-CX_LH.CX_LH_061BK"/>
     <cge:Meas_Ref ObjectId="42384"/>
    <cge:TPSR_Ref TObjectID="7017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42402">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4014.466667 -325.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7022" ObjectName="SW-CX_LH.CX_LH_063BK"/>
     <cge:Meas_Ref ObjectId="42402"/>
    <cge:TPSR_Ref TObjectID="7022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42419">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4133.866667 -325.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7027" ObjectName="SW-CX_LH.CX_LH_065BK"/>
     <cge:Meas_Ref ObjectId="42419"/>
    <cge:TPSR_Ref TObjectID="7027"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42437">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.066667 -325.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7032" ObjectName="SW-CX_LH.CX_LH_067BK"/>
     <cge:Meas_Ref ObjectId="42437"/>
    <cge:TPSR_Ref TObjectID="7032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42476">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4807.666667 -322.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7041" ObjectName="SW-CX_LH.CX_LH_062BK"/>
     <cge:Meas_Ref ObjectId="42476"/>
    <cge:TPSR_Ref TObjectID="7041"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42494">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4929.066667 -322.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7046" ObjectName="SW-CX_LH.CX_LH_064BK"/>
     <cge:Meas_Ref ObjectId="42494"/>
    <cge:TPSR_Ref TObjectID="7046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42513">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5049.466667 -322.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7051" ObjectName="SW-CX_LH.CX_LH_066BK"/>
     <cge:Meas_Ref ObjectId="42513"/>
    <cge:TPSR_Ref TObjectID="7051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42529">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5195.866667 -321.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7056" ObjectName="SW-CX_LH.CX_LH_068BK"/>
     <cge:Meas_Ref ObjectId="42529"/>
    <cge:TPSR_Ref TObjectID="7056"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42547">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5316.066667 -321.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7061" ObjectName="SW-CX_LH.CX_LH_072BK"/>
     <cge:Meas_Ref ObjectId="42547"/>
    <cge:TPSR_Ref TObjectID="7061"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42577">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5461.866667 -321.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7069" ObjectName="SW-CX_LH.CX_LH_079BK"/>
     <cge:Meas_Ref ObjectId="42577"/>
    <cge:TPSR_Ref TObjectID="7069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42642">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.136318 -807.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7081" ObjectName="SW-CX_LH.CX_LH_302BK"/>
     <cge:Meas_Ref ObjectId="42642"/>
    <cge:TPSR_Ref TObjectID="7081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42641">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.136318 -544.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7080" ObjectName="SW-CX_LH.CX_LH_002BK"/>
     <cge:Meas_Ref ObjectId="42641"/>
    <cge:TPSR_Ref TObjectID="7080"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42628">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3885.000000 -802.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7075" ObjectName="SW-CX_LH.CX_LH_301BK"/>
     <cge:Meas_Ref ObjectId="42628"/>
    <cge:TPSR_Ref TObjectID="7075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42627">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3885.000000 -544.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7074" ObjectName="SW-CX_LH.CX_LH_001BK"/>
     <cge:Meas_Ref ObjectId="42627"/>
    <cge:TPSR_Ref TObjectID="7074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42346">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3956.333333 -972.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7007" ObjectName="SW-CX_LH.CX_LH_361BK"/>
     <cge:Meas_Ref ObjectId="42346"/>
    <cge:TPSR_Ref TObjectID="7007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42366">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4518.333333 -971.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7012" ObjectName="SW-CX_LH.CX_LH_362BK"/>
     <cge:Meas_Ref ObjectId="42366"/>
    <cge:TPSR_Ref TObjectID="7012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187366">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4800.333333 -1032.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11150" ObjectName="SW-CX_LH.CX_LH_363BK"/>
     <cge:Meas_Ref ObjectId="187366"/>
    <cge:TPSR_Ref TObjectID="11150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42456">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4394.000000 -552.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7036" ObjectName="SW-CX_LH.CX_LH_012BK"/>
     <cge:Meas_Ref ObjectId="42456"/>
    <cge:TPSR_Ref TObjectID="7036"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298242">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4317.000000 -982.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46188" ObjectName="SW-CX_LH.CX_LH_312BK"/>
     <cge:Meas_Ref ObjectId="298242"/>
    <cge:TPSR_Ref TObjectID="46188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298344">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3627.000000 -325.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46196" ObjectName="SW-CX_LH.CX_LH_083BK"/>
     <cge:Meas_Ref ObjectId="298344"/>
    <cge:TPSR_Ref TObjectID="46196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298327">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3538.000000 -333.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46192" ObjectName="SW-CX_LH.CX_LH_082BK"/>
     <cge:Meas_Ref ObjectId="298327"/>
    <cge:TPSR_Ref TObjectID="46192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298299">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3419.000000 -329.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46183" ObjectName="SW-CX_LH.CX_LH_081BK"/>
     <cge:Meas_Ref ObjectId="298299"/>
    <cge:TPSR_Ref TObjectID="46183"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2135e00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3805.000000 -1086.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21b5de0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5026.000000 -1076.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="CX_LH" flowDrawDirect="1" flowShape="0" id="AC-35kV.donglvdaTlh_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4527,-1174 4527,-1206 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38054" ObjectName="AC-35kV.donglvdaTlh_line"/>
    <cge:TPSR_Ref TObjectID="38054_SS-61"/></metadata>
   <polyline fill="none" opacity="0" points="4527,-1174 4527,-1206 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_LH" endPointId="0" endStationName="PAS_XN" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_lczyb" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4809,-1260 4809,-1294 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="39638" ObjectName="AC-35kV.LN_lczyb"/>
    <cge:TPSR_Ref TObjectID="39638_SS-61"/></metadata>
   <polyline fill="none" opacity="0" points="4809,-1260 4809,-1294 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_LJT" endPointId="0" endStationName="CX_LH" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_luolv" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3965,-1171 3965,-1208 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="46071" ObjectName="AC-35kV.LN_luolv"/>
    <cge:TPSR_Ref TObjectID="46071_SS-61"/></metadata>
   <polyline fill="none" opacity="0" points="3965,-1171 3965,-1208 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_LH.072Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5316.000000 -29.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34485" ObjectName="EC-CX_LH.072Ld"/>
    <cge:TPSR_Ref TObjectID="34485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_LH.068Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5196.000000 -29.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34484" ObjectName="EC-CX_LH.068Ld"/>
    <cge:TPSR_Ref TObjectID="34484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_LH.066Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5049.000000 -30.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34483" ObjectName="EC-CX_LH.066Ld"/>
    <cge:TPSR_Ref TObjectID="34483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_LH.064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4929.000000 -30.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34482" ObjectName="EC-CX_LH.064Ld"/>
    <cge:TPSR_Ref TObjectID="34482"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_LH.062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4808.000000 -14.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34481" ObjectName="EC-CX_LH.062Ld"/>
    <cge:TPSR_Ref TObjectID="34481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_LH.061Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3895.000000 -29.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34477" ObjectName="EC-CX_LH.061Ld"/>
    <cge:TPSR_Ref TObjectID="34477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_LH.063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 -29.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34478" ObjectName="EC-CX_LH.063Ld"/>
    <cge:TPSR_Ref TObjectID="34478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_LH.065Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4135.000000 -29.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34479" ObjectName="EC-CX_LH.065Ld"/>
    <cge:TPSR_Ref TObjectID="34479"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_LH.067Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4255.000000 -29.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34480" ObjectName="EC-CX_LH.067Ld"/>
    <cge:TPSR_Ref TObjectID="34480"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_LH.081Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3419.000000 -3.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46207" ObjectName="EC-CX_LH.081Ld"/>
    <cge:TPSR_Ref TObjectID="46207"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_211c020" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3671.000000 -376.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_211cab0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3809.000000 -377.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_211d540" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3790.000000 -526.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_211dfd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3929.000000 -377.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_211ea60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4049.000000 -377.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_211f4f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4169.000000 -377.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_211ff80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4842.000000 -378.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2120a10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4962.000000 -378.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21214a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5109.000000 -377.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2121f30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5229.000000 -377.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21229c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5375.000000 -377.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2123450" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4548.136318 -526.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2123ee0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4722.000000 -378.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_212fac0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4245.000000 -532.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2180290" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4310.000000 -345.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2181fe0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.000000 -347.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21a1bb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3878.000000 -1078.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21a2740" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4426.000000 -1081.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21aae90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4714.000000 -1158.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21abb80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4715.000000 -1093.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21ac870" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4714.000000 -998.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21af5d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3750.000000 -997.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41c3210" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4966.000000 -986.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41d11c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4271.000000 -1059.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41d1c50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4408.000000 -1056.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_421e3f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3668.000000 33.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_421f560" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3660.000000 -199.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4237550" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3570.000000 -202.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4240c10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3497.000000 -184.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_42416a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3521.000000 -184.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4247990" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3448.000000 -188.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_424db40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3449.000000 -38.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4258680" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3748.000000 -923.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_1e2dd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-396 3766,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7062@0" ObjectIDZND0="7065@x" ObjectIDZND1="7063@x" Pin0InfoVect0LinkObjId="SW-42564_0" Pin0InfoVect1LinkObjId="SW-42562_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42561_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-396 3766,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c764d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-382 3766,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7063@x" ObjectIDND1="7062@x" ObjectIDZND0="7065@1" Pin0InfoVect0LinkObjId="SW-42564_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42562_0" Pin1InfoVect1LinkObjId="SW-42561_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-382 3766,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c78280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3689,-382 3704,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_211c020@0" ObjectIDZND0="7063@0" Pin0InfoVect0LinkObjId="SW-42562_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_211c020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3689,-382 3704,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c5fc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3740,-382 3766,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7063@1" ObjectIDZND0="7065@x" ObjectIDZND1="7062@x" Pin0InfoVect0LinkObjId="SW-42564_0" Pin0InfoVect1LinkObjId="SW-42561_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42562_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3740,-382 3766,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c5f880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3903,-396 3903,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7013@0" ObjectIDZND0="7017@x" ObjectIDZND1="7014@x" Pin0InfoVect0LinkObjId="SW-42384_0" Pin0InfoVect1LinkObjId="SW-42381_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3903,-396 3903,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e28480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3903,-383 3903,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7014@x" ObjectIDND1="7013@x" ObjectIDZND0="7017@1" Pin0InfoVect0LinkObjId="SW-42384_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42381_0" Pin1InfoVect1LinkObjId="SW-42380_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3903,-383 3903,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1660a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3827,-383 3842,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_211cab0@0" ObjectIDZND0="7014@0" Pin0InfoVect0LinkObjId="SW-42381_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_211cab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3827,-383 3842,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e79120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3878,-383 3903,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7014@1" ObjectIDZND0="7017@x" ObjectIDZND1="7013@x" Pin0InfoVect0LinkObjId="SW-42384_0" Pin0InfoVect1LinkObjId="SW-42380_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42381_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3878,-383 3903,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f5bf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-396 4023,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7018@0" ObjectIDZND0="7022@x" ObjectIDZND1="7019@x" Pin0InfoVect0LinkObjId="SW-42402_0" Pin0InfoVect1LinkObjId="SW-42399_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42398_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-396 4023,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1651ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-383 4023,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7019@x" ObjectIDND1="7018@x" ObjectIDZND0="7022@1" Pin0InfoVect0LinkObjId="SW-42402_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42399_0" Pin1InfoVect1LinkObjId="SW-42398_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-383 4023,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1651ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3947,-383 3962,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_211dfd0@0" ObjectIDZND0="7019@0" Pin0InfoVect0LinkObjId="SW-42399_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_211dfd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3947,-383 3962,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c62a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3998,-383 4023,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7019@1" ObjectIDZND0="7022@x" ObjectIDZND1="7018@x" Pin0InfoVect0LinkObjId="SW-42402_0" Pin0InfoVect1LinkObjId="SW-42398_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42399_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3998,-383 4023,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2028df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-396 4143,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7023@0" ObjectIDZND0="7027@x" ObjectIDZND1="7024@x" Pin0InfoVect0LinkObjId="SW-42419_0" Pin0InfoVect1LinkObjId="SW-42416_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42415_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-396 4143,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2029010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-383 4143,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7024@x" ObjectIDND1="7023@x" ObjectIDZND0="7027@1" Pin0InfoVect0LinkObjId="SW-42419_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42416_0" Pin1InfoVect1LinkObjId="SW-42415_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-383 4143,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2029230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-383 4082,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_211ea60@0" ObjectIDZND0="7024@0" Pin0InfoVect0LinkObjId="SW-42416_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_211ea60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-383 4082,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2029450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4118,-383 4143,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7024@1" ObjectIDZND0="7027@x" ObjectIDZND1="7023@x" Pin0InfoVect0LinkObjId="SW-42419_0" Pin0InfoVect1LinkObjId="SW-42415_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42416_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4118,-383 4143,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2006af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4263,-396 4263,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7028@0" ObjectIDZND0="7032@x" ObjectIDZND1="7029@x" Pin0InfoVect0LinkObjId="SW-42437_0" Pin0InfoVect1LinkObjId="SW-42434_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42433_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4263,-396 4263,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2006d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4263,-383 4263,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7029@x" ObjectIDND1="7028@x" ObjectIDZND0="7032@1" Pin0InfoVect0LinkObjId="SW-42437_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42434_0" Pin1InfoVect1LinkObjId="SW-42433_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4263,-383 4263,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2006fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4187,-383 4202,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_211f4f0@0" ObjectIDZND0="7029@0" Pin0InfoVect0LinkObjId="SW-42434_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_211f4f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4187,-383 4202,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2007210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-383 4263,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7029@1" ObjectIDZND0="7032@x" ObjectIDZND1="7028@x" Pin0InfoVect0LinkObjId="SW-42437_0" Pin0InfoVect1LinkObjId="SW-42433_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42434_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-383 4263,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f34570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4740,-384 4757,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2123ee0@0" ObjectIDZND0="7038@0" Pin0InfoVect0LinkObjId="SW-42473_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2123ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4740,-384 4757,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f3cf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-397 4938,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20586@0" ObjectIDZND0="7046@x" ObjectIDZND1="7043@x" Pin0InfoVect0LinkObjId="SW-42494_0" Pin0InfoVect1LinkObjId="SW-42491_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104245_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-397 4938,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f3d1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-384 4938,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7043@x" ObjectIDND1="20586@x" ObjectIDZND0="7046@1" Pin0InfoVect0LinkObjId="SW-42494_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42491_0" Pin1InfoVect1LinkObjId="SW-104245_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-384 4938,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f3d410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4860,-384 4877,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_211ff80@0" ObjectIDZND0="7043@0" Pin0InfoVect0LinkObjId="SW-42491_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_211ff80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4860,-384 4877,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f3d670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-384 4938,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7043@1" ObjectIDZND0="7046@x" ObjectIDZND1="20586@x" Pin0InfoVect0LinkObjId="SW-42494_0" Pin0InfoVect1LinkObjId="SW-104245_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42491_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-384 4938,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f461a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5058,-397 5058,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7047@0" ObjectIDZND0="7051@x" ObjectIDZND1="7048@x" Pin0InfoVect0LinkObjId="SW-42513_0" Pin0InfoVect1LinkObjId="SW-42510_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42509_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5058,-397 5058,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f46400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5058,-384 5058,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7048@x" ObjectIDND1="7047@x" ObjectIDZND0="7051@1" Pin0InfoVect0LinkObjId="SW-42513_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42510_0" Pin1InfoVect1LinkObjId="SW-42509_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5058,-384 5058,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f46660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4980,-384 4997,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2120a10@0" ObjectIDZND0="7048@0" Pin0InfoVect0LinkObjId="SW-42510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2120a10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4980,-384 4997,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f468c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5033,-384 5058,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7048@1" ObjectIDZND0="7051@x" ObjectIDZND1="7047@x" Pin0InfoVect0LinkObjId="SW-42513_0" Pin0InfoVect1LinkObjId="SW-42509_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42510_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5033,-384 5058,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e9a970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5205,-396 5205,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7052@0" ObjectIDZND0="7056@x" ObjectIDZND1="7053@x" Pin0InfoVect0LinkObjId="SW-42529_0" Pin0InfoVect1LinkObjId="SW-42526_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42525_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5205,-396 5205,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e9abd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5205,-383 5205,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7053@x" ObjectIDND1="7052@x" ObjectIDZND0="7056@1" Pin0InfoVect0LinkObjId="SW-42529_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42526_0" Pin1InfoVect1LinkObjId="SW-42525_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5205,-383 5205,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e9ae30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5127,-383 5144,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_21214a0@0" ObjectIDZND0="7053@0" Pin0InfoVect0LinkObjId="SW-42526_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21214a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5127,-383 5144,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e9b090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5180,-383 5205,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7053@1" ObjectIDZND0="7056@x" ObjectIDZND1="7052@x" Pin0InfoVect0LinkObjId="SW-42529_0" Pin0InfoVect1LinkObjId="SW-42525_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42526_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5180,-383 5205,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ea3f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5325,-396 5325,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7057@0" ObjectIDZND0="7061@x" ObjectIDZND1="7058@x" Pin0InfoVect0LinkObjId="SW-42547_0" Pin0InfoVect1LinkObjId="SW-42544_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42543_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5325,-396 5325,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ea4190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5325,-383 5325,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7058@x" ObjectIDND1="7057@x" ObjectIDZND0="7061@1" Pin0InfoVect0LinkObjId="SW-42547_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42544_0" Pin1InfoVect1LinkObjId="SW-42543_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5325,-383 5325,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ea43f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5247,-383 5264,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2121f30@0" ObjectIDZND0="7058@0" Pin0InfoVect0LinkObjId="SW-42544_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2121f30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5247,-383 5264,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ea4650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5300,-383 5325,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7058@1" ObjectIDZND0="7061@x" ObjectIDZND1="7057@x" Pin0InfoVect0LinkObjId="SW-42547_0" Pin0InfoVect1LinkObjId="SW-42543_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42544_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5300,-383 5325,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ead4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5471,-396 5471,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7066@0" ObjectIDZND0="7069@x" ObjectIDZND1="7067@x" Pin0InfoVect0LinkObjId="SW-42577_0" Pin0InfoVect1LinkObjId="SW-42575_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42574_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5471,-396 5471,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ead750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5471,-383 5471,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7067@x" ObjectIDND1="7066@x" ObjectIDZND0="7069@1" Pin0InfoVect0LinkObjId="SW-42577_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42575_0" Pin1InfoVect1LinkObjId="SW-42574_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5471,-383 5471,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ead9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5393,-383 5410,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_21229c0@0" ObjectIDZND0="7067@0" Pin0InfoVect0LinkObjId="SW-42575_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21229c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5393,-383 5410,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1eadc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5446,-383 5471,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7067@1" ObjectIDZND0="7069@x" ObjectIDZND1="7066@x" Pin0InfoVect0LinkObjId="SW-42577_0" Pin0InfoVect1LinkObjId="SW-42574_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42575_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5446,-383 5471,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_206d1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4566,-532 4591,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2123450@0" ObjectIDZND0="7079@0" Pin0InfoVect0LinkObjId="SW-42639_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2123450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4566,-532 4591,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2076370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3894,-908 3894,-891 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10915@0" ObjectIDZND0="7070@1" Pin0InfoVect0LinkObjId="SW-42622_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41cbd00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3894,-908 3894,-891 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20765d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3894,-810 3894,-795 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="7075@0" ObjectIDZND0="7090@1" Pin0InfoVect0LinkObjId="g_407eb20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42628_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3894,-810 3894,-795 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2076830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3894,-479 3894,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7072@0" ObjectIDZND0="10916@0" Pin0InfoVect0LinkObjId="g_424e7f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42624_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3894,-479 3894,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2079180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3808,-532 3833,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_211d540@0" ObjectIDZND0="7073@0" Pin0InfoVect0LinkObjId="SW-42625_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_211d540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3808,-532 3833,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20793e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3869,-532 3894,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7073@1" ObjectIDZND0="7074@x" ObjectIDZND1="7072@x" Pin0InfoVect0LinkObjId="SW-42627_0" Pin0InfoVect1LinkObjId="SW-42624_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42625_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3869,-532 3894,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2079640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3894,-552 3894,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="7074@0" ObjectIDZND0="7073@x" ObjectIDZND1="7072@x" Pin0InfoVect0LinkObjId="SW-42625_0" Pin0InfoVect1LinkObjId="SW-42624_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42627_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3894,-552 3894,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20798a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3894,-532 3894,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7073@x" ObjectIDND1="7074@x" ObjectIDZND0="7072@1" Pin0InfoVect0LinkObjId="SW-42624_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42625_0" Pin1InfoVect1LinkObjId="SW-42627_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3894,-532 3894,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2082c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3985,-1114 3965,-1114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="7006@0" ObjectIDZND0="g_2117c10@0" ObjectIDZND1="7005@x" ObjectIDZND2="7004@x" Pin0InfoVect0LinkObjId="g_2117c10_0" Pin0InfoVect1LinkObjId="SW-42343_0" Pin0InfoVect2LinkObjId="SW-42342_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42344_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3985,-1114 3965,-1114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2082e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3965,-1172 3965,-1114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="46071@1" ObjectIDZND0="g_2117c10@0" ObjectIDZND1="7005@x" ObjectIDZND2="7004@x" Pin0InfoVect0LinkObjId="g_2117c10_0" Pin0InfoVect1LinkObjId="SW-42343_0" Pin0InfoVect2LinkObjId="SW-42342_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3965,-1172 3965,-1114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1eedc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-538 4337,-519 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="7035@x" ObjectIDND1="g_1ef9510@0" ObjectIDND2="7036@x" ObjectIDZND0="7034@1" Pin0InfoVect0LinkObjId="SW-42454_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-42455_0" Pin1InfoVect1LinkObjId="g_1ef9510_0" Pin1InfoVect2LinkObjId="SW-42456_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-538 4337,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1efbaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3904,-152 3904,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7016@1" ObjectIDZND0="g_1efad50@1" Pin0InfoVect0LinkObjId="g_1efad50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42383_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3904,-152 3904,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1efca50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-152 4024,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7021@1" ObjectIDZND0="g_1efbd00@1" Pin0InfoVect0LinkObjId="g_1efbd00_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42401_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-152 4024,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1efda00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-152 4144,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7026@1" ObjectIDZND0="g_1efccb0@1" Pin0InfoVect0LinkObjId="g_1efccb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42418_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-152 4144,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1efe9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-152 4264,-175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7031@1" ObjectIDZND0="g_1efdc60@1" Pin0InfoVect0LinkObjId="g_1efdc60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42436_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-152 4264,-175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f006b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-153 4938,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7045@1" ObjectIDZND0="g_1eff960@1" Pin0InfoVect0LinkObjId="g_1eff960_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42493_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-153 4938,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2100b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5058,-153 5058,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7050@1" ObjectIDZND0="g_1f00910@1" Pin0InfoVect0LinkObjId="g_1f00910_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42512_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5058,-153 5058,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2101980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5205,-152 5205,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7055@1" ObjectIDZND0="g_2100d30@1" Pin0InfoVect0LinkObjId="g_2100d30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42528_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5205,-152 5205,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2102860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5325,-152 5325,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7060@1" ObjectIDZND0="g_2101bb0@1" Pin0InfoVect0LinkObjId="g_2101bb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42546_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5325,-152 5325,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2109870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5013,-836 5013,-855 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_21092a0@1" ObjectIDZND0="7088@0" Pin0InfoVect0LinkObjId="SW-42656_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21092a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5013,-836 5013,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2109ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5013,-786 5013,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="7089@1" ObjectIDZND0="g_21092a0@0" Pin0InfoVect0LinkObjId="g_21092a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5013,-786 5013,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_212f4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,-908 4809,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="46175@0" ObjectIDZND0="11155@0" Pin0InfoVect0LinkObjId="SW-187367_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21ada20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4809,-908 4809,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_212f6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4527,-1025 4527,-1006 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7009@0" ObjectIDZND0="7012@1" Pin0InfoVect0LinkObjId="SW-42366_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42361_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4527,-1025 4527,-1006 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_212f8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4314,-538 4337,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="7035@1" ObjectIDZND0="7034@x" ObjectIDZND1="g_1ef9510@0" ObjectIDZND2="7036@x" Pin0InfoVect0LinkObjId="SW-42454_0" Pin0InfoVect1LinkObjId="g_1ef9510_0" Pin0InfoVect2LinkObjId="SW-42456_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42455_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4314,-538 4337,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2132e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4263,-538 4278,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_212fac0@0" ObjectIDZND0="7035@0" Pin0InfoVect0LinkObjId="SW-42455_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_212fac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4263,-538 4278,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2135ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,-1251 4809,-1261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_21350e0@1" ObjectIDZND0="39638@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21350e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4809,-1251 4809,-1261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2139420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3827,-1023 3788,-1023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="7083@x" ObjectIDND1="7082@x" ObjectIDND2="g_2138a50@0" ObjectIDZND0="g_21ae140@0" Pin0InfoVect0LinkObjId="g_21ae140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-42651_0" Pin1InfoVect1LinkObjId="SW-42650_0" Pin1InfoVect2LinkObjId="g_2138a50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3827,-1023 3788,-1023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_213def0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3894,-637 3894,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7071@0" ObjectIDZND0="g_2075900@1" Pin0InfoVect0LinkObjId="g_2075900_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42623_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3894,-637 3894,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21657c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-312 3766,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7064@0" ObjectIDZND0="7065@0" Pin0InfoVect0LinkObjId="SW-42564_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42563_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-312 3766,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2165a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3904,-312 3904,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7015@0" ObjectIDZND0="7017@0" Pin0InfoVect0LinkObjId="SW-42384_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42382_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3904,-312 3904,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2165c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-310 4024,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7020@0" ObjectIDZND0="7022@0" Pin0InfoVect0LinkObjId="SW-42402_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-310 4024,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2165ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-312 4144,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7025@0" ObjectIDZND0="7027@0" Pin0InfoVect0LinkObjId="SW-42419_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42417_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-312 4144,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2166140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-311 4264,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7030@0" ObjectIDZND0="7032@0" Pin0InfoVect0LinkObjId="SW-42437_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42435_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-311 4264,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2174850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4817,-309 4817,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7039@0" ObjectIDZND0="7041@0" Pin0InfoVect0LinkObjId="SW-42476_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42474_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4817,-309 4817,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2174ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-310 4938,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7044@0" ObjectIDZND0="7046@0" Pin0InfoVect0LinkObjId="SW-42494_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42492_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-310 4938,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2174d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5058,-308 5058,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7049@0" ObjectIDZND0="7051@0" Pin0InfoVect0LinkObjId="SW-42513_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42511_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5058,-308 5058,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2179ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5205,-309 5205,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7054@0" ObjectIDZND0="7056@0" Pin0InfoVect0LinkObjId="SW-42529_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42527_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5205,-309 5205,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_217ebf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5325,-329 5325,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7061@0" ObjectIDZND0="7059@0" Pin0InfoVect0LinkObjId="SW-42545_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42547_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5325,-329 5325,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_217f480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5471,-308 5471,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7068@0" ObjectIDZND0="7069@0" Pin0InfoVect0LinkObjId="SW-42577_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42576_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5471,-308 5471,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21800a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4385,-351 4385,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="7085@x" ObjectIDND1="g_200b7a0@0" ObjectIDZND0="7084@0" Pin0InfoVect0LinkObjId="SW-42652_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42653_0" Pin1InfoVect1LinkObjId="g_200b7a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4385,-351 4385,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2180b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-351 4341,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2180290@0" ObjectIDZND0="7085@0" Pin0InfoVect0LinkObjId="SW-42653_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2180290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-351 4341,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2180df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-351 4385,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="7085@1" ObjectIDZND0="7084@x" ObjectIDZND1="g_200b7a0@0" Pin0InfoVect0LinkObjId="SW-42652_0" Pin0InfoVect1LinkObjId="g_200b7a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42653_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4377,-351 4385,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21818c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4628,-353 4628,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="7087@x" ObjectIDND1="g_1f301a0@0" ObjectIDZND0="7086@0" Pin0InfoVect0LinkObjId="SW-42654_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42655_0" Pin1InfoVect1LinkObjId="g_1f301a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4628,-353 4628,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2181b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4564,-353 4580,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2181fe0@0" ObjectIDZND0="7087@0" Pin0InfoVect0LinkObjId="SW-42655_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2181fe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4564,-353 4580,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2181d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4616,-353 4628,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="7087@1" ObjectIDZND0="7086@x" ObjectIDZND1="g_1f301a0@0" Pin0InfoVect0LinkObjId="SW-42654_0" Pin0InfoVect1LinkObjId="g_1f301a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42655_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4616,-353 4628,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_218aab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4817,-384 4817,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7041@x" ObjectIDND1="7038@x" ObjectIDZND0="7037@0" Pin0InfoVect0LinkObjId="SW-42472_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42476_0" Pin1InfoVect1LinkObjId="SW-42473_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4817,-384 4817,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_218b5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4793,-384 4817,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="7038@1" ObjectIDZND0="7037@x" ObjectIDZND1="7041@x" Pin0InfoVect0LinkObjId="SW-42472_0" Pin0InfoVect1LinkObjId="SW-42476_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42473_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4793,-384 4817,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_218b800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4817,-384 4817,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7037@x" ObjectIDND1="7038@x" ObjectIDZND0="7041@1" Pin0InfoVect0LinkObjId="SW-42476_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42472_0" Pin1InfoVect1LinkObjId="SW-42473_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4817,-384 4817,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a1180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3894,-855 3894,-837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7070@0" ObjectIDZND0="7075@1" Pin0InfoVect0LinkObjId="SW-42628_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42622_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3894,-855 3894,-837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a1370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3917,-1114 3965,-1114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2117c10@0" ObjectIDZND0="7005@x" ObjectIDZND1="7004@x" ObjectIDZND2="7006@x" Pin0InfoVect0LinkObjId="SW-42343_0" Pin0InfoVect1LinkObjId="SW-42342_0" Pin0InfoVect2LinkObjId="SW-42344_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2117c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3917,-1114 3965,-1114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a1560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4022,-1114 4044,-1114 4044,-1127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7006@1" ObjectIDZND0="g_20830e0@0" Pin0InfoVect0LinkObjId="g_20830e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42344_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4022,-1114 4044,-1114 4044,-1127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a1750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3945,-1084 3965,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="7005@1" ObjectIDZND0="g_2117c10@0" ObjectIDZND1="7006@x" ObjectIDZND2="46071@1" Pin0InfoVect0LinkObjId="g_2117c10_0" Pin0InfoVect1LinkObjId="SW-42344_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42343_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3945,-1084 3965,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a1980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3965,-1084 3965,-1114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="7005@x" ObjectIDND1="7004@x" ObjectIDZND0="g_2117c10@0" ObjectIDZND1="7006@x" ObjectIDZND2="46071@1" Pin0InfoVect0LinkObjId="g_2117c10_0" Pin0InfoVect1LinkObjId="SW-42344_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42343_0" Pin1InfoVect1LinkObjId="SW-42342_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3965,-1084 3965,-1114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a24e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3896,-1084 3909,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_21a1bb0@0" ObjectIDZND0="7005@0" Pin0InfoVect0LinkObjId="SW-42343_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21a1bb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3896,-1084 3909,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a3130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4444,-1087 4457,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_21a2740@0" ObjectIDZND0="7010@0" Pin0InfoVect0LinkObjId="SW-42362_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21a2740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4444,-1087 4457,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a4bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-1087 4527,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="7010@1" ObjectIDZND0="7009@x" ObjectIDZND1="7011@x" ObjectIDZND2="g_21a3390@0" Pin0InfoVect0LinkObjId="SW-42361_0" Pin0InfoVect1LinkObjId="SW-42363_0" Pin0InfoVect2LinkObjId="g_21a3390_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42362_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-1087 4527,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a56e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4527,-1061 4527,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="7009@1" ObjectIDZND0="7010@x" ObjectIDZND1="7011@x" ObjectIDZND2="g_21a3390@0" Pin0InfoVect0LinkObjId="SW-42362_0" Pin0InfoVect1LinkObjId="SW-42363_0" Pin0InfoVect2LinkObjId="g_21a3390_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42361_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4527,-1061 4527,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a5940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4481,-1117 4527,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_21a3390@0" ObjectIDZND0="7010@x" ObjectIDZND1="7009@x" ObjectIDZND2="7011@x" Pin0InfoVect0LinkObjId="SW-42362_0" Pin0InfoVect1LinkObjId="SW-42361_0" Pin0InfoVect2LinkObjId="SW-42363_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21a3390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4481,-1117 4527,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a6650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4527,-1087 4527,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="7010@x" ObjectIDND1="7009@x" ObjectIDZND0="7011@x" ObjectIDZND1="g_21a3390@0" ObjectIDZND2="38054@1" Pin0InfoVect0LinkObjId="SW-42363_0" Pin0InfoVect1LinkObjId="g_21a3390_0" Pin0InfoVect2LinkObjId="g_21a68b0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42362_0" Pin1InfoVect1LinkObjId="SW-42361_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4527,-1087 4527,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a68b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4527,-1117 4527,-1175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="7010@x" ObjectIDND1="7009@x" ObjectIDND2="7011@x" ObjectIDZND0="38054@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-42362_0" Pin1InfoVect1LinkObjId="SW-42361_0" Pin1InfoVect2LinkObjId="SW-42363_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4527,-1117 4527,-1175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a6b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4546,-1116 4527,-1116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="7011@0" ObjectIDZND0="7010@x" ObjectIDZND1="7009@x" ObjectIDZND2="g_21a3390@0" Pin0InfoVect0LinkObjId="SW-42362_0" Pin0InfoVect1LinkObjId="SW-42361_0" Pin0InfoVect2LinkObjId="g_21a3390_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42363_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4546,-1116 4527,-1116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a6d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4582,-1116 4596,-1116 4596,-1128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7011@1" ObjectIDZND0="g_21a3f60@0" Pin0InfoVect0LinkObjId="g_21a3f60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42363_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4582,-1116 4596,-1116 4596,-1128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a6fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4783,-1194 4809,-1194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2118820@0" ObjectIDZND0="g_21350e0@0" ObjectIDZND1="11158@x" ObjectIDZND2="11156@x" Pin0InfoVect0LinkObjId="g_21350e0_0" Pin0InfoVect1LinkObjId="SW-187371_0" Pin0InfoVect2LinkObjId="SW-187368_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2118820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4783,-1194 4809,-1194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a7ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,-1194 4809,-1212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2118820@0" ObjectIDND1="11158@x" ObjectIDND2="11156@x" ObjectIDZND0="g_21350e0@0" Pin0InfoVect0LinkObjId="g_21350e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2118820_0" Pin1InfoVect1LinkObjId="SW-187371_0" Pin1InfoVect2LinkObjId="SW-187368_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4809,-1194 4809,-1212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a7d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4782,-1004 4809,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28380@1" ObjectIDZND0="11155@x" ObjectIDZND1="11150@x" Pin0InfoVect0LinkObjId="SW-187367_0" Pin0InfoVect1LinkObjId="SW-187366_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187369_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4782,-1004 4809,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a8810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,-983 4809,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="11155@1" ObjectIDZND0="28380@x" ObjectIDZND1="11150@x" Pin0InfoVect0LinkObjId="SW-187369_0" Pin0InfoVect1LinkObjId="SW-187366_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187367_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4809,-983 4809,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a8a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,-1004 4809,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="28380@x" ObjectIDND1="11155@x" ObjectIDZND0="11150@0" Pin0InfoVect0LinkObjId="SW-187366_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-187369_0" Pin1InfoVect1LinkObjId="SW-187367_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4809,-1004 4809,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a8cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4782,-1099 4809,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28381@1" ObjectIDZND0="11156@x" ObjectIDZND1="11150@x" Pin0InfoVect0LinkObjId="SW-187368_0" Pin0InfoVect1LinkObjId="SW-187366_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187370_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4782,-1099 4809,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a97c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,-1123 4809,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="11156@0" ObjectIDZND0="28381@x" ObjectIDZND1="11150@x" Pin0InfoVect0LinkObjId="SW-187370_0" Pin0InfoVect1LinkObjId="SW-187366_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187368_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4809,-1123 4809,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a9a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,-1099 4809,-1067 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="28381@x" ObjectIDND1="11156@x" ObjectIDZND0="11150@1" Pin0InfoVect0LinkObjId="SW-187366_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-187370_0" Pin1InfoVect1LinkObjId="SW-187368_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4809,-1099 4809,-1067 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a9c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4782,-1165 4809,-1165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="11158@1" ObjectIDZND0="11156@x" ObjectIDZND1="g_2118820@0" ObjectIDZND2="g_21350e0@0" Pin0InfoVect0LinkObjId="SW-187368_0" Pin0InfoVect1LinkObjId="g_2118820_0" Pin0InfoVect2LinkObjId="g_21350e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187371_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4782,-1165 4809,-1165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21aa770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,-1159 4809,-1165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="11156@1" ObjectIDZND0="11158@x" ObjectIDZND1="g_2118820@0" ObjectIDZND2="g_21350e0@0" Pin0InfoVect0LinkObjId="SW-187371_0" Pin0InfoVect1LinkObjId="g_2118820_0" Pin0InfoVect2LinkObjId="g_21350e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187368_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4809,-1159 4809,-1165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21aa9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,-1165 4809,-1194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="11158@x" ObjectIDND1="11156@x" ObjectIDZND0="g_2118820@0" ObjectIDZND1="g_21350e0@0" Pin0InfoVect0LinkObjId="g_2118820_0" Pin0InfoVect1LinkObjId="g_21350e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-187371_0" Pin1InfoVect1LinkObjId="SW-187368_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4809,-1165 4809,-1194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21aac30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4732,-1164 4749,-1165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_21aae90@0" ObjectIDZND0="11158@0" Pin0InfoVect0LinkObjId="SW-187371_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21aae90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4732,-1164 4749,-1165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21ab920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4733,-1099 4746,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_21abb80@0" ObjectIDZND0="28381@0" Pin0InfoVect0LinkObjId="SW-187370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21abb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4733,-1099 4746,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21ac610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4732,-1004 4745,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_21ac870@0" ObjectIDZND0="28380@0" Pin0InfoVect0LinkObjId="SW-187369_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21ac870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4732,-1004 4745,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21ad560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4527,-908 4527,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="46175@0" ObjectIDZND0="7008@0" Pin0InfoVect0LinkObjId="SW-42360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21ada20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4527,-908 4527,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21ad7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4527,-962 4527,-979 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7008@1" ObjectIDZND0="7012@0" Pin0InfoVect0LinkObjId="SW-42366_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42360_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4527,-962 4527,-979 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21ada20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5013,-891 5013,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7088@1" ObjectIDZND0="46175@0" Pin0InfoVect0LinkObjId="g_41cbf60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42656_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5013,-891 5013,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21adc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3827,-1068 3825,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2138a50@0" ObjectIDZND0="g_2135e00@0" Pin0InfoVect0LinkObjId="g_2135e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2138a50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3827,-1068 3825,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21adee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3827,-1036 3827,-1023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2138a50@1" ObjectIDZND0="7083@x" ObjectIDZND1="7082@x" ObjectIDZND2="g_21ae140@0" Pin0InfoVect0LinkObjId="SW-42651_0" Pin0InfoVect1LinkObjId="SW-42650_0" Pin0InfoVect2LinkObjId="g_21ae140_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2138a50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3827,-1036 3827,-1023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21aeeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3818,-1003 3827,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="7083@1" ObjectIDZND0="g_2138a50@0" ObjectIDZND1="g_21ae140@0" ObjectIDZND2="7082@x" Pin0InfoVect0LinkObjId="g_2138a50_0" Pin0InfoVect1LinkObjId="g_21ae140_0" Pin0InfoVect2LinkObjId="SW-42650_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42651_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3818,-1003 3827,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21af110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3827,-1023 3827,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2138a50@0" ObjectIDND1="g_21ae140@0" ObjectIDZND0="7083@x" ObjectIDZND1="7082@x" Pin0InfoVect0LinkObjId="SW-42651_0" Pin0InfoVect1LinkObjId="SW-42650_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2138a50_0" Pin1InfoVect1LinkObjId="g_21ae140_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3827,-1023 3827,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21af370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3827,-1003 3827,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2138a50@0" ObjectIDND1="g_21ae140@0" ObjectIDND2="7083@x" ObjectIDZND0="7082@1" Pin0InfoVect0LinkObjId="SW-42650_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2138a50_0" Pin1InfoVect1LinkObjId="g_21ae140_0" Pin1InfoVect2LinkObjId="SW-42651_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3827,-1003 3827,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21b0060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3768,-1003 3779,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_21af5d0@0" ObjectIDZND0="7083@0" Pin0InfoVect0LinkObjId="SW-42651_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21af5d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3768,-1003 3779,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21b9150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5046,-1012 5007,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_21b8780@0" ObjectIDND1="46201@x" ObjectIDND2="46200@x" ObjectIDZND0="g_21b9610@0" Pin0InfoVect0LinkObjId="g_21b9610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21b8780_0" Pin1InfoVect1LinkObjId="SW-298241_0" Pin1InfoVect2LinkObjId="SW-298240_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5046,-1012 5007,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21b93b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5046,-1025 5046,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_21b8780@1" ObjectIDZND0="g_21b9610@0" ObjectIDZND1="46201@x" ObjectIDZND2="46200@x" Pin0InfoVect0LinkObjId="g_21b9610_0" Pin0InfoVect1LinkObjId="SW-298241_0" Pin0InfoVect2LinkObjId="SW-298240_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21b8780_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5046,-1025 5046,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41c2af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5037,-992 5046,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="46201@1" ObjectIDZND0="g_21b9610@0" ObjectIDZND1="g_21b8780@0" ObjectIDZND2="46200@x" Pin0InfoVect0LinkObjId="g_21b9610_0" Pin0InfoVect1LinkObjId="g_21b8780_0" Pin0InfoVect2LinkObjId="SW-298240_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298241_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5037,-992 5046,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41c2d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5046,-1012 5046,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_21b9610@0" ObjectIDND1="g_21b8780@0" ObjectIDZND0="46201@x" ObjectIDZND1="46200@x" Pin0InfoVect0LinkObjId="SW-298241_0" Pin0InfoVect1LinkObjId="SW-298240_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21b9610_0" Pin1InfoVect1LinkObjId="g_21b8780_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5046,-1012 5046,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41c2fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5046,-992 5046,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="46201@x" ObjectIDND1="g_21b9610@0" ObjectIDND2="g_21b8780@0" ObjectIDZND0="46200@1" Pin0InfoVect0LinkObjId="SW-298240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298241_0" Pin1InfoVect1LinkObjId="g_21b9610_0" Pin1InfoVect2LinkObjId="g_21b8780_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5046,-992 5046,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41c3c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4984,-992 4998,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_41c3210@0" ObjectIDZND0="46201@0" Pin0InfoVect0LinkObjId="SW-298241_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41c3210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4984,-992 4998,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41cbd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4262,-929 4262,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46186@0" ObjectIDZND0="10915@0" Pin0InfoVect0LinkObjId="g_425a0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298243_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4262,-929 4262,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41cbf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4444,-929 4444,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46187@0" ObjectIDZND0="46175@0" Pin0InfoVect0LinkObjId="g_21ada20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298244_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4444,-929 4444,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41d26e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4414,-1061 4414,-1047 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_41d1c50@0" ObjectIDZND0="46190@1" Pin0InfoVect0LinkObjId="SW-298246_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41d1c50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4414,-1061 4414,-1047 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41d2940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-1064 4277,-1047 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_41d11c0@0" ObjectIDZND0="46189@1" Pin0InfoVect0LinkObjId="SW-298245_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41d11c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-1064 4277,-1047 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41d2ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-1011 4277,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46189@0" ObjectIDZND0="46186@x" ObjectIDZND1="46188@x" Pin0InfoVect0LinkObjId="SW-298243_0" Pin0InfoVect1LinkObjId="SW-298242_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298245_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-1011 4277,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41d2e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4262,-965 4262,-992 4277,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46186@1" ObjectIDZND0="46189@x" ObjectIDZND1="46188@x" Pin0InfoVect0LinkObjId="SW-298245_0" Pin0InfoVect1LinkObjId="SW-298242_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298243_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4262,-965 4262,-992 4277,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41d3060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-992 4326,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="46189@x" ObjectIDND1="46186@x" ObjectIDZND0="46188@1" Pin0InfoVect0LinkObjId="SW-298242_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-298245_0" Pin1InfoVect1LinkObjId="SW-298243_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-992 4326,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41d32c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4414,-1011 4414,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="46190@0" ObjectIDZND0="46188@x" ObjectIDZND1="46187@x" Pin0InfoVect0LinkObjId="SW-298242_0" Pin0InfoVect1LinkObjId="SW-298244_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298246_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4414,-1011 4414,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41d3520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-992 4414,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="46188@0" ObjectIDZND0="46190@x" ObjectIDZND1="46187@x" Pin0InfoVect0LinkObjId="SW-298246_0" Pin0InfoVect1LinkObjId="SW-298244_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298242_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-992 4414,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41d3780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4414,-992 4444,-992 4444,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="46190@x" ObjectIDND1="46188@x" ObjectIDZND0="46187@1" Pin0InfoVect0LinkObjId="SW-298244_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-298246_0" Pin1InfoVect1LinkObjId="SW-298242_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4414,-992 4444,-992 4444,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41d85a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4346,-574 4346,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1ef9510@0" ObjectIDZND0="7036@x" ObjectIDZND1="7034@x" ObjectIDZND2="7035@x" Pin0InfoVect0LinkObjId="SW-42456_0" Pin0InfoVect1LinkObjId="SW-42454_0" Pin0InfoVect2LinkObjId="SW-42455_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ef9510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4346,-574 4346,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41d9090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4403,-562 4346,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="7036@1" ObjectIDZND0="g_1ef9510@0" ObjectIDZND1="7034@x" ObjectIDZND2="7035@x" Pin0InfoVect0LinkObjId="g_1ef9510_0" Pin0InfoVect1LinkObjId="SW-42454_0" Pin0InfoVect2LinkObjId="SW-42455_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42456_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4403,-562 4346,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41d92f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4350,-546 4337,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDZND0="g_1ef9510@0" ObjectIDZND1="7036@x" ObjectIDZND2="7034@x" Pin0InfoVect0LinkObjId="g_1ef9510_0" Pin0InfoVect1LinkObjId="SW-42456_0" Pin0InfoVect2LinkObjId="SW-42454_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4350,-546 4337,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41d9de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4346,-562 4337,-562 4337,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1ef9510@0" ObjectIDND1="7036@x" ObjectIDZND0="7034@x" ObjectIDZND1="7035@x" Pin0InfoVect0LinkObjId="SW-42454_0" Pin0InfoVect1LinkObjId="SW-42455_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ef9510_0" Pin1InfoVect1LinkObjId="SW-42456_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4346,-562 4337,-562 4337,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41da040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-545 4337,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1ef9510@0" ObjectIDND1="7036@x" ObjectIDZND0="7034@x" ObjectIDZND1="7035@x" Pin0InfoVect0LinkObjId="SW-42454_0" Pin0InfoVect1LinkObjId="SW-42455_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ef9510_0" Pin1InfoVect1LinkObjId="SW-42456_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-545 4337,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41ddd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4478,-543 4487,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" EndDevType1="switch" ObjectIDZND0="7036@x" ObjectIDZND1="7033@x" Pin0InfoVect0LinkObjId="SW-42456_0" Pin0InfoVect1LinkObjId="SW-42453_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4478,-543 4487,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41de870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4430,-562 4487,-562 4487,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7036@0" ObjectIDZND0="7033@x" Pin0InfoVect0LinkObjId="SW-42453_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42456_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4430,-562 4487,-562 4487,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41dead0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4487,-543 4487,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7036@x" ObjectIDZND0="7033@0" Pin0InfoVect0LinkObjId="SW-42453_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42456_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4487,-543 4487,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41e6970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3784,-159 3766,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="g_41e5bc0@0" ObjectIDZND0="g_1efa030@0" ObjectIDZND1="40716@x" Pin0InfoVect0LinkObjId="g_1efa030_0" Pin0InfoVect1LinkObjId="CB-CX_LH.CX_LH_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41e5bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3784,-159 3766,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41e7460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-177 3766,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" EndDevType1="lightningRod" ObjectIDND0="g_1efa030@1" ObjectIDZND0="40716@x" ObjectIDZND1="g_41e5bc0@0" Pin0InfoVect0LinkObjId="CB-CX_LH.CX_LH_Cb1_0" Pin0InfoVect1LinkObjId="g_41e5bc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1efa030_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-177 3766,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41e76c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-159 3766,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_1efa030@0" ObjectIDND1="g_41e5bc0@0" ObjectIDZND0="40716@0" Pin0InfoVect0LinkObjId="CB-CX_LH.CX_LH_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1efa030_0" Pin1InfoVect1LinkObjId="g_41e5bc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-159 3766,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41e7920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3786,-239 3766,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_41e4e10@0" ObjectIDZND0="g_1efa030@0" ObjectIDZND1="7064@x" Pin0InfoVect0LinkObjId="g_1efa030_0" Pin0InfoVect1LinkObjId="SW-42563_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41e4e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3786,-239 3766,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41e8410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-230 3766,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1efa030@0" ObjectIDZND0="g_41e4e10@0" ObjectIDZND1="7064@x" Pin0InfoVect0LinkObjId="g_41e4e10_0" Pin0InfoVect1LinkObjId="SW-42563_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1efa030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-230 3766,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41e8670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-239 3766,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_41e4e10@0" ObjectIDND1="g_1efa030@0" ObjectIDZND0="7064@1" Pin0InfoVect0LinkObjId="SW-42563_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41e4e10_0" Pin1InfoVect1LinkObjId="g_1efa030_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-239 3766,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41e88d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-251 3904,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_41e33b0@0" ObjectIDZND0="g_1efad50@0" ObjectIDZND1="7015@x" Pin0InfoVect0LinkObjId="g_1efad50_0" Pin0InfoVect1LinkObjId="SW-42382_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41e33b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-251 3904,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41e93c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3904,-229 3904,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1efad50@0" ObjectIDZND0="g_41e33b0@0" ObjectIDZND1="7015@x" Pin0InfoVect0LinkObjId="g_41e33b0_0" Pin0InfoVect1LinkObjId="SW-42382_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1efad50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3904,-229 3904,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41e9620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3904,-251 3904,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_41e33b0@0" ObjectIDND1="g_1efad50@0" ObjectIDZND0="7015@1" Pin0InfoVect0LinkObjId="SW-42382_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41e33b0_0" Pin1InfoVect1LinkObjId="g_1efad50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3904,-251 3904,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41f1030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5502,-236 5471,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_41ef4d0@0" ObjectIDZND0="g_2102ac0@0" ObjectIDZND1="7068@x" Pin0InfoVect0LinkObjId="g_2102ac0_0" Pin0InfoVect1LinkObjId="SW-42576_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41ef4d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5502,-236 5471,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41f1b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5471,-225 5471,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2102ac0@0" ObjectIDZND0="g_41ef4d0@0" ObjectIDZND1="7068@x" Pin0InfoVect0LinkObjId="g_41ef4d0_0" Pin0InfoVect1LinkObjId="SW-42576_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2102ac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5471,-225 5471,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41f1d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5471,-236 5471,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_41ef4d0@0" ObjectIDND1="g_2102ac0@0" ObjectIDZND0="7068@1" Pin0InfoVect0LinkObjId="SW-42576_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41ef4d0_0" Pin1InfoVect1LinkObjId="g_2102ac0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5471,-236 5471,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41f1fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5500,-156 5471,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="g_41f0280@0" ObjectIDZND0="g_2102ac0@0" ObjectIDZND1="40715@x" Pin0InfoVect0LinkObjId="g_2102ac0_0" Pin0InfoVect1LinkObjId="CB-CX_LH.CX_LH_Cb2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41f0280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5500,-156 5471,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41f2ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5471,-172 5471,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="g_2102ac0@1" ObjectIDZND0="g_41f0280@0" ObjectIDZND1="40715@x" Pin0InfoVect0LinkObjId="g_41f0280_0" Pin0InfoVect1LinkObjId="CB-CX_LH.CX_LH_Cb2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2102ac0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5471,-172 5471,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41f2d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5471,-156 5471,-130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_41f0280@0" ObjectIDND1="g_2102ac0@0" ObjectIDZND0="40715@0" Pin0InfoVect0LinkObjId="CB-CX_LH.CX_LH_Cb2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41f0280_0" Pin1InfoVect1LinkObjId="g_2102ac0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5471,-156 5471,-130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41f9910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-100 3904,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_41e40e0@0" ObjectIDZND0="34477@x" ObjectIDZND1="7016@x" Pin0InfoVect0LinkObjId="EC-CX_LH.061Ld_0" Pin0InfoVect1LinkObjId="SW-42383_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41e40e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-100 3904,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fa400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3904,-56 3904,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34477@0" ObjectIDZND0="g_41e40e0@0" ObjectIDZND1="7016@x" Pin0InfoVect0LinkObjId="g_41e40e0_0" Pin0InfoVect1LinkObjId="SW-42383_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_LH.061Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3904,-56 3904,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fa660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3904,-100 3904,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_41e40e0@0" ObjectIDND1="34477@x" ObjectIDZND0="7016@0" Pin0InfoVect0LinkObjId="SW-42383_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41e40e0_0" Pin1InfoVect1LinkObjId="EC-CX_LH.061Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3904,-100 3904,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fa8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4064,-249 4024,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_41ea5b0@0" ObjectIDZND0="g_1efbd00@0" ObjectIDZND1="7020@x" Pin0InfoVect0LinkObjId="g_1efbd00_0" Pin0InfoVect1LinkObjId="SW-42400_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41ea5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4064,-249 4024,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fb3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-229 4024,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1efbd00@0" ObjectIDZND0="g_41ea5b0@0" ObjectIDZND1="7020@x" Pin0InfoVect0LinkObjId="g_41ea5b0_0" Pin0InfoVect1LinkObjId="SW-42400_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1efbd00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-229 4024,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fb610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-249 4024,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_41ea5b0@0" ObjectIDND1="g_1efbd00@0" ObjectIDZND0="7020@1" Pin0InfoVect0LinkObjId="SW-42400_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41ea5b0_0" Pin1InfoVect1LinkObjId="g_1efbd00_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-249 4024,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fb870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-98 4024,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_41e9880@0" ObjectIDZND0="34478@x" ObjectIDZND1="7021@x" Pin0InfoVect0LinkObjId="EC-CX_LH.063Ld_0" Pin0InfoVect1LinkObjId="SW-42401_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41e9880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-98 4024,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fc360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-56 4024,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34478@0" ObjectIDZND0="g_41e9880@0" ObjectIDZND1="7021@x" Pin0InfoVect0LinkObjId="g_41e9880_0" Pin0InfoVect1LinkObjId="SW-42401_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_LH.063Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-56 4024,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fc5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-98 4024,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_41e9880@0" ObjectIDND1="34478@x" ObjectIDZND0="7021@0" Pin0InfoVect0LinkObjId="SW-42401_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41e9880_0" Pin1InfoVect1LinkObjId="EC-CX_LH.063Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-98 4024,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fc820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4185,-247 4144,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_41ec010@0" ObjectIDZND0="g_1efccb0@0" ObjectIDZND1="7025@x" Pin0InfoVect0LinkObjId="g_1efccb0_0" Pin0InfoVect1LinkObjId="SW-42417_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41ec010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4185,-247 4144,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fd310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-229 4144,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1efccb0@0" ObjectIDZND0="g_41ec010@0" ObjectIDZND1="7025@x" Pin0InfoVect0LinkObjId="g_41ec010_0" Pin0InfoVect1LinkObjId="SW-42417_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1efccb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-229 4144,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fd570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-247 4144,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_41ec010@0" ObjectIDND1="g_1efccb0@0" ObjectIDZND0="7025@1" Pin0InfoVect0LinkObjId="SW-42417_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41ec010_0" Pin1InfoVect1LinkObjId="g_1efccb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-247 4144,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fd7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4188,-96 4144,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_41eb2e0@0" ObjectIDZND0="34479@x" ObjectIDZND1="7026@x" Pin0InfoVect0LinkObjId="EC-CX_LH.065Ld_0" Pin0InfoVect1LinkObjId="SW-42418_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41eb2e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4188,-96 4144,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fe2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-56 4144,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34479@0" ObjectIDZND0="g_41eb2e0@0" ObjectIDZND1="7026@x" Pin0InfoVect0LinkObjId="g_41eb2e0_0" Pin0InfoVect1LinkObjId="SW-42418_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_LH.065Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-56 4144,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fe520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-96 4144,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_41eb2e0@0" ObjectIDND1="34479@x" ObjectIDZND0="7026@0" Pin0InfoVect0LinkObjId="SW-42418_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41eb2e0_0" Pin1InfoVect1LinkObjId="EC-CX_LH.065Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-96 4144,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fe780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4301,-241 4264,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_41eda70@0" ObjectIDZND0="g_1efdc60@0" ObjectIDZND1="7030@x" Pin0InfoVect0LinkObjId="g_1efdc60_0" Pin0InfoVect1LinkObjId="SW-42435_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41eda70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4301,-241 4264,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41ff270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-228 4264,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1efdc60@0" ObjectIDZND0="g_41eda70@0" ObjectIDZND1="7030@x" Pin0InfoVect0LinkObjId="g_41eda70_0" Pin0InfoVect1LinkObjId="SW-42435_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1efdc60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-228 4264,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41ff4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-241 4264,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_41eda70@0" ObjectIDND1="g_1efdc60@0" ObjectIDZND0="7030@1" Pin0InfoVect0LinkObjId="SW-42435_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41eda70_0" Pin1InfoVect1LinkObjId="g_1efdc60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-241 4264,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41ff730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4304,-90 4264,-90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_41ecd40@0" ObjectIDZND0="34480@x" ObjectIDZND1="7031@x" Pin0InfoVect0LinkObjId="EC-CX_LH.067Ld_0" Pin0InfoVect1LinkObjId="SW-42436_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41ecd40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4304,-90 4264,-90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4200220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-56 4264,-90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34480@0" ObjectIDZND0="g_41ecd40@0" ObjectIDZND1="7031@x" Pin0InfoVect0LinkObjId="g_41ecd40_0" Pin0InfoVect1LinkObjId="SW-42436_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_LH.067Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-56 4264,-90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4200480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-90 4264,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_41ecd40@0" ObjectIDND1="34480@x" ObjectIDZND0="7031@0" Pin0InfoVect0LinkObjId="SW-42436_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41ecd40_0" Pin1InfoVect1LinkObjId="EC-CX_LH.067Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-90 4264,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42006e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4853,-237 4817,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_41ee7a0@0" ObjectIDZND0="g_1efec10@0" ObjectIDZND1="7039@x" Pin0InfoVect0LinkObjId="g_1efec10_0" Pin0InfoVect1LinkObjId="SW-42474_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41ee7a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4853,-237 4817,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42011d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4817,-226 4817,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1efec10@0" ObjectIDZND0="g_41ee7a0@0" ObjectIDZND1="7039@x" Pin0InfoVect0LinkObjId="g_41ee7a0_0" Pin0InfoVect1LinkObjId="SW-42474_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1efec10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4817,-226 4817,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4201430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4817,-237 4817,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_41ee7a0@0" ObjectIDND1="g_1efec10@0" ObjectIDZND0="7039@1" Pin0InfoVect0LinkObjId="SW-42474_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41ee7a0_0" Pin1InfoVect1LinkObjId="g_1efec10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4817,-237 4817,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4201690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4981,-238 4938,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_41f2f90@0" ObjectIDZND0="g_1eff960@0" ObjectIDZND1="7044@x" Pin0InfoVect0LinkObjId="g_1eff960_0" Pin0InfoVect1LinkObjId="SW-42492_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41f2f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4981,-238 4938,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4202180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-225 4938,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1eff960@0" ObjectIDZND0="g_41f2f90@0" ObjectIDZND1="7044@x" Pin0InfoVect0LinkObjId="g_41f2f90_0" Pin0InfoVect1LinkObjId="SW-42492_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1eff960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-225 4938,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42023e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-238 4938,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_41f2f90@0" ObjectIDND1="g_1eff960@0" ObjectIDZND0="7044@1" Pin0InfoVect0LinkObjId="SW-42492_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41f2f90_0" Pin1InfoVect1LinkObjId="g_1eff960_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-238 4938,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4202640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4984,-87 4938,-87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_41f3cc0@0" ObjectIDZND0="34482@x" ObjectIDZND1="7045@x" Pin0InfoVect0LinkObjId="EC-CX_LH.064Ld_0" Pin0InfoVect1LinkObjId="SW-42493_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41f3cc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4984,-87 4938,-87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4203130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-57 4938,-87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34482@0" ObjectIDZND0="g_41f3cc0@0" ObjectIDZND1="7045@x" Pin0InfoVect0LinkObjId="g_41f3cc0_0" Pin0InfoVect1LinkObjId="SW-42493_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_LH.064Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-57 4938,-87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4203390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-87 4938,-117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_41f3cc0@0" ObjectIDND1="34482@x" ObjectIDZND0="7045@0" Pin0InfoVect0LinkObjId="SW-42493_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41f3cc0_0" Pin1InfoVect1LinkObjId="EC-CX_LH.064Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-87 4938,-117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42035f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5111,-236 5058,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_41f49f0@0" ObjectIDZND0="g_1f00910@0" ObjectIDZND1="7049@x" Pin0InfoVect0LinkObjId="g_1f00910_0" Pin0InfoVect1LinkObjId="SW-42511_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41f49f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5111,-236 5058,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42040e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5058,-225 5058,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1f00910@0" ObjectIDZND0="g_41f49f0@0" ObjectIDZND1="7049@x" Pin0InfoVect0LinkObjId="g_41f49f0_0" Pin0InfoVect1LinkObjId="SW-42511_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f00910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5058,-225 5058,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4204340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5058,-235 5058,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_41f49f0@0" ObjectIDND1="g_1f00910@0" ObjectIDZND0="7049@1" Pin0InfoVect0LinkObjId="SW-42511_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41f49f0_0" Pin1InfoVect1LinkObjId="g_1f00910_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5058,-235 5058,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42045a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5114,-85 5058,-85 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_41f5720@0" ObjectIDZND0="34483@x" ObjectIDZND1="7050@x" Pin0InfoVect0LinkObjId="EC-CX_LH.066Ld_0" Pin0InfoVect1LinkObjId="SW-42512_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41f5720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5114,-85 5058,-85 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4205090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5058,-57 5058,-85 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34483@0" ObjectIDZND0="g_41f5720@0" ObjectIDZND1="7050@x" Pin0InfoVect0LinkObjId="g_41f5720_0" Pin0InfoVect1LinkObjId="SW-42512_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_LH.066Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5058,-57 5058,-85 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42052f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5058,-85 5058,-117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_41f5720@0" ObjectIDND1="34483@x" ObjectIDZND0="7050@0" Pin0InfoVect0LinkObjId="SW-42512_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41f5720_0" Pin1InfoVect1LinkObjId="EC-CX_LH.066Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5058,-85 5058,-117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4205550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5251,-235 5205,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_41f6450@0" ObjectIDZND0="g_2100d30@0" ObjectIDZND1="7054@x" Pin0InfoVect0LinkObjId="g_2100d30_0" Pin0InfoVect1LinkObjId="SW-42527_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41f6450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5251,-235 5205,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4206040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5205,-224 5205,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2100d30@0" ObjectIDZND0="g_41f6450@0" ObjectIDZND1="7054@x" Pin0InfoVect0LinkObjId="g_41f6450_0" Pin0InfoVect1LinkObjId="SW-42527_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2100d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5205,-224 5205,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42062a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5205,-235 5205,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_41f6450@0" ObjectIDND1="g_2100d30@0" ObjectIDZND0="7054@1" Pin0InfoVect0LinkObjId="SW-42527_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41f6450_0" Pin1InfoVect1LinkObjId="g_2100d30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5205,-235 5205,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4206500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5254,-84 5205,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_41f7180@0" ObjectIDZND0="34484@x" ObjectIDZND1="7055@x" Pin0InfoVect0LinkObjId="EC-CX_LH.068Ld_0" Pin0InfoVect1LinkObjId="SW-42528_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41f7180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5254,-84 5205,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4206ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5205,-56 5205,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34484@0" ObjectIDZND0="g_41f7180@0" ObjectIDZND1="7055@x" Pin0InfoVect0LinkObjId="g_41f7180_0" Pin0InfoVect1LinkObjId="SW-42528_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_LH.068Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5205,-56 5205,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4207250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5205,-84 5205,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_41f7180@0" ObjectIDND1="34484@x" ObjectIDZND0="7055@0" Pin0InfoVect0LinkObjId="SW-42528_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41f7180_0" Pin1InfoVect1LinkObjId="EC-CX_LH.068Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5205,-84 5205,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42074b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5363,-238 5325,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_41f7eb0@0" ObjectIDZND0="g_2101bb0@0" ObjectIDZND1="7059@x" Pin0InfoVect0LinkObjId="g_2101bb0_0" Pin0InfoVect1LinkObjId="SW-42545_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41f7eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5363,-238 5325,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4207fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5325,-226 5325,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2101bb0@0" ObjectIDZND0="g_41f7eb0@0" ObjectIDZND1="7059@x" Pin0InfoVect0LinkObjId="g_41f7eb0_0" Pin0InfoVect1LinkObjId="SW-42545_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2101bb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5325,-226 5325,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4208200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5325,-238 5325,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_41f7eb0@0" ObjectIDND1="g_2101bb0@0" ObjectIDZND0="7059@1" Pin0InfoVect0LinkObjId="SW-42545_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41f7eb0_0" Pin1InfoVect1LinkObjId="g_2101bb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5325,-238 5325,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4208460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5366,-87 5325,-87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_41f8be0@0" ObjectIDZND0="34485@x" ObjectIDZND1="7060@x" Pin0InfoVect0LinkObjId="EC-CX_LH.072Ld_0" Pin0InfoVect1LinkObjId="SW-42546_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41f8be0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5366,-87 5325,-87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4208f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5325,-56 5325,-87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34485@0" ObjectIDZND0="g_41f8be0@0" ObjectIDZND1="7060@x" Pin0InfoVect0LinkObjId="g_41f8be0_0" Pin0InfoVect1LinkObjId="SW-42546_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_LH.072Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5325,-56 5325,-87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42091b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5325,-87 5325,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_41f8be0@0" ObjectIDND1="34485@x" ObjectIDZND0="7060@0" Pin0InfoVect0LinkObjId="SW-42546_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41f8be0_0" Pin1InfoVect1LinkObjId="EC-CX_LH.072Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5325,-87 5325,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4214750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3636,-399 3636,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="46197@1" ObjectIDZND0="46196@1" Pin0InfoVect0LinkObjId="SW-298344_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298345_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3636,-399 3636,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42149b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3636,-303 3636,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="46198@1" ObjectIDZND0="46196@0" Pin0InfoVect0LinkObjId="SW-298344_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298345_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3636,-303 3636,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_421ee40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3636,-148 3674,-148 3674,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="46195@1" Pin0InfoVect0LinkObjId="SW-298346_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3636,-148 3674,-148 3674,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_421f0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3674,-96 3674,-85 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="46195@0" ObjectIDZND0="g_421d7c0@0" Pin0InfoVect0LinkObjId="g_421d7c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298346_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3674,-96 3674,-85 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_421f300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3674,-51 3674,-39 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_421d7c0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_421d7c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3674,-51 3674,-39 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_421ffb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3666,-217 3666,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_421f560@0" ObjectIDZND0="46205@0" Pin0InfoVect0LinkObjId="SW-298347_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_421f560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3666,-217 3666,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4220210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3666,-269 3636,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="46205@1" ObjectIDZND0="46198@x" ObjectIDZND1="g_4215b10@0" ObjectIDZND2="g_421ca90@0" Pin0InfoVect0LinkObjId="SW-298345_0" Pin0InfoVect1LinkObjId="g_4215b10_0" Pin0InfoVect2LinkObjId="g_421ca90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298347_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3666,-269 3636,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4220f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3636,-286 3636,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="46198@0" ObjectIDZND0="46205@x" ObjectIDZND1="g_4215b10@0" ObjectIDZND2="g_421ca90@0" Pin0InfoVect0LinkObjId="SW-298347_0" Pin0InfoVect1LinkObjId="g_4215b10_0" Pin0InfoVect2LinkObjId="g_421ca90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298345_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3636,-286 3636,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4221180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3636,-269 3636,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="46205@x" ObjectIDND1="46198@x" ObjectIDND2="g_421ca90@0" ObjectIDZND0="g_4215b10@0" Pin0InfoVect0LinkObjId="g_4215b10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298347_0" Pin1InfoVect1LinkObjId="SW-298345_0" Pin1InfoVect2LinkObjId="g_421ca90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3636,-269 3636,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42213e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3604,-269 3636,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_421ca90@0" ObjectIDZND0="46205@x" ObjectIDZND1="46198@x" ObjectIDZND2="g_4215b10@0" Pin0InfoVect0LinkObjId="SW-298347_0" Pin0InfoVect1LinkObjId="SW-298345_0" Pin0InfoVect2LinkObjId="g_4215b10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_421ca90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3604,-269 3636,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4237fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3576,-220 3576,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_4237550@0" ObjectIDZND0="46204@0" Pin0InfoVect0LinkObjId="SW-298330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4237550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3576,-220 3576,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42422c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3527,-189 3527,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_42416a0@0" ObjectIDZND0="46203@1" Pin0InfoVect0LinkObjId="SW-298331_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_42416a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3527,-189 3527,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4242520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3503,-189 3503,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" ObjectIDND0="g_4240c10@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4240c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3503,-189 3503,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4247730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3454,-206 3454,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_4247990@0" ObjectIDZND0="46182@0" Pin0InfoVect0LinkObjId="SW-298301_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4247990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3454,-206 3454,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_424e590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3636,-163 3636,-182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_4214c10@0" ObjectIDZND0="g_4215b10@1" Pin0InfoVect0LinkObjId="g_4215b10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4214c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3636,-163 3636,-182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_424e7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3636,-416 3636,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46197@0" ObjectIDZND0="10916@0" Pin0InfoVect0LinkObjId="g_2076830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298345_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3636,-416 3636,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_424f020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-432 3766,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7062@1" ObjectIDZND0="10916@0" Pin0InfoVect0LinkObjId="g_2076830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42561_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-432 3766,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_424f850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3903,-432 3903,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7013@1" ObjectIDZND0="10916@0" Pin0InfoVect0LinkObjId="g_2076830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42380_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3903,-432 3903,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4250080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-432 4023,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7018@1" ObjectIDZND0="10916@0" Pin0InfoVect0LinkObjId="g_2076830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42398_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-432 4023,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42508b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-432 4143,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7023@1" ObjectIDZND0="10916@0" Pin0InfoVect0LinkObjId="g_2076830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42415_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-432 4143,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42510e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4263,-432 4263,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7028@1" ObjectIDZND0="10916@0" Pin0InfoVect0LinkObjId="g_2076830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42433_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4263,-432 4263,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4251910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4385,-409 4385,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7084@1" ObjectIDZND0="10916@0" Pin0InfoVect0LinkObjId="g_2076830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42652_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4385,-409 4385,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4252140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-483 4337,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7034@0" ObjectIDZND0="10916@0" Pin0InfoVect0LinkObjId="g_2076830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42454_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-483 4337,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4252970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4628,-409 4628,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7086@1" ObjectIDZND0="10917@0" Pin0InfoVect0LinkObjId="g_4252bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42654_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4628,-409 4628,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4252bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4817,-433 4817,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7037@1" ObjectIDZND0="10917@0" Pin0InfoVect0LinkObjId="g_4252970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42472_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4817,-433 4817,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4252e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-433 4938,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20586@1" ObjectIDZND0="10917@0" Pin0InfoVect0LinkObjId="g_4252970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-104245_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-433 4938,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4253090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5058,-433 5058,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7047@1" ObjectIDZND0="10917@0" Pin0InfoVect0LinkObjId="g_4252970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42509_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5058,-433 5058,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42532f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5205,-432 5205,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7052@1" ObjectIDZND0="10917@0" Pin0InfoVect0LinkObjId="g_4252970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42525_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5205,-432 5205,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4253550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5325,-432 5325,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7057@1" ObjectIDZND0="10917@0" Pin0InfoVect0LinkObjId="g_4252970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42543_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5325,-432 5325,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42537b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5471,-432 5471,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7066@1" ObjectIDZND0="10917@0" Pin0InfoVect0LinkObjId="g_4252970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42574_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5471,-432 5471,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4255ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4487,-487 4487,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7033@1" ObjectIDZND0="10917@0" Pin0InfoVect0LinkObjId="g_4252970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42453_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4487,-487 4487,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4259110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-929 3777,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_4258680@0" ObjectIDZND0="46199@0" Pin0InfoVect0LinkObjId="SW-298232_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4258680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-929 3777,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4259370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-929 3827,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="46199@1" ObjectIDZND0="7082@x" ObjectIDZND1="10915@0" Pin0InfoVect0LinkObjId="SW-42650_0" Pin0InfoVect1LinkObjId="g_41cbd00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298232_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-929 3827,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4259e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3827,-943 3827,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="7082@0" ObjectIDZND0="46199@x" ObjectIDZND1="10915@0" Pin0InfoVect0LinkObjId="SW-298232_0" Pin0InfoVect1LinkObjId="g_41cbd00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3827,-943 3827,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_425a0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3827,-929 3827,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="46199@x" ObjectIDND1="7082@x" ObjectIDZND0="10915@0" Pin0InfoVect0LinkObjId="g_41cbd00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-298232_0" Pin1InfoVect1LinkObjId="SW-42650_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3827,-929 3827,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_425a950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5046,-938 5046,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46200@0" ObjectIDZND0="46175@0" Pin0InfoVect0LinkObjId="g_21ada20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5046,-938 5046,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_425fd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-332 4385,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="g_200b7a0@0" ObjectIDZND1="7085@x" ObjectIDZND2="7084@x" Pin0InfoVect0LinkObjId="g_200b7a0_0" Pin0InfoVect1LinkObjId="SW-42653_0" Pin0InfoVect2LinkObjId="SW-42652_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-332 4385,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42606b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4385,-314 4385,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_200b7a0@0" ObjectIDZND0="7085@x" ObjectIDZND1="7084@x" Pin0InfoVect0LinkObjId="SW-42653_0" Pin0InfoVect1LinkObjId="SW-42652_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_200b7a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4385,-314 4385,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42608a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4385,-332 4385,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_200b7a0@0" ObjectIDZND0="7085@x" ObjectIDZND1="7084@x" Pin0InfoVect0LinkObjId="SW-42653_0" Pin0InfoVect1LinkObjId="SW-42652_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_200b7a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4385,-332 4385,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4260ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4640,-330 4628,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="g_1f301a0@0" ObjectIDZND1="7087@x" ObjectIDZND2="7086@x" Pin0InfoVect0LinkObjId="g_1f301a0_0" Pin0InfoVect1LinkObjId="SW-42655_0" Pin0InfoVect2LinkObjId="SW-42654_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4640,-330 4628,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4261550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4628,-316 4628,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1f301a0@0" ObjectIDZND0="7087@x" ObjectIDZND1="7086@x" Pin0InfoVect0LinkObjId="SW-42655_0" Pin0InfoVect1LinkObjId="SW-42654_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f301a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4628,-316 4628,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4261790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4628,-330 4628,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1f301a0@0" ObjectIDZND0="7087@x" ObjectIDZND1="7086@x" Pin0InfoVect0LinkObjId="SW-42655_0" Pin0InfoVect1LinkObjId="SW-42654_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f301a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4628,-330 4628,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4275860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3514,-272 3576,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_4234020@0" ObjectIDZND0="46204@1" Pin0InfoVect0LinkObjId="SW-298330_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4234020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3514,-272 3576,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4275a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3547,-311 3547,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="46194@1" ObjectIDZND0="46192@0" Pin0InfoVect0LinkObjId="SW-298327_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298328_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3547,-311 3547,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4275c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3547,-257 3547,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_4238200@0" ObjectIDZND0="46194@0" Pin0InfoVect0LinkObjId="SW-298328_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4238200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3547,-257 3547,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4275e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3547,-190 3547,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="46191@1" ObjectIDZND0="g_4238200@1" Pin0InfoVect0LinkObjId="g_4238200_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298329_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3547,-190 3547,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4276060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3547,-368 3547,-407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="46192@1" ObjectIDZND0="46193@1" Pin0InfoVect0LinkObjId="SW-298328_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298327_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3547,-368 3547,-407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4276290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3547,-424 3547,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46193@0" ObjectIDZND0="10916@0" Pin0InfoVect0LinkObjId="g_2076830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298328_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3547,-424 3547,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4276a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3527,-145 3547,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="46203@0" ObjectIDZND0="46176@x" ObjectIDZND1="46191@x" Pin0InfoVect0LinkObjId="CB-CX_LH.CX_LH_Cb3_0" Pin0InfoVect1LinkObjId="SW-298329_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298331_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3527,-145 3547,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42774f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3547,-127 3547,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="46176@0" ObjectIDZND0="46203@x" ObjectIDZND1="46191@x" Pin0InfoVect0LinkObjId="SW-298331_0" Pin0InfoVect1LinkObjId="SW-298329_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-CX_LH.CX_LH_Cb3_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3547,-127 3547,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4277750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3547,-145 3547,-154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="46203@x" ObjectIDND1="46176@x" ObjectIDZND0="46191@0" Pin0InfoVect0LinkObjId="SW-298329_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-298331_0" Pin1InfoVect1LinkObjId="CB-CX_LH.CX_LH_Cb3_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3547,-145 3547,-154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42779b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4652,-891 4652,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7076@1" ObjectIDZND0="46175@0" Pin0InfoVect0LinkObjId="g_21ada20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42636_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4652,-891 4652,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4277c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4652,-461 4652,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10917@0" ObjectIDZND0="7078@0" Pin0InfoVect0LinkObjId="SW-42638_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4252970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4652,-461 4652,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4277e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4652,-842 4652,-855 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7081@1" ObjectIDZND0="7076@0" Pin0InfoVect0LinkObjId="SW-42636_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42642_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4652,-842 4652,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42780d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4652,-801 4652,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="7091@1" ObjectIDZND0="7081@0" Pin0InfoVect0LinkObjId="SW-42642_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4652,-801 4652,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4278ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4627,-532 4652,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="7079@1" ObjectIDZND0="7078@x" ObjectIDZND1="7080@x" Pin0InfoVect0LinkObjId="SW-42638_0" Pin0InfoVect1LinkObjId="SW-42641_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42639_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4627,-532 4652,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42799c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4652,-515 4652,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="7078@1" ObjectIDZND0="7079@x" ObjectIDZND1="7080@x" Pin0InfoVect0LinkObjId="SW-42639_0" Pin0InfoVect1LinkObjId="SW-42641_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42638_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4652,-515 4652,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4279c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4652,-532 4652,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7079@x" ObjectIDND1="7078@x" ObjectIDZND0="7080@0" Pin0InfoVect0LinkObjId="SW-42641_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42639_0" Pin1InfoVect1LinkObjId="SW-42638_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4652,-532 4652,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4279e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-589 4652,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_41d77f0@0" ObjectIDZND0="7080@x" ObjectIDZND1="7077@x" Pin0InfoVect0LinkObjId="SW-42641_0" Pin0InfoVect1LinkObjId="SW-42637_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41d77f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-589 4652,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_427a970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4652,-579 4652,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="7080@1" ObjectIDZND0="g_41d77f0@0" ObjectIDZND1="7077@x" Pin0InfoVect0LinkObjId="g_41d77f0_0" Pin0InfoVect1LinkObjId="SW-42637_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42641_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4652,-579 4652,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_427abd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4652,-589 4652,-598 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_41d77f0@0" ObjectIDND1="7080@x" ObjectIDZND0="7077@1" Pin0InfoVect0LinkObjId="SW-42637_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41d77f0_0" Pin1InfoVect1LinkObjId="SW-42641_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4652,-589 4652,-598 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_427afd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5046,-1078 5046,-1057 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_21b5de0@0" ObjectIDZND0="g_21b8780@0" Pin0InfoVect0LinkObjId="g_21b8780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21b5de0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5046,-1078 5046,-1057 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_427b7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4691,-41 4691,-58 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2135e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2135e00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4691,-41 4691,-58 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_427ba40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4691,-103 4691,-115 4714,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2127c50@0" Pin0InfoVect0LinkObjId="g_2127c50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2135e00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4691,-103 4691,-115 4714,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_427ec50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4817,-51 4817,-41 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_211b2d0@1" ObjectIDZND0="34481@0" Pin0InfoVect0LinkObjId="EC-CX_LH.062Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_211b2d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4817,-51 4817,-41 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_427ee40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4817,-162 4817,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7040@1" ObjectIDZND0="g_1efec10@1" Pin0InfoVect0LinkObjId="g_1efec10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42475_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4817,-162 4817,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_427f030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4793,-115 4817,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2127c50@1" ObjectIDZND0="g_211b2d0@0" ObjectIDZND1="7040@x" Pin0InfoVect0LinkObjId="g_211b2d0_0" Pin0InfoVect1LinkObjId="SW-42475_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2127c50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4793,-115 4817,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_427f9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4817,-104 4817,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_211b2d0@0" ObjectIDZND0="g_2127c50@0" ObjectIDZND1="7040@x" Pin0InfoVect0LinkObjId="g_2127c50_0" Pin0InfoVect1LinkObjId="SW-42475_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_211b2d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4817,-104 4817,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_427fc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4817,-115 4817,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2127c50@0" ObjectIDND1="g_211b2d0@0" ObjectIDZND0="7040@0" Pin0InfoVect0LinkObjId="SW-42475_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2127c50_0" Pin1InfoVect1LinkObjId="g_211b2d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4817,-115 4817,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4280420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3428,-420 3428,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46184@0" ObjectIDZND0="10916@0" Pin0InfoVect0LinkObjId="g_2076830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3428,-420 3428,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4280c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3428,-364 3428,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="46183@1" ObjectIDZND0="46184@1" Pin0InfoVect0LinkObjId="SW-298300_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298299_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3428,-364 3428,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4280eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3428,-307 3428,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="46185@1" ObjectIDZND0="46183@0" Pin0InfoVect0LinkObjId="SW-298299_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298300_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3428,-307 3428,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4281110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3428,-171 3428,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="46181@1" ObjectIDZND0="g_42469e0@1" Pin0InfoVect0LinkObjId="g_42469e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298302_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3428,-171 3428,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4281370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3399,-92 3428,-92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_4245cb0@0" ObjectIDZND0="46207@x" ObjectIDZND1="46202@x" ObjectIDZND2="46181@x" Pin0InfoVect0LinkObjId="EC-CX_LH.081Ld_0" Pin0InfoVect1LinkObjId="SW-298305_0" Pin0InfoVect2LinkObjId="SW-298302_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4245cb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3399,-92 3428,-92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4077140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3428,-30 3428,-92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="46207@0" ObjectIDZND0="g_4245cb0@0" ObjectIDZND1="46202@x" ObjectIDZND2="46181@x" Pin0InfoVect0LinkObjId="g_4245cb0_0" Pin0InfoVect1LinkObjId="SW-298305_0" Pin0InfoVect2LinkObjId="SW-298302_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_LH.081Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3428,-30 3428,-92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40773a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3455,-107 3428,-107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="46202@1" ObjectIDZND0="g_4245cb0@0" ObjectIDZND1="46207@x" ObjectIDZND2="46181@x" Pin0InfoVect0LinkObjId="g_4245cb0_0" Pin0InfoVect1LinkObjId="EC-CX_LH.081Ld_0" Pin0InfoVect2LinkObjId="SW-298302_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298305_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3455,-107 3428,-107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4077e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3428,-92 3428,-107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_4245cb0@0" ObjectIDND1="46207@x" ObjectIDZND0="46202@x" ObjectIDZND1="46181@x" Pin0InfoVect0LinkObjId="SW-298305_0" Pin0InfoVect1LinkObjId="SW-298302_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4245cb0_0" Pin1InfoVect1LinkObjId="EC-CX_LH.081Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3428,-92 3428,-107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40780b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3428,-107 3428,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="46202@x" ObjectIDND1="g_4245cb0@0" ObjectIDND2="46207@x" ObjectIDZND0="46181@0" Pin0InfoVect0LinkObjId="SW-298302_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-298305_0" Pin1InfoVect1LinkObjId="g_4245cb0_0" Pin1InfoVect2LinkObjId="EC-CX_LH.081Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3428,-107 3428,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4078310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3455,-71 3455,-56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46202@0" ObjectIDZND0="g_424db40@0" Pin0InfoVect0LinkObjId="g_424db40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298305_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3455,-71 3455,-56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4078570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3454,-257 3428,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="46182@1" ObjectIDZND0="g_42469e0@0" ObjectIDZND1="g_4244f80@0" ObjectIDZND2="46185@x" Pin0InfoVect0LinkObjId="g_42469e0_0" Pin0InfoVect1LinkObjId="g_4244f80_0" Pin0InfoVect2LinkObjId="SW-298300_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-298301_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3454,-257 3428,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4079010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3428,-247 3428,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_42469e0@0" ObjectIDZND0="46182@x" ObjectIDZND1="g_4244f80@0" ObjectIDZND2="46185@x" Pin0InfoVect0LinkObjId="SW-298301_0" Pin0InfoVect1LinkObjId="g_4244f80_0" Pin0InfoVect2LinkObjId="SW-298300_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_42469e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3428,-247 3428,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4079270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3399,-267 3428,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_4244f80@0" ObjectIDZND0="46182@x" ObjectIDZND1="g_42469e0@0" ObjectIDZND2="46185@x" Pin0InfoVect0LinkObjId="SW-298301_0" Pin0InfoVect1LinkObjId="g_42469e0_0" Pin0InfoVect2LinkObjId="SW-298300_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4244f80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3399,-267 3428,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4079d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3428,-257 3428,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="46182@x" ObjectIDND1="g_42469e0@0" ObjectIDZND0="g_4244f80@0" ObjectIDZND1="46185@x" Pin0InfoVect0LinkObjId="g_4244f80_0" Pin0InfoVect1LinkObjId="SW-298300_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-298301_0" Pin1InfoVect1LinkObjId="g_42469e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3428,-257 3428,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4079f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3428,-267 3428,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_4244f80@0" ObjectIDND1="46182@x" ObjectIDND2="g_42469e0@0" ObjectIDZND0="46185@0" Pin0InfoVect0LinkObjId="SW-298300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4244f80_0" Pin1InfoVect1LinkObjId="SW-298301_0" Pin1InfoVect2LinkObjId="g_42469e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3428,-267 3428,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_407da30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3965,-1006 3965,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7007@1" ObjectIDZND0="7004@0" Pin0InfoVect0LinkObjId="SW-42342_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42346_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3965,-1006 3965,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_407dc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3965,-1061 3965,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="7004@1" ObjectIDZND0="7005@x" ObjectIDZND1="g_2117c10@0" ObjectIDZND2="7006@x" Pin0InfoVect0LinkObjId="SW-42343_0" Pin0InfoVect1LinkObjId="g_2117c10_0" Pin0InfoVect2LinkObjId="SW-42344_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42342_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3965,-1061 3965,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_407e8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3894,-601 3894,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7071@1" ObjectIDZND0="7074@1" Pin0InfoVect0LinkObjId="SW-42627_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42623_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3894,-601 3894,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_407eb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3915,-708 3894,-708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="g_407de10@0" ObjectIDZND0="7090@x" ObjectIDZND1="g_2075900@0" Pin0InfoVect0LinkObjId="g_20765d0_0" Pin0InfoVect1LinkObjId="g_2075900_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_407de10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3915,-708 3894,-708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_407f610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3894,-715 3894,-708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="7090@0" ObjectIDZND0="g_407de10@0" ObjectIDZND1="g_2075900@0" Pin0InfoVect0LinkObjId="g_407de10_0" Pin0InfoVect1LinkObjId="g_2075900_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20765d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3894,-715 3894,-708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_407f870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3894,-708 3894,-700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="g_407de10@0" ObjectIDND1="7090@x" ObjectIDZND0="g_2075900@0" Pin0InfoVect0LinkObjId="g_2075900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_407de10_0" Pin1InfoVect1LinkObjId="g_20765d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3894,-708 3894,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_407fad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4652,-714 4652,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="7091@0" ObjectIDZND0="g_1eb6650@0" Pin0InfoVect0LinkObjId="g_1eb6650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4652,-714 4652,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_407fd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4652,-645 4652,-634 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1eb6650@1" ObjectIDZND0="7077@0" Pin0InfoVect0LinkObjId="SW-42637_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1eb6650_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4652,-645 4652,-634 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4080560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3965,-980 3965,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7007@0" ObjectIDZND0="7003@1" Pin0InfoVect0LinkObjId="SW-42341_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42346_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3965,-980 3965,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40807c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3965,-924 3965,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7003@0" ObjectIDZND0="10915@0" Pin0InfoVect0LinkObjId="g_41cbd00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42341_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3965,-924 3965,-908 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectPoint_Layer"/><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-298003" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3384.000000 23.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298003" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46183"/>
     <cge:Term_Ref ObjectID="30991"/>
    <cge:TPSR_Ref TObjectID="46183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-298004" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3384.000000 23.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298004" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46183"/>
     <cge:Term_Ref ObjectID="30991"/>
    <cge:TPSR_Ref TObjectID="46183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-298006" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3384.000000 23.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298006" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46183"/>
     <cge:Term_Ref ObjectID="30991"/>
    <cge:TPSR_Ref TObjectID="46183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-298012" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3516.000000 20.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298012" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46192"/>
     <cge:Term_Ref ObjectID="31590"/>
    <cge:TPSR_Ref TObjectID="46192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-298013" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3516.000000 20.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298013" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46192"/>
     <cge:Term_Ref ObjectID="31590"/>
    <cge:TPSR_Ref TObjectID="46192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42336" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3599.000000 20.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42336" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46196"/>
     <cge:Term_Ref ObjectID="31598"/>
    <cge:TPSR_Ref TObjectID="46196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42337" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3599.000000 20.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42337" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46196"/>
     <cge:Term_Ref ObjectID="31598"/>
    <cge:TPSR_Ref TObjectID="46196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42338" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3599.000000 20.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42338" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46196"/>
     <cge:Term_Ref ObjectID="31598"/>
    <cge:TPSR_Ref TObjectID="46196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42273" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3757.000000 3.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42273" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7065"/>
     <cge:Term_Ref ObjectID="10253"/>
    <cge:TPSR_Ref TObjectID="7065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42268" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3757.000000 3.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42268" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7065"/>
     <cge:Term_Ref ObjectID="10253"/>
    <cge:TPSR_Ref TObjectID="7065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42215" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3872.000000 23.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42215" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7017"/>
     <cge:Term_Ref ObjectID="10157"/>
    <cge:TPSR_Ref TObjectID="7017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42216" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3872.000000 23.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42216" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7017"/>
     <cge:Term_Ref ObjectID="10157"/>
    <cge:TPSR_Ref TObjectID="7017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42211" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3872.000000 23.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42211" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7017"/>
     <cge:Term_Ref ObjectID="10157"/>
    <cge:TPSR_Ref TObjectID="7017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42221" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3995.000000 23.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42221" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7022"/>
     <cge:Term_Ref ObjectID="10167"/>
    <cge:TPSR_Ref TObjectID="7022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42222" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3995.000000 23.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42222" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7022"/>
     <cge:Term_Ref ObjectID="10167"/>
    <cge:TPSR_Ref TObjectID="7022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42217" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3995.000000 23.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42217" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7022"/>
     <cge:Term_Ref ObjectID="10167"/>
    <cge:TPSR_Ref TObjectID="7022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42661" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4117.000000 23.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42661" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7027"/>
     <cge:Term_Ref ObjectID="10177"/>
    <cge:TPSR_Ref TObjectID="7027"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42662" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4117.000000 23.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42662" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7027"/>
     <cge:Term_Ref ObjectID="10177"/>
    <cge:TPSR_Ref TObjectID="7027"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42657" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4117.000000 23.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42657" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7027"/>
     <cge:Term_Ref ObjectID="10177"/>
    <cge:TPSR_Ref TObjectID="7027"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42230" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4236.000000 23.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42230" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7032"/>
     <cge:Term_Ref ObjectID="10187"/>
    <cge:TPSR_Ref TObjectID="7032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42231" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4236.000000 23.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42231" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7032"/>
     <cge:Term_Ref ObjectID="10187"/>
    <cge:TPSR_Ref TObjectID="7032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42223" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4236.000000 23.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42223" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7032"/>
     <cge:Term_Ref ObjectID="10187"/>
    <cge:TPSR_Ref TObjectID="7032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42279" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5502.000000 -6.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42279" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7069"/>
     <cge:Term_Ref ObjectID="10261"/>
    <cge:TPSR_Ref TObjectID="7069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42274" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5502.000000 -6.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42274" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7069"/>
     <cge:Term_Ref ObjectID="10261"/>
    <cge:TPSR_Ref TObjectID="7069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42266" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5306.000000 23.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42266" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7061"/>
     <cge:Term_Ref ObjectID="10245"/>
    <cge:TPSR_Ref TObjectID="7061"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42267" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5306.000000 23.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42267" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7061"/>
     <cge:Term_Ref ObjectID="10245"/>
    <cge:TPSR_Ref TObjectID="7061"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42262" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5306.000000 23.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42262" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7061"/>
     <cge:Term_Ref ObjectID="10245"/>
    <cge:TPSR_Ref TObjectID="7061"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42260" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5191.000000 23.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42260" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7056"/>
     <cge:Term_Ref ObjectID="10235"/>
    <cge:TPSR_Ref TObjectID="7056"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42261" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5191.000000 23.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42261" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7056"/>
     <cge:Term_Ref ObjectID="10235"/>
    <cge:TPSR_Ref TObjectID="7056"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42256" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5191.000000 23.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42256" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7056"/>
     <cge:Term_Ref ObjectID="10235"/>
    <cge:TPSR_Ref TObjectID="7056"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42254" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5039.000000 23.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42254" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7051"/>
     <cge:Term_Ref ObjectID="10225"/>
    <cge:TPSR_Ref TObjectID="7051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42255" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5039.000000 23.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42255" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7051"/>
     <cge:Term_Ref ObjectID="10225"/>
    <cge:TPSR_Ref TObjectID="7051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42250" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5039.000000 23.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42250" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7051"/>
     <cge:Term_Ref ObjectID="10225"/>
    <cge:TPSR_Ref TObjectID="7051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42248" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4924.000000 23.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42248" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7046"/>
     <cge:Term_Ref ObjectID="10215"/>
    <cge:TPSR_Ref TObjectID="7046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42249" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4924.000000 23.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42249" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7046"/>
     <cge:Term_Ref ObjectID="10215"/>
    <cge:TPSR_Ref TObjectID="7046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42244" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4924.000000 23.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42244" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7046"/>
     <cge:Term_Ref ObjectID="10215"/>
    <cge:TPSR_Ref TObjectID="7046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42242" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4787.000000 23.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42242" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7041"/>
     <cge:Term_Ref ObjectID="10205"/>
    <cge:TPSR_Ref TObjectID="7041"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42243" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4787.000000 23.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42243" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7041"/>
     <cge:Term_Ref ObjectID="10205"/>
    <cge:TPSR_Ref TObjectID="7041"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42238" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4787.000000 23.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42238" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7041"/>
     <cge:Term_Ref ObjectID="10205"/>
    <cge:TPSR_Ref TObjectID="7041"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42287" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4002.000000 -844.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42287" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7075"/>
     <cge:Term_Ref ObjectID="10273"/>
    <cge:TPSR_Ref TObjectID="7075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42288" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4002.000000 -844.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42288" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7075"/>
     <cge:Term_Ref ObjectID="10273"/>
    <cge:TPSR_Ref TObjectID="7075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42280" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4002.000000 -844.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42280" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7075"/>
     <cge:Term_Ref ObjectID="10273"/>
    <cge:TPSR_Ref TObjectID="7075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42299" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4007.000000 -559.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42299" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7074"/>
     <cge:Term_Ref ObjectID="10271"/>
    <cge:TPSR_Ref TObjectID="7074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42300" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4007.000000 -559.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42300" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7074"/>
     <cge:Term_Ref ObjectID="10271"/>
    <cge:TPSR_Ref TObjectID="7074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42292" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4007.000000 -559.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42292" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7074"/>
     <cge:Term_Ref ObjectID="10271"/>
    <cge:TPSR_Ref TObjectID="7074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42312" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4761.000000 -831.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42312" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7081"/>
     <cge:Term_Ref ObjectID="10285"/>
    <cge:TPSR_Ref TObjectID="7081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42313" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4761.000000 -831.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42313" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7081"/>
     <cge:Term_Ref ObjectID="10285"/>
    <cge:TPSR_Ref TObjectID="7081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42305" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4761.000000 -831.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42305" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7081"/>
     <cge:Term_Ref ObjectID="10285"/>
    <cge:TPSR_Ref TObjectID="7081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42323" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4771.000000 -564.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42323" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7080"/>
     <cge:Term_Ref ObjectID="10283"/>
    <cge:TPSR_Ref TObjectID="7080"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42324" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4771.000000 -564.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42324" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7080"/>
     <cge:Term_Ref ObjectID="10283"/>
    <cge:TPSR_Ref TObjectID="7080"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42317" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4771.000000 -564.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42317" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7080"/>
     <cge:Term_Ref ObjectID="10283"/>
    <cge:TPSR_Ref TObjectID="7080"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-187408" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4905.000000 -1043.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187408" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11150"/>
     <cge:Term_Ref ObjectID="40312"/>
    <cge:TPSR_Ref TObjectID="11150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-187409" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4905.000000 -1043.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187409" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11150"/>
     <cge:Term_Ref ObjectID="40312"/>
    <cge:TPSR_Ref TObjectID="11150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-187401" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4905.000000 -1043.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187401" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11150"/>
     <cge:Term_Ref ObjectID="40312"/>
    <cge:TPSR_Ref TObjectID="11150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42206" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4638.000000 -1010.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42206" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7012"/>
     <cge:Term_Ref ObjectID="10147"/>
    <cge:TPSR_Ref TObjectID="7012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42207" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4638.000000 -1010.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42207" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7012"/>
     <cge:Term_Ref ObjectID="10147"/>
    <cge:TPSR_Ref TObjectID="7012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42199" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4638.000000 -1010.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42199" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7012"/>
     <cge:Term_Ref ObjectID="10147"/>
    <cge:TPSR_Ref TObjectID="7012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42194" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4099.000000 -1028.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42194" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7007"/>
     <cge:Term_Ref ObjectID="10137"/>
    <cge:TPSR_Ref TObjectID="7007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42195" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4099.000000 -1028.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42195" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7007"/>
     <cge:Term_Ref ObjectID="10137"/>
    <cge:TPSR_Ref TObjectID="7007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42187" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4099.000000 -1028.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42187" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7007"/>
     <cge:Term_Ref ObjectID="10137"/>
    <cge:TPSR_Ref TObjectID="7007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-298226" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4364.000000 -1111.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298226" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46188"/>
     <cge:Term_Ref ObjectID="31582"/>
    <cge:TPSR_Ref TObjectID="46188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-298227" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4364.000000 -1111.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298227" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46188"/>
     <cge:Term_Ref ObjectID="31582"/>
    <cge:TPSR_Ref TObjectID="46188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-298229" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4364.000000 -1111.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="298229" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46188"/>
     <cge:Term_Ref ObjectID="31582"/>
    <cge:TPSR_Ref TObjectID="46188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-42283" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3701.000000 -900.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42283" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10915"/>
     <cge:Term_Ref ObjectID="15216"/>
    <cge:TPSR_Ref TObjectID="10915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-42284" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3701.000000 -900.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42284" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10915"/>
     <cge:Term_Ref ObjectID="15216"/>
    <cge:TPSR_Ref TObjectID="10915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-42285" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3701.000000 -900.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42285" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10915"/>
     <cge:Term_Ref ObjectID="15216"/>
    <cge:TPSR_Ref TObjectID="10915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-104076" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3701.000000 -900.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="104076" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10915"/>
     <cge:Term_Ref ObjectID="15216"/>
    <cge:TPSR_Ref TObjectID="10915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-42196" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3701.000000 -900.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42196" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10915"/>
     <cge:Term_Ref ObjectID="15216"/>
    <cge:TPSR_Ref TObjectID="10915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="1" id="ME-42308" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5160.000000 -905.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42308" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46175"/>
     <cge:Term_Ref ObjectID="30854"/>
    <cge:TPSR_Ref TObjectID="46175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-42309" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5160.000000 -905.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42309" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46175"/>
     <cge:Term_Ref ObjectID="30854"/>
    <cge:TPSR_Ref TObjectID="46175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="1" id="ME-42310" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5160.000000 -905.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42310" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46175"/>
     <cge:Term_Ref ObjectID="30854"/>
    <cge:TPSR_Ref TObjectID="46175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="1" id="ME-104077" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5160.000000 -905.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="104077" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46175"/>
     <cge:Term_Ref ObjectID="30854"/>
    <cge:TPSR_Ref TObjectID="46175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="1" id="ME-42208" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5160.000000 -905.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42208" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46175"/>
     <cge:Term_Ref ObjectID="30854"/>
    <cge:TPSR_Ref TObjectID="46175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-42320" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5431.000000 -543.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42320" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10917"/>
     <cge:Term_Ref ObjectID="15218"/>
    <cge:TPSR_Ref TObjectID="10917"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-42663" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5431.000000 -543.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42663" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10917"/>
     <cge:Term_Ref ObjectID="15218"/>
    <cge:TPSR_Ref TObjectID="10917"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-42321" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5431.000000 -543.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42321" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10917"/>
     <cge:Term_Ref ObjectID="15218"/>
    <cge:TPSR_Ref TObjectID="10917"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-42209" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5431.000000 -543.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42209" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10917"/>
     <cge:Term_Ref ObjectID="15218"/>
    <cge:TPSR_Ref TObjectID="10917"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-42204" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5431.000000 -543.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42204" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10917"/>
     <cge:Term_Ref ObjectID="15218"/>
    <cge:TPSR_Ref TObjectID="10917"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-42295" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3526.000000 -541.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42295" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10916"/>
     <cge:Term_Ref ObjectID="15217"/>
    <cge:TPSR_Ref TObjectID="10916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-42296" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3526.000000 -541.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42296" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10916"/>
     <cge:Term_Ref ObjectID="15217"/>
    <cge:TPSR_Ref TObjectID="10916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-42297" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3526.000000 -541.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42297" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10916"/>
     <cge:Term_Ref ObjectID="15217"/>
    <cge:TPSR_Ref TObjectID="10916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-42198" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3526.000000 -541.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42198" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10916"/>
     <cge:Term_Ref ObjectID="15217"/>
    <cge:TPSR_Ref TObjectID="10916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-42197" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3526.000000 -541.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42197" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10916"/>
     <cge:Term_Ref ObjectID="15217"/>
    <cge:TPSR_Ref TObjectID="10916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42236" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4448.000000 -658.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42236" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7036"/>
     <cge:Term_Ref ObjectID="10195"/>
    <cge:TPSR_Ref TObjectID="7036"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42237" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4448.000000 -658.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42237" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7036"/>
     <cge:Term_Ref ObjectID="10195"/>
    <cge:TPSR_Ref TObjectID="7036"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42232" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4448.000000 -658.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42232" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7036"/>
     <cge:Term_Ref ObjectID="10195"/>
    <cge:TPSR_Ref TObjectID="7036"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3192" y="-1174"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3192" y="-1174"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3144" y="-1191"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3144" y="-1191"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3975" y="-1001"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3975" y="-1001"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4537" y="-1000"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4537" y="-1000"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3774" y="-354"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3774" y="-354"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3912" y="-354"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3912" y="-354"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4033" y="-354"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4033" y="-354"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4152" y="-354"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4152" y="-354"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4272" y="-354"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4272" y="-354"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4827" y="-351"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4827" y="-351"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4947" y="-351"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4947" y="-351"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5068" y="-351"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5068" y="-351"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5214" y="-350"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5214" y="-350"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="5334" y="-350"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="5334" y="-350"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5480" y="-350"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5480" y="-350"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4402" y="-595"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4402" y="-595"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4820" y="-1062"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4820" y="-1062"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3342" y="-1160"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3342" y="-1160"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3342" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3342" y="-1195"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="63" x="3748" y="-766"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="63" x="3748" y="-766"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="62" x="4521" y="-774"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="62" x="4521" y="-774"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="3293" y="-1071"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="3293" y="-1071"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="24" qtmmishow="hidden" width="96" x="3081" y="-787"/>
    </a>
   <metadata/><rect fill="white" height="24" opacity="0" stroke="white" transform="" width="96" x="3081" y="-787"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="17" qtmmishow="hidden" width="27" x="4325" y="-1019"/>
    </a>
   <metadata/><rect fill="white" height="17" opacity="0" stroke="white" transform="" width="27" x="4325" y="-1019"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="17" qtmmishow="hidden" width="28" x="3436" y="-360"/>
    </a>
   <metadata/><rect fill="white" height="17" opacity="0" stroke="white" transform="" width="28" x="3436" y="-360"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="17" qtmmishow="hidden" width="28" x="3555" y="-364"/>
    </a>
   <metadata/><rect fill="white" height="17" opacity="0" stroke="white" transform="" width="28" x="3555" y="-364"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="17" qtmmishow="hidden" width="29" x="3644" y="-356"/>
    </a>
   <metadata/><rect fill="white" height="17" opacity="0" stroke="white" transform="" width="29" x="3644" y="-356"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="67" x="3100" y="-848"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="67" x="3100" y="-848"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3192" y="-1174"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3144" y="-1191"/></g>
   <g href="35kV吕合变35kV罗吕线361间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3975" y="-1001"/></g>
   <g href="35kV吕合变35kV东吕大线362间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4537" y="-1000"/></g>
   <g href="35kV吕合变10kV1号电容器069间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3774" y="-354"/></g>
   <g href="35kV吕合变10kV煤矿Ⅰ回线061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3912" y="-354"/></g>
   <g href="35kV吕合变10kV楚雄四号院线063间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4033" y="-354"/></g>
   <g href="35kV吕合变10kV石鼓煤矿线065间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4152" y="-354"/></g>
   <g href="35kV吕合变10kV吕九线067间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4272" y="-354"/></g>
   <g href="35kV吕合变10kV吕合集镇线062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4827" y="-351"/></g>
   <g href="35kV吕合变10kV煤矿Ⅱ回线064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4947" y="-351"/></g>
   <g href="35kV吕合变10kV斗阁线066间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5068" y="-351"/></g>
   <g href="35kV吕合变10kV吕氮留守处068间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5214" y="-350"/></g>
   <g href="35kV吕合变10kV石吕线072间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="5334" y="-350"/></g>
   <g href="35kV吕合变10kV2号电容器079间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5480" y="-350"/></g>
   <g href="35kV吕合变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4402" y="-595"/></g>
   <g href="35kV吕合变35kV鹿城站用变线363间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4820" y="-1062"/></g>
   <g href="cx_配调_配网接线图35_楚雄.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3342" y="-1160"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3342" y="-1195"/></g>
   <g href="35kV吕合变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="63" x="3748" y="-766"/></g>
   <g href="35kV吕合变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="62" x="4521" y="-774"/></g>
   <g href="AVC吕合站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="3293" y="-1071"/></g>
   <g href="35kV吕合变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="24" qtmmishow="hidden" width="96" x="3081" y="-787"/></g>
   <g href="35kV吕合变35kV分段312间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="17" qtmmishow="hidden" width="27" x="4325" y="-1019"/></g>
   <g href="35kV吕合变10kV背阴线081间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="17" qtmmishow="hidden" width="28" x="3436" y="-360"/></g>
   <g href="35kV吕合变10kV3号电容器082间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="17" qtmmishow="hidden" width="28" x="3555" y="-364"/></g>
   <g href="35kV吕合变10kV接地变083间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="17" qtmmishow="hidden" width="29" x="3644" y="-356"/></g>
   <g href="35kV吕合变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="67" x="3100" y="-848"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425b7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4029.000000 1015.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425ba40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.000000 1000.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425bc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4043.000000 1030.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425bfb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4571.000000 998.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425c210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4594.000000 983.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425c450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4585.000000 1013.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425c780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4840.000000 1027.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425c9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4863.000000 1012.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425cc20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4854.000000 1042.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425cf50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3934.000000 829.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425d1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3957.000000 814.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425d3f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3948.000000 844.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425d720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3931.000000 547.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425d980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3954.000000 532.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425dbc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3945.000000 562.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425def0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4374.000000 642.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425e150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4397.000000 627.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425e390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4388.000000 657.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425e6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4295.000000 1098.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425e920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4318.000000 1083.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425eb60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4309.000000 1113.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425ee90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4696.000000 816.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425f0f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4719.000000 801.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425f330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4710.000000 831.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425f660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4700.000000 551.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425f8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4723.000000 536.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425fb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4714.000000 566.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4264a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3304.000000 -38.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4264c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3327.000000 -53.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4264ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3318.000000 -23.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4267cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3477.000000 -34.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4267f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3454.000000 -19.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4268250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3716.000000 -18.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42684b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3693.000000 -3.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42687e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5448.000000 -10.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4268a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5425.000000 5.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_426dbc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3642.000000 899.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_426de30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3642.000000 885.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_426e070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3642.000000 871.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_426e2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3648.000000 856.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_426e4f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3634.000000 841.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_426e820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3470.000000 540.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_426ea90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3470.000000 526.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_426ecd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3470.000000 512.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_426ef10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3476.000000 497.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_426f150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3462.000000 482.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4271f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5107.000000 905.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4272570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5107.000000 891.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42727b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5107.000000 877.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42729f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 862.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4272c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5099.000000 847.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4272f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5373.000000 544.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42731d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5373.000000 530.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4273410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5373.000000 516.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4273650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5379.000000 501.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4273890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5365.000000 486.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-42561">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 -391.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7062" ObjectName="SW-CX_LH.CX_LH_0691SW"/>
     <cge:Meas_Ref ObjectId="42561"/>
    <cge:TPSR_Ref TObjectID="7062"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42562">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3713.000000 -356.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7063" ObjectName="SW-CX_LH.CX_LH_06917SW"/>
     <cge:Meas_Ref ObjectId="42562"/>
    <cge:TPSR_Ref TObjectID="7063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42380">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3894.000000 -391.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7013" ObjectName="SW-CX_LH.CX_LH_0611SW"/>
     <cge:Meas_Ref ObjectId="42380"/>
    <cge:TPSR_Ref TObjectID="7013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42381">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3851.400000 -357.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7014" ObjectName="SW-CX_LH.CX_LH_06117SW"/>
     <cge:Meas_Ref ObjectId="42381"/>
    <cge:TPSR_Ref TObjectID="7014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42383">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3895.000000 -111.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7016" ObjectName="SW-CX_LH.CX_LH_0616SW"/>
     <cge:Meas_Ref ObjectId="42383"/>
    <cge:TPSR_Ref TObjectID="7016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42398">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4015.000000 -391.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7018" ObjectName="SW-CX_LH.CX_LH_0631SW"/>
     <cge:Meas_Ref ObjectId="42398"/>
    <cge:TPSR_Ref TObjectID="7018"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42399">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3970.800000 -357.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7019" ObjectName="SW-CX_LH.CX_LH_06317SW"/>
     <cge:Meas_Ref ObjectId="42399"/>
    <cge:TPSR_Ref TObjectID="7019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42401">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4016.000000 -111.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7021" ObjectName="SW-CX_LH.CX_LH_0636SW"/>
     <cge:Meas_Ref ObjectId="42401"/>
    <cge:TPSR_Ref TObjectID="7021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42415">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4134.000000 -391.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7023" ObjectName="SW-CX_LH.CX_LH_0651SW"/>
     <cge:Meas_Ref ObjectId="42415"/>
    <cge:TPSR_Ref TObjectID="7023"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42416">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4091.200000 -357.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7024" ObjectName="SW-CX_LH.CX_LH_06517SW"/>
     <cge:Meas_Ref ObjectId="42416"/>
    <cge:TPSR_Ref TObjectID="7024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42418">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4135.000000 -111.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7026" ObjectName="SW-CX_LH.CX_LH_0656SW"/>
     <cge:Meas_Ref ObjectId="42418"/>
    <cge:TPSR_Ref TObjectID="7026"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42433">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.000000 -391.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7028" ObjectName="SW-CX_LH.CX_LH_0671SW"/>
     <cge:Meas_Ref ObjectId="42433"/>
    <cge:TPSR_Ref TObjectID="7028"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42434">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4211.400000 -357.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7029" ObjectName="SW-CX_LH.CX_LH_06717SW"/>
     <cge:Meas_Ref ObjectId="42434"/>
    <cge:TPSR_Ref TObjectID="7029"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42436">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4255.000000 -111.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7031" ObjectName="SW-CX_LH.CX_LH_0676SW"/>
     <cge:Meas_Ref ObjectId="42436"/>
    <cge:TPSR_Ref TObjectID="7031"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42653">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4350.000000 -325.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7085" ObjectName="SW-CX_LH.CX_LH_09017SW"/>
     <cge:Meas_Ref ObjectId="42653"/>
    <cge:TPSR_Ref TObjectID="7085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42655">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4589.000000 -327.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7087" ObjectName="SW-CX_LH.CX_LH_09027SW"/>
     <cge:Meas_Ref ObjectId="42655"/>
    <cge:TPSR_Ref TObjectID="7087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42475">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4808.000000 -121.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7040" ObjectName="SW-CX_LH.CX_LH_0626SW"/>
     <cge:Meas_Ref ObjectId="42475"/>
    <cge:TPSR_Ref TObjectID="7040"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-104245">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4929.000000 -392.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20586" ObjectName="SW-CX_LH.CX_LH_0641SW"/>
     <cge:Meas_Ref ObjectId="104245"/>
    <cge:TPSR_Ref TObjectID="20586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42491">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4886.400000 -358.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7043" ObjectName="SW-CX_LH.CX_LH_06417SW"/>
     <cge:Meas_Ref ObjectId="42491"/>
    <cge:TPSR_Ref TObjectID="7043"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42493">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4929.000000 -112.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7045" ObjectName="SW-CX_LH.CX_LH_0646SW"/>
     <cge:Meas_Ref ObjectId="42493"/>
    <cge:TPSR_Ref TObjectID="7045"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42509">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 5050.000000 -392.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7047" ObjectName="SW-CX_LH.CX_LH_0661SW"/>
     <cge:Meas_Ref ObjectId="42509"/>
    <cge:TPSR_Ref TObjectID="7047"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42510">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5005.800000 -358.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7048" ObjectName="SW-CX_LH.CX_LH_06617SW"/>
     <cge:Meas_Ref ObjectId="42510"/>
    <cge:TPSR_Ref TObjectID="7048"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42512">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 5050.000000 -112.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7050" ObjectName="SW-CX_LH.CX_LH_0666SW"/>
     <cge:Meas_Ref ObjectId="42512"/>
    <cge:TPSR_Ref TObjectID="7050"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42525">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5196.000000 -391.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7052" ObjectName="SW-CX_LH.CX_LH_0681SW"/>
     <cge:Meas_Ref ObjectId="42525"/>
    <cge:TPSR_Ref TObjectID="7052"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42526">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5153.200000 -357.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7053" ObjectName="SW-CX_LH.CX_LH_06817SW"/>
     <cge:Meas_Ref ObjectId="42526"/>
    <cge:TPSR_Ref TObjectID="7053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42528">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5196.000000 -111.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7055" ObjectName="SW-CX_LH.CX_LH_0686SW"/>
     <cge:Meas_Ref ObjectId="42528"/>
    <cge:TPSR_Ref TObjectID="7055"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42543">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5316.000000 -391.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7057" ObjectName="SW-CX_LH.CX_LH_0721SW"/>
     <cge:Meas_Ref ObjectId="42543"/>
    <cge:TPSR_Ref TObjectID="7057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42544">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5273.400000 -357.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7058" ObjectName="SW-CX_LH.CX_LH_07217SW"/>
     <cge:Meas_Ref ObjectId="42544"/>
    <cge:TPSR_Ref TObjectID="7058"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42546">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5316.000000 -111.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7060" ObjectName="SW-CX_LH.CX_LH_0726SW"/>
     <cge:Meas_Ref ObjectId="42546"/>
    <cge:TPSR_Ref TObjectID="7060"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42574">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5462.000000 -391.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7066" ObjectName="SW-CX_LH.CX_LH_0791SW"/>
     <cge:Meas_Ref ObjectId="42574"/>
    <cge:TPSR_Ref TObjectID="7066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42575">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5419.200000 -357.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7067" ObjectName="SW-CX_LH.CX_LH_07917SW"/>
     <cge:Meas_Ref ObjectId="42575"/>
    <cge:TPSR_Ref TObjectID="7067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42636">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.136318 -850.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7076" ObjectName="SW-CX_LH.CX_LH_3022SW"/>
     <cge:Meas_Ref ObjectId="42636"/>
    <cge:TPSR_Ref TObjectID="7076"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42638">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.136318 -474.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7078" ObjectName="SW-CX_LH.CX_LH_0021SW"/>
     <cge:Meas_Ref ObjectId="42638"/>
    <cge:TPSR_Ref TObjectID="7078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42639">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4600.136318 -506.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7079" ObjectName="SW-CX_LH.CX_LH_00217SW"/>
     <cge:Meas_Ref ObjectId="42639"/>
    <cge:TPSR_Ref TObjectID="7079"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42622">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3885.000000 -850.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7070" ObjectName="SW-CX_LH.CX_LH_3011SW"/>
     <cge:Meas_Ref ObjectId="42622"/>
    <cge:TPSR_Ref TObjectID="7070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42624">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3885.000000 -474.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7072" ObjectName="SW-CX_LH.CX_LH_0011SW"/>
     <cge:Meas_Ref ObjectId="42624"/>
    <cge:TPSR_Ref TObjectID="7072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42625">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.045455 -0.000000 0.000000 -1.076923 3841.000000 -504.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7073" ObjectName="SW-CX_LH.CX_LH_00117SW"/>
     <cge:Meas_Ref ObjectId="42625"/>
    <cge:TPSR_Ref TObjectID="7073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42342">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3957.000000 -1020.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7004" ObjectName="SW-CX_LH.CX_LH_3616SW"/>
     <cge:Meas_Ref ObjectId="42342"/>
    <cge:TPSR_Ref TObjectID="7004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42341">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3957.000000 -919.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7003" ObjectName="SW-CX_LH.CX_LH_3611SW"/>
     <cge:Meas_Ref ObjectId="42341"/>
    <cge:TPSR_Ref TObjectID="7003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42343">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3918.000000 -1058.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7005" ObjectName="SW-CX_LH.CX_LH_36167SW"/>
     <cge:Meas_Ref ObjectId="42343"/>
    <cge:TPSR_Ref TObjectID="7005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42344">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.045455 -0.000000 0.000000 -1.076923 3994.000000 -1086.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7006" ObjectName="SW-CX_LH.CX_LH_3619SW"/>
     <cge:Meas_Ref ObjectId="42344"/>
    <cge:TPSR_Ref TObjectID="7006"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42361">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4519.000000 -1020.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7009" ObjectName="SW-CX_LH.CX_LH_3626SW"/>
     <cge:Meas_Ref ObjectId="42361"/>
    <cge:TPSR_Ref TObjectID="7009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42360">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4519.000000 -921.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7008" ObjectName="SW-CX_LH.CX_LH_3622SW"/>
     <cge:Meas_Ref ObjectId="42360"/>
    <cge:TPSR_Ref TObjectID="7008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42362">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4465.000000 -1061.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7010" ObjectName="SW-CX_LH.CX_LH_36267SW"/>
     <cge:Meas_Ref ObjectId="42362"/>
    <cge:TPSR_Ref TObjectID="7010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42363">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4555.000000 -1090.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7011" ObjectName="SW-CX_LH.CX_LH_3629SW"/>
     <cge:Meas_Ref ObjectId="42363"/>
    <cge:TPSR_Ref TObjectID="7011"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187368">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4801.000000 -1118.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11156" ObjectName="SW-CX_LH.CX_LH_3636SW"/>
     <cge:Meas_Ref ObjectId="187368"/>
    <cge:TPSR_Ref TObjectID="11156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187367">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4801.000000 -942.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11155" ObjectName="SW-CX_LH.CX_LH_3632SW"/>
     <cge:Meas_Ref ObjectId="187367"/>
    <cge:TPSR_Ref TObjectID="11155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187371">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4755.000000 -1139.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11158" ObjectName="SW-CX_LH.CX_LH_36367SW"/>
     <cge:Meas_Ref ObjectId="187371"/>
    <cge:TPSR_Ref TObjectID="11158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42454">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4328.000000 -478.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7034" ObjectName="SW-CX_LH.CX_LH_0121SW"/>
     <cge:Meas_Ref ObjectId="42454"/>
    <cge:TPSR_Ref TObjectID="7034"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42650">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3818.000000 -938.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7082" ObjectName="SW-CX_LH.CX_LH_3901SW"/>
     <cge:Meas_Ref ObjectId="42650"/>
    <cge:TPSR_Ref TObjectID="7082"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42651">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3788.000000 -977.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7083" ObjectName="SW-CX_LH.CX_LH_39017SW"/>
     <cge:Meas_Ref ObjectId="42651"/>
    <cge:TPSR_Ref TObjectID="7083"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42656">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5004.000000 -850.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7088" ObjectName="SW-CX_LH.CX_LH_3032SW"/>
     <cge:Meas_Ref ObjectId="42656"/>
    <cge:TPSR_Ref TObjectID="7088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187370">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4755.000000 -1073.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28381" ObjectName="SW-CX_LH.CX_LH_36360SW"/>
     <cge:Meas_Ref ObjectId="187370"/>
    <cge:TPSR_Ref TObjectID="28381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187369">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4755.000000 -978.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28380" ObjectName="SW-CX_LH.CX_LH_36327SW"/>
     <cge:Meas_Ref ObjectId="187369"/>
    <cge:TPSR_Ref TObjectID="28380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42455">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4287.400000 -512.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7035" ObjectName="SW-CX_LH.CX_LH_01217SW"/>
     <cge:Meas_Ref ObjectId="42455"/>
    <cge:TPSR_Ref TObjectID="7035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42623">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3858.000000 -596.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7071" ObjectName="SW-CX_LH.CX_LH_0016SW"/>
     <cge:Meas_Ref ObjectId="42623"/>
    <cge:TPSR_Ref TObjectID="7071"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42453">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4451.000000 -482.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7033" ObjectName="SW-CX_LH.CX_LH_0122SW"/>
     <cge:Meas_Ref ObjectId="42453"/>
    <cge:TPSR_Ref TObjectID="7033"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42637">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.136318 -593.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7077" ObjectName="SW-CX_LH.CX_LH_0026SW"/>
     <cge:Meas_Ref ObjectId="42637"/>
    <cge:TPSR_Ref TObjectID="7077"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42563">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3730.000000 -271.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7064" ObjectName="SW-CX_LH.CX_LH_0692SW"/>
     <cge:Meas_Ref ObjectId="42563"/>
    <cge:TPSR_Ref TObjectID="7064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42382">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3868.000000 -271.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7015" ObjectName="SW-CX_LH.CX_LH_0612SW"/>
     <cge:Meas_Ref ObjectId="42382"/>
    <cge:TPSR_Ref TObjectID="7015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42400">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3988.000000 -269.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7020" ObjectName="SW-CX_LH.CX_LH_0632SW"/>
     <cge:Meas_Ref ObjectId="42400"/>
    <cge:TPSR_Ref TObjectID="7020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42417">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4108.000000 -271.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7025" ObjectName="SW-CX_LH.CX_LH_0652SW"/>
     <cge:Meas_Ref ObjectId="42417"/>
    <cge:TPSR_Ref TObjectID="7025"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42435">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4228.000000 -270.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7030" ObjectName="SW-CX_LH.CX_LH_0672SW"/>
     <cge:Meas_Ref ObjectId="42435"/>
    <cge:TPSR_Ref TObjectID="7030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42474">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4781.000000 -268.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7039" ObjectName="SW-CX_LH.CX_LH_0622SW"/>
     <cge:Meas_Ref ObjectId="42474"/>
    <cge:TPSR_Ref TObjectID="7039"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42492">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4902.000000 -269.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7044" ObjectName="SW-CX_LH.CX_LH_0642SW"/>
     <cge:Meas_Ref ObjectId="42492"/>
    <cge:TPSR_Ref TObjectID="7044"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42511">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5022.000000 -267.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7049" ObjectName="SW-CX_LH.CX_LH_0662SW"/>
     <cge:Meas_Ref ObjectId="42511"/>
    <cge:TPSR_Ref TObjectID="7049"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42527">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5169.000000 -268.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7054" ObjectName="SW-CX_LH.CX_LH_0682SW"/>
     <cge:Meas_Ref ObjectId="42527"/>
    <cge:TPSR_Ref TObjectID="7054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42576">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5435.000000 -267.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7068" ObjectName="SW-CX_LH.CX_LH_0792SW"/>
     <cge:Meas_Ref ObjectId="42576"/>
    <cge:TPSR_Ref TObjectID="7068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42545">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5289.000000 -269.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7059" ObjectName="SW-CX_LH.CX_LH_0722SW"/>
     <cge:Meas_Ref ObjectId="42545"/>
    <cge:TPSR_Ref TObjectID="7059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42652">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4376.000000 -368.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7084" ObjectName="SW-CX_LH.CX_LH_0901SW"/>
     <cge:Meas_Ref ObjectId="42652"/>
    <cge:TPSR_Ref TObjectID="7084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42654">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4619.000000 -368.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7086" ObjectName="SW-CX_LH.CX_LH_0902SW"/>
     <cge:Meas_Ref ObjectId="42654"/>
    <cge:TPSR_Ref TObjectID="7086"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42472">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4808.000000 -392.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7037" ObjectName="SW-CX_LH.CX_LH_0621SW"/>
     <cge:Meas_Ref ObjectId="42472"/>
    <cge:TPSR_Ref TObjectID="7037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298240">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5037.000000 -933.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46200" ObjectName="SW-CX_LH.CX_LH_3902SW"/>
     <cge:Meas_Ref ObjectId="298240"/>
    <cge:TPSR_Ref TObjectID="46200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298241">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5007.000000 -966.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46201" ObjectName="SW-CX_LH.CX_LH_39027SW"/>
     <cge:Meas_Ref ObjectId="298241"/>
    <cge:TPSR_Ref TObjectID="46201"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298243">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4253.000000 -924.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46186" ObjectName="SW-CX_LH.CX_LH_3121SW"/>
     <cge:Meas_Ref ObjectId="298243"/>
    <cge:TPSR_Ref TObjectID="46186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298244">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4435.000000 -924.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46187" ObjectName="SW-CX_LH.CX_LH_3122SW"/>
     <cge:Meas_Ref ObjectId="298244"/>
    <cge:TPSR_Ref TObjectID="46187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298245">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4268.000000 -1006.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46189" ObjectName="SW-CX_LH.CX_LH_31217SW"/>
     <cge:Meas_Ref ObjectId="298245"/>
    <cge:TPSR_Ref TObjectID="46189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298246">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4405.000000 -1006.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46190" ObjectName="SW-CX_LH.CX_LH_31227SW"/>
     <cge:Meas_Ref ObjectId="298246"/>
    <cge:TPSR_Ref TObjectID="46190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4686.000000 -53.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298345">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3626.000000 -392.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46197" ObjectName="SW-CX_LH.CX_LH_083XC"/>
     <cge:Meas_Ref ObjectId="298345"/>
    <cge:TPSR_Ref TObjectID="46197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298345">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3626.000000 -279.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46198" ObjectName="SW-CX_LH.CX_LH_083XC1"/>
     <cge:Meas_Ref ObjectId="298345"/>
    <cge:TPSR_Ref TObjectID="46198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298346">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3665.000000 -91.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46195" ObjectName="SW-CX_LH.CX_LH_0836SW"/>
     <cge:Meas_Ref ObjectId="298346"/>
    <cge:TPSR_Ref TObjectID="46195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298347">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3657.000000 -228.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46205" ObjectName="SW-CX_LH.CX_LH_08360SW"/>
     <cge:Meas_Ref ObjectId="298347"/>
    <cge:TPSR_Ref TObjectID="46205"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298328">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3537.000000 -400.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46193" ObjectName="SW-CX_LH.CX_LH_082XC"/>
     <cge:Meas_Ref ObjectId="298328"/>
    <cge:TPSR_Ref TObjectID="46193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298328">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3537.000000 -287.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46194" ObjectName="SW-CX_LH.CX_LH_082XC1"/>
     <cge:Meas_Ref ObjectId="298328"/>
    <cge:TPSR_Ref TObjectID="46194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298300">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3418.000000 -396.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46184" ObjectName="SW-CX_LH.CX_LH_081XC"/>
     <cge:Meas_Ref ObjectId="298300"/>
    <cge:TPSR_Ref TObjectID="46184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298300">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3418.000000 -283.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46185" ObjectName="SW-CX_LH.CX_LH_081XC1"/>
     <cge:Meas_Ref ObjectId="298300"/>
    <cge:TPSR_Ref TObjectID="46185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298330">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3567.000000 -231.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46204" ObjectName="SW-CX_LH.CX_LH_08260SW"/>
     <cge:Meas_Ref ObjectId="298330"/>
    <cge:TPSR_Ref TObjectID="46204"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298331">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3494.000000 -140.000000)" xlink:href="#switch2:shape45_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46203" ObjectName="SW-CX_LH.CX_LH_08267SW"/>
     <cge:Meas_Ref ObjectId="298331"/>
    <cge:TPSR_Ref TObjectID="46203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298329">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3538.000000 -149.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46191" ObjectName="SW-CX_LH.CX_LH_0826SW"/>
     <cge:Meas_Ref ObjectId="298329"/>
    <cge:TPSR_Ref TObjectID="46191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298302">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3419.000000 -130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46181" ObjectName="SW-CX_LH.CX_LH_0816SW"/>
     <cge:Meas_Ref ObjectId="298302"/>
    <cge:TPSR_Ref TObjectID="46181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298305">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3446.000000 -66.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46202" ObjectName="SW-CX_LH.CX_LH_08167SW"/>
     <cge:Meas_Ref ObjectId="298305"/>
    <cge:TPSR_Ref TObjectID="46202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298232">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3786.000000 -903.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46199" ObjectName="SW-CX_LH.CX_LH_39010SW"/>
     <cge:Meas_Ref ObjectId="298232"/>
    <cge:TPSR_Ref TObjectID="46199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-298301">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3445.000000 -216.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46182" ObjectName="SW-CX_LH.CX_LH_08160SW"/>
     <cge:Meas_Ref ObjectId="298301"/>
    <cge:TPSR_Ref TObjectID="46182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42473">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4766.400000 -358.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7038" ObjectName="SW-CX_LH.CX_LH_06217SW"/>
     <cge:Meas_Ref ObjectId="42473"/>
    <cge:TPSR_Ref TObjectID="7038"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_LH.CX_LH_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3710.000000 -67.000000)" xlink:href="#capacitor:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40716" ObjectName="CB-CX_LH.CX_LH_Cb1"/>
    <cge:TPSR_Ref TObjectID="40716"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_LH.CX_LH_Cb2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5415.000000 -60.000000)" xlink:href="#capacitor:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40715" ObjectName="CB-CX_LH.CX_LH_Cb2"/>
    <cge:TPSR_Ref TObjectID="40715"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_LH.CX_LH_Cb3">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3575.000000 -17.000000)" xlink:href="#capacitor:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46176" ObjectName="CB-CX_LH.CX_LH_Cb3"/>
    <cge:TPSR_Ref TObjectID="46176"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_200b7a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4352.000000 -220.000000)" xlink:href="#lightningRod:shape127"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f301a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 -222.000000)" xlink:href="#lightningRod:shape127"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1eb6650">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4646.636318 -640.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2075900">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3889.500000 -642.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20830e0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4052.000000 -1200.000000)" xlink:href="#lightningRod:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ef9510">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4339.000000 -569.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1efa030">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3761.000000 -172.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1efad50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3899.000000 -171.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1efbd00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4019.000000 -171.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1efccb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4139.000000 -171.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1efdc60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4259.000000 -170.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1efec10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4812.000000 -168.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1eff960">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4933.000000 -167.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f00910">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5053.000000 -167.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2100d30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5200.000000 -166.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2101bb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5320.000000 -168.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2102ac0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5466.000000 -167.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21092a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5004.000000 -800.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2117c10">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3925.500000 -1109.500000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2118820">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4788.000000 -1203.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_211b2d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4812.000000 -46.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2127c50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4709.000000 -106.000000)" xlink:href="#lightningRod:shape22"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21350e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4799.000000 -1207.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2138a50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3818.000000 -1031.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21a3390">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4489.500000 -1112.500000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21a3f60">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4604.000000 -1201.000000)" xlink:href="#lightningRod:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21ae140">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3796.500000 -1018.500000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21b8780">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5037.000000 -1020.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21b9610">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5015.500000 -1007.500000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41d77f0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4727.136318 -596.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41daf20">
    <use class="BV-0KV" transform="matrix(0.000000 -0.360000 -0.350877 -0.000000 4395.175439 -536.180000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41dcc90">
    <use class="BV-0KV" transform="matrix(-0.000000 0.360000 0.350877 0.000000 4429.824561 -552.820000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41df9b0">
    <use class="BV-0KV" transform="matrix(0.000000 -0.360000 -0.350877 -0.000000 4686.175439 -320.180000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41e1720">
    <use class="BV-0KV" transform="matrix(0.000000 -0.360000 -0.350877 -0.000000 4445.175439 -322.180000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41e33b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3930.000000 -197.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41e40e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41e4e10">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3845.000000 -247.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41e5bc0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3843.000000 -167.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41e9880">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4060.000000 -44.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41ea5b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4057.000000 -195.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41eb2e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4181.000000 -42.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41ec010">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4178.000000 -193.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41ecd40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4297.000000 -36.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41eda70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -187.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41ee7a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4846.000000 -183.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41ef4d0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5561.000000 -244.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41f0280">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5559.000000 -164.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41f2f90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4974.000000 -184.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41f3cc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4977.000000 -33.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41f49f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5104.000000 -182.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41f5720">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5107.000000 -31.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41f6450">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5244.000000 -181.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41f7180">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5247.000000 -30.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41f7eb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5356.000000 -184.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41f8be0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5359.000000 -33.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4214c10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3619.000000 -130.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4215b10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3631.000000 -177.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_421ca90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3597.000000 -215.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_421d7c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3667.500000 -46.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4234020">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3507.000000 -218.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4238200">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.500000 -199.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4244f80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3392.000000 -213.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4245cb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3392.000000 -38.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_42469e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3423.500000 -189.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_407de10">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3973.000000 -715.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="10915" cx="3894" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10915" cx="4262" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10916" cx="3894" cy="-461" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10916" cx="3636" cy="-461" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10916" cx="3766" cy="-461" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10916" cx="3903" cy="-461" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10916" cx="4023" cy="-461" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10916" cx="4143" cy="-461" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10916" cx="4263" cy="-461" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10916" cx="4385" cy="-461" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10916" cx="4337" cy="-461" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46175" cx="4809" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46175" cx="4527" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46175" cx="5013" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46175" cx="5046" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46175" cx="4444" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10917" cx="4628" cy="-461" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10917" cx="4817" cy="-461" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10917" cx="4938" cy="-461" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10917" cx="5058" cy="-461" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10917" cx="5205" cy="-461" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10917" cx="5325" cy="-461" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10917" cx="5471" cy="-461" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10917" cx="4487" cy="-461" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10916" cx="3547" cy="-461" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46175" cx="4652" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10917" cx="4652" cy="-461" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10915" cx="3827" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10916" cx="3428" cy="-461" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10915" cx="3965" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eede90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3720.000000 -62.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eee4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3871.000000 -16.000000) translate(0,15)">煤矿I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eee820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3972.000000 -16.000000) translate(0,15)">楚雄四号院线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eeea80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4110.000000 -16.000000) translate(0,15)">石鼓煤矿线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eeed20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4239.000000 -16.000000) translate(0,15)">吕九线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eeef60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4787.000000 -16.000000) translate(0,15)">吕合集镇线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eef1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4901.000000 -16.000000) translate(0,15)">煤矿II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eef3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5033.000000 -16.000000) translate(0,15)">斗阁线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eef5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5157.000000 -21.000000) translate(0,15)">吕氮留守处</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eef790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5301.000000 -21.000000) translate(0,15)">石吕线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eefbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5430.000000 -58.000000) translate(0,15)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eefe10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4878.000000 -1201.000000) translate(0,15)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eefe10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4878.000000 -1201.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eefe10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4878.000000 -1201.000000) translate(0,51)">鹿</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eefe10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4878.000000 -1201.000000) translate(0,69)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eefe10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4878.000000 -1201.000000) translate(0,87)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eefe10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4878.000000 -1201.000000) translate(0,105)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eefe10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4878.000000 -1201.000000) translate(0,123)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eefe10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4878.000000 -1201.000000) translate(0,141)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ef0280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4484.000000 -1275.000000) translate(0,15)">至110kV东瓜变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ef0280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4484.000000 -1275.000000) translate(0,33)">东吕大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ef0cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 -1279.000000) translate(0,15)">至110kV罗家屯变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ef8dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4309.000000 -206.000000) translate(0,15)">10kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ef92d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4574.000000 -206.000000) translate(0,15)">10kVII段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2107830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3972.000000 -952.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2107e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3972.000000 -1051.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21080a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3978.000000 -1140.000000) translate(0,12)">3619</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21082e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3904.000000 -1076.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2108520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4537.000000 -1000.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2108760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4534.000000 -951.000000) translate(0,12)">3622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21089a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4534.000000 -1050.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2108be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4447.000000 -1070.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2108e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4553.000000 -1101.000000) translate(0,12)">3629</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2109060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5020.000000 -880.000000) translate(0,12)">3032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2109d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3905.000000 -833.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210a220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3903.000000 -882.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210a460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3905.000000 -575.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210a6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3903.000000 -506.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210a8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3830.000000 -557.000000) translate(0,12)">00117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210ab20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3834.000000 -964.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210ad60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3776.000000 -995.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210afa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4659.136318 -880.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210b1e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4661.136318 -831.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210b420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4661.136318 -573.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4659.136318 -504.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210b8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4589.136318 -558.000000) translate(0,12)">00217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210bae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4402.000000 -595.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210bd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4344.000000 -508.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210bf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3774.000000 -354.000000) translate(0,12)">069</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210c1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3772.000000 -421.000000) translate(0,12)">0691</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210c3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3702.000000 -409.000000) translate(0,12)">06917</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210c620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3912.000000 -354.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210c860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3910.000000 -421.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210caa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3840.000000 -409.000000) translate(0,12)">06117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210cce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3911.000000 -141.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210cf20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.000000 -354.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210d160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4031.000000 -141.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210d3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4030.000000 -421.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210d5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3960.000000 -409.000000) translate(0,12)">06317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210d820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4080.000000 -409.000000) translate(0,12)">06517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210da60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4150.000000 -421.000000) translate(0,12)">0651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210dca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4151.000000 -141.000000) translate(0,12)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210dee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4152.000000 -354.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210e120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4272.000000 -354.000000) translate(0,12)">067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210e360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4271.000000 -141.000000) translate(0,12)">0676</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210e5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4270.000000 -421.000000) translate(0,12)">0671</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210e7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4200.000000 -409.000000) translate(0,12)">06717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210ea20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4392.000000 -398.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210ec60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4339.000000 -372.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210eea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4635.000000 -400.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210f0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4578.000000 -379.000000) translate(0,12)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4826.000000 -142.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210f560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4827.000000 -351.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210f7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4947.000000 -351.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210f9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4946.000000 -142.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210fc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4945.000000 -422.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_210fe60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4875.000000 -410.000000) translate(0,12)">06417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21100a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5068.000000 -351.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21102e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5065.000000 -422.000000) translate(0,12)">0661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2110520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4995.000000 -410.000000) translate(0,12)">06617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2110760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5066.000000 -142.000000) translate(0,12)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21109a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5214.000000 -350.000000) translate(0,12)">068</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2110be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5213.000000 -141.000000) translate(0,12)">0686</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2110e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5212.000000 -421.000000) translate(0,12)">0681</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2111060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5142.000000 -409.000000) translate(0,12)">06817</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21112a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5480.000000 -350.000000) translate(0,12)">079</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21114e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5478.000000 -421.000000) translate(0,12)">0791</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2111720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5408.000000 -409.000000) translate(0,12)">07917</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2111960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5262.000000 -409.000000) translate(0,12)">07217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2111ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5332.000000 -421.000000) translate(0,12)">0721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2111de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5333.000000 -141.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2112020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5334.000000 -350.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2112340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3089.000000 -1023.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2112340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3089.000000 -1023.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2112340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3089.000000 -1023.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2112340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3089.000000 -1023.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2112340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3089.000000 -1023.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2112340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3089.000000 -1023.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2112340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3089.000000 -1023.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21126b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -585.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21126b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -585.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21126b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -585.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21126b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -585.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21126b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -585.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21126b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -585.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21126b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -585.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21126b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -585.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21126b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -585.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21126b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -585.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21126b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -585.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21126b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -585.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21126b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -585.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21126b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -585.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21126b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -585.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21126b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -585.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21126b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -585.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21126b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -585.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_21130c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3230.500000 -1163.500000) translate(0,16)">吕合变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2113470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3650.000000 -933.000000) translate(0,12)">35kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21136b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3377.000000 -483.000000) translate(0,12)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21138f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5261.000000 -482.000000) translate(0,12)">10kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2113b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4820.000000 -1062.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2113d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4817.000000 -973.000000) translate(0,12)">3632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2113fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4817.000000 -1149.000000) translate(0,12)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21141f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4741.000000 -1153.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2114860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3957.000000 -737.000000) translate(0,12)">温 度：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2114f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3729.000000 -735.000000) translate(0,12)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2114f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3729.000000 -735.000000) translate(0,27)">SZ11-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2114f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3729.000000 -735.000000) translate(0,42)">35±3×2.5%/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2115170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4481.000000 -738.000000) translate(0,12)">2号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2115170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4481.000000 -738.000000) translate(0,27)">SZ9-4000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2115170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4481.000000 -738.000000) translate(0,42)">35±3×2.5%/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2125190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3957.000000 -754.000000) translate(0,12)">档 位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2125800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4684.000000 -728.000000) translate(0,12)">温 度：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2125e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4684.000000 -745.000000) translate(0,12)">档 位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21260b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3975.000000 -1001.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2128df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4573.000000 50.000000) translate(0,15)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2128df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4573.000000 50.000000) translate(0,33)">S13-MR-100kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_212e8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4742.000000 -1082.000000) translate(0,12)">36360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_212ef10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4737.000000 -992.000000) translate(0,12)">36327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_212f150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3208.000000 -225.000000) translate(0,15)">4785</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2133080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4276.000000 -564.000000) translate(0,12)">01217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_21340d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3353.000000 -1152.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2134580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3353.000000 -1187.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2134870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3105.000000 -848.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2134ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4723.000000 -1314.000000) translate(0,15)">至500kV鹿城变外接电源</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_213e470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3901.000000 -626.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2143270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4494.000000 -512.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2148070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4659.136318 -623.000000) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21663a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3773.000000 -301.000000) translate(0,12)">0692</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21669d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3911.000000 -301.000000) translate(0,12)">0612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2166c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4031.000000 -299.000000) translate(0,12)">0632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2166e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4151.000000 -301.000000) translate(0,12)">0652</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2167090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4271.000000 -300.000000) translate(0,12)">0672</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2174f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4825.000000 -298.000000) translate(0,12)">0622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21755a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4945.000000 -299.000000) translate(0,12)">0642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21757e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5065.000000 -297.000000) translate(0,12)">0662</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217a120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5212.000000 -298.000000) translate(0,12)">0682</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217ee50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5332.000000 -299.000000) translate(0,12)">0722</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217f670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5478.000000 -297.000000) translate(0,12)">0792</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_218ba60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4825.000000 -422.000000) translate(0,12)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_218c220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3081.000000 -188.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_218c220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3081.000000 -188.000000) translate(0,38)">心变运二班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_218e710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3208.000000 -198.500000) translate(0,16)">13508785260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_218e710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3208.000000 -198.500000) translate(0,36)">18787879001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_218e710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3208.000000 -198.500000) translate(0,56)">18787879002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21917c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3747.000000 -763.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2191df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4525.136318 -774.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2193c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4979.000000 -688.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2193c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4979.000000 -688.000000) translate(0,33)">S9-50kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_219f120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3309.500000 -1059.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_219f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3083.000000 -786.000000) translate(0,20)">隔刀远控</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21a0c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3922.000000 -1260.000000) translate(0,15)">35kV罗吕线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b5570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5053.000000 -959.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b5ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4995.000000 -984.000000) translate(0,12)">39027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4253a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5122.000000 -935.000000) translate(0,12)">35kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425a320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3774.000000 -957.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425ab40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4326.000000 -1018.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425adf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4269.000000 -955.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425b030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4284.000000 -1037.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425b270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4451.000000 -955.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_425b4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4421.000000 -1037.000000) translate(0,12)">31227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42619f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3437.000000 -359.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4261fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3435.000000 -161.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4262210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3460.000000 -84.000000) translate(0,12)">08167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4262450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3485.000000 -3.000000) translate(0,15)">3号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4262690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3600.000000 -60.000000) translate(0,15)">1号小电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4262690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3600.000000 -60.000000) translate(0,33)">阻成套</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4262690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3600.000000 -60.000000) translate(0,51)">装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4263680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3556.000000 -363.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4263b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3564.000000 -295.000000) translate(0,12)">08260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4263dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3494.000000 -220.000000) translate(0,12)">08267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4264010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3554.000000 -180.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4264250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3645.000000 -355.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4264490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3673.000000 -259.000000) translate(0,12)">08360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42646d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3681.000000 -122.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_427e670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3446.000000 -289.000000) translate(0,12)">08160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_407a1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3378.000000 -1.000000) translate(0,12)">10kV背阴线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_407d400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4767.000000 -410.000000) translate(0,12)">06217</text>
  </g><g id="CircleFilled_Layer">
   <ellipse DF8003:Layer="PUBLIC" cx="3673" cy="-23" fill="none" fillStyle="0" rx="9.5" ry="10.5" stroke="rgb(60,120,255)" stroke-width="0.594017"/>
   <ellipse DF8003:Layer="PUBLIC" cx="3673" cy="-2" fill="none" fillStyle="0" rx="9.5" ry="10.5" stroke="rgb(60,120,255)" stroke-width="0.594017"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_LH.CX_LH_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3373,-461 4410,-461 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10916" ObjectName="BS-CX_LH.CX_LH_9IM"/>
    <cge:TPSR_Ref TObjectID="10916"/></metadata>
   <polyline fill="none" opacity="0" points="3373,-461 4410,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LH.CX_LH_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4446,-461 5510,-461 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10917" ObjectName="BS-CX_LH.CX_LH_9IIM"/>
    <cge:TPSR_Ref TObjectID="10917"/></metadata>
   <polyline fill="none" opacity="0" points="4446,-461 5510,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LH.CX_LH_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3660,-908 4293,-908 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10915" ObjectName="BS-CX_LH.CX_LH_3IM"/>
    <cge:TPSR_Ref TObjectID="10915"/></metadata>
   <polyline fill="none" opacity="0" points="3660,-908 4293,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LH.CX_LH_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4394,-908 5244,-908 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="46175" ObjectName="BS-CX_LH.CX_LH_3IIM"/>
    <cge:TPSR_Ref TObjectID="46175"/></metadata>
   <polyline fill="none" opacity="0" points="4394,-908 5244,-908 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3180.500000 -1115.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-42304" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4010.000000 -736.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42304" ObjectName="CX_LH:CX_LH_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-104505" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4010.000000 -753.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="104505" ObjectName="CX_LH:CX_LH_1T_Tap"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-42328" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4737.000000 -727.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42328" ObjectName="CX_LH:CX_LH_2T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-104506" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4737.000000 -744.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="104506" ObjectName="CX_LH:CX_LH_2T_Tap"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-200691" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3205.000000 -979.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200691" ObjectName="CX_LH:CX_LH_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-200692" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3203.000000 -940.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200692" ObjectName="CX_LH:CX_LH_sumQ"/>
    </metadata>
   </g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3070" y="-1075"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3293" y="-1070"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37330" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3356.000000 -1083.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5912" ObjectName="DYN-CX_LH"/>
     <cge:Meas_Ref ObjectId="37330"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_LH.CX_LH_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="10305"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.882353 3859.000000 -711.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.882353 3859.000000 -711.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="7090" ObjectName="TF-CX_LH.CX_LH_1T"/>
    <cge:TPSR_Ref TObjectID="7090"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_LH.CX_LH_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="10309"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.969697 -0.000000 0.000000 -0.941176 4614.136318 -710.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.969697 -0.000000 0.000000 -0.941176 4614.136318 -710.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="7091" ObjectName="TF-CX_LH.CX_LH_2T"/>
    <cge:TPSR_Ref TObjectID="7091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_LH.CX_LH_Zyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="10301"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5045.000000 -791.000000)" xlink:href="#transformer2:shape55_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5045.000000 -791.000000)" xlink:href="#transformer2:shape55_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="7089" ObjectName="TF-CX_LH.CX_LH_Zyb"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4706.000000 52.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4706.000000 52.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_LH"/>
</svg>