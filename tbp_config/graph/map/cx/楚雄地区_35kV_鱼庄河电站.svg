<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1199 1546 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3332580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3333080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3333a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33345c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3335840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3336360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3336f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33379d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33383f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3338d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3338d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_333aa50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_333aa50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_333bcf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_333d830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_333e480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_333ecd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_333f6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3340cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33414c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3341b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3342540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3343720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33440a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3344b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3349fe0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_334acc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33468d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3347e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33488d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_334bcf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_334d050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1211" width="1556" x="3112" y="-1204"/>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="4000" cy="-971" fill="none" fillStyle="0" r="6.5" stroke="rgb(0,238,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4010" cy="-971" fill="none" fillStyle="0" r="6.5" stroke="rgb(0,238,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3999" cy="-958" fill="none" fillStyle="0" r="6.5" stroke="rgb(0,238,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4010" cy="-958" fill="none" fillStyle="0" r="6.5" stroke="rgb(0,238,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3575" cy="-776" fill="none" fillStyle="0" r="6.5" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3586" cy="-781" fill="none" fillStyle="0" r="6.5" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3575" cy="-787" fill="none" fillStyle="0" r="6.5" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4000" cy="-261" fill="none" fillStyle="0" r="7.5" stroke="rgb(218,165,32)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4010" cy="-256" fill="none" fillStyle="0" r="7.5" stroke="rgb(218,165,32)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4011" cy="-268" fill="none" fillStyle="0" r="7.5" stroke="rgb(218,165,32)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4569" cy="-771" fill="none" fillStyle="0" r="6.5" stroke="rgb(0,238,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4580" cy="-765" fill="none" fillStyle="0" r="6.5" stroke="rgb(0,238,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4569" cy="-760" fill="none" fillStyle="0" r="6.5" stroke="rgb(0,238,0)" stroke-width="1"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c452c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4018.000000 114.500000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c46c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4048.000000 25.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c478d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4034.500000 60.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c745a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4044.000000 96.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c75100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4044.000000 78.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c75380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4024.000000 45.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c75bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4020.000000 134.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3590,-490 3596,-483 3592,-481 3586,-488 3590,-490 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" points="4043,-959 4048,-959 4044,-967 4043,-960 4043,-959 " stroke="rgb(0,238,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(255,255,0)" points="3618,-771 3623,-771 3619,-779 3618,-772 3618,-771 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(255,255,0)" points="3461,-476 3464,-476 3463,-472 3461,-476 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(244,164,96)" points="3964,-261 3968,-261 3967,-257 3964,-261 " stroke="rgb(218,165,32)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" points="4613,-761 4618,-761 4614,-769 4613,-762 4613,-761 " stroke="rgb(0,238,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" points="4259,-800 4262,-800 4261,-796 4259,-800 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" points="4385,-801 4388,-801 4387,-797 4385,-801 " stroke="rgb(0,255,0)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3865.000000 -887.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3945.000000 -676.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3751,-1105 3904,-1105 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3751,-1105 3904,-1105 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3817,-857 4065,-857 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3817,-857 4065,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3480,-629 4065,-629 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3480,-629 4065,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3472,-184 3578,-184 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3472,-184 3578,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3704,-184 3810,-184 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3704,-184 3810,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-368 3680,-316 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3680,-368 3680,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3875,-362 4491,-362 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3875,-362 4491,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4168,-665 4663,-665 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4168,-665 4663,-665 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3237.000000 -1119.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-93767" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4112.000000 -132.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93767" ObjectName="SB_YZH:SB_YZH_611BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-93768" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4112.000000 -111.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93768" ObjectName="SB_YZH:SB_YZH_611BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-93769" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4112.000000 -97.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93769" ObjectName="SB_YZH:SB_YZH_611BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-93770" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4112.000000 -78.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93770" ObjectName="SB_YZH:SB_YZH_611BK_Ic"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-93771" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4112.000000 -59.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93771" ObjectName="SB_YZH:SB_YZH_611BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-93772" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4112.000000 -40.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93772" ObjectName="SB_YZH:SB_YZH_611BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-93773" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4112.000000 -26.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93773" ObjectName="SB_YZH:SB_YZH_611BK_COS"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-93776" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4237.000000 -96.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93776" ObjectName="SB_YZH:SB_YZH_612BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-93777" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4237.000000 -77.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93777" ObjectName="SB_YZH:SB_YZH_612BK_Ic"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-93778" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4237.000000 -58.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93778" ObjectName="SB_YZH:SB_YZH_612BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-93779" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4237.000000 -39.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93779" ObjectName="SB_YZH:SB_YZH_612BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-93780" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4237.000000 -25.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93780" ObjectName="SB_YZH:SB_YZH_612BK_COS"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-93774" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4237.000000 -131.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93774" ObjectName="SB_YZH:SB_YZH_612BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-93775" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4237.000000 -110.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93775" ObjectName="SB_YZH:SB_YZH_612BK_Uc"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b821d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -1026.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b821d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -1026.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b821d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -1026.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b821d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -1026.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b821d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -1026.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b821d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -1026.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b821d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -1026.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c9a520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -588.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c9a520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -588.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c9a520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -588.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c9a520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -588.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c9a520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -588.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c9a520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -588.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c9a520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -588.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c9a520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -588.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c9a520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -588.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c9a520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -588.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c9a520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -588.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c9a520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -588.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c9a520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -588.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c9a520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -588.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c9a520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -588.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c9a520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -588.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c9a520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -588.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c9a520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -588.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2a28f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3276.000000 -1167.500000) translate(0,16)">鱼庄河电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_29ef2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3964.000000 -192.000000) translate(0,10)">6kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2d6f5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4150.000000 -390.000000) translate(0,10)">6kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2a3d920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4393.000000 -238.000000) translate(0,10)">41B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_29bbaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4347.000000 -188.000000) translate(0,10)">厂用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_29f1510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3623.000000 -457.000000) translate(0,10)">42B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2b69060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3575.000000 -408.000000) translate(0,10)">厂用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2b74ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4453.000000 -856.000000) translate(0,10)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2a0a550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.000000 -870.000000) translate(0,10)">鄂嘉线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2a081f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -868.000000) translate(0,10)">平掌线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2a36340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4550.000000 -856.000000) translate(0,10)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2d19f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3788.000000 -851.000000) translate(0,10)">10kV专用母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2cdc780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3969.000000 -1054.000000) translate(0,10)">10kV专用母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2ce5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3886.000000 -1074.000000) translate(0,10)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2ce5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3886.000000 -1074.000000) translate(0,22)">湾</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2ce5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3886.000000 -1074.000000) translate(0,34)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2ce5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3886.000000 -1074.000000) translate(0,46)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2ce5120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3886.000000 -1074.000000) translate(0,58)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2d21250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3553.000000 -892.000000) translate(0,10)">35kV段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2d21fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3495.000000 -649.000000) translate(0,10)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2cf9e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3788.000000 -1123.000000) translate(0,10)">大湾电站10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2cf9fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3743.000000 -956.000000) translate(0,10)">大湾电站施工台变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2cf0430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -202.000000) translate(0,10)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2cf05a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4218.000000 -200.000000) translate(0,10)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2bf5c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3533.000000 -359.000000) translate(0,10)">鱼</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2bf5c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3533.000000 -359.000000) translate(0,22)">庄</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2bf5c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3533.000000 -359.000000) translate(0,34)">河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2bf5c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3533.000000 -359.000000) translate(0,46)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2bf5e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3638.000000 -387.000000) translate(0,10)">纳嫩河电站35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2d423e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3537.000000 -551.000000) translate(0,9)">3712</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2d42550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3534.000000 -609.000000) translate(0,9)">3711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2d42800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3537.000000 -582.000000) translate(0,9)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2d42970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3767.000000 -581.000000) translate(0,9)">372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2d42ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3767.000000 -611.000000) translate(0,9)">3721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2d42c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3771.000000 -550.000000) translate(0,9)">3722</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2d42dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3907.000000 -419.000000) translate(0,9)">621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2d42f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3910.000000 -390.000000) translate(0,9)">6211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2d430a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3999.000000 -347.000000) translate(0,9)">6201</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2d43210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4139.000000 -348.000000) translate(0,9)">6111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2d43380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4140.000000 -319.000000) translate(0,9)">611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2ce8a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4254.000000 -348.000000) translate(0,9)">6121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2ce8bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4252.000000 -318.000000) translate(0,9)">612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2ce8d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4377.000000 -345.000000) translate(0,9)">6231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2ce8e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4428.000000 -393.000000) translate(0,9)">6221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2ce9000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4427.000000 -418.000000) translate(0,9)">622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2ce9170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4427.000000 -623.000000) translate(0,9)">421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2ce92e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4430.000000 -650.000000) translate(0,9)">4211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2ce9450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4489.000000 -728.000000) translate(0,9)">473</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2ce95c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4491.000000 -698.000000) translate(0,9)">4731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2ce9730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -685.000000) translate(0,9)">4701</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2ce98a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4369.000000 -727.000000) translate(0,9)">472</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2ce9a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4371.000000 -696.000000) translate(0,9)">4721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2ce9b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4243.000000 -702.000000) translate(0,9)">4711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2ce9cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4411.000000 -756.000000) translate(0,9)">4732D</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2ce9e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4246.000000 -729.000000) translate(0,9)">471</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cea110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.000000 -887.000000) translate(0,9)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cea280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3886.000000 -888.000000) translate(0,9)">0711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cea3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3879.000000 -983.000000) translate(0,9)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cea560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3968.000000 -704.000000) translate(0,9)">303</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cd0a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3966.000000 -658.000000) translate(0,9)">3031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cd0b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3561.000000 -695.000000) translate(0,9)">3701</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2cd0ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3804.000000 -703.000000) translate(0,12)">Ud%=6.67%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2cd1d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3810.000000 -739.000000) translate(0,12)">2.5MVA,Y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2d1e150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3808.000000 -721.000000) translate(0,12)">35±2x2.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2d1e980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3809.000000 -765.000000) translate(0,12)">S9-2500/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2d1f5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3809.000000 -788.000000) translate(0,12)">3号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2c79e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3885.000000 -920.000000) translate(0,9)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="9" graphid="g_2c7a280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4582.000000 -660.000000) translate(0,8)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2ce1410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3535.000000 -429.000000) translate(0,9)">3712D</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cec570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3658.000000 -694.000000) translate(0,9)">3701D</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2ced240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3909.000000 -581.000000) translate(0,9)">311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cbbd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3907.000000 -610.000000) translate(0,9)">3111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cbc5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4169.000000 -795.000000) translate(0,9)">4712D</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cbc710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4301.000000 -791.000000) translate(0,9)">4722D</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cbcc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4488.000000 -587.000000) translate(0,9)">4212D</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cbd010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4478.000000 -457.000000) translate(0,9)">6222D</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cbd420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3958.000000 -461.000000) translate(0,9)">6212D</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cbd5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3967.000000 -580.000000) translate(0,9)">S7－4000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cbdb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3966.000000 -558.000000) translate(0,9)">38.5±5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2c519b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.000000 -542.000000) translate(0,9)">6.3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2c51ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4036.000000 -552.000000) translate(0,9)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2c52160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3964.000000 -524.000000) translate(0,9)">Y/△－11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2c52910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3966.000000 -504.000000) translate(0,9)">UR%＝7.08</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2c53220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3966.000000 -486.000000) translate(0,9)">I0%＝1.2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2c535f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4541.000000 -566.000000) translate(0,9)">S7－800/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cd8290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4545.000000 -544.000000) translate(0,9)">11±5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cd8520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4559.000000 -527.000000) translate(0,9)">6.3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cd86d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4610.000000 -537.000000) translate(0,9)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cd8880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4538.000000 -509.000000) translate(0,9)">Y/△－11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cd8a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4539.000000 -490.000000) translate(0,9)">UR%＝5.33</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cd8be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4081.000000 -179.000000) translate(0,9)">SFW-1600-8/1430</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cd9530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4078.000000 -163.000000) translate(0,9)">6.3kV、183.3A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cd9b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4204.000000 -181.000000) translate(0,9)">SFW-1600-8/1430</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2cd9f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4209.000000 -165.000000) translate(0,9)">6.3kV、183.3A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2bb56d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4488.000000 -789.000000) translate(0,9)">4732</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2bb6300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3756.000000 -942.000000) translate(0,12)">S11-400/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2bb6910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3843.000000 -1084.000000) translate(0,9)">0411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb7140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3994.000000 -764.000000) translate(0,12)">3号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2cfc020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3959.000000 -601.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cfc450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3815.000000 -518.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_2cfc690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4541.000000 -591.000000) translate(0,12)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cfc8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4309.000000 -520.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2cfcc30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3433.000000 -174.000000) translate(0,11)">110kV双柏变35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2d04e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3537.000000 -222.000000) translate(0,9)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_2d052b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3700.000000 -176.000000) translate(0,11)">35kV鄂嘉变35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2d05770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3768.000000 -222.000000) translate(0,9)">332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2d37df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3498.000000 -461.000000) translate(0,9)">3702</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2c9db90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4089.000000 -149.000000) translate(0,9)">cos∮=0.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2c9efd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4220.000000 -151.000000) translate(0,9)">cos∮=0.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ca88b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3266.000000 -232.000000) translate(0,12)">7813955</text>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4129,-231 4129,-232 4129,-233 4128,-234 4128,-235 4127,-236 4127,-236 4126,-237 4125,-238 4124,-238 4123,-238 4122,-239 4121,-239 4121,-239 4120,-239 4119,-238 4118,-238 4117,-238 4116,-237 4115,-236 4115,-236 4114,-235 4114,-234 4113,-233 4113,-232 4113,-231 " stroke="rgb(205,133,63)" stroke-width="0.42"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4129,-232 4129,-231 4129,-230 4130,-229 4130,-228 4131,-227 4131,-226 4132,-226 4133,-225 4134,-225 4135,-224 4136,-224 4137,-224 4138,-224 4139,-224 4140,-224 4141,-225 4142,-225 4143,-226 4144,-226 4144,-227 4145,-228 4145,-229 4146,-230 4146,-231 4146,-232 " stroke="rgb(205,133,63)" stroke-width="0.44"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3981,-980 3982,-980 3982,-980 3983,-980 3983,-980 3984,-980 3984,-980 3985,-980 3985,-981 3986,-981 3986,-982 3986,-982 3987,-983 3987,-983 3987,-984 3987,-984 3987,-985 3987,-985 3986,-986 3986,-986 3986,-987 3985,-987 3985,-988 3984,-988 3984,-988 3983,-988 3983,-988 3982,-988 3982,-988 3981,-988 " stroke="rgb(0,238,0)" stroke-width="0.0275"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3981,-988 3982,-988 3982,-988 3983,-988 3983,-989 3984,-989 3984,-989 3984,-990 3985,-990 3985,-990 3985,-991 3985,-992 3986,-992 3986,-993 3986,-993 3985,-994 3985,-994 3985,-995 3985,-995 3984,-996 3984,-996 3984,-996 3983,-997 3983,-997 3982,-997 " stroke="rgb(0,238,0)" stroke-width="0.03"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4551,-782 4552,-782 4552,-782 4553,-782 4553,-782 4553,-783 4554,-783 4554,-783 4555,-784 4555,-784 4555,-785 4555,-785 4555,-785 4555,-786 4555,-787 4555,-787 4555,-787 4555,-788 4555,-788 4554,-789 4554,-789 4553,-789 4553,-790 4553,-790 4552,-790 4552,-790 4551,-790 " stroke="rgb(0,255,0)" stroke-width="0.0275"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4551,-790 4552,-790 4552,-790 4553,-790 4553,-791 4554,-791 4554,-791 4554,-792 4555,-792 4555,-792 4555,-793 4555,-794 4556,-794 4556,-795 4556,-795 4555,-796 4555,-796 4555,-797 4555,-797 4554,-798 4554,-798 4554,-798 4553,-799 4553,-799 4552,-799 " stroke="rgb(0,255,0)" stroke-width="0.03"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4242,-232 4242,-233 4242,-234 4241,-235 4241,-236 4240,-237 4240,-237 4239,-238 4238,-239 4237,-239 4236,-239 4235,-240 4234,-240 4234,-240 4233,-240 4232,-239 4231,-239 4230,-239 4229,-238 4228,-237 4228,-237 4227,-236 4227,-235 4226,-234 4226,-233 4226,-232 " stroke="rgb(205,133,63)" stroke-width="0.2625"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4242,-233 4242,-232 4242,-231 4243,-230 4243,-229 4244,-228 4244,-227 4245,-227 4246,-226 4247,-226 4248,-225 4249,-225 4250,-225 4251,-225 4252,-225 4253,-225 4254,-226 4255,-226 4256,-227 4257,-227 4257,-228 4258,-229 4258,-230 4259,-231 4259,-232 4259,-233 " stroke="rgb(205,133,63)" stroke-width="0.275"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3119" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3121" y="-1027"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-598"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="19" stroke="rgb(0,238,0)" stroke-width="1" width="11" x="3994" y="-943"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="19" stroke="rgb(0,238,0)" stroke-width="1" width="11" x="4039" y="-973"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="19" stroke="rgb(255,255,0)" stroke-width="1" width="11" x="3569" y="-756"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="19" stroke="rgb(255,255,0)" stroke-width="1" width="11" x="3614" y="-785"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="19" stroke="rgb(255,255,0)" stroke-width="1" width="11" x="3522" y="-585"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="19" stroke="rgb(255,255,0)" stroke-width="1" width="11" x="3753" y="-584"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="10" stroke="rgb(255,255,0)" stroke-width="1" width="5" x="3492" y="-441"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="10" stroke="rgb(255,255,0)" stroke-width="1" width="5" x="3460" y="-479"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="19" stroke="rgb(255,255,0)" stroke-width="1" width="11" x="3522" y="-225"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="19" stroke="rgb(255,255,0)" stroke-width="1" width="11" x="3753" y="-225"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="11" stroke="rgb(255,255,0)" stroke-width="1" width="22" x="3699" y="-350"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="19" stroke="rgb(255,255,0)" stroke-width="1" width="11" x="3894" y="-584"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="19" stroke="rgb(218,165,32)" stroke-width="1" width="11" x="3894" y="-422"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="12" stroke="rgb(218,165,32)" stroke-width="1" width="8" x="3962" y="-265"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="12" stroke="rgb(218,165,32)" stroke-width="1" width="8" x="4008" y="-302"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="12" stroke="rgb(218,165,32)" stroke-width="1" width="8" x="4126" y="-322"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="12" stroke="rgb(218,165,32)" stroke-width="1" width="8" x="4239" y="-323"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="12" stroke="rgb(218,165,32)" stroke-width="1" width="8" x="4365" y="-319"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="12" stroke="rgb(218,165,32)" stroke-width="1" width="8" x="4415" y="-415"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="16" stroke="rgb(0,255,0)" stroke-width="1" width="9" x="4415" y="-625"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="16" stroke="rgb(0,255,0)" stroke-width="1" width="9" x="4231" y="-727"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="16" stroke="rgb(0,255,0)" stroke-width="1" width="9" x="4356" y="-726"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="16" stroke="rgb(0,255,0)" stroke-width="1" width="9" x="4477" y="-726"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="16" stroke="rgb(0,255,0)" stroke-width="1" width="10" x="4563" y="-738"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="19" stroke="rgb(0,238,0)" stroke-width="1" width="11" x="4609" y="-775"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="10" stroke="rgb(0,255,0)" stroke-width="1" width="5" x="4258" y="-803"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="10" stroke="rgb(0,255,0)" stroke-width="1" width="5" x="4384" y="-804"/>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2c94df0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3842.000000 -1006.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3249" y="-1178"/></g>
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3200" y="-1195"/></g>
  </g><g id="Circle_Layer">
   <circle DF8003:Layer="PUBLIC" cx="4130" cy="-232" fill="none" r="25" stroke="rgb(218,165,32)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4130" cy="-283" fill="none" r="2" stroke="rgb(218,165,32)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4243" cy="-232" fill="none" r="25" stroke="rgb(218,165,32)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4243" cy="-283" fill="none" r="2" stroke="rgb(218,165,32)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3900" cy="-493" fill="none" r="25" stroke="rgb(255,165,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3900" cy="-533" fill="none" r="25" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4419" cy="-443" fill="none" r="2" stroke="rgb(218,165,32)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4419" cy="-494" fill="none" r="25" stroke="rgb(205,133,63)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4419" cy="-534" fill="none" r="25" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4420" cy="-586" fill="none" r="2" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3955" cy="-784" fill="none" r="25" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3955" cy="-744" fill="none" r="25" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4022" cy="-882" fill="none" r="2" stroke="rgb(0,238,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3874" cy="-881" fill="none" r="2.5" stroke="rgb(0,238,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3874" cy="-941" fill="none" r="2" stroke="rgb(0,238,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4369" cy="-283" fill="none" r="2" stroke="rgb(218,165,32)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4368" cy="-238" fill="none" r="17.5" stroke="rgb(218,165,32)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4368" cy="-218" fill="none" r="17.5" stroke="rgb(218,165,32)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3594" cy="-449" fill="none" r="17.5" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3595" cy="-429" fill="none" r="17" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3495" cy="-399" fill="none" r="7.5" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3495" cy="-409" fill="none" r="7.5" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4237" cy="-690" fill="none" r="2.5" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4361" cy="-688" fill="none" r="2.5" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4481" cy="-688" fill="none" r="2.5" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4592" cy="-687" fill="none" r="2" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4130" cy="-341" fill="none" r="2" stroke="rgb(218,165,32)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4243" cy="-341" fill="none" r="2" stroke="rgb(218,165,32)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4369" cy="-340" fill="none" r="2" stroke="rgb(218,165,32)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3900" cy="-385" fill="none" r="2" stroke="rgb(218,165,32)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3989" cy="-341" fill="none" r="2" stroke="rgb(218,165,32)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4420" cy="-644" fill="none" r="2" stroke="rgb(0,255,0)" stroke-width="1"/>
   <ellipse DF8003:Layer="PUBLIC" cx="3955" cy="-833" fill="none" rx="3.5" ry="2.5" stroke="rgb(0,238,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4419" cy="-388" fill="none" r="2" stroke="rgb(218,165,32)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3795" cy="-1000" fill="none" r="16" stroke="rgb(0,238,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3795" cy="-974" fill="none" r="16" stroke="rgb(0,238,0)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3789.000000 -1040.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3865.000000 -1047.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3865.000000 -956.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3947.000000 -632.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3640.000000 -672.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3589.000000 -669.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4130" x2="4130" y1="-257" y2="-310"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4130" x2="4125" y1="-290" y2="-299"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4135" x2="4130" y1="-299" y2="-290"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4135" x2="4125" y1="-299" y2="-299"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4130" x2="4135" y1="-275" y2="-266"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4125" x2="4130" y1="-266" y2="-275"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4125" x2="4135" y1="-266" y2="-266"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4130" x2="4130" y1="-333" y2="-322"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4130" x2="4130" y1="-348" y2="-363"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4243" x2="4243" y1="-257" y2="-311"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4243" x2="4238" y1="-291" y2="-299"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4248" x2="4243" y1="-299" y2="-291"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4248" x2="4238" y1="-299" y2="-299"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4243" x2="4248" y1="-275" y2="-267"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4238" x2="4243" y1="-267" y2="-275"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4238" x2="4248" y1="-267" y2="-267"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4243" x2="4243" y1="-333" y2="-323"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4243" x2="4243" y1="-348" y2="-363"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3900" x2="3900" y1="-468" y2="-423"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3900" x2="3905" y1="-444" y2="-435"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3895" x2="3900" y1="-435" y2="-444"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3895" x2="3905" y1="-435" y2="-435"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3900" x2="3895" y1="-450" y2="-459"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3905" x2="3900" y1="-459" y2="-450"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3905" x2="3895" y1="-459" y2="-459"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3900" x2="3900" y1="-392" y2="-403"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3900" x2="3900" y1="-377" y2="-362"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3889" x2="3900" y1="-527" y2="-533"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3911" x2="3900" y1="-527" y2="-533"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3900" x2="3900" y1="-533" y2="-546"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,193,37)" stroke-width="1" x1="3889" x2="3911" y1="-483" y2="-483"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,193,37)" stroke-width="1" x1="3900" x2="3889" y1="-501" y2="-483"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,193,37)" stroke-width="1" x1="3911" x2="3900" y1="-483" y2="-501"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4419" x2="4419" y1="-469" y2="-415"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4419" x2="4424" y1="-435" y2="-427"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4414" x2="4419" y1="-427" y2="-435"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4414" x2="4424" y1="-427" y2="-427"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4419" x2="4414" y1="-451" y2="-459"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4424" x2="4419" y1="-459" y2="-451"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4424" x2="4414" y1="-459" y2="-459"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4419" x2="4419" y1="-378" y2="-363"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4409" x2="4419" y1="-528" y2="-534"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4430" x2="4419" y1="-528" y2="-534"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4419" x2="4419" y1="-534" y2="-546"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(205,133,63)" stroke-width="1" x1="4409" x2="4431" y1="-483" y2="-483"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(205,133,63)" stroke-width="1" x1="4420" x2="4409" y1="-502" y2="-483"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(205,133,63)" stroke-width="1" x1="4431" x2="4420" y1="-483" y2="-502"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4420" x2="4420" y1="-559" y2="-609"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4420" x2="4415" y1="-593" y2="-602"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4425" x2="4420" y1="-602" y2="-593"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4425" x2="4415" y1="-602" y2="-602"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4420" x2="4425" y1="-578" y2="-569"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4415" x2="4420" y1="-569" y2="-578"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4415" x2="4425" y1="-569" y2="-569"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4420" x2="4420" y1="-636" y2="-625"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4420" x2="4420" y1="-651" y2="-666"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4018" x2="4025" y1="-874" y2="-874"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4022" x2="4022" y1="-874" y2="-859"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3999" x2="3995" y1="-959" y2="-957"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3999" x2="4002" y1="-959" y2="-957"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3999" x2="3999" y1="-959" y2="-963"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3999" x2="3995" y1="-972" y2="-970"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3999" x2="4002" y1="-972" y2="-970"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3999" x2="3999" y1="-972" y2="-975"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4014" x2="4013" y1="-961" y2="-958"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4011" x2="4011" y1="-972" y2="-975"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4011" x2="4014" y1="-972" y2="-970"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4008" x2="4014" y1="-961" y2="-961"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4010" x2="4008" y1="-958" y2="-961"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4011" x2="4008" y1="-972" y2="-970"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3956" x2="3956" y1="-643" y2="-628"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3955" x2="3955" y1="-675" y2="-685"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3955" x2="3955" y1="-709" y2="-719"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3965" x2="3955" y1="-751" y2="-744"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3944" x2="3955" y1="-751" y2="-744"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3955" x2="3955" y1="-744" y2="-732"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3965" x2="3943" y1="-795" y2="-795"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3954" x2="3965" y1="-776" y2="-795"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3943" x2="3954" y1="-795" y2="-776"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4015" x2="4015" y1="-1015" y2="-1025"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4022" x2="4022" y1="-889" y2="-903"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4045" x2="3999" y1="-903" y2="-903"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3999" x2="3999" y1="-903" y2="-952"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3999" x2="3981" y1="-959" y2="-959"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3981" x2="3981" y1="-959" y2="-980"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4045" x2="3982" y1="-1015" y2="-1015"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3982" x2="3982" y1="-997" y2="-1015"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4045" x2="4045" y1="-903" y2="-959"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4045" x2="4045" y1="-973" y2="-1015"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4018" x2="4025" y1="-889" y2="-889"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4020" x2="4014" y1="-881" y2="-879"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4030" x2="4024" y1="-884" y2="-882"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4011" x2="4011" y1="-878" y2="-886"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="4011" x2="4001" y1="-882" y2="-882"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3870" x2="3877" y1="-873" y2="-873"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3874" x2="3874" y1="-873" y2="-858"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3874" x2="3874" y1="-888" y2="-897"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3870" x2="3877" y1="-888" y2="-888"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3872" x2="3866" y1="-881" y2="-879"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3882" x2="3876" y1="-884" y2="-882"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3863" x2="3863" y1="-878" y2="-886"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3863" x2="3853" y1="-882" y2="-882"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3874" x2="3874" y1="-922" y2="-960"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3874" x2="3869" y1="-948" y2="-957"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3879" x2="3874" y1="-957" y2="-948"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3879" x2="3869" y1="-957" y2="-957"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3874" x2="3879" y1="-939" y2="-930"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3869" x2="3874" y1="-930" y2="-939"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3869" x2="3879" y1="-930" y2="-930"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3874" x2="3849" y1="-1003" y2="-1003"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3849" x2="3849" y1="-1003" y2="-1013"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4236" x2="4236" y1="-682" y2="-667"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4236" x2="4236" y1="-697" y2="-711"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4236" x2="4236" y1="-728" y2="-777"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4236" x2="4236" y1="-792" y2="-847"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4236" x2="4261" y1="-810" y2="-810"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4261" x2="4261" y1="-810" y2="-800"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4361" x2="4361" y1="-681" y2="-666"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4362" x2="4362" y1="-696" y2="-710"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4361" x2="4361" y1="-726" y2="-775"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4361" x2="4361" y1="-790" y2="-845"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4482" x2="4482" y1="-681" y2="-666"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4481" x2="4481" y1="-696" y2="-710"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4481" x2="4481" y1="-726" y2="-775"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4481" x2="4481" y1="-791" y2="-846"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4569" x2="4569" y1="-772" y2="-776"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4569" x2="4572" y1="-772" y2="-770"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4569" x2="4565" y1="-772" y2="-770"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4569" x2="4569" y1="-761" y2="-764"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4569" x2="4572" y1="-761" y2="-759"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4569" x2="4565" y1="-761" y2="-759"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4579" x2="4582" y1="-769" y2="-767"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4579" x2="4579" y1="-763" y2="-769"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4582" x2="4579" y1="-764" y2="-763"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4592" x2="4592" y1="-680" y2="-665"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4592" x2="4592" y1="-695" y2="-705"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4585" x2="4585" y1="-817" y2="-827"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4615" x2="4569" y1="-704" y2="-704"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4569" x2="4551" y1="-761" y2="-761"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4615" x2="4552" y1="-817" y2="-817"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4615" x2="4615" y1="-704" y2="-765"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4615" x2="4615" y1="-775" y2="-817"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4569" x2="4569" y1="-704" y2="-719"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4569" x2="4569" y1="-719" y2="-753"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4012" x2="4012" y1="-256" y2="-252"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4012" x2="4009" y1="-256" y2="-258"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4012" x2="4015" y1="-256" y2="-258"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4012" x2="4012" y1="-267" y2="-263"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4012" x2="4009" y1="-267" y2="-269"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4012" x2="4015" y1="-267" y2="-269"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4002" x2="3999" y1="-259" y2="-261"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4002" x2="4002" y1="-265" y2="-259"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3999" x2="4002" y1="-263" y2="-265"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3989" x2="3989" y1="-348" y2="-363"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3989" x2="3989" y1="-333" y2="-323"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3995" x2="3995" y1="-211" y2="-201"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3966" x2="4012" y1="-323" y2="-323"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4012" x2="4030" y1="-267" y2="-267"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4030" x2="4029" y1="-267" y2="-229"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3966" x2="4029" y1="-211" y2="-211"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4029" x2="4029" y1="-229" y2="-211"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3966" x2="3966" y1="-323" y2="-261"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3966" x2="3966" y1="-253" y2="-211"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4012" x2="4012" y1="-323" y2="-276"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4369" x2="4364" y1="-290" y2="-299"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4374" x2="4369" y1="-299" y2="-290"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4374" x2="4364" y1="-299" y2="-299"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4369" x2="4374" y1="-275" y2="-266"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4364" x2="4369" y1="-266" y2="-275"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4364" x2="4374" y1="-266" y2="-266"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4369" x2="4369" y1="-347" y2="-362"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4361" x2="4367" y1="-240" y2="-243"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4374" x2="4367" y1="-240" y2="-243"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4367" x2="4367" y1="-243" y2="-251"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4362" x2="4368" y1="-209" y2="-213"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4375" x2="4368" y1="-209" y2="-213"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4368" x2="4368" y1="-213" y2="-220"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3759" x2="3759" y1="-614" y2="-629"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3759" x2="3759" y1="-599" y2="-583"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3759" x2="3752" y1="-599" y2="-612"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3763" x2="3755" y1="-614" y2="-614"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3759" x2="3759" y1="-565" y2="-551"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3528" x2="3528" y1="-614" y2="-629"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3528" x2="3528" y1="-599" y2="-585"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3528" x2="3521" y1="-599" y2="-612"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3532" x2="3525" y1="-614" y2="-614"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3528" x2="3528" y1="-565" y2="-551"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3587" x2="3594" y1="-450" y2="-453"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3600" x2="3594" y1="-450" y2="-453"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3594" x2="3594" y1="-453" y2="-461"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3588" x2="3595" y1="-419" y2="-423"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3601" x2="3595" y1="-419" y2="-423"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3595" x2="3595" y1="-423" y2="-430"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3595" x2="3595" y1="-512" y2="-494"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3595" x2="3595" y1="-479" y2="-467"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3495" x2="3495" y1="-462" y2="-477"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3493" x2="3498" y1="-410" y2="-410"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3493" x2="3498" y1="-398" y2="-398"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3595" x2="3587" y1="-479" y2="-492"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3598" x2="3591" y1="-494" y2="-494"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3463" x2="3463" y1="-512" y2="-499"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3463" x2="3463" y1="-469" y2="-459"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3463" x2="3463" y1="-499" y2="-475"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3528" x2="3528" y1="-536" y2="-225"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3759" x2="3759" y1="-536" y2="-225"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4369" x2="4369" y1="-332" y2="-257"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4361" x2="4361" y1="-790" y2="-775"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4236" x2="4236" y1="-792" y2="-777"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3495" x2="3495" y1="-447" y2="-417"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4211" x2="4236" y1="-810" y2="-810"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4211" x2="4211" y1="-795" y2="-810"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4362" x2="4387" y1="-810" y2="-810"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4387" x2="4387" y1="-810" y2="-800"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4337" x2="4362" y1="-810" y2="-810"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4337" x2="4337" y1="-795" y2="-810"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4233" x2="4240" y1="-682" y2="-682"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4232" x2="4240" y1="-697" y2="-697"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4235" x2="4229" y1="-690" y2="-688"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4244" x2="4238" y1="-693" y2="-691"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4225" x2="4225" y1="-687" y2="-695"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4225" x2="4215" y1="-691" y2="-691"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4358" x2="4365" y1="-681" y2="-681"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4358" x2="4365" y1="-696" y2="-696"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4360" x2="4354" y1="-688" y2="-686"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4369" x2="4363" y1="-691" y2="-690"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4350" x2="4350" y1="-685" y2="-693"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4350" x2="4340" y1="-689" y2="-689"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4478" x2="4485" y1="-681" y2="-681"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4478" x2="4485" y1="-696" y2="-696"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4480" x2="4474" y1="-688" y2="-686"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4489" x2="4483" y1="-691" y2="-689"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4470" x2="4470" y1="-685" y2="-693"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4470" x2="4460" y1="-689" y2="-689"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4588" x2="4595" y1="-680" y2="-680"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4588" x2="4595" y1="-695" y2="-695"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4590" x2="4584" y1="-687" y2="-685"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4600" x2="4594" y1="-690" y2="-688"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4581" x2="4581" y1="-684" y2="-692"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4581" x2="4571" y1="-688" y2="-688"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4126" x2="4134" y1="-333" y2="-333"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4126" x2="4134" y1="-348" y2="-348"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4128" x2="4122" y1="-340" y2="-339"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4138" x2="4132" y1="-343" y2="-342"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4119" x2="4119" y1="-338" y2="-345"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4119" x2="4109" y1="-341" y2="-341"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4239" x2="4247" y1="-333" y2="-333"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4239" x2="4247" y1="-348" y2="-348"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4241" x2="4235" y1="-340" y2="-339"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4251" x2="4245" y1="-343" y2="-342"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4232" x2="4232" y1="-338" y2="-345"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4232" x2="4222" y1="-341" y2="-341"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4365" x2="4372" y1="-332" y2="-332"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4365" x2="4372" y1="-347" y2="-347"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4367" x2="4361" y1="-340" y2="-338"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4376" x2="4370" y1="-343" y2="-341"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4357" x2="4357" y1="-337" y2="-344"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4357" x2="4347" y1="-341" y2="-341"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3896" x2="3904" y1="-377" y2="-377"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3896" x2="3903" y1="-392" y2="-392"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3898" x2="3892" y1="-385" y2="-383"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3908" x2="3902" y1="-388" y2="-386"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3889" x2="3889" y1="-382" y2="-390"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3889" x2="3879" y1="-386" y2="-386"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3985" x2="3993" y1="-333" y2="-333"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3985" x2="3993" y1="-348" y2="-348"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3987" x2="3981" y1="-340" y2="-339"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3997" x2="3991" y1="-343" y2="-342"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3978" x2="3978" y1="-338" y2="-345"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3978" x2="3968" y1="-341" y2="-341"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3463" x2="3595" y1="-512" y2="-512"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3495" x2="3562" y1="-477" y2="-477"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3562" x2="3562" y1="-462" y2="-477"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3562" x2="3562" y1="-447" y2="-437"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3623" x2="3623" y1="-630" y2="-664"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3598" x2="3649" y1="-664" y2="-664"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3575" x2="3575" y1="-789" y2="-792"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3575" x2="3578" y1="-789" y2="-787"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3575" x2="3572" y1="-789" y2="-787"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3575" x2="3575" y1="-777" y2="-781"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3575" x2="3578" y1="-777" y2="-775"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3575" x2="3572" y1="-777" y2="-775"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3585" x2="3588" y1="-786" y2="-784"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3585" x2="3585" y1="-779" y2="-786"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3588" x2="3585" y1="-781" y2="-779"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3598" x2="3598" y1="-679" y2="-664"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3591" x2="3591" y1="-833" y2="-843"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3621" x2="3575" y1="-721" y2="-721"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3575" x2="3557" y1="-777" y2="-777"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3621" x2="3558" y1="-833" y2="-833"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3621" x2="3621" y1="-721" y2="-771"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3621" x2="3621" y1="-785" y2="-833"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3575" x2="3575" y1="-721" y2="-759"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3575" x2="3575" y1="-755" y2="-770"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3598" x2="3598" y1="-709" y2="-721"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3649" x2="3649" y1="-679" y2="-664"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3649" x2="3649" y1="-713" y2="-723"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4416" x2="4423" y1="-636" y2="-636"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4416" x2="4423" y1="-651" y2="-651"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4418" x2="4412" y1="-643" y2="-641"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4428" x2="4422" y1="-646" y2="-644"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4409" x2="4409" y1="-640" y2="-648"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4409" x2="4399" y1="-644" y2="-644"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3900" x2="3900" y1="-614" y2="-629"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3900" x2="3900" y1="-599" y2="-584"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3900" x2="3892" y1="-599" y2="-612"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3903" x2="3896" y1="-614" y2="-614"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3900" x2="3900" y1="-565" y2="-558"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3955" x2="3955" y1="-809" y2="-860"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3955" x2="3948" y1="-841" y2="-850"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3962" x2="3955" y1="-850" y2="-841"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3962" x2="3948" y1="-850" y2="-850"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3955" x2="3962" y1="-826" y2="-817"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3948" x2="3955" y1="-817" y2="-826"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3948" x2="3962" y1="-817" y2="-817"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4481" x2="4461" y1="-767" y2="-767"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4461" x2="4461" y1="-767" y2="-758"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4420" x2="4476" y1="-603" y2="-603"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4476" x2="4476" y1="-603" y2="-592"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4419" x2="4438" y1="-426" y2="-426"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4438" x2="4466" y1="-426" y2="-426"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4466" x2="4466" y1="-426" y2="-441"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3900" x2="3919" y1="-429" y2="-429"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3919" x2="3947" y1="-429" y2="-429"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3947" x2="3947" y1="-429" y2="-445"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="3965" x2="4035" y1="-544" y2="-544"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="4539" x2="4609" y1="-530" y2="-530"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4415" x2="4422" y1="-395" y2="-395"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4417" x2="4411" y1="-387" y2="-386"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4427" x2="4421" y1="-390" y2="-389"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4408" x2="4408" y1="-385" y2="-392"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4408" x2="4398" y1="-388" y2="-388"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4416" x2="4423" y1="-378" y2="-378"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4419" x2="4419" y1="-369" y2="-362"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4419" x2="4419" y1="-402" y2="-395"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4551" x2="4551" y1="-761" y2="-782"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4552" x2="4552" y1="-799" y2="-817"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3874" x2="3874" y1="-1088" y2="-1105"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3874" x2="3874" y1="-992" y2="-1059"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3794" x2="3794" y1="-1088" y2="-1103"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3794" x2="3794" y1="-1045" y2="-1016"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3802" x2="3795" y1="-978" y2="-974"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3788" x2="3795" y1="-978" y2="-974"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3795" x2="3795" y1="-974" y2="-966"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3802" x2="3795" y1="-1007" y2="-1003"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3788" x2="3795" y1="-1007" y2="-1003"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3795" x2="3795" y1="-1003" y2="-995"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3947" x2="3955" y1="-460" y2="-447"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3944" x2="3951" y1="-445" y2="-445"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="3947" x2="3947" y1="-460" y2="-470"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(238,238,0)" stroke-width="1" x1="3528" x2="3528" y1="-206" y2="-186"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(238,238,0)" stroke-width="1" x1="3759" x2="3759" y1="-206" y2="-187"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3759" x2="3721" y1="-344" y2="-344"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3699" x2="3680" y1="-344" y2="-344"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3853" x2="3853" y1="-885" y2="-878"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3851" x2="3851" y1="-884" y2="-879"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3849" x2="3849" y1="-883" y2="-879"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4018" x2="4011" y1="-1025" y2="-1025"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4017" x2="4012" y1="-1027" y2="-1027"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4016" x2="4012" y1="-1029" y2="-1029"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4001" x2="4001" y1="-885" y2="-878"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3999" x2="3999" y1="-884" y2="-879"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3997" x2="3997" y1="-883" y2="-879"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3653" x2="3646" y1="-723" y2="-723"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3652" x2="3647" y1="-725" y2="-725"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3651" x2="3647" y1="-727" y2="-727"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3558" x2="3558" y1="-777" y2="-833"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3595" x2="3588" y1="-843" y2="-843"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3594" x2="3589" y1="-845" y2="-845"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3593" x2="3589" y1="-847" y2="-847"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3528" x2="3521" y1="-536" y2="-549"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3532" x2="3525" y1="-551" y2="-551"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3759" x2="3752" y1="-536" y2="-549"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3763" x2="3756" y1="-551" y2="-551"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3562" x2="3555" y1="-447" y2="-460"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3565" x2="3558" y1="-462" y2="-462"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3495" x2="3488" y1="-447" y2="-460"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3498" x2="3491" y1="-462" y2="-462"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3558" x2="3565" y1="-437" y2="-437"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3559" x2="3564" y1="-435" y2="-435"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3560" x2="3563" y1="-434" y2="-434"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3460" x2="3465" y1="-457" y2="-457"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3461" x2="3464" y1="-456" y2="-456"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3459" x2="3466" y1="-459" y2="-459"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="3951" x2="3944" y1="-470" y2="-470"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="3950" x2="3945" y1="-472" y2="-472"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="3949" x2="3945" y1="-474" y2="-474"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="3879" x2="3879" y1="-390" y2="-383"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="3877" x2="3877" y1="-389" y2="-384"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="3875" x2="3875" y1="-388" y2="-384"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="3968" x2="3968" y1="-344" y2="-337"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="3966" x2="3966" y1="-343" y2="-338"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="3964" x2="3964" y1="-342" y2="-338"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="4109" x2="4109" y1="-344" y2="-337"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="4107" x2="4107" y1="-343" y2="-338"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="4105" x2="4105" y1="-342" y2="-338"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="4222" x2="4222" y1="-344" y2="-337"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="4220" x2="4220" y1="-343" y2="-338"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="4218" x2="4218" y1="-342" y2="-338"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="4347" x2="4347" y1="-344" y2="-337"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="4345" x2="4345" y1="-343" y2="-338"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="4343" x2="4343" y1="-342" y2="-338"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="3991" x2="3998" y1="-201" y2="-201"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="3992" x2="3997" y1="-199" y2="-199"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="3993" x2="3997" y1="-197" y2="-197"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4466" x2="4474" y1="-456" y2="-443"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4463" x2="4470" y1="-441" y2="-441"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(218,165,32)" stroke-width="1" x1="4466" x2="4466" y1="-456" y2="-466"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="4470" x2="4463" y1="-466" y2="-466"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="4469" x2="4464" y1="-468" y2="-468"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="4468" x2="4464" y1="-470" y2="-470"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="4398" x2="4398" y1="-391" y2="-384"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="4396" x2="4396" y1="-390" y2="-385"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(221,191,27)" stroke-width="1" x1="4394" x2="4394" y1="-389" y2="-385"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4475" x2="4483" y1="-577" y2="-590"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4472" x2="4479" y1="-592" y2="-592"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4475" x2="4475" y1="-577" y2="-567"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4479" x2="4472" y1="-567" y2="-567"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4478" x2="4473" y1="-565" y2="-565"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4477" x2="4473" y1="-563" y2="-563"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4399" x2="4399" y1="-640" y2="-647"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4397" x2="4397" y1="-641" y2="-646"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4395" x2="4395" y1="-642" y2="-646"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4215" x2="4215" y1="-688" y2="-695"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4213" x2="4213" y1="-689" y2="-694"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4211" x2="4211" y1="-690" y2="-694"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4340" x2="4340" y1="-685" y2="-692"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4338" x2="4338" y1="-686" y2="-691"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4336" x2="4336" y1="-687" y2="-691"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4460" x2="4460" y1="-685" y2="-692"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4458" x2="4458" y1="-686" y2="-691"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4456" x2="4456" y1="-687" y2="-691"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4571" x2="4571" y1="-684" y2="-691"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4569" x2="4569" y1="-685" y2="-690"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4567" x2="4567" y1="-686" y2="-690"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4588" x2="4581" y1="-828" y2="-828"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4587" x2="4582" y1="-830" y2="-830"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4586" x2="4582" y1="-832" y2="-832"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4211" x2="4219" y1="-780" y2="-793"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4208" x2="4215" y1="-795" y2="-795"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4211" x2="4211" y1="-780" y2="-770"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4215" x2="4208" y1="-770" y2="-770"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4214" x2="4209" y1="-768" y2="-768"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4213" x2="4209" y1="-766" y2="-766"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4335" x2="4343" y1="-780" y2="-793"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4332" x2="4339" y1="-795" y2="-795"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4335" x2="4335" y1="-780" y2="-770"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4339" x2="4332" y1="-770" y2="-770"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4338" x2="4333" y1="-768" y2="-768"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4337" x2="4333" y1="-766" y2="-766"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4459" x2="4467" y1="-743" y2="-756"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4456" x2="4463" y1="-758" y2="-758"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4459" x2="4459" y1="-743" y2="-733"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4463" x2="4456" y1="-733" y2="-733"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4462" x2="4457" y1="-731" y2="-731"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4461" x2="4457" y1="-729" y2="-729"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4481" x2="4474" y1="-774" y2="-787"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4478" x2="4485" y1="-791" y2="-791"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4261" x2="4261" y1="-793" y2="-783"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4258" x2="4263" y1="-781" y2="-781"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4259" x2="4262" y1="-780" y2="-780"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4257" x2="4264" y1="-783" y2="-783"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4387" x2="4387" y1="-794" y2="-784"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4384" x2="4389" y1="-782" y2="-782"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4385" x2="4388" y1="-781" y2="-781"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4383" x2="4390" y1="-784" y2="-784"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3249" y="-1178"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3249" y="-1178"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3200" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3200" y="-1195"/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YM_XHKDZ"/>
</svg>