<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-90" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3118 -1265 2117 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="5" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="26" y2="26"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="2" y2="2"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="20" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="15" y2="18"/>
    <ellipse cx="19" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="14" cy="16" fillStyle="0" rx="9" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape192">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="26" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="5,19 17,9 5,0 5,19 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="38,1 26,10 38,19 38,1 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape10_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape10_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="33" x2="33" y1="33" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="49" x2="33" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="49" x2="33" y1="24" y2="33"/>
   </symbol>
   <symbol id="transformer2:shape38_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,57 6,57 6,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="32" y1="51" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
   </symbol>
   <symbol id="transformer2:shape38_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="30,87 26,78 36,78 30,87 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
   </symbol>
   <symbol id="voltageTransformer:shape54">
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="32" y2="23"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_39242f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3925460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3925e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3926af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3927d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_39289c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3929560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3929f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_392b990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_392b990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_392d120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_392d120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_392ef90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_392ef90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_392ffa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_39311e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3931d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3932c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_39335e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3934ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3935940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3936200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_39369c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3937aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3938420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3938f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_39398d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_393adc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_393b8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_393c930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_393d580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_394b890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_394c360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_393fdc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_39413a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1211" width="2127" x="3113" y="-1270"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e60370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4058.000000 790.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e61590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4047.000000 775.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e62440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4072.000000 760.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e62ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4050.000000 588.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e631a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4039.000000 573.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebf250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4064.000000 558.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebf670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4430.000000 977.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebf930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4419.000000 962.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebfb70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4444.000000 947.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebff90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4717.000000 970.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec0250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4706.000000 955.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec0490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4731.000000 940.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec08b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4827.000000 806.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec0b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4816.000000 791.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec0db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4841.000000 776.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec11d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4816.000000 601.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec1490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4805.000000 586.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec16d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4830.000000 571.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec1af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3749.000000 120.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec1db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3738.000000 105.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec1ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3763.000000 90.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec2410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3957.000000 116.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec26d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3946.000000 101.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec2910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3971.000000 86.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec2d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4193.000000 124.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec2ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4182.000000 109.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec3230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4207.000000 94.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec3650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4401.000000 117.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec3910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4390.000000 102.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec3b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4415.000000 87.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec3f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4630.000000 116.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec4230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4619.000000 101.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec4470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4644.000000 86.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec4890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4820.000000 112.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec4b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4809.000000 97.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec4d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4834.000000 82.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec51b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5013.000000 110.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec5470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5002.000000 95.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec56b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5027.000000 80.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec5d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4086.000000 657.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec6870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4086.000000 672.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec7a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4888.000000 665.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec7ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4888.000000 680.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec9930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3769.000000 930.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eca530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3760.000000 898.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eca7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3769.000000 945.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eca9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3769.000000 916.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e648d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5047.000000 573.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e64d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5038.000000 541.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e64fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5047.000000 588.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e651e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5047.000000 559.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 37.000000 -35.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e659b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3492.000000 582.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e65c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3484.000000 553.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e65e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3492.000000 597.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e66040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3485.000000 538.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e66280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3485.000000 521.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e664c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3492.000000 568.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-54690">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4754.931385 -752.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9660" ObjectName="SW-LF_RX.LF_RX_301BK"/>
     <cge:Meas_Ref ObjectId="54690"/>
    <cge:TPSR_Ref TObjectID="9660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54589">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4754.931385 -568.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9637" ObjectName="SW-LF_RX.LF_RX_401BK"/>
     <cge:Meas_Ref ObjectId="54589"/>
    <cge:TPSR_Ref TObjectID="9637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54703">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3966.738982 -752.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9666" ObjectName="SW-LF_RX.LF_RX_302BK"/>
     <cge:Meas_Ref ObjectId="54703"/>
    <cge:TPSR_Ref TObjectID="9666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54697">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3965.738982 -568.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9662" ObjectName="SW-LF_RX.LF_RX_402BK"/>
     <cge:Meas_Ref ObjectId="54697"/>
    <cge:TPSR_Ref TObjectID="9662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80186">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4319.040767 -938.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17547" ObjectName="SW-LF_RX.LF_RX_353BK"/>
     <cge:Meas_Ref ObjectId="80186"/>
    <cge:TPSR_Ref TObjectID="17547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54772">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5080.272727 -353.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9672" ObjectName="SW-LF_RX.LF_RX_457BK"/>
     <cge:Meas_Ref ObjectId="54772"/>
    <cge:TPSR_Ref TObjectID="9672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54611">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3807.330233 -356.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9642" ObjectName="SW-LF_RX.LF_RX_456BK"/>
     <cge:Meas_Ref ObjectId="54611"/>
    <cge:TPSR_Ref TObjectID="9642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54676">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4872.666667 -355.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9657" ObjectName="SW-LF_RX.LF_RX_451BK"/>
     <cge:Meas_Ref ObjectId="54676"/>
    <cge:TPSR_Ref TObjectID="9657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54624">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4024.317829 -355.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9645" ObjectName="SW-LF_RX.LF_RX_455BK"/>
     <cge:Meas_Ref ObjectId="54624"/>
    <cge:TPSR_Ref TObjectID="9645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54663">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4671.333333 -350.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9654" ObjectName="SW-LF_RX.LF_RX_452BK"/>
     <cge:Meas_Ref ObjectId="54663"/>
    <cge:TPSR_Ref TObjectID="9654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54637">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4236.330233 -358.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9648" ObjectName="SW-LF_RX.LF_RX_454BK"/>
     <cge:Meas_Ref ObjectId="54637"/>
    <cge:TPSR_Ref TObjectID="9648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54650">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4474.000000 -356.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9651" ObjectName="SW-LF_RX.LF_RX_453BK"/>
     <cge:Meas_Ref ObjectId="54650"/>
    <cge:TPSR_Ref TObjectID="9651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54709">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4288.334884 -514.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9670" ObjectName="SW-LF_RX.LF_RX_412BK"/>
     <cge:Meas_Ref ObjectId="54709"/>
    <cge:TPSR_Ref TObjectID="9670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-117642">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.950000 -0.000000 0.000000 -1.000000 4666.667237 -940.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22001" ObjectName="SW-LF_RX.LF_RX_351BK"/>
     <cge:Meas_Ref ObjectId="117642"/>
    <cge:TPSR_Ref TObjectID="22001"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2ea3c00">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4358.000000 -1197.000000)" xlink:href="#voltageTransformer:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ea4810">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4746.000000 -1200.000000)" xlink:href="#voltageTransformer:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_GY" endPointId="0" endStationName="LF_RX" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_GuoRen" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4328,-1239 4328,-1157 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18072" ObjectName="AC-35kV.LN_GuoRen"/>
    <cge:TPSR_Ref TObjectID="18072_SS-90"/></metadata>
   <polyline fill="none" opacity="0" points="4328,-1239 4328,-1157 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_QF" endPointId="0" endStationName="LF_RX" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_RenXing" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4675,-1241 4675,-1159 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18073" ObjectName="AC-35kV.LN_RenXing"/>
    <cge:TPSR_Ref TObjectID="18073_SS-90"/></metadata>
   <polyline fill="none" opacity="0" points="4675,-1241 4675,-1159 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-LF_RX.LF_RX_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="13763"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3938.000000 -626.000000)" xlink:href="#transformer2:shape10_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3938.000000 -626.000000)" xlink:href="#transformer2:shape10_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="9678" ObjectName="TF-LF_RX.LF_RX_2T"/>
    <cge:TPSR_Ref TObjectID="9678"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_RX.LF_RX_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="13759"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4726.000000 -626.000000)" xlink:href="#transformer2:shape10_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4726.000000 -626.000000)" xlink:href="#transformer2:shape10_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="9677" ObjectName="TF-LF_RX.LF_RX_1T"/>
    <cge:TPSR_Ref TObjectID="9677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -630.000000)" xlink:href="#transformer2:shape38_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -630.000000)" xlink:href="#transformer2:shape38_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3607.000000 -217.000000)" xlink:href="#transformer2:shape38_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3607.000000 -217.000000)" xlink:href="#transformer2:shape38_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2f527c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4045.233888 -967.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eb6e40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4091.233888 -965.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f60e30">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4040.233888 -1083.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f957f0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3714.000000 -568.485271)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f96200">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3751.485271 -679.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f4e610">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4296.040767 -1100.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ebb1d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5049.000000 -193.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f6e110">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3775.330233 -204.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f5f960">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4841.666667 -203.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e95790">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3992.317829 -203.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e97160">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4637.333333 -198.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e91be0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4204.330233 -205.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f2a190">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4443.000000 -204.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fb78e0">
    <use class="BV-35KV" transform="matrix(0.950000 -0.000000 0.000000 -1.000000 4643.817237 -1102.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f05ab0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3840.000000 -802.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2edc650">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3732.000000 -595.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e7c070">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4598.485271 -683.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e7d5e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4579.000000 -600.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e7ec20">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4561.000000 -570.485271)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f0ac10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4696.000000 -1074.000000)" xlink:href="#lightningRod:shape192"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37c0aa0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3840.000000 -735.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37e4040">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3633.000000 -415.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e50150">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3633.000000 -336.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3236.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-80997" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3254.000000 -909.000000) translate(0,21)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80997" ObjectName="LF_RX:LF_RX_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-80996" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3254.000000 -947.000000) translate(0,21)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80996" ObjectName="LF_RX:LF_RX_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-226248" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3255.000000 -1030.000000) translate(0,21)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226248" ObjectName="LF_RX:LF_RX_sumP2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-226248" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3255.000000 -988.000000) translate(0,21)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226248" ObjectName="LF_RX:LF_RX_sumP2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-117552" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4163.000000 -673.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117552" ObjectName="LF_RX:LF_RX_2T_Tap"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-117553" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4162.000000 -653.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117553" ObjectName="LF_RX:LF_RX_2T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-117536" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4977.000000 -681.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117536" ObjectName="LF_RX:LF_RX_1T_Tap"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-117537" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4952.000000 -661.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117537" ObjectName="LF_RX:LF_RX_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-117522" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3835.000000 -931.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117522" ObjectName="LF_RX:LF_RX_301BK_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-117521" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3835.000000 -949.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117521" ObjectName="LF_RX:LF_RX_301BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-117523" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3835.000000 -913.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117523" ObjectName="LF_RX:LF_RX_301BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-117524" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3835.000000 -897.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117524" ObjectName="LF_RX:LF_RX_301BK_Uab"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-117558" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4487.000000 -974.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117558" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17547"/>
     <cge:Term_Ref ObjectID="13679"/>
    <cge:TPSR_Ref TObjectID="17547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-117559" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4487.000000 -974.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17547"/>
     <cge:Term_Ref ObjectID="13679"/>
    <cge:TPSR_Ref TObjectID="17547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-117555" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4487.000000 -974.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17547"/>
     <cge:Term_Ref ObjectID="13679"/>
    <cge:TPSR_Ref TObjectID="17547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-117570" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3810.000000 -121.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117570" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9642"/>
     <cge:Term_Ref ObjectID="13717"/>
    <cge:TPSR_Ref TObjectID="9642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-117571" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3810.000000 -121.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117571" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9642"/>
     <cge:Term_Ref ObjectID="13717"/>
    <cge:TPSR_Ref TObjectID="9642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-117567" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3810.000000 -121.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117567" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9642"/>
     <cge:Term_Ref ObjectID="13717"/>
    <cge:TPSR_Ref TObjectID="9642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-117576" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -119.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117576" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9645"/>
     <cge:Term_Ref ObjectID="13723"/>
    <cge:TPSR_Ref TObjectID="9645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-117577" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -119.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117577" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9645"/>
     <cge:Term_Ref ObjectID="13723"/>
    <cge:TPSR_Ref TObjectID="9645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-117573" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -119.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117573" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9645"/>
     <cge:Term_Ref ObjectID="13723"/>
    <cge:TPSR_Ref TObjectID="9645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-117582" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4253.000000 -124.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117582" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9648"/>
     <cge:Term_Ref ObjectID="13735"/>
    <cge:TPSR_Ref TObjectID="9648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-117583" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4253.000000 -124.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117583" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9648"/>
     <cge:Term_Ref ObjectID="13735"/>
    <cge:TPSR_Ref TObjectID="9648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-117579" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4253.000000 -124.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117579" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9648"/>
     <cge:Term_Ref ObjectID="13735"/>
    <cge:TPSR_Ref TObjectID="9648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-117588" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4467.000000 -120.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117588" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9651"/>
     <cge:Term_Ref ObjectID="13729"/>
    <cge:TPSR_Ref TObjectID="9651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-117589" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4467.000000 -120.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117589" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9651"/>
     <cge:Term_Ref ObjectID="13729"/>
    <cge:TPSR_Ref TObjectID="9651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-117585" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4467.000000 -120.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117585" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9651"/>
     <cge:Term_Ref ObjectID="13729"/>
    <cge:TPSR_Ref TObjectID="9651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-117594" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4693.000000 -120.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117594" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9654"/>
     <cge:Term_Ref ObjectID="13747"/>
    <cge:TPSR_Ref TObjectID="9654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-117595" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4693.000000 -120.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117595" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9654"/>
     <cge:Term_Ref ObjectID="13747"/>
    <cge:TPSR_Ref TObjectID="9654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-117591" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4693.000000 -120.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117591" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9654"/>
     <cge:Term_Ref ObjectID="13747"/>
    <cge:TPSR_Ref TObjectID="9654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-117606" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5074.000000 -109.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117606" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9672"/>
     <cge:Term_Ref ObjectID="13753"/>
    <cge:TPSR_Ref TObjectID="9672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-117607" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5074.000000 -109.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117607" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9672"/>
     <cge:Term_Ref ObjectID="13753"/>
    <cge:TPSR_Ref TObjectID="9672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-117603" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5074.000000 -109.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117603" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9672"/>
     <cge:Term_Ref ObjectID="13753"/>
    <cge:TPSR_Ref TObjectID="9672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-117564" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4780.000000 -969.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117564" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22001"/>
     <cge:Term_Ref ObjectID="13691"/>
    <cge:TPSR_Ref TObjectID="22001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-117565" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4780.000000 -969.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117565" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22001"/>
     <cge:Term_Ref ObjectID="13691"/>
    <cge:TPSR_Ref TObjectID="22001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-117561" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4780.000000 -969.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117561" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22001"/>
     <cge:Term_Ref ObjectID="13691"/>
    <cge:TPSR_Ref TObjectID="22001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-117547" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4119.000000 -787.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117547" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9666"/>
     <cge:Term_Ref ObjectID="13709"/>
    <cge:TPSR_Ref TObjectID="9666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-117548" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4119.000000 -787.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117548" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9666"/>
     <cge:Term_Ref ObjectID="13709"/>
    <cge:TPSR_Ref TObjectID="9666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-117544" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4119.000000 -787.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117544" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9666"/>
     <cge:Term_Ref ObjectID="13709"/>
    <cge:TPSR_Ref TObjectID="9666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-117531" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4891.000000 -806.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117531" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9660"/>
     <cge:Term_Ref ObjectID="13701"/>
    <cge:TPSR_Ref TObjectID="9660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-117532" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4891.000000 -806.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117532" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9660"/>
     <cge:Term_Ref ObjectID="13701"/>
    <cge:TPSR_Ref TObjectID="9660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-117528" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4891.000000 -806.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117528" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9660"/>
     <cge:Term_Ref ObjectID="13701"/>
    <cge:TPSR_Ref TObjectID="9660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118272" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4881.000000 -603.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118272" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9637"/>
     <cge:Term_Ref ObjectID="13705"/>
    <cge:TPSR_Ref TObjectID="9637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118273" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4881.000000 -603.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118273" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9637"/>
     <cge:Term_Ref ObjectID="13705"/>
    <cge:TPSR_Ref TObjectID="9637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118269" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4881.000000 -603.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118269" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9637"/>
     <cge:Term_Ref ObjectID="13705"/>
    <cge:TPSR_Ref TObjectID="9637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-118284" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4112.000000 -590.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118284" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9662"/>
     <cge:Term_Ref ObjectID="13713"/>
    <cge:TPSR_Ref TObjectID="9662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-118285" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4112.000000 -590.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118285" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9662"/>
     <cge:Term_Ref ObjectID="13713"/>
    <cge:TPSR_Ref TObjectID="9662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-118281" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4112.000000 -590.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118281" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9662"/>
     <cge:Term_Ref ObjectID="13713"/>
    <cge:TPSR_Ref TObjectID="9662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-117600" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4882.000000 -115.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117600" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9657"/>
     <cge:Term_Ref ObjectID="13741"/>
    <cge:TPSR_Ref TObjectID="9657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-117601" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4882.000000 -115.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117601" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9657"/>
     <cge:Term_Ref ObjectID="13741"/>
    <cge:TPSR_Ref TObjectID="9657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-117597" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4882.000000 -115.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="117597" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9657"/>
     <cge:Term_Ref ObjectID="13741"/>
    <cge:TPSR_Ref TObjectID="9657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-118248" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5100.000000 -586.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118248" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17555"/>
     <cge:Term_Ref ObjectID="24035"/>
    <cge:TPSR_Ref TObjectID="17555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-118249" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5100.000000 -586.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118249" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17555"/>
     <cge:Term_Ref ObjectID="24035"/>
    <cge:TPSR_Ref TObjectID="17555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-118250" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5100.000000 -586.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118250" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17555"/>
     <cge:Term_Ref ObjectID="24035"/>
    <cge:TPSR_Ref TObjectID="17555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-118265" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5100.000000 -586.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118265" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17555"/>
     <cge:Term_Ref ObjectID="24035"/>
    <cge:TPSR_Ref TObjectID="17555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-118275" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 -629.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118275" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17556"/>
     <cge:Term_Ref ObjectID="24036"/>
    <cge:TPSR_Ref TObjectID="17556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-118276" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 -629.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118276" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17556"/>
     <cge:Term_Ref ObjectID="24036"/>
    <cge:TPSR_Ref TObjectID="17556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-118277" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 -629.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118277" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17556"/>
     <cge:Term_Ref ObjectID="24036"/>
    <cge:TPSR_Ref TObjectID="17556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-118278" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 -629.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118278" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17556"/>
     <cge:Term_Ref ObjectID="24036"/>
    <cge:TPSR_Ref TObjectID="17556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-118279" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 -629.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118279" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17556"/>
     <cge:Term_Ref ObjectID="24036"/>
    <cge:TPSR_Ref TObjectID="17556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-118280" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 -629.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118280" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17556"/>
     <cge:Term_Ref ObjectID="24036"/>
    <cge:TPSR_Ref TObjectID="17556"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3248" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3199" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="64" x="4004" y="-674"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="64" x="4004" y="-674"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="5043" y="-383"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="5043" y="-383"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="4891" y="-386"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="4891" y="-386"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="4688" y="-381"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="4688" y="-381"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="4492" y="-387"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="4492" y="-387"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="33" x="4253" y="-388"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="33" x="4253" y="-388"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="32" x="4042" y="-386"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="32" x="4042" y="-386"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="32" x="3825" y="-387"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="32" x="3825" y="-387"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="4284" y="-966"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="4284" y="-966"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4630" y="-970"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4630" y="-970"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="32" x="4307" y="-544"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="32" x="4307" y="-544"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="68" x="4800" y="-673"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="68" x="4800" y="-673"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="74" x="3179" y="-772"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="74" x="3179" y="-772"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3527" y="-1169"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3527" y="-1169"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3527" y="-1128"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3527" y="-1128"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="3671" y="-1154"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="3671" y="-1154"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/></g>
   <g href="35kV仁兴变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="64" x="4004" y="-674"/></g>
   <g href="35kV仁兴变10kV工投能源线457间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="5043" y="-383"/></g>
   <g href="35kV仁兴变10kV滇中引水专线451间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="4891" y="-386"/></g>
   <g href="35kV仁兴变10kV大箐线452间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="4688" y="-381"/></g>
   <g href="35kV仁兴变10kV彰保线453间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="4492" y="-387"/></g>
   <g href="35kV仁兴变10kV左所线454间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="33" x="4253" y="-388"/></g>
   <g href="35kV仁兴变10kV大平坝线455间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="32" x="4042" y="-386"/></g>
   <g href="35kV仁兴变10kV马鞍线456间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="32" x="3825" y="-387"/></g>
   <g href="35kV仁兴变35kV果仁线353间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="4284" y="-966"/></g>
   <g href="35kV仁兴变35kV仁兴线351间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4630" y="-970"/></g>
   <g href="35kV仁兴变10kV母联412间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="32" x="4307" y="-544"/></g>
   <g href="35kV仁兴变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="68" x="4800" y="-673"/></g>
   <g href="35kV仁兴变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="74" x="3179" y="-772"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3527" y="-1169"/></g>
   <g href="cx_配调_配网接线图35_禄丰.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3527" y="-1128"/></g>
   <g href="AVC仁兴站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="3671" y="-1154"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3671" y="-1153"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-LF_RX.LF_RX_35M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3807,-866 4974,-866 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17554" ObjectName="BS-LF_RX.LF_RX_35M"/>
    <cge:TPSR_Ref TObjectID="17554"/></metadata>
   <polyline fill="none" opacity="0" points="3807,-866 4974,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_RX.LF_RX_10IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4408,-498 5172,-498 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17555" ObjectName="BS-LF_RX.LF_RX_10IM"/>
    <cge:TPSR_Ref TObjectID="17555"/></metadata>
   <polyline fill="none" opacity="0" points="4408,-498 5172,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_RX.LF_RX_10IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4325,-498 3583,-498 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17556" ObjectName="BS-LF_RX.LF_RX_10IIM"/>
    <cge:TPSR_Ref TObjectID="17556"/></metadata>
   <polyline fill="none" opacity="0" points="4325,-498 3583,-498 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2f01410" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4011.233888 -990.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f36f10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4389.040767 -924.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fc2830" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4389.040767 -984.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fd81c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4389.040767 -1057.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f7aad0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5112.000000 -332.117647)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ecd200" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5112.000000 -182.117647)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2edff40" refnum="0">
    <use class="BV-0KV" transform="matrix(0.950000 -0.000000 0.000000 -1.000000 4733.167237 -986.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fb6a00" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -0.950000 -1.000000 -0.000000 4612.717237 -1132.450000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ea2f50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3982.000000 -984.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="17555" cx="4764" cy="-498" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17555" cx="5089" cy="-498" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17555" cx="4882" cy="-498" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17555" cx="4483" cy="-498" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17554" cx="4054" cy="-866" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17554" cx="4764" cy="-866" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17554" cx="4328" cy="-866" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17554" cx="4675" cy="-866" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17554" cx="3845" cy="-866" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17555" cx="4584" cy="-498" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17555" cx="4680" cy="-498" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17554" cx="3976" cy="-866" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17555" cx="4438" cy="-498" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17556" cx="3975" cy="-498" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17556" cx="4297" cy="-498" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17556" cx="3816" cy="-498" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17556" cx="4033" cy="-498" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17556" cx="4245" cy="-498" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17556" cx="3737" cy="-498" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17556" cx="3638" cy="-498" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fac6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fac6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fac6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fac6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fac6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fac6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fac6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fac6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fac6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dbb340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dbb340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dbb340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dbb340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dbb340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dbb340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dbb340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dbb340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dbb340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dbb340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dbb340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dbb340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dbb340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dbb340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dbb340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dbb340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dbb340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dbb340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2c77b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3283.000000 -1166.500000) translate(0,16)">仁兴变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f61a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3975.000000 -1107.000000) translate(0,15)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ff5540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4900.000000 -892.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2fe0d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4537.000000 -730.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2fe0d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4537.000000 -730.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f97190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3503.000000 -533.000000) translate(0,15)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2f97380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5105.000000 -486.000000) translate(0,18)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb7b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4061.233888 -907.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb7e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3963.233888 -972.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb8000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4777.931385 -780.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f97570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4770.931385 -835.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f976e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3989.738982 -780.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f97850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3982.738982 -835.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fdff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3984.738982 -597.000000) translate(0,12)">402</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fe0140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3982.738982 -542.000000) translate(0,12)">4021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fe02f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4772.931385 -597.000000) translate(0,12)">401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fe04a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4770.931385 -546.000000) translate(0,12)">4011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fe0650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3687.485271 -549.000000) translate(0,12)">4902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fe0800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4591.000000 -539.000000) translate(0,12)">4901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f3dce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4276.000000 -1263.000000) translate(0,15)">35kV果仁线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f3e7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4344.040767 -956.000000) translate(0,12)">35317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f3ea40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.040767 -906.000000) translate(0,12)">3531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f3ec20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4284.040767 -966.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f3ee00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.040767 -1031.000000) translate(0,12)">3536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f3efe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4344.040767 -1016.000000) translate(0,12)">35360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f3f3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4346.040767 -1054.000000) translate(0,12)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2f3f580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3269.000000 -239.000000) translate(0,17)">4861549</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2f3f580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3269.000000 -239.000000) translate(0,38)">15758580347</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f720d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5035.000000 -146.000000) translate(0,15)">工投能源线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f725c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5045.000000 -382.000000) translate(0,12)">457</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f727d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5039.000000 -466.000000) translate(0,12)">4571</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f729b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5042.000000 -319.000000) translate(0,12)">4572</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f73160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5129.000000 -396.000000) translate(0,12)">45717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f733d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5129.000000 -245.000000) translate(0,12)">45727</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f5cd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3790.000000 -146.000000) translate(0,15)">马鞍线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f5d510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3828.330233 -385.000000) translate(0,12)">456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f74ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4828.000000 -146.000000) translate(0,15)">滇中引水专线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f757a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4894.000000 -384.000000) translate(0,12)">451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f75ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4889.000000 -322.000000) translate(0,12)">4512</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f75d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4889.000000 -467.000000) translate(0,12)">4511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e967a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3996.000000 -146.000000) translate(0,15)">大平坝线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f198d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4045.984496 -384.000000) translate(0,12)">455</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f19c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4040.984496 -322.000000) translate(0,12)">4552</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f19e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4040.984496 -467.000000) translate(0,12)">4551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e98140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4649.000000 -146.000000) translate(0,15)">大箐线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e98cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -379.000000) translate(0,12)">452</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e92bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4219.000000 -146.000000) translate(0,15)">左所线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e93a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4256.663566 -386.000000) translate(0,12)">454</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f2b0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4452.000000 -146.000000) translate(0,15)">彰保线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f2bfa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4495.000000 -385.000000) translate(0,12)">453</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f2c300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4490.000000 -323.000000) translate(0,12)">4532</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f2c540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4490.000000 -468.000000) translate(0,12)">4531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed52f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4445.334884 -537.000000) translate(0,12)">4121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fcfaf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4310.334884 -541.000000) translate(0,12)">412</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd1780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3754.000000 -384.000000) translate(0,12)">400/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd1f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3975.000000 -384.000000) translate(0,12)">100/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd2300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4183.000000 -384.000000) translate(0,12)">150/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd2580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4421.000000 -384.000000) translate(0,12)">400/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd27c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4617.000000 -384.000000) translate(0,12)">300/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd2a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4815.000000 -384.000000) translate(0,12)">600/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd2c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4998.000000 -384.000000) translate(0,12)">300/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f04850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4388.000000 -1166.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f05720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3710.000000 -787.000000) translate(0,15)">35kV#1站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2fb8db0" transform="matrix(0.950000 -0.000000 -0.000000 1.000000 4628.900000 -1265.000000) translate(0,15)">35kV仁兴线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fb96c0" transform="matrix(0.950000 -0.000000 -0.000000 1.000000 4630.867237 -906.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fb9940" transform="matrix(0.950000 -0.000000 -0.000000 1.000000 4633.417237 -968.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fb9b80" transform="matrix(0.950000 -0.000000 -0.000000 1.000000 4632.467237 -1033.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fb9dc0" transform="matrix(0.950000 -0.000000 -0.000000 1.000000 4690.417237 -1018.000000) translate(0,12)">35160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2fba190" transform="matrix(0.950000 -0.000000 -0.000000 1.000000 4777.750000 -1169.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e36830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3609.000000 -672.000000) translate(0,15)">10kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e36830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3609.000000 -672.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e3b050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4007.000000 -673.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f23ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4801.000000 -672.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f24420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3181.000000 -769.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2ed7be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3538.000000 -1161.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2ed8de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3538.000000 -1122.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e7a490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4028.000000 -717.000000) translate(0,15)">SZ11-10000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e7bc10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4802.000000 -728.000000) translate(0,15)">SZ11-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0bb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3825.000000 -465.000000) translate(0,12)">4561</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0c040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3823.000000 -321.000000) translate(0,12)">4562</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0c280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4685.000000 -466.000000) translate(0,12)">4521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0c4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4687.000000 -315.000000) translate(0,12)">4522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4252.000000 -322.000000) translate(0,12)">4542</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0e6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4252.000000 -467.000000) translate(0,12)">4541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e5ce00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4553.000000 -1114.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2e5d2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3118.000000 -187.000000) translate(0,17)">禄丰巡维中心</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2e5d2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3118.000000 -187.000000) translate(0,38)">腰站变值班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2f9ee40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3269.000000 -176.500000) translate(0,17)">13508785653</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2e63c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3686.500000 -1141.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e17c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3577.000000 -193.000000) translate(0,15)">2号站用变</text>
  </g><g id="SolidLine_Layer">
   <g DF8003:Layer="PUBLIC" stroke-width="1"><metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-LF_RX.LD_RX_456">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3807.000000 -158.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18118" ObjectName="EC-LF_RX.LD_RX_456"/>
    <cge:TPSR_Ref TObjectID="18118"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_RX.LD_RX_455">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4024.000000 -159.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18117" ObjectName="EC-LF_RX.LD_RX_455"/>
    <cge:TPSR_Ref TObjectID="18117"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_RX.LD_RX_454">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4236.000000 -162.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18116" ObjectName="EC-LF_RX.LD_RX_454"/>
    <cge:TPSR_Ref TObjectID="18116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_RX.LD_RX_453">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4474.000000 -163.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18115" ObjectName="EC-LF_RX.LD_RX_453"/>
    <cge:TPSR_Ref TObjectID="18115"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_RX.LD_RX_452">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4671.000000 -154.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18114" ObjectName="EC-LF_RX.LD_RX_452"/>
    <cge:TPSR_Ref TObjectID="18114"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_RX.LD_RX_451">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4873.000000 -156.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18113" ObjectName="EC-LF_RX.LD_RX_451"/>
    <cge:TPSR_Ref TObjectID="18113"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_RX.LD_RX_457">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5080.000000 -155.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18119" ObjectName="EC-LF_RX.LD_RX_457"/>
    <cge:TPSR_Ref TObjectID="18119"/></metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-52539" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3481.000000 -1094.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9249" ObjectName="DYN-LF_RX"/>
     <cge:Meas_Ref ObjectId="52539"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-54705">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4045.233888 -877.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9668" ObjectName="SW-LF_RX.LF_RX_3901SW"/>
     <cge:Meas_Ref ObjectId="54705"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54706">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4011.233888 -940.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9669" ObjectName="SW-LF_RX.LF_RX_39017SW"/>
     <cge:Meas_Ref ObjectId="54706"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54591">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4575.000000 -509.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9639" ObjectName="SW-LF_RX.LF_RX_4901SW"/>
     <cge:Meas_Ref ObjectId="54591"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54607">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4754.931385 -805.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32012" ObjectName="SW-LF_RX.LF_RX_3011SW"/>
     <cge:Meas_Ref ObjectId="54607"/>
    <cge:TPSR_Ref TObjectID="32012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54590">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4754.931385 -516.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32010" ObjectName="SW-LF_RX.LF_RX_4011SW"/>
     <cge:Meas_Ref ObjectId="54590"/>
    <cge:TPSR_Ref TObjectID="32010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54704">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3966.738982 -805.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32033" ObjectName="SW-LF_RX.LF_RX_3021SW"/>
     <cge:Meas_Ref ObjectId="54704"/>
    <cge:TPSR_Ref TObjectID="32033"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54698">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3965.738982 -512.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32024" ObjectName="SW-LF_RX.LF_RX_4021SW"/>
     <cge:Meas_Ref ObjectId="54698"/>
    <cge:TPSR_Ref TObjectID="32024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54699">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3728.485271 -518.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9664" ObjectName="SW-LF_RX.LF_RX_4902SW"/>
     <cge:Meas_Ref ObjectId="54699"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80189">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4319.040767 -1000.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17551" ObjectName="SW-LF_RX.LF_RX_3536SW"/>
     <cge:Meas_Ref ObjectId="80189"/>
    <cge:TPSR_Ref TObjectID="17551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80187">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4319.040767 -874.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32006" ObjectName="SW-LF_RX.LF_RX_3531SW"/>
     <cge:Meas_Ref ObjectId="80187"/>
    <cge:TPSR_Ref TObjectID="32006"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80185">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4341.040767 -925.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32007" ObjectName="SW-LF_RX.LF_RX_35317SW"/>
     <cge:Meas_Ref ObjectId="80185"/>
    <cge:TPSR_Ref TObjectID="32007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-80190">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4341.040767 -985.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32008" ObjectName="SW-LF_RX.LF_RX_35360SW"/>
     <cge:Meas_Ref ObjectId="80190"/>
    <cge:TPSR_Ref TObjectID="32008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-117641">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4341.040767 -1058.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32009" ObjectName="SW-LF_RX.LF_RX_35367SW"/>
     <cge:Meas_Ref ObjectId="117641"/>
    <cge:TPSR_Ref TObjectID="32009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-117646">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5080.272727 -290.117647)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32029" ObjectName="SW-LF_RX.LF_RX_4572SW"/>
     <cge:Meas_Ref ObjectId="117646"/>
    <cge:TPSR_Ref TObjectID="32029"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54788">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 -366.117647)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32032" ObjectName="SW-LF_RX.LF_RX_45717SW"/>
     <cge:Meas_Ref ObjectId="54788"/>
    <cge:TPSR_Ref TObjectID="32032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54787">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 -214.117647)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32031" ObjectName="SW-LF_RX.LF_RX_45727SW"/>
     <cge:Meas_Ref ObjectId="54787"/>
    <cge:TPSR_Ref TObjectID="32031"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54774">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5080.272727 -433.117647)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32030" ObjectName="SW-LF_RX.LF_RX_4571SW"/>
     <cge:Meas_Ref ObjectId="54774"/>
    <cge:TPSR_Ref TObjectID="32030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54677">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4873.000000 -435.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32022" ObjectName="SW-LF_RX.LF_RX_4511SW"/>
     <cge:Meas_Ref ObjectId="54677"/>
    <cge:TPSR_Ref TObjectID="32022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54678">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4873.000000 -290.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32023" ObjectName="SW-LF_RX.LF_RX_4512SW"/>
     <cge:Meas_Ref ObjectId="54678"/>
    <cge:TPSR_Ref TObjectID="32023"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54625">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4024.984496 -435.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32015" ObjectName="SW-LF_RX.LF_RX_4551SW"/>
     <cge:Meas_Ref ObjectId="54625"/>
    <cge:TPSR_Ref TObjectID="32015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54626">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4024.984496 -290.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32016" ObjectName="SW-LF_RX.LF_RX_4552SW"/>
     <cge:Meas_Ref ObjectId="54626"/>
    <cge:TPSR_Ref TObjectID="32016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54651">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4474.000000 -436.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32018" ObjectName="SW-LF_RX.LF_RX_4531SW"/>
     <cge:Meas_Ref ObjectId="54651"/>
    <cge:TPSR_Ref TObjectID="32018"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54652">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4474.000000 -291.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32019" ObjectName="SW-LF_RX.LF_RX_4532SW"/>
     <cge:Meas_Ref ObjectId="54652"/>
    <cge:TPSR_Ref TObjectID="32019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54710">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4429.334884 -510.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32036" ObjectName="SW-LF_RX.LF_RX_4121SW"/>
     <cge:Meas_Ref ObjectId="54710"/>
    <cge:TPSR_Ref TObjectID="32036"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54664">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4671.000000 -436.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32020" ObjectName="SW-LF_RX.LF_RX_4521SW"/>
     <cge:Meas_Ref ObjectId="54664"/>
    <cge:TPSR_Ref TObjectID="32020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54665">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4671.000000 -285.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32021" ObjectName="SW-LF_RX.LF_RX_4522SW"/>
     <cge:Meas_Ref ObjectId="54665"/>
    <cge:TPSR_Ref TObjectID="32021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54638">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4236.000000 -437.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32017" ObjectName="SW-LF_RX.LF_RX_4541SW"/>
     <cge:Meas_Ref ObjectId="54638"/>
    <cge:TPSR_Ref TObjectID="32017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54639">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4236.000000 -292.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32028" ObjectName="SW-LF_RX.LF_RX_4542SW"/>
     <cge:Meas_Ref ObjectId="54639"/>
    <cge:TPSR_Ref TObjectID="32028"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54612">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3807.000000 -436.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32013" ObjectName="SW-LF_RX.LF_RX_4561SW"/>
     <cge:Meas_Ref ObjectId="54612"/>
    <cge:TPSR_Ref TObjectID="32013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54613">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3807.000000 -291.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32014" ObjectName="SW-LF_RX.LF_RX_4562SW"/>
     <cge:Meas_Ref ObjectId="54613"/>
    <cge:TPSR_Ref TObjectID="32014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-117643">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.950000 -0.000000 0.000000 -1.000000 4666.667237 -1002.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31999" ObjectName="SW-LF_RX.LF_RX_3516SW"/>
     <cge:Meas_Ref ObjectId="117643"/>
    <cge:TPSR_Ref TObjectID="31999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54692">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.950000 -0.000000 0.000000 -1.000000 4666.667237 -876.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32002" ObjectName="SW-LF_RX.LF_RX_3511SW"/>
     <cge:Meas_Ref ObjectId="54692"/>
    <cge:TPSR_Ref TObjectID="32002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-117644">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.950000 -0.000000 0.000000 -1.000000 4687.567237 -987.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32000" ObjectName="SW-LF_RX.LF_RX_35160SW"/>
     <cge:Meas_Ref ObjectId="117644"/>
    <cge:TPSR_Ref TObjectID="32000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3985.000000 -1013.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4362.000000 -1103.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4750.000000 -1106.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-117645">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4598.000000 -1086.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32001" ObjectName="SW-LF_RX.LF_RX_35167SW"/>
     <cge:Meas_Ref ObjectId="117645"/>
    <cge:TPSR_Ref TObjectID="32001"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2e77ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4054,-866 4054,-882 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17554@0" ObjectIDZND0="9668@0" Pin0InfoVect0LinkObjId="SW-54705_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f8c200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4054,-866 4054,-882 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f3ab50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4054,-918 4054,-933 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9668@1" ObjectIDZND0="g_2eb6e40@0" ObjectIDZND1="g_2f527c0@0" ObjectIDZND2="9669@x" Pin0InfoVect0LinkObjId="g_2eb6e40_0" Pin0InfoVect1LinkObjId="g_2f527c0_0" Pin0InfoVect2LinkObjId="SW-54706_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54705_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4054,-918 4054,-933 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eb8170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4054,-1003 4054,-1058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2f527c0@1" ObjectIDZND0="g_2f60e30@0" Pin0InfoVect0LinkObjId="g_2f60e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f527c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4054,-1003 4054,-1058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eb8360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4054,-960 4098,-960 4098,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9668@x" ObjectIDND1="9669@x" ObjectIDND2="g_2f527c0@0" ObjectIDZND0="g_2eb6e40@0" Pin0InfoVect0LinkObjId="g_2eb6e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-54705_0" Pin1InfoVect1LinkObjId="SW-54706_0" Pin1InfoVect2LinkObjId="g_2f527c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4054,-960 4098,-960 4098,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f45520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4054,-933 4054,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9668@x" ObjectIDND1="9669@x" ObjectIDZND0="g_2eb6e40@0" ObjectIDZND1="g_2f527c0@0" Pin0InfoVect0LinkObjId="g_2eb6e40_0" Pin0InfoVect1LinkObjId="g_2f527c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54705_0" Pin1InfoVect1LinkObjId="SW-54706_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4054,-933 4054,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f45710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4054,-960 4054,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9668@x" ObjectIDND1="9669@x" ObjectIDND2="g_2eb6e40@0" ObjectIDZND0="g_2f527c0@0" Pin0InfoVect0LinkObjId="g_2f527c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-54705_0" Pin1InfoVect1LinkObjId="SW-54706_0" Pin1InfoVect2LinkObjId="g_2eb6e40_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4054,-960 4054,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ff5160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4054,-933 4020,-933 4020,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2eb6e40@0" ObjectIDND1="g_2f527c0@0" ObjectIDND2="9668@x" ObjectIDZND0="9669@0" Pin0InfoVect0LinkObjId="SW-54706_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2eb6e40_0" Pin1InfoVect1LinkObjId="g_2f527c0_0" Pin1InfoVect2LinkObjId="SW-54705_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4054,-933 4020,-933 4020,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ff5350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4020,-981 4020,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9669@1" ObjectIDZND0="g_2f01410@0" Pin0InfoVect0LinkObjId="g_2f01410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54706_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4020,-981 4020,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f61860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4040,-1075 3990,-1075 3990,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2ea3c00_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4040,-1075 3990,-1075 3990,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fe0ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4584,-498 4584,-514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17555@0" ObjectIDZND0="9639@0" Pin0InfoVect0LinkObjId="SW-54591_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f841d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4584,-498 4584,-514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f47660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-866 4764,-846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17554@0" ObjectIDZND0="32012@1" Pin0InfoVect0LinkObjId="SW-54607_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f8c200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-866 4764,-846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f48a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-810 4764,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32012@0" ObjectIDZND0="9660@1" Pin0InfoVect0LinkObjId="SW-54690_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54607_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-810 4764,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f825d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-631 4764,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="9677@1" ObjectIDZND0="9637@1" Pin0InfoVect0LinkObjId="SW-54589_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f3a3d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-631 4764,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f83fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-576 4764,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9637@0" ObjectIDZND0="32010@1" Pin0InfoVect0LinkObjId="SW-54590_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54589_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-576 4764,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f841d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-521 4764,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32010@0" ObjectIDZND0="17555@0" Pin0InfoVect0LinkObjId="g_2f3e5b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-521 4764,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ebcce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-866 3976,-846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17554@0" ObjectIDZND0="32033@1" Pin0InfoVect0LinkObjId="SW-54704_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f8c200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-866 3976,-846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ebe590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-810 3976,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32033@0" ObjectIDZND0="9666@1" Pin0InfoVect0LinkObjId="SW-54703_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54704_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-810 3976,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f8e3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3975,-631 3975,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="9678@1" ObjectIDZND0="9662@1" Pin0InfoVect0LinkObjId="SW-54697_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3975,-631 3975,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f900e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3975,-576 3975,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9662@0" ObjectIDZND0="32024@1" Pin0InfoVect0LinkObjId="SW-54698_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54697_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3975,-576 3975,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f902d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3975,-517 3975,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32024@0" ObjectIDZND0="17556@0" Pin0InfoVect0LinkObjId="g_2f3e3c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54698_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3975,-517 3975,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f95600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-646 3737,-655 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2edc650@1" ObjectIDZND0="g_2f96200@0" Pin0InfoVect0LinkObjId="g_2f96200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2edc650_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-646 3737,-655 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f8c200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-879 4328,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32006@1" ObjectIDZND0="17554@0" Pin0InfoVect0LinkObjId="g_2f6b490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80187_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-879 4328,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f36ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-946 4328,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="17547@0" ObjectIDZND0="32006@x" ObjectIDZND1="32007@x" Pin0InfoVect0LinkObjId="SW-80187_0" Pin0InfoVect1LinkObjId="SW-80185_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80186_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-946 4328,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f36cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-930 4328,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="17547@x" ObjectIDND1="32007@x" ObjectIDZND0="32006@0" Pin0InfoVect0LinkObjId="SW-80187_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-80186_0" Pin1InfoVect1LinkObjId="SW-80185_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-930 4328,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fc23f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-930 4346,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="17547@x" ObjectIDND1="32006@x" ObjectIDZND0="32007@0" Pin0InfoVect0LinkObjId="SW-80185_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-80186_0" Pin1InfoVect1LinkObjId="SW-80187_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-930 4346,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fc2610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4382,-930 4393,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32007@1" ObjectIDZND0="g_2f36f10@0" Pin0InfoVect0LinkObjId="g_2f36f10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80185_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4382,-930 4393,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fd71b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-990 4346,-990 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="17551@x" ObjectIDND1="17547@x" ObjectIDZND0="32008@0" Pin0InfoVect0LinkObjId="SW-80190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-80189_0" Pin1InfoVect1LinkObjId="SW-80186_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-990 4346,-990 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fd73d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4382,-990 4393,-990 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32008@1" ObjectIDZND0="g_2fc2830@0" Pin0InfoVect0LinkObjId="g_2fc2830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80190_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4382,-990 4393,-990 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fd7d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-1005 4328,-990 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="17551@1" ObjectIDZND0="32008@x" ObjectIDZND1="17547@x" Pin0InfoVect0LinkObjId="SW-80190_0" Pin0InfoVect1LinkObjId="SW-80186_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-80189_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-1005 4328,-990 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fd7fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-990 4328,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="32008@x" ObjectIDND1="17551@x" ObjectIDZND0="17547@1" Pin0InfoVect0LinkObjId="SW-80186_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-80190_0" Pin1InfoVect1LinkObjId="SW-80189_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-990 4328,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f4d690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-1063 4346,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="17551@x" ObjectIDND1="0@x" ObjectIDND2="g_2f4e610@0" ObjectIDZND0="32009@0" Pin0InfoVect0LinkObjId="SW-117641_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-80189_0" Pin1InfoVect1LinkObjId="g_2ea3c00_0" Pin1InfoVect2LinkObjId="g_2f4e610_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-1063 4346,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f4d8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4382,-1063 4393,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32009@1" ObjectIDZND0="g_2fd81c0@0" Pin0InfoVect0LinkObjId="g_2fd81c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-117641_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4382,-1063 4393,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f4e1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-1063 4328,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="32009@x" ObjectIDND1="0@x" ObjectIDND2="g_2f4e610@0" ObjectIDZND0="17551@0" Pin0InfoVect0LinkObjId="SW-80189_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-117641_0" Pin1InfoVect1LinkObjId="g_2ea3c00_0" Pin1InfoVect2LinkObjId="g_2f4e610_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-1063 4328,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f4e3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4367,-1165 4367,-1153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_2ea3c00@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2ea3c00_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ea3c00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4367,-1165 4367,-1153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f4f200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4302,-1104 4302,-1090 4328,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2f4e610@0" ObjectIDZND0="0@x" ObjectIDZND1="32009@x" ObjectIDZND2="17551@x" Pin0InfoVect0LinkObjId="g_2ea3c00_0" Pin0InfoVect1LinkObjId="SW-117641_0" Pin0InfoVect2LinkObjId="SW-80189_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f4e610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4302,-1104 4302,-1090 4328,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f4fbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-1157 4328,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="18072@1" ObjectIDZND0="g_2f4e610@0" ObjectIDZND1="0@x" ObjectIDZND2="32009@x" Pin0InfoVect0LinkObjId="g_2f4e610_0" Pin0InfoVect1LinkObjId="g_2ea3c00_0" Pin0InfoVect2LinkObjId="SW-117641_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-1157 4328,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f4fdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4367,-1108 4367,-1080 4328,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="32009@x" ObjectIDZND1="17551@x" ObjectIDZND2="g_2f4e610@0" Pin0InfoVect0LinkObjId="SW-117641_0" Pin0InfoVect1LinkObjId="SW-80189_0" Pin0InfoVect2LinkObjId="g_2f4e610_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ea3c00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4367,-1108 4367,-1080 4328,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f3d8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-1063 4328,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="32009@x" ObjectIDND1="17551@x" ObjectIDZND0="0@x" ObjectIDZND1="g_2f4e610@0" ObjectIDZND2="18072@1" Pin0InfoVect0LinkObjId="g_2ea3c00_0" Pin0InfoVect1LinkObjId="g_2f4e610_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-117641_0" Pin1InfoVect1LinkObjId="SW-80189_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-1063 4328,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f3dac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-1080 4328,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="0@x" ObjectIDND1="32009@x" ObjectIDND2="17551@x" ObjectIDZND0="g_2f4e610@0" ObjectIDZND1="18072@1" Pin0InfoVect0LinkObjId="g_2f4e610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ea3c00_0" Pin1InfoVect1LinkObjId="SW-117641_0" Pin1InfoVect2LinkObjId="SW-80189_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-1080 4328,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f3e1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4297,-549 4297,-565 4438,-565 4438,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9670@1" ObjectIDZND0="32036@1" Pin0InfoVect0LinkObjId="SW-54710_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54709_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4297,-549 4297,-565 4438,-565 4438,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f3e3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4297,-522 4297,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="9670@0" ObjectIDZND0="17556@0" Pin0InfoVect0LinkObjId="g_2f902d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54709_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4297,-522 4297,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f3e5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4438,-515 4438,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32036@0" ObjectIDZND0="17555@0" Pin0InfoVect0LinkObjId="g_2f841d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4438,-515 4438,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ebafb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5089,-259 5056,-259 5056,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="18119@x" ObjectIDND1="32031@x" ObjectIDND2="32029@x" ObjectIDZND0="g_2ebb1d0@0" Pin0InfoVect0LinkObjId="g_2ebb1d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-LF_RX.LD_RX_457_0" Pin1InfoVect1LinkObjId="SW-54787_0" Pin1InfoVect2LinkObjId="SW-117646_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5089,-259 5056,-259 5056,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f78250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5089,-330 5089,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32029@1" ObjectIDZND0="9672@0" Pin0InfoVect0LinkObjId="SW-54772_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-117646_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5089,-330 5089,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f7a690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5089,-417 5122,-417 5122,-406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="32030@x" ObjectIDND1="9672@x" ObjectIDZND0="32032@1" Pin0InfoVect0LinkObjId="SW-54788_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54774_0" Pin1InfoVect1LinkObjId="SW-54772_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5089,-417 5122,-417 5122,-406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f7a8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5122,-371 5122,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32032@0" ObjectIDZND0="g_2f7aad0@0" Pin0InfoVect0LinkObjId="g_2f7aad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54788_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5122,-371 5122,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eccdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5089,-268 5122,-268 5122,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="32029@x" ObjectIDND1="g_2ebb1d0@0" ObjectIDND2="18119@x" ObjectIDZND0="32031@1" Pin0InfoVect0LinkObjId="SW-54787_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-117646_0" Pin1InfoVect1LinkObjId="g_2ebb1d0_0" Pin1InfoVect2LinkObjId="EC-LF_RX.LD_RX_457_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5089,-268 5122,-268 5122,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eccfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5122,-219 5122,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32031@0" ObjectIDZND0="g_2ecd200@0" Pin0InfoVect0LinkObjId="g_2ecd200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54787_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5122,-219 5122,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f72b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5089,-295 5089,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="32029@0" ObjectIDZND0="32031@x" ObjectIDZND1="g_2ebb1d0@0" ObjectIDZND2="18119@x" Pin0InfoVect0LinkObjId="SW-54787_0" Pin0InfoVect1LinkObjId="g_2ebb1d0_0" Pin0InfoVect2LinkObjId="EC-LF_RX.LD_RX_457_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-117646_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5089,-295 5089,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f72d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5089,-438 5089,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="32030@0" ObjectIDZND0="32032@x" ObjectIDZND1="9672@x" Pin0InfoVect0LinkObjId="SW-54788_0" Pin0InfoVect1LinkObjId="SW-54772_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54774_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5089,-438 5089,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f72f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5089,-417 5089,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="32030@x" ObjectIDND1="32032@x" ObjectIDZND0="9672@1" Pin0InfoVect0LinkObjId="SW-54772_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54774_0" Pin1InfoVect1LinkObjId="SW-54788_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5089,-417 5089,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f6bdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5089,-498 5089,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17555@0" ObjectIDZND0="32030@1" Pin0InfoVect0LinkObjId="SW-54774_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f841d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5089,-498 5089,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f6dcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3816,-270 3783,-270 3783,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="18118@x" ObjectIDND1="32014@x" ObjectIDZND0="g_2f6e110@0" Pin0InfoVect0LinkObjId="g_2f6e110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-LF_RX.LD_RX_456_0" Pin1InfoVect1LinkObjId="SW-54613_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3816,-270 3783,-270 3783,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f6def0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3816,-270 3816,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2f6e110@0" ObjectIDND1="32014@x" ObjectIDZND0="18118@0" Pin0InfoVect0LinkObjId="EC-LF_RX.LD_RX_456_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f6e110_0" Pin1InfoVect1LinkObjId="SW-54613_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3816,-270 3816,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f6ed00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3816,-332 3816,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32014@1" ObjectIDZND0="9642@0" Pin0InfoVect0LinkObjId="SW-54611_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54613_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3816,-332 3816,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f5d130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3816,-441 3816,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32013@0" ObjectIDZND0="9642@1" Pin0InfoVect0LinkObjId="SW-54611_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54612_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3816,-441 3816,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f5d320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3816,-296 3816,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="32014@0" ObjectIDZND0="18118@x" ObjectIDZND1="g_2f6e110@0" Pin0InfoVect0LinkObjId="EC-LF_RX.LD_RX_456_0" Pin0InfoVect1LinkObjId="g_2f6e110_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54613_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3816,-296 3816,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f5ecc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4882,-498 4882,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17555@0" ObjectIDZND0="32022@1" Pin0InfoVect0LinkObjId="SW-54677_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f841d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4882,-498 4882,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f5f4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4882,-269 4849,-269 4849,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="32023@x" ObjectIDND1="18113@x" ObjectIDZND0="g_2f5f960@0" Pin0InfoVect0LinkObjId="g_2f5f960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54678_0" Pin1InfoVect1LinkObjId="EC-LF_RX.LD_RX_451_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4882,-269 4849,-269 4849,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f5f700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4882,-269 4882,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2f5f960@0" ObjectIDND1="32023@x" ObjectIDZND0="18113@0" Pin0InfoVect0LinkObjId="EC-LF_RX.LD_RX_451_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f5f960_0" Pin1InfoVect1LinkObjId="SW-54678_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4882,-269 4882,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f5fd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4882,-331 4882,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32023@1" ObjectIDZND0="9657@0" Pin0InfoVect0LinkObjId="SW-54676_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54678_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4882,-331 4882,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f753c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4882,-390 4882,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9657@1" ObjectIDZND0="32022@0" Pin0InfoVect0LinkObjId="SW-54677_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54676_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4882,-390 4882,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f755b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4882,-295 4882,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="32023@0" ObjectIDZND0="g_2f5f960@0" ObjectIDZND1="18113@x" Pin0InfoVect0LinkObjId="g_2f5f960_0" Pin0InfoVect1LinkObjId="EC-LF_RX.LD_RX_451_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54678_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4882,-295 4882,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e952d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4033,-269 4000,-269 4000,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="32016@x" ObjectIDND1="18117@x" ObjectIDZND0="g_2e95790@0" Pin0InfoVect0LinkObjId="g_2e95790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54626_0" Pin1InfoVect1LinkObjId="EC-LF_RX.LD_RX_455_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4033,-269 4000,-269 4000,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e96540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4033,-331 4033,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32016@1" ObjectIDZND0="9645@0" Pin0InfoVect0LinkObjId="SW-54624_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54626_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4033,-331 4033,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f194f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4033,-440 4033,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32015@0" ObjectIDZND0="9645@1" Pin0InfoVect0LinkObjId="SW-54624_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54625_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4033,-440 4033,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f196e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4033,-295 4033,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="32016@0" ObjectIDZND0="g_2e95790@0" ObjectIDZND1="18117@x" Pin0InfoVect0LinkObjId="g_2e95790_0" Pin0InfoVect1LinkObjId="EC-LF_RX.LD_RX_455_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54626_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4033,-295 4033,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f1a0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4680,-498 4680,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17555@0" ObjectIDZND0="32020@1" Pin0InfoVect0LinkObjId="SW-54664_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f841d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4680,-498 4680,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f1c130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4680,-264 4645,-264 4645,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="18114@x" ObjectIDND1="32021@x" ObjectIDZND0="g_2e97160@0" Pin0InfoVect0LinkObjId="g_2e97160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-LF_RX.LD_RX_452_0" Pin1InfoVect1LinkObjId="SW-54665_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4680,-264 4645,-264 4645,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f1c390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4680,-264 4680,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2e97160@0" ObjectIDND1="32021@x" ObjectIDZND0="18114@0" Pin0InfoVect0LinkObjId="EC-LF_RX.LD_RX_452_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e97160_0" Pin1InfoVect1LinkObjId="SW-54665_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4680,-264 4680,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e97ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4680,-326 4680,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32021@1" ObjectIDZND0="9654@0" Pin0InfoVect0LinkObjId="SW-54663_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54665_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4680,-326 4680,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e98910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4680,-386 4680,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9654@1" ObjectIDZND0="32020@0" Pin0InfoVect0LinkObjId="SW-54664_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54663_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4680,-386 4680,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e98b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4680,-290 4680,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="32021@0" ObjectIDZND0="g_2e97160@0" ObjectIDZND1="18114@x" Pin0InfoVect0LinkObjId="g_2e97160_0" Pin0InfoVect1LinkObjId="EC-LF_RX.LD_RX_452_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54665_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4680,-290 4680,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e91720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-271 4212,-271 4212,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="18116@x" ObjectIDND1="32028@x" ObjectIDZND0="g_2e91be0@0" Pin0InfoVect0LinkObjId="g_2e91be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-LF_RX.LD_RX_454_0" Pin1InfoVect1LinkObjId="SW-54639_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-271 4212,-271 4212,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e91980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-271 4245,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2e91be0@0" ObjectIDND1="32028@x" ObjectIDZND0="18116@0" Pin0InfoVect0LinkObjId="EC-LF_RX.LD_RX_454_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e91be0_0" Pin1InfoVect1LinkObjId="SW-54639_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-271 4245,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e92990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-333 4245,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32028@1" ObjectIDZND0="9648@0" Pin0InfoVect0LinkObjId="SW-54637_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54639_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-333 4245,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e93670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-442 4245,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32017@0" ObjectIDZND0="9648@1" Pin0InfoVect0LinkObjId="SW-54637_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54638_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-442 4245,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e93860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-297 4245,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="32028@0" ObjectIDZND0="18116@x" ObjectIDZND1="g_2e91be0@0" Pin0InfoVect0LinkObjId="EC-LF_RX.LD_RX_454_0" Pin0InfoVect1LinkObjId="g_2e91be0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54639_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-297 4245,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f4c170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4483,-498 4483,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17555@0" ObjectIDZND0="32018@1" Pin0InfoVect0LinkObjId="SW-54651_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f841d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4483,-498 4483,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f89c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4483,-270 4450,-270 4450,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="32019@x" ObjectIDND1="18115@x" ObjectIDZND0="g_2f2a190@0" Pin0InfoVect0LinkObjId="g_2f2a190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54652_0" Pin1InfoVect1LinkObjId="EC-LF_RX.LD_RX_453_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4483,-270 4450,-270 4450,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f89ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4483,-270 4483,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2f2a190@0" ObjectIDND1="32019@x" ObjectIDZND0="18115@0" Pin0InfoVect0LinkObjId="EC-LF_RX.LD_RX_453_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f2a190_0" Pin1InfoVect1LinkObjId="SW-54652_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4483,-270 4483,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f2ae70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4483,-332 4483,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32019@1" ObjectIDZND0="9651@0" Pin0InfoVect0LinkObjId="SW-54650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54652_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4483,-332 4483,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f2bbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4483,-391 4483,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9651@1" ObjectIDZND0="32018@0" Pin0InfoVect0LinkObjId="SW-54651_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54650_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4483,-391 4483,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f2bdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4483,-296 4483,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="32019@0" ObjectIDZND0="g_2f2a190@0" ObjectIDZND1="18115@x" Pin0InfoVect0LinkObjId="g_2f2a190_0" Pin0InfoVect1LinkObjId="EC-LF_RX.LD_RX_453_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54652_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4483,-296 4483,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ff8ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3816,-477 3816,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32013@1" ObjectIDZND0="17556@0" Pin0InfoVect0LinkObjId="g_2f902d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54612_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3816,-477 3816,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ff9120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4033,-476 4033,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32015@1" ObjectIDZND0="17556@0" Pin0InfoVect0LinkObjId="g_2f902d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54625_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4033,-476 4033,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f6b490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4675,-881 4675,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32002@1" ObjectIDZND0="17554@0" Pin0InfoVect0LinkObjId="g_2f8c200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54692_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4675,-881 4675,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ee2f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4726,-992 4737,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32000@1" ObjectIDZND0="g_2edff40@0" Pin0InfoVect0LinkObjId="g_2edff40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-117644_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4726,-992 4737,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fb7450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4607,-1126 4606,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32001@1" ObjectIDZND0="g_2fb6a00@0" Pin0InfoVect0LinkObjId="g_2fb6a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-117645_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4607,-1126 4606,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fb7680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-1168 4755,-1156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_2ea4810@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2ea3c00_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ea4810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-1168 4755,-1156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fb8690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4650,-1106 4650,-1092 4675,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2fb78e0@0" ObjectIDZND0="g_2f0ac10@0" ObjectIDZND1="31999@x" ObjectIDZND2="32001@x" Pin0InfoVect0LinkObjId="g_2f0ac10_0" Pin0InfoVect1LinkObjId="SW-117643_0" Pin0InfoVect2LinkObjId="SW-117645_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fb78e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4650,-1106 4650,-1092 4675,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fb88f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4675,-1159 4675,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="18073@1" ObjectIDZND0="g_2fb78e0@0" ObjectIDZND1="g_2f0ac10@0" ObjectIDZND2="31999@x" Pin0InfoVect0LinkObjId="g_2fb78e0_0" Pin0InfoVect1LinkObjId="g_2f0ac10_0" Pin0InfoVect2LinkObjId="SW-117643_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4675,-1159 4675,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fb8b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4675,-1082 4675,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_2f0ac10@0" ObjectIDND1="31999@x" ObjectIDND2="32001@x" ObjectIDZND0="g_2fb78e0@0" ObjectIDZND1="18073@1" Pin0InfoVect0LinkObjId="g_2fb78e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f0ac10_0" Pin1InfoVect1LinkObjId="SW-117643_0" Pin1InfoVect2LinkObjId="SW-117645_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4675,-1082 4675,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fbb450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4675,-948 4675,-917 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22001@0" ObjectIDZND0="32002@0" Pin0InfoVect0LinkObjId="SW-54692_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-117642_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4675,-948 4675,-917 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f3a1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-711 3976,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="9678@0" ObjectIDZND0="9666@0" Pin0InfoVect0LinkObjId="SW-54703_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-711 3976,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f3a3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-760 4764,-711 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="9660@0" ObjectIDZND0="9677@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-760 4764,-711 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2edd200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-576 3708,-576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9664@x" ObjectIDND1="g_2edc650@0" ObjectIDZND0="g_2f957f0@0" Pin0InfoVect0LinkObjId="g_2f957f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54699_0" Pin1InfoVect1LinkObjId="g_2edc650_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-576 3708,-576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e79fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-559 3737,-576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9664@1" ObjectIDZND0="g_2f957f0@0" ObjectIDZND1="g_2edc650@0" Pin0InfoVect0LinkObjId="g_2f957f0_0" Pin0InfoVect1LinkObjId="g_2edc650_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54699_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-559 3737,-576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e7a230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-576 3737,-600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2f957f0@0" ObjectIDND1="9664@x" ObjectIDZND0="g_2edc650@0" Pin0InfoVect0LinkObjId="g_2edc650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f957f0_0" Pin1InfoVect1LinkObjId="SW-54699_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-576 3737,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e7e190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4584,-650 4584,-659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2e7d5e0@1" ObjectIDZND0="g_2e7c070@0" Pin0InfoVect0LinkObjId="g_2e7c070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e7d5e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4584,-650 4584,-659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e7e9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4584,-578 4555,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9639@x" ObjectIDND1="g_2e7d5e0@0" ObjectIDZND0="g_2e7ec20@0" Pin0InfoVect0LinkObjId="g_2e7ec20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54591_0" Pin1InfoVect1LinkObjId="g_2e7d5e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4584,-578 4555,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e80260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4584,-550 4584,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9639@1" ObjectIDZND0="g_2e7ec20@0" ObjectIDZND1="g_2e7d5e0@0" Pin0InfoVect0LinkObjId="g_2e7ec20_0" Pin0InfoVect1LinkObjId="g_2e7d5e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54591_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4584,-550 4584,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e9e790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4584,-578 4584,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2e7ec20@0" ObjectIDND1="9639@x" ObjectIDZND0="g_2e7d5e0@0" Pin0InfoVect0LinkObjId="g_2e7d5e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e7ec20_0" Pin1InfoVect1LinkObjId="SW-54591_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4584,-578 4584,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ea2220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5089,-268 5089,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="32031@x" ObjectIDND1="32029@x" ObjectIDZND0="g_2ebb1d0@0" ObjectIDZND1="18119@x" Pin0InfoVect0LinkObjId="g_2ebb1d0_0" Pin0InfoVect1LinkObjId="EC-LF_RX.LD_RX_457_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54787_0" Pin1InfoVect1LinkObjId="SW-117646_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5089,-268 5089,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ea2460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5089,-259 5089,-182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_2ebb1d0@0" ObjectIDND1="32031@x" ObjectIDND2="32029@x" ObjectIDZND0="18119@0" Pin0InfoVect0LinkObjId="EC-LF_RX.LD_RX_457_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ebb1d0_0" Pin1InfoVect1LinkObjId="SW-54787_0" Pin1InfoVect2LinkObjId="SW-117646_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5089,-259 5089,-182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ea39a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3990,-1018 3990,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2ea2f50@0" Pin0InfoVect0LinkObjId="g_2ea2f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ea3c00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3990,-1018 3990,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f0b690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-1111 4755,-1083 4733,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2f0ac10@0" Pin0InfoVect0LinkObjId="g_2f0ac10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ea3c00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-1111 4755,-1083 4733,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f0b8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4700,-1083 4675,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_2f0ac10@1" ObjectIDZND0="g_2fb78e0@0" ObjectIDZND1="18073@1" ObjectIDZND2="31999@x" Pin0InfoVect0LinkObjId="g_2fb78e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="SW-117643_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f0ac10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4700,-1083 4675,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f0e900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4675,-1043 4675,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="31999@0" ObjectIDZND0="g_2fb78e0@0" ObjectIDZND1="18073@1" ObjectIDZND2="g_2f0ac10@0" Pin0InfoVect0LinkObjId="g_2fb78e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="g_2f0ac10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-117643_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4675,-1043 4675,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f0f270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4675,-1082 4675,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2fb78e0@0" ObjectIDND1="18073@1" ObjectIDND2="g_2f0ac10@0" ObjectIDZND0="31999@x" ObjectIDZND1="32001@x" Pin0InfoVect0LinkObjId="SW-117643_0" Pin0InfoVect1LinkObjId="SW-117645_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2fb78e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="g_2f0ac10_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4675,-1082 4675,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f0f460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4675,-1068 4607,-1068 4607,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="31999@x" ObjectIDND1="g_2fb78e0@0" ObjectIDND2="18073@1" ObjectIDZND0="32001@0" Pin0InfoVect0LinkObjId="SW-117645_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-117643_0" Pin1InfoVect1LinkObjId="g_2fb78e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4675,-1068 4607,-1068 4607,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e58ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4692,-992 4675,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="32000@0" ObjectIDZND0="31999@x" ObjectIDZND1="22001@x" Pin0InfoVect0LinkObjId="SW-117643_0" Pin0InfoVect1LinkObjId="SW-117642_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-117644_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4692,-992 4675,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e59950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4675,-1007 4675,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="31999@1" ObjectIDZND0="32000@x" ObjectIDZND1="22001@x" Pin0InfoVect0LinkObjId="SW-117644_0" Pin0InfoVect1LinkObjId="SW-117642_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-117643_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4675,-1007 4675,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e59bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4675,-992 4675,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="32000@x" ObjectIDND1="31999@x" ObjectIDZND0="22001@1" Pin0InfoVect0LinkObjId="SW-117642_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-117644_0" Pin1InfoVect1LinkObjId="SW-117643_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4675,-992 4675,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e59e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-478 4245,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32017@1" ObjectIDZND0="17556@0" Pin0InfoVect0LinkObjId="g_2f902d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54638_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-478 4245,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fe09b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-523 3737,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9664@0" ObjectIDZND0="17556@0" Pin0InfoVect0LinkObjId="g_2f902d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54699_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-523 3737,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37cd140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3845,-852 3845,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_2f05ab0@1" ObjectIDZND0="17554@0" Pin0InfoVect0LinkObjId="g_2f8c200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f05ab0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3845,-852 3845,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37db080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3845,-723 3845,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_37c0aa0@0" Pin0InfoVect0LinkObjId="g_37c0aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ea3c00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3845,-723 3845,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37db270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3845,-793 3845,-807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_37c0aa0@1" ObjectIDZND0="g_2f05ab0@0" Pin0InfoVect0LinkObjId="g_2f05ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37c0aa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3845,-793 3845,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e95530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4033,-269 4033,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2e95790@0" ObjectIDND1="32016@x" ObjectIDZND0="18117@0" Pin0InfoVect0LinkObjId="EC-LF_RX.LD_RX_455_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e95790_0" Pin1InfoVect1LinkObjId="SW-54626_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4033,-269 4033,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e31f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3638,-498 3638,-473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="17556@0" ObjectIDZND0="g_37e4040@1" Pin0InfoVect0LinkObjId="g_37e4040_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f902d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3638,-498 3638,-473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3125900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3638,-420 3638,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_37e4040@0" ObjectIDZND0="g_2e50150@1" Pin0InfoVect0LinkObjId="g_2e50150_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37e4040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3638,-420 3638,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3125af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3638,-341 3638,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2e50150@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2ea3c00_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e50150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3638,-341 3638,-310 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="LF_RX"/>
</svg>