<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-50" aopId="256" id="thSvg" viewBox="3117 -1200 1755 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.890909" x1="29" x2="29" y1="6" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.583333" x1="26" x2="26" y1="4" y2="13"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="18" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="5" x2="5" y1="13" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape44">
    <ellipse cx="11" cy="16" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="29" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline fill="none" points="27,39 5,17 5,5 "/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape113">
    <ellipse cx="12" cy="15" rx="11" ry="12.5" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="8" x2="12" y1="18" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="12" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="16" x2="12" y1="18" y2="14"/>
    <ellipse cx="12" cy="35" rx="11" ry="12" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="17" y1="41" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="7" x2="17" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="7" y1="41" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape130">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="43" x2="43" y1="33" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="53" x2="33" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="53" x2="33" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="43" x2="43" y1="15" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="77" x2="77" y1="33" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="87" x2="67" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="87" x2="67" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="77" x2="77" y1="15" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="33" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="20" x2="0" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="20" x2="0" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="15" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="77" x2="10" y1="33" y2="33"/>
   </symbol>
   <symbol id="lightningRod:shape99">
    <polyline arcFlag="1" fill="none" points="26,30 26,31 26,32 26,33 25,34 25,35 24,36 24,36 23,37 22,37 21,38 20,38 19,38 18,39 17,38 16,38 15,38 14,37 14,37 13,36 12,36 12,35 11,34 11,33 11,32 10,31 11,30 "/>
    <polyline fill="none" points="41,30 42,29 41,29 41,28 41,27 40,26 40,25 39,24 38,24 38,23 37,23 36,22 35,22 34,22 33,22 32,22 31,23 30,23 29,24 28,24 28,25 27,26 27,27 26,28 26,29 26,29 26,30 "/>
    <circle cx="25" cy="29" r="24" stroke-width="0.5"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape162">
    <ellipse cx="18" cy="67" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="15" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="40" y1="15" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="60" y1="57" y2="82"/>
    <rect height="27" stroke-width="0.416667" width="14" x="12" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="59" x2="20" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="60" x2="60" y1="15" y2="53"/>
    <rect height="27" stroke-width="0.416667" width="14" x="53" y="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="18" y1="82" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="30" x2="42" y1="92" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="36" x2="36" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="40" x2="32" y1="94" y2="94"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="18" y1="75" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="39" x2="36" y1="97" y2="97"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="21" x2="18" y1="71" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="18" x2="18" y1="69" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="15" x2="18" y1="71" y2="69"/>
    <ellipse cx="8" cy="62" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="8" x2="8" y1="64" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="66" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="5" x2="8" y1="66" y2="64"/>
    <ellipse cx="17" cy="56" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="32" x2="29" y1="61" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="32" x2="28" y1="60" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="29" x2="29" y1="58" y2="64"/>
    <ellipse cx="28" cy="61" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="18" x2="18" y1="54" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="21" x2="18" y1="56" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="15" x2="18" y1="56" y2="54"/>
   </symbol>
   <symbol id="lightningRod:shape163">
    <ellipse cx="19" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <polyline fill="none" points="34,21 34,9 19,9 "/>
    <polyline fill="none" points="35,33 35,30 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="29" y1="28" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="44" y1="28" y2="28"/>
    <rect height="9" stroke-width="1" width="5" x="32" y="21"/>
    <ellipse cx="8" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="32" x2="38" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="34" x2="36" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="31" x2="39" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="7" x2="7" y1="20" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="10" x2="7" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="4" x2="7" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="10" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="9" x2="9" y1="12" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="19" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="19" y2="21"/>
   </symbol>
   <symbol id="load:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="5" y2="29"/>
    <polyline fill="none" points="1,19 9,31 17,19 "/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="18" x2="1" y1="33" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.675" x1="18" x2="18" y1="27" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.845585" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="21" x2="1" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="19" x2="28" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="19" x2="19" y1="28" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="-8" x2="1" y1="29" y2="29"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="16" x2="-1" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="17" x2="25" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="16" x2="16" y1="28" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="-7" x2="-2" y1="27" y2="27"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="21" x2="1" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="19" x2="28" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="19" x2="19" y1="26" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="-8" x2="1" y1="27" y2="27"/>
   </symbol>
   <symbol id="transformer2:shape8_0">
    <circle cx="42" cy="16" r="15" stroke-width="1"/>
    <polyline fill="none" points="41,15 41,40 70,40 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape8_1">
    <circle cx="20" cy="16" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="22" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="voltageTransformer:shape6">
    <circle cx="18" cy="15" r="6" stroke-width="0.431185"/>
    <circle cx="11" cy="11" r="6" stroke-width="0.431185"/>
    <circle cx="18" cy="7" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="32" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(0.512795 -0.000000 0.000000 -1.035714 2.846957 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1211" width="1765" x="3112" y="-1205"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-599"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-40402">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4240.000000 -887.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6542" ObjectName="SW-CX_HQ.CX_HQ_3116SW"/>
     <cge:Meas_Ref ObjectId="40402"/>
    <cge:TPSR_Ref TObjectID="6542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40404">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4480.000000 -917.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6544" ObjectName="SW-CX_HQ.CX_HQ_3901SW"/>
     <cge:Meas_Ref ObjectId="40404"/>
    <cge:TPSR_Ref TObjectID="6544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40410">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.022727 -0.000000 0.000000 -1.000000 4522.000000 -892.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6550" ObjectName="SW-CX_HQ.CX_HQ_39010SW"/>
     <cge:Meas_Ref ObjectId="40410"/>
    <cge:TPSR_Ref TObjectID="6550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40414">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.045455 -0.000000 0.000000 -1.076923 4633.000000 -308.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6554" ObjectName="SW-CX_HQ.CX_HQ_69027SW"/>
     <cge:Meas_Ref ObjectId="40414"/>
    <cge:TPSR_Ref TObjectID="6554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40403">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.068182 -0.000000 0.000000 -0.923077 4282.000000 -988.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6543" ObjectName="SW-CX_HQ.CX_HQ_31167SW"/>
     <cge:Meas_Ref ObjectId="40403"/>
    <cge:TPSR_Ref TObjectID="6543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40409">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.022727 -0.000000 0.000000 -1.000000 4522.000000 -970.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6549" ObjectName="SW-CX_HQ.CX_HQ_39017SW"/>
     <cge:Meas_Ref ObjectId="40409"/>
    <cge:TPSR_Ref TObjectID="6549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40401">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4076.000000 -783.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6541" ObjectName="SW-CX_HQ.CX_HQ_3011SW"/>
     <cge:Meas_Ref ObjectId="40401"/>
    <cge:TPSR_Ref TObjectID="6541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40406">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4076.000000 -413.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6546" ObjectName="SW-CX_HQ.CX_HQ_6011SW"/>
     <cge:Meas_Ref ObjectId="40406"/>
    <cge:TPSR_Ref TObjectID="6546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40408">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.022727 -0.000000 0.000000 -1.076923 4119.000000 -459.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6548" ObjectName="SW-CX_HQ.CX_HQ_60117SW"/>
     <cge:Meas_Ref ObjectId="40408"/>
    <cge:TPSR_Ref TObjectID="6548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40407">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4076.000000 -537.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6547" ObjectName="SW-CX_HQ.CX_HQ_6016SW"/>
     <cge:Meas_Ref ObjectId="40407"/>
    <cge:TPSR_Ref TObjectID="6547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40418">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3855.000000 -338.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6558" ObjectName="SW-CX_HQ.CX_HQ_6211SW"/>
     <cge:Meas_Ref ObjectId="40418"/>
    <cge:TPSR_Ref TObjectID="6558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40422">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4112.000000 -338.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6562" ObjectName="SW-CX_HQ.CX_HQ_6221SW"/>
     <cge:Meas_Ref ObjectId="40422"/>
    <cge:TPSR_Ref TObjectID="6562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40426">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4368.000000 -338.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6566" ObjectName="SW-CX_HQ.CX_HQ_6231SW"/>
     <cge:Meas_Ref ObjectId="40426"/>
    <cge:TPSR_Ref TObjectID="6566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40411">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3808.000000 -413.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6551" ObjectName="SW-CX_HQ.CX_HQ_6901SW"/>
     <cge:Meas_Ref ObjectId="40411"/>
    <cge:TPSR_Ref TObjectID="6551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40412">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.022727 -0.000000 0.000000 -1.076923 3849.000000 -460.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6552" ObjectName="SW-CX_HQ.CX_HQ_69017SW"/>
     <cge:Meas_Ref ObjectId="40412"/>
    <cge:TPSR_Ref TObjectID="6552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40415">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4338.000000 -415.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6555" ObjectName="SW-CX_HQ.CX_HQ_6903SW"/>
     <cge:Meas_Ref ObjectId="40415"/>
    <cge:TPSR_Ref TObjectID="6555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40416">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.022727 -0.000000 0.000000 -1.076923 4379.000000 -462.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6556" ObjectName="SW-CX_HQ.CX_HQ_69037SW"/>
     <cge:Meas_Ref ObjectId="40416"/>
    <cge:TPSR_Ref TObjectID="6556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4655.000000 -412.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11303" ObjectName="SW-CX_HQ.CX_HQ_6411SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.022727 -0.000000 0.000000 -1.076923 4696.000000 -459.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11304" ObjectName="SW-CX_HQ.CX_HQ_64117SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4655.000000 -528.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11305" ObjectName="SW-CX_HQ.CX_HQ_6416SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40427">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.045455 -0.000000 0.000000 -1.076923 4342.000000 -315.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6567" ObjectName="SW-CX_HQ.CX_HQ_62317SW"/>
     <cge:Meas_Ref ObjectId="40427"/>
    <cge:TPSR_Ref TObjectID="6567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40423">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.045455 -0.000000 0.000000 -1.076923 4086.000000 -315.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6563" ObjectName="SW-CX_HQ.CX_HQ_62217SW"/>
     <cge:Meas_Ref ObjectId="40423"/>
    <cge:TPSR_Ref TObjectID="6563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40419">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.045455 -0.000000 0.000000 -1.076923 3830.000000 -315.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6559" ObjectName="SW-CX_HQ.CX_HQ_62117SW"/>
     <cge:Meas_Ref ObjectId="40419"/>
    <cge:TPSR_Ref TObjectID="6559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40420">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 -213.000000)" xlink:href="#switch2:shape0-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6560" ObjectName="SW-CX_HQ.CX_HQ_6911SW"/>
     <cge:Meas_Ref ObjectId="40420"/>
    <cge:TPSR_Ref TObjectID="6560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40424">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 -212.000000)" xlink:href="#switch2:shape0-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6564" ObjectName="SW-CX_HQ.CX_HQ_6921SW"/>
     <cge:Meas_Ref ObjectId="40424"/>
    <cge:TPSR_Ref TObjectID="6564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40428">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4271.000000 -213.000000)" xlink:href="#switch2:shape0-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6568" ObjectName="SW-CX_HQ.CX_HQ_6931SW"/>
     <cge:Meas_Ref ObjectId="40428"/>
    <cge:TPSR_Ref TObjectID="6568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40413">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4590.000000 -336.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6553" ObjectName="SW-CX_HQ.CX_HQ_6902SW"/>
     <cge:Meas_Ref ObjectId="40413"/>
    <cge:TPSR_Ref TObjectID="6553"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_HQ.CX_HQ_6IM">
    <g class="BV-6KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3669,-413 4872,-413 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6539" ObjectName="BS-CX_HQ.CX_HQ_6IM"/>
    <cge:TPSR_Ref TObjectID="6539"/></metadata>
   <polyline fill="none" opacity="0" points="3669,-413 4872,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HQ.CX_HQ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3845,-881 4699,-881 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6538" ObjectName="BS-CX_HQ.CX_HQ_3IM"/>
    <cge:TPSR_Ref TObjectID="6538"/></metadata>
   <polyline fill="none" opacity="0" points="3845,-881 4699,-881 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4246.000000 -1052.000000)" xlink:href="#load:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_4160870">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4665.000000 -326.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_415f850">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.055556 4151.000000 -478.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_403b600">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.055556 4411.000000 -481.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4031b30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.055556 4728.000000 -478.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3009130">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3881.000000 -479.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3705540">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3781.000000 -334.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ffb070">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4038.000000 -334.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ffb380">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -334.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3af3bf0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4554.000000 -987.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fb0c80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4554.000000 -909.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3333d20">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4315.000000 -1003.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_4155f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-880 4255,-909 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6538@0" ObjectIDZND0="6542@0" Pin0InfoVect0LinkObjId="SW-40402_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2eef200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-880 4255,-909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30fb270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-918 4513,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="6544@x" ObjectIDND1="6538@0" ObjectIDZND0="6550@0" Pin0InfoVect0LinkObjId="SW-40410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40404_0" Pin1InfoVect1LinkObjId="g_2eef200_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-918 4513,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c1b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4549,-918 4559,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6550@1" ObjectIDZND0="g_3fb0c80@0" Pin0InfoVect0LinkObjId="g_3fb0c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40410_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4549,-918 4559,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_4174420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4660,-335 4670,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6554@1" ObjectIDZND0="g_4160870@0" Pin0InfoVect0LinkObjId="g_4160870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40414_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4660,-335 4670,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4175780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3998,-880 3998,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="6538@0" ObjectIDZND0="g_400a700@0" Pin0InfoVect0LinkObjId="g_400a700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2eef200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3998,-880 3998,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c3ac80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3998,-1014 3998,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_400a700@1" Pin0InfoVect0LinkObjId="g_400a700_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3998,-1014 3998,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c0d360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-1012 4223,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="6542@x" ObjectIDND1="0@x" ObjectIDND2="6543@x" ObjectIDZND0="g_2e14a90@0" Pin0InfoVect0LinkObjId="g_2e14a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40402_0" Pin1InfoVect1LinkObjId="EC-0_0" Pin1InfoVect2LinkObjId="SW-40403_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-1012 4223,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3afeb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-1057 4255,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2e14a90@0" ObjectIDZND1="6542@x" ObjectIDZND2="6543@x" Pin0InfoVect0LinkObjId="g_2e14a90_0" Pin0InfoVect1LinkObjId="SW-40402_0" Pin0InfoVect2LinkObjId="SW-40403_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-1057 4255,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a61b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-1012 4255,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2e14a90@0" ObjectIDND1="0@x" ObjectIDND2="6543@x" ObjectIDZND0="6542@1" Pin0InfoVect0LinkObjId="SW-40402_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e14a90_0" Pin1InfoVect1LinkObjId="EC-0_0" Pin1InfoVect2LinkObjId="SW-40403_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-1012 4255,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f0a520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4256,-1012 4274,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_2e14a90@0" ObjectIDND1="6542@x" ObjectIDND2="0@x" ObjectIDZND0="6543@0" Pin0InfoVect0LinkObjId="SW-40403_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e14a90_0" Pin1InfoVect1LinkObjId="SW-40402_0" Pin1InfoVect2LinkObjId="EC-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4256,-1012 4274,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34f94f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-1012 4320,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6543@1" ObjectIDZND0="g_3333d20@0" Pin0InfoVect0LinkObjId="g_3333d20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40403_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-1012 4320,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3776010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-880 4495,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6538@0" ObjectIDZND0="6544@x" ObjectIDZND1="6550@x" Pin0InfoVect0LinkObjId="SW-40404_0" Pin0InfoVect1LinkObjId="SW-40410_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2eef200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-880 4495,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ceb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-918 4495,-939 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6538@0" ObjectIDND1="6550@x" ObjectIDZND0="6544@0" Pin0InfoVect0LinkObjId="SW-40404_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2eef200_0" Pin1InfoVect1LinkObjId="SW-40410_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-918 4495,-939 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ccbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-996 4513,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="6544@x" ObjectIDND1="g_2e1cdb0@0" ObjectIDZND0="6549@0" Pin0InfoVect0LinkObjId="SW-40409_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40404_0" Pin1InfoVect1LinkObjId="g_2e1cdb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-996 4513,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_354cb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4549,-996 4559,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6549@1" ObjectIDZND0="g_3af3bf0@0" Pin0InfoVect0LinkObjId="g_3af3bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40409_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4549,-996 4559,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_386b340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-975 4495,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6544@1" ObjectIDZND0="g_2e1cdb0@0" ObjectIDZND1="6549@x" Pin0InfoVect0LinkObjId="g_2e1cdb0_0" Pin0InfoVect1LinkObjId="SW-40409_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40404_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-975 4495,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_415a510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4092,-487 4110,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6546@x" ObjectIDND1="6545@x" ObjectIDZND0="6548@0" Pin0InfoVect0LinkObjId="SW-40408_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40406_0" Pin1InfoVect1LinkObjId="SW-40405_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4092,-487 4110,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_35f24d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4146,-487 4156,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6548@1" ObjectIDZND0="g_415f850@0" Pin0InfoVect0LinkObjId="g_415f850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40408_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4146,-487 4156,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3b02990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-487 4091,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6545@x" ObjectIDND1="6548@x" ObjectIDZND0="6546@1" Pin0InfoVect0LinkObjId="SW-40406_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40405_0" Pin1InfoVect1LinkObjId="SW-40408_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-487 4091,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_34aaab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-501 4091,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6545@0" ObjectIDZND0="6546@x" ObjectIDZND1="6548@x" Pin0InfoVect0LinkObjId="SW-40406_0" Pin0InfoVect1LinkObjId="SW-40408_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40405_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-501 4091,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_34787d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-413 3870,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6539@0" ObjectIDZND0="6558@1" Pin0InfoVect0LinkObjId="SW-40418_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3acba40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-413 3870,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3567f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-360 3870,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6558@0" ObjectIDZND0="6557@x" ObjectIDZND1="6559@x" Pin0InfoVect0LinkObjId="SW-40417_0" Pin0InfoVect1LinkObjId="SW-40419_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40418_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-360 3870,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_34c5300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-343 3870,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6558@x" ObjectIDND1="6559@x" ObjectIDZND0="6557@1" Pin0InfoVect0LinkObjId="SW-40417_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40418_0" Pin1InfoVect1LinkObjId="SW-40419_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-343 3870,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3c04460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-298 3870,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="6557@0" ObjectIDZND0="g_2e47b80@0" ObjectIDZND1="6560@x" ObjectIDZND2="g_34c5110@0" Pin0InfoVect0LinkObjId="g_2e47b80_0" Pin0InfoVect1LinkObjId="SW-40420_0" Pin0InfoVect2LinkObjId="g_34c5110_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40417_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-298 3870,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3c08d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-288 3870,-246 3870,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="6557@x" ObjectIDND1="g_2e47b80@0" ObjectIDND2="6560@x" ObjectIDZND0="g_34c5110@0" Pin0InfoVect0LinkObjId="g_34c5110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40417_0" Pin1InfoVect1LinkObjId="g_2e47b80_0" Pin1InfoVect2LinkObjId="SW-40420_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-288 3870,-246 3870,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_311cb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3869,-185 3869,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_34c5110@1" ObjectIDZND0="g_3557d70@0" Pin0InfoVect0LinkObjId="g_3557d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34c5110_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3869,-185 3869,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3871810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-288 3820,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6557@x" ObjectIDND1="g_34c5110@0" ObjectIDZND0="g_2e47b80@0" ObjectIDZND1="6560@x" Pin0InfoVect0LinkObjId="g_2e47b80_0" Pin0InfoVect1LinkObjId="SW-40420_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40417_0" Pin1InfoVect1LinkObjId="g_34c5110_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-288 3820,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3123910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-360 4127,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6562@0" ObjectIDZND0="6561@x" ObjectIDZND1="6563@x" Pin0InfoVect0LinkObjId="SW-40421_0" Pin0InfoVect1LinkObjId="SW-40423_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40422_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-360 4127,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3562b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-343 4127,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6562@x" ObjectIDND1="6563@x" ObjectIDZND0="6561@1" Pin0InfoVect0LinkObjId="SW-40421_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40422_0" Pin1InfoVect1LinkObjId="SW-40423_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-343 4127,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_34c7b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-304 4127,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="6561@0" ObjectIDZND0="g_33ce1b0@0" ObjectIDZND1="g_3656090@0" ObjectIDZND2="6564@x" Pin0InfoVect0LinkObjId="g_33ce1b0_0" Pin0InfoVect1LinkObjId="g_3656090_0" Pin0InfoVect2LinkObjId="SW-40424_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40421_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-304 4127,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_33787c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-288 4127,-246 4127,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="6561@x" ObjectIDND1="g_3656090@0" ObjectIDND2="6564@x" ObjectIDZND0="g_33ce1b0@0" Pin0InfoVect0LinkObjId="g_33ce1b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40421_0" Pin1InfoVect1LinkObjId="g_3656090_0" Pin1InfoVect2LinkObjId="SW-40424_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-288 4127,-246 4127,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_372f1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-185 4127,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_33ce1b0@1" ObjectIDZND0="g_2ee0bb0@0" Pin0InfoVect0LinkObjId="g_2ee0bb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33ce1b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-185 4127,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2e3cfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-288 4077,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_33ce1b0@0" ObjectIDND1="6561@x" ObjectIDZND0="g_3656090@0" ObjectIDZND1="6564@x" Pin0InfoVect0LinkObjId="g_3656090_0" Pin0InfoVect1LinkObjId="SW-40424_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33ce1b0_0" Pin1InfoVect1LinkObjId="SW-40421_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-288 4077,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3adb6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4383,-415 4383,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6539@0" ObjectIDZND0="6566@1" Pin0InfoVect0LinkObjId="SW-40426_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3acba40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4383,-415 4383,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3adb4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4383,-360 4383,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6566@0" ObjectIDZND0="6565@x" ObjectIDZND1="6567@x" Pin0InfoVect0LinkObjId="SW-40425_0" Pin0InfoVect1LinkObjId="SW-40427_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40426_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4383,-360 4383,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3ada940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4383,-343 4383,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6566@x" ObjectIDND1="6567@x" ObjectIDZND0="6565@1" Pin0InfoVect0LinkObjId="SW-40425_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40426_0" Pin1InfoVect1LinkObjId="SW-40427_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4383,-343 4383,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3ada720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4383,-303 4383,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="6565@0" ObjectIDZND0="g_2e24a60@0" ObjectIDZND1="g_33183f0@0" ObjectIDZND2="6568@x" Pin0InfoVect0LinkObjId="g_2e24a60_0" Pin0InfoVect1LinkObjId="g_33183f0_0" Pin0InfoVect2LinkObjId="SW-40428_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40425_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4383,-303 4383,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3ad9080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4383,-185 4383,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2e24a60@1" ObjectIDZND0="g_31fb7c0@0" Pin0InfoVect0LinkObjId="g_31fb7c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e24a60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4383,-185 4383,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3acba40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-435 4091,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6546@0" ObjectIDZND0="6539@0" Pin0InfoVect0LinkObjId="g_3ac0a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40406_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-435 4091,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3ac0a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-396 4127,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6562@1" ObjectIDZND0="6539@0" Pin0InfoVect0LinkObjId="g_3acba40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40422_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-396 4127,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3b35b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3823,-413 3823,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6539@0" ObjectIDZND0="6551@0" Pin0InfoVect0LinkObjId="SW-40411_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3acba40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3823,-413 3823,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3b34ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3822,-488 3840,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="6551@x" ObjectIDND1="g_3c31a00@0" ObjectIDZND0="6552@0" Pin0InfoVect0LinkObjId="SW-40412_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40411_0" Pin1InfoVect1LinkObjId="g_3c31a00_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3822,-488 3840,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3b34270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3876,-488 3886,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6552@1" ObjectIDZND0="g_3009130@0" Pin0InfoVect0LinkObjId="g_3009130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40412_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3876,-488 3886,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_4049bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3823,-471 3823,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6551@1" ObjectIDZND0="g_3c31a00@0" ObjectIDZND1="6552@x" Pin0InfoVect0LinkObjId="g_3c31a00_0" Pin0InfoVect1LinkObjId="SW-40412_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40411_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3823,-471 3823,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_401ebf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3823,-488 3823,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6551@x" ObjectIDND1="6552@x" ObjectIDZND0="g_3c31a00@0" Pin0InfoVect0LinkObjId="g_3c31a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40411_0" Pin1InfoVect1LinkObjId="SW-40412_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3823,-488 3823,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_401a960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-413 4353,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6539@0" ObjectIDZND0="6555@0" Pin0InfoVect0LinkObjId="SW-40415_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3acba40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-413 4353,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2e2bda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-490 4370,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="6555@x" ObjectIDND1="g_2e13280@0" ObjectIDND2="g_2ee79f0@0" ObjectIDZND0="6556@0" Pin0InfoVect0LinkObjId="SW-40416_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40415_0" Pin1InfoVect1LinkObjId="g_2e13280_0" Pin1InfoVect2LinkObjId="g_2ee79f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-490 4370,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_40243c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4406,-490 4416,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6556@1" ObjectIDZND0="g_403b600@0" Pin0InfoVect0LinkObjId="g_403b600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40416_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4406,-490 4416,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2e114a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-473 4353,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="6555@1" ObjectIDZND0="g_2e13280@0" ObjectIDZND1="g_2ee79f0@0" ObjectIDZND2="6556@x" Pin0InfoVect0LinkObjId="g_2e13280_0" Pin0InfoVect1LinkObjId="g_2ee79f0_0" Pin0InfoVect2LinkObjId="SW-40416_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40415_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-473 4353,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_30ddaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4383,-237 4383,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2e24a60@0" ObjectIDZND0="6565@x" ObjectIDZND1="g_33183f0@0" ObjectIDZND2="6568@x" Pin0InfoVect0LinkObjId="SW-40425_0" Pin0InfoVect1LinkObjId="g_33183f0_0" Pin0InfoVect2LinkObjId="SW-40428_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e24a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4383,-237 4383,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3829300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4383,-288 4333,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6565@x" ObjectIDND1="g_2e24a60@0" ObjectIDZND0="g_33183f0@0" ObjectIDZND1="6568@x" Pin0InfoVect0LinkObjId="g_33183f0_0" Pin0InfoVect1LinkObjId="SW-40428_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40425_0" Pin1InfoVect1LinkObjId="g_2e24a60_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4383,-288 4333,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3221ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-490 4320,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="6555@x" ObjectIDND1="g_2e13280@0" ObjectIDND2="6556@x" ObjectIDZND0="g_2ee79f0@0" Pin0InfoVect0LinkObjId="g_2ee79f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40415_0" Pin1InfoVect1LinkObjId="g_2e13280_0" Pin1InfoVect2LinkObjId="SW-40416_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-490 4320,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3c48af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-413 4670,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6539@0" ObjectIDZND0="11303@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3acba40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-413 4670,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3c02bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4669,-487 4687,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="11303@x" ObjectIDND1="10970@x" ObjectIDZND0="11304@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4669,-487 4687,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_30e9ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4723,-487 4733,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11304@1" ObjectIDZND0="g_4031b30@0" Pin0InfoVect0LinkObjId="g_4031b30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4723,-487 4733,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_30ea430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-470 4670,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="11303@1" ObjectIDZND0="10970@x" ObjectIDZND1="11304@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-470 4670,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_322a5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-487 4670,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="11303@x" ObjectIDND1="11304@x" ObjectIDZND0="10970@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-487 4670,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3795260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-528 4670,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10970@1" ObjectIDZND0="11305@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-528 4670,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2ea9910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-608 4702,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="11305@x" ObjectIDND1="g_3002fd0@0" ObjectIDZND0="g_38271e0@0" Pin0InfoVect0LinkObjId="g_38271e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3002fd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-608 4702,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3836c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-586 4670,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="11305@1" ObjectIDZND0="g_3002fd0@0" ObjectIDZND1="g_38271e0@0" Pin0InfoVect0LinkObjId="g_3002fd0_0" Pin0InfoVect1LinkObjId="g_38271e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-586 4670,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_33b13a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-608 4670,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="11305@x" ObjectIDND1="g_38271e0@0" ObjectIDZND0="g_3002fd0@0" Pin0InfoVect0LinkObjId="g_3002fd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_38271e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-608 4670,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_30e8be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-752 4702,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDND1="g_349c610@0" ObjectIDZND0="g_2ffcc20@0" Pin0InfoVect0LinkObjId="g_2ffcc20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="g_349c610_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-752 4702,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3777ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-752 4670,-770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_349c610@0" ObjectIDND1="g_2ffcc20@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="EC-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_349c610_0" Pin1InfoVect1LinkObjId="g_2ffcc20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-752 4670,-770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3531cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-1089 4495,-1048 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3aade90@0" ObjectIDZND0="g_2e1cdb0@1" Pin0InfoVect0LinkObjId="g_2e1cdb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3aade90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-1089 4495,-1048 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2efc1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-1017 4495,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2e1cdb0@0" ObjectIDZND0="6544@x" ObjectIDZND1="6549@x" Pin0InfoVect0LinkObjId="SW-40404_0" Pin0InfoVect1LinkObjId="SW-40409_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e1cdb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-1017 4495,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_32cbf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-595 4091,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="6547@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40407_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-595 4091,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35a15c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-705 4091,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="6540@0" Pin0InfoVect0LinkObjId="SW-40400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-705 4091,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32ce6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-771 4091,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6540@1" ObjectIDZND0="6541@0" Pin0InfoVect0LinkObjId="SW-40401_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40400_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-771 4091,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eef200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-841 4091,-880 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6541@1" ObjectIDZND0="6538@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40401_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-841 4091,-880 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_34c63e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-490 4353,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="6555@x" ObjectIDND1="g_2ee79f0@0" ObjectIDND2="6556@x" ObjectIDZND0="g_2e13280@0" Pin0InfoVect0LinkObjId="g_2e13280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40415_0" Pin1InfoVect1LinkObjId="g_2ee79f0_0" Pin1InfoVect2LinkObjId="SW-40416_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-490 4353,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3257880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-540 4353,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2e13280@1" ObjectIDZND0="g_30171f0@0" Pin0InfoVect0LinkObjId="g_30171f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e13280_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-540 4353,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_374ed20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3773,-179 3773,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2e0ead0@0" ObjectIDZND0="g_34c9310@0" Pin0InfoVect0LinkObjId="g_34c9310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e0ead0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3773,-179 3773,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3259440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-288 3820,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="6557@x" ObjectIDND1="g_34c5110@0" ObjectIDND2="6560@x" ObjectIDZND0="g_2e47b80@1" Pin0InfoVect0LinkObjId="g_2e47b80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40417_0" Pin1InfoVect1LinkObjId="g_34c5110_0" Pin1InfoVect2LinkObjId="SW-40420_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-288 3820,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_355a2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-186 3820,-168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2e47b80@0" ObjectIDZND0="g_2ef6360@1" Pin0InfoVect0LinkObjId="g_2ef6360_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e47b80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-186 3820,-168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_38389c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-288 4077,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_33ce1b0@0" ObjectIDND1="6561@x" ObjectIDND2="6564@x" ObjectIDZND0="g_3656090@1" Pin0InfoVect0LinkObjId="g_3656090_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_33ce1b0_0" Pin1InfoVect1LinkObjId="SW-40421_0" Pin1InfoVect2LinkObjId="SW-40424_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-288 4077,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2ef45e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-186 4077,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3656090@0" ObjectIDZND0="g_379d5f0@1" Pin0InfoVect0LinkObjId="g_379d5f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3656090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-186 4077,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_382a4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4030,-183 4030,-154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_3043440@0" ObjectIDZND0="g_34e2920@0" Pin0InfoVect0LinkObjId="g_34e2920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3043440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4030,-183 4030,-154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_415ba00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4286,-187 4286,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2ef5150@0" ObjectIDZND0="g_37632f0@0" Pin0InfoVect0LinkObjId="g_37632f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ef5150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4286,-187 4286,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_4162d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4333,-288 4333,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="6565@x" ObjectIDND1="g_2e24a60@0" ObjectIDND2="6568@x" ObjectIDZND0="g_33183f0@1" Pin0InfoVect0LinkObjId="g_33183f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40425_0" Pin1InfoVect1LinkObjId="g_2e24a60_0" Pin1InfoVect2LinkObjId="SW-40428_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4333,-288 4333,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3c4d8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4333,-192 4333,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_33183f0@0" ObjectIDZND0="g_32e2e80@1" Pin0InfoVect0LinkObjId="g_32e2e80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33183f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4333,-192 4333,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2e35210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-752 4670,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDND1="g_2ffcc20@0" ObjectIDZND0="g_349c610@1" Pin0InfoVect0LinkObjId="g_349c610_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="g_2ffcc20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-752 4670,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_401dec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-690 4670,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_349c610@0" ObjectIDZND0="g_3002fd0@1" Pin0InfoVect0LinkObjId="g_3002fd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_349c610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-690 4670,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_30e6540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4383,-343 4369,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6566@x" ObjectIDND1="6565@x" ObjectIDZND0="6567@1" Pin0InfoVect0LinkObjId="SW-40427_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40426_0" Pin1InfoVect1LinkObjId="SW-40425_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4383,-343 4369,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2e49950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4333,-343 4319,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6567@0" ObjectIDZND0="g_2ffb380@0" Pin0InfoVect0LinkObjId="g_2ffb380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40427_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4333,-343 4319,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_30ed460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-343 4113,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6562@x" ObjectIDND1="6561@x" ObjectIDZND0="6563@1" Pin0InfoVect0LinkObjId="SW-40423_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40422_0" Pin1InfoVect1LinkObjId="SW-40421_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-343 4113,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_37a9ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-343 4063,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6563@0" ObjectIDZND0="g_2ffb070@0" Pin0InfoVect0LinkObjId="g_2ffb070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40423_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-343 4063,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2e21970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-343 3857,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6558@x" ObjectIDND1="6557@x" ObjectIDZND0="6559@1" Pin0InfoVect0LinkObjId="SW-40419_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40418_0" Pin1InfoVect1LinkObjId="SW-40417_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-343 3857,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3008020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3821,-343 3806,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6559@0" ObjectIDZND0="g_3705540@0" Pin0InfoVect0LinkObjId="g_3705540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40419_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3821,-343 3806,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2eeaf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4605,-293 4605,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3731ad0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3731ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4605,-293 4605,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ed1e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4605,-232 4605,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4605,-232 4605,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3b589d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-288 3773,-288 3773,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="6557@x" ObjectIDND1="g_34c5110@0" ObjectIDND2="g_2e47b80@0" ObjectIDZND0="6560@1" Pin0InfoVect0LinkObjId="SW-40420_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40417_0" Pin1InfoVect1LinkObjId="g_34c5110_0" Pin1InfoVect2LinkObjId="g_2e47b80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-288 3773,-288 3773,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_385f720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3773,-236 3773,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6560@0" ObjectIDZND0="g_2e0ead0@1" Pin0InfoVect0LinkObjId="g_2e0ead0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3773,-236 3773,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2e17030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-288 4030,-288 4030,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_33ce1b0@0" ObjectIDND1="6561@x" ObjectIDND2="g_3656090@0" ObjectIDZND0="6564@1" Pin0InfoVect0LinkObjId="SW-40424_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_33ce1b0_0" Pin1InfoVect1LinkObjId="SW-40421_0" Pin1InfoVect2LinkObjId="g_3656090_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-288 4030,-288 4030,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2e19f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4030,-235 4030,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6564@0" ObjectIDZND0="g_3043440@1" Pin0InfoVect0LinkObjId="g_3043440_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40424_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4030,-235 4030,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2ffb730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4333,-288 4286,-288 4286,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="6565@x" ObjectIDND1="g_2e24a60@0" ObjectIDND2="g_33183f0@0" ObjectIDZND0="6568@1" Pin0InfoVect0LinkObjId="SW-40428_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40425_0" Pin1InfoVect1LinkObjId="g_2e24a60_0" Pin1InfoVect2LinkObjId="g_33183f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4333,-288 4286,-288 4286,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2ec2ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4286,-236 4286,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6568@0" ObjectIDZND0="g_2ef5150@1" Pin0InfoVect0LinkObjId="g_2ef5150_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40428_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4286,-236 4286,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_34ab2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-528 4091,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6545@1" ObjectIDZND0="6547@0" Pin0InfoVect0LinkObjId="SW-40407_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40405_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-528 4091,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_33cb150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4624,-336 4605,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6554@0" ObjectIDZND0="g_3731ad0@0" ObjectIDZND1="6553@x" Pin0InfoVect0LinkObjId="g_3731ad0_0" Pin0InfoVect1LinkObjId="SW-40413_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40414_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4624,-336 4605,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3c051e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4605,-336 4605,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6553@x" ObjectIDND1="6554@x" ObjectIDZND0="g_3731ad0@1" Pin0InfoVect0LinkObjId="g_3731ad0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40413_0" Pin1InfoVect1LinkObjId="SW-40414_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4605,-336 4605,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3b28920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4605,-336 4605,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3731ad0@0" ObjectIDND1="6554@x" ObjectIDZND0="6553@0" Pin0InfoVect0LinkObjId="SW-40413_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3731ad0_0" Pin1InfoVect1LinkObjId="SW-40414_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4605,-336 4605,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3680810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4605,-394 4605,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6553@1" ObjectIDZND0="6539@0" Pin0InfoVect0LinkObjId="g_3acba40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40413_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4605,-394 4605,-413 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="6539" cx="3870" cy="-413" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6539" cx="4383" cy="-415" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6539" cx="4091" cy="-413" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6539" cx="4127" cy="-413" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6539" cx="3823" cy="-413" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6539" cx="4353" cy="-413" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6539" cx="4670" cy="-413" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6538" cx="4091" cy="-880" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6538" cx="4255" cy="-880" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6538" cx="3998" cy="-880" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6538" cx="4495" cy="-880" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6539" cx="4605" cy="-413" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" id="DYN-37319" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3414.000000 -1097.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5901" ObjectName="DYN-CX_HQ"/>
     <cge:Meas_Ref ObjectId="37319"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4263.000000 -1100.000000) translate(0,15)">花桥线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4466.000000 -1142.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4695.000000 -669.000000) translate(0,12)">近区变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4695.000000 -669.000000) translate(0,27)">400kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4695.000000 -669.000000) translate(0,42)">6/10kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4560.000000 -168.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3797.000000 -123.000000) translate(0,12)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3840.000000 -90.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3840.000000 -90.000000) translate(0,27)">800kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3765.000000 -120.000000) translate(0,12)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4056.000000 -126.000000) translate(0,12)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4097.000000 -90.000000) translate(0,12)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4097.000000 -90.000000) translate(0,27)">800kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4022.000000 -123.000000) translate(0,12)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4308.000000 -123.000000) translate(0,12)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4353.000000 -90.000000) translate(0,12)">3号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4353.000000 -90.000000) translate(0,27)">800kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4278.000000 -130.000000) translate(0,12)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4334.000000 -606.000000) translate(0,12)">电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4629.000000 -270.000000) translate(0,12)">#2站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4689.000000 -799.000000) translate(0,12)">10kV近区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3915.000000 -898.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3671.000000 -443.000000) translate(0,12)">6kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4262.000000 -934.000000) translate(0,12)">3116</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4273.000000 -1038.000000) translate(0,12)">31167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4502.000000 -964.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4514.000000 -943.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4515.000000 -1020.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4100.000000 -765.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4098.000000 -830.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4100.000000 -522.000000) translate(0,12)">601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4098.000000 -460.000000) translate(0,12)">6011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4098.000000 -584.000000) translate(0,12)">6016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4132.000000 -516.000000) translate(0,12)">60117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4360.000000 -462.000000) translate(0,12)">6903</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4368.000000 -516.000000) translate(0,12)">69037</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3830.000000 -460.000000) translate(0,12)">6901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3838.000000 -514.000000) translate(0,12)">69017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3879.000000 -319.000000) translate(0,12)">621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3877.000000 -385.000000) translate(0,12)">6211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3819.000000 -369.000000) translate(0,12)">62117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4136.000000 -319.000000) translate(0,12)">622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4134.000000 -385.000000) translate(0,12)">6221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4075.000000 -369.000000) translate(0,12)">62217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4392.000000 -319.000000) translate(0,12)">623</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4390.000000 -385.000000) translate(0,12)">6231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4331.000000 -369.000000) translate(0,12)">62317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4621.000000 -361.000000) translate(0,12)">69027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4014.000000 -925.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4007.000000 -673.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4224.000000 -1131.000000) translate(0,15)">至110kV舍资变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3139.000000 -578.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3131.000000 -1057.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3131.000000 -1057.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3131.000000 -1057.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3131.000000 -1057.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3131.000000 -1057.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3131.000000 -1057.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3131.000000 -1057.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3276.000000 -1168.500000) translate(0,16)">花桥水电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3935.000000 -1129.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3786.000000 -621.000000) translate(0,12)">6kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4627.000000 -464.000000) translate(0,12)">6411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4697.000000 -513.000000) translate(0,12)">64117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4634.000000 -521.000000) translate(0,12)">641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4625.000000 -577.000000) translate(0,12)">6416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3780.000000 -261.000000) translate(0,12)">6911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4037.000000 -260.000000) translate(0,12)">6921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4293.000000 -261.000000) translate(0,12)">6931</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4127.000000 -685.000000) translate(0,12)">S9-4000</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4127.000000 -685.000000) translate(0,27)">38.5±3×5%/6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4127.000000 -685.000000) translate(0,42)">Y,d11,Ud＝7.17%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4612.000000 -708.000000) translate(0,12)">0416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4626.000000 -248.000000) translate(0,12)">SC9-50/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4626.000000 -248.000000) translate(0,27)">6×5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4612.000000 -384.000000) translate(0,12)">6902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,238,0)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3257.000000 -223.000000) translate(0,16)">4813318</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-40400">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4082.000000 -736.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6540" ObjectName="SW-CX_HQ.CX_HQ_301BK"/>
     <cge:Meas_Ref ObjectId="40400"/>
    <cge:TPSR_Ref TObjectID="6540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40405">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4082.000000 -493.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6545" ObjectName="SW-CX_HQ.CX_HQ_601BK"/>
     <cge:Meas_Ref ObjectId="40405"/>
    <cge:TPSR_Ref TObjectID="6545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40417">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3861.000000 -290.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6557" ObjectName="SW-CX_HQ.CX_HQ_621BK"/>
     <cge:Meas_Ref ObjectId="40417"/>
    <cge:TPSR_Ref TObjectID="6557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40421">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4118.000000 -290.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6561" ObjectName="SW-CX_HQ.CX_HQ_622BK"/>
     <cge:Meas_Ref ObjectId="40421"/>
    <cge:TPSR_Ref TObjectID="6561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40425">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4374.000000 -290.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6565" ObjectName="SW-CX_HQ.CX_HQ_623BK"/>
     <cge:Meas_Ref ObjectId="40425"/>
    <cge:TPSR_Ref TObjectID="6565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(0.785714 -0.000000 0.000000 -1.000000 4663.428571 -493.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10970" ObjectName="SW-CX_HQ.CX_HQ_641BK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10970"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-770 4670,-799 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4670,-770 4670,-799 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2ef6360">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3808.000000 -130.000000)" xlink:href="#lightningRod:shape44"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_400a700">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4003.000000 -955.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_379d5f0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4064.000000 -131.000000)" xlink:href="#lightningRod:shape44"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32e2e80">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4321.000000 -132.000000)" xlink:href="#lightningRod:shape44"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3002fd0">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4682.000000 -672.000000)" xlink:href="#lightningRod:shape113"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e14a90">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4228.500000 -1004.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e1cdb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4486.000000 -1012.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30171f0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4310.000000 -550.000000)" xlink:href="#lightningRod:shape130"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e13280">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4344.000000 -504.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e0ead0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3764.000000 -174.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e47b80">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3811.000000 -181.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3656090">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4068.000000 -181.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3043440">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4021.000000 -178.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3557d70">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3876.000000 -172.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ee0bb0">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4135.000000 -172.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31fb7c0">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4391.000000 -172.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ef5150">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4277.000000 -182.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33183f0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4324.000000 -187.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3731ad0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4596.000000 -288.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_349c610">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4675.000000 -740.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34c5110">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3844.000000 -180.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33ce1b0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4102.000000 -180.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e24a60">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4358.000000 -183.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ee79f0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4262.000000 -484.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ffcc20">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4761.000000 -759.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38271e0">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4761.000000 -615.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c31a00">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3784.000000 -507.000000)" xlink:href="#lightningRod:shape162"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3aade90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4476.000000 -1087.000000)" xlink:href="#lightningRod:shape163"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40373" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4207.000000 -787.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40373" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6540"/>
     <cge:Term_Ref ObjectID="9268"/>
    <cge:TPSR_Ref TObjectID="6540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40374" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4207.000000 -787.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40374" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6540"/>
     <cge:Term_Ref ObjectID="9268"/>
    <cge:TPSR_Ref TObjectID="6540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40391" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4207.000000 -787.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40391" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6540"/>
     <cge:Term_Ref ObjectID="9268"/>
    <cge:TPSR_Ref TObjectID="6540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-40375" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4207.000000 -787.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40375" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6540"/>
     <cge:Term_Ref ObjectID="9268"/>
    <cge:TPSR_Ref TObjectID="6540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40388" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4195.000000 -562.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40388" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6545"/>
     <cge:Term_Ref ObjectID="9278"/>
    <cge:TPSR_Ref TObjectID="6545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40389" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4195.000000 -562.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40389" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6545"/>
     <cge:Term_Ref ObjectID="9278"/>
    <cge:TPSR_Ref TObjectID="6545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40376" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3850.000000 -51.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40376" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6557"/>
     <cge:Term_Ref ObjectID="9302"/>
    <cge:TPSR_Ref TObjectID="6557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40377" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3850.000000 -51.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40377" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6557"/>
     <cge:Term_Ref ObjectID="9302"/>
    <cge:TPSR_Ref TObjectID="6557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40378" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3850.000000 -51.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40378" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6557"/>
     <cge:Term_Ref ObjectID="9302"/>
    <cge:TPSR_Ref TObjectID="6557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40380" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4115.000000 -48.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40380" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6561"/>
     <cge:Term_Ref ObjectID="9310"/>
    <cge:TPSR_Ref TObjectID="6561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40381" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4115.000000 -48.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40381" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6561"/>
     <cge:Term_Ref ObjectID="9310"/>
    <cge:TPSR_Ref TObjectID="6561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40382" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4115.000000 -48.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40382" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6561"/>
     <cge:Term_Ref ObjectID="9310"/>
    <cge:TPSR_Ref TObjectID="6561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40384" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4360.000000 -44.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40384" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6565"/>
     <cge:Term_Ref ObjectID="9318"/>
    <cge:TPSR_Ref TObjectID="6565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40385" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4360.000000 -44.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40385" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6565"/>
     <cge:Term_Ref ObjectID="9318"/>
    <cge:TPSR_Ref TObjectID="6565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40386" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4360.000000 -44.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40386" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6565"/>
     <cge:Term_Ref ObjectID="9318"/>
    <cge:TPSR_Ref TObjectID="6565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-40396" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3732.000000 -399.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40396" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6539"/>
     <cge:Term_Ref ObjectID="9267"/>
    <cge:TPSR_Ref TObjectID="6539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-40397" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3732.000000 -399.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40397" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6539"/>
     <cge:Term_Ref ObjectID="9267"/>
    <cge:TPSR_Ref TObjectID="6539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-40398" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3732.000000 -399.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40398" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6539"/>
     <cge:Term_Ref ObjectID="9267"/>
    <cge:TPSR_Ref TObjectID="6539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-40399" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3732.000000 -399.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40399" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6539"/>
     <cge:Term_Ref ObjectID="9267"/>
    <cge:TPSR_Ref TObjectID="6539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-40393" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3790.000000 -912.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40393" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6538"/>
     <cge:Term_Ref ObjectID="9266"/>
    <cge:TPSR_Ref TObjectID="6538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-40394" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3790.000000 -912.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40394" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6538"/>
     <cge:Term_Ref ObjectID="9266"/>
    <cge:TPSR_Ref TObjectID="6538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-40395" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3790.000000 -912.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40395" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6538"/>
     <cge:Term_Ref ObjectID="9266"/>
    <cge:TPSR_Ref TObjectID="6538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-58363" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3790.000000 -912.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58363" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6538"/>
     <cge:Term_Ref ObjectID="9266"/>
    <cge:TPSR_Ref TObjectID="6538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-58365" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4762.000000 -566.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58365" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10970"/>
     <cge:Term_Ref ObjectID="15291"/>
    <cge:TPSR_Ref TObjectID="10970"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-58366" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4762.000000 -566.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58366" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10970"/>
     <cge:Term_Ref ObjectID="15291"/>
    <cge:TPSR_Ref TObjectID="10970"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40429" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4762.000000 -566.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40429" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10970"/>
     <cge:Term_Ref ObjectID="15291"/>
    <cge:TPSR_Ref TObjectID="10970"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="lf_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3247" y="-1179"/></g>
   <g href="lf_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3198" y="-1196"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4711.000000 565.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4700.000000 550.333333) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4720.000000 535.666667) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3699.000000 868.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3707.000000 883.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3707.000000 898.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3707.000000 913.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4150.000000 786.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4139.000000 771.333333) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4164.000000 756.666667) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4168.000000 742.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3644.000000 356.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3652.000000 369.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3652.000000 382.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3652.000000 395.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4299.000000 45.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4288.000000 30.333333) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4313.000000 15.666667) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4055.000000 48.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4044.000000 33.333333) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4069.000000 18.666667) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3788.000000 51.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3777.000000 36.333333) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3802.000000 21.666667) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4136.500000 562.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4148.500000 547.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4013.000000 -1009.000000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4013.000000 -1009.000000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4067.000000 -620.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4067.000000 -620.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4592.000000 -227.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4592.000000 -227.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259" style="fill-opacity:0">
    <a>
     
     <rect height="41" qtmmishow="hidden" width="138" x="3247" y="-1179"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3247" y="-1179"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124" style="fill-opacity:0">
    <a>
     
     <rect height="69" qtmmishow="hidden" width="77" x="3198" y="-1196"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3198" y="-1196"/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_34c9310">
    <use class="BV-6KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3763.500000 -156.500000)" xlink:href="#voltageTransformer:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34e2920">
    <use class="BV-6KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4020.500000 -159.500000)" xlink:href="#voltageTransformer:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37632f0">
    <use class="BV-6KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4276.500000 -166.500000)" xlink:href="#voltageTransformer:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3235.000000 -1120.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-78593" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 0.000000 0.000000 1.395515 3254.538462 -1015.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78593" ObjectName="CX_HQ:CX_HQ_sumP"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_HQ"/>
</svg>