<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-78" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3114 -1200 2048 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="18" y2="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="hydroGenerator:shape2">
    <polyline points="12,27 11,28 12,28 12,29 12,30 13,31 13,32 14,33 15,33 15,34 16,34 17,35 18,35 19,35 20,35 21,35 22,34 23,34 24,33 25,33 25,32 26,31 26,30 27,29 27,28 27,28 27,27 " stroke-width="0.06"/>
    <circle cx="27" cy="27" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <polyline arcFlag="1" points="28,27 28,26 28,25 28,24 29,23 29,22 30,21 30,21 31,20 32,20 33,19 34,19 35,19 36,18 37,19 38,19 39,19 40,20 40,20 41,21 42,21 42,22 43,23 43,24 43,25 44,26 43,27 " stroke-width="0.06"/>
   </symbol>
   <symbol id="lightningRod:shape116">
    <circle cx="8" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="14" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="18" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape44">
    <ellipse cx="11" cy="16" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="29" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape53">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="5" x2="5" y1="13" y2="4"/>
    <polyline arcFlag="1" points="5,13 6,13 6,13 7,13 8,13 8,14 9,14 10,15 10,15 10,16 11,17 11,17 11,18 11,19 11,20 11,20 10,21 10,22 10,22 9,23 8,23 8,24 7,24 6,24 6,24 5,24 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="5,24 6,24 6,24 7,24 8,24 8,25 9,25 10,26 10,26 10,27 11,28 11,28 11,29 11,30 11,31 11,31 10,32 10,33 10,33 9,34 8,34 8,35 7,35 6,35 6,35 5,35 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="5,35 6,35 6,35 7,35 8,35 8,36 9,36 10,37 10,37 10,38 11,39 11,39 11,40 11,41 11,42 11,42 10,43 10,44 10,44 9,45 8,45 8,46 7,46 6,46 6,46 5,46 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="5" x2="5" y1="55" y2="46"/>
   </symbol>
   <symbol id="lightningRod:shape74">
    <circle cx="39" cy="14" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="19" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="19" y2="19"/>
    <circle cx="30" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="30" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="71" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="31" y1="71" y2="71"/>
    <rect height="27" stroke-width="0.416667" width="14" x="24" y="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="82" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0587025" x1="31" x2="34" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173913" x1="30" x2="30" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.108974" x1="30" x2="27" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0587025" x1="31" x2="34" y1="9" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173913" x1="30" x2="30" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.108974" x1="30" x2="27" y1="8" y2="11"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,17 39,15 45,15 43,18 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape13_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="transformer:shape0_0">
    <circle cx="26" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="20" x2="20" y1="32" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="35" x2="20" y1="23" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="35" x2="20" y1="23" y2="32"/>
   </symbol>
   <symbol id="transformer:shape0_1">
    <circle cx="26" cy="61" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="25" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="33" x2="25" y1="73" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="18" y1="66" y2="73"/>
   </symbol>
   <symbol id="transformer:shape0-2">
    <circle cx="56" cy="45" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="62" x2="62" y1="37" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="70" x2="62" y1="53" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="62" x2="55" y1="46" y2="53"/>
   </symbol>
   <symbol id="transformer2:shape2_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape2_1">
    <circle cx="13" cy="18" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="18" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="9" y1="20" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="18" y1="20" y2="12"/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="13" y2="11"/>
    <circle cx="7" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="24" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="15" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="6" y2="4"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_23be810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23eb170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23ebb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_236fe60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2370eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2371990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23723b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2439880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_24370e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2438d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_250ff40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2510cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2511440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24171b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25132e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2513ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24125d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2414800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24151c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24783b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2415c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2440690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24412b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2689790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2442080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_23904c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2391aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1211" width="2058" x="3109" y="-1205"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3115" y="-599"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3115" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3115" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="338" lineStyle="1" stroke="rgb(255,255,255)" stroke-dasharray="10 5 " stroke-width="1" width="160" x="3808" y="-1107"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-47872">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3897.000000 -760.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8735" ObjectName="SW-CX_BGSJ.CX_BGSJ_1511SW"/>
     <cge:Meas_Ref ObjectId="47872"/>
    <cge:TPSR_Ref TObjectID="8735"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47847">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3897.000000 -681.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8710" ObjectName="SW-CX_BGSJ.CX_BGSJ_1011SW"/>
     <cge:Meas_Ref ObjectId="47847"/>
    <cge:TPSR_Ref TObjectID="8710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47848">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3897.000000 -562.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8711" ObjectName="SW-CX_BGSJ.CX_BGSJ_1016SW"/>
     <cge:Meas_Ref ObjectId="47848"/>
    <cge:TPSR_Ref TObjectID="8711"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47843">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3905.000000 -391.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8706" ObjectName="SW-CX_BGSJ.CX_BGSJ_6011XC"/>
     <cge:Meas_Ref ObjectId="47843"/>
    <cge:TPSR_Ref TObjectID="8706"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47829">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3663.000000 -324.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8698" ObjectName="SW-CX_BGSJ.CX_BGSJ_651XC"/>
     <cge:Meas_Ref ObjectId="47829"/>
    <cge:TPSR_Ref TObjectID="8698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47829">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3663.000000 -246.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8699" ObjectName="SW-CX_BGSJ.CX_BGSJ_651XC1"/>
     <cge:Meas_Ref ObjectId="47829"/>
    <cge:TPSR_Ref TObjectID="8699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3595.000000 -193.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3728.000000 -196.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3807.000000 -324.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3934.000000 -324.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3934.000000 -244.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47834">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4094.000000 -322.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8701" ObjectName="SW-CX_BGSJ.CX_BGSJ_652XC"/>
     <cge:Meas_Ref ObjectId="47834"/>
    <cge:TPSR_Ref TObjectID="8701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47834">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4094.000000 -244.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8702" ObjectName="SW-CX_BGSJ.CX_BGSJ_652XC1"/>
     <cge:Meas_Ref ObjectId="47834"/>
    <cge:TPSR_Ref TObjectID="8702"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4026.000000 -193.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4159.000000 -194.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3663.000000 -474.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3663.000000 -396.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47858">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4129.000000 -652.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8721" ObjectName="SW-CX_BGSJ.CX_BGSJ_1901SW"/>
     <cge:Meas_Ref ObjectId="47858"/>
    <cge:TPSR_Ref TObjectID="8721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47853">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4345.000000 -675.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8715" ObjectName="SW-CX_BGSJ.CX_BGSJ_1021SW"/>
     <cge:Meas_Ref ObjectId="47853"/>
    <cge:TPSR_Ref TObjectID="8715"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47854">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4345.000000 -556.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8716" ObjectName="SW-CX_BGSJ.CX_BGSJ_1026SW"/>
     <cge:Meas_Ref ObjectId="47854"/>
    <cge:TPSR_Ref TObjectID="8716"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47877">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4749.000000 -854.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8740" ObjectName="SW-CX_BGSJ.CX_BGSJ_351XC"/>
     <cge:Meas_Ref ObjectId="47877"/>
    <cge:TPSR_Ref TObjectID="8740"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47877">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4749.000000 -781.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8741" ObjectName="SW-CX_BGSJ.CX_BGSJ_351XC1"/>
     <cge:Meas_Ref ObjectId="47877"/>
    <cge:TPSR_Ref TObjectID="8741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47839">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4349.000000 -320.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8704" ObjectName="SW-CX_BGSJ.CX_BGSJ_653XC"/>
     <cge:Meas_Ref ObjectId="47839"/>
    <cge:TPSR_Ref TObjectID="8704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47839">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4349.000000 -242.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8705" ObjectName="SW-CX_BGSJ.CX_BGSJ_653XC1"/>
     <cge:Meas_Ref ObjectId="47839"/>
    <cge:TPSR_Ref TObjectID="8705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4281.000000 -191.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4414.000000 -192.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4527.000000 -320.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4692.000000 -320.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4692.000000 -240.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.000000 -723.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47873">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3897.000000 -886.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8736" ObjectName="SW-CX_BGSJ.CX_BGSJ_1516SW"/>
     <cge:Meas_Ref ObjectId="47873"/>
    <cge:TPSR_Ref TObjectID="8736"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47875">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3867.000000 -941.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8738" ObjectName="SW-CX_BGSJ.CX_BGSJ_15167SW"/>
     <cge:Meas_Ref ObjectId="47875"/>
    <cge:TPSR_Ref TObjectID="8738"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47874">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3867.000000 -808.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8737" ObjectName="SW-CX_BGSJ.CX_BGSJ_15117SW"/>
     <cge:Meas_Ref ObjectId="47874"/>
    <cge:TPSR_Ref TObjectID="8737"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47859">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4171.000000 -709.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8722" ObjectName="SW-CX_BGSJ.CX_BGSJ_19010SW"/>
     <cge:Meas_Ref ObjectId="47859"/>
    <cge:TPSR_Ref TObjectID="8722"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47860">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4173.000000 -628.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8723" ObjectName="SW-CX_BGSJ.CX_BGSJ_19017SW"/>
     <cge:Meas_Ref ObjectId="47860"/>
    <cge:TPSR_Ref TObjectID="8723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47849">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3940.000000 -605.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8712" ObjectName="SW-CX_BGSJ.CX_BGSJ_10167SW"/>
     <cge:Meas_Ref ObjectId="47849"/>
    <cge:TPSR_Ref TObjectID="8712"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47845">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3812.000000 -462.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8708" ObjectName="SW-CX_BGSJ.CX_BGSJ_1010SW"/>
     <cge:Meas_Ref ObjectId="47845"/>
    <cge:TPSR_Ref TObjectID="8708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47851">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4258.000000 -461.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8713" ObjectName="SW-CX_BGSJ.CX_BGSJ_1020SW"/>
     <cge:Meas_Ref ObjectId="47851"/>
    <cge:TPSR_Ref TObjectID="8713"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47855">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4387.000000 -599.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8717" ObjectName="SW-CX_BGSJ.CX_BGSJ_10267SW"/>
     <cge:Meas_Ref ObjectId="47855"/>
    <cge:TPSR_Ref TObjectID="8717"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47867">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4128.000000 -760.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8730" ObjectName="SW-CX_BGSJ.CX_BGSJ_1521SW"/>
     <cge:Meas_Ref ObjectId="47867"/>
    <cge:TPSR_Ref TObjectID="8730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47868">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4128.000000 -889.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8731" ObjectName="SW-CX_BGSJ.CX_BGSJ_1526SW"/>
     <cge:Meas_Ref ObjectId="47868"/>
    <cge:TPSR_Ref TObjectID="8731"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47870">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.000000 -943.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8733" ObjectName="SW-CX_BGSJ.CX_BGSJ_15267SW"/>
     <cge:Meas_Ref ObjectId="47870"/>
    <cge:TPSR_Ref TObjectID="8733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47869">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.000000 -811.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8732" ObjectName="SW-CX_BGSJ.CX_BGSJ_15217SW"/>
     <cge:Meas_Ref ObjectId="47869"/>
    <cge:TPSR_Ref TObjectID="8732"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47862">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4344.000000 -759.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8725" ObjectName="SW-CX_BGSJ.CX_BGSJ_1531SW"/>
     <cge:Meas_Ref ObjectId="47862"/>
    <cge:TPSR_Ref TObjectID="8725"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47863">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4344.000000 -886.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8726" ObjectName="SW-CX_BGSJ.CX_BGSJ_1536SW"/>
     <cge:Meas_Ref ObjectId="47863"/>
    <cge:TPSR_Ref TObjectID="8726"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47865">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4314.000000 -941.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8728" ObjectName="SW-CX_BGSJ.CX_BGSJ_15367SW"/>
     <cge:Meas_Ref ObjectId="47865"/>
    <cge:TPSR_Ref TObjectID="8728"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47864">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4314.000000 -808.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8727" ObjectName="SW-CX_BGSJ.CX_BGSJ_15317SW"/>
     <cge:Meas_Ref ObjectId="47864"/>
    <cge:TPSR_Ref TObjectID="8727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47857">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4749.000000 -654.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8719" ObjectName="SW-CX_BGSJ.CX_BGSJ_302XC"/>
     <cge:Meas_Ref ObjectId="47857"/>
    <cge:TPSR_Ref TObjectID="8719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47857">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4749.000000 -576.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8720" ObjectName="SW-CX_BGSJ.CX_BGSJ_302XC1"/>
     <cge:Meas_Ref ObjectId="47857"/>
    <cge:TPSR_Ref TObjectID="8720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5010.500000 -1067.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4904.000000 -1055.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4928.500000 -1067.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2a1e7c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4872.000000 -616.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3673,-647 3673,-717 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3673,-647 3673,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_BGSJ" endPointId="0" endStationName="CX_LHSE" flowDrawDirect="1" flowShape="0" id="AC-110kV.bue_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4360,-1138 4360,-1100 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11886" ObjectName="AC-110kV.bue_line"/>
    <cge:TPSR_Ref TObjectID="11886_SS-78"/></metadata>
   <polyline fill="none" opacity="0" points="4360,-1138 4360,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_NDH" endPointId="0" endStationName="CX_BGSJ" flowDrawDirect="1" flowShape="0" id="AC-110kV.nibu_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4142,-1095 4142,-1147 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11878" ObjectName="AC-110kV.nibu_line"/>
    <cge:TPSR_Ref TObjectID="11878_SS-78"/></metadata>
   <polyline fill="none" opacity="0" points="4142,-1095 4142,-1147 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_BGHSJ" endPointId="0" endStationName="CX_BGSJ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_buGuanHe4" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4759,-1011 4759,-959 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47298" ObjectName="AC-35kV.LN_buGuanHe4"/>
    <cge:TPSR_Ref TObjectID="47298_SS-78"/></metadata>
   <polyline fill="none" opacity="0" points="4759,-1011 4759,-959 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_22c9850" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3820.000000 -959.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dc38c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3820.000000 -826.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dc4cd0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4237.000000 -727.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_433bf60" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4239.000000 -646.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f11670" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4006.000000 -623.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34cdab0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3817.000000 -445.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ff07c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 -444.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ff16d0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4453.000000 -617.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41fe1d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4051.000000 -961.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e56860" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4051.000000 -829.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34dbcf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4267.000000 -959.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34d2b70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4267.000000 -826.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3dbbe90" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4912.500000 -1099.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_422b2d0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4994.500000 -1099.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_25e4660">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3591.000000 -113.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_42bc7f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3724.000000 -120.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_418fea0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3773.000000 -108.000000)" xlink:href="#lightningRod:shape44"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ef6370">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3834.000000 -214.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cee7c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4022.000000 -113.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_42bd0f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4155.000000 -118.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_42bdbb0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4204.000000 -113.000000)" xlink:href="#lightningRod:shape44"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c9bb70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3610.000000 -569.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_231e900">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4707.000000 -882.000000)" xlink:href="#lightningRod:shape44"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fa4b10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4277.000000 -111.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ca9450">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4410.000000 -116.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ca9f90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4459.000000 -104.000000)" xlink:href="#lightningRod:shape44"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_360bb80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4554.000000 -210.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40e3760">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3714.500000 -621.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4117340">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4528.500000 -497.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40c8f40">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3908.000000 -982.000000)" xlink:href="#lightningRod:shape53"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4130c90">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3884.000000 -1057.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4225cb0">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4175.000000 -536.000000)" xlink:href="#lightningRod:shape74"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fae680">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3941.000000 -578.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fafb50">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3849.500000 -518.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34ce4c0">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4295.500000 -517.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4193290">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4388.000000 -572.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4194b80">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3645.000000 -480.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e9e5b0">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3643.000000 -287.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e9f910">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3596.000000 -149.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ea0650">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3729.000000 -148.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ea1390">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4027.000000 -148.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26145c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4160.000000 -145.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2615300">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3777.500000 -276.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26160b0">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3929.000000 -285.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44855a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3839.000000 -249.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44862e0">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4088.000000 -284.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44877b0">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4344.000000 -282.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32bb360">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4282.000000 -147.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32bc0a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4415.000000 -145.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32bcde0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4497.500000 -272.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_43afaa0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4559.000000 -247.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_43b07e0">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4688.000000 -282.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_448bf20">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4138.000000 -984.000000)" xlink:href="#lightningRod:shape53"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f1b4d0">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4116.000000 -1057.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d66c0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4355.000000 -982.000000)" xlink:href="#lightningRod:shape53"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35cf080">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4331.000000 -1057.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34d41e0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4732.000000 -934.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33850d0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4849.500000 -706.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3385e80">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 4751.500000 -903.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d41830">
    <use class="BV-110KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3873.000000 -1067.000000)" xlink:href="#lightningRod:shape44"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d427c0">
    <use class="BV-110KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4104.000000 -1068.000000)" xlink:href="#lightningRod:shape44"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d43750">
    <use class="BV-110KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4321.000000 -1063.000000)" xlink:href="#lightningRod:shape44"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d446e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3615.000000 -601.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b57730">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4878.000000 -660.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3232.000000 -1116.013514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62767" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3242.000000 -1012.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62767" ObjectName="CX_BGSJ:CX_BGSJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-70651" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3239.000000 -971.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70651" ObjectName="CX_BGSJ:CX_BGSJ_sumQ"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47814" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4184.000000 -923.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47814" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8729"/>
     <cge:Term_Ref ObjectID="12314"/>
    <cge:TPSR_Ref TObjectID="8729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47815" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4184.000000 -923.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47815" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8729"/>
     <cge:Term_Ref ObjectID="12314"/>
    <cge:TPSR_Ref TObjectID="8729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47813" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4184.000000 -923.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47813" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8729"/>
     <cge:Term_Ref ObjectID="12314"/>
    <cge:TPSR_Ref TObjectID="8729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47824" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4403.000000 -923.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47824" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8724"/>
     <cge:Term_Ref ObjectID="12304"/>
    <cge:TPSR_Ref TObjectID="8724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47825" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4403.000000 -923.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47825" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8724"/>
     <cge:Term_Ref ObjectID="12304"/>
    <cge:TPSR_Ref TObjectID="8724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47823" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4403.000000 -923.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47823" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8724"/>
     <cge:Term_Ref ObjectID="12304"/>
    <cge:TPSR_Ref TObjectID="8724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47787" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3702.000000 -70.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47787" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8697"/>
     <cge:Term_Ref ObjectID="12250"/>
    <cge:TPSR_Ref TObjectID="8697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47788" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3702.000000 -70.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47788" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8697"/>
     <cge:Term_Ref ObjectID="12250"/>
    <cge:TPSR_Ref TObjectID="8697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47785" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3702.000000 -70.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47785" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8697"/>
     <cge:Term_Ref ObjectID="12250"/>
    <cge:TPSR_Ref TObjectID="8697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47792" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4124.000000 -70.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47792" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8700"/>
     <cge:Term_Ref ObjectID="12256"/>
    <cge:TPSR_Ref TObjectID="8700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47793" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4124.000000 -70.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47793" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8700"/>
     <cge:Term_Ref ObjectID="12256"/>
    <cge:TPSR_Ref TObjectID="8700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47790" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4124.000000 -70.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47790" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8700"/>
     <cge:Term_Ref ObjectID="12256"/>
    <cge:TPSR_Ref TObjectID="8700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47797" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4377.000000 -70.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47797" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8703"/>
     <cge:Term_Ref ObjectID="12262"/>
    <cge:TPSR_Ref TObjectID="8703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47798" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4377.000000 -70.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47798" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8703"/>
     <cge:Term_Ref ObjectID="12262"/>
    <cge:TPSR_Ref TObjectID="8703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47795" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4377.000000 -70.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47795" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8703"/>
     <cge:Term_Ref ObjectID="12262"/>
    <cge:TPSR_Ref TObjectID="8703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="1">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4681.000000 -650.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8718"/>
     <cge:Term_Ref ObjectID="12292"/>
    <cge:TPSR_Ref TObjectID="8718"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="1">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4681.000000 -650.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8718"/>
     <cge:Term_Ref ObjectID="12292"/>
    <cge:TPSR_Ref TObjectID="8718"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="1">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4681.000000 -650.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8718"/>
     <cge:Term_Ref ObjectID="12292"/>
    <cge:TPSR_Ref TObjectID="8718"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47819" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4873.000000 -857.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47819" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8739"/>
     <cge:Term_Ref ObjectID="12334"/>
    <cge:TPSR_Ref TObjectID="8739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47820" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4873.000000 -857.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47820" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8739"/>
     <cge:Term_Ref ObjectID="12334"/>
    <cge:TPSR_Ref TObjectID="8739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47818" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4873.000000 -857.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47818" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8739"/>
     <cge:Term_Ref ObjectID="12334"/>
    <cge:TPSR_Ref TObjectID="8739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-47889" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -414.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47889" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8694"/>
     <cge:Term_Ref ObjectID="12247"/>
    <cge:TPSR_Ref TObjectID="8694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-47802" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -414.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47802" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8694"/>
     <cge:Term_Ref ObjectID="12247"/>
    <cge:TPSR_Ref TObjectID="8694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-47803" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -414.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47803" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8694"/>
     <cge:Term_Ref ObjectID="12247"/>
    <cge:TPSR_Ref TObjectID="8694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-47891" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3728.000000 -811.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47891" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8696"/>
     <cge:Term_Ref ObjectID="12249"/>
    <cge:TPSR_Ref TObjectID="8696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-47806" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3728.000000 -811.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47806" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8696"/>
     <cge:Term_Ref ObjectID="12249"/>
    <cge:TPSR_Ref TObjectID="8696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-47807" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3728.000000 -811.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47807" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8696"/>
     <cge:Term_Ref ObjectID="12249"/>
    <cge:TPSR_Ref TObjectID="8696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-47890" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4671.000000 -811.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47890" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8695"/>
     <cge:Term_Ref ObjectID="12248"/>
    <cge:TPSR_Ref TObjectID="8695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-47804" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4671.000000 -811.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47804" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8695"/>
     <cge:Term_Ref ObjectID="12248"/>
    <cge:TPSR_Ref TObjectID="8695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-47805" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4671.000000 -811.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47805" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8695"/>
     <cge:Term_Ref ObjectID="12248"/>
    <cge:TPSR_Ref TObjectID="8695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-47888" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3583.000000 -437.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47888" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8693"/>
     <cge:Term_Ref ObjectID="12246"/>
    <cge:TPSR_Ref TObjectID="8693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-47800" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3583.000000 -437.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47800" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8693"/>
     <cge:Term_Ref ObjectID="12246"/>
    <cge:TPSR_Ref TObjectID="8693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-47801" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3583.000000 -437.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47801" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8693"/>
     <cge:Term_Ref ObjectID="12246"/>
    <cge:TPSR_Ref TObjectID="8693"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3244" y="-1175"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3244" y="-1175"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3195" y="-1192"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3195" y="-1192"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_水电.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3244" y="-1175"/></g>
   <g href="cx_索引_接线图_地调直调_水电.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3195" y="-1192"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-47846">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3903.000000 -633.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8709" ObjectName="SW-CX_BGSJ.CX_BGSJ_101BK"/>
     <cge:Meas_Ref ObjectId="47846"/>
    <cge:TPSR_Ref TObjectID="8709"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47871">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3903.000000 -850.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8734" ObjectName="SW-CX_BGSJ.CX_BGSJ_151BK"/>
     <cge:Meas_Ref ObjectId="47871"/>
    <cge:TPSR_Ref TObjectID="8734"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47828">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3664.000000 -280.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8697" ObjectName="SW-CX_BGSJ.CX_BGSJ_651BK"/>
     <cge:Meas_Ref ObjectId="47828"/>
    <cge:TPSR_Ref TObjectID="8697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47880">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3935.000000 -280.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8745" ObjectName="SW-CX_BGSJ.CX_BGSJ_654BK"/>
     <cge:Meas_Ref ObjectId="47880"/>
    <cge:TPSR_Ref TObjectID="8745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47833">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4095.000000 -278.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8700" ObjectName="SW-CX_BGSJ.CX_BGSJ_652BK"/>
     <cge:Meas_Ref ObjectId="47833"/>
    <cge:TPSR_Ref TObjectID="8700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47852">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4351.000000 -627.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8714" ObjectName="SW-CX_BGSJ.CX_BGSJ_102BK"/>
     <cge:Meas_Ref ObjectId="47852"/>
    <cge:TPSR_Ref TObjectID="8714"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47876">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4750.000000 -811.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8739" ObjectName="SW-CX_BGSJ.CX_BGSJ_351BK"/>
     <cge:Meas_Ref ObjectId="47876"/>
    <cge:TPSR_Ref TObjectID="8739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47838">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4350.000000 -276.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8703" ObjectName="SW-CX_BGSJ.CX_BGSJ_653BK"/>
     <cge:Meas_Ref ObjectId="47838"/>
    <cge:TPSR_Ref TObjectID="8703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47881">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4693.000000 -276.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8746" ObjectName="SW-CX_BGSJ.CX_BGSJ_655BK"/>
     <cge:Meas_Ref ObjectId="47881"/>
    <cge:TPSR_Ref TObjectID="8746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47882">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3664.000000 -427.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8747" ObjectName="SW-CX_BGSJ.CX_BGSJ_656BK"/>
     <cge:Meas_Ref ObjectId="47882"/>
    <cge:TPSR_Ref TObjectID="8747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47866">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4134.000000 -851.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8729" ObjectName="SW-CX_BGSJ.CX_BGSJ_152BK"/>
     <cge:Meas_Ref ObjectId="47866"/>
    <cge:TPSR_Ref TObjectID="8729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47861">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4350.000000 -850.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8724" ObjectName="SW-CX_BGSJ.CX_BGSJ_153BK"/>
     <cge:Meas_Ref ObjectId="47861"/>
    <cge:TPSR_Ref TObjectID="8724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47856">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4750.000000 -607.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8718" ObjectName="SW-CX_BGSJ.CX_BGSJ_302BK"/>
     <cge:Meas_Ref ObjectId="47856"/>
    <cge:TPSR_Ref TObjectID="8718"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5047.000000 -1031.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_BGSJ.CX_BGSJ_6IM">
    <g class="BV-6KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3629,-368 4151,-368 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="8693" ObjectName="BS-CX_BGSJ.CX_BGSJ_6IM"/>
    <cge:TPSR_Ref TObjectID="8693"/></metadata>
   <polyline fill="none" opacity="0" points="3629,-368 4151,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_BGSJ.CX_BGSJ_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3693,-761 4430,-761 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="8696" ObjectName="BS-CX_BGSJ.CX_BGSJ_1IM"/>
    <cge:TPSR_Ref TObjectID="8696"/></metadata>
   <polyline fill="none" opacity="0" points="3693,-761 4430,-761 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_BGSJ.CX_BGSJ_6IIM">
    <g class="BV-6KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4291,-367 4765,-367 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="8694" ObjectName="BS-CX_BGSJ.CX_BGSJ_6IIM"/>
    <cge:TPSR_Ref TObjectID="8694"/></metadata>
   <polyline fill="none" opacity="0" points="4291,-367 4765,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_BGSJ.CX_BGSJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4644,-761 5008,-761 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="8695" ObjectName="BS-CX_BGSJ.CX_BGSJ_3IM"/>
    <cge:TPSR_Ref TObjectID="8695"/></metadata>
   <polyline fill="none" opacity="0" points="4644,-761 5008,-761 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_BGSJ.CX_BGH3_GN1">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3645.000000 -105.000000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15885" ObjectName="SM-CX_BGSJ.CX_BGH3_GN1"/>
    <cge:TPSR_Ref TObjectID="15885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_BGSJ.CX_BGH3_GN2">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4076.000000 -103.000000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15886" ObjectName="SM-CX_BGSJ.CX_BGH3_GN2"/>
    <cge:TPSR_Ref TObjectID="15886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_BGSJ.CX_BGH3_GN3">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4331.000000 -101.000000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15887" ObjectName="SM-CX_BGSJ.CX_BGH3_GN3"/>
    <cge:TPSR_Ref TObjectID="15887"/></metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="8696" cx="3912" cy="-761" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8696" cx="3912" cy="-761" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8693" cx="3673" cy="-368" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8693" cx="3817" cy="-368" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8693" cx="3944" cy="-368" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8693" cx="4104" cy="-368" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8693" cx="3673" cy="-368" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8696" cx="4360" cy="-761" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8694" cx="4359" cy="-367" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8695" cx="4759" cy="-761" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8694" cx="4359" cy="-367" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8694" cx="4537" cy="-367" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8694" cx="4702" cy="-367" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8695" cx="4887" cy="-761" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8693" cx="3912" cy="-368" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8696" cx="4144" cy="-761" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8696" cx="4143" cy="-761" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8696" cx="4359" cy="-761" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8695" cx="4759" cy="-761" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c4d380" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3587.000000 -110.000000) translate(0,12)">1YH1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3598ad0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3631.000000 -103.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3de7610" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4107.000000 -1173.000000) translate(0,12)">至泥堵河电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_400a210" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4163.000000 -1147.000000) translate(0,12)">泥</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_400a210" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4163.000000 -1147.000000) translate(0,27)">不</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_400a210" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4163.000000 -1147.000000) translate(0,42)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f9990" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4297.000000 -1173.000000) translate(0,12)">至老虎山二级电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43ab020" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4379.000000 -1147.000000) translate(0,12)">不</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43ab020" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4379.000000 -1147.000000) translate(0,27)">鄂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43ab020" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4379.000000 -1147.000000) translate(0,42)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42be680" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3720.000000 -112.000000) translate(0,12)">1YH2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43ad390" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3765.000000 -107.000000) translate(0,12)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43ad5a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3776.000000 -212.000000) translate(0,12)">6kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43ad7f0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3862.000000 -228.000000) translate(0,12)">1号厂用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43ada20" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3910.000000 -92.000000) translate(0,12)">至厂用I段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43adc60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4022.000000 -110.000000) translate(0,12)">2YH1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43adea0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4069.000000 -94.000000) translate(0,12)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43ae0e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4155.000000 -112.000000) translate(0,12)">2YH2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43ae320" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4197.000000 -112.000000) translate(0,12)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c9c6b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3639.000000 -739.000000) translate(0,12)">10kV近区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c9cfb0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3606.000000 -544.000000) translate(0,12)">近区变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_266b7c0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5147.000000 -1096.000000) translate(0,12)">不</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_266b7c0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5147.000000 -1096.000000) translate(0,27)">管</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_266b7c0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5147.000000 -1096.000000) translate(0,42)">河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_266b7c0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5147.000000 -1096.000000) translate(0,57)">四</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_266b7c0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5147.000000 -1096.000000) translate(0,72)">级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_266b7c0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5147.000000 -1096.000000) translate(0,87)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_266b7c0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5147.000000 -1096.000000) translate(0,102)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_266c410" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4273.000000 -106.000000) translate(0,12)">3YH1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e8f180" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4317.000000 -99.000000) translate(0,12)">3号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4338980" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4406.000000 -108.000000) translate(0,12)">3YH2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4338fb0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4457.000000 -105.000000) translate(0,12)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43391f0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4496.000000 -205.000000) translate(0,12)">6kV II段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4339440" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4620.000000 -224.000000) translate(0,12)">2号厂用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4339670" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4668.000000 -108.000000) translate(0,12)">至厂用II段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40e31d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4859.000000 -606.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b58470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 -880.500000) translate(0,12)">151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b58aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3919.000000 -807.000000) translate(0,12)">1511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b58ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3857.000000 -860.000000) translate(0,12)">15117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b58f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3919.000000 -933.000000) translate(0,12)">1516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b59160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3857.000000 -993.000000) translate(0,12)">15167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b593a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4152.000000 -880.500000) translate(0,12)">152</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b595e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4150.000000 -807.000000) translate(0,12)">1521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b59820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4088.000000 -863.000000) translate(0,12)">15217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b59a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4150.000000 -936.000000) translate(0,12)">1526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b59ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4087.000000 -995.000000) translate(0,12)">15267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b59ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4368.000000 -880.500000) translate(0,12)">153</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b5a120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4366.000000 -806.000000) translate(0,12)">1531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b5a360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4304.000000 -860.000000) translate(0,12)">15317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b5a5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4366.000000 -933.000000) translate(0,12)">1536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2317ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4303.000000 -993.000000) translate(0,12)">15367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2318100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 -662.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2318340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3919.000000 -728.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2318580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3919.000000 -609.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23187c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3957.000000 -659.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2318a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3780.000000 -513.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2318c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4151.000000 -699.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2318e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4160.000000 -761.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23190c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4162.000000 -680.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2319300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4369.000000 -656.000000) translate(0,12)">102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2319540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4367.000000 -722.000000) translate(0,12)">1021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2319780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4367.000000 -603.000000) translate(0,12)">1026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23199c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4406.000000 -653.000000) translate(0,12)">10267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2319c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4220.000000 -514.000000) translate(0,12)">1020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2319e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3693.000000 -760.000000) translate(0,12)">110kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_231a080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3545.000000 -386.000000) translate(0,12)">6kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_231a2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4225.000000 -386.000000) translate(0,12)">6kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_231a500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3682.000000 -456.000000) translate(0,12)">656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_231a740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3682.000000 -309.000000) translate(0,12)">651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_231a980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3953.000000 -309.000000) translate(0,12)">654</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_231abc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -307.000000) translate(0,12)">652</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_231ae00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4368.000000 -305.000000) translate(0,12)">653</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_231b040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4711.000000 -305.000000) translate(0,12)">655</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_231b280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4773.000000 -636.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_231b4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4768.000000 -840.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3de4540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4645.000000 -752.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3de4780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.000000 -518.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3de49c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4414.000000 -495.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -578.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -578.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -578.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -578.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -578.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -578.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -578.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -578.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -578.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -578.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -578.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -578.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -578.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -578.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -578.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -578.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -578.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -578.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5af20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3128.000000 -1057.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5af20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3128.000000 -1057.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5af20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3128.000000 -1057.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5af20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3128.000000 -1057.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5af20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3128.000000 -1057.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5af20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3128.000000 -1057.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3e5af20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3128.000000 -1057.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_3e5b740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3270.000000 -1164.500000) translate(0,16)">不管河三级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5c390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 -483.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5c390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 -483.000000) translate(0,27)">SF9-16000/121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5c390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 -483.000000) translate(0,42)">YNd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5c390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 -483.000000) translate(0,57)">121±2.5%/6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5c6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4455.000000 -588.000000) translate(0,12)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5c6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4455.000000 -588.000000) translate(0,27)">SF9-16000/121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5c6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4455.000000 -588.000000) translate(0,42)">YNyn0d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5c6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4455.000000 -588.000000) translate(0,57)">121±2×2.5%/38.5±2×2.5%/6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5c9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3486.000000 -90.000000) translate(0,12)">1号发电机参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5c9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3486.000000 -90.000000) translate(0,27)">SFW6300-8/2150</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5c9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3486.000000 -90.000000) translate(0,42)">Pe=6.3MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5c9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3486.000000 -90.000000) translate(0,57)">Ue=6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5c9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3486.000000 -90.000000) translate(0,72)">Ie=722A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5c9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3486.000000 -90.000000) translate(0,87)">CoS∮=0.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5cba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.000000 -90.000000) translate(0,12)">2号发电机参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5cba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.000000 -90.000000) translate(0,27)">SFW6300-8/2150</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5cba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.000000 -90.000000) translate(0,42)">Pe=6.3MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5cba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.000000 -90.000000) translate(0,57)">Ue=6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5cba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.000000 -90.000000) translate(0,72)">Ie=722A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5cba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.000000 -90.000000) translate(0,87)">CoS∮=0.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5ce10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4430.000000 -89.000000) translate(0,12)">3号发电机参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5ce10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4430.000000 -89.000000) translate(0,27)">SFW6300-8/2150</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5ce10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4430.000000 -89.000000) translate(0,42)">Pe=6.3MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5ce10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4430.000000 -89.000000) translate(0,57)">Ue=6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5ce10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4430.000000 -89.000000) translate(0,72)">Ie=722A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5ce10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4430.000000 -89.000000) translate(0,87)">CoS∮=0.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3fd0400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3935.000000 -1060.000000) translate(0,16)">空</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3fd0400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3935.000000 -1060.000000) translate(0,36)">间</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3fd0400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3935.000000 -1060.000000) translate(0,56)">隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dbbc20" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4765.000000 -1005.000000) translate(0,12)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dbbc20" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4765.000000 -1005.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dbbc20" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4765.000000 -1005.000000) translate(0,42)">不</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dbbc20" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4765.000000 -1005.000000) translate(0,57)">管</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dbbc20" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4765.000000 -1005.000000) translate(0,72)">河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dbbc20" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4765.000000 -1005.000000) translate(0,87)">四</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dbbc20" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4765.000000 -1005.000000) translate(0,102)">级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dbbc20" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4765.000000 -1005.000000) translate(0,117)">线</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4019500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4050.000000 923.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4019780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4039.000000 908.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4019980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4064.000000 893.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4019da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3636.000000 70.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_401a060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3625.000000 55.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_401a2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3650.000000 40.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_401b7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4065.000000 70.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_401ba70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4054.000000 55.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_401bcb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4079.000000 40.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3de2110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4318.000000 70.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3de23d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4307.000000 55.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3de2610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4332.000000 40.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3de3510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4622.000000 650.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3de37a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4611.000000 635.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3de39e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4636.000000 620.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3de3e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4815.000000 857.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3de40c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4804.000000 842.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3de4300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4829.000000 827.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd11f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3639.000000 796.500000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd1640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 812.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd1880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3653.000000 781.000000) translate(0,12)">F（Hz）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd1bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3495.000000 423.500000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd1e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3502.000000 439.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd2050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3509.000000 408.000000) translate(0,12)">F（Hz）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd2860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4634.000000 400.500000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd2a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4641.000000 416.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fd2ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4648.000000 385.000000) translate(0,12)">F（Hz）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dbace0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4578.000000 797.500000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dbaf40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4585.000000 813.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dbb180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4592.000000 782.000000) translate(0,12)">F（Hz）：</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3660.000000 -509.000000)" xlink:href="#transformer2:shape2_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3660.000000 -509.000000)" xlink:href="#transformer2:shape2_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3929.000000 -144.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3929.000000 -144.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_BGSJ.CX_BGSJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="12354"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3887.000000 -469.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3887.000000 -469.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="8748" ObjectName="TF-CX_BGSJ.CX_BGSJ_1T"/>
    <cge:TPSR_Ref TObjectID="8748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4687.000000 -136.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4687.000000 -136.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5101.000000 -1027.000000)" xlink:href="#transformer2:shape2_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5101.000000 -1027.000000)" xlink:href="#transformer2:shape2_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-47766" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3416.000000 -1083.500000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8675" ObjectName="DYN-CX_BGSJ"/>
     <cge:Meas_Ref ObjectId="47766"/>
    </metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_BGSJ.CX_BGSJ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="12357"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4334.000000 -465.000000)" xlink:href="#transformer:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="12359"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4334.000000 -465.000000)" xlink:href="#transformer:shape0_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="12361"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4334.000000 -465.000000)" xlink:href="#transformer:shape0-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="8749" ObjectName="TF-CX_BGSJ.CX_BGSJ_2T"/>
    <cge:TPSR_Ref TObjectID="8749"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_25e6670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-761 3912,-781 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8696@0" ObjectIDZND0="8735@0" Pin0InfoVect0LinkObjId="SW-47872_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40dbc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-761 3912,-781 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35971d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-761 3912,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8696@0" ObjectIDZND0="8710@1" Pin0InfoVect0LinkObjId="SW-47847_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40dbc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-761 3912,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35fb760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-703 3912,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8710@0" ObjectIDZND0="8709@1" Pin0InfoVect0LinkObjId="SW-47846_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47847_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-703 3912,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_43af2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-348 3673,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8698@0" ObjectIDZND0="8693@0" Pin0InfoVect0LinkObjId="g_42be420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47829_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-348 3673,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_43af510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-315 3673,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8697@1" ObjectIDZND0="8698@1" Pin0InfoVect0LinkObjId="SW-47829_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47828_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-315 3673,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_43af770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-239 3673,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="15885@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="8699@1" Pin0InfoVect0LinkObjId="SW-47829_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_BGSJ.CX_BGH3_GN1_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-239 3673,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_40fd210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-158 3673,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="15885@0" ObjectIDZND0="8699@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-47829_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_BGSJ.CX_BGH3_GN1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-158 3673,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_40fd470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-194 3785,-194 3785,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_3ea0650@0" ObjectIDZND0="g_418fea0@1" Pin0InfoVect0LinkObjId="g_418fea0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3ea0650_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-194 3785,-194 3785,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25e4400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-239 3605,-239 3605,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="15885@x" ObjectIDND1="8699@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_BGSJ.CX_BGH3_GN1_0" Pin1InfoVect1LinkObjId="SW-47829_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-239 3605,-239 3605,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_42bc590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-220 3738,-239 3673,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="hydroGenerator" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="15885@x" ObjectIDZND1="8699@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SM-CX_BGSJ.CX_BGH3_GN1_0" Pin0InfoVect1LinkObjId="SW-47829_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-220 3738,-239 3673,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_418fc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-194 3738,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_418fea0@0" ObjectIDND1="g_3ea0650@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_418fea0_0" Pin1InfoVect1LinkObjId="g_3ea0650_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-194 3738,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3ef5c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3817,-368 3817,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8693@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_43af2b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3817,-368 3817,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ef5eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3784,-272 3784,-301 3817,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2615300@0" ObjectIDZND0="0@x" ObjectIDZND1="g_44855a0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_44855a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2615300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3784,-272 3784,-301 3817,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ef6110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3817,-301 3817,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2615300@0" ObjectIDND1="g_44855a0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2615300_0" Pin1InfoVect1LinkObjId="g_44855a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3817,-301 3817,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3e531a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-368 3944,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8693@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_43af2b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-368 3944,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_29e7b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-331 3944,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="8745@1" Pin0InfoVect0LinkObjId="SW-47880_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-331 3944,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3631b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-250 3944,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-250 3944,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_4455580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-313 4104,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8700@1" ObjectIDZND0="8701@1" Pin0InfoVect0LinkObjId="SW-47834_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47833_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-313 4104,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_44557e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-237 4104,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="15886@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="8702@1" Pin0InfoVect0LinkObjId="SW-47834_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_BGSJ.CX_BGH3_GN2_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-237 4104,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_4455a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-156 4104,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="15886@0" ObjectIDZND0="8702@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-47834_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_BGSJ.CX_BGH3_GN2_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-156 4104,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4455ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-192 4216,-192 4216,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_26145c0@0" ObjectIDZND0="g_42bdbb0@1" Pin0InfoVect0LinkObjId="g_42bdbb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_26145c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-192 4216,-192 4216,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3cee560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-237 4036,-237 4036,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="15886@x" ObjectIDND1="8702@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_BGSJ.CX_BGH3_GN2_0" Pin1InfoVect1LinkObjId="SW-47834_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-237 4036,-237 4036,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_42bce90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-218 4169,-237 4104,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="hydroGenerator" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="15886@x" ObjectIDZND1="8702@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SM-CX_BGSJ.CX_BGH3_GN2_0" Pin0InfoVect1LinkObjId="SW-47834_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-218 4169,-237 4104,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_42bd950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-192 4169,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_42bdbb0@0" ObjectIDND1="g_26145c0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_42bdbb0_0" Pin1InfoVect1LinkObjId="g_26145c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-192 4169,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_42be420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-346 4104,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8701@0" ObjectIDZND0="8693@0" Pin0InfoVect0LinkObjId="g_43af2b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47834_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-346 4104,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3283400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-368 3673,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8693@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_43af2b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-368 3673,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c9b6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-498 3673,-514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-498 3673,-514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c9c450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-648 3721,-648 3721,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="transformer2" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDND1="0@x" ObjectIDND2="g_3d446e0@0" ObjectIDZND0="g_40e3760@0" Pin0InfoVect0LinkObjId="g_40e3760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_3d446e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-648 3721,-648 3721,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c9d230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-556 3673,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_40e3760@0" ObjectIDZND1="0@1" ObjectIDZND2="g_3d446e0@0" Pin0InfoVect0LinkObjId="g_40e3760_0" Pin0InfoVect1LinkObjId="SW-0_1" Pin0InfoVect2LinkObjId="g_3d446e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-556 3673,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3517c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-761 4360,-733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8696@0" ObjectIDZND0="8715@1" Pin0InfoVect0LinkObjId="SW-47853_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40dbc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-761 4360,-733 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2671d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-697 4360,-662 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8715@0" ObjectIDZND0="8714@1" Pin0InfoVect0LinkObjId="SW-47852_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47853_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-697 4360,-662 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3ef6b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-470 4359,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="busSection" ObjectIDND0="8749@0" ObjectIDZND0="8694@0" Pin0InfoVect0LinkObjId="g_3e7f3f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ef6db0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-470 4359,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ef6db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4535,-493 4535,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="g_4117340@0" ObjectIDZND0="8749@x" ObjectIDZND1="8720@x" Pin0InfoVect0LinkObjId="g_4194920_0" Pin0InfoVect1LinkObjId="SW-47857_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4117340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4535,-493 4535,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ef7010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4414,-511 4535,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="8749@2" ObjectIDZND0="g_4117340@0" ObjectIDZND1="8720@x" Pin0InfoVect0LinkObjId="g_4117340_0" Pin0InfoVect1LinkObjId="SW-47857_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ef6db0_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4414,-511 4535,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_231df80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4759,-787 4759,-761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8741@1" ObjectIDZND0="8695@0" Pin0InfoVect0LinkObjId="g_3d41600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47877_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4759,-787 4759,-761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_231e1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4759,-819 4759,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8739@0" ObjectIDZND0="8741@0" Pin0InfoVect0LinkObjId="SW-47877_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47876_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4759,-819 4759,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_231e440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4759,-861 4759,-846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8740@1" ObjectIDZND0="8739@1" Pin0InfoVect0LinkObjId="SW-47876_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47877_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4759,-861 4759,-846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_231e6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4759,-894 4759,-878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_3385e80@0" ObjectIDND1="g_34d41e0@0" ObjectIDND2="47298@1" ObjectIDZND0="8740@0" Pin0InfoVect0LinkObjId="SW-47877_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3385e80_0" Pin1InfoVect1LinkObjId="g_34d41e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4759,-894 4759,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_266b560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4902,-1040 4757,-1040 4757,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4902,-1040 4757,-1040 4757,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3ef9ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-311 4359,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8703@1" ObjectIDZND0="8704@1" Pin0InfoVect0LinkObjId="SW-47839_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47838_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-311 4359,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3efa140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-235 4359,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="15887@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="8705@1" Pin0InfoVect0LinkObjId="SW-47839_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_BGSJ.CX_BGH3_GN3_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-235 4359,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3efa3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-154 4359,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="15887@0" ObjectIDZND0="8705@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-47839_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_BGSJ.CX_BGH3_GN3_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-154 4359,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3efa600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4424,-190 4471,-190 4471,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_32bc0a0@0" ObjectIDZND0="g_3ca9f90@1" Pin0InfoVect0LinkObjId="g_3ca9f90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_32bc0a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4424,-190 4471,-190 4471,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3fa48b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-235 4291,-235 4291,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="15887@x" ObjectIDND1="8705@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_BGSJ.CX_BGH3_GN3_0" Pin1InfoVect1LinkObjId="SW-47839_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-235 4291,-235 4291,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3ca91f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4424,-216 4424,-235 4359,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="hydroGenerator" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="15887@x" ObjectIDZND1="8705@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SM-CX_BGSJ.CX_BGH3_GN3_0" Pin0InfoVect1LinkObjId="SW-47839_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4424,-216 4424,-235 4359,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ca9d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4424,-190 4424,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_3ca9f90@0" ObjectIDND1="g_32bc0a0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3ca9f90_0" Pin1InfoVect1LinkObjId="g_32bc0a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4424,-190 4424,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_360b6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4504,-268 4504,-297 4537,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_32bcde0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_43afaa0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_43afaa0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32bcde0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4504,-268 4504,-297 4537,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_360b920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-297 4537,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_32bcde0@0" ObjectIDND1="g_43afaa0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32bcde0_0" Pin1InfoVect1LinkObjId="g_43afaa0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-297 4537,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2a769e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4702,-327 4702,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="8746@1" Pin0InfoVect0LinkObjId="SW-47881_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4702,-327 4702,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3e7f3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-344 4359,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8704@0" ObjectIDZND0="8694@0" Pin0InfoVect0LinkObjId="g_3ef6b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47839_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-344 4359,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3e7f5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-344 4537,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="8694@0" Pin0InfoVect0LinkObjId="g_3ef6b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-344 4537,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3e7f7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4702,-344 4702,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="8694@0" Pin0InfoVect0LinkObjId="g_3ef6b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4702,-344 4702,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a1e0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4887,-761 4887,-747 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8695@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_231df80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4887,-761 4887,-747 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a1e300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4887,-719 4856,-719 4856,-702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2b57730@0" ObjectIDZND0="g_33850d0@0" Pin0InfoVect0LinkObjId="g_33850d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b57730_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4887,-719 4856,-719 4856,-702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a1e560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4887,-730 4887,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_33850d0@0" ObjectIDZND1="g_2b57730@0" Pin0InfoVect0LinkObjId="g_33850d0_0" Pin0InfoVect1LinkObjId="g_2b57730_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4887,-730 4887,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_42a4370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-420 3673,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="8747@0" Pin0InfoVect0LinkObjId="SW-47882_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-420 3673,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_42a57f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-385 3912,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8706@1" ObjectIDZND0="8693@0" Pin0InfoVect0LinkObjId="g_43af2b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47843_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-385 3912,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_42a5a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-474 3912,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="8748@0" ObjectIDZND0="8706@0" Pin0InfoVect0LinkObjId="SW-47843_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3faf8f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-474 3912,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4130a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3879,-1050 3912,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_4130c90@0" ObjectIDZND0="g_40c8f40@0" ObjectIDZND1="g_3d41830@0" Pin0InfoVect0LinkObjId="g_40c8f40_0" Pin0InfoVect1LinkObjId="g_3d41830_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4130c90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3879,-1050 3912,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4131a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3913,-1050 3913,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_4130c90@0" ObjectIDND1="g_3d41830@0" ObjectIDZND0="g_40c8f40@0" Pin0InfoVect0LinkObjId="g_40c8f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4130c90_0" Pin1InfoVect1LinkObjId="g_3d41830_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3913,-1050 3913,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4131ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3913,-947 3913,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="8736@1" ObjectIDZND0="g_40c8f40@0" ObjectIDZND1="8738@x" Pin0InfoVect0LinkObjId="g_40c8f40_0" Pin0InfoVect1LinkObjId="SW-47875_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47873_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3913,-947 3913,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4131f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3913,-967 3913,-987 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="8736@x" ObjectIDND1="8738@x" ObjectIDZND0="g_40c8f40@1" Pin0InfoVect0LinkObjId="g_40c8f40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47873_0" Pin1InfoVect1LinkObjId="SW-47875_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3913,-967 3913,-987 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22c9390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3913,-967 3894,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_40c8f40@0" ObjectIDND1="8736@x" ObjectIDZND0="8738@1" Pin0InfoVect0LinkObjId="SW-47875_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_40c8f40_0" Pin1InfoVect1LinkObjId="SW-47873_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3913,-967 3894,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22c95f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-967 3845,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8738@0" ObjectIDZND0="g_22c9850@0" Pin0InfoVect0LinkObjId="g_22c9850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47875_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-967 3845,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dc3660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-834 3845,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8737@0" ObjectIDZND0="g_3dc38c0@0" Pin0InfoVect0LinkObjId="g_3dc38c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47874_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-834 3845,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dc4350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3913,-834 3894,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="8735@x" ObjectIDND1="8734@x" ObjectIDZND0="8737@1" Pin0InfoVect0LinkObjId="SW-47874_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47872_0" Pin1InfoVect1LinkObjId="SW-47871_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3913,-834 3894,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dc45b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-818 3912,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="8735@1" ObjectIDZND0="8737@x" ObjectIDZND1="8734@x" Pin0InfoVect0LinkObjId="SW-47874_0" Pin0InfoVect1LinkObjId="SW-47871_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47872_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-818 3912,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dc4810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-834 3912,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8735@x" ObjectIDND1="8737@x" ObjectIDZND0="8734@0" Pin0InfoVect0LinkObjId="SW-47871_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47872_0" Pin1InfoVect1LinkObjId="SW-47874_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-834 3912,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dc4a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4198,-735 4211,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8722@1" ObjectIDZND0="g_3dc4cd0@0" Pin0InfoVect0LinkObjId="g_3dc4cd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47859_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4198,-735 4211,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dc5760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-735 4162,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="8721@x" ObjectIDND1="8696@0" ObjectIDZND0="8722@0" Pin0InfoVect0LinkObjId="SW-47859_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47858_0" Pin1InfoVect1LinkObjId="g_40dbc20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-735 4162,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dc59c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-710 4144,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="8721@1" ObjectIDZND0="8722@x" ObjectIDZND1="8696@0" Pin0InfoVect0LinkObjId="SW-47859_0" Pin0InfoVect1LinkObjId="g_40dbc20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47858_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-710 4144,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40dbc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-735 4144,-761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="8721@x" ObjectIDND1="8722@x" ObjectIDZND0="8696@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47858_0" Pin1InfoVect1LinkObjId="SW-47859_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-735 4144,-761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40de770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4200,-654 4213,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8723@1" ObjectIDZND0="g_433bf60@0" Pin0InfoVect0LinkObjId="g_433bf60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47860_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4200,-654 4213,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_433c9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4145,-654 4164,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_4225cb0@0" ObjectIDND1="8721@x" ObjectIDZND0="8723@0" Pin0InfoVect0LinkObjId="SW-47860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4225cb0_0" Pin1InfoVect1LinkObjId="SW-47858_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4145,-654 4164,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_42257f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-617 4144,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_4225cb0@0" ObjectIDZND0="8723@x" ObjectIDZND1="8721@x" Pin0InfoVect0LinkObjId="SW-47860_0" Pin0InfoVect1LinkObjId="SW-47858_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4225cb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-617 4144,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4225a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-654 4144,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_4225cb0@0" ObjectIDND1="8723@x" ObjectIDZND0="8721@0" Pin0InfoVect0LinkObjId="SW-47858_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4225cb0_0" Pin1InfoVect1LinkObjId="SW-47860_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-654 4144,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f11410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-631 3980,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8712@1" ObjectIDZND0="g_3f11670@0" Pin0InfoVect0LinkObjId="g_3f11670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47849_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-631 3980,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f12100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-631 3931,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="8711@x" ObjectIDND1="8709@x" ObjectIDZND0="8712@0" Pin0InfoVect0LinkObjId="SW-47849_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47848_0" Pin1InfoVect1LinkObjId="SW-47846_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-631 3931,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fae1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-620 3912,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="8711@1" ObjectIDZND0="8712@x" ObjectIDZND1="8709@x" Pin0InfoVect0LinkObjId="SW-47849_0" Pin0InfoVect1LinkObjId="SW-47846_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47848_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-620 3912,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3fae420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-631 3912,-641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8711@x" ObjectIDND1="8712@x" ObjectIDZND0="8709@0" Pin0InfoVect0LinkObjId="SW-47846_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47848_0" Pin1InfoVect1LinkObjId="SW-47849_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-631 3912,-641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3faf430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3945,-571 3912,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_3fae680@0" ObjectIDZND0="8711@x" ObjectIDZND1="8748@x" Pin0InfoVect0LinkObjId="SW-47848_0" Pin0InfoVect1LinkObjId="g_3faf8f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fae680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3945,-571 3912,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3faf690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-584 3912,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="8711@0" ObjectIDZND0="g_3fae680@0" ObjectIDZND1="8748@x" Pin0InfoVect0LinkObjId="g_3fae680_0" Pin0InfoVect1LinkObjId="g_3faf8f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47848_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-584 3912,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3faf8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-571 3912,-554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="8711@x" ObjectIDND1="g_3fae680@0" ObjectIDZND0="8748@1" Pin0InfoVect0LinkObjId="g_40c1c10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47848_0" Pin1InfoVect1LinkObjId="g_3fae680_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-571 3912,-554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40c1c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-514 3856,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="g_3fafb50@0" ObjectIDZND0="8748@x" ObjectIDZND1="8708@x" Pin0InfoVect0LinkObjId="g_3faf8f0_0" Pin0InfoVect1LinkObjId="SW-47845_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fafb50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-514 3856,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_40c1e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3913,-532 3856,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="8748@x" ObjectIDZND0="g_3fafb50@0" ObjectIDZND1="8708@x" Pin0InfoVect0LinkObjId="g_3fafb50_0" Pin0InfoVect1LinkObjId="SW-47845_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3faf8f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3913,-532 3856,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34cd5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-532 3827,-532 3827,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="8748@x" ObjectIDND1="g_3fafb50@0" ObjectIDZND0="8708@1" Pin0InfoVect0LinkObjId="SW-47845_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3faf8f0_0" Pin1InfoVect1LinkObjId="g_3fafb50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-532 3827,-532 3827,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34cd850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3827,-484 3827,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8708@0" ObjectIDZND0="g_34cdab0@0" Pin0InfoVect0LinkObjId="g_34cdab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47845_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3827,-484 3827,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34cf270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4302,-513 4302,-531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="g_34ce4c0@0" ObjectIDZND0="8713@x" ObjectIDZND1="8749@x" Pin0InfoVect0LinkObjId="SW-47851_0" Pin0InfoVect1LinkObjId="g_3ef6db0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34ce4c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4302,-513 4302,-531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ff0300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4302,-531 4273,-531 4273,-519 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="g_34ce4c0@0" ObjectIDND1="8749@x" ObjectIDZND0="8713@1" Pin0InfoVect0LinkObjId="SW-47851_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_34ce4c0_0" Pin1InfoVect1LinkObjId="g_3ef6db0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4302,-531 4273,-531 4273,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ff0560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4273,-483 4273,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8713@0" ObjectIDZND0="g_3ff07c0@0" Pin0InfoVect0LinkObjId="g_3ff07c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47851_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4273,-483 4273,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ff1210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-531 4302,-531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="8749@x" ObjectIDZND0="8713@x" ObjectIDZND1="g_34ce4c0@0" Pin0InfoVect0LinkObjId="SW-47851_0" Pin0InfoVect1LinkObjId="g_34ce4c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ef6db0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-531 4302,-531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ff1470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4414,-625 4427,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8717@1" ObjectIDZND0="g_3ff16d0@0" Pin0InfoVect0LinkObjId="g_3ff16d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47855_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4414,-625 4427,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f30410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-625 4378,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="8716@x" ObjectIDND1="8714@x" ObjectIDZND0="8717@0" Pin0InfoVect0LinkObjId="SW-47855_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47854_0" Pin1InfoVect1LinkObjId="SW-47852_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-625 4378,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4193fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4392,-565 4359,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="g_4193290@0" ObjectIDZND0="8716@x" ObjectIDZND1="8749@x" Pin0InfoVect0LinkObjId="SW-47854_0" Pin0InfoVect1LinkObjId="g_3ef6db0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4193290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4392,-565 4359,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4194200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-614 4360,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="8716@1" ObjectIDZND0="8717@x" ObjectIDZND1="8714@x" Pin0InfoVect0LinkObjId="SW-47855_0" Pin0InfoVect1LinkObjId="SW-47852_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47854_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-614 4360,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4194460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-625 4360,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8716@x" ObjectIDND1="8717@x" ObjectIDZND0="8714@0" Pin0InfoVect0LinkObjId="SW-47852_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47854_0" Pin1InfoVect1LinkObjId="SW-47855_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-625 4360,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_41946c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-578 4360,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="8716@0" ObjectIDZND0="g_4193290@0" ObjectIDZND1="8749@x" Pin0InfoVect0LinkObjId="g_4193290_0" Pin0InfoVect1LinkObjId="g_3ef6db0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47854_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-578 4360,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_4194920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-565 4360,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer" ObjectIDND0="8716@x" ObjectIDND1="g_4193290@0" ObjectIDZND0="8749@1" Pin0InfoVect0LinkObjId="g_3ef6db0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47854_0" Pin1InfoVect1LinkObjId="g_4193290_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-565 4360,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_4195930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3640,-473 3673,-473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_4194b80@0" ObjectIDZND0="8747@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-47882_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4194b80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3640,-473 3673,-473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_4195b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-462 3673,-473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="8747@1" ObjectIDZND0="g_4194b80@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_4194b80_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47882_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-462 3673,-473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3e9e350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-473 3673,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="8747@x" ObjectIDND1="g_4194b80@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47882_0" Pin1InfoVect1LinkObjId="g_4194b80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-473 3673,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3e9f1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3638,-280 3671,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_3e9e5b0@0" ObjectIDZND0="8699@x" ObjectIDZND1="8697@x" Pin0InfoVect0LinkObjId="SW-47829_0" Pin0InfoVect1LinkObjId="SW-47828_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e9e5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3638,-280 3671,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3e9f450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-270 3673,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="8699@0" ObjectIDZND0="g_3e9e5b0@0" ObjectIDZND1="8697@x" Pin0InfoVect0LinkObjId="g_3e9e5b0_0" Pin0InfoVect1LinkObjId="SW-47828_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47829_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-270 3673,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_3e9f6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-280 3673,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="8699@x" ObjectIDND1="g_3e9e5b0@0" ObjectIDZND0="8697@0" Pin0InfoVect0LinkObjId="SW-47828_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47829_0" Pin1InfoVect1LinkObjId="g_3e9e5b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-280 3673,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ea0190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3605,-200 3605,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3e9f910@1" Pin0InfoVect0LinkObjId="g_3e9f910_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3605,-200 3605,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ea03f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3605,-154 3605,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3e9f910@0" ObjectIDZND0="g_25e4660@0" Pin0InfoVect0LinkObjId="g_25e4660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e9f910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3605,-154 3605,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ea0ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-144 3738,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_42bc7f0@0" ObjectIDZND0="g_3ea0650@0" Pin0InfoVect0LinkObjId="g_3ea0650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_42bc7f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-144 3738,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ea1130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-184 3738,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3ea0650@1" ObjectIDZND0="0@x" ObjectIDZND1="g_418fea0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_418fea0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ea0650_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-184 3738,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2614100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4036,-200 4036,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3ea1390@1" Pin0InfoVect0LinkObjId="g_3ea1390_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4036,-200 4036,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2614360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4036,-153 4036,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3ea1390@0" ObjectIDZND0="g_3cee7c0@0" Pin0InfoVect0LinkObjId="g_3cee7c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ea1390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4036,-153 4036,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2614e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-142 4169,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_42bd0f0@0" ObjectIDZND0="g_26145c0@0" Pin0InfoVect0LinkObjId="g_26145c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_42bd0f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-142 4169,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26150a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-181 4169,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_26145c0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_42bdbb0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_42bdbb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26145c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-181 4169,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_4484e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3924,-278 3944,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_26160b0@0" ObjectIDZND0="8745@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-47880_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26160b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3924,-278 3944,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_44850e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-288 3944,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="8745@0" ObjectIDZND0="g_26160b0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_26160b0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-288 3944,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_4485340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-278 3944,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="8745@x" ObjectIDND1="g_26160b0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47880_0" Pin1InfoVect1LinkObjId="g_26160b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-278 3944,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4485e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3817,-301 3848,-301 3848,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2615300@0" ObjectIDZND0="g_44855a0@1" Pin0InfoVect0LinkObjId="g_44855a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2615300_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3817,-301 3848,-301 3848,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4486080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-254 3848,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_44855a0@0" ObjectIDZND0="g_3ef6370@0" Pin0InfoVect0LinkObjId="g_3ef6370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_44855a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-254 3848,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_4487090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4083,-277 4103,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_44862e0@0" ObjectIDZND0="8702@x" ObjectIDZND1="8700@x" Pin0InfoVect0LinkObjId="SW-47834_0" Pin0InfoVect1LinkObjId="SW-47833_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_44862e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4083,-277 4103,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_44872f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-268 4104,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="8702@0" ObjectIDZND0="g_44862e0@0" ObjectIDZND1="8700@x" Pin0InfoVect0LinkObjId="g_44862e0_0" Pin0InfoVect1LinkObjId="SW-47833_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47834_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-268 4104,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_4487550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-277 4104,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="8702@x" ObjectIDND1="g_44862e0@0" ObjectIDZND0="8700@0" Pin0InfoVect0LinkObjId="SW-47833_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47834_0" Pin1InfoVect1LinkObjId="g_44862e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-277 4104,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_32bac40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-275 4359,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_44877b0@0" ObjectIDZND0="8705@x" ObjectIDZND1="8703@x" Pin0InfoVect0LinkObjId="SW-47839_0" Pin0InfoVect1LinkObjId="SW-47838_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_44877b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-275 4359,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_32baea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-266 4359,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="8705@0" ObjectIDZND0="g_44877b0@0" ObjectIDZND1="8703@x" Pin0InfoVect0LinkObjId="g_44877b0_0" Pin0InfoVect1LinkObjId="SW-47838_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47839_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-266 4359,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_32bb100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-275 4359,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="8705@x" ObjectIDND1="g_44877b0@0" ObjectIDZND0="8703@0" Pin0InfoVect0LinkObjId="SW-47838_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47839_0" Pin1InfoVect1LinkObjId="g_44877b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-275 4359,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32bbbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4291,-198 4291,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_32bb360@1" Pin0InfoVect0LinkObjId="g_32bb360_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4291,-198 4291,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32bbe40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4291,-152 4291,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_32bb360@0" ObjectIDZND0="g_3fa4b10@0" Pin0InfoVect0LinkObjId="g_3fa4b10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32bb360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4291,-152 4291,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32bc920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4424,-140 4424,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3ca9450@0" ObjectIDZND0="g_32bc0a0@0" Pin0InfoVect0LinkObjId="g_32bc0a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ca9450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4424,-140 4424,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32bcb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4424,-181 4424,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_32bc0a0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_3ca9f90@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3ca9f90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32bc0a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4424,-181 4424,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_43b0320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-297 4568,-297 4568,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_32bcde0@0" ObjectIDZND0="g_43afaa0@1" Pin0InfoVect0LinkObjId="g_43afaa0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_32bcde0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-297 4568,-297 4568,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_43b0580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4568,-252 4568,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_43afaa0@0" ObjectIDZND0="g_360bb80@0" Pin0InfoVect0LinkObjId="g_360bb80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_43afaa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4568,-252 4568,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_43b1590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4683,-275 4703,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_43b07e0@0" ObjectIDZND0="8746@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-47881_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_43b07e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4683,-275 4703,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_43b17f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4702,-284 4702,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="8746@0" ObjectIDZND0="g_43b07e0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_43b07e0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47881_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4702,-284 4702,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_43b1a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4702,-275 4702,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="8746@x" ObjectIDND1="g_43b07e0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47881_0" Pin1InfoVect1LinkObjId="g_43b07e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4702,-275 4702,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3dd1170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4702,-246 4702,-229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4702,-246 4702,-229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3dd13d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-885 3912,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8734@1" ObjectIDZND0="8736@0" Pin0InfoVect0LinkObjId="SW-47873_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47871_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-885 3912,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f1b270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4111,-1050 4144,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="g_3f1b4d0@0" ObjectIDZND0="g_448bf20@0" ObjectIDZND1="g_3d427c0@0" ObjectIDZND2="11878@1" Pin0InfoVect0LinkObjId="g_448bf20_0" Pin0InfoVect1LinkObjId="g_3d427c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f1b4d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4111,-1050 4144,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35f6490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-1050 4143,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="g_3f1b4d0@0" ObjectIDND1="g_3d427c0@0" ObjectIDND2="11878@1" ObjectIDZND0="g_448bf20@0" Pin0InfoVect0LinkObjId="g_448bf20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f1b4d0_0" Pin1InfoVect1LinkObjId="g_3d427c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-1050 4143,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35f66f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-947 4143,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="8731@1" ObjectIDZND0="g_448bf20@0" ObjectIDZND1="8733@x" Pin0InfoVect0LinkObjId="g_448bf20_0" Pin0InfoVect1LinkObjId="SW-47870_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47868_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-947 4143,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35f6950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-969 4143,-989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="8731@x" ObjectIDND1="8733@x" ObjectIDZND0="g_448bf20@1" Pin0InfoVect0LinkObjId="g_448bf20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47868_0" Pin1InfoVect1LinkObjId="SW-47870_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-969 4143,-989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_41fdd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-969 4125,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_448bf20@0" ObjectIDND1="8731@x" ObjectIDZND0="8733@1" Pin0InfoVect0LinkObjId="SW-47870_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_448bf20_0" Pin1InfoVect1LinkObjId="SW-47868_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-969 4125,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_41fdf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4089,-969 4076,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8733@0" ObjectIDZND0="g_41fe1d0@0" Pin0InfoVect0LinkObjId="g_41fe1d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4089,-969 4076,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3e56600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4089,-837 4076,-837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8732@0" ObjectIDZND0="g_3e56860@0" Pin0InfoVect0LinkObjId="g_3e56860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47869_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4089,-837 4076,-837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3e572f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-837 4125,-837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="8730@x" ObjectIDND1="8729@x" ObjectIDZND0="8732@1" Pin0InfoVect0LinkObjId="SW-47869_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47867_0" Pin1InfoVect1LinkObjId="SW-47866_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-837 4125,-837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3e57550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-818 4143,-837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="8730@1" ObjectIDZND0="8732@x" ObjectIDZND1="8729@x" Pin0InfoVect0LinkObjId="SW-47869_0" Pin0InfoVect1LinkObjId="SW-47866_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47867_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-818 4143,-837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3e577b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-837 4143,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8730@x" ObjectIDND1="8732@x" ObjectIDZND0="8729@0" Pin0InfoVect0LinkObjId="SW-47866_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47867_0" Pin1InfoVect1LinkObjId="SW-47869_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-837 4143,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3e57a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-886 4143,-911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8729@1" ObjectIDZND0="8731@0" Pin0InfoVect0LinkObjId="SW-47868_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47866_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-886 4143,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3e57c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-761 4143,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8696@0" ObjectIDZND0="8730@0" Pin0InfoVect0LinkObjId="SW-47867_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40dbc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-761 4143,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35cee20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4326,-1050 4359,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="g_35cf080@0" ObjectIDZND0="g_25d66c0@0" ObjectIDZND1="g_3d43750@0" ObjectIDZND2="11886@1" Pin0InfoVect0LinkObjId="g_25d66c0_0" Pin0InfoVect1LinkObjId="g_3d43750_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35cf080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4326,-1050 4359,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35cfe30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-1050 4360,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="g_35cf080@0" ObjectIDND1="g_3d43750@0" ObjectIDND2="11886@1" ObjectIDZND0="g_25d66c0@0" Pin0InfoVect0LinkObjId="g_25d66c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_35cf080_0" Pin1InfoVect1LinkObjId="g_3d43750_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-1050 4360,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35d0090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-947 4360,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="8726@1" ObjectIDZND0="g_25d66c0@0" ObjectIDZND1="8728@x" Pin0InfoVect0LinkObjId="g_25d66c0_0" Pin0InfoVect1LinkObjId="SW-47865_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47863_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-947 4360,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35d02f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-967 4360,-987 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="8726@x" ObjectIDND1="8728@x" ObjectIDZND0="g_25d66c0@1" Pin0InfoVect0LinkObjId="g_25d66c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47863_0" Pin1InfoVect1LinkObjId="SW-47865_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-967 4360,-987 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34db830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-967 4341,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_25d66c0@0" ObjectIDND1="8726@x" ObjectIDZND0="8728@1" Pin0InfoVect0LinkObjId="SW-47865_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25d66c0_0" Pin1InfoVect1LinkObjId="SW-47863_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-967 4341,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34dba90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-967 4292,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8728@0" ObjectIDZND0="g_34dbcf0@0" Pin0InfoVect0LinkObjId="g_34dbcf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47865_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-967 4292,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34d2910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-834 4292,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8727@0" ObjectIDZND0="g_34d2b70@0" Pin0InfoVect0LinkObjId="g_34d2b70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47864_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-834 4292,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34d3600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-834 4341,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="8725@x" ObjectIDND1="8724@x" ObjectIDZND0="8727@1" Pin0InfoVect0LinkObjId="SW-47864_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47862_0" Pin1InfoVect1LinkObjId="SW-47861_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-834 4341,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34d3860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-818 4359,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="8725@1" ObjectIDZND0="8727@x" ObjectIDZND1="8724@x" Pin0InfoVect0LinkObjId="SW-47864_0" Pin0InfoVect1LinkObjId="SW-47861_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47862_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-818 4359,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34d3ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-834 4359,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8725@x" ObjectIDND1="8727@x" ObjectIDZND0="8724@0" Pin0InfoVect0LinkObjId="SW-47861_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47862_0" Pin1InfoVect1LinkObjId="SW-47864_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-834 4359,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34d3d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-885 4359,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8724@1" ObjectIDZND0="8726@0" Pin0InfoVect0LinkObjId="SW-47863_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47861_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-885 4359,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34d3f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-761 4359,-781 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8696@0" ObjectIDZND0="8725@0" Pin0InfoVect0LinkObjId="SW-47862_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40dbc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4359,-761 4359,-781 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3386700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4702,-894 4715,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_231e900@0" ObjectIDZND0="g_3385e80@1" Pin0InfoVect0LinkObjId="g_3385e80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_231e900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4702,-894 4715,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3386960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4746,-894 4759,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="g_3385e80@0" ObjectIDZND0="8740@x" ObjectIDZND1="g_34d41e0@0" ObjectIDZND2="47298@1" Pin0InfoVect0LinkObjId="SW-47877_0" Pin0InfoVect1LinkObjId="g_34d41e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3385e80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4746,-894 4759,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4115120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4535,-511 4759,-511 4759,-582 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="8749@x" ObjectIDND1="g_4117340@0" ObjectIDZND0="8720@1" Pin0InfoVect0LinkObjId="SW-47857_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3ef6db0_0" Pin1InfoVect1LinkObjId="g_4117340_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4535,-511 4759,-511 4759,-582 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4115380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4759,-600 4759,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8720@0" ObjectIDZND0="8718@0" Pin0InfoVect0LinkObjId="SW-47856_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47857_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4759,-600 4759,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41155e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4759,-642 4759,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8718@1" ObjectIDZND0="8719@1" Pin0InfoVect0LinkObjId="SW-47857_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47856_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4759,-642 4759,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d41600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4759,-678 4759,-761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8719@0" ObjectIDZND0="8695@0" Pin0InfoVect0LinkObjId="g_231df80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47857_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4759,-678 4759,-761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3d420a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-1079 3913,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_3d41830@0" ObjectIDZND0="g_40c8f40@0" ObjectIDZND1="g_4130c90@0" Pin0InfoVect0LinkObjId="g_40c8f40_0" Pin0InfoVect1LinkObjId="g_4130c90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d41830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-1079 3913,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3d42300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3913,-1088 3913,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDZND0="g_3d41830@0" ObjectIDZND1="g_40c8f40@0" ObjectIDZND2="g_4130c90@0" Pin0InfoVect0LinkObjId="g_3d41830_0" Pin0InfoVect1LinkObjId="g_40c8f40_0" Pin0InfoVect2LinkObjId="g_4130c90_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3913,-1088 3913,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3d42560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3913,-1079 3913,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_3d41830@0" ObjectIDZND0="g_40c8f40@0" ObjectIDZND1="g_4130c90@0" Pin0InfoVect0LinkObjId="g_40c8f40_0" Pin0InfoVect1LinkObjId="g_4130c90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d41830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3913,-1079 3913,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3d43030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4099,-1080 4143,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="g_3d427c0@0" ObjectIDZND0="g_448bf20@0" ObjectIDZND1="g_3f1b4d0@0" ObjectIDZND2="11878@1" Pin0InfoVect0LinkObjId="g_448bf20_0" Pin0InfoVect1LinkObjId="g_3f1b4d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d427c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4099,-1080 4143,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3d43290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-1098 4143,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="11878@1" ObjectIDZND0="g_3d427c0@0" ObjectIDZND1="g_448bf20@0" ObjectIDZND2="g_3f1b4d0@0" Pin0InfoVect0LinkObjId="g_3d427c0_0" Pin0InfoVect1LinkObjId="g_448bf20_0" Pin0InfoVect2LinkObjId="g_3f1b4d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-1098 4143,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3d434f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-1080 4143,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_3d427c0@0" ObjectIDND1="11878@1" ObjectIDZND0="g_448bf20@0" ObjectIDZND1="g_3f1b4d0@0" Pin0InfoVect0LinkObjId="g_448bf20_0" Pin0InfoVect1LinkObjId="g_3f1b4d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3d427c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-1080 4143,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3d43fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4316,-1075 4361,-1075 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="g_3d43750@0" ObjectIDZND0="g_25d66c0@0" ObjectIDZND1="g_35cf080@0" ObjectIDZND2="11886@1" Pin0InfoVect0LinkObjId="g_25d66c0_0" Pin0InfoVect1LinkObjId="g_35cf080_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d43750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4316,-1075 4361,-1075 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3d44220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-1098 4360,-1075 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="11886@1" ObjectIDZND0="g_3d43750@0" ObjectIDZND1="g_25d66c0@0" ObjectIDZND2="g_35cf080@0" Pin0InfoVect0LinkObjId="g_3d43750_0" Pin0InfoVect1LinkObjId="g_25d66c0_0" Pin0InfoVect2LinkObjId="g_35cf080_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-1098 4360,-1075 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3d44480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-1075 4360,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_3d43750@0" ObjectIDND1="11886@1" ObjectIDZND0="g_25d66c0@0" ObjectIDZND1="g_35cf080@0" Pin0InfoVect0LinkObjId="g_25d66c0_0" Pin0InfoVect1LinkObjId="g_35cf080_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3d43750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-1075 4360,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b57270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-648 3624,-648 3624,-637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="transformer2" EndDevType0="lightningRod" ObjectIDND0="g_40e3760@0" ObjectIDND1="0@1" ObjectIDND2="0@x" ObjectIDZND0="g_3d446e0@1" Pin0InfoVect0LinkObjId="g_3d446e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_40e3760_0" Pin1InfoVect1LinkObjId="SW-0_1" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-648 3624,-648 3624,-637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b574d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3624,-606 3624,-593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3d446e0@0" ObjectIDZND0="g_3c9bb70@0" Pin0InfoVect0LinkObjId="g_3c9bb70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d446e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3624,-606 3624,-593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b57fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4887,-719 4887,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_33850d0@0" ObjectIDZND0="g_2b57730@1" Pin0InfoVect0LinkObjId="g_2b57730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_33850d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4887,-719 4887,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b58210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4887,-665 4887,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2b57730@0" ObjectIDZND0="g_2a1e7c0@0" Pin0InfoVect0LinkObjId="g_2a1e7c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b57730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4887,-665 4887,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3dbc6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4902,-1040 4902,-1058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4902,-1040 4902,-1058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3dbc8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4902,-1094 4902,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_3dbbe90@0" Pin0InfoVect0LinkObjId="g_3dbbe90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4902,-1094 4902,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_422bd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4984,-1040 4984,-1058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4984,-1040 4984,-1058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_422bfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4984,-1094 4984,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_422b2d0@0" Pin0InfoVect0LinkObjId="g_422b2d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4984,-1094 4984,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29eaa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4902,-1040 4926,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4902,-1040 4926,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29eac90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4962,-1040 4984,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4962,-1040 4984,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29ecee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5011,-1040 4984,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5011,-1040 4984,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4109c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-1040 5096,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-1040 5096,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4109ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5054,-1040 5038,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5054,-1040 5038,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b3bdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4759,-960 4759,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="47298@1" ObjectIDZND0="8740@x" ObjectIDZND1="g_3385e80@0" ObjectIDZND2="g_34d41e0@0" Pin0InfoVect0LinkObjId="SW-47877_0" Pin0InfoVect1LinkObjId="g_3385e80_0" Pin0InfoVect2LinkObjId="g_34d41e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4759,-960 4759,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b3c8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4759,-894 4759,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="8740@x" ObjectIDND1="g_3385e80@0" ObjectIDZND0="g_34d41e0@0" ObjectIDZND1="47298@1" Pin0InfoVect0LinkObjId="g_34d41e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47877_0" Pin1InfoVect1LinkObjId="g_3385e80_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4759,-894 4759,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b3cb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4759,-927 4727,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="8740@x" ObjectIDND1="g_3385e80@0" ObjectIDND2="47298@1" ObjectIDZND0="g_34d41e0@0" Pin0InfoVect0LinkObjId="g_34d41e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-47877_0" Pin1InfoVect1LinkObjId="g_3385e80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4759,-927 4727,-927 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_BGSJ"/>
</svg>