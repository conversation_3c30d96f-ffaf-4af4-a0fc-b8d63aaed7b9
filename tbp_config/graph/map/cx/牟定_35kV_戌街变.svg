<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-200" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-672 -1338 2121 1221">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape45">
    <polyline arcFlag="1" points="19,100 17,100 15,99 14,99 12,98 11,97 9,96 8,94 7,92 7,91 6,89 6,87 6,85 7,83 7,82 8,80 9,79 11,77 12,76 14,75 15,75 17,74 19,74 21,74 23,75 24,75 26,76 27,77 29,79 30,80 31,82 31,83 32,85 32,87 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="32" x2="19" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="19" x2="19" y1="100" y2="111"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="14" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="55" y2="47"/>
    <polyline arcFlag="1" points="36,14 37,14 38,14 38,14 39,14 39,15 40,15 40,16 41,16 41,17 41,17 41,18 42,19 42,19 42,20 41,21 41,21 41,22 41,22 40,23 40,23 39,24 39,24 38,24 38,25 37,25 36,25 " stroke-width="1"/>
    <polyline arcFlag="1" points="36,36 37,36 38,36 38,37 39,37 39,37 40,38 40,38 41,39 41,39 41,40 41,40 42,41 42,42 42,42 41,43 41,44 41,44 41,45 40,45 40,46 39,46 39,47 38,47 38,47 37,47 36,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="19" x2="19" y1="87" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="2" x2="35" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="19" x2="36" y1="8" y2="8"/>
    <rect height="23" stroke-width="0.369608" width="12" x="13" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="19" x2="36" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="2" x2="2" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="35" x2="35" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="19" x2="19" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="10" x2="26" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="11" x2="26" y1="21" y2="21"/>
    <polyline arcFlag="1" points="36,25 37,25 38,25 38,25 39,26 39,26 40,26 40,27 41,27 41,28 41,29 41,29 42,30 42,31 42,31 41,32 41,33 41,33 41,34 40,34 40,35 39,35 39,35 38,36 38,36 37,36 36,36 " stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape141">
    <polyline DF8003:Layer="PUBLIC" points="18,1 18,16 30,8 18,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="4" x2="84" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="72,1 72,16 60,8 72,1 "/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape54_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <rect height="28" stroke-width="1" width="14" x="90" y="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="95" x2="98" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="93" x2="101" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="97" x2="97" y1="28" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="103" x2="91" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="97" x2="97" y1="75" y2="40"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="39" y1="75" y2="75"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape54_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape42_0">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="73" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="79" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="81" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape42_1">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="49" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="55" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="57" y2="55"/>
   </symbol>
   <symbol id="transformer2:shape40_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape40_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <polyline DF8003:Layer="PUBLIC" points="30,87 26,78 36,78 30,87 "/>
   </symbol>
   <symbol id="voltageTransformer:shape79">
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="34" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="13" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="15" y2="9"/>
    <polyline points="40,23 28,32 28,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="37" y2="46"/>
    <rect height="14" stroke-width="1" width="8" x="30" y="23"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="23" y2="13"/>
   </symbol>
   <symbol id="voltageTransformer:shape135">
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="6" y2="52"/>
    <ellipse cx="8" cy="67" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="59" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="6" y1="69" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="57" y2="57"/>
   </symbol>
   <symbol id="voltageTransformer:shape136">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="4" y1="9" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="7" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="5" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="6" y1="7" y2="7"/>
    <ellipse cx="36" cy="17" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="24" cy="18" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="29" cy="8" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="4" y1="19" y2="19"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1692a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15e69b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_16197f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15dcdf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15de050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_164d130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_164db50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_164e610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1693bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1693bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1659b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1659b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15379c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15379c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_15389e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15a31d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_15a3e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_15a4bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_16b26a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_16b3dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1635980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1636160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1636920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_168c180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_168c980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_168d550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_168df40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_15cdf90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_15ceab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_15cfc50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15d0870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15c8150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1524d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_15f86c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1639970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1231" width="2131" x="-677" y="-1343"/>
  </g><g id="Line_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="449,-354 467,-354 467,-374 " stroke="rgb(0,255,0)" stroke-width="0.75"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(0,255,0)" stroke-dasharray="10 5 " stroke-width="1" x1="403" x2="504" y1="-433" y2="-433"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.873333" x1="494" x2="494" y1="-466" y2="-441"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.77887" x1="493" x2="495" y1="-441" y2="-441"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.77887" x1="494" x2="487" y1="-427" y2="-440"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="450,-243 450,-223 494,-223 494,-427 " stroke="rgb(0,255,0)" stroke-width="0.570644"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.322998" x1="497" x2="491" y1="-470" y2="-470"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.228882" x1="495" x2="493" y1="-473" y2="-473"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="500" x2="488" y1="-466" y2="-466"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.1875" x1="386" x2="386" y1="-306" y2="-303"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.344531" x1="389" x2="389" y1="-308" y2="-301"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.305149" x1="393" x2="402" y1="-304" y2="-304"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.560509" x1="393" x2="393" y1="-299" y2="-310"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="449" x2="407" y1="-304" y2="-304"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="401,-290 397,-290 397,-319 401,-319 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(0,255,0)" stroke-dasharray="10 5 " stroke-width="1" x1="1130" x2="1231" y1="-433" y2="-433"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1175,-383 1193,-383 1193,-403 " stroke="rgb(0,255,0)" stroke-width="0.75"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.1875" x1="1112" x2="1112" y1="-335" y2="-332"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.344531" x1="1115" x2="1115" y1="-337" y2="-330"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.305149" x1="1119" x2="1128" y1="-333" y2="-333"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.560509" x1="1119" x2="1119" y1="-328" y2="-339"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1175" x2="1133" y1="-333" y2="-333"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1127,-319 1123,-319 1123,-348 1127,-348 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-269 1176,-252 1220,-252 1220,-425 " stroke="rgb(0,255,0)" stroke-width="0.570644"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.873333" x1="1220" x2="1220" y1="-463" y2="-438"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.77887" x1="1219" x2="1221" y1="-438" y2="-438"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.77887" x1="1220" x2="1213" y1="-424" y2="-437"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.322998" x1="1223" x2="1217" y1="-467" y2="-467"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.228882" x1="1221" x2="1219" y1="-470" y2="-470"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1226" x2="1214" y1="-463" y2="-463"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-132299">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 267.000000 -1059.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24249" ObjectName="SW-MD_XJ.MD_XJ_362BK"/>
     <cge:Meas_Ref ObjectId="132299"/>
    <cge:TPSR_Ref TObjectID="24249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132271">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 717.000000 -1059.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24242" ObjectName="SW-MD_XJ.MD_XJ_361BK"/>
     <cge:Meas_Ref ObjectId="132271"/>
    <cge:TPSR_Ref TObjectID="24242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132329">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 232.165268 -864.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24334" ObjectName="SW-MD_XJ.MD_XJ_301BK"/>
     <cge:Meas_Ref ObjectId="132329"/>
    <cge:TPSR_Ref TObjectID="24334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132399">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 795.402135 -860.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24336" ObjectName="SW-MD_XJ.MD_XJ_302BK"/>
     <cge:Meas_Ref ObjectId="132399"/>
    <cge:TPSR_Ref TObjectID="24336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132342">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 232.165268 -683.190476)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24335" ObjectName="SW-MD_XJ.MD_XJ_001BK"/>
     <cge:Meas_Ref ObjectId="132342"/>
    <cge:TPSR_Ref TObjectID="24335"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132413">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 795.402135 -678.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24337" ObjectName="SW-MD_XJ.MD_XJ_002BK"/>
     <cge:Meas_Ref ObjectId="132413"/>
    <cge:TPSR_Ref TObjectID="24337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133007">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.022727 -0.000000 0.000000 -0.951763 448.603071 -648.061224)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24333" ObjectName="SW-MD_XJ.MD_XJ_012BK"/>
     <cge:Meas_Ref ObjectId="133007"/>
    <cge:TPSR_Ref TObjectID="24333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132468">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -250.466185 -544.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24255" ObjectName="SW-MD_XJ.MD_XJ_065BK"/>
     <cge:Meas_Ref ObjectId="132468"/>
    <cge:TPSR_Ref TObjectID="24255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132491">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -105.166185 -543.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24260" ObjectName="SW-MD_XJ.MD_XJ_064BK"/>
     <cge:Meas_Ref ObjectId="132491"/>
    <cge:TPSR_Ref TObjectID="24260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132514">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 40.633815 -544.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24265" ObjectName="SW-MD_XJ.MD_XJ_063BK"/>
     <cge:Meas_Ref ObjectId="132514"/>
    <cge:TPSR_Ref TObjectID="24265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132537">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 327.233815 -544.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24270" ObjectName="SW-MD_XJ.MD_XJ_062BK"/>
     <cge:Meas_Ref ObjectId="132537"/>
    <cge:TPSR_Ref TObjectID="24270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132560">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.007692 -0.000000 0.000000 -0.986568 440.960344 -561.086978)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24275" ObjectName="SW-MD_XJ.MD_XJ_061BK"/>
     <cge:Meas_Ref ObjectId="132560"/>
    <cge:TPSR_Ref TObjectID="24275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132583">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 598.033815 -544.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24281" ObjectName="SW-MD_XJ.MD_XJ_071BK"/>
     <cge:Meas_Ref ObjectId="132583"/>
    <cge:TPSR_Ref TObjectID="24281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132603">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 890.033815 -543.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24283" ObjectName="SW-MD_XJ.MD_XJ_072BK"/>
     <cge:Meas_Ref ObjectId="132603"/>
    <cge:TPSR_Ref TObjectID="24283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132626">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1036.533815 -543.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24288" ObjectName="SW-MD_XJ.MD_XJ_073BK"/>
     <cge:Meas_Ref ObjectId="132626"/>
    <cge:TPSR_Ref TObjectID="24288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132648">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.007692 -0.000000 0.000000 -0.986568 1167.107692 -551.086978)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24292" ObjectName="SW-MD_XJ.MD_XJ_074BK"/>
     <cge:Meas_Ref ObjectId="132648"/>
    <cge:TPSR_Ref TObjectID="24292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132671">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1302.605244 -542.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24298" ObjectName="SW-MD_XJ.MD_XJ_075BK"/>
     <cge:Meas_Ref ObjectId="132671"/>
    <cge:TPSR_Ref TObjectID="24298"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1dbc180">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 203.900000 -460.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17c2920">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 743.500000 -480.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d2f0d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 308.000000 -1241.000000)" xlink:href="#voltageTransformer:shape135"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d2fa50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 755.000000 -1245.000000)" xlink:href="#voltageTransformer:shape135"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d3d800">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 462.000000 -817.000000)" xlink:href="#voltageTransformer:shape136"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d41b00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 79.000000 -518.000000)" xlink:href="#voltageTransformer:shape135"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d474f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 635.000000 -518.000000)" xlink:href="#voltageTransformer:shape135"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d49ef0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1065.000000 -517.000000)" xlink:href="#voltageTransformer:shape135"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="MD_XJ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_xinxuan2Txj" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="276,-1285 276,-1240 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37801" ObjectName="AC-35kV.LN_xinxuan2Txj"/>
    <cge:TPSR_Ref TObjectID="37801_SS-200"/></metadata>
   <polyline fill="none" opacity="0" points="276,-1285 276,-1240 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="MD_XJ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_xinxuan1Txj" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="726,-1283 726,-1238 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37797" ObjectName="AC-35kV.LN_xinxuan1Txj"/>
    <cge:TPSR_Ref TObjectID="37797_SS-200"/></metadata>
   <polyline fill="none" opacity="0" points="726,-1283 726,-1238 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -249.000000 -311.190476)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_XJ.064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -104.200000 -309.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34301" ObjectName="EC-MD_XJ.064Ld"/>
    <cge:TPSR_Ref TObjectID="34301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_XJ.063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 40.600000 -310.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34300" ObjectName="EC-MD_XJ.063Ld"/>
    <cge:TPSR_Ref TObjectID="34300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_XJ.062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 327.200000 -310.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34299" ObjectName="EC-MD_XJ.062Ld"/>
    <cge:TPSR_Ref TObjectID="34299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_XJ.071Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 598.000000 -310.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34302" ObjectName="EC-MD_XJ.071Ld"/>
    <cge:TPSR_Ref TObjectID="34302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 890.000000 -309.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_XJ.073Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1037.500000 -309.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34303" ObjectName="EC-MD_XJ.073Ld"/>
    <cge:TPSR_Ref TObjectID="34303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_XJ.075Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1303.500000 -308.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34304" ObjectName="EC-MD_XJ.075Ld"/>
    <cge:TPSR_Ref TObjectID="34304"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_14d9f80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 190.000000 -1166.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14da8b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 645.000000 -1175.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14db800" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 643.000000 -1106.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14dc290" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 643.000000 -1043.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14dd900" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 403.000000 -890.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d908e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -319.000000 -496.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1da1bd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -174.200000 -494.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1db2ee0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -29.400000 -495.190476)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dcd7c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 257.200000 -495.190476)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17bc480" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 527.000000 -495.190476)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17d7220" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 820.000000 -494.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17e8510" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 967.500000 -494.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1807bb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1233.500000 -493.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1812f80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 408.000000 -455.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1813c70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1134.000000 -450.190476)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d43a40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 371.000000 -523.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d4d410" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1093.000000 -522.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1552220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="258,-1172 276,-1172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="24253@0" ObjectIDZND0="24251@x" ObjectIDZND1="g_1d32b30@0" ObjectIDZND2="24252@x" Pin0InfoVect0LinkObjId="SW-132302_0" Pin0InfoVect1LinkObjId="g_1d32b30_0" Pin0InfoVect2LinkObjId="SW-132303_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132304_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="258,-1172 276,-1172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1552480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="276,-1172 276,-1147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="24253@x" ObjectIDND1="g_1d32b30@0" ObjectIDND2="24252@x" ObjectIDZND0="24251@1" Pin0InfoVect0LinkObjId="SW-132302_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-132304_0" Pin1InfoVect1LinkObjId="g_1d32b30_0" Pin1InfoVect2LinkObjId="SW-132303_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="276,-1172 276,-1147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15526e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-995 726,-977 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24243@0" ObjectIDZND0="24325@0" Pin0InfoVect0LinkObjId="g_1554100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132273_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="726,-995 726,-977 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1552940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="711,-1049 726,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="24246@0" ObjectIDZND0="24242@x" ObjectIDZND1="24243@x" Pin0InfoVect0LinkObjId="SW-132271_0" Pin0InfoVect1LinkObjId="SW-132273_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132278_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="711,-1049 726,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1552ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-1067 726,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24242@0" ObjectIDZND0="24246@x" ObjectIDZND1="24243@x" Pin0InfoVect0LinkObjId="SW-132278_0" Pin0InfoVect1LinkObjId="SW-132273_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132271_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="726,-1067 726,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1552e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-1049 726,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="24246@x" ObjectIDND1="24242@x" ObjectIDZND0="24243@1" Pin0InfoVect0LinkObjId="SW-132273_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-132278_0" Pin1InfoVect1LinkObjId="SW-132271_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="726,-1049 726,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1553060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="711,-1112 726,-1112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24247@0" ObjectIDZND0="24244@x" ObjectIDZND1="24242@x" Pin0InfoVect0LinkObjId="SW-132275_0" Pin0InfoVect1LinkObjId="SW-132271_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132279_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="711,-1112 726,-1112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15532c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-1124 726,-1112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24244@0" ObjectIDZND0="24247@x" ObjectIDZND1="24242@x" Pin0InfoVect0LinkObjId="SW-132279_0" Pin0InfoVect1LinkObjId="SW-132271_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132275_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="726,-1124 726,-1112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1553520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-1112 726,-1094 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="24247@x" ObjectIDND1="24244@x" ObjectIDZND0="24242@1" Pin0InfoVect0LinkObjId="SW-132271_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-132279_0" Pin1InfoVect1LinkObjId="SW-132275_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="726,-1112 726,-1094 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1553780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="241,-977 241,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24325@0" ObjectIDZND0="24340@1" Pin0InfoVect0LinkObjId="SW-132331_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15526e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="241,-977 241,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15539e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="241,-730 241,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24342@1" ObjectIDZND0="24335@1" Pin0InfoVect0LinkObjId="SW-132342_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132345_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="241,-730 241,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1553c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="241,-691 241,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24335@0" ObjectIDZND0="24341@1" Pin0InfoVect0LinkObjId="SW-132345_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132342_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="241,-691 241,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1553ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="241,-663 241,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24341@0" ObjectIDZND0="24326@0" Pin0InfoVect0LinkObjId="g_1554ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132345_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="241,-663 241,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1554100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="804,-956 804,-977 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24343@1" ObjectIDZND0="24325@0" Pin0InfoVect0LinkObjId="g_15526e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132401_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="804,-956 804,-977 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1554360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="804,-920 804,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24343@0" ObjectIDZND0="24336@1" Pin0InfoVect0LinkObjId="SW-132399_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132401_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="804,-920 804,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15545c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="804,-732 804,-713 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24344@1" ObjectIDZND0="24337@1" Pin0InfoVect0LinkObjId="SW-132413_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132415_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="804,-732 804,-713 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1554820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="804,-686 804,-670 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24337@0" ObjectIDZND0="24345@1" Pin0InfoVect0LinkObjId="SW-132415_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132413_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="804,-686 804,-670 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1554a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="804,-653 804,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24345@0" ObjectIDZND0="24327@0" Pin0InfoVect0LinkObjId="g_15558d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132415_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="804,-653 804,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1554ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="458,-642 458,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24304@0" ObjectIDZND0="24326@0" Pin0InfoVect0LinkObjId="g_1553ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132694_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="458,-642 458,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1554f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="458,-651 458,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24304@1" ObjectIDZND0="24333@0" Pin0InfoVect0LinkObjId="SW-133007_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132694_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="458,-651 458,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15551a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="458,-681 458,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24333@1" ObjectIDZND0="24303@1" Pin0InfoVect0LinkObjId="SW-132694_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133007_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="458,-681 458,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1555400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="458,-697 458,-707 593,-707 593,-693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="24303@0" ObjectIDZND0="24307@0" Pin0InfoVect0LinkObjId="SW-132737_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132694_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="458,-697 458,-707 593,-707 593,-693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1555670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="593,-677 593,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="24307@1" ObjectIDZND0="24308@1" Pin0InfoVect0LinkObjId="SW-132737_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132737_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="593,-677 593,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15558d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="593,-647 593,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24308@0" ObjectIDZND0="24327@0" Pin0InfoVect0LinkObjId="g_1554a80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132737_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="593,-647 593,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1555b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="316,-1234 316,-1246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="24252@1" ObjectIDZND0="g_1d2f0d0@0" Pin0InfoVect0LinkObjId="g_1d2f0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132303_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="316,-1234 316,-1246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e28dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="484,-977 484,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24325@0" ObjectIDZND0="24309@1" Pin0InfoVect0LinkObjId="SW-132748_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15526e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="484,-977 484,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e296f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="764,-756 804,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_1d3b8b0@0" ObjectIDZND0="24344@x" ObjectIDZND1="24317@x" Pin0InfoVect0LinkObjId="SW-132415_0" Pin0InfoVect1LinkObjId="g_1d3c660_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d3b8b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="764,-756 804,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e298e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="804,-756 804,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_1d3b8b0@0" ObjectIDND1="24317@x" ObjectIDZND0="24344@0" Pin0InfoVect0LinkObjId="SW-132415_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d3b8b0_0" Pin1InfoVect1LinkObjId="g_1d3c660_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="804,-756 804,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d99b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-1181 726,-1181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="24248@0" ObjectIDZND0="24244@x" ObjectIDZND1="24245@x" ObjectIDZND2="g_1d338a0@0" Pin0InfoVect0LinkObjId="SW-132275_0" Pin0InfoVect1LinkObjId="SW-132277_0" Pin0InfoVect2LinkObjId="g_1d338a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="713,-1181 726,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d9ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-1160 726,-1181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="24244@1" ObjectIDZND0="24248@x" ObjectIDZND1="24245@x" ObjectIDZND2="g_1d338a0@0" Pin0InfoVect0LinkObjId="SW-132280_0" Pin0InfoVect1LinkObjId="SW-132277_0" Pin0InfoVect2LinkObjId="g_1d338a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132275_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="726,-1160 726,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d9d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-1191 763,-1191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="24248@x" ObjectIDND1="24244@x" ObjectIDND2="37797@1" ObjectIDZND0="24245@x" ObjectIDZND1="g_1d338a0@0" Pin0InfoVect0LinkObjId="SW-132277_0" Pin0InfoVect1LinkObjId="g_1d338a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-132280_0" Pin1InfoVect1LinkObjId="SW-132275_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="726,-1191 763,-1191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14db340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="222,-1172 208,-1172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24253@1" ObjectIDZND0="g_14d9f80@0" Pin0InfoVect0LinkObjId="g_14d9f80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132304_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="222,-1172 208,-1172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14db5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="663,-1181 677,-1181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_14da8b0@0" ObjectIDZND0="24248@1" Pin0InfoVect0LinkObjId="SW-132280_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14da8b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="663,-1181 677,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14dcd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="675,-1112 661,-1112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24247@1" ObjectIDZND0="g_14db800@0" Pin0InfoVect0LinkObjId="g_14db800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132279_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="675,-1112 661,-1112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14dcf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="675,-1049 661,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24246@1" ObjectIDZND0="g_14dc290@0" Pin0InfoVect0LinkObjId="g_14dc290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132278_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="675,-1049 661,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14dd1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="276,-1032 276,-1067 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24250@1" ObjectIDZND0="24249@0" Pin0InfoVect0LinkObjId="SW-132299_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132301_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="276,-1032 276,-1067 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14dd440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="276,-1094 276,-1111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24249@1" ObjectIDZND0="24251@0" Pin0InfoVect0LinkObjId="SW-132302_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132299_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="276,-1094 276,-1111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14dd6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="763,-1234 763,-1250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="24245@1" ObjectIDZND0="g_1d2fa50@0" Pin0InfoVect0LinkObjId="g_1d2fa50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132277_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="763,-1234 763,-1250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14de390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="472,-896 484,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24310@0" ObjectIDZND0="24309@x" ObjectIDZND1="g_2e27bb0@0" ObjectIDZND2="g_1d3e5c0@0" Pin0InfoVect0LinkObjId="SW-132748_0" Pin0InfoVect1LinkObjId="g_2e27bb0_0" Pin0InfoVect2LinkObjId="g_1d3e5c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="472,-896 484,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14de5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="484,-907 484,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24309@0" ObjectIDZND0="24310@x" ObjectIDZND1="g_2e27bb0@0" ObjectIDZND2="g_1d3e5c0@0" Pin0InfoVect0LinkObjId="SW-132750_0" Pin0InfoVect1LinkObjId="g_2e27bb0_0" Pin0InfoVect2LinkObjId="g_1d3e5c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132748_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="484,-907 484,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14de850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="436,-896 421,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24310@1" ObjectIDZND0="g_14dd900@0" Pin0InfoVect0LinkObjId="g_14dd900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132750_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="436,-896 421,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d8d9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-240,-611 -240,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24258@0" ObjectIDZND0="24326@0" Pin0InfoVect0LinkObjId="g_1553ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132472_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-240,-611 -240,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d8dc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-240,-580 -240,-594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24255@1" ObjectIDZND0="24258@1" Pin0InfoVect0LinkObjId="SW-132472_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132468_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-240,-580 -240,-594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d8de80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-240,-552 -240,-541 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24255@0" ObjectIDZND0="24259@1" Pin0InfoVect0LinkObjId="SW-132472_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132468_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-240,-552 -240,-541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d91370">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-251,-502 -240,-502 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="24257@1" ObjectIDZND0="g_14e3ba0@0" ObjectIDZND1="24259@x" Pin0InfoVect0LinkObjId="g_14e3ba0_0" Pin0InfoVect1LinkObjId="SW-132472_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132471_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-251,-502 -240,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d915d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-287,-502 -301,-502 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24257@0" ObjectIDZND0="g_1d908e0@0" Pin0InfoVect0LinkObjId="g_1d908e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132471_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-287,-502 -301,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d91830">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-240,-442 -240,-424 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_14e3ba0@1" ObjectIDZND0="24256@1" Pin0InfoVect0LinkObjId="SW-132470_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14e3ba0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-240,-442 -240,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d91a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-240,-502 -240,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="24257@x" ObjectIDND1="24259@x" ObjectIDZND0="g_14e3ba0@0" Pin0InfoVect0LinkObjId="g_14e3ba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-132471_0" Pin1InfoVect1LinkObjId="SW-132472_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-240,-502 -240,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d91cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-240,-523 -240,-502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="24259@0" ObjectIDZND0="24257@x" ObjectIDZND1="g_14e3ba0@0" Pin0InfoVect0LinkObjId="SW-132471_0" Pin0InfoVect1LinkObjId="g_14e3ba0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132472_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-240,-523 -240,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d92650">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-206,-349 -206,-364 -240,-364 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1d8cd10@0" ObjectIDZND0="24256@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-132470_0" Pin0InfoVect1LinkObjId="g_1dbc180_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d8cd10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-206,-349 -206,-364 -240,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d928b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-240,-388 -240,-364 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24256@0" ObjectIDZND0="g_1d8cd10@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1d8cd10_0" Pin0InfoVect1LinkObjId="g_1dbc180_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-240,-388 -240,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d92b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-240,-364 -240,-338 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1d8cd10@0" ObjectIDND1="24256@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1dbc180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d8cd10_0" Pin1InfoVect1LinkObjId="SW-132470_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-240,-364 -240,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d9ecb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-95,-608 -95,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24263@0" ObjectIDZND0="24326@0" Pin0InfoVect0LinkObjId="g_1553ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132495_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-95,-608 -95,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d9ef10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-95,-577 -95,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24260@1" ObjectIDZND0="24263@1" Pin0InfoVect0LinkObjId="SW-132495_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132491_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-95,-577 -95,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d9f170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-95,-551 -95,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24260@0" ObjectIDZND0="24264@1" Pin0InfoVect0LinkObjId="SW-132495_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132491_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-95,-551 -95,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1da2660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-106,-500 -95,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="24262@1" ObjectIDZND0="g_1d9ad00@0" ObjectIDZND1="24264@x" Pin0InfoVect0LinkObjId="g_1d9ad00_0" Pin0InfoVect1LinkObjId="SW-132495_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132494_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-106,-500 -95,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1da28c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-142,-500 -156,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24262@0" ObjectIDZND0="g_1da1bd0@0" Pin0InfoVect0LinkObjId="g_1da1bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132494_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-142,-500 -156,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1da2b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-95,-441 -95,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d9ad00@1" ObjectIDZND0="24261@1" Pin0InfoVect0LinkObjId="SW-132493_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d9ad00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-95,-441 -95,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1da2d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-95,-500 -95,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="24262@x" ObjectIDND1="24264@x" ObjectIDZND0="g_1d9ad00@0" Pin0InfoVect0LinkObjId="g_1d9ad00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-132494_0" Pin1InfoVect1LinkObjId="SW-132495_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-95,-500 -95,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1da2fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-95,-522 -95,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="24264@0" ObjectIDZND0="24262@x" ObjectIDZND1="g_1d9ad00@0" Pin0InfoVect0LinkObjId="SW-132494_0" Pin0InfoVect1LinkObjId="g_1d9ad00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132495_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-95,-522 -95,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1da3960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-61,-347 -61,-362 -95,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1d9df80@0" ObjectIDZND0="24261@x" ObjectIDZND1="34301@x" Pin0InfoVect0LinkObjId="SW-132493_0" Pin0InfoVect1LinkObjId="EC-MD_XJ.064Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d9df80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-61,-347 -61,-362 -95,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1da3bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-95,-386 -95,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24261@0" ObjectIDZND0="g_1d9df80@0" ObjectIDZND1="34301@x" Pin0InfoVect0LinkObjId="g_1d9df80_0" Pin0InfoVect1LinkObjId="EC-MD_XJ.064Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132493_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-95,-386 -95,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1da3e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-95,-362 -95,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1d9df80@0" ObjectIDND1="24261@x" ObjectIDZND0="34301@0" Pin0InfoVect0LinkObjId="EC-MD_XJ.064Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d9df80_0" Pin1InfoVect1LinkObjId="SW-132493_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-95,-362 -95,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1daffc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="50,-610 50,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24268@0" ObjectIDZND0="24326@0" Pin0InfoVect0LinkObjId="g_1553ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132518_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="50,-610 50,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1db0220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="50,-579 50,-593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24265@1" ObjectIDZND0="24268@1" Pin0InfoVect0LinkObjId="SW-132518_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132514_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="50,-579 50,-593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1db0480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="50,-552 50,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24265@0" ObjectIDZND0="24269@1" Pin0InfoVect0LinkObjId="SW-132518_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132514_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="50,-552 50,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1db3970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="39,-501 50,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="24267@1" ObjectIDZND0="g_1dac010@0" ObjectIDZND1="g_1d41b00@0" ObjectIDZND2="24269@x" Pin0InfoVect0LinkObjId="g_1dac010_0" Pin0InfoVect1LinkObjId="g_1d41b00_0" Pin0InfoVect2LinkObjId="SW-132518_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132517_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="39,-501 50,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1db3bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3,-501 -11,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24267@0" ObjectIDZND0="g_1db2ee0@0" Pin0InfoVect0LinkObjId="g_1db2ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132517_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3,-501 -11,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1db3e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="50,-441 50,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1dac010@1" ObjectIDZND0="24266@1" Pin0InfoVect0LinkObjId="SW-132516_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dac010_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="50,-441 50,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1db4090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="50,-501 50,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="24267@x" ObjectIDND1="g_1d41b00@0" ObjectIDND2="24269@x" ObjectIDZND0="g_1dac010@0" Pin0InfoVect0LinkObjId="g_1dac010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-132517_0" Pin1InfoVect1LinkObjId="g_1d41b00_0" Pin1InfoVect2LinkObjId="SW-132518_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="50,-501 50,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1db4a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="84,-348 84,-363 50,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1daf290@0" ObjectIDZND0="24266@x" ObjectIDZND1="34300@x" Pin0InfoVect0LinkObjId="SW-132516_0" Pin0InfoVect1LinkObjId="EC-MD_XJ.063Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1daf290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="84,-348 84,-363 50,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1db4c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="50,-387 50,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24266@0" ObjectIDZND0="g_1daf290@0" ObjectIDZND1="34300@x" Pin0InfoVect0LinkObjId="g_1daf290_0" Pin0InfoVect1LinkObjId="EC-MD_XJ.063Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132516_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="50,-387 50,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1db4ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="50,-363 50,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1daf290@0" ObjectIDND1="24266@x" ObjectIDZND0="34300@0" Pin0InfoVect0LinkObjId="EC-MD_XJ.063Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1daf290_0" Pin1InfoVect1LinkObjId="SW-132516_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="50,-363 50,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1db7f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="87,-523 87,-513 50,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1d41b00@0" ObjectIDZND0="24269@x" ObjectIDZND1="24267@x" ObjectIDZND2="g_1dac010@0" Pin0InfoVect0LinkObjId="SW-132518_0" Pin0InfoVect1LinkObjId="SW-132517_0" Pin0InfoVect2LinkObjId="g_1dac010_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d41b00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="87,-523 87,-513 50,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1db8190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="50,-522 50,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="24269@0" ObjectIDZND0="g_1d41b00@0" ObjectIDZND1="24267@x" ObjectIDZND2="g_1dac010@0" Pin0InfoVect0LinkObjId="g_1d41b00_0" Pin0InfoVect1LinkObjId="SW-132517_0" Pin0InfoVect2LinkObjId="g_1dac010_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132518_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="50,-522 50,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1db83f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="50,-513 50,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1d41b00@0" ObjectIDND1="24269@x" ObjectIDZND0="24267@x" ObjectIDZND1="g_1dac010@0" Pin0InfoVect0LinkObjId="SW-132517_0" Pin0InfoVect1LinkObjId="g_1dac010_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d41b00_0" Pin1InfoVect1LinkObjId="SW-132518_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="50,-513 50,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dcad60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-610 336,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24273@0" ObjectIDZND0="24326@0" Pin0InfoVect0LinkObjId="g_1553ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132541_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="336,-610 336,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dce250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="325,-501 336,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="24272@1" ObjectIDZND0="g_1dc6db0@0" ObjectIDZND1="24274@x" Pin0InfoVect0LinkObjId="g_1dc6db0_0" Pin0InfoVect1LinkObjId="SW-132541_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132540_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="325,-501 336,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dce4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="289,-501 275,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24272@0" ObjectIDZND0="g_1dcd7c0@0" Pin0InfoVect0LinkObjId="g_1dcd7c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="289,-501 275,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dce710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-441 336,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1dc6db0@1" ObjectIDZND0="24271@1" Pin0InfoVect0LinkObjId="SW-132539_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dc6db0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="336,-441 336,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dce970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-501 336,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="24272@x" ObjectIDND1="24274@x" ObjectIDZND0="g_1dc6db0@0" Pin0InfoVect0LinkObjId="g_1dc6db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-132540_0" Pin1InfoVect1LinkObjId="SW-132541_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="336,-501 336,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dcebd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-522 336,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="24274@0" ObjectIDZND0="24272@x" ObjectIDZND1="g_1dc6db0@0" Pin0InfoVect0LinkObjId="SW-132540_0" Pin0InfoVect1LinkObjId="g_1dc6db0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132541_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="336,-522 336,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dcf550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="370,-348 370,-363 336,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1dca030@0" ObjectIDZND0="24271@x" ObjectIDZND1="34299@x" Pin0InfoVect0LinkObjId="SW-132539_0" Pin0InfoVect1LinkObjId="EC-MD_XJ.062Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dca030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="370,-348 370,-363 336,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dcf7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-387 336,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24271@0" ObjectIDZND0="g_1dca030@0" ObjectIDZND1="34299@x" Pin0InfoVect0LinkObjId="g_1dca030_0" Pin0InfoVect1LinkObjId="EC-MD_XJ.062Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132539_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="336,-387 336,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dcfa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-363 336,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1dca030@0" ObjectIDND1="24271@x" ObjectIDZND0="34299@0" Pin0InfoVect0LinkObjId="EC-MD_XJ.062Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1dca030_0" Pin1InfoVect1LinkObjId="SW-132539_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="336,-363 336,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1de95c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-610 607,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24330@0" ObjectIDZND0="24327@0" Pin0InfoVect0LinkObjId="g_1554a80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133005_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="607,-610 607,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1de9820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-579 607,-593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24281@1" ObjectIDZND0="24330@1" Pin0InfoVect0LinkObjId="SW-133005_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132583_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="607,-579 607,-593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1de9a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-552 607,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24281@0" ObjectIDZND0="24331@1" Pin0InfoVect0LinkObjId="SW-133005_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132583_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="607,-552 607,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17bcf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="595,-501 607,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="24329@1" ObjectIDZND0="g_1de5610@0" ObjectIDZND1="24331@x" ObjectIDZND2="g_1d474f0@0" Pin0InfoVect0LinkObjId="g_1de5610_0" Pin0InfoVect1LinkObjId="SW-133005_0" Pin0InfoVect2LinkObjId="g_1d474f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133004_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="595,-501 607,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17bd170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="559,-501 545,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24329@0" ObjectIDZND0="g_17bc480@0" Pin0InfoVect0LinkObjId="g_17bc480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133004_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="559,-501 545,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17bd3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-441 607,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1de5610@1" ObjectIDZND0="24328@1" Pin0InfoVect0LinkObjId="SW-133003_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1de5610_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="607,-441 607,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17bd630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-501 607,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="24329@x" ObjectIDND1="24331@x" ObjectIDND2="g_1d474f0@0" ObjectIDZND0="g_1de5610@0" Pin0InfoVect0LinkObjId="g_1de5610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-133004_0" Pin1InfoVect1LinkObjId="SW-133005_0" Pin1InfoVect2LinkObjId="g_1d474f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="607,-501 607,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17bdfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="640,-348 640,-363 607,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1de8890@0" ObjectIDZND0="24328@x" ObjectIDZND1="34302@x" Pin0InfoVect0LinkObjId="SW-133003_0" Pin0InfoVect1LinkObjId="EC-MD_XJ.071Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1de8890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="640,-348 640,-363 607,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17be210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-387 607,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24328@0" ObjectIDZND0="g_1de8890@0" ObjectIDZND1="34302@x" Pin0InfoVect0LinkObjId="g_1de8890_0" Pin0InfoVect1LinkObjId="EC-MD_XJ.071Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133003_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="607,-387 607,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17be470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-363 607,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1de8890@0" ObjectIDND1="24328@x" ObjectIDZND0="34302@0" Pin0InfoVect0LinkObjId="EC-MD_XJ.071Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1de8890_0" Pin1InfoVect1LinkObjId="SW-133003_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="607,-363 607,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17c14d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="643,-523 643,-513 607,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1d474f0@0" ObjectIDZND0="24331@x" ObjectIDZND1="24329@x" ObjectIDZND2="g_1de5610@0" Pin0InfoVect0LinkObjId="SW-133005_0" Pin0InfoVect1LinkObjId="SW-133004_0" Pin0InfoVect2LinkObjId="g_1de5610_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d474f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="643,-523 643,-513 607,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17c1730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-522 607,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="24331@0" ObjectIDZND0="24329@x" ObjectIDZND1="g_1de5610@0" ObjectIDZND2="g_1d474f0@0" Pin0InfoVect0LinkObjId="SW-133004_0" Pin0InfoVect1LinkObjId="g_1de5610_0" Pin0InfoVect2LinkObjId="g_1d474f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133005_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="607,-522 607,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17c1990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-513 607,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="24331@x" ObjectIDND1="g_1d474f0@0" ObjectIDZND0="24329@x" ObjectIDZND1="g_1de5610@0" Pin0InfoVect0LinkObjId="SW-133004_0" Pin0InfoVect1LinkObjId="g_1de5610_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-133005_0" Pin1InfoVect1LinkObjId="g_1d474f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="607,-513 607,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17d4300">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="899,-608 899,-631 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24286@0" ObjectIDZND0="24327@0" Pin0InfoVect0LinkObjId="g_1554a80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132607_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="899,-608 899,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17d4560">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="899,-577 899,-592 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24283@1" ObjectIDZND0="24286@1" Pin0InfoVect0LinkObjId="SW-132607_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132603_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="899,-577 899,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17d47c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="899,-551 899,-538 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24283@0" ObjectIDZND0="24287@1" Pin0InfoVect0LinkObjId="SW-132607_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132603_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="899,-551 899,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17d7cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="888,-500 900,-500 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="24285@1" ObjectIDZND0="g_17d0350@0" ObjectIDZND1="24287@x" Pin0InfoVect0LinkObjId="g_17d0350_0" Pin0InfoVect1LinkObjId="SW-132607_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132606_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="888,-500 900,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17d7f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="852,-500 838,-500 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24285@0" ObjectIDZND0="g_17d7220@0" Pin0InfoVect0LinkObjId="g_17d7220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132606_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="852,-500 838,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17d8170">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="899,-441 899,-422 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_17d0350@1" ObjectIDZND0="24284@1" Pin0InfoVect0LinkObjId="SW-132605_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17d0350_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="899,-441 899,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17d83d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="899,-500 899,-485 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="24285@x" ObjectIDND1="24287@x" ObjectIDZND0="g_17d0350@0" Pin0InfoVect0LinkObjId="g_17d0350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-132606_0" Pin1InfoVect1LinkObjId="SW-132607_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="899,-500 899,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17d8630">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="899,-522 899,-500 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="24287@0" ObjectIDZND0="24285@x" ObjectIDZND1="g_17d0350@0" Pin0InfoVect0LinkObjId="SW-132606_0" Pin0InfoVect1LinkObjId="g_17d0350_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132607_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="899,-522 899,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17d8f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="933,-347 933,-362 899,-362 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_17d35d0@0" ObjectIDZND0="24284@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-132605_0" Pin0InfoVect1LinkObjId="g_1dbc180_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17d35d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="933,-347 933,-362 899,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17d91f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="899,-386 899,-362 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24284@0" ObjectIDZND0="g_17d35d0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_17d35d0_0" Pin0InfoVect1LinkObjId="g_1dbc180_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132605_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="899,-386 899,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17d9450">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="899,-362 899,-336 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_17d35d0@0" ObjectIDND1="24284@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1dbc180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_17d35d0_0" Pin1InfoVect1LinkObjId="SW-132605_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="899,-362 899,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17e55f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1046,-608 1046,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24290@0" ObjectIDZND0="24327@0" Pin0InfoVect0LinkObjId="g_1554a80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132629_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1046,-608 1046,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17e5850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1046,-577 1046,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24288@1" ObjectIDZND0="24290@1" Pin0InfoVect0LinkObjId="SW-132629_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132626_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1046,-577 1046,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17e5ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1046,-551 1046,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24288@0" ObjectIDZND0="24291@1" Pin0InfoVect0LinkObjId="SW-132629_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132626_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1046,-551 1046,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17e8fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1035,-500 1047,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="24332@1" ObjectIDZND0="g_17e1640@0" ObjectIDZND1="24291@x" ObjectIDZND2="g_1d49ef0@0" Pin0InfoVect0LinkObjId="g_17e1640_0" Pin0InfoVect1LinkObjId="SW-132629_0" Pin0InfoVect2LinkObjId="g_1d49ef0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133006_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1035,-500 1047,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17e9200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="999,-500 985,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24332@0" ObjectIDZND0="g_17e8510@0" Pin0InfoVect0LinkObjId="g_17e8510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133006_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="999,-500 985,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17e9460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1046,-441 1046,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_17e1640@1" ObjectIDZND0="24289@1" Pin0InfoVect0LinkObjId="SW-132628_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17e1640_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1046,-441 1046,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17e96c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1046,-500 1046,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="24332@x" ObjectIDND1="24291@x" ObjectIDND2="g_1d49ef0@0" ObjectIDZND0="g_17e1640@0" Pin0InfoVect0LinkObjId="g_17e1640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-133006_0" Pin1InfoVect1LinkObjId="SW-132629_0" Pin1InfoVect2LinkObjId="g_1d49ef0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1046,-500 1046,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17ea040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1046,-386 1046,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="24289@0" ObjectIDZND0="34303@x" ObjectIDZND1="g_17e48c0@0" Pin0InfoVect0LinkObjId="EC-MD_XJ.073Ld_0" Pin0InfoVect1LinkObjId="g_17e48c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132628_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1046,-386 1046,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17ea2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1046,-362 1046,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24289@x" ObjectIDND1="g_17e48c0@0" ObjectIDZND0="34303@0" Pin0InfoVect0LinkObjId="EC-MD_XJ.073Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-132628_0" Pin1InfoVect1LinkObjId="g_17e48c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1046,-362 1046,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17ed300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1073,-522 1073,-512 1046,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1d49ef0@0" ObjectIDZND0="24291@x" ObjectIDZND1="24332@x" ObjectIDZND2="g_17e1640@0" Pin0InfoVect0LinkObjId="SW-132629_0" Pin0InfoVect1LinkObjId="SW-133006_0" Pin0InfoVect2LinkObjId="g_17e1640_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d49ef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1073,-522 1073,-512 1046,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17ed560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1046,-522 1046,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="24291@0" ObjectIDZND0="24332@x" ObjectIDZND1="g_17e1640@0" ObjectIDZND2="g_1d49ef0@0" Pin0InfoVect0LinkObjId="SW-133006_0" Pin0InfoVect1LinkObjId="g_17e1640_0" Pin0InfoVect2LinkObjId="g_1d49ef0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132629_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1046,-522 1046,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17ed7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1046,-512 1046,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="24291@x" ObjectIDND1="g_1d49ef0@0" ObjectIDZND0="24332@x" ObjectIDZND1="g_17e1640@0" Pin0InfoVect0LinkObjId="SW-133006_0" Pin0InfoVect1LinkObjId="g_17e1640_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-132629_0" Pin1InfoVect1LinkObjId="g_1d49ef0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1046,-512 1046,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17f34a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-631 1176,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24327@0" ObjectIDZND0="24296@0" Pin0InfoVect0LinkObjId="SW-132653_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1554a80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-631 1176,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17f3700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-593 1176,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24296@1" ObjectIDZND0="24292@1" Pin0InfoVect0LinkObjId="SW-132648_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132653_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-593 1176,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17f3960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-559 1176,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24292@0" ObjectIDZND0="24297@1" Pin0InfoVect0LinkObjId="SW-132653_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132648_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-559 1176,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1804c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-608 1312,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24301@0" ObjectIDZND0="24327@0" Pin0InfoVect0LinkObjId="g_1554a80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132675_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-608 1312,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1804ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-577 1312,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24298@1" ObjectIDZND0="24301@1" Pin0InfoVect0LinkObjId="SW-132675_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132671_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-577 1312,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1805150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-550 1312,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24298@0" ObjectIDZND0="24302@1" Pin0InfoVect0LinkObjId="SW-132675_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132671_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-550 1312,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1808640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1301,-499 1312,-499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="24300@1" ObjectIDZND0="g_1800ce0@0" ObjectIDZND1="24302@x" Pin0InfoVect0LinkObjId="g_1800ce0_0" Pin0InfoVect1LinkObjId="SW-132675_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132674_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1301,-499 1312,-499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18088a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1265,-499 1251,-499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24300@0" ObjectIDZND0="g_1807bb0@0" Pin0InfoVect0LinkObjId="g_1807bb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132674_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1265,-499 1251,-499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1808b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-439 1312,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1800ce0@1" ObjectIDZND0="24299@1" Pin0InfoVect0LinkObjId="SW-132673_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1800ce0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-439 1312,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1808d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-499 1312,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="24300@x" ObjectIDND1="24302@x" ObjectIDZND0="g_1800ce0@0" Pin0InfoVect0LinkObjId="g_1800ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-132674_0" Pin1InfoVect1LinkObjId="SW-132675_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-499 1312,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1808fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-520 1312,-499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="24302@0" ObjectIDZND0="24300@x" ObjectIDZND1="g_1800ce0@0" Pin0InfoVect0LinkObjId="SW-132674_0" Pin0InfoVect1LinkObjId="g_1800ce0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132675_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-520 1312,-499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1809940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1346,-347 1346,-361 1312,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1803f60@0" ObjectIDZND0="24299@x" ObjectIDZND1="34304@x" Pin0InfoVect0LinkObjId="SW-132673_0" Pin0InfoVect1LinkObjId="EC-MD_XJ.075Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1803f60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1346,-347 1346,-361 1312,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1809ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-385 1312,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24299@0" ObjectIDZND0="g_1803f60@0" ObjectIDZND1="34304@x" Pin0InfoVect0LinkObjId="g_1803f60_0" Pin0InfoVect1LinkObjId="EC-MD_XJ.075Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132673_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-385 1312,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1809e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-361 1312,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1803f60@0" ObjectIDND1="24299@x" ObjectIDZND0="34304@0" Pin0InfoVect0LinkObjId="EC-MD_XJ.075Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1803f60_0" Pin1InfoVect1LinkObjId="SW-132673_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-361 1312,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_180fc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1418,-554 1418,-531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="24306@0" ObjectIDZND0="24339@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132736_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1418,-554 1418,-531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1813a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="414,-460 414,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1812f80@0" ObjectIDZND0="24277@1" Pin0InfoVect0LinkObjId="SW-132563_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1812f80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="414,-460 414,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1814700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1140,-455 1140,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1813c70@0" ObjectIDZND0="24295@1" Pin0InfoVect0LinkObjId="SW-132652_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1813c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1140,-455 1140,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d0e410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="335,-1187 314,-1187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_1d32b30@0" ObjectIDZND0="24253@x" ObjectIDZND1="24251@x" ObjectIDZND2="37801@1" Pin0InfoVect0LinkObjId="SW-132304_0" Pin0InfoVect1LinkObjId="SW-132302_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d32b30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="335,-1187 314,-1187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d0e600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="314,-1187 276,-1187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_1d32b30@0" ObjectIDND1="24252@x" ObjectIDZND0="24253@x" ObjectIDZND1="24251@x" ObjectIDZND2="37801@1" Pin0InfoVect0LinkObjId="SW-132304_0" Pin0InfoVect1LinkObjId="SW-132302_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d32b30_0" Pin1InfoVect1LinkObjId="SW-132303_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="314,-1187 276,-1187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d0e7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="316,-1198 316,-1187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="24252@0" ObjectIDZND0="24253@x" ObjectIDZND1="24251@x" ObjectIDZND2="37801@1" Pin0InfoVect0LinkObjId="SW-132304_0" Pin0InfoVect1LinkObjId="SW-132302_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132303_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="316,-1198 316,-1187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d0ef00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="185,-631 185,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24326@0" ObjectIDZND0="24311@0" Pin0InfoVect0LinkObjId="SW-132754_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1553ea0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="185,-631 185,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d0f150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="185,-586 185,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="24311@1" ObjectIDZND0="24312@1" Pin0InfoVect0LinkObjId="SW-132754_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132754_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="185,-586 185,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d0f3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="226,-522 226,-531 185,-531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1dbb450@0" ObjectIDZND0="24312@x" ObjectIDZND1="g_1d48c80@0" Pin0InfoVect0LinkObjId="SW-132754_0" Pin0InfoVect1LinkObjId="g_1d48c80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dbb450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="226,-522 226,-531 185,-531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d0fe80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="185,-551 185,-531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="24312@0" ObjectIDZND0="g_1dbb450@0" ObjectIDZND1="g_1d48c80@0" Pin0InfoVect0LinkObjId="g_1dbb450_0" Pin0InfoVect1LinkObjId="g_1d48c80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132754_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="185,-551 185,-531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d100c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="724,-602 724,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24313@0" ObjectIDZND0="24327@0" Pin0InfoVect0LinkObjId="g_1554a80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132759_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="724,-602 724,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d10840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="724,-585 724,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="24313@1" ObjectIDZND0="24314@1" Pin0InfoVect0LinkObjId="SW-132759_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132759_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="724,-585 724,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d10aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="724,-530 772,-530 771,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="24314@x" ObjectIDND1="g_1d48560@0" ObjectIDZND0="g_17c1bf0@0" Pin0InfoVect0LinkObjId="g_17c1bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-132759_0" Pin1InfoVect1LinkObjId="g_1d48560_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="724,-530 772,-530 771,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d11570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="724,-550 724,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="24314@0" ObjectIDZND0="g_17c1bf0@0" ObjectIDZND1="g_1d48560@0" Pin0InfoVect0LinkObjId="g_17c1bf0_0" Pin0InfoVect1LinkObjId="g_1d48560_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132759_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="724,-550 724,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d18290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-539 336,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24274@1" ObjectIDZND0="24270@0" Pin0InfoVect0LinkObjId="SW-132537_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132541_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="336,-539 336,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d18480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-579 336,-593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24270@1" ObjectIDZND0="24273@1" Pin0InfoVect0LinkObjId="SW-132541_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132537_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="336,-579 336,-593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d19c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="276,-996 276,-977 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24250@0" ObjectIDZND0="24325@0" Pin0InfoVect0LinkObjId="g_15526e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132301_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="276,-996 276,-977 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d1b4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,-884 524,-896 484,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2e27bb0@0" ObjectIDZND0="24310@x" ObjectIDZND1="24309@x" ObjectIDZND2="g_1d3e5c0@0" Pin0InfoVect0LinkObjId="SW-132750_0" Pin0InfoVect1LinkObjId="SW-132748_0" Pin0InfoVect2LinkObjId="g_1d3e5c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e27bb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="524,-884 524,-896 484,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d1be60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="763,-1198 763,-1191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="24245@0" ObjectIDZND0="g_1d338a0@0" ObjectIDZND1="24248@x" ObjectIDZND2="24244@x" Pin0InfoVect0LinkObjId="g_1d338a0_0" Pin0InfoVect1LinkObjId="SW-132280_0" Pin0InfoVect2LinkObjId="SW-132275_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132277_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="763,-1198 763,-1191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d1c050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="763,-1191 781,-1191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="24245@x" ObjectIDND1="24248@x" ObjectIDND2="24244@x" ObjectIDZND0="g_1d338a0@0" Pin0InfoVect0LinkObjId="g_1d338a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-132277_0" Pin1InfoVect1LinkObjId="SW-132280_0" Pin1InfoVect2LinkObjId="SW-132275_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="763,-1191 781,-1191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d1c260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1418,-631 1418,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24327@0" ObjectIDZND0="24305@0" Pin0InfoVect0LinkObjId="SW-132736_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1554a80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1418,-631 1418,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d1c490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1418,-586 1418,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="24305@1" ObjectIDZND0="24306@1" Pin0InfoVect0LinkObjId="SW-132736_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132736_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1418,-586 1418,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d1cca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="450,-622 450,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24279@0" ObjectIDZND0="24326@0" Pin0InfoVect0LinkObjId="g_1553ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132565_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="450,-622 450,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d1d460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="450,-605 450,-596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24279@1" ObjectIDZND0="24275@1" Pin0InfoVect0LinkObjId="SW-132560_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132565_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="450,-605 450,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d1d6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="450,-569 450,-560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24275@0" ObjectIDZND0="24280@1" Pin0InfoVect0LinkObjId="SW-132565_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="450,-569 450,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d2dbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-528 1176,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="24294@x" ObjectIDND1="g_17eda20@0" ObjectIDZND0="24297@0" Pin0InfoVect0LinkObjId="SW-132653_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-132651_0" Pin1InfoVect1LinkObjId="g_17eda20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-528 1176,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d31090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-1181 726,-1191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="24248@x" ObjectIDND1="24244@x" ObjectIDZND0="24245@x" ObjectIDZND1="g_1d338a0@0" ObjectIDZND2="37797@1" Pin0InfoVect0LinkObjId="SW-132277_0" Pin0InfoVect1LinkObjId="g_1d338a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-132280_0" Pin1InfoVect1LinkObjId="SW-132275_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="726,-1181 726,-1191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d31b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="276,-1172 276,-1187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="24253@x" ObjectIDND1="24251@x" ObjectIDZND0="g_1d32b30@0" ObjectIDZND1="24252@x" ObjectIDZND2="37801@1" Pin0InfoVect0LinkObjId="g_1d32b30_0" Pin0InfoVect1LinkObjId="SW-132303_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-132304_0" Pin1InfoVect1LinkObjId="SW-132302_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="276,-1172 276,-1187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d32670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="276,-1239 276,-1187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="37801@1" ObjectIDZND0="24253@x" ObjectIDZND1="24251@x" ObjectIDZND2="g_1d32b30@0" Pin0InfoVect0LinkObjId="SW-132304_0" Pin0InfoVect1LinkObjId="SW-132302_0" Pin0InfoVect2LinkObjId="g_1d32b30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="276,-1239 276,-1187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d328d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-1239 726,-1191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="37797@1" ObjectIDZND0="24245@x" ObjectIDZND1="g_1d338a0@0" ObjectIDZND2="24248@x" Pin0InfoVect0LinkObjId="SW-132277_0" Pin0InfoVect1LinkObjId="g_1d338a0_0" Pin0InfoVect2LinkObjId="SW-132280_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="726,-1239 726,-1191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d37310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="194,-756 241,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_1d38520@0" ObjectIDZND0="24342@x" ObjectIDZND1="24316@x" Pin0InfoVect0LinkObjId="SW-132345_0" Pin0InfoVect1LinkObjId="g_1d37a30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d38520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="194,-756 241,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d37570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="241,-756 241,-747 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_1d38520@0" ObjectIDND1="24316@x" ObjectIDZND0="24342@0" Pin0InfoVect0LinkObjId="SW-132345_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d38520_0" Pin1InfoVect1LinkObjId="g_1d37a30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="241,-756 241,-747 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d377d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="241,-926 241,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24340@0" ObjectIDZND0="24334@1" Pin0InfoVect0LinkObjId="SW-132329_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132331_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="241,-926 241,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d37a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="241,-872 241,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="24334@0" ObjectIDZND0="24316@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132329_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="241,-872 241,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d37c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="241,-766 241,-755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="24316@0" ObjectIDZND0="g_1d38520@0" ObjectIDZND1="24342@x" Pin0InfoVect0LinkObjId="g_1d38520_0" Pin0InfoVect1LinkObjId="SW-132345_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d37a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="241,-766 241,-755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d3c660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="804,-756 804,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_1d3b8b0@0" ObjectIDND1="24344@x" ObjectIDZND0="24317@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d3b8b0_0" Pin1InfoVect1LinkObjId="SW-132415_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="804,-756 804,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3c8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="804,-858 804,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="24317@1" ObjectIDZND0="24336@0" Pin0InfoVect0LinkObjId="SW-132399_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d3c660_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="804,-858 804,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3ee40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="484,-844 484,-855 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_1d3d800@0" ObjectIDZND0="g_1d3e5c0@1" Pin0InfoVect0LinkObjId="g_1d3e5c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d3d800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="484,-844 484,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3f0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="484,-886 484,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1d3e5c0@0" ObjectIDZND0="24310@x" ObjectIDZND1="24309@x" ObjectIDZND2="g_2e27bb0@0" Pin0InfoVect0LinkObjId="SW-132750_0" Pin0InfoVect1LinkObjId="SW-132748_0" Pin0InfoVect2LinkObjId="g_2e27bb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d3e5c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="484,-886 484,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d41640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="613,-977 613,-964 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24325@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1dbc180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15526e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="613,-977 613,-964 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d418a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="613,-912 613,-890 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="24338@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dbc180_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="613,-912 613,-890 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d43660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="450,-543 450,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="24280@0" ObjectIDZND0="24278@x" ObjectIDZND1="g_1dd4b40@0" Pin0InfoVect0LinkObjId="SW-132564_0" Pin0InfoVect1LinkObjId="g_1dd4b40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132565_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="450,-543 450,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d43850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="450,-529 450,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="24280@x" ObjectIDND1="24278@x" ObjectIDZND0="g_1dd4b40@0" Pin0InfoVect0LinkObjId="g_1dd4b40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-132565_0" Pin1InfoVect1LinkObjId="SW-132564_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="450,-529 450,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d46810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="450,-529 435,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="24280@x" ObjectIDND1="g_1dd4b40@0" ObjectIDZND0="24278@1" Pin0InfoVect0LinkObjId="SW-132564_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-132565_0" Pin1InfoVect1LinkObjId="g_1dd4b40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="450,-529 435,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d46a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="399,-529 389,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24278@0" ObjectIDZND0="g_1d43a40@0" Pin0InfoVect0LinkObjId="g_1d43a40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132564_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="399,-529 389,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d47300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="450,-458 450,-447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1dd4b40@1" ObjectIDZND0="24276@1" Pin0InfoVect0LinkObjId="SW-132562_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dd4b40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="450,-458 450,-447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d49570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="185,-531 185,-514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1dbb450@0" ObjectIDND1="24312@x" ObjectIDZND0="g_1d48c80@0" Pin0InfoVect0LinkObjId="g_1d48c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1dbb450_0" Pin1InfoVect1LinkObjId="SW-132754_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="185,-531 185,-514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d497d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="185,-482 185,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1d48c80@1" ObjectIDZND0="g_1dbc180@0" Pin0InfoVect0LinkObjId="g_1dbc180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d48c80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="185,-482 185,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d49a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="724,-530 724,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_17c1bf0@0" ObjectIDND1="24314@x" ObjectIDZND0="g_1d48560@0" Pin0InfoVect0LinkObjId="g_1d48560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_17c1bf0_0" Pin1InfoVect1LinkObjId="SW-132759_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="724,-530 724,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d49c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="724,-488 724,-475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1d48560@1" ObjectIDZND0="g_17c2920@0" Pin0InfoVect0LinkObjId="g_17c2920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d48560_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="724,-488 724,-475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d4fff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1111,-528 1123,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1d4d410@0" ObjectIDZND0="24294@0" Pin0InfoVect0LinkObjId="SW-132651_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d4d410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1111,-528 1123,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d50250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1159,-528 1176,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="24294@1" ObjectIDZND0="24297@x" ObjectIDZND1="g_17eda20@0" Pin0InfoVect0LinkObjId="SW-132653_0" Pin0InfoVect1LinkObjId="g_17eda20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132651_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1159,-528 1176,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d50ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-519 1176,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_17eda20@0" ObjectIDZND0="24297@x" ObjectIDZND1="24294@x" Pin0InfoVect0LinkObjId="SW-132653_0" Pin0InfoVect1LinkObjId="SW-132651_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17eda20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-519 1176,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d50cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-447 1176,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24293@1" ObjectIDZND0="g_17eda20@1" Pin0InfoVect0LinkObjId="g_17eda20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132650_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-447 1176,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d51b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="414,-421 414,-411 450,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="24277@0" ObjectIDZND0="24276@x" ObjectIDZND1="37765@x" Pin0InfoVect0LinkObjId="SW-132562_0" Pin0InfoVect1LinkObjId="CB-MD_XJ.MD_XJ_cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132563_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="414,-421 414,-411 450,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d524a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="450,-419 450,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="24276@0" ObjectIDZND0="24277@x" ObjectIDZND1="37765@x" Pin0InfoVect0LinkObjId="SW-132563_0" Pin0InfoVect1LinkObjId="CB-MD_XJ.MD_XJ_cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132562_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="450,-419 450,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d53cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="450,-411 450,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="24277@x" ObjectIDND1="24276@x" ObjectIDZND0="37765@0" Pin0InfoVect0LinkObjId="CB-MD_XJ.MD_XJ_cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-132563_0" Pin1InfoVect1LinkObjId="SW-132562_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="450,-411 450,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d56d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1080,-347 1080,-362 1046,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_17e48c0@0" ObjectIDZND0="24289@x" ObjectIDZND1="34303@x" Pin0InfoVect0LinkObjId="SW-132628_0" Pin0InfoVect1LinkObjId="EC-MD_XJ.073Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17e48c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1080,-347 1080,-362 1046,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d56f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-411 1176,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="24293@x" ObjectIDND1="24295@x" ObjectIDZND0="37785@0" Pin0InfoVect0LinkObjId="CB-MD_XJ.MD_XJ_cb2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-132650_0" Pin1InfoVect1LinkObjId="SW-132652_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-411 1176,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d578b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-419 1176,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="24293@0" ObjectIDZND0="24295@x" ObjectIDZND1="37785@x" Pin0InfoVect0LinkObjId="SW-132652_0" Pin0InfoVect1LinkObjId="CB-MD_XJ.MD_XJ_cb2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-419 1176,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d57b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-411 1140,-411 1140,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="24293@x" ObjectIDND1="37785@x" ObjectIDZND0="24295@0" Pin0InfoVect0LinkObjId="SW-132652_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-132650_0" Pin1InfoVect1LinkObjId="CB-MD_XJ.MD_XJ_cb2_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-411 1140,-411 1140,-416 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="24326" cx="241" cy="-631" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24326" cx="458" cy="-631" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24327" cx="804" cy="-631" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24327" cx="593" cy="-631" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24326" cx="-240" cy="-631" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24326" cx="-95" cy="-631" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24326" cx="50" cy="-631" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24326" cx="336" cy="-631" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24327" cx="607" cy="-631" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24327" cx="899" cy="-631" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24327" cx="1176" cy="-631" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24327" cx="1312" cy="-631" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24327" cx="1418" cy="-631" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24326" cx="185" cy="-631" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24327" cx="724" cy="-631" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24325" cx="726" cy="-977" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24325" cx="484" cy="-977" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24325" cx="276" cy="-977" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24327" cx="1046" cy="-631" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24326" cx="450" cy="-631" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24325" cx="241" cy="-977" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24325" cx="804" cy="-977" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24325" cx="613" cy="-977" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-132200" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -364.000000 -1229.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24218" ObjectName="DYN-MD_XJ"/>
     <cge:Meas_Ref ObjectId="132200"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1573bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -518.000000 -1310.500000) translate(0,16)">戌街变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e3a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -1169.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e3a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -1169.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e3a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -1169.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e3a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -1169.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e3a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -1169.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e3a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -1169.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e3a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -1169.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e3a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -1169.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15e3a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -1169.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2be16a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -731.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2be16a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -731.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2be16a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -731.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2be16a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -731.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2be16a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -731.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2be16a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -731.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2be16a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -731.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2be16a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -731.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2be16a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -731.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2be16a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -731.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2be16a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -731.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2be16a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -731.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2be16a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -731.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2be16a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -731.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2be16a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -731.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2be16a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -731.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2be16a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -731.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2be16a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -658.000000 -731.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6cbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6.000000 -965.000000) translate(0,12)">35kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6d0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -103.000000 -654.000000) translate(0,12)">10kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ca5d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 67.000000 -821.000000) translate(0,12)">SZ11-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ca5d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 67.000000 -821.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ca5d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 67.000000 -821.000000) translate(0,42)">yNd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ca5f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 635.000000 -1308.000000) translate(0,12)">35kV新戌安I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ca61a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 183.000000 -1313.000000) translate(0,12)">35kV新戌安II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e22830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 572.000000 -950.000000) translate(0,12)">3401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e288e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 437.000000 -940.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e28fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 431.000000 -915.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e29270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 440.000000 -816.000000) translate(0,12)">35kV电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e294c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1216.000000 -660.000000) translate(0,12)">10kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e29ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 138.000000 -580.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e29f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 682.500000 -583.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d5720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1374.000000 -568.000000) translate(0,12)">0761</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d5930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -261.000000 -284.000000) translate(0,12)">备用线一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d5d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -135.000000 -284.000000) translate(0,12)">工业园区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d6d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -284.000000) translate(0,12)">长箐线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d7590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 160.000000 -405.000000) translate(0,12)">10kVI母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d7810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 314.000000 -284.000000) translate(0,12)">左家线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d7a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 610.000000 -284.000000) translate(0,12)">大冲线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d8290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 707.000000 -426.000000) translate(0,12)">10kVⅡ母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d8510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 856.000000 -284.000000) translate(0,12)">备用线二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d8750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1031.000000 -284.000000) translate(0,12)">华戌线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d8c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1159.000000 -239.000000) translate(0,12)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d8ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1294.000000 -284.000000) translate(0,12)">水桥线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d9730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 485.000000 -697.000000) translate(0,12)">10kV分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1f7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -280.000000 -573.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1fc10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -573.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d20060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 9.000000 -574.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d204b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 296.000000 -575.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d20900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 408.000000 -588.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d20d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 569.000000 -572.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d211a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 859.000000 -573.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d215f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1000.000000 -573.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d21a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1133.000000 -573.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d21e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1266.000000 -573.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d221a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 684.000000 -1089.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d223e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 235.000000 -1087.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d228a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 422.000000 -676.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d27d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 233.000000 -1021.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d28360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 233.000000 -1136.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d285a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 219.000000 -1194.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d287e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 322.000000 -1223.000000) translate(0,12)">3629</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d28a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 733.000000 -1020.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d28c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 673.000000 -1071.000000) translate(0,12)">36117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d28ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 673.000000 -1134.000000) translate(0,12)">36160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d290e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 733.000000 -1149.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d29320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 675.000000 -1203.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d29560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 772.000000 -1224.000000) translate(0,12)">3619</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d297a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 251.000000 -893.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d299e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 245.000000 -950.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d29c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 251.000000 -712.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d29e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 814.000000 -889.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2a0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 811.000000 -945.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2a2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 814.000000 -707.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2a520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -292.000000 -528.000000) translate(0,12)">06527</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2a760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -283.000000 -410.000000) translate(0,12)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2a9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -144.000000 -526.000000) translate(0,12)">06427</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2abe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -146.000000 -410.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2ae20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 0.000000 -527.000000) translate(0,12)">06327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2b060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4.000000 -411.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2b2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 286.000000 -527.000000) translate(0,12)">06227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2b4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 288.000000 -412.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2b720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 366.000000 -428.000000) translate(0,12)">06167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2b960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 457.000000 -435.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2bba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 557.000000 -527.000000) translate(0,12)">07127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2bde0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 560.000000 -409.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2c020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 849.000000 -526.000000) translate(0,12)">07227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2c260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 850.000000 -410.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 997.000000 -526.000000) translate(0,12)">07327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1004.000000 -404.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2c920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1090.000000 -435.000000) translate(0,12)">07467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2cb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1182.000000 -431.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2cda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1263.000000 -525.000000) translate(0,12)">07527</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2cfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1269.000000 -409.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2d220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 550.000000 -677.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2df10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -649.000000 -947.000000) translate(0,12)">公共信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1d2e960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -273.000000 -1277.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1d2ede0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -273.000000 -1312.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d34610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 636.000000 -818.000000) translate(0,12)">SZ11-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d34610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 636.000000 -818.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d34610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 636.000000 -818.000000) translate(0,42)">yNd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d37ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 156.000000 -842.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d3cb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 722.000000 -841.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d428b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 67.000000 -505.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d46cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 397.000000 -557.000000) translate(0,12)">06127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d47f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 630.000000 -506.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4ac60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1064.000000 -506.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4cba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -436.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4d1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 631.000000 -853.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d504b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1121.000000 -553.000000) translate(0,12)">07427</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d51800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 399.000000 -218.000000) translate(0,12)">10kV1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1d5ba00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -368.000000 -1311.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1d640e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -672.000000 -325.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1d640e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -672.000000 -325.000000) translate(0,38)">心变运二班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d64320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -540.000000 -334.000000) translate(0,16)">13508785260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d64320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -540.000000 -334.000000) translate(0,36)">18787879001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d64320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -540.000000 -334.000000) translate(0,56)">18787879002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d64f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -540.000000 -381.000000) translate(0,16)">5331154</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d64f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -540.000000 -381.000000) translate(0,36)">2259</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d651f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -733.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d65400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -748.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d65640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -764.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d65880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -240.000000 -716.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d65ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -234.000000 -656.000000) translate(0,12)">F(Hz)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d65d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -254.000000 -701.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d65f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -254.000000 -685.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d66180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -254.000000 -670.000000) translate(0,12)">Uca(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d663c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1325.000000 -728.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d66600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1325.000000 -743.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d66840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1325.000000 -759.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d66a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1331.000000 -711.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d66cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1337.000000 -651.000000) translate(0,12)">F(Hz)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d66f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1317.000000 -696.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d67140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1317.000000 -680.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d67380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1317.000000 -665.000000) translate(0,12)">Uca(kV):</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="14" stroke="rgb(0,255,0)" stroke-width="1" width="27" x="402" y="-311"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="14" stroke="rgb(0,255,0)" stroke-width="1" width="27" x="1128" y="-340"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-MD_XJ.MD_XJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4,-977 1082,-977 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24325" ObjectName="BS-MD_XJ.MD_XJ_3IM"/>
    <cge:TPSR_Ref TObjectID="24325"/></metadata>
   <polyline fill="none" opacity="0" points="4,-977 1082,-977 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-MD_XJ.MD_XJ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-252,-631 494,-631 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24326" ObjectName="BS-MD_XJ.MD_XJ_9IM"/>
    <cge:TPSR_Ref TObjectID="24326"/></metadata>
   <polyline fill="none" opacity="0" points="-252,-631 494,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-MD_XJ.MD_XJ_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="567,-631 1440,-631 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24327" ObjectName="BS-MD_XJ.MD_XJ_9IIM"/>
    <cge:TPSR_Ref TObjectID="24327"/></metadata>
   <polyline fill="none" opacity="0" points="567,-631 1440,-631 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-MD_XJ.MD_XJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34318"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 202.000000 -761.000000)" xlink:href="#transformer2:shape54_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 202.000000 -761.000000)" xlink:href="#transformer2:shape54_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24316" ObjectName="TF-MD_XJ.MD_XJ_1T"/>
    <cge:TPSR_Ref TObjectID="24316"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-MD_XJ.MD_XJ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34322"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.000000 -761.000000)" xlink:href="#transformer2:shape54_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.000000 -761.000000)" xlink:href="#transformer2:shape54_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24317" ObjectName="TF-MD_XJ.MD_XJ_2T"/>
    <cge:TPSR_Ref TObjectID="24317"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-MD_XJ.MD_XJ_Zyb1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34349"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 582.000000 -797.000000)" xlink:href="#transformer2:shape42_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 582.000000 -797.000000)" xlink:href="#transformer2:shape42_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24338" ObjectName="TF-MD_XJ.MD_XJ_Zyb1"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-MD_XJ.MD_XJ_Zyb2">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34353"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1387.000000 -438.000000)" xlink:href="#transformer2:shape40_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1387.000000 -438.000000)" xlink:href="#transformer2:shape40_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24339" ObjectName="TF-MD_XJ.MD_XJ_Zyb2"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -567.000000 -1262.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-215175" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -546.000000 -1166.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215175" ObjectName="MD_XJ:MD_XJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-215175" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -546.000000 -1125.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215175" ObjectName="MD_XJ:MD_XJ_sumP"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-555" y="-1321"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-555" y="-1321"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-604" y="-1338"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-604" y="-1338"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="-282" y="-574"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="-282" y="-574"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="-137" y="-573"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="-137" y="-573"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="9" y="-574"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="9" y="-574"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="296" y="-575"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="296" y="-575"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="408" y="-588"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="408" y="-588"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="569" y="-572"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="569" y="-572"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="859" y="-573"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="859" y="-573"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1000" y="-573"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1000" y="-573"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1133" y="-573"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1133" y="-573"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1266" y="-573"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1266" y="-573"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="235" y="-1087"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="235" y="-1087"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="684" y="-1089"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="684" y="-1089"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="422" y="-676"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="422" y="-676"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="21" qtmmishow="hidden" width="63" x="-651" y="-950"/>
    </a>
   <metadata/><rect fill="white" height="21" opacity="0" stroke="white" transform="" width="63" x="-651" y="-950"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-284" y="-1285"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-284" y="-1285"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-284" y="-1320"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-284" y="-1320"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="156" y="-842"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="156" y="-842"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="722" y="-841"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="722" y="-841"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="-375,-1336 -378,-1339 -378,-1277 -375,-1280 -375,-1336" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="-375,-1336 -378,-1339 -314,-1339 -317,-1336 -375,-1336" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="-375,-1280 -378,-1277 -314,-1277 -317,-1280 -375,-1280" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="-317,-1336 -314,-1339 -314,-1277 -317,-1280 -317,-1336" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="56" stroke="rgb(255,255,255)" width="58" x="-375" y="-1336"/>
     <rect fill="none" height="56" qtmmishow="hidden" stroke="rgb(0,0,0)" width="58" x="-375" y="-1336"/>
    </a>
   <metadata/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d0d740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 309.000000 1100.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d0da00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 298.000000 1085.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d0dc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 323.000000 1070.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d0df70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 311.000000 812.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d0e1d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 311.000000 827.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d118c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 875.000000 806.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d11f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 875.000000 821.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d12820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.000000 1101.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d12ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 754.000000 1086.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d12d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 779.000000 1071.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1a4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 15.000000 1045.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1a9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 15.000000 1060.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1abf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 15.000000 1076.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1ae30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 21.000000 1028.250000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1b070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 27.000000 998.000000) translate(0,12)">F(Hz)</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1b2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 1013.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1e6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 471.000000 754.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1ed10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 460.000000 739.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1ef50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 485.000000 724.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d22eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 314.000000 859.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d231e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 310.000000 877.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d23420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 296.000000 907.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d23660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 285.000000 892.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d23990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 314.000000 683.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d23c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 310.000000 701.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d23e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 296.000000 731.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d24080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 285.000000 716.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d243b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 887.000000 859.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d24620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 883.000000 877.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d24860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 869.000000 907.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d24aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 858.000000 892.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d24dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 886.000000 683.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d25040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 882.000000 701.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d25280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 868.000000 731.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d254c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 857.000000 716.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d257f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -280.000000 177.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d25a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -280.000000 193.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d25ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -280.000000 211.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d25ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -294.000000 241.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d26120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -305.000000 225.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5cf00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -140.000000 178.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5d4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -140.000000 194.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5d720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -140.000000 212.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5d960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -154.000000 242.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5dba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -165.000000 226.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5ded0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 8.000000 178.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5e140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 8.000000 194.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5e380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 8.000000 212.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5e5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -6.000000 242.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5e800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -17.000000 226.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5eb30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 286.000000 181.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5eda0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 286.000000 197.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5efe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 286.000000 215.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5f220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 272.000000 245.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5f460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 261.000000 229.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5f790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 428.000000 132.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5fa00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 428.000000 148.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5fc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 428.000000 166.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5fe80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 414.000000 196.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d600c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 403.000000 180.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d603f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 572.000000 181.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d60660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 572.000000 197.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d608a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 572.000000 215.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d60ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 558.000000 245.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d60d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 547.000000 229.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d61050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 856.000000 182.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d612c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 856.000000 198.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d61500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 856.000000 216.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d61740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 842.000000 246.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d61980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 831.000000 230.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d61cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1015.000000 175.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d61f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1015.000000 191.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d62160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1015.000000 209.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d623a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1001.000000 239.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d625e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 990.000000 223.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d62910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1146.000000 142.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d62b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1146.000000 158.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d62dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1146.000000 176.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d63000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1132.000000 206.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d63240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1121.000000 190.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d63570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1278.000000 189.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d637e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1278.000000 205.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d63a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1278.000000 223.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d63c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1264.000000 253.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d63ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1253.000000 237.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d676b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 693.000000 1273.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="700" cy="1266" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-MD_XJ.MD_XJ_cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 431.000000 -240.000000)" xlink:href="#capacitor:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37765" ObjectName="CB-MD_XJ.MD_XJ_cb1"/>
    <cge:TPSR_Ref TObjectID="37765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-MD_XJ.MD_XJ_cb2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1157.000000 -269.000000)" xlink:href="#capacitor:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37785" ObjectName="CB-MD_XJ.MD_XJ_cb2"/>
    <cge:TPSR_Ref TObjectID="37785"/></metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-132854" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 367.000000 -1100.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132854" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24249"/>
     <cge:Term_Ref ObjectID="34182"/>
    <cge:TPSR_Ref TObjectID="24249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-132855" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 367.000000 -1100.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132855" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24249"/>
     <cge:Term_Ref ObjectID="34182"/>
    <cge:TPSR_Ref TObjectID="24249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-132848" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 367.000000 -1100.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132848" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24249"/>
     <cge:Term_Ref ObjectID="34182"/>
    <cge:TPSR_Ref TObjectID="24249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-132877" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 349.000000 -904.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132877" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24334"/>
     <cge:Term_Ref ObjectID="34339"/>
    <cge:TPSR_Ref TObjectID="24334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-132878" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 349.000000 -904.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132878" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24334"/>
     <cge:Term_Ref ObjectID="34339"/>
    <cge:TPSR_Ref TObjectID="24334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-132871" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 349.000000 -904.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132871" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24334"/>
     <cge:Term_Ref ObjectID="34339"/>
    <cge:TPSR_Ref TObjectID="24334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-133030" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 349.000000 -904.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133030" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24334"/>
     <cge:Term_Ref ObjectID="34339"/>
    <cge:TPSR_Ref TObjectID="24334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-132846" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 825.000000 -1101.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132846" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24242"/>
     <cge:Term_Ref ObjectID="34168"/>
    <cge:TPSR_Ref TObjectID="24242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-132847" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 825.000000 -1101.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132847" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24242"/>
     <cge:Term_Ref ObjectID="34168"/>
    <cge:TPSR_Ref TObjectID="24242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-132840" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 825.000000 -1101.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132840" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24242"/>
     <cge:Term_Ref ObjectID="34168"/>
    <cge:TPSR_Ref TObjectID="24242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-132891" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 925.000000 -906.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132891" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24336"/>
     <cge:Term_Ref ObjectID="34343"/>
    <cge:TPSR_Ref TObjectID="24336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-132892" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 925.000000 -906.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132892" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24336"/>
     <cge:Term_Ref ObjectID="34343"/>
    <cge:TPSR_Ref TObjectID="24336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-132888" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 925.000000 -906.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132888" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24336"/>
     <cge:Term_Ref ObjectID="34343"/>
    <cge:TPSR_Ref TObjectID="24336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-133039" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 925.000000 -906.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133039" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24336"/>
     <cge:Term_Ref ObjectID="34343"/>
    <cge:TPSR_Ref TObjectID="24336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-132908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -231.000000 -240.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24255"/>
     <cge:Term_Ref ObjectID="34194"/>
    <cge:TPSR_Ref TObjectID="24255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-132909" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -231.000000 -240.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132909" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24255"/>
     <cge:Term_Ref ObjectID="34194"/>
    <cge:TPSR_Ref TObjectID="24255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-132902" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -231.000000 -240.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132902" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24255"/>
     <cge:Term_Ref ObjectID="34194"/>
    <cge:TPSR_Ref TObjectID="24255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-132903" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -231.000000 -240.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132903" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24255"/>
     <cge:Term_Ref ObjectID="34194"/>
    <cge:TPSR_Ref TObjectID="24255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-132904" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -231.000000 -240.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132904" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24255"/>
     <cge:Term_Ref ObjectID="34194"/>
    <cge:TPSR_Ref TObjectID="24255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-132916" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -94.000000 -239.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132916" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24260"/>
     <cge:Term_Ref ObjectID="34204"/>
    <cge:TPSR_Ref TObjectID="24260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-132917" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -94.000000 -239.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132917" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24260"/>
     <cge:Term_Ref ObjectID="34204"/>
    <cge:TPSR_Ref TObjectID="24260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-132910" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -94.000000 -239.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132910" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24260"/>
     <cge:Term_Ref ObjectID="34204"/>
    <cge:TPSR_Ref TObjectID="24260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-132911" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -94.000000 -239.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132911" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24260"/>
     <cge:Term_Ref ObjectID="34204"/>
    <cge:TPSR_Ref TObjectID="24260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-132912" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -94.000000 -239.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132912" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24260"/>
     <cge:Term_Ref ObjectID="34204"/>
    <cge:TPSR_Ref TObjectID="24260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-132924" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 54.000000 -239.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132924" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24265"/>
     <cge:Term_Ref ObjectID="34214"/>
    <cge:TPSR_Ref TObjectID="24265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-132925" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 54.000000 -239.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132925" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24265"/>
     <cge:Term_Ref ObjectID="34214"/>
    <cge:TPSR_Ref TObjectID="24265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-132918" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 54.000000 -239.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132918" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24265"/>
     <cge:Term_Ref ObjectID="34214"/>
    <cge:TPSR_Ref TObjectID="24265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-132919" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 54.000000 -239.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132919" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24265"/>
     <cge:Term_Ref ObjectID="34214"/>
    <cge:TPSR_Ref TObjectID="24265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-132920" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 54.000000 -239.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132920" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24265"/>
     <cge:Term_Ref ObjectID="34214"/>
    <cge:TPSR_Ref TObjectID="24265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-132932" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 332.000000 -241.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24270"/>
     <cge:Term_Ref ObjectID="34224"/>
    <cge:TPSR_Ref TObjectID="24270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-132933" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 332.000000 -241.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132933" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24270"/>
     <cge:Term_Ref ObjectID="34224"/>
    <cge:TPSR_Ref TObjectID="24270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-132926" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 332.000000 -241.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132926" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24270"/>
     <cge:Term_Ref ObjectID="34224"/>
    <cge:TPSR_Ref TObjectID="24270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-132927" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 332.000000 -241.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132927" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24270"/>
     <cge:Term_Ref ObjectID="34224"/>
    <cge:TPSR_Ref TObjectID="24270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-132928" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 332.000000 -241.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132928" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24270"/>
     <cge:Term_Ref ObjectID="34224"/>
    <cge:TPSR_Ref TObjectID="24270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-132948" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 618.500000 -242.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132948" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24281"/>
     <cge:Term_Ref ObjectID="34246"/>
    <cge:TPSR_Ref TObjectID="24281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-132949" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 618.500000 -242.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132949" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24281"/>
     <cge:Term_Ref ObjectID="34246"/>
    <cge:TPSR_Ref TObjectID="24281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-132942" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 618.500000 -242.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132942" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24281"/>
     <cge:Term_Ref ObjectID="34246"/>
    <cge:TPSR_Ref TObjectID="24281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-132943" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 618.500000 -242.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132943" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24281"/>
     <cge:Term_Ref ObjectID="34246"/>
    <cge:TPSR_Ref TObjectID="24281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-132944" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 618.500000 -242.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132944" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24281"/>
     <cge:Term_Ref ObjectID="34246"/>
    <cge:TPSR_Ref TObjectID="24281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-132956" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 902.500000 -242.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132956" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24283"/>
     <cge:Term_Ref ObjectID="34250"/>
    <cge:TPSR_Ref TObjectID="24283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-132957" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 902.500000 -242.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132957" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24283"/>
     <cge:Term_Ref ObjectID="34250"/>
    <cge:TPSR_Ref TObjectID="24283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-132950" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 902.500000 -242.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132950" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24283"/>
     <cge:Term_Ref ObjectID="34250"/>
    <cge:TPSR_Ref TObjectID="24283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-132951" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 902.500000 -242.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132951" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24283"/>
     <cge:Term_Ref ObjectID="34250"/>
    <cge:TPSR_Ref TObjectID="24283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-132952" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 902.500000 -242.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132952" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24283"/>
     <cge:Term_Ref ObjectID="34250"/>
    <cge:TPSR_Ref TObjectID="24283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-132964" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -235.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132964" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24288"/>
     <cge:Term_Ref ObjectID="34260"/>
    <cge:TPSR_Ref TObjectID="24288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-132965" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -235.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132965" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24288"/>
     <cge:Term_Ref ObjectID="34260"/>
    <cge:TPSR_Ref TObjectID="24288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-132958" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -235.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24288"/>
     <cge:Term_Ref ObjectID="34260"/>
    <cge:TPSR_Ref TObjectID="24288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-132959" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -235.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132959" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24288"/>
     <cge:Term_Ref ObjectID="34260"/>
    <cge:TPSR_Ref TObjectID="24288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-132960" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -235.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132960" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24288"/>
     <cge:Term_Ref ObjectID="34260"/>
    <cge:TPSR_Ref TObjectID="24288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-132972" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1192.000000 -202.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132972" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24292"/>
     <cge:Term_Ref ObjectID="34268"/>
    <cge:TPSR_Ref TObjectID="24292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-132973" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1192.000000 -202.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132973" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24292"/>
     <cge:Term_Ref ObjectID="34268"/>
    <cge:TPSR_Ref TObjectID="24292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-132966" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1192.000000 -202.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132966" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24292"/>
     <cge:Term_Ref ObjectID="34268"/>
    <cge:TPSR_Ref TObjectID="24292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-132967" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1192.000000 -202.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132967" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24292"/>
     <cge:Term_Ref ObjectID="34268"/>
    <cge:TPSR_Ref TObjectID="24292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-132968" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1192.000000 -202.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132968" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24292"/>
     <cge:Term_Ref ObjectID="34268"/>
    <cge:TPSR_Ref TObjectID="24292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-132980" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1325.000000 -250.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132980" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24298"/>
     <cge:Term_Ref ObjectID="34280"/>
    <cge:TPSR_Ref TObjectID="24298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-132981" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1325.000000 -250.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132981" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24298"/>
     <cge:Term_Ref ObjectID="34280"/>
    <cge:TPSR_Ref TObjectID="24298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-132974" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1325.000000 -250.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132974" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24298"/>
     <cge:Term_Ref ObjectID="34280"/>
    <cge:TPSR_Ref TObjectID="24298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-132975" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1325.000000 -250.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132975" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24298"/>
     <cge:Term_Ref ObjectID="34280"/>
    <cge:TPSR_Ref TObjectID="24298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-132976" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1325.000000 -250.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132976" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24298"/>
     <cge:Term_Ref ObjectID="34280"/>
    <cge:TPSR_Ref TObjectID="24298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-132856" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 75.000000 -1076.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132856" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24325"/>
     <cge:Term_Ref ObjectID="34324"/>
    <cge:TPSR_Ref TObjectID="24325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-132857" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 75.000000 -1076.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132857" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24325"/>
     <cge:Term_Ref ObjectID="34324"/>
    <cge:TPSR_Ref TObjectID="24325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-132858" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 75.000000 -1076.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132858" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24325"/>
     <cge:Term_Ref ObjectID="34324"/>
    <cge:TPSR_Ref TObjectID="24325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-132862" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 75.000000 -1076.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132862" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24325"/>
     <cge:Term_Ref ObjectID="34324"/>
    <cge:TPSR_Ref TObjectID="24325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-132859" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 75.000000 -1076.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132859" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24325"/>
     <cge:Term_Ref ObjectID="34324"/>
    <cge:TPSR_Ref TObjectID="24325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-133396" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 75.000000 -1076.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133396" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24325"/>
     <cge:Term_Ref ObjectID="34324"/>
    <cge:TPSR_Ref TObjectID="24325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-132990" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -186.000000 -761.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132990" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24326"/>
     <cge:Term_Ref ObjectID="34325"/>
    <cge:TPSR_Ref TObjectID="24326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-132991" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -186.000000 -761.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132991" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24326"/>
     <cge:Term_Ref ObjectID="34325"/>
    <cge:TPSR_Ref TObjectID="24326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-132992" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -186.000000 -761.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132992" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24326"/>
     <cge:Term_Ref ObjectID="34325"/>
    <cge:TPSR_Ref TObjectID="24326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-132996" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -186.000000 -761.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132996" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24326"/>
     <cge:Term_Ref ObjectID="34325"/>
    <cge:TPSR_Ref TObjectID="24326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-132993" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -186.000000 -761.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132993" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24326"/>
     <cge:Term_Ref ObjectID="34325"/>
    <cge:TPSR_Ref TObjectID="24326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-132994" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -186.000000 -761.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132994" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24326"/>
     <cge:Term_Ref ObjectID="34325"/>
    <cge:TPSR_Ref TObjectID="24326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-132995" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -186.000000 -761.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132995" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24326"/>
     <cge:Term_Ref ObjectID="34325"/>
    <cge:TPSR_Ref TObjectID="24326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-133397" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -186.000000 -761.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133397" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24326"/>
     <cge:Term_Ref ObjectID="34325"/>
    <cge:TPSR_Ref TObjectID="24326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-132997" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -757.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132997" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24327"/>
     <cge:Term_Ref ObjectID="34326"/>
    <cge:TPSR_Ref TObjectID="24327"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-132998" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -757.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132998" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24327"/>
     <cge:Term_Ref ObjectID="34326"/>
    <cge:TPSR_Ref TObjectID="24327"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-132999" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -757.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132999" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24327"/>
     <cge:Term_Ref ObjectID="34326"/>
    <cge:TPSR_Ref TObjectID="24327"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-133062" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -757.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133062" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24327"/>
     <cge:Term_Ref ObjectID="34326"/>
    <cge:TPSR_Ref TObjectID="24327"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-133000" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -757.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133000" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24327"/>
     <cge:Term_Ref ObjectID="34326"/>
    <cge:TPSR_Ref TObjectID="24327"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-133001" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -757.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133001" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24327"/>
     <cge:Term_Ref ObjectID="34326"/>
    <cge:TPSR_Ref TObjectID="24327"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-133002" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -757.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133002" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24327"/>
     <cge:Term_Ref ObjectID="34326"/>
    <cge:TPSR_Ref TObjectID="24327"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-133398" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -757.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133398" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24327"/>
     <cge:Term_Ref ObjectID="34326"/>
    <cge:TPSR_Ref TObjectID="24327"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-132882" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 351.000000 -727.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132882" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24335"/>
     <cge:Term_Ref ObjectID="34341"/>
    <cge:TPSR_Ref TObjectID="24335"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-132883" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 351.000000 -727.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132883" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24335"/>
     <cge:Term_Ref ObjectID="34341"/>
    <cge:TPSR_Ref TObjectID="24335"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-132879" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 351.000000 -727.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132879" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24335"/>
     <cge:Term_Ref ObjectID="34341"/>
    <cge:TPSR_Ref TObjectID="24335"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-132884" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 351.000000 -727.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132884" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24335"/>
     <cge:Term_Ref ObjectID="34341"/>
    <cge:TPSR_Ref TObjectID="24335"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-132896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 921.000000 -725.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24337"/>
     <cge:Term_Ref ObjectID="34345"/>
    <cge:TPSR_Ref TObjectID="24337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-132897" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 921.000000 -725.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132897" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24337"/>
     <cge:Term_Ref ObjectID="34345"/>
    <cge:TPSR_Ref TObjectID="24337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-132893" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 921.000000 -725.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132893" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24337"/>
     <cge:Term_Ref ObjectID="34345"/>
    <cge:TPSR_Ref TObjectID="24337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-132898" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 921.000000 -725.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132898" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24337"/>
     <cge:Term_Ref ObjectID="34345"/>
    <cge:TPSR_Ref TObjectID="24337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-132988" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 528.000000 -754.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132988" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24333"/>
     <cge:Term_Ref ObjectID="34337"/>
    <cge:TPSR_Ref TObjectID="24333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-132989" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 528.000000 -754.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132989" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24333"/>
     <cge:Term_Ref ObjectID="34337"/>
    <cge:TPSR_Ref TObjectID="24333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-132982" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 528.000000 -754.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132982" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24333"/>
     <cge:Term_Ref ObjectID="34337"/>
    <cge:TPSR_Ref TObjectID="24333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-132887" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 378.000000 -827.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132887" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24316"/>
     <cge:Term_Ref ObjectID="34316"/>
    <cge:TPSR_Ref TObjectID="24316"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-132885" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 378.000000 -827.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132885" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24316"/>
     <cge:Term_Ref ObjectID="34316"/>
    <cge:TPSR_Ref TObjectID="24316"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-132901" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 945.000000 -821.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132901" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24317"/>
     <cge:Term_Ref ObjectID="34320"/>
    <cge:TPSR_Ref TObjectID="24317"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-132899" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 945.000000 -821.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132899" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24317"/>
     <cge:Term_Ref ObjectID="34320"/>
    <cge:TPSR_Ref TObjectID="24317"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-132940" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 471.000000 -196.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132940" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24275"/>
     <cge:Term_Ref ObjectID="34234"/>
    <cge:TPSR_Ref TObjectID="24275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-132941" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 471.000000 -196.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132941" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24275"/>
     <cge:Term_Ref ObjectID="34234"/>
    <cge:TPSR_Ref TObjectID="24275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-132934" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 471.000000 -196.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132934" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24275"/>
     <cge:Term_Ref ObjectID="34234"/>
    <cge:TPSR_Ref TObjectID="24275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-132935" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 471.000000 -196.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132935" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24275"/>
     <cge:Term_Ref ObjectID="34234"/>
    <cge:TPSR_Ref TObjectID="24275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-132936" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 471.000000 -196.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="132936" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24275"/>
     <cge:Term_Ref ObjectID="34234"/>
    <cge:TPSR_Ref TObjectID="24275"/></metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-132301">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 267.000000 -991.190476)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24250" ObjectName="SW-MD_XJ.MD_XJ_3621SW"/>
     <cge:Meas_Ref ObjectId="132301"/>
    <cge:TPSR_Ref TObjectID="24250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132302">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 267.000000 -1106.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24251" ObjectName="SW-MD_XJ.MD_XJ_3626SW"/>
     <cge:Meas_Ref ObjectId="132302"/>
    <cge:TPSR_Ref TObjectID="24251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132275">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 717.000000 -1119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24244" ObjectName="SW-MD_XJ.MD_XJ_3616SW"/>
     <cge:Meas_Ref ObjectId="132275"/>
    <cge:TPSR_Ref TObjectID="24244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132273">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 717.000000 -990.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24243" ObjectName="SW-MD_XJ.MD_XJ_3611SW"/>
     <cge:Meas_Ref ObjectId="132273"/>
    <cge:TPSR_Ref TObjectID="24243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132304">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 263.000000 -1163.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24253" ObjectName="SW-MD_XJ.MD_XJ_36267SW"/>
     <cge:Meas_Ref ObjectId="132304"/>
    <cge:TPSR_Ref TObjectID="24253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132280">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 718.000000 -1172.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24248" ObjectName="SW-MD_XJ.MD_XJ_36167SW"/>
     <cge:Meas_Ref ObjectId="132280"/>
    <cge:TPSR_Ref TObjectID="24248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132401">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 795.402135 -915.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24343" ObjectName="SW-MD_XJ.MD_XJ_3021SW"/>
     <cge:Meas_Ref ObjectId="132401"/>
    <cge:TPSR_Ref TObjectID="24343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132331">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 232.165268 -921.190476)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24340" ObjectName="SW-MD_XJ.MD_XJ_3011SW"/>
     <cge:Meas_Ref ObjectId="132331"/>
    <cge:TPSR_Ref TObjectID="24340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132278">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 716.000000 -1040.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24246" ObjectName="SW-MD_XJ.MD_XJ_36117SW"/>
     <cge:Meas_Ref ObjectId="132278"/>
    <cge:TPSR_Ref TObjectID="24246"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132279">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 716.000000 -1103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24247" ObjectName="SW-MD_XJ.MD_XJ_36160SW"/>
     <cge:Meas_Ref ObjectId="132279"/>
    <cge:TPSR_Ref TObjectID="24247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132303">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 307.000000 -1193.190476)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24252" ObjectName="SW-MD_XJ.MD_XJ_3629SW"/>
     <cge:Meas_Ref ObjectId="132303"/>
    <cge:TPSR_Ref TObjectID="24252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132277">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 754.000000 -1193.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24245" ObjectName="SW-MD_XJ.MD_XJ_3619SW"/>
     <cge:Meas_Ref ObjectId="132277"/>
    <cge:TPSR_Ref TObjectID="24245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132345">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 231.165268 -723.190476)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24342" ObjectName="SW-MD_XJ.MD_XJ_001XC"/>
     <cge:Meas_Ref ObjectId="132345"/>
    <cge:TPSR_Ref TObjectID="24342"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132345">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 231.165268 -656.190476)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24341" ObjectName="SW-MD_XJ.MD_XJ_001XC1"/>
     <cge:Meas_Ref ObjectId="132345"/>
    <cge:TPSR_Ref TObjectID="24341"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132415">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 794.402135 -725.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24344" ObjectName="SW-MD_XJ.MD_XJ_002XC"/>
     <cge:Meas_Ref ObjectId="132415"/>
    <cge:TPSR_Ref TObjectID="24344"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132415">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 794.402135 -646.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24345" ObjectName="SW-MD_XJ.MD_XJ_002XC1"/>
     <cge:Meas_Ref ObjectId="132415"/>
    <cge:TPSR_Ref TObjectID="24345"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132694">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.750000 -0.000000 0.000000 -0.551020 450.225585 -684.020408)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24303" ObjectName="SW-MD_XJ.MD_XJ_012XC"/>
     <cge:Meas_Ref ObjectId="132694"/>
    <cge:TPSR_Ref TObjectID="24303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132694">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.750000 -0.000000 0.000000 -0.551020 450.225585 -637.897959)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24304" ObjectName="SW-MD_XJ.MD_XJ_012XC1"/>
     <cge:Meas_Ref ObjectId="132694"/>
    <cge:TPSR_Ref TObjectID="24304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132737">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.789474 -0.000000 0.000000 -0.870968 585.643348 -671.551459)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24307" ObjectName="SW-MD_XJ.MD_XJ_0122XC"/>
     <cge:Meas_Ref ObjectId="132737"/>
    <cge:TPSR_Ref TObjectID="24307"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132737">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.789474 -0.000000 0.000000 -0.870968 585.643348 -641.258065)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24308" ObjectName="SW-MD_XJ.MD_XJ_0122XC1"/>
     <cge:Meas_Ref ObjectId="132737"/>
    <cge:TPSR_Ref TObjectID="24308"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 607.500000 -911.190476)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132748">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 475.000000 -902.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24309" ObjectName="SW-MD_XJ.MD_XJ_3901SW"/>
     <cge:Meas_Ref ObjectId="132748"/>
    <cge:TPSR_Ref TObjectID="24309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132750">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 477.000000 -887.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24310" ObjectName="SW-MD_XJ.MD_XJ_39017SW"/>
     <cge:Meas_Ref ObjectId="132750"/>
    <cge:TPSR_Ref TObjectID="24310"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132472">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -249.966185 -516.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24259" ObjectName="SW-MD_XJ.MD_XJ_065XC1"/>
     <cge:Meas_Ref ObjectId="132472"/>
    <cge:TPSR_Ref TObjectID="24259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132470">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -248.966185 -383.190476)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24256" ObjectName="SW-MD_XJ.MD_XJ_0656SW"/>
     <cge:Meas_Ref ObjectId="132470"/>
    <cge:TPSR_Ref TObjectID="24256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132471">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 -292.000000 -511.224291)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24257" ObjectName="SW-MD_XJ.MD_XJ_06527SW"/>
     <cge:Meas_Ref ObjectId="132471"/>
    <cge:TPSR_Ref TObjectID="24257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132472">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -249.966185 -587.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24258" ObjectName="SW-MD_XJ.MD_XJ_065XC"/>
     <cge:Meas_Ref ObjectId="132472"/>
    <cge:TPSR_Ref TObjectID="24258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132495">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -105.166185 -515.190476)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24264" ObjectName="SW-MD_XJ.MD_XJ_064XC1"/>
     <cge:Meas_Ref ObjectId="132495"/>
    <cge:TPSR_Ref TObjectID="24264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132493">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -104.166185 -381.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24261" ObjectName="SW-MD_XJ.MD_XJ_0646SW"/>
     <cge:Meas_Ref ObjectId="132493"/>
    <cge:TPSR_Ref TObjectID="24261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132494">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 -147.200000 -509.033815)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24262" ObjectName="SW-MD_XJ.MD_XJ_06427SW"/>
     <cge:Meas_Ref ObjectId="132494"/>
    <cge:TPSR_Ref TObjectID="24262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132495">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -105.166185 -585.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24263" ObjectName="SW-MD_XJ.MD_XJ_064XC"/>
     <cge:Meas_Ref ObjectId="132495"/>
    <cge:TPSR_Ref TObjectID="24263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132518">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 39.633815 -515.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24269" ObjectName="SW-MD_XJ.MD_XJ_063XC1"/>
     <cge:Meas_Ref ObjectId="132518"/>
    <cge:TPSR_Ref TObjectID="24269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132516">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 40.633815 -382.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24266" ObjectName="SW-MD_XJ.MD_XJ_0636SW"/>
     <cge:Meas_Ref ObjectId="132516"/>
    <cge:TPSR_Ref TObjectID="24266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132517">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 -2.400000 -510.224291)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24267" ObjectName="SW-MD_XJ.MD_XJ_06327SW"/>
     <cge:Meas_Ref ObjectId="132517"/>
    <cge:TPSR_Ref TObjectID="24267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132518">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 39.633815 -586.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24268" ObjectName="SW-MD_XJ.MD_XJ_063XC"/>
     <cge:Meas_Ref ObjectId="132518"/>
    <cge:TPSR_Ref TObjectID="24268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132754">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 174.900000 -579.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24311" ObjectName="SW-MD_XJ.MD_XJ_0901XC"/>
     <cge:Meas_Ref ObjectId="132754"/>
    <cge:TPSR_Ref TObjectID="24311"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132754">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 174.900000 -544.190476)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24312" ObjectName="SW-MD_XJ.MD_XJ_0901XC1"/>
     <cge:Meas_Ref ObjectId="132754"/>
    <cge:TPSR_Ref TObjectID="24312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132541">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 326.233815 -515.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24274" ObjectName="SW-MD_XJ.MD_XJ_062XC1"/>
     <cge:Meas_Ref ObjectId="132541"/>
    <cge:TPSR_Ref TObjectID="24274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132539">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 327.233815 -382.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24271" ObjectName="SW-MD_XJ.MD_XJ_0626SW"/>
     <cge:Meas_Ref ObjectId="132539"/>
    <cge:TPSR_Ref TObjectID="24271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132540">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 284.200000 -510.224291)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24272" ObjectName="SW-MD_XJ.MD_XJ_06227SW"/>
     <cge:Meas_Ref ObjectId="132540"/>
    <cge:TPSR_Ref TObjectID="24272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132541">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 326.233815 -586.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24273" ObjectName="SW-MD_XJ.MD_XJ_062XC"/>
     <cge:Meas_Ref ObjectId="132541"/>
    <cge:TPSR_Ref TObjectID="24273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132562">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.873333 -0.000000 0.000000 -0.778870 443.098965 -415.222417)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24276" ObjectName="SW-MD_XJ.MD_XJ_0616BK"/>
     <cge:Meas_Ref ObjectId="132562"/>
    <cge:TPSR_Ref TObjectID="24276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132563">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.873333 0.000000 0.000000 -0.778870 407.116667 -416.979115)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24277" ObjectName="SW-MD_XJ.MD_XJ_06167SW"/>
     <cge:Meas_Ref ObjectId="132563"/>
    <cge:TPSR_Ref TObjectID="24277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132565">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.970370 -0.000000 0.000000 -1.001404 440.250702 -597.704182)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24279" ObjectName="SW-MD_XJ.MD_XJ_061XC"/>
     <cge:Meas_Ref ObjectId="132565"/>
    <cge:TPSR_Ref TObjectID="24279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132565">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.970370 -0.000000 0.000000 -0.992650 440.250702 -535.850338)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24280" ObjectName="SW-MD_XJ.MD_XJ_061XC1"/>
     <cge:Meas_Ref ObjectId="132565"/>
    <cge:TPSR_Ref TObjectID="24280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133005">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 597.033815 -515.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24331" ObjectName="SW-MD_XJ.MD_XJ_071XC1"/>
     <cge:Meas_Ref ObjectId="133005"/>
    <cge:TPSR_Ref TObjectID="24331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133003">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 598.033815 -382.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24328" ObjectName="SW-MD_XJ.MD_XJ_0716SW"/>
     <cge:Meas_Ref ObjectId="133003"/>
    <cge:TPSR_Ref TObjectID="24328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133004">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 554.000000 -510.224291)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24329" ObjectName="SW-MD_XJ.MD_XJ_07127SW"/>
     <cge:Meas_Ref ObjectId="133004"/>
    <cge:TPSR_Ref TObjectID="24329"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133005">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 597.033815 -586.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24330" ObjectName="SW-MD_XJ.MD_XJ_071XC"/>
     <cge:Meas_Ref ObjectId="133005"/>
    <cge:TPSR_Ref TObjectID="24330"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132759">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 714.500000 -543.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24314" ObjectName="SW-MD_XJ.MD_XJ_0902XC1"/>
     <cge:Meas_Ref ObjectId="132759"/>
    <cge:TPSR_Ref TObjectID="24314"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132759">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 714.500000 -578.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24313" ObjectName="SW-MD_XJ.MD_XJ_0902XC"/>
     <cge:Meas_Ref ObjectId="132759"/>
    <cge:TPSR_Ref TObjectID="24313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132607">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 889.033815 -515.190476)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24287" ObjectName="SW-MD_XJ.MD_XJ_072XC1"/>
     <cge:Meas_Ref ObjectId="132607"/>
    <cge:TPSR_Ref TObjectID="24287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132605">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 890.033815 -381.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24284" ObjectName="SW-MD_XJ.MD_XJ_0726SW"/>
     <cge:Meas_Ref ObjectId="132605"/>
    <cge:TPSR_Ref TObjectID="24284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132606">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 847.000000 -509.033815)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24285" ObjectName="SW-MD_XJ.MD_XJ_07227SW"/>
     <cge:Meas_Ref ObjectId="132606"/>
    <cge:TPSR_Ref TObjectID="24285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132607">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 889.033815 -585.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24286" ObjectName="SW-MD_XJ.MD_XJ_072XC"/>
     <cge:Meas_Ref ObjectId="132607"/>
    <cge:TPSR_Ref TObjectID="24286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132629">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1035.533815 -515.190476)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24291" ObjectName="SW-MD_XJ.MD_XJ_073XC1"/>
     <cge:Meas_Ref ObjectId="132629"/>
    <cge:TPSR_Ref TObjectID="24291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132628">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1036.533815 -381.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24289" ObjectName="SW-MD_XJ.MD_XJ_0736SW"/>
     <cge:Meas_Ref ObjectId="132628"/>
    <cge:TPSR_Ref TObjectID="24289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133006">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 994.500000 -509.033815)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24332" ObjectName="SW-MD_XJ.MD_XJ_07327SW"/>
     <cge:Meas_Ref ObjectId="133006"/>
    <cge:TPSR_Ref TObjectID="24332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132629">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1035.533815 -585.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24290" ObjectName="SW-MD_XJ.MD_XJ_073XC"/>
     <cge:Meas_Ref ObjectId="132629"/>
    <cge:TPSR_Ref TObjectID="24290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132650">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.873333 -0.000000 0.000000 -0.778870 1168.360000 -415.031941)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24293" ObjectName="SW-MD_XJ.MD_XJ_0746SW"/>
     <cge:Meas_Ref ObjectId="132650"/>
    <cge:TPSR_Ref TObjectID="24293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132652">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.873333 0.000000 0.000000 -0.778870 1133.116667 -412.169592)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24295" ObjectName="SW-MD_XJ.MD_XJ_07467SW"/>
     <cge:Meas_Ref ObjectId="132652"/>
    <cge:TPSR_Ref TObjectID="24295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132653">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.873333 -0.000000 0.000000 -0.778870 1167.486667 -587.412776)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24296" ObjectName="SW-MD_XJ.MD_XJ_074XC"/>
     <cge:Meas_Ref ObjectId="132653"/>
    <cge:TPSR_Ref TObjectID="24296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132653">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.873333 -0.000000 0.000000 -0.778870 1167.486667 -532.303498)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24297" ObjectName="SW-MD_XJ.MD_XJ_074XC1"/>
     <cge:Meas_Ref ObjectId="132653"/>
    <cge:TPSR_Ref TObjectID="24297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132675">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1301.533815 -513.190476)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24302" ObjectName="SW-MD_XJ.MD_XJ_075XC1"/>
     <cge:Meas_Ref ObjectId="132675"/>
    <cge:TPSR_Ref TObjectID="24302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132673">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1302.533815 -380.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24299" ObjectName="SW-MD_XJ.MD_XJ_0756SW"/>
     <cge:Meas_Ref ObjectId="132673"/>
    <cge:TPSR_Ref TObjectID="24299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132674">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1260.500000 -508.033815)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24300" ObjectName="SW-MD_XJ.MD_XJ_07527SW"/>
     <cge:Meas_Ref ObjectId="132674"/>
    <cge:TPSR_Ref TObjectID="24300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132675">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1301.533815 -584.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24301" ObjectName="SW-MD_XJ.MD_XJ_075XC"/>
     <cge:Meas_Ref ObjectId="132675"/>
    <cge:TPSR_Ref TObjectID="24301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132736">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1408.000000 -579.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24305" ObjectName="SW-MD_XJ.MD_XJ_0761XC"/>
     <cge:Meas_Ref ObjectId="132736"/>
    <cge:TPSR_Ref TObjectID="24305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132736">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1408.000000 -547.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24306" ObjectName="SW-MD_XJ.MD_XJ_0761XC1"/>
     <cge:Meas_Ref ObjectId="132736"/>
    <cge:TPSR_Ref TObjectID="24306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132564">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 394.000000 -524.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24278" ObjectName="SW-MD_XJ.MD_XJ_06127SW"/>
     <cge:Meas_Ref ObjectId="132564"/>
    <cge:TPSR_Ref TObjectID="24278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132651">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1118.000000 -523.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24294" ObjectName="SW-MD_XJ.MD_XJ_07427SW"/>
     <cge:Meas_Ref ObjectId="132651"/>
    <cge:TPSR_Ref TObjectID="24294"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-555" y="-1321"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-604" y="-1338"/></g>
   <g href="35kV戌街变备用Ⅰ线065间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="-282" y="-574"/></g>
   <g href="35kV戌街变工业园区线064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="-137" y="-573"/></g>
   <g href="35kV戌街变10kV长箐线063断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="9" y="-574"/></g>
   <g href="35kV戌街变10kV左家线062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="296" y="-575"/></g>
   <g href="35kV戌街变1号电容器061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="408" y="-588"/></g>
   <g href="35kV戌街变大冲线071间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="569" y="-572"/></g>
   <g href="35kV戌街变备用Ⅱ线072间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="859" y="-573"/></g>
   <g href="35kV戌街变华戌线073间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1000" y="-573"/></g>
   <g href="35kV戌街变2号电容器074间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1133" y="-573"/></g>
   <g href="35kV戌街变水桥线075间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1266" y="-573"/></g>
   <g href="35kV戌街变新戌安Ⅱ回线362间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="235" y="-1087"/></g>
   <g href="35kV戌街变新戌安Ⅰ回线361间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="684" y="-1089"/></g>
   <g href="35kV戌街变分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="422" y="-676"/></g>
   <g href="35kV戌街变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="21" qtmmishow="hidden" width="63" x="-651" y="-950"/></g>
   <g href="cx_配调_配网接线图35_牟定.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-284" y="-1285"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-284" y="-1320"/></g>
   <g href="35kV戌街变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="156" y="-842"/></g>
   <g href="35kV戌街变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="722" y="-841"/></g>
   <g href="AVC戌街站.svg" style="fill-opacity:0"><rect height="56" qtmmishow="hidden" stroke="rgb(0,0,0)" width="58" x="-375" y="-1336"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2e27bb0">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 517.000000 -830.190476)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14e3ba0">
    <use class="BV-10KV" transform="matrix(0.000000 -0.573034 -1.000000 -0.000000 -230.966185 -439.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d8cd10">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -212.966185 -295.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d9ad00">
    <use class="BV-10KV" transform="matrix(0.000000 -0.573034 -1.000000 -0.000000 -86.166185 -437.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d9df80">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -68.166185 -293.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dac010">
    <use class="BV-10KV" transform="matrix(0.000000 -0.573034 -1.000000 -0.000000 58.633815 -438.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1daf290">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 76.633815 -294.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dbb450">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 218.900000 -468.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dc6db0">
    <use class="BV-10KV" transform="matrix(0.000000 -0.573034 -1.000000 -0.000000 345.233815 -438.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dca030">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 363.233815 -294.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dd4b40">
    <use class="BV-10KV" transform="matrix(0.000000 -0.778870 -0.873333 -0.000000 458.818965 -454.416520)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1de5610">
    <use class="BV-10KV" transform="matrix(0.000000 -0.573034 -1.000000 -0.000000 616.033815 -438.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1de8890">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 633.033815 -294.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17c1bf0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 764.500000 -470.190476)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17d0350">
    <use class="BV-10KV" transform="matrix(0.000000 -0.573034 -1.000000 -0.000000 908.033815 -437.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17d35d0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 926.033815 -293.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17e1640">
    <use class="BV-10KV" transform="matrix(0.000000 -0.573034 -1.000000 -0.000000 1054.533815 -437.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17e48c0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1072.533815 -293.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17eda20">
    <use class="BV-10KV" transform="matrix(0.000000 -0.778870 -0.873333 -0.000000 1184.080000 -453.416520)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1800ce0">
    <use class="BV-10KV" transform="matrix(0.000000 -0.573034 -1.000000 -0.000000 1320.533815 -434.809524)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1803f60">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1338.533815 -293.190476)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d32b30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 331.000000 -1179.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d338a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 777.000000 -1183.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d38520">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 137.000000 -749.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d3b8b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 707.000000 -749.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d3e5c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 475.000000 -850.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d48560">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 715.000000 -483.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d48c80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 176.000000 -477.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="MD_XJ"/>
</svg>