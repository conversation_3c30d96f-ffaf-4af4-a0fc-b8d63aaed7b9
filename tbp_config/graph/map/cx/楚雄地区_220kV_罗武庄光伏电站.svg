<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-349" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-358 -1206 3410 1933">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape16_0">
    <circle cx="31" cy="5" fillStyle="0" r="4" stroke-width="0.888889"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="15" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="33" y1="5" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="9" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="35" y1="5" y2="5"/>
   </symbol>
   <symbol id="breaker2:shape16_1">
    <circle cx="31" cy="5" fillStyle="0" r="4" stroke-width="0.888889"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="15" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="35" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="9" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="35" y1="5" y2="5"/>
   </symbol>
   <symbol id="breaker2:shape16-UnNor1">
    <circle cx="31" cy="5" fillStyle="0" r="4" stroke-width="0.888889"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="15" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="33" y1="5" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="9" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="35" y1="5" y2="5"/>
   </symbol>
   <symbol id="breaker2:shape16-UnNor2">
    <circle cx="31" cy="5" fillStyle="0" r="4" stroke-width="0.888889"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="15" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="35" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="9" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="35" y1="5" y2="5"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape37">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,63 0,73 10,73 5,63 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,28 0,18 10,18 5,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="86" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape126">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="46" y2="29"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape194">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="10" y1="8" y2="8"/>
    <polyline DF8003:Layer="PUBLIC" points="22,1 22,16 10,8 22,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="36" x2="22" y1="9" y2="9"/>
   </symbol>
   <symbol id="load:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="15"/>
    <polyline DF8003:Layer="PUBLIC" points="1,15 10,15 5,25 0,15 1,15 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="15" y2="25"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="reactance:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="39" y2="47"/>
    <polyline points="13,39 15,39 17,38 18,38 20,37 21,36 23,35 24,33 25,31 25,30 26,28 26,26 26,24 25,22 25,21 24,19 23,18 21,16 20,15 18,14 17,14 15,13 13,13 11,13 9,14 8,14 6,15 5,16 3,18 2,19 1,21 1,22 0,24 0,26 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="0" x2="12" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="5" y2="26"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape44_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.8" x1="29" x2="32" y1="19" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape44_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="43" x2="43" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="10" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="10" x2="10" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="6" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="3" x2="3" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.8" x1="30" x2="30" y1="13" y2="6"/>
   </symbol>
   <symbol id="switch2:shape44-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="43" x2="43" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="10" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="10" x2="10" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="6" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="3" x2="3" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.8" x1="30" x2="30" y1="13" y2="6"/>
   </symbol>
   <symbol id="switch2:shape44-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="43" x2="43" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="10" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="10" x2="10" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="6" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="3" x2="3" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.8" x1="30" x2="30" y1="13" y2="6"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape7_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape7_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape13_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="transformer:shape5_0">
    <circle cx="38" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="60" y1="56" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="55" x2="60" y1="90" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="58" y1="90" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="60" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="37" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="37" y1="29" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="30" y1="22" y2="29"/>
   </symbol>
   <symbol id="transformer:shape5_1">
    <circle cx="38" cy="61" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="37" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="37" y1="73" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="30" y1="66" y2="73"/>
   </symbol>
   <symbol id="transformer:shape5-2">
    <circle cx="68" cy="45" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="69" x2="69" y1="54" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="84" x2="69" y1="45" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="84" x2="69" y1="45" y2="54"/>
   </symbol>
   <symbol id="transformer2:shape77_0">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,61 6,61 6,32 " stroke-width="1"/>
    <circle cx="31" cy="64" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="19" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="49" y2="31"/>
    <polyline DF8003:Layer="PUBLIC" points="31,18 25,31 37,31 31,18 31,19 31,18 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="31" y1="56" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="61" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="61" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="61" y2="56"/>
   </symbol>
   <symbol id="transformer2:shape77_1">
    <circle cx="31" cy="86" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="31" y1="90" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="31" y1="90" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="26" y1="90" y2="90"/>
   </symbol>
   <symbol id="voltageTransformer:shape40">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="64" x2="64" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="61" x2="61" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="57" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="46" x2="46" y1="0" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="39" x2="39" y1="0" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="9" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="45" y1="9" y2="9"/>
    <circle cx="31" cy="46" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="38" cy="40" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="31" cy="36" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="23" x2="23" y1="1" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="16" x2="16" y1="1" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.503497" x1="39" x2="22" y1="10" y2="10"/>
   </symbol>
   <symbol id="voltageTransformer:shape71">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="12" y2="12"/>
    <ellipse cx="39" cy="19" fillStyle="0" rx="15" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.36031" x1="30" x2="34" y1="55" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.39762" x1="25" x2="21" y1="55" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.77579" x1="21" x2="35" y1="62" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="33" x2="41" y1="16" y2="20"/>
    <ellipse cx="16" cy="20" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="40" x2="41" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="47" x2="41" y1="16" y2="20"/>
    <ellipse cx="28" cy="56" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="32" y2="32"/>
    <ellipse cx="40" cy="39" fillStyle="0" rx="15" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="32" y2="32"/>
    <ellipse cx="15" cy="40" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="15" x2="16" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="22" x2="16" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="16" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="33" x2="41" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="40" x2="41" y1="40" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="47" x2="41" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="15" x2="16" y1="40" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="21" x2="15" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="16" y1="36" y2="40"/>
   </symbol>
   <symbol id="voltageTransformer:shape146">
    <circle cx="23" cy="32" r="7.5" stroke-width="0.804311"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="17" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="34" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="25" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="3" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="4" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="47" y2="37"/>
    <circle cx="34" cy="27" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="23" x2="20" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="18" y2="21"/>
    <circle cx="33" cy="14" r="7.5" stroke-width="0.804311"/>
    <circle cx="44" cy="20" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="34" x2="32" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="32" x2="29" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="34" x2="32" y1="12" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="35" x2="32" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="37" x2="35" y1="30" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="37" x2="35" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="47" x2="43" y1="22" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="47" x2="43" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="43" x2="43" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="23" x2="21" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="35" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="29" y2="32"/>
    <circle cx="23" cy="21" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_365b5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_365c6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_365d090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_365dd30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_365ef60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_365fc00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_36607a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_36610c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3662870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3662870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3663c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3663c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3665330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3665330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_3665fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3667c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3668960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3669780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_366a0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_366b780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_366bf80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_366c670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_366ce00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_366dee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_366e940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_366f370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_366fd60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3671310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3671dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3672da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3673980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3681f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3674dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3675a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3676fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1943" width="3420" x="-363" y="-1211"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af4680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2929.000000 73.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2504160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2946.000000 58.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aae1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2937.000000 87.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_272b3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2938.000000 103.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2749820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2937.000000 118.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_274bb80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2937.000000 133.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29c7250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 764.000000 723.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29c74b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 781.000000 708.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29c76f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 772.000000 737.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29c7930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 773.000000 753.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29c7b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 772.000000 768.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29c7db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 772.000000 783.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a0c490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2303.000000 811.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a0cb80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2292.000000 796.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a0d1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2317.000000 781.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a0d730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2129.000000 578.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a0d920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2118.000000 563.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a0db30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2143.000000 548.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a0de30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2072.000000 159.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a0e060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2061.000000 144.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a0e270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2086.000000 129.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a75db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 353.000000 -299.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a762c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 342.000000 -314.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a76500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 367.000000 -329.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29fd560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -3.000000 84.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293ead0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 14.000000 69.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293ece0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 98.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293ef20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 114.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293f160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 129.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293f3a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 144.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c60580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1315.000000 -160.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c607e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1304.000000 -175.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c60a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1329.000000 -190.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c612f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1505.000000 -288.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c61550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1494.000000 -303.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c61790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1519.000000 -318.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c64d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1340.000000 309.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c64fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1329.000000 294.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c65200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1354.000000 279.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c660e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1438.000000 528.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c66340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1427.000000 513.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c66580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1452.000000 498.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1805,617 1808,625 1802,625 1805,617 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1756,628 1760,634 1754,634 1756,628 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1254,620 1257,628 1251,628 1254,620 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1205,631 1209,637 1203,637 1205,631 " stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-320720">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1982.000000 -139.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49878" ObjectName="SW-CX_LWZ.CX_LWZ_302BK"/>
     <cge:Meas_Ref ObjectId="320720"/>
    <cge:TPSR_Ref TObjectID="49878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320712">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1983.000000 -533.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49871" ObjectName="SW-CX_LWZ.CX_LWZ_202BK"/>
     <cge:Meas_Ref ObjectId="320712"/>
    <cge:TPSR_Ref TObjectID="49871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320793">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2340.000000 65.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49939" ObjectName="SW-CX_LWZ.CX_LWZ_356BK"/>
     <cge:Meas_Ref ObjectId="320793"/>
    <cge:TPSR_Ref TObjectID="49939"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320783">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2477.000000 70.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49931" ObjectName="SW-CX_LWZ.CX_LWZ_357BK"/>
     <cge:Meas_Ref ObjectId="320783"/>
    <cge:TPSR_Ref TObjectID="49931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320778">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2621.000000 68.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49927" ObjectName="SW-CX_LWZ.CX_LWZ_358BK"/>
     <cge:Meas_Ref ObjectId="320778"/>
    <cge:TPSR_Ref TObjectID="49927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320768">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2904.000000 66.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49919" ObjectName="SW-CX_LWZ.CX_LWZ_361BK"/>
     <cge:Meas_Ref ObjectId="320768"/>
    <cge:TPSR_Ref TObjectID="49919"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320680">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2050.000000 -759.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49852" ObjectName="SW-CX_LWZ.CX_LWZ_261BK"/>
     <cge:Meas_Ref ObjectId="320680"/>
    <cge:TPSR_Ref TObjectID="49852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320707">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1218.000000 -114.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49868" ObjectName="SW-CX_LWZ.CX_LWZ_301BK"/>
     <cge:Meas_Ref ObjectId="320707"/>
    <cge:TPSR_Ref TObjectID="49868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320699">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1219.000000 -508.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49861" ObjectName="SW-CX_LWZ.CX_LWZ_201BK"/>
     <cge:Meas_Ref ObjectId="320699"/>
    <cge:TPSR_Ref TObjectID="49861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320773">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2758.000000 64.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49923" ObjectName="SW-CX_LWZ.CX_LWZ_359BK"/>
     <cge:Meas_Ref ObjectId="320773"/>
    <cge:TPSR_Ref TObjectID="49923"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320798">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2194.000000 65.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49943" ObjectName="SW-CX_LWZ.CX_LWZ_355BK"/>
     <cge:Meas_Ref ObjectId="320798"/>
    <cge:TPSR_Ref TObjectID="49943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320803">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2058.000000 62.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49947" ObjectName="SW-CX_LWZ.CX_LWZ_354BK"/>
     <cge:Meas_Ref ObjectId="320803"/>
    <cge:TPSR_Ref TObjectID="49947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320727">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1905.000000 63.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49885" ObjectName="SW-CX_LWZ.CX_LWZ_353BK"/>
     <cge:Meas_Ref ObjectId="320727"/>
    <cge:TPSR_Ref TObjectID="49885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320740">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1767.867233 59.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49896" ObjectName="SW-CX_LWZ.CX_LWZ_352BK"/>
     <cge:Meas_Ref ObjectId="320740"/>
    <cge:TPSR_Ref TObjectID="49896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320745">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1783.500000 468.500000)" xlink:href="#breaker2:shape16_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49902" ObjectName="SW-CX_LWZ.CX_LWZ_3520BK"/>
     <cge:Meas_Ref ObjectId="320745"/>
    <cge:TPSR_Ref TObjectID="49902"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1632.000000 63.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320788">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1064.000000 73.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49935" ObjectName="SW-CX_LWZ.CX_LWZ_347BK"/>
     <cge:Meas_Ref ObjectId="320788"/>
    <cge:TPSR_Ref TObjectID="49935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320732">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1216.867233 62.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49889" ObjectName="SW-CX_LWZ.CX_LWZ_348BK"/>
     <cge:Meas_Ref ObjectId="320732"/>
    <cge:TPSR_Ref TObjectID="49889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320737">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1232.500000 471.500000)" xlink:href="#breaker2:shape16_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49895" ObjectName="SW-CX_LWZ.CX_LWZ_3480BK"/>
     <cge:Meas_Ref ObjectId="320737"/>
    <cge:TPSR_Ref TObjectID="49895"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320763">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 927.000000 68.806972)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49915" ObjectName="SW-CX_LWZ.CX_LWZ_346BK"/>
     <cge:Meas_Ref ObjectId="320763"/>
    <cge:TPSR_Ref TObjectID="49915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320758">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 760.000000 69.806972)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49911" ObjectName="SW-CX_LWZ.CX_LWZ_345BK"/>
     <cge:Meas_Ref ObjectId="320758"/>
    <cge:TPSR_Ref TObjectID="49911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320748">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 449.000000 70.806972)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49903" ObjectName="SW-CX_LWZ.CX_LWZ_343BK"/>
     <cge:Meas_Ref ObjectId="320748"/>
    <cge:TPSR_Ref TObjectID="49903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 145.000000 68.806972)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 307.000000 70.806972)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320753">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 600.000000 69.806972)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49907" ObjectName="SW-CX_LWZ.CX_LWZ_344BK"/>
     <cge:Meas_Ref ObjectId="320753"/>
    <cge:TPSR_Ref TObjectID="49907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-323605">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1543.000000 107.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="50086" ObjectName="SW-CX_LWZ.CX_LWZ_312BK"/>
     <cge:Meas_Ref ObjectId="323605"/>
    <cge:TPSR_Ref TObjectID="50086"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_LWZ.CX_LWZ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="50645"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1954.000000 -316.000000)" xlink:href="#transformer:shape5_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="50647"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1954.000000 -316.000000)" xlink:href="#transformer:shape5_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="50649"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1954.000000 -316.000000)" xlink:href="#transformer:shape5-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="49952" ObjectName="TF-CX_LWZ.CX_LWZ_2T"/>
    <cge:TPSR_Ref TObjectID="49952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_LWZ.CX_LWZ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="50638"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1190.000000 -291.000000)" xlink:href="#transformer:shape5_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="50640"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1190.000000 -291.000000)" xlink:href="#transformer:shape5_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="50642"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1190.000000 -291.000000)" xlink:href="#transformer:shape5-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="49951" ObjectName="TF-CX_LWZ.CX_LWZ_1T"/>
    <cge:TPSR_Ref TObjectID="49951"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_LWZ.CX_LWZ_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1516,-23 2987,-23 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49956" ObjectName="BS-CX_LWZ.CX_LWZ_3IIM"/>
    <cge:TPSR_Ref TObjectID="49956"/></metadata>
   <polyline fill="none" opacity="0" points="1516,-23 2987,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LWZ.CX_LWZ_2IM">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="850,-685 2488,-685 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49954" ObjectName="BS-CX_LWZ.CX_LWZ_2IM"/>
    <cge:TPSR_Ref TObjectID="49954"/></metadata>
   <polyline fill="none" opacity="0" points="850,-685 2488,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LWZ.CX_LWZ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-44,-26 1462,-26 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49955" ObjectName="BS-CX_LWZ.CX_LWZ_3IM"/>
    <cge:TPSR_Ref TObjectID="49955"/></metadata>
   <polyline fill="none" opacity="0" points="-44,-26 1462,-26 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2054.000000 -1129.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2345.000000 239.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2482.000000 244.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2626.000000 242.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2909.000000 238.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2763.000000 236.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2199.000000 239.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2063.000000 236.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1637.000000 237.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1069.000000 247.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 932.000000 236.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 765.000000 237.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 454.000000 238.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 150.000000 236.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 312.000000 238.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 605.000000 237.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2a1f1b0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.620690 -0.000000 0.000000 -0.681818 1928.103448 -424.636364)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a1fa90" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.620690 -0.000000 0.000000 -0.681818 1912.344828 -424.272727)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_296c590" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.620690 -0.000000 0.000000 -0.681818 1145.103448 -400.636364)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_296cfc0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.620690 -0.000000 0.000000 -0.681818 1129.344828 -400.272727)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28bd950" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1730.740650 160.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2939d30" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1179.740650 163.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-220KV" id="g_2a01aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1641,-727 1667,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="49859@0" ObjectIDZND0="49954@0" ObjectIDZND1="49858@x" Pin0InfoVect0LinkObjId="g_2a22f10_0" Pin0InfoVect1LinkObjId="SW-320695_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320696_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1641,-727 1667,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a02380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-685 1667,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49954@0" ObjectIDZND0="49859@x" ObjectIDZND1="49858@x" Pin0InfoVect0LinkObjId="SW-320696_0" Pin0InfoVect1LinkObjId="SW-320695_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a01aa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-685 1667,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a93cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-727 1667,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="49859@x" ObjectIDND1="49954@0" ObjectIDZND0="49858@0" Pin0InfoVect0LinkObjId="SW-320695_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320696_0" Pin1InfoVect1LinkObjId="g_2a01aa0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-727 1667,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a93ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1687,-809 1667,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="49860@0" ObjectIDZND0="49858@x" ObjectIDZND1="g_2a94990@0" ObjectIDZND2="g_296a580@0" Pin0InfoVect0LinkObjId="SW-320695_0" Pin0InfoVect1LinkObjId="g_2a94990_0" Pin0InfoVect2LinkObjId="g_296a580_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320697_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1687,-809 1667,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a947a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-787 1667,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="49858@1" ObjectIDZND0="49860@x" ObjectIDZND1="g_2a94990@0" ObjectIDZND2="g_296a580@0" Pin0InfoVect0LinkObjId="SW-320697_0" Pin0InfoVect1LinkObjId="g_2a94990_0" Pin0InfoVect2LinkObjId="g_296a580_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320695_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-787 1667,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a95360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1694,-864 1667,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_2a94990@0" ObjectIDZND0="49860@x" ObjectIDZND1="49858@x" ObjectIDZND2="g_296a580@0" Pin0InfoVect0LinkObjId="SW-320697_0" Pin0InfoVect1LinkObjId="SW-320695_0" Pin0InfoVect2LinkObjId="g_296a580_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a94990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1694,-864 1667,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_296a1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-809 1667,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="49860@x" ObjectIDND1="49858@x" ObjectIDZND0="g_2a94990@0" ObjectIDZND1="g_296a580@0" Pin0InfoVect0LinkObjId="g_2a94990_0" Pin0InfoVect1LinkObjId="g_296a580_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320697_0" Pin1InfoVect1LinkObjId="SW-320695_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-809 1667,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_296a390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-864 1667,-898 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_2a94990@0" ObjectIDND1="49860@x" ObjectIDND2="49858@x" ObjectIDZND0="g_296a580@0" Pin0InfoVect0LinkObjId="g_296a580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a94990_0" Pin1InfoVect1LinkObjId="SW-320697_0" Pin1InfoVect2LinkObjId="SW-320695_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-864 1667,-898 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a3c060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1741,-241 1741,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2a3cd20@0" ObjectIDZND0="g_2754ec0@0" Pin0InfoVect0LinkObjId="g_2754ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a3cd20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1741,-241 1741,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a3c940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1741,-169 1741,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2754ec0@1" ObjectIDZND0="g_2710e30@0" ObjectIDZND1="49884@x" Pin0InfoVect0LinkObjId="g_2710e30_0" Pin0InfoVect1LinkObjId="SW-320725_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2754ec0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1741,-169 1741,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a3cb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1741,-142 1741,-112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2754ec0@0" ObjectIDND1="g_2710e30@0" ObjectIDZND0="49884@0" Pin0InfoVect0LinkObjId="SW-320725_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2754ec0_0" Pin1InfoVect1LinkObjId="g_2710e30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1741,-142 1741,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a5e3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2349,57 2349,85 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49939@0" ObjectIDZND0="49941@1" Pin0InfoVect0LinkObjId="SW-320794_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320793_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2349,57 2349,85 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a5ec80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2349,13 2349,30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49940@1" ObjectIDZND0="49939@1" Pin0InfoVect0LinkObjId="SW-320793_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320794_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2349,13 2349,30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29ee200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2350,99 2350,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="49941@0" ObjectIDZND0="g_2a8fe40@0" ObjectIDZND1="g_29ee420@0" ObjectIDZND2="49942@x" Pin0InfoVect0LinkObjId="g_2a8fe40_0" Pin0InfoVect1LinkObjId="g_29ee420_0" Pin0InfoVect2LinkObjId="SW-320795_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320794_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2350,99 2350,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29eed40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2350,187 2350,218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_29ee420@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29ee420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2350,187 2350,218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29eef60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2349,-4 2349,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49940@0" ObjectIDZND0="49956@0" Pin0InfoVect0LinkObjId="g_2a9a700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320794_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2349,-4 2349,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab6a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2486,62 2486,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49931@0" ObjectIDZND0="49933@1" Pin0InfoVect0LinkObjId="SW-320784_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320783_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2486,62 2486,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab71b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2486,18 2486,35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49932@1" ObjectIDZND0="49931@1" Pin0InfoVect0LinkObjId="SW-320783_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320784_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2486,18 2486,35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a9a4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2487,104 2487,125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="49933@0" ObjectIDZND0="g_2a712d0@0" ObjectIDZND1="49934@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2a712d0_0" Pin0InfoVect1LinkObjId="SW-320785_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320784_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2487,104 2487,125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a9a700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2486,1 2486,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49932@0" ObjectIDZND0="49956@0" Pin0InfoVect0LinkObjId="g_29eef60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320784_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2486,1 2486,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ae39e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2630,60 2630,88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49927@0" ObjectIDZND0="49929@1" Pin0InfoVect0LinkObjId="SW-320779_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320778_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2630,60 2630,88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ae4250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2630,16 2630,33 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49928@1" ObjectIDZND0="49927@1" Pin0InfoVect0LinkObjId="SW-320778_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320779_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2630,16 2630,33 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a7a3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2631,102 2631,123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="49929@0" ObjectIDZND0="g_2aa3ac0@0" ObjectIDZND1="49930@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2aa3ac0_0" Pin0InfoVect1LinkObjId="SW-320780_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320779_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2631,102 2631,123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a7a600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2630,-1 2630,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49928@0" ObjectIDZND0="49956@0" Pin0InfoVect0LinkObjId="g_29eef60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320779_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2630,-1 2630,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29ff570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,58 2913,86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49919@0" ObjectIDZND0="49921@1" Pin0InfoVect0LinkObjId="SW-320769_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320768_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,58 2913,86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29ff7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,14 2913,31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49920@1" ObjectIDZND0="49919@1" Pin0InfoVect0LinkObjId="SW-320768_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320769_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,14 2913,31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29c5790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-3 2913,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49920@0" ObjectIDZND0="49956@0" Pin0InfoVect0LinkObjId="g_29eef60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320769_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-3 2913,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29417f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-685 1992,-650 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="49954@0" ObjectIDZND0="49872@1" Pin0InfoVect0LinkObjId="SW-320713_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a01aa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-685 1992,-650 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a20520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1908,-421 1908,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2941a50@1" ObjectIDZND0="g_2a1fa90@0" Pin0InfoVect0LinkObjId="g_2a1fa90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2941a50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1908,-421 1908,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a20780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1924,-428 1924,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a1f1b0@0" ObjectIDZND0="49877@1" Pin0InfoVect0LinkObjId="SW-320718_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a1f1b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1924,-428 1924,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a22f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2059,-707 2059,-685 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49853@0" ObjectIDZND0="49954@0" Pin0InfoVect0LinkObjId="g_2a01aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320681_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2059,-707 2059,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a23170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2085,-1095 2059,-1095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="g_2abecb0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2aad490@0" ObjectIDZND2="49857@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2aad490_0" Pin0InfoVect2LinkObjId="SW-320685_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2abecb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2085,-1095 2059,-1095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a23c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2059,-1134 2059,-1095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2abecb0@0" ObjectIDZND1="g_2aad490@0" ObjectIDZND2="49857@x" Pin0InfoVect0LinkObjId="g_2abecb0_0" Pin0InfoVect1LinkObjId="g_2aad490_0" Pin0InfoVect2LinkObjId="SW-320685_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2059,-1134 2059,-1095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a31850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2034,-1070 2059,-1070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_2aad490@0" ObjectIDZND0="g_2abecb0@0" ObjectIDZND1="0@x" ObjectIDZND2="49857@x" Pin0InfoVect0LinkObjId="g_2abecb0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-320685_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aad490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2034,-1070 2059,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a32340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2059,-1095 2059,-1070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2abecb0@0" ObjectIDND1="0@x" ObjectIDZND0="g_2aad490@0" ObjectIDZND1="49857@x" ObjectIDZND2="49854@x" Pin0InfoVect0LinkObjId="g_2aad490_0" Pin0InfoVect1LinkObjId="SW-320685_0" Pin0InfoVect2LinkObjId="SW-320682_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2abecb0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2059,-1095 2059,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a325a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2081,-896 2059,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="49857@0" ObjectIDZND0="g_2aad490@0" ObjectIDZND1="g_2abecb0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2aad490_0" Pin0InfoVect1LinkObjId="g_2abecb0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320685_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2081,-896 2059,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a33090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2059,-1070 2059,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2aad490@0" ObjectIDND1="g_2abecb0@0" ObjectIDND2="0@x" ObjectIDZND0="49857@x" ObjectIDZND1="49854@x" Pin0InfoVect0LinkObjId="SW-320685_0" Pin0InfoVect1LinkObjId="SW-320682_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2aad490_0" Pin1InfoVect1LinkObjId="g_2abecb0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2059,-1070 2059,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a332f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2059,-896 2059,-860 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="49857@x" ObjectIDND1="g_2aad490@0" ObjectIDND2="g_2abecb0@0" ObjectIDZND0="49854@1" Pin0InfoVect0LinkObjId="SW-320682_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320685_0" Pin1InfoVect1LinkObjId="g_2aad490_0" Pin1InfoVect2LinkObjId="g_2abecb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2059,-896 2059,-860 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a33550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2046,-362 2071,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" ObjectIDND0="49952@2" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28d9660_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2046,-362 2071,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a35800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1718,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1718,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28d8f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1991,-193 1991,-175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49879@1" ObjectIDZND0="49878@1" Pin0InfoVect0LinkObjId="SW-320720_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320721_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1991,-193 1991,-175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28d91a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1991,-148 1991,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49878@0" ObjectIDZND0="49880@1" Pin0InfoVect0LinkObjId="SW-320721_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1991,-148 1991,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28d9400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1991,-104 1991,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49880@0" ObjectIDZND0="49956@0" Pin0InfoVect0LinkObjId="g_29eef60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320721_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1991,-104 1991,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28d9660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2003,-225 1991,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="g_2a35a50@0" ObjectIDZND0="49952@x" ObjectIDZND1="49879@x" Pin0InfoVect0LinkObjId="g_29d6610_0" Pin0InfoVect1LinkObjId="SW-320721_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a35a50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2003,-225 1991,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28da130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1991,-321 1991,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="49952@0" ObjectIDZND0="g_2a35a50@0" ObjectIDZND1="49879@x" Pin0InfoVect0LinkObjId="g_2a35a50_0" Pin0InfoVect1LinkObjId="SW-320721_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28d9660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1991,-321 1991,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28da390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1991,-225 1991,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="g_2a35a50@0" ObjectIDND1="49952@x" ObjectIDZND0="49879@0" Pin0InfoVect0LinkObjId="SW-320721_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a35a50_0" Pin1InfoVect1LinkObjId="g_28d9660_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1991,-225 1991,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29cfca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1282,-337 1307,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" ObjectIDND0="49951@2" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29d1f50_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1282,-337 1307,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29d1f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1212,-317 1212,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="transformer" ObjectIDND0="49951@x" ObjectIDZND0="49951@x" Pin0InfoVect0LinkObjId="g_29d3390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29d3390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1212,-317 1212,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29d2ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,-168 1227,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49869@1" ObjectIDZND0="49868@1" Pin0InfoVect0LinkObjId="SW-320707_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320708_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1227,-168 1227,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29d3130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,-123 1227,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49868@0" ObjectIDZND0="49870@1" Pin0InfoVect0LinkObjId="SW-320708_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320707_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1227,-123 1227,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29d3390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1239,-200 1227,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="g_29d21a0@0" ObjectIDZND0="49951@x" ObjectIDZND1="49869@x" Pin0InfoVect0LinkObjId="g_29d1f50_0" Pin0InfoVect1LinkObjId="SW-320708_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29d21a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1239,-200 1227,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29d35f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,-296 1227,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="49951@0" ObjectIDZND0="g_29d21a0@0" ObjectIDZND1="49869@x" Pin0InfoVect0LinkObjId="g_29d21a0_0" Pin0InfoVect1LinkObjId="SW-320708_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29d1f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1227,-296 1227,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29d3850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,-200 1227,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="49951@x" ObjectIDND1="g_29d21a0@0" ObjectIDZND0="49869@0" Pin0InfoVect0LinkObjId="SW-320708_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29d1f50_0" Pin1InfoVect1LinkObjId="g_29d21a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1227,-200 1227,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29d4340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1228,-625 1228,-685 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49862@1" ObjectIDZND0="49954@0" Pin0InfoVect0LinkObjId="g_2a01aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320700_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1228,-625 1228,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29d45a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,-79 1227,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49870@0" ObjectIDZND0="49955@0" Pin0InfoVect0LinkObjId="g_292c580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320708_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1227,-79 1227,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29d4dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1924,-382 1908,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="49952@x" ObjectIDND1="49877@x" ObjectIDZND0="g_2941a50@0" ObjectIDZND1="g_29422c0@0" Pin0InfoVect0LinkObjId="g_2941a50_0" Pin0InfoVect1LinkObjId="g_29422c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28d9660_0" Pin1InfoVect1LinkObjId="SW-320718_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1924,-382 1908,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29d58c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-382 1924,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="49952@x" ObjectIDZND0="g_2941a50@0" ObjectIDZND1="g_29422c0@0" ObjectIDZND2="49877@x" Pin0InfoVect0LinkObjId="g_2941a50_0" Pin0InfoVect1LinkObjId="g_29422c0_0" Pin0InfoVect2LinkObjId="SW-320718_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28d9660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-382 1924,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29d5b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1924,-382 1924,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="switch" ObjectIDND0="g_2941a50@0" ObjectIDND1="g_29422c0@0" ObjectIDND2="49952@x" ObjectIDZND0="49877@0" Pin0InfoVect0LinkObjId="SW-320718_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2941a50_0" Pin1InfoVect1LinkObjId="g_29422c0_0" Pin1InfoVect2LinkObjId="g_28d9660_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1924,-382 1924,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29d6610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1908,-393 1908,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2941a50@0" ObjectIDZND0="49952@x" ObjectIDZND1="49877@x" ObjectIDZND2="g_29422c0@0" Pin0InfoVect0LinkObjId="g_28d9660_0" Pin0InfoVect1LinkObjId="SW-320718_0" Pin0InfoVect2LinkObjId="g_29422c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2941a50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1908,-393 1908,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29d6870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1908,-382 1888,-382 1888,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="49952@x" ObjectIDND1="49877@x" ObjectIDND2="g_2941a50@0" ObjectIDZND0="g_29422c0@0" Pin0InfoVect0LinkObjId="g_29422c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28d9660_0" Pin1InfoVect1LinkObjId="SW-320718_0" Pin1InfoVect2LinkObjId="g_2941a50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1908,-382 1888,-382 1888,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_296da50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1125,-397 1125,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_29d6ad0@1" ObjectIDZND0="g_296cfc0@0" Pin0InfoVect0LinkObjId="g_296cfc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29d6ad0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1125,-397 1125,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_296dcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1141,-404 1141,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_296c590@0" ObjectIDZND0="49867@1" Pin0InfoVect0LinkObjId="SW-320705_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_296c590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1141,-404 1141,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2970440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1125,-369 1125,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer" EndDevType2="switch" ObjectIDND0="g_29d6ad0@0" ObjectIDZND0="g_29d7340@0" ObjectIDZND1="49951@x" ObjectIDZND2="49867@x" Pin0InfoVect0LinkObjId="g_29d7340_0" Pin0InfoVect1LinkObjId="g_29d1f50_0" Pin0InfoVect2LinkObjId="SW-320705_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29d6ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1125,-369 1125,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29706a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1125,-358 1105,-358 1105,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_29d6ad0@0" ObjectIDND1="49951@x" ObjectIDND2="49867@x" ObjectIDZND0="g_29d7340@0" Pin0InfoVect0LinkObjId="g_29d7340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29d6ad0_0" Pin1InfoVect1LinkObjId="g_29d1f50_0" Pin1InfoVect2LinkObjId="SW-320705_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1125,-358 1105,-358 1105,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2971190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1141,-358 1228,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="transformer" ObjectIDND0="49867@x" ObjectIDND1="g_29d6ad0@0" ObjectIDND2="g_29d7340@0" ObjectIDZND0="49951@x" Pin0InfoVect0LinkObjId="g_29d1f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320705_0" Pin1InfoVect1LinkObjId="g_29d6ad0_0" Pin1InfoVect2LinkObjId="g_29d7340_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1141,-358 1228,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2971c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1141,-371 1141,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="49867@0" ObjectIDZND0="49951@x" ObjectIDZND1="g_29d6ad0@0" ObjectIDZND2="g_29d7340@0" Pin0InfoVect0LinkObjId="g_29d1f50_0" Pin0InfoVect1LinkObjId="g_29d6ad0_0" Pin0InfoVect2LinkObjId="g_29d7340_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320705_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1141,-371 1141,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2971ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1141,-358 1125,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="49951@x" ObjectIDND1="49867@x" ObjectIDZND0="g_29d6ad0@0" ObjectIDZND1="g_29d7340@0" Pin0InfoVect0LinkObjId="g_29d6ad0_0" Pin0InfoVect1LinkObjId="g_29d7340_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29d1f50_0" Pin1InfoVect1LinkObjId="SW-320705_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1141,-358 1125,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29748e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1183,-312 1228,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="0@1" ObjectIDZND0="49951@x" Pin0InfoVect0LinkObjId="g_29d1f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1183,-312 1228,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2912cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1930,-337 1992,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="0@1" ObjectIDZND0="49952@x" Pin0InfoVect0LinkObjId="g_28d9660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1930,-337 1992,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2913e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1698,-142 1741,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2710e30@0" ObjectIDZND0="g_2754ec0@0" ObjectIDZND1="49884@x" Pin0InfoVect0LinkObjId="g_2754ec0_0" Pin0InfoVect1LinkObjId="SW-320725_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2710e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1698,-142 1741,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2914040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2878,122 2914,122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="49922@0" ObjectIDZND0="49921@x" ObjectIDZND1="g_2a3f1f0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-320769_0" Pin0InfoVect1LinkObjId="g_2a3f1f0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2878,122 2914,122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2914ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2914,100 2914,122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="49921@0" ObjectIDZND0="49922@x" ObjectIDZND1="g_2a3f1f0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-320770_0" Pin0InfoVect1LinkObjId="g_2a3f1f0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320769_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2914,100 2914,122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2914db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2914,122 2956,122 2956,145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="49922@x" ObjectIDND1="49921@x" ObjectIDND2="0@x" ObjectIDZND0="g_2a3f1f0@0" Pin0InfoVect0LinkObjId="g_2a3f1f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320770_0" Pin1InfoVect1LinkObjId="SW-320769_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2914,122 2956,122 2956,145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2915b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2914,122 2914,217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="49922@x" ObjectIDND1="49921@x" ObjectIDND2="g_2a3f1f0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320770_0" Pin1InfoVect1LinkObjId="SW-320769_0" Pin1InfoVect2LinkObjId="g_2a3f1f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2914,122 2914,217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2915d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2673,147 2673,123 2631,123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_2aa3ac0@0" ObjectIDZND0="49929@x" ObjectIDZND1="49930@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-320779_0" Pin0InfoVect1LinkObjId="SW-320780_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aa3ac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2673,147 2673,123 2631,123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28e2850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2767,56 2767,84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49923@0" ObjectIDZND0="49925@1" Pin0InfoVect0LinkObjId="SW-320774_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320773_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2767,56 2767,84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28e2ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2767,12 2767,29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49924@1" ObjectIDZND0="49923@1" Pin0InfoVect0LinkObjId="SW-320773_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320774_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2767,12 2767,29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a70a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2732,120 2768,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="49926@0" ObjectIDZND0="49925@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2915fe0@0" Pin0InfoVect0LinkObjId="SW-320774_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2915fe0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320775_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2732,120 2768,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a72d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2768,98 2768,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="49925@0" ObjectIDZND0="49926@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2915fe0@0" Pin0InfoVect0LinkObjId="SW-320775_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2915fe0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320774_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2768,98 2768,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a7530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2768,120 2810,120 2810,143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="49925@x" ObjectIDND1="49926@x" ObjectIDND2="0@x" ObjectIDZND0="g_2915fe0@0" Pin0InfoVect0LinkObjId="g_2915fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320774_0" Pin1InfoVect1LinkObjId="SW-320775_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2768,120 2810,120 2810,143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a83a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2768,120 2768,215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="49925@x" ObjectIDND1="49926@x" ObjectIDND2="g_2915fe0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320774_0" Pin1InfoVect1LinkObjId="SW-320775_0" Pin1InfoVect2LinkObjId="g_2915fe0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2768,120 2768,215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a90b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2767,-23 2767,-5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="49956@0" ObjectIDZND0="49924@0" Pin0InfoVect0LinkObjId="SW-320774_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29eef60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2767,-23 2767,-5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a98e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2529,149 2529,125 2487,125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_2a712d0@0" ObjectIDZND0="49933@x" ObjectIDZND1="49934@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-320784_0" Pin0InfoVect1LinkObjId="SW-320785_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a712d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2529,149 2529,125 2487,125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a9b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2594,123 2631,123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="49930@0" ObjectIDZND0="49929@x" ObjectIDZND1="g_2aa3ac0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-320779_0" Pin0InfoVect1LinkObjId="g_2aa3ac0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2594,123 2631,123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a9da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2631,123 2631,221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="49929@x" ObjectIDND1="g_2aa3ac0@0" ObjectIDND2="49930@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320779_0" Pin1InfoVect1LinkObjId="g_2aa3ac0_0" Pin1InfoVect2LinkObjId="SW-320780_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2631,123 2631,221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29aa000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2392,144 2392,120 2350,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2a8fe40@0" ObjectIDZND0="49941@x" ObjectIDZND1="g_29ee420@0" ObjectIDZND2="49942@x" Pin0InfoVect0LinkObjId="SW-320794_0" Pin0InfoVect1LinkObjId="g_29ee420_0" Pin0InfoVect2LinkObjId="SW-320795_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a8fe40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2392,144 2392,120 2350,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29aa260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2444,125 2487,125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="49934@0" ObjectIDZND0="49933@x" ObjectIDZND1="g_2a712d0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-320784_0" Pin0InfoVect1LinkObjId="g_2a712d0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320785_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2444,125 2487,125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29aa4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2487,125 2487,223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="49933@x" ObjectIDND1="g_2a712d0@0" ObjectIDND2="49934@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320784_0" Pin1InfoVect1LinkObjId="g_2a712d0_0" Pin1InfoVect2LinkObjId="SW-320785_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2487,125 2487,223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29aa720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2350,120 2350,149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="49941@x" ObjectIDND1="g_2a8fe40@0" ObjectIDND2="49942@x" ObjectIDZND0="g_29ee420@1" Pin0InfoVect0LinkObjId="g_29ee420_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320794_0" Pin1InfoVect1LinkObjId="g_2a8fe40_0" Pin1InfoVect2LinkObjId="SW-320795_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2350,120 2350,149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29aa980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2304,120 2350,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="49942@0" ObjectIDZND0="49941@x" ObjectIDZND1="g_2a8fe40@0" ObjectIDZND2="g_29ee420@0" Pin0InfoVect0LinkObjId="SW-320794_0" Pin0InfoVect1LinkObjId="g_2a8fe40_0" Pin0InfoVect2LinkObjId="g_29ee420_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320795_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2304,120 2350,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29b0820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2203,57 2203,85 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49943@0" ObjectIDZND0="49945@1" Pin0InfoVect0LinkObjId="SW-320799_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320798_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2203,57 2203,85 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29b10b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2203,13 2203,30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49944@1" ObjectIDZND0="49943@1" Pin0InfoVect0LinkObjId="SW-320798_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320799_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2203,13 2203,30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29b5e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2204,99 2204,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="49945@0" ObjectIDZND0="g_29aabe0@0" ObjectIDZND1="49946@x" ObjectIDZND2="g_29b60d0@0" Pin0InfoVect0LinkObjId="g_29aabe0_0" Pin0InfoVect1LinkObjId="SW-320800_0" Pin0InfoVect2LinkObjId="g_29b60d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320799_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2204,99 2204,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29b6af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2204,187 2204,218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_29b60d0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29b60d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2204,187 2204,218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29babb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2246,144 2246,120 2204,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_29aabe0@0" ObjectIDZND0="49945@x" ObjectIDZND1="49946@x" ObjectIDZND2="g_29b60d0@0" Pin0InfoVect0LinkObjId="SW-320799_0" Pin0InfoVect1LinkObjId="SW-320800_0" Pin0InfoVect2LinkObjId="g_29b60d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29aabe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2246,144 2246,120 2204,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29bae10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2204,120 2204,149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_29aabe0@0" ObjectIDND1="49945@x" ObjectIDND2="49946@x" ObjectIDZND0="g_29b60d0@1" Pin0InfoVect0LinkObjId="g_29b60d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29aabe0_0" Pin1InfoVect1LinkObjId="SW-320799_0" Pin1InfoVect2LinkObjId="SW-320800_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2204,120 2204,149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29bb070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2158,120 2204,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="49946@0" ObjectIDZND0="g_29aabe0@0" ObjectIDZND1="49945@x" ObjectIDZND2="g_29b60d0@0" Pin0InfoVect0LinkObjId="g_29aabe0_0" Pin0InfoVect1LinkObjId="SW-320799_0" Pin0InfoVect2LinkObjId="g_29b60d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2158,120 2204,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2979140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2067,54 2067,82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49947@0" ObjectIDZND0="49949@1" Pin0InfoVect0LinkObjId="SW-320804_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320803_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2067,54 2067,82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2979b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2067,10 2067,27 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49948@1" ObjectIDZND0="49947@1" Pin0InfoVect0LinkObjId="SW-320803_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320804_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2067,10 2067,27 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_297cc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2068,96 2068,117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="49949@0" ObjectIDZND0="g_29bcbe0@0" ObjectIDZND1="g_297ce70@0" ObjectIDZND2="49950@x" Pin0InfoVect0LinkObjId="g_29bcbe0_0" Pin0InfoVect1LinkObjId="g_297ce70_0" Pin0InfoVect2LinkObjId="SW-320805_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320804_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2068,96 2068,117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_297d890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2068,184 2068,215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_297ce70@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_297ce70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2068,184 2068,215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2919f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2110,141 2110,117 2068,117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_29bcbe0@0" ObjectIDZND0="49949@x" ObjectIDZND1="g_297ce70@0" ObjectIDZND2="49950@x" Pin0InfoVect0LinkObjId="SW-320804_0" Pin0InfoVect1LinkObjId="g_297ce70_0" Pin0InfoVect2LinkObjId="SW-320805_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29bcbe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2110,141 2110,117 2068,117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_291a1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2068,117 2068,146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="49949@x" ObjectIDND1="g_29bcbe0@0" ObjectIDND2="49950@x" ObjectIDZND0="g_297ce70@1" Pin0InfoVect0LinkObjId="g_297ce70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320804_0" Pin1InfoVect1LinkObjId="g_29bcbe0_0" Pin1InfoVect2LinkObjId="SW-320805_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2068,117 2068,146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_291a440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2022,117 2068,117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="49950@0" ObjectIDZND0="49949@x" ObjectIDZND1="g_29bcbe0@0" ObjectIDZND2="g_297ce70@0" Pin0InfoVect0LinkObjId="SW-320804_0" Pin0InfoVect1LinkObjId="g_29bcbe0_0" Pin0InfoVect2LinkObjId="g_297ce70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320805_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2022,117 2068,117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_299b6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2079,-811 2059,-811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49856@0" ObjectIDZND0="49854@x" ObjectIDZND1="49852@x" Pin0InfoVect0LinkObjId="SW-320682_0" Pin0InfoVect1LinkObjId="SW-320680_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320684_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2079,-811 2059,-811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_299c1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2059,-824 2059,-811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49854@0" ObjectIDZND0="49856@x" ObjectIDZND1="49852@x" Pin0InfoVect0LinkObjId="SW-320684_0" Pin0InfoVect1LinkObjId="SW-320680_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320682_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2059,-824 2059,-811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_299c400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2059,-811 2059,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49856@x" ObjectIDND1="49854@x" ObjectIDZND0="49852@1" Pin0InfoVect0LinkObjId="SW-320680_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320684_0" Pin1InfoVect1LinkObjId="SW-320682_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2059,-811 2059,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_299c660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2080,-753 2059,-753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="49855@0" ObjectIDZND0="49852@x" ObjectIDZND1="49853@x" Pin0InfoVect0LinkObjId="SW-320680_0" Pin0InfoVect1LinkObjId="SW-320681_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320683_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2080,-753 2059,-753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_299d150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2059,-767 2059,-753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49852@0" ObjectIDZND0="49855@x" ObjectIDZND1="49853@x" Pin0InfoVect0LinkObjId="SW-320683_0" Pin0InfoVect1LinkObjId="SW-320681_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2059,-767 2059,-753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_299d3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2059,-753 2059,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49855@x" ObjectIDND1="49852@x" ObjectIDZND0="49853@1" Pin0InfoVect0LinkObjId="SW-320681_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320683_0" Pin1InfoVect1LinkObjId="SW-320680_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2059,-753 2059,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29a3180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1250,-568 1228,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49864@0" ObjectIDZND0="49862@x" ObjectIDZND1="49861@x" Pin0InfoVect0LinkObjId="SW-320700_0" Pin0InfoVect1LinkObjId="SW-320699_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320702_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1250,-568 1228,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29a3c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1228,-589 1228,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49862@0" ObjectIDZND0="49864@x" ObjectIDZND1="49861@x" Pin0InfoVect0LinkObjId="SW-320702_0" Pin0InfoVect1LinkObjId="SW-320699_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1228,-589 1228,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29a3ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1228,-568 1228,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49864@x" ObjectIDND1="49862@x" ObjectIDZND0="49861@1" Pin0InfoVect0LinkObjId="SW-320699_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320702_0" Pin1InfoVect1LinkObjId="SW-320700_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1228,-568 1228,-544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29a6930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1228,-446 1228,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="49863@0" ObjectIDZND0="49866@x" ObjectIDZND1="49951@x" Pin0InfoVect0LinkObjId="SW-320704_0" Pin0InfoVect1LinkObjId="g_29d1f50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320701_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1228,-446 1228,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29d8dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1245,-401 1228,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="49866@0" ObjectIDZND0="49863@x" ObjectIDZND1="49951@x" Pin0InfoVect0LinkObjId="SW-320701_0" Pin0InfoVect1LinkObjId="g_29d1f50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320704_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1245,-401 1228,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29d9030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1228,-401 1228,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer" ObjectIDND0="49863@x" ObjectIDND1="49866@x" ObjectIDZND0="49951@1" Pin0InfoVect0LinkObjId="g_29d1f50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320701_0" Pin1InfoVect1LinkObjId="SW-320704_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1228,-401 1228,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29dbff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-497 1228,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="49865@0" ObjectIDZND0="49861@x" ObjectIDZND1="49863@x" Pin0InfoVect0LinkObjId="SW-320699_0" Pin0InfoVect1LinkObjId="SW-320701_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320703_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-497 1228,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29dcae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1228,-517 1228,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49861@0" ObjectIDZND0="49865@x" ObjectIDZND1="49863@x" Pin0InfoVect0LinkObjId="SW-320703_0" Pin0InfoVect1LinkObjId="SW-320701_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320699_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1228,-517 1228,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29dcd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1228,-497 1228,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49865@x" ObjectIDND1="49861@x" ObjectIDZND0="49863@1" Pin0InfoVect0LinkObjId="SW-320701_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320703_0" Pin1InfoVect1LinkObjId="SW-320699_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1228,-497 1228,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29e28f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-471 1992,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="49873@0" ObjectIDZND0="49876@x" ObjectIDZND1="49952@x" Pin0InfoVect0LinkObjId="SW-320717_0" Pin0InfoVect1LinkObjId="g_28d9660_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320714_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-471 1992,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29e33e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2009,-426 1992,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="49876@0" ObjectIDZND0="49873@x" ObjectIDZND1="49952@x" Pin0InfoVect0LinkObjId="SW-320714_0" Pin0InfoVect1LinkObjId="g_28d9660_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320717_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2009,-426 1992,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29e3640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-426 1992,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer" ObjectIDND0="49873@x" ObjectIDND1="49876@x" ObjectIDZND0="49952@1" Pin0InfoVect0LinkObjId="g_28d9660_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320714_0" Pin1InfoVect1LinkObjId="SW-320717_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-426 1992,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2981720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2016,-520 1992,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="49875@0" ObjectIDZND0="49871@x" ObjectIDZND1="49873@x" Pin0InfoVect0LinkObjId="SW-320712_0" Pin0InfoVect1LinkObjId="SW-320714_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320716_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2016,-520 1992,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29821f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-542 1992,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49871@0" ObjectIDZND0="49875@x" ObjectIDZND1="49873@x" Pin0InfoVect0LinkObjId="SW-320716_0" Pin0InfoVect1LinkObjId="SW-320714_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320712_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-542 1992,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2982450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-520 1992,-507 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49875@x" ObjectIDND1="49871@x" ObjectIDZND0="49873@1" Pin0InfoVect0LinkObjId="SW-320714_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320716_0" Pin1InfoVect1LinkObjId="SW-320712_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-520 1992,-507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29826b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2014,-592 1992,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="49874@0" ObjectIDZND0="49871@x" ObjectIDZND1="49872@x" Pin0InfoVect0LinkObjId="SW-320712_0" Pin0InfoVect1LinkObjId="SW-320713_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320715_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2014,-592 1992,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2983180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-569 1992,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49871@1" ObjectIDZND0="49874@x" ObjectIDZND1="49872@x" Pin0InfoVect0LinkObjId="SW-320715_0" Pin0InfoVect1LinkObjId="SW-320713_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320712_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-569 1992,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29833e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-592 1992,-614 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49874@x" ObjectIDND1="49871@x" ObjectIDZND0="49872@0" Pin0InfoVect0LinkObjId="SW-320713_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320715_0" Pin1InfoVect1LinkObjId="SW-320712_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-592 1992,-614 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2989a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1914,55 1914,83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49885@0" ObjectIDZND0="49887@1" Pin0InfoVect0LinkObjId="SW-320728_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320727_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1914,55 1914,83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2989cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1914,11 1914,28 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49886@1" ObjectIDZND0="49885@1" Pin0InfoVect0LinkObjId="SW-320727_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320728_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1914,11 1914,28 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29902c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1915,97 1915,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="49887@0" ObjectIDZND0="g_2990520@0" ObjectIDZND1="g_29840f0@0" ObjectIDZND2="49888@x" Pin0InfoVect0LinkObjId="g_2990520_0" Pin0InfoVect1LinkObjId="g_29840f0_0" Pin0InfoVect2LinkObjId="SW-320729_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320728_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1915,97 1915,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28af320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1915,118 1915,147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="49887@x" ObjectIDND1="g_29840f0@0" ObjectIDND2="49888@x" ObjectIDZND0="g_2990520@1" Pin0InfoVect0LinkObjId="g_2990520_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320728_0" Pin1InfoVect1LinkObjId="g_29840f0_0" Pin1InfoVect2LinkObjId="SW-320729_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1915,118 1915,147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28b1e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1915,216 1915,185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2990520@0" Pin0InfoVect0LinkObjId="g_2990520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1915,216 1915,185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28b5340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1777,13 1777,23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49897@1" ObjectIDZND0="49896@1" Pin0InfoVect0LinkObjId="SW-320740_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320741_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1777,13 1777,23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28b55a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1777,50 1777,62 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49896@0" ObjectIDZND0="49898@1" Pin0InfoVect0LinkObjId="SW-320741_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1777,50 1777,62 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28be3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1737,164 1737,152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_28bd950@0" ObjectIDZND0="49899@0" Pin0InfoVect0LinkObjId="SW-320742_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28bd950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1737,164 1737,152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28be640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1777,79 1777,116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="49898@0" ObjectIDZND0="g_28be8a0@0" ObjectIDZND1="g_28ba670@0" ObjectIDZND2="49899@x" Pin0InfoVect0LinkObjId="g_28be8a0_0" Pin0InfoVect1LinkObjId="g_28ba670_0" Pin0InfoVect2LinkObjId="SW-320742_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320741_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1777,79 1777,116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28bf2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1777,146 1777,116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_28be8a0@1" ObjectIDZND0="49898@x" ObjectIDZND1="g_28ba670@0" ObjectIDZND2="49899@x" Pin0InfoVect0LinkObjId="SW-320741_0" Pin0InfoVect1LinkObjId="g_28ba670_0" Pin0InfoVect2LinkObjId="SW-320742_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28be8a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1777,146 1777,116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28c2490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1778,304 1778,258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="49901@1" ObjectIDZND0="g_28bf520@1" Pin0InfoVect0LinkObjId="g_28bf520_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320744_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1778,304 1778,258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28c3400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1778,557 1778,540 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="reactance" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1778,557 1778,540 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28c3660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1778,498 1778,476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="49902@x" Pin0InfoVect0LinkObjId="SW-320745_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1778,498 1778,476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28c38c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1735,456 1735,476 1778,476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="reactance" EndDevType1="breaker" ObjectIDZND0="0@x" ObjectIDZND1="49902@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-320745_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1735,456 1735,476 1778,476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28c3b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1778,476 1778,460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDZND0="49902@0" Pin0InfoVect0LinkObjId="SW-320745_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1778,476 1778,460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28cb140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1772,320 1731,320 1731,344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1772,320 1731,320 1731,344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28cb330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1744,351 1778,351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="49900@0" ObjectIDZND0="49901@x" ObjectIDZND1="g_28c6ad0@0" ObjectIDZND2="49902@x" Pin0InfoVect0LinkObjId="SW-320744_0" Pin0InfoVect1LinkObjId="g_28c6ad0_0" Pin0InfoVect2LinkObjId="SW-320745_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320743_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1744,351 1778,351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28cb560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1778,351 1778,340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_28c6ad0@0" ObjectIDND1="49902@x" ObjectIDND2="49900@x" ObjectIDZND0="49901@0" Pin0InfoVect0LinkObjId="SW-320744_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28c6ad0_0" Pin1InfoVect1LinkObjId="SW-320745_0" Pin1InfoVect2LinkObjId="SW-320743_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1778,351 1778,340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28cb790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1778,424 1778,409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="49902@1" ObjectIDZND0="g_28c6ad0@0" ObjectIDZND1="49901@x" ObjectIDZND2="49900@x" Pin0InfoVect0LinkObjId="g_28c6ad0_0" Pin0InfoVect1LinkObjId="SW-320744_0" Pin0InfoVect2LinkObjId="SW-320743_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320745_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1778,424 1778,409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28cb9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1778,409 1735,409 1735,431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="49902@x" ObjectIDND1="g_28c6ad0@0" ObjectIDND2="49901@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320745_0" Pin1InfoVect1LinkObjId="g_28c6ad0_0" Pin1InfoVect2LinkObjId="SW-320744_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1778,409 1735,409 1735,431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28cbbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1759,373 1778,373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_28c6ad0@0" ObjectIDZND0="49901@x" ObjectIDZND1="49900@x" ObjectIDZND2="49902@x" Pin0InfoVect0LinkObjId="SW-320744_0" Pin0InfoVect1LinkObjId="SW-320743_0" Pin0InfoVect2LinkObjId="SW-320745_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28c6ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1759,373 1778,373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28cbe20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1778,351 1778,373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="49901@x" ObjectIDND1="49900@x" ObjectIDZND0="g_28c6ad0@0" ObjectIDZND1="49902@x" Pin0InfoVect0LinkObjId="g_28c6ad0_0" Pin0InfoVect1LinkObjId="SW-320745_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320744_0" Pin1InfoVect1LinkObjId="SW-320743_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1778,351 1778,373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28cc050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1778,373 1778,409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_28c6ad0@0" ObjectIDND1="49901@x" ObjectIDND2="49900@x" ObjectIDZND0="49902@x" Pin0InfoVect0LinkObjId="SW-320745_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28c6ad0_0" Pin1InfoVect1LinkObjId="SW-320744_0" Pin1InfoVect2LinkObjId="SW-320743_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1778,373 1778,409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28cc280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1777,229 1777,195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_28bf520@0" ObjectIDZND0="g_28be8a0@0" Pin0InfoVect0LinkObjId="g_28be8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28bf520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1777,229 1777,195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28cec00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1778,542 1778,557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="reactance" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1778,542 1778,557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28cee60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1778,557 1778,596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" ObjectIDND0="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1778,557 1778,596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28d2640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2203,-4 2203,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49944@0" ObjectIDZND0="49956@0" Pin0InfoVect0LinkObjId="g_29eef60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320799_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2203,-4 2203,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28d2e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2265,66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2265,66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28d30d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2067,-7 2067,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49948@0" ObjectIDZND0="49956@0" Pin0InfoVect0LinkObjId="g_29eef60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320804_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2067,-7 2067,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28d3900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1819,136 1819,116 1777,116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_28ba670@0" ObjectIDZND0="49898@x" ObjectIDZND1="g_28be8a0@0" ObjectIDZND2="49899@x" Pin0InfoVect0LinkObjId="SW-320741_0" Pin0InfoVect1LinkObjId="g_28be8a0_0" Pin0InfoVect2LinkObjId="SW-320742_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28ba670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1819,136 1819,116 1777,116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28d3b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1957,142 1957,118 1915,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_29840f0@0" ObjectIDZND0="49887@x" ObjectIDZND1="g_2990520@0" ObjectIDZND2="49888@x" Pin0InfoVect0LinkObjId="SW-320728_0" Pin0InfoVect1LinkObjId="g_2990520_0" Pin0InfoVect2LinkObjId="SW-320729_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29840f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1957,142 1957,118 1915,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28d4870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1914,-6 1914,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49886@0" ObjectIDZND0="49956@0" Pin0InfoVect0LinkObjId="g_29eef60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320728_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1914,-6 1914,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28d50a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1915,118 1880,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="49887@x" ObjectIDND1="g_2990520@0" ObjectIDND2="g_29840f0@0" ObjectIDZND0="49888@0" Pin0InfoVect0LinkObjId="SW-320729_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320728_0" Pin1InfoVect1LinkObjId="g_2990520_0" Pin1InfoVect2LinkObjId="g_29840f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1915,118 1880,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b26ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1641,55 1641,83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1641,55 1641,83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b27360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1641,11 1641,28 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1641,11 1641,28 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b2a280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1642,97 1642,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_28d5300@0" ObjectIDZND1="0@x" ObjectIDZND2="g_2b2a4e0@0" Pin0InfoVect0LinkObjId="g_28d5300_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2b2a4e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1642,97 1642,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b2af00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1642,185 1642,216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2b2a4e0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b2a4e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1642,185 1642,216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b2f000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1684,142 1684,118 1642,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_28d5300@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2b2a4e0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2b2a4e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28d5300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1684,142 1684,118 1642,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b2f260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1642,118 1642,147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_28d5300@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_2b2a4e0@1" Pin0InfoVect0LinkObjId="g_2b2a4e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28d5300_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1642,118 1642,147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b2f4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1596,118 1642,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_28d5300@0" ObjectIDZND1="0@x" ObjectIDZND2="g_2b2a4e0@0" Pin0InfoVect0LinkObjId="g_28d5300_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2b2a4e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1596,118 1642,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b303d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1641,-6 1641,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="49956@0" Pin0InfoVect0LinkObjId="g_29eef60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1641,-6 1641,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b31480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1777,116 1737,116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="49898@x" ObjectIDND1="g_28be8a0@0" ObjectIDND2="g_28ba670@0" ObjectIDZND0="49899@1" Pin0InfoVect0LinkObjId="SW-320742_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320741_0" Pin1InfoVect1LinkObjId="g_28be8a0_0" Pin1InfoVect2LinkObjId="g_28ba670_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1777,116 1737,116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b347e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1777,-4 1777,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49897@0" ObjectIDZND0="49956@0" Pin0InfoVect0LinkObjId="g_29eef60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320741_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1777,-4 1777,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2925ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1073,65 1073,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49935@0" ObjectIDZND0="49937@1" Pin0InfoVect0LinkObjId="SW-320789_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320788_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1073,65 1073,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2925d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1073,21 1073,38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49936@1" ObjectIDZND0="49935@1" Pin0InfoVect0LinkObjId="SW-320788_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320789_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1073,21 1073,38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_292c320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1074,107 1074,128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="49937@0" ObjectIDZND0="g_2b35010@0" ObjectIDZND1="49938@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2b35010_0" Pin0InfoVect1LinkObjId="SW-320790_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320789_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1074,107 1074,128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_292c580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1073,4 1073,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49936@0" ObjectIDZND0="49955@0" Pin0InfoVect0LinkObjId="g_29d45a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320789_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1073,4 1073,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_292d3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1116,152 1116,128 1074,128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_2b35010@0" ObjectIDZND0="49937@x" ObjectIDZND1="49938@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-320789_0" Pin0InfoVect1LinkObjId="SW-320790_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b35010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1116,152 1116,128 1074,128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_292d650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1031,128 1074,128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="49938@0" ObjectIDZND0="49937@x" ObjectIDZND1="g_2b35010@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-320789_0" Pin0InfoVect1LinkObjId="g_2b35010_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1031,128 1074,128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_292d8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1074,128 1074,226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="49937@x" ObjectIDND1="g_2b35010@0" ObjectIDND2="49938@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320789_0" Pin1InfoVect1LinkObjId="g_2b35010_0" Pin1InfoVect2LinkObjId="SW-320790_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1074,128 1074,226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2931680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1226,16 1226,26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49890@1" ObjectIDZND0="49889@1" Pin0InfoVect0LinkObjId="SW-320732_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320733_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1226,16 1226,26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29318e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1226,53 1226,65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49889@0" ObjectIDZND0="49891@1" Pin0InfoVect0LinkObjId="SW-320733_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320732_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1226,53 1226,65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_293a7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1186,167 1186,155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2939d30@0" ObjectIDZND0="49892@0" Pin0InfoVect0LinkObjId="SW-320734_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2939d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1186,167 1186,155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_293aa20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1226,82 1226,119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="49891@0" ObjectIDZND0="g_293ac80@0" ObjectIDZND1="49892@x" ObjectIDZND2="g_2936a50@0" Pin0InfoVect0LinkObjId="g_293ac80_0" Pin0InfoVect1LinkObjId="SW-320734_0" Pin0InfoVect2LinkObjId="g_2936a50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320733_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1226,82 1226,119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_293b6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1226,149 1226,119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_293ac80@1" ObjectIDZND0="49891@x" ObjectIDZND1="49892@x" ObjectIDZND2="g_2936a50@0" Pin0InfoVect0LinkObjId="SW-320733_0" Pin0InfoVect1LinkObjId="SW-320734_0" Pin0InfoVect2LinkObjId="g_2936a50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_293ac80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1226,149 1226,119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_293e870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,307 1227,261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="49894@1" ObjectIDZND0="g_293b900@1" Pin0InfoVect0LinkObjId="g_293b900_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320736_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1227,307 1227,261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2946c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,560 1227,543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="reactance" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1227,560 1227,543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2946e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,501 1227,479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="49895@x" Pin0InfoVect0LinkObjId="SW-320737_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1227,501 1227,479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29470d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1184,459 1184,479 1227,479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="reactance" EndDevType1="breaker" ObjectIDZND0="0@x" ObjectIDZND1="49895@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-320737_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1184,459 1184,479 1227,479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2947330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,479 1227,463 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDZND0="49895@0" Pin0InfoVect0LinkObjId="SW-320737_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1227,479 1227,463 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_294b090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1221,323 1180,323 1180,347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1221,323 1180,323 1180,347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_294b2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,354 1227,354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="49893@0" ObjectIDZND0="49894@x" ObjectIDZND1="g_294a2e0@0" ObjectIDZND2="49895@x" Pin0InfoVect0LinkObjId="SW-320736_0" Pin0InfoVect1LinkObjId="g_294a2e0_0" Pin0InfoVect2LinkObjId="SW-320737_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320735_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1193,354 1227,354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_294b550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,354 1227,343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_294a2e0@0" ObjectIDND1="49895@x" ObjectIDND2="49893@x" ObjectIDZND0="49894@0" Pin0InfoVect0LinkObjId="SW-320736_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_294a2e0_0" Pin1InfoVect1LinkObjId="SW-320737_0" Pin1InfoVect2LinkObjId="SW-320735_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1227,354 1227,343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_294b7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,427 1227,412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="49895@1" ObjectIDZND0="49894@x" ObjectIDZND1="49893@x" ObjectIDZND2="g_294a2e0@0" Pin0InfoVect0LinkObjId="SW-320736_0" Pin0InfoVect1LinkObjId="SW-320735_0" Pin0InfoVect2LinkObjId="g_294a2e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320737_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1227,427 1227,412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_294ba10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,412 1184,412 1184,434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="49895@x" ObjectIDND1="49894@x" ObjectIDND2="49893@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320737_0" Pin1InfoVect1LinkObjId="SW-320736_0" Pin1InfoVect2LinkObjId="SW-320735_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1227,412 1184,412 1184,434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_294bc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1208,376 1227,376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_294a2e0@0" ObjectIDZND0="49894@x" ObjectIDZND1="49893@x" ObjectIDZND2="49895@x" Pin0InfoVect0LinkObjId="SW-320736_0" Pin0InfoVect1LinkObjId="SW-320735_0" Pin0InfoVect2LinkObjId="SW-320737_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_294a2e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1208,376 1227,376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_294bed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,354 1227,376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="49894@x" ObjectIDND1="49893@x" ObjectIDZND0="g_294a2e0@0" ObjectIDZND1="49895@x" Pin0InfoVect0LinkObjId="g_294a2e0_0" Pin0InfoVect1LinkObjId="SW-320737_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-320736_0" Pin1InfoVect1LinkObjId="SW-320735_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1227,354 1227,376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_294c130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,376 1227,412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="49894@x" ObjectIDND1="49893@x" ObjectIDND2="g_294a2e0@0" ObjectIDZND0="49895@x" Pin0InfoVect0LinkObjId="SW-320737_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320736_0" Pin1InfoVect1LinkObjId="SW-320735_0" Pin1InfoVect2LinkObjId="g_294a2e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1227,376 1227,412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_294c390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1226,232 1226,198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_293b900@0" ObjectIDZND0="g_293ac80@0" Pin0InfoVect0LinkObjId="g_293ac80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_293b900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1226,232 1226,198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_294ed40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,545 1227,560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="reactance" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1227,545 1227,560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_294efa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,560 1227,599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" ObjectIDND0="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1227,560 1227,599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_294f200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1268,139 1268,119 1226,119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2936a50@0" ObjectIDZND0="g_293ac80@0" ObjectIDZND1="49891@x" ObjectIDZND2="49892@x" Pin0InfoVect0LinkObjId="g_293ac80_0" Pin0InfoVect1LinkObjId="SW-320733_0" Pin0InfoVect2LinkObjId="SW-320734_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2936a50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1268,139 1268,119 1226,119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_294f460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1226,119 1186,119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_293ac80@0" ObjectIDND1="49891@x" ObjectIDND2="g_2936a50@0" ObjectIDZND0="49892@1" Pin0InfoVect0LinkObjId="SW-320734_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_293ac80_0" Pin1InfoVect1LinkObjId="SW-320733_0" Pin1InfoVect2LinkObjId="g_2936a50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1226,119 1186,119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_294f6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1226,-1 1226,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49890@0" ObjectIDZND0="49955@0" Pin0InfoVect0LinkObjId="g_29d45a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320733_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1226,-1 1226,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2959ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="936,61 936,88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49915@0" ObjectIDZND0="49917@1" Pin0InfoVect0LinkObjId="SW-320764_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320763_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="936,61 936,88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2959f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="936,18 936,35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49916@1" ObjectIDZND0="49915@1" Pin0InfoVect0LinkObjId="SW-320763_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320764_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="936,18 936,35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29604b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="937,101 937,121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="49917@0" ObjectIDZND0="g_2954330@0" ObjectIDZND1="49918@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2954330_0" Pin0InfoVect1LinkObjId="SW-320765_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320764_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="937,101 937,121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2960710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="936,2 936,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49916@0" ObjectIDZND0="49955@0" Pin0InfoVect0LinkObjId="g_29d45a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320764_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="936,2 936,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28e9f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="979,145 979,121 937,121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_2954330@0" ObjectIDZND0="49917@x" ObjectIDZND1="49918@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-320764_0" Pin0InfoVect1LinkObjId="SW-320765_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2954330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="979,145 979,121 937,121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28ea100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="894,121 937,121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="49918@0" ObjectIDZND0="49917@x" ObjectIDZND1="g_2954330@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-320764_0" Pin0InfoVect1LinkObjId="g_2954330_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320765_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="894,121 937,121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28ea2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="937,121 937,216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="49917@x" ObjectIDND1="g_2954330@0" ObjectIDND2="49918@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320764_0" Pin1InfoVect1LinkObjId="g_2954330_0" Pin1InfoVect2LinkObjId="SW-320765_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="937,121 937,216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28edb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="769,62 769,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49911@0" ObjectIDZND0="49913@1" Pin0InfoVect0LinkObjId="SW-320759_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320758_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="769,62 769,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28edd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="769,19 769,36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49912@1" ObjectIDZND0="49911@1" Pin0InfoVect0LinkObjId="SW-320758_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320759_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="769,19 769,36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28f43a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="770,102 770,122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="49913@0" ObjectIDZND0="g_28f60a0@0" ObjectIDZND1="49914@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_28f60a0_0" Pin0InfoVect1LinkObjId="SW-320760_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320759_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="770,102 770,122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28f4600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="769,3 769,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49912@0" ObjectIDZND0="49955@0" Pin0InfoVect0LinkObjId="g_29d45a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320759_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="769,3 769,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28f5ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="812,146 812,122 770,122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_28f60a0@0" ObjectIDZND0="49913@x" ObjectIDZND1="49914@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-320759_0" Pin0InfoVect1LinkObjId="SW-320760_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28f60a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="812,146 812,122 770,122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28f5ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="727,122 770,122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="49914@0" ObjectIDZND0="49913@x" ObjectIDZND1="g_28f60a0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-320759_0" Pin0InfoVect1LinkObjId="g_28f60a0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="727,122 770,122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28f5e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="770,122 770,217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="49913@x" ObjectIDND1="g_28f60a0@0" ObjectIDND2="49914@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320759_0" Pin1InfoVect1LinkObjId="g_28f60a0_0" Pin1InfoVect2LinkObjId="SW-320760_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="770,122 770,217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28fc4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="458,63 458,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49903@0" ObjectIDZND0="49905@1" Pin0InfoVect0LinkObjId="SW-320749_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320748_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="458,63 458,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28fc740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="458,20 458,37 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49904@1" ObjectIDZND0="49903@1" Pin0InfoVect0LinkObjId="SW-320748_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320749_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="458,20 458,37 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2902d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="459,103 459,123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="49905@0" ObjectIDZND0="g_2904a50@0" ObjectIDZND1="49906@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2904a50_0" Pin0InfoVect1LinkObjId="SW-320750_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="459,103 459,123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2902fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="458,4 458,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49904@0" ObjectIDZND0="49955@0" Pin0InfoVect0LinkObjId="g_29d45a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="458,4 458,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2904460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="501,147 501,123 459,123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_2904a50@0" ObjectIDZND0="49905@x" ObjectIDZND1="49906@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-320749_0" Pin0InfoVect1LinkObjId="SW-320750_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2904a50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="501,147 501,123 459,123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2904650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,123 459,123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="49906@0" ObjectIDZND0="49905@x" ObjectIDZND1="g_2904a50@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-320749_0" Pin0InfoVect1LinkObjId="g_2904a50_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="416,123 459,123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2904840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="459,123 459,218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="49905@x" ObjectIDND1="g_2904a50@0" ObjectIDND2="49906@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320749_0" Pin1InfoVect1LinkObjId="g_2904a50_0" Pin1InfoVect2LinkObjId="SW-320750_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="459,123 459,218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_290aeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="154,61 154,88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="154,61 154,88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_290b110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="154,18 154,35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="154,18 154,35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c1b550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="155,101 155,121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="0@0" ObjectIDZND0="g_2c1d220@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2c1d220_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="155,101 155,121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c1b7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="154,2 154,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="49955@0" Pin0InfoVect0LinkObjId="g_29d45a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="154,2 154,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c1cc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="197,145 197,121 155,121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_2c1d220@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c1d220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="197,145 197,121 155,121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c1ce40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="112,121 155,121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2c1d220@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2c1d220_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="112,121 155,121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c1d030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="155,121 155,216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="0@x" ObjectIDND1="g_2c1d220@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2c1d220_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="155,121 155,216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c23610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="316,63 316,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="316,63 316,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c23870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="316,20 316,37 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="316,20 316,37 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c29e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="317,103 317,123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="0@0" ObjectIDZND0="g_2c2c190@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2c2c190_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="317,103 317,123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c2a060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="316,4 316,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="49955@0" Pin0InfoVect0LinkObjId="g_29d45a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="316,4 316,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c2bbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="359,147 359,123 317,123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_2c2c190@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c2c190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="359,147 359,123 317,123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c2bdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="274,123 317,123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2c2c190@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2c2c190_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="274,123 317,123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c2bfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="317,123 317,218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="0@x" ObjectIDND1="g_2c2c190@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2c2c190_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="317,123 317,218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c32570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="609,62 609,89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49907@0" ObjectIDZND0="49909@1" Pin0InfoVect0LinkObjId="SW-320754_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320753_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="609,62 609,89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c327d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="609,19 609,36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49908@1" ObjectIDZND0="49907@1" Pin0InfoVect0LinkObjId="SW-320753_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320754_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="609,19 609,36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c38de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="610,102 610,122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="49909@0" ObjectIDZND0="g_2c3aae0@0" ObjectIDZND1="49910@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2c3aae0_0" Pin0InfoVect1LinkObjId="SW-320755_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320754_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="610,102 610,122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c39040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="609,3 609,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49908@0" ObjectIDZND0="49955@0" Pin0InfoVect0LinkObjId="g_29d45a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320754_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="609,3 609,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c3a4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="652,146 652,122 610,122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_2c3aae0@0" ObjectIDZND0="49909@x" ObjectIDZND1="49910@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-320754_0" Pin0InfoVect1LinkObjId="SW-320755_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c3aae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="652,146 652,122 610,122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c3a6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="567,122 610,122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="49910@0" ObjectIDZND0="49909@x" ObjectIDZND1="g_2c3aae0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-320754_0" Pin0InfoVect1LinkObjId="g_2c3aae0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320755_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="567,122 610,122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c3a8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="610,122 610,217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="49909@x" ObjectIDND1="g_2c3aae0@0" ObjectIDND2="49910@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-320754_0" Pin1InfoVect1LinkObjId="g_2c3aae0_0" Pin1InfoVect2LinkObjId="SW-320755_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="610,122 610,217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c47200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1741,-63 1741,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49884@1" ObjectIDZND0="49956@0" Pin0InfoVect0LinkObjId="g_29eef60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320725_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1741,-63 1741,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c48fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="988,-244 988,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2c49700@0" ObjectIDZND0="g_2c47a30@0" Pin0InfoVect0LinkObjId="g_2c47a30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c49700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="988,-244 988,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c49240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="988,-172 988,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2c47a30@1" ObjectIDZND0="49881@x" ObjectIDZND1="g_2c482b0@0" Pin0InfoVect0LinkObjId="SW-320724_0" Pin0InfoVect1LinkObjId="g_2c482b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c47a30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="988,-172 988,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c494a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="988,-145 988,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c47a30@0" ObjectIDND1="g_2c482b0@0" ObjectIDZND0="49881@0" Pin0InfoVect0LinkObjId="SW-320724_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c47a30_0" Pin1InfoVect1LinkObjId="g_2c482b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="988,-145 988,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c4c920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="945,-145 988,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2c482b0@0" ObjectIDZND0="49881@x" ObjectIDZND1="g_2c47a30@0" Pin0InfoVect0LinkObjId="SW-320724_0" Pin0InfoVect1LinkObjId="g_2c47a30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c482b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="945,-145 988,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c50830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="988,-66 988,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49881@1" ObjectIDZND0="49955@0" Pin0InfoVect0LinkObjId="g_29d45a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-320724_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="988,-66 988,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c52620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1879,-321 1879,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c518f0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c518f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1879,-321 1879,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c53110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1895,-336 1879,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2c518f0@0" Pin0InfoVect0LinkObjId="g_2c518f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1895,-336 1879,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c53370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1879,-336 1865,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_2c518f0@0" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c518f0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1879,-336 1865,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c54300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1133,-298 1133,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c535d0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c535d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1133,-298 1133,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c54df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1147,-312 1133,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2c535d0@0" Pin0InfoVect0LinkObjId="g_2c535d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1147,-312 1133,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2c55050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1133,-312 1126,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_2c535d0@0" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c535d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1133,-312 1126,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c684d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1553,8 1553,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="50086@0" ObjectIDZND0="49956@0" Pin0InfoVect0LinkObjId="g_29eef60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-323605_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1553,8 1553,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c72740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1553,97 1553,110 1434,110 1434,70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="50086@1" ObjectIDZND0="50183@x" Pin0InfoVect0LinkObjId="SW-323606_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-323605_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1553,97 1553,110 1434,110 1434,70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c729b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1434,1 1434,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="50183@x" ObjectIDZND0="49955@0" Pin0InfoVect0LinkObjId="g_29d45a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-323606_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1434,1 1434,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3001010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1434,18 1434,53 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="50183@1" ObjectIDZND0="50184@1" Pin0InfoVect0LinkObjId="SW-323606_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-323606_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1434,18 1434,53 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="49956" cx="2349" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49956" cx="2486" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49956" cx="2630" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49956" cx="2913" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49954" cx="1667" cy="-685" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49956" cx="1991" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49955" cx="1227" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49956" cx="2767" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49954" cx="2059" cy="-685" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49954" cx="1228" cy="-685" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49954" cx="1992" cy="-685" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49956" cx="2203" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49956" cx="2067" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49956" cx="1914" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49956" cx="1641" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49956" cx="1777" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49955" cx="1226" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49955" cx="1073" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49955" cx="936" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49955" cx="769" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49955" cx="609" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49955" cx="458" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49955" cx="316" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49955" cx="154" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49956" cx="1741" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49955" cx="988" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49956" cx="1553" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49955" cx="1434" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-320658" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -54.000000 -976.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49831" ObjectName="DYN-CX_LWZ"/>
     <cge:Meas_Ref ObjectId="320658"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a4b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a4b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a4b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a4b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a4b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a4b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a4b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a4b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a4b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a4b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a4b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a4b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a4b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a4b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a4b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a4b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a4b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a4b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad0aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad0aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad0aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad0aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad0aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad0aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad0aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_280a6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -1084.500000) translate(0,16)">罗武庄光伏电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26c7e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1694.500000 -328.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26c7e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1694.500000 -328.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2add820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -202.500000 -13.000000) translate(0,15)">7342128</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2abf680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2004.000000 -1206.000000) translate(0,17)">220kV罗垭线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2a5e610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2370.000000 37.000000) translate(0,17)">356</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2ab6ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2499.000000 43.000000) translate(0,17)">357</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2ae3c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2646.000000 40.000000) translate(0,17)">358</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29c59f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2293.000000 262.000000) translate(0,15)">#2-1储能装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2abfee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2433.000000 262.000000) translate(0,15)">35kV8号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ac02f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2587.000000 262.000000) translate(0,15)">35kV7号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29c6870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2732.000000 262.000000) translate(0,15)">35kV6号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29c6a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2869.000000 262.000000) translate(0,15)">35kV5号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29c6c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1620.500000 -1018.000000) translate(0,15)">220kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29c6c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1620.500000 -1018.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29c7ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -669.000000) translate(0,12)">IM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a76740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 916.000000 -471.000000) translate(0,12)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a76740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 916.000000 -471.000000) translate(0,27)">SFZB18-150000/230</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a76740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 916.000000 -471.000000) translate(0,42)">230±8×1.25%/37kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a76740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 916.000000 -471.000000) translate(0,57)">YN，yn0+d11 Ud%=14%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29fd1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2107.000000 -369.000000) translate(0,12)">2号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29fd1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2107.000000 -369.000000) translate(0,27)">SFZB18-150000/220</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29fd1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2107.000000 -369.000000) translate(0,42)">230±8×1.25%/37kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29fd1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2107.000000 -369.000000) translate(0,57)">YN，yn0+d11 Ud%=14%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2912f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2992.000000 -29.000000) translate(0,12)">35kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29139f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -82.000000 -50.000000) translate(0,12)">35kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_29b0a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2223.000000 41.000000) translate(0,17)">355</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29bbd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2188.000000 265.000000) translate(0,15)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_29793a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2086.000000 38.000000) translate(0,17)">354</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29dcfa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.000000 -300.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2983640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2026.000000 -321.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2983c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2046.000000 259.000000) translate(0,15)">备用二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28af580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1877.000000 327.000000) translate(0,15)">#1站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_28b2050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1928.000000 43.000000) translate(0,17)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_28c7880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1712.000000 672.000000) translate(0,17)">2号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_28c7880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1712.000000 672.000000) translate(0,38)">    ±Mvar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b26d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1607.000000 37.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b2f720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1592.000000 252.000000) translate(0,15)">滤波备用二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2953470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1151.000000 685.000000) translate(0,17)">1号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2953470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1151.000000 685.000000) translate(0,38)">    ±Mvar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28e98d0" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 883.000000 253.333333) translate(0,15)">35kV4号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28f5470" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 716.000000 254.333333) translate(0,15)">35kV3号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2903e20" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 405.000000 255.333333) translate(0,15)">35kV1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c1c620" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 101.000000 253.333333) translate(0,15)">滤波备用1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c2aed0" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 263.000000 255.333333) translate(0,15)">土建备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c39eb0" transform="matrix(1.000000 -0.000000 -0.000000 0.962963 556.000000 254.333333) translate(0,15)">35kV2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c552b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 908.500000 -341.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c552b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 908.500000 -341.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c558e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2932.000000 37.000000) translate(0,17)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c55b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2844.000000 186.000000) translate(0,17)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c55d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2075.000000 -788.000000) translate(0,17)">261</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c55fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2079.000000 -729.000000) translate(0,17)">2611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c561e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2074.000000 -850.000000) translate(0,17)">2616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c56420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2148.000000 -903.000000) translate(0,17)">26167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c56660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2145.000000 -818.000000) translate(0,17)">26160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c568a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2148.000000 -762.000000) translate(0,17)">26117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c56ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1516.000000 -43.000000) translate(0,12)">IIM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c56d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -44.000000 -46.000000) translate(0,12)">IM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c56f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1692.000000 -777.000000) translate(0,17)">2901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c57560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1752.000000 -819.000000) translate(0,17)">29017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c57760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1525.000000 -741.000000) translate(0,17)">29010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c579a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1247.000000 -620.000000) translate(0,17)">2011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c57be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1312.000000 -576.000000) translate(0,17)">20117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c57e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1245.000000 -538.000000) translate(0,17)">201</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c58060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1313.000000 -507.000000) translate(0,17)">20160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c582a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1235.000000 -471.000000) translate(0,17)">2016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c584e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1295.000000 -413.000000) translate(0,17)">20167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c58720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1155.000000 -394.000000) translate(0,17)">2010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c58960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1239.000000 -141.000000) translate(0,17)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c58ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1999.000000 -639.000000) translate(0,17)">2021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c58de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2064.000000 -607.000000) translate(0,17)">20217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c59020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2006.000000 -563.000000) translate(0,17)">202</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c59260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2072.000000 -529.000000) translate(0,17)">20260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c594a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1999.000000 -496.000000) translate(0,17)">2026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c596e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2071.000000 -438.000000) translate(0,17)">20267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c59920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1930.000000 -415.000000) translate(0,17)">2020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c59b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2000.000000 -169.000000) translate(0,17)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c59da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2784.000000 35.000000) translate(0,17)">359</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c59fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2702.000000 190.000000) translate(0,17)">35967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5a220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2564.000000 184.000000) translate(0,17)">35867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5a460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2425.000000 186.000000) translate(0,17)">35767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5a6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2283.000000 189.000000) translate(0,17)">35667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5a8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2137.000000 187.000000) translate(0,17)">35567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5ab20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1996.000000 187.000000) translate(0,17)">35467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5ad60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1850.000000 183.000000) translate(0,17)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5afa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1799.000000 41.000000) translate(0,17)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5b1e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1719.000000 184.000000) translate(0,17)">35237</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5b420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1794.000000 315.000000) translate(0,17)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1646.000000 340.000000) translate(0,17)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5b8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1793.000000 439.000000) translate(0,17)">3520</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5bae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1242.000000 37.000000) translate(0,17)">348</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5bd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1164.000000 194.000000) translate(0,17)">34837</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5bf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1246.000000 449.000000) translate(0,17)">3480</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5c1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1099.000000 347.000000) translate(0,17)">34867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5c3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1246.000000 325.000000) translate(0,17)">3486</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c5c620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1033.000000 268.000000) translate(0,15)">#1-1储能装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5c860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1087.000000 46.000000) translate(0,17)">347</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5caa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1002.000000 188.000000) translate(0,17)">34767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5cce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 947.000000 40.000000) translate(0,17)">346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5cf20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 872.000000 193.000000) translate(0,17)">34667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5d160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 790.000000 42.000000) translate(0,17)">345</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5d3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 694.000000 181.000000) translate(0,17)">34567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5d5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 618.000000 41.000000) translate(0,17)">344</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5d820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 541.000000 182.000000) translate(0,17)">34467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5da60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 471.000000 44.000000) translate(0,17)">343</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5dca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 393.000000 182.000000) translate(0,17)">34367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5dee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1017.000000 -98.000000) translate(0,17)">3911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c5e120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1760.000000 -95.000000) translate(0,17)">3921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c68bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1565.000000 46.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6d520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1565.000000 46.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c72c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1446.000000 28.000000) translate(0,12)">3121</text>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="1618" x2="1754" y1="377" y2="377"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2059" x2="2059" y1="-1046" y2="-1046"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2061" x2="2061" y1="-1053" y2="-1053"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2060" x2="2060" y1="-1054" y2="-1054"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1106" x2="1097" y1="-312" y2="-312"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1096" x2="1096" y1="-317" y2="-307"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1094" x2="1094" y1="-315" y2="-309"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1093" x2="1093" y1="-314" y2="-310"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1846" x2="1837" y1="-337" y2="-337"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1836" x2="1836" y1="-342" y2="-332"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1834" x2="1834" y1="-340" y2="-334"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1833" x2="1833" y1="-339" y2="-335"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1824" x2="1824" y1="597" y2="650"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1726" x2="1726" y1="597" y2="650"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1726" x2="1824" y1="650" y2="650"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1762" x2="1805" y1="606" y2="606"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1805" x2="1805" y1="606" y2="641"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1805" x2="1763" y1="641" y2="641"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1808" x2="1802" y1="618" y2="618"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1762" x2="1762" y1="646" y2="635"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1762" x2="1762" y1="601" y2="612"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1749" x2="1749" y1="615" y2="634"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1742" x2="1742" y1="620" y2="631"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1742" x2="1734" y1="625" y2="625"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1762" x2="1749" y1="635" y2="627"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1762" x2="1749" y1="612" y2="619"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1726" x2="1725" y1="624" y2="624"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1849" x2="1824" y1="646" y2="646"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1824" x2="1849" y1="602" y2="602"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1849" x2="1849" y1="602" y2="617"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1849" x2="1849" y1="630" y2="646"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1835" x2="1873" y1="630" y2="630"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1726" x2="1824" y1="597" y2="597"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1273" x2="1273" y1="600" y2="653"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1175" x2="1175" y1="600" y2="653"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1175" x2="1273" y1="653" y2="653"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1211" x2="1254" y1="609" y2="609"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1254" x2="1254" y1="609" y2="644"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1254" x2="1212" y1="644" y2="644"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1257" x2="1251" y1="621" y2="621"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1211" x2="1211" y1="649" y2="638"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1211" x2="1211" y1="604" y2="615"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1198" x2="1198" y1="618" y2="637"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1191" x2="1191" y1="623" y2="634"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1191" x2="1183" y1="628" y2="628"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1211" x2="1198" y1="638" y2="630"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1211" x2="1198" y1="615" y2="622"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1175" x2="1174" y1="627" y2="627"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1298" x2="1273" y1="649" y2="649"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1273" x2="1298" y1="605" y2="605"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1298" x2="1298" y1="605" y2="620"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1298" x2="1298" y1="633" y2="649"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1284" x2="1322" y1="633" y2="633"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1175" x2="1273" y1="600" y2="600"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-320721">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2001.227848 -217.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49879" ObjectName="SW-CX_LWZ.CX_LWZ_302XC"/>
     <cge:Meas_Ref ObjectId="320721"/>
    <cge:TPSR_Ref TObjectID="49879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320721">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2001.000000 -127.811765)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49880" ObjectName="SW-CX_LWZ.CX_LWZ_302XC1"/>
     <cge:Meas_Ref ObjectId="320721"/>
    <cge:TPSR_Ref TObjectID="49880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320717">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(0.857143 -0.000000 0.000000 -0.812500 2005.000000 -421.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49876" ObjectName="SW-CX_LWZ.CX_LWZ_20267SW"/>
     <cge:Meas_Ref ObjectId="320717"/>
    <cge:TPSR_Ref TObjectID="49876"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320685">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2076.000000 -889.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49857" ObjectName="SW-CX_LWZ.CX_LWZ_26167SW"/>
     <cge:Meas_Ref ObjectId="320685"/>
    <cge:TPSR_Ref TObjectID="49857"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320696">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1590.000000 -720.000000)" xlink:href="#switch2:shape44_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49859" ObjectName="SW-CX_LWZ.CX_LWZ_29010SW"/>
     <cge:Meas_Ref ObjectId="320696"/>
    <cge:TPSR_Ref TObjectID="49859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320695">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1662.000000 -746.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49858" ObjectName="SW-CX_LWZ.CX_LWZ_2901SW"/>
     <cge:Meas_Ref ObjectId="320695"/>
    <cge:TPSR_Ref TObjectID="49858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320697">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1682.000000 -802.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49860" ObjectName="SW-CX_LWZ.CX_LWZ_29017SW"/>
     <cge:Meas_Ref ObjectId="320697"/>
    <cge:TPSR_Ref TObjectID="49860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320794">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2339.000000 20.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49940" ObjectName="SW-CX_LWZ.CX_LWZ_356XC"/>
     <cge:Meas_Ref ObjectId="320794"/>
    <cge:TPSR_Ref TObjectID="49940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320794">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2339.772152 106.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49941" ObjectName="SW-CX_LWZ.CX_LWZ_356XC1"/>
     <cge:Meas_Ref ObjectId="320794"/>
    <cge:TPSR_Ref TObjectID="49941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320795">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2296.000000 173.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49942" ObjectName="SW-CX_LWZ.CX_LWZ_35667SW"/>
     <cge:Meas_Ref ObjectId="320795"/>
    <cge:TPSR_Ref TObjectID="49942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320784">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2476.000000 25.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49932" ObjectName="SW-CX_LWZ.CX_LWZ_357XC"/>
     <cge:Meas_Ref ObjectId="320784"/>
    <cge:TPSR_Ref TObjectID="49932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320784">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2476.772152 111.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49933" ObjectName="SW-CX_LWZ.CX_LWZ_357XC1"/>
     <cge:Meas_Ref ObjectId="320784"/>
    <cge:TPSR_Ref TObjectID="49933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320785">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2435.000000 176.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49934" ObjectName="SW-CX_LWZ.CX_LWZ_35767SW"/>
     <cge:Meas_Ref ObjectId="320785"/>
    <cge:TPSR_Ref TObjectID="49934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320779">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2620.000000 23.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49928" ObjectName="SW-CX_LWZ.CX_LWZ_358XC"/>
     <cge:Meas_Ref ObjectId="320779"/>
    <cge:TPSR_Ref TObjectID="49928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320779">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2620.772152 109.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49929" ObjectName="SW-CX_LWZ.CX_LWZ_358XC1"/>
     <cge:Meas_Ref ObjectId="320779"/>
    <cge:TPSR_Ref TObjectID="49929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320780">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2585.000000 174.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49930" ObjectName="SW-CX_LWZ.CX_LWZ_35867SW"/>
     <cge:Meas_Ref ObjectId="320780"/>
    <cge:TPSR_Ref TObjectID="49930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320769">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2903.000000 21.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49920" ObjectName="SW-CX_LWZ.CX_LWZ_361XC"/>
     <cge:Meas_Ref ObjectId="320769"/>
    <cge:TPSR_Ref TObjectID="49920"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320769">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2903.772152 107.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49921" ObjectName="SW-CX_LWZ.CX_LWZ_361XC1"/>
     <cge:Meas_Ref ObjectId="320769"/>
    <cge:TPSR_Ref TObjectID="49921"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320770">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2869.000000 173.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49922" ObjectName="SW-CX_LWZ.CX_LWZ_36167SW"/>
     <cge:Meas_Ref ObjectId="320770"/>
    <cge:TPSR_Ref TObjectID="49922"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320718">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -0.681818 -0.620690 -0.000000 1927.482759 -391.909091)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49877" ObjectName="SW-CX_LWZ.CX_LWZ_2020SW"/>
     <cge:Meas_Ref ObjectId="320718"/>
    <cge:TPSR_Ref TObjectID="49877"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320708">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1237.227848 -192.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49869" ObjectName="SW-CX_LWZ.CX_LWZ_301XC"/>
     <cge:Meas_Ref ObjectId="320708"/>
    <cge:TPSR_Ref TObjectID="49869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320708">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1237.000000 -102.811765)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49870" ObjectName="SW-CX_LWZ.CX_LWZ_301XC1"/>
     <cge:Meas_Ref ObjectId="320708"/>
    <cge:TPSR_Ref TObjectID="49870"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320704">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(0.857143 -0.000000 0.000000 -0.812500 1241.000000 -396.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49866" ObjectName="SW-CX_LWZ.CX_LWZ_20167SW"/>
     <cge:Meas_Ref ObjectId="320704"/>
    <cge:TPSR_Ref TObjectID="49866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320705">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -0.681818 -0.620690 -0.000000 1144.482759 -367.909091)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49867" ObjectName="SW-CX_LWZ.CX_LWZ_2010SW"/>
     <cge:Meas_Ref ObjectId="320705"/>
    <cge:TPSR_Ref TObjectID="49867"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1142.500000 -321.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1890.500000 -345.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320774">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2757.000000 19.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49924" ObjectName="SW-CX_LWZ.CX_LWZ_359XC"/>
     <cge:Meas_Ref ObjectId="320774"/>
    <cge:TPSR_Ref TObjectID="49924"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320774">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2757.772152 105.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49925" ObjectName="SW-CX_LWZ.CX_LWZ_359XC1"/>
     <cge:Meas_Ref ObjectId="320774"/>
    <cge:TPSR_Ref TObjectID="49925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320775">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2723.000000 171.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49926" ObjectName="SW-CX_LWZ.CX_LWZ_35967SW"/>
     <cge:Meas_Ref ObjectId="320775"/>
    <cge:TPSR_Ref TObjectID="49926"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320799">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2193.000000 20.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49944" ObjectName="SW-CX_LWZ.CX_LWZ_355XC"/>
     <cge:Meas_Ref ObjectId="320799"/>
    <cge:TPSR_Ref TObjectID="49944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320799">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2193.772152 106.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49945" ObjectName="SW-CX_LWZ.CX_LWZ_355XC1"/>
     <cge:Meas_Ref ObjectId="320799"/>
    <cge:TPSR_Ref TObjectID="49945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320800">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2149.000000 171.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49946" ObjectName="SW-CX_LWZ.CX_LWZ_35567SW"/>
     <cge:Meas_Ref ObjectId="320800"/>
    <cge:TPSR_Ref TObjectID="49946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320804">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2057.000000 17.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49948" ObjectName="SW-CX_LWZ.CX_LWZ_354XC"/>
     <cge:Meas_Ref ObjectId="320804"/>
    <cge:TPSR_Ref TObjectID="49948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320804">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2057.772152 103.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49949" ObjectName="SW-CX_LWZ.CX_LWZ_354XC1"/>
     <cge:Meas_Ref ObjectId="320804"/>
    <cge:TPSR_Ref TObjectID="49949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320805">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2013.000000 168.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49950" ObjectName="SW-CX_LWZ.CX_LWZ_35467SW"/>
     <cge:Meas_Ref ObjectId="320805"/>
    <cge:TPSR_Ref TObjectID="49950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320682">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2050.000000 -819.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49854" ObjectName="SW-CX_LWZ.CX_LWZ_2616SW"/>
     <cge:Meas_Ref ObjectId="320682"/>
    <cge:TPSR_Ref TObjectID="49854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320681">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2050.000000 -702.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49853" ObjectName="SW-CX_LWZ.CX_LWZ_2611SW"/>
     <cge:Meas_Ref ObjectId="320681"/>
    <cge:TPSR_Ref TObjectID="49853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320684">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2074.000000 -804.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49856" ObjectName="SW-CX_LWZ.CX_LWZ_26160SW"/>
     <cge:Meas_Ref ObjectId="320684"/>
    <cge:TPSR_Ref TObjectID="49856"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320683">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2075.000000 -746.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49855" ObjectName="SW-CX_LWZ.CX_LWZ_26117SW"/>
     <cge:Meas_Ref ObjectId="320683"/>
    <cge:TPSR_Ref TObjectID="49855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320700">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1219.000000 -584.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49862" ObjectName="SW-CX_LWZ.CX_LWZ_2011SW"/>
     <cge:Meas_Ref ObjectId="320700"/>
    <cge:TPSR_Ref TObjectID="49862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320702">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1245.000000 -561.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49864" ObjectName="SW-CX_LWZ.CX_LWZ_20117SW"/>
     <cge:Meas_Ref ObjectId="320702"/>
    <cge:TPSR_Ref TObjectID="49864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320701">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1219.000000 -441.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49863" ObjectName="SW-CX_LWZ.CX_LWZ_2016SW"/>
     <cge:Meas_Ref ObjectId="320701"/>
    <cge:TPSR_Ref TObjectID="49863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320703">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1243.000000 -490.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49865" ObjectName="SW-CX_LWZ.CX_LWZ_20160SW"/>
     <cge:Meas_Ref ObjectId="320703"/>
    <cge:TPSR_Ref TObjectID="49865"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320713">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1983.000000 -609.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49872" ObjectName="SW-CX_LWZ.CX_LWZ_2021SW"/>
     <cge:Meas_Ref ObjectId="320713"/>
    <cge:TPSR_Ref TObjectID="49872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320714">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1983.000000 -466.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49873" ObjectName="SW-CX_LWZ.CX_LWZ_2026SW"/>
     <cge:Meas_Ref ObjectId="320714"/>
    <cge:TPSR_Ref TObjectID="49873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320715">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(0.857143 -0.000000 0.000000 -0.812500 2010.000000 -587.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49874" ObjectName="SW-CX_LWZ.CX_LWZ_20217SW"/>
     <cge:Meas_Ref ObjectId="320715"/>
    <cge:TPSR_Ref TObjectID="49874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320716">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(0.857143 -0.000000 0.000000 -0.812500 2012.000000 -515.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49875" ObjectName="SW-CX_LWZ.CX_LWZ_20260SW"/>
     <cge:Meas_Ref ObjectId="320716"/>
    <cge:TPSR_Ref TObjectID="49875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320728">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1904.000000 18.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49886" ObjectName="SW-CX_LWZ.CX_LWZ_353XC"/>
     <cge:Meas_Ref ObjectId="320728"/>
    <cge:TPSR_Ref TObjectID="49886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320728">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1904.772152 104.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49887" ObjectName="SW-CX_LWZ.CX_LWZ_353XC1"/>
     <cge:Meas_Ref ObjectId="320728"/>
    <cge:TPSR_Ref TObjectID="49887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320729">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1871.000000 169.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49888" ObjectName="SW-CX_LWZ.CX_LWZ_35367SW"/>
     <cge:Meas_Ref ObjectId="320729"/>
    <cge:TPSR_Ref TObjectID="49888"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320741">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1766.867233 86.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49898" ObjectName="SW-CX_LWZ.CX_LWZ_352XC1"/>
     <cge:Meas_Ref ObjectId="320741"/>
    <cge:TPSR_Ref TObjectID="49898"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320741">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1766.867233 20.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49897" ObjectName="SW-CX_LWZ.CX_LWZ_352XC"/>
     <cge:Meas_Ref ObjectId="320741"/>
    <cge:TPSR_Ref TObjectID="49897"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320742">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1741.740650 157.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49899" ObjectName="SW-CX_LWZ.CX_LWZ_35237SW"/>
     <cge:Meas_Ref ObjectId="320742"/>
    <cge:TPSR_Ref TObjectID="49899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320744">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1782.740650 345.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49901" ObjectName="SW-CX_LWZ.CX_LWZ_3526SW"/>
     <cge:Meas_Ref ObjectId="320744"/>
    <cge:TPSR_Ref TObjectID="49901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1631.000000 18.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1631.772152 104.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1587.000000 169.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320743">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1693.000000 358.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49900" ObjectName="SW-CX_LWZ.CX_LWZ_35267SW"/>
     <cge:Meas_Ref ObjectId="320743"/>
    <cge:TPSR_Ref TObjectID="49900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320789">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1063.000000 28.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49936" ObjectName="SW-CX_LWZ.CX_LWZ_347XC"/>
     <cge:Meas_Ref ObjectId="320789"/>
    <cge:TPSR_Ref TObjectID="49936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320789">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1063.772152 114.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49937" ObjectName="SW-CX_LWZ.CX_LWZ_347XC1"/>
     <cge:Meas_Ref ObjectId="320789"/>
    <cge:TPSR_Ref TObjectID="49937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320790">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1022.000000 179.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49938" ObjectName="SW-CX_LWZ.CX_LWZ_34767SW"/>
     <cge:Meas_Ref ObjectId="320790"/>
    <cge:TPSR_Ref TObjectID="49938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320733">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1215.867233 89.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49891" ObjectName="SW-CX_LWZ.CX_LWZ_348XC1"/>
     <cge:Meas_Ref ObjectId="320733"/>
    <cge:TPSR_Ref TObjectID="49891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320733">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1215.867233 23.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49890" ObjectName="SW-CX_LWZ.CX_LWZ_348XC"/>
     <cge:Meas_Ref ObjectId="320733"/>
    <cge:TPSR_Ref TObjectID="49890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320734">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1190.740650 160.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49892" ObjectName="SW-CX_LWZ.CX_LWZ_34837SW"/>
     <cge:Meas_Ref ObjectId="320734"/>
    <cge:TPSR_Ref TObjectID="49892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320736">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1231.740650 348.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49894" ObjectName="SW-CX_LWZ.CX_LWZ_3486SW"/>
     <cge:Meas_Ref ObjectId="320736"/>
    <cge:TPSR_Ref TObjectID="49894"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320764">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 926.000000 25.167756)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49916" ObjectName="SW-CX_LWZ.CX_LWZ_346XC"/>
     <cge:Meas_Ref ObjectId="320764"/>
    <cge:TPSR_Ref TObjectID="49916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320764">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 926.772152 107.925926)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49917" ObjectName="SW-CX_LWZ.CX_LWZ_346XC1"/>
     <cge:Meas_Ref ObjectId="320764"/>
    <cge:TPSR_Ref TObjectID="49917"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320765">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 885.000000 170.518519)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49918" ObjectName="SW-CX_LWZ.CX_LWZ_34667SW"/>
     <cge:Meas_Ref ObjectId="320765"/>
    <cge:TPSR_Ref TObjectID="49918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320759">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 759.000000 26.167756)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49912" ObjectName="SW-CX_LWZ.CX_LWZ_345XC"/>
     <cge:Meas_Ref ObjectId="320759"/>
    <cge:TPSR_Ref TObjectID="49912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320759">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 759.772152 108.925926)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49913" ObjectName="SW-CX_LWZ.CX_LWZ_345XC1"/>
     <cge:Meas_Ref ObjectId="320759"/>
    <cge:TPSR_Ref TObjectID="49913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320760">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 718.000000 171.518519)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49914" ObjectName="SW-CX_LWZ.CX_LWZ_34567SW"/>
     <cge:Meas_Ref ObjectId="320760"/>
    <cge:TPSR_Ref TObjectID="49914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320749">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 448.000000 27.167756)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49904" ObjectName="SW-CX_LWZ.CX_LWZ_343XC"/>
     <cge:Meas_Ref ObjectId="320749"/>
    <cge:TPSR_Ref TObjectID="49904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320749">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 448.772152 109.925926)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49905" ObjectName="SW-CX_LWZ.CX_LWZ_343XC1"/>
     <cge:Meas_Ref ObjectId="320749"/>
    <cge:TPSR_Ref TObjectID="49905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320750">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 407.000000 172.518519)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49906" ObjectName="SW-CX_LWZ.CX_LWZ_34367SW"/>
     <cge:Meas_Ref ObjectId="320750"/>
    <cge:TPSR_Ref TObjectID="49906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 144.000000 25.167756)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 144.772152 107.925926)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 103.000000 170.518519)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 306.000000 27.167756)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 306.772152 109.925926)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 265.000000 172.518519)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320754">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 599.000000 26.167756)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49908" ObjectName="SW-CX_LWZ.CX_LWZ_344XC"/>
     <cge:Meas_Ref ObjectId="320754"/>
    <cge:TPSR_Ref TObjectID="49908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320754">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 599.772152 108.925926)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49909" ObjectName="SW-CX_LWZ.CX_LWZ_344XC1"/>
     <cge:Meas_Ref ObjectId="320754"/>
    <cge:TPSR_Ref TObjectID="49909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320755">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 558.000000 171.518519)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49910" ObjectName="SW-CX_LWZ.CX_LWZ_34467SW"/>
     <cge:Meas_Ref ObjectId="320755"/>
    <cge:TPSR_Ref TObjectID="49910"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320735">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1142.000000 361.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49893" ObjectName="SW-CX_LWZ.CX_LWZ_34867SW"/>
     <cge:Meas_Ref ObjectId="320735"/>
    <cge:TPSR_Ref TObjectID="49893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320725">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.708861 1734.000000 -67.797468)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49884" ObjectName="SW-CX_LWZ.CX_LWZ_3921XC1"/>
     <cge:Meas_Ref ObjectId="320725"/>
    <cge:TPSR_Ref TObjectID="49884"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-320724">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.708861 981.000000 -70.797468)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49881" ObjectName="SW-CX_LWZ.CX_LWZ_3911XC"/>
     <cge:Meas_Ref ObjectId="320724"/>
    <cge:TPSR_Ref TObjectID="49881"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-323606">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1423.867233 25.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="50183" ObjectName="SW-CX_LWZ.CX_LWZ_3121XC"/>
     <cge:Meas_Ref ObjectId="323606"/>
    <cge:TPSR_Ref TObjectID="50183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-323606">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1423.867233 77.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="50184" ObjectName="SW-CX_LWZ.CX_LWZ_3121XC1"/>
     <cge:Meas_Ref ObjectId="323606"/>
    <cge:TPSR_Ref TObjectID="50184"/></metadata>
   </g>
  </g><g id="Reactance_Layer">
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1764.740650 545.000000)" xlink:href="#reactance:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1213.740650 548.000000)" xlink:href="#reactance:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2754ec0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1732.000000 -164.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2710e30">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1705.000000 -196.188235)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2abecb0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2081.000000 -1087.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a94990">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1690.000000 -856.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a8fe40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2385.000000 198.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29ee420">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 2345.000000 189.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a712d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2522.000000 203.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aa3ac0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2666.000000 201.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a3f1f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2949.000000 199.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2941a50">
    <use class="BV-220KV" transform="matrix(-0.620690 0.000000 -0.000000 -0.681818 1911.724138 -389.863636)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29422c0">
    <use class="BV-220KV" transform="matrix(-0.620690 -0.000000 0.000000 -0.681818 1892.724138 -389.863636)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a35a50">
    <use class="BV-35KV" transform="matrix(0.000000 -0.642857 -0.694915 -0.000000 2041.000000 -221.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29d21a0">
    <use class="BV-35KV" transform="matrix(0.000000 -0.642857 -0.694915 -0.000000 1277.000000 -196.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29d6ad0">
    <use class="BV-220KV" transform="matrix(-0.620690 0.000000 -0.000000 -0.681818 1128.724138 -365.863636)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29d7340">
    <use class="BV-220KV" transform="matrix(-0.620690 -0.000000 0.000000 -0.681818 1109.724138 -365.863636)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2915fe0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2803.000000 197.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29aabe0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2239.000000 198.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29b60d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 2199.000000 189.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29bcbe0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2103.000000 195.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_297ce70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 2063.000000 186.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29840f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1950.000000 196.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2990520">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 1910.000000 187.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28ba670">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1825.740650 194.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28be8a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 1771.740650 199.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28bf520">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1785.740650 263.000000)" xlink:href="#lightningRod:shape194"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28c6ad0">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1701.740650 380.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28d5300">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1677.000000 196.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b2a4e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 1637.000000 187.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b35010">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1109.000000 206.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2936a50">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1274.740650 197.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_293ac80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 1220.740650 202.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_293b900">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1234.740650 266.000000)" xlink:href="#lightningRod:shape194"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_294a2e0">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1150.740650 383.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2954330">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 972.000000 197.518519)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28f60a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 805.000000 198.518519)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2904a50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 494.000000 199.518519)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c1d220">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 190.000000 197.518519)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c2c190">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 352.000000 199.518519)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c3aae0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.962963 645.000000 198.518519)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c47a30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 979.000000 -167.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c482b0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 952.000000 -199.188235)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c518f0">
    <use class="BV-0KV" transform="matrix(0.514286 0.000000 0.000000 -0.463277 1875.400000 -296.333333)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c535d0">
    <use class="BV-0KV" transform="matrix(0.514286 0.000000 0.000000 -0.463277 1129.400000 -273.333333)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320911" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 418.000000 302.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320911" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49903"/>
     <cge:Term_Ref ObjectID="50541"/>
    <cge:TPSR_Ref TObjectID="49903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320912" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 418.000000 302.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320912" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49903"/>
     <cge:Term_Ref ObjectID="50541"/>
    <cge:TPSR_Ref TObjectID="49903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320907" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 418.000000 302.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320907" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49903"/>
     <cge:Term_Ref ObjectID="50541"/>
    <cge:TPSR_Ref TObjectID="49903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320923" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 583.000000 303.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320923" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49907"/>
     <cge:Term_Ref ObjectID="50549"/>
    <cge:TPSR_Ref TObjectID="49907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320924" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 583.000000 303.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320924" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49907"/>
     <cge:Term_Ref ObjectID="50549"/>
    <cge:TPSR_Ref TObjectID="49907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320919" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 583.000000 303.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320919" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49907"/>
     <cge:Term_Ref ObjectID="50549"/>
    <cge:TPSR_Ref TObjectID="49907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320935" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 738.000000 300.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320935" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49911"/>
     <cge:Term_Ref ObjectID="50557"/>
    <cge:TPSR_Ref TObjectID="49911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320936" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 738.000000 300.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320936" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49911"/>
     <cge:Term_Ref ObjectID="50557"/>
    <cge:TPSR_Ref TObjectID="49911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320931" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 738.000000 300.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320931" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49911"/>
     <cge:Term_Ref ObjectID="50557"/>
    <cge:TPSR_Ref TObjectID="49911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320947" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 905.000000 299.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320947" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49915"/>
     <cge:Term_Ref ObjectID="50565"/>
    <cge:TPSR_Ref TObjectID="49915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320948" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 905.000000 299.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320948" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49915"/>
     <cge:Term_Ref ObjectID="50565"/>
    <cge:TPSR_Ref TObjectID="49915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320943" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 905.000000 299.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320943" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49915"/>
     <cge:Term_Ref ObjectID="50565"/>
    <cge:TPSR_Ref TObjectID="49915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320959" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 302.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320959" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49935"/>
     <cge:Term_Ref ObjectID="50605"/>
    <cge:TPSR_Ref TObjectID="49935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320960" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 302.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320960" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49935"/>
     <cge:Term_Ref ObjectID="50605"/>
    <cge:TPSR_Ref TObjectID="49935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320955" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 302.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320955" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49935"/>
     <cge:Term_Ref ObjectID="50605"/>
    <cge:TPSR_Ref TObjectID="49935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320971" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1372.000000 161.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320971" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49889"/>
     <cge:Term_Ref ObjectID="50513"/>
    <cge:TPSR_Ref TObjectID="49889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320972" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1372.000000 161.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320972" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49889"/>
     <cge:Term_Ref ObjectID="50513"/>
    <cge:TPSR_Ref TObjectID="49889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320967" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1372.000000 161.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320967" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49889"/>
     <cge:Term_Ref ObjectID="50513"/>
    <cge:TPSR_Ref TObjectID="49889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320983" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1576.000000 290.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320983" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49896"/>
     <cge:Term_Ref ObjectID="50527"/>
    <cge:TPSR_Ref TObjectID="49896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320984" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1576.000000 290.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320984" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49896"/>
     <cge:Term_Ref ObjectID="50527"/>
    <cge:TPSR_Ref TObjectID="49896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320979" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1576.000000 290.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320979" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49896"/>
     <cge:Term_Ref ObjectID="50527"/>
    <cge:TPSR_Ref TObjectID="49896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320899" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1882.000000 353.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320899" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49885"/>
     <cge:Term_Ref ObjectID="50505"/>
    <cge:TPSR_Ref TObjectID="49885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320900" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1882.000000 353.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320900" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49885"/>
     <cge:Term_Ref ObjectID="50505"/>
    <cge:TPSR_Ref TObjectID="49885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320895" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1882.000000 353.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320895" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49885"/>
     <cge:Term_Ref ObjectID="50505"/>
    <cge:TPSR_Ref TObjectID="49885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-321067" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2049.000000 350.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="321067" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49947"/>
     <cge:Term_Ref ObjectID="50629"/>
    <cge:TPSR_Ref TObjectID="49947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-321068" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2049.000000 350.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="321068" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49947"/>
     <cge:Term_Ref ObjectID="50629"/>
    <cge:TPSR_Ref TObjectID="49947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-321063" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2049.000000 350.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="321063" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49947"/>
     <cge:Term_Ref ObjectID="50629"/>
    <cge:TPSR_Ref TObjectID="49947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-321055" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2185.000000 353.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="321055" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49943"/>
     <cge:Term_Ref ObjectID="50621"/>
    <cge:TPSR_Ref TObjectID="49943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-321056" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2185.000000 353.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="321056" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49943"/>
     <cge:Term_Ref ObjectID="50621"/>
    <cge:TPSR_Ref TObjectID="49943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-321051" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2185.000000 353.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="321051" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49943"/>
     <cge:Term_Ref ObjectID="50621"/>
    <cge:TPSR_Ref TObjectID="49943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320995" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2331.000000 353.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320995" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49939"/>
     <cge:Term_Ref ObjectID="50613"/>
    <cge:TPSR_Ref TObjectID="49939"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320996" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2331.000000 353.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320996" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49939"/>
     <cge:Term_Ref ObjectID="50613"/>
    <cge:TPSR_Ref TObjectID="49939"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320991" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2331.000000 353.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320991" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49939"/>
     <cge:Term_Ref ObjectID="50613"/>
    <cge:TPSR_Ref TObjectID="49939"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-321043" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2468.000000 358.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="321043" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49931"/>
     <cge:Term_Ref ObjectID="50597"/>
    <cge:TPSR_Ref TObjectID="49931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-321044" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2468.000000 358.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="321044" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49931"/>
     <cge:Term_Ref ObjectID="50597"/>
    <cge:TPSR_Ref TObjectID="49931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-321039" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2468.000000 358.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="321039" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49931"/>
     <cge:Term_Ref ObjectID="50597"/>
    <cge:TPSR_Ref TObjectID="49931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-321031" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2612.000000 356.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="321031" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49927"/>
     <cge:Term_Ref ObjectID="50589"/>
    <cge:TPSR_Ref TObjectID="49927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-321032" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2612.000000 356.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="321032" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49927"/>
     <cge:Term_Ref ObjectID="50589"/>
    <cge:TPSR_Ref TObjectID="49927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-321027" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2612.000000 356.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="321027" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49927"/>
     <cge:Term_Ref ObjectID="50589"/>
    <cge:TPSR_Ref TObjectID="49927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-321019" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2749.000000 352.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="321019" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49923"/>
     <cge:Term_Ref ObjectID="50581"/>
    <cge:TPSR_Ref TObjectID="49923"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-321020" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2749.000000 352.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="321020" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49923"/>
     <cge:Term_Ref ObjectID="50581"/>
    <cge:TPSR_Ref TObjectID="49923"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-321015" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2749.000000 352.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="321015" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49923"/>
     <cge:Term_Ref ObjectID="50581"/>
    <cge:TPSR_Ref TObjectID="49923"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-321007" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2895.000000 354.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="321007" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49919"/>
     <cge:Term_Ref ObjectID="50573"/>
    <cge:TPSR_Ref TObjectID="49919"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-321008" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2895.000000 354.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="321008" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49919"/>
     <cge:Term_Ref ObjectID="50573"/>
    <cge:TPSR_Ref TObjectID="49919"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-321003" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2895.000000 354.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="321003" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49919"/>
     <cge:Term_Ref ObjectID="50573"/>
    <cge:TPSR_Ref TObjectID="49919"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320842" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1400.000000 -307.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320842" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49868"/>
     <cge:Term_Ref ObjectID="50471"/>
    <cge:TPSR_Ref TObjectID="49868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320843" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1400.000000 -307.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320843" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49868"/>
     <cge:Term_Ref ObjectID="50471"/>
    <cge:TPSR_Ref TObjectID="49868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320839" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1400.000000 -307.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320839" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49868"/>
     <cge:Term_Ref ObjectID="50471"/>
    <cge:TPSR_Ref TObjectID="49868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320836" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1503.000000 -529.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320836" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49861"/>
     <cge:Term_Ref ObjectID="50457"/>
    <cge:TPSR_Ref TObjectID="49861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320837" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1503.000000 -529.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320837" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49861"/>
     <cge:Term_Ref ObjectID="50457"/>
    <cge:TPSR_Ref TObjectID="49861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320833" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1503.000000 -529.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320833" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49861"/>
     <cge:Term_Ref ObjectID="50457"/>
    <cge:TPSR_Ref TObjectID="49861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320852" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2197.000000 -578.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320852" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49871"/>
     <cge:Term_Ref ObjectID="50477"/>
    <cge:TPSR_Ref TObjectID="49871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320853" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2197.000000 -578.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320853" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49871"/>
     <cge:Term_Ref ObjectID="50477"/>
    <cge:TPSR_Ref TObjectID="49871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320849" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2197.000000 -578.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320849" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49871"/>
     <cge:Term_Ref ObjectID="50477"/>
    <cge:TPSR_Ref TObjectID="49871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320858" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2134.000000 -155.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320858" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49878"/>
     <cge:Term_Ref ObjectID="50491"/>
    <cge:TPSR_Ref TObjectID="49878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320859" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2134.000000 -155.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320859" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49878"/>
     <cge:Term_Ref ObjectID="50491"/>
    <cge:TPSR_Ref TObjectID="49878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320855" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2134.000000 -155.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320855" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49878"/>
     <cge:Term_Ref ObjectID="50491"/>
    <cge:TPSR_Ref TObjectID="49878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-320830" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2360.000000 -808.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320830" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49852"/>
     <cge:Term_Ref ObjectID="50439"/>
    <cge:TPSR_Ref TObjectID="49852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-320831" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2360.000000 -808.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320831" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49852"/>
     <cge:Term_Ref ObjectID="50439"/>
    <cge:TPSR_Ref TObjectID="49852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-320827" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2360.000000 -808.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320827" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49852"/>
     <cge:Term_Ref ObjectID="50439"/>
    <cge:TPSR_Ref TObjectID="49852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-320865" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 841.000000 -781.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320865" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49954"/>
     <cge:Term_Ref ObjectID="50651"/>
    <cge:TPSR_Ref TObjectID="49954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-320866" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 841.000000 -781.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320866" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49954"/>
     <cge:Term_Ref ObjectID="50651"/>
    <cge:TPSR_Ref TObjectID="49954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-320867" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 841.000000 -781.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320867" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49954"/>
     <cge:Term_Ref ObjectID="50651"/>
    <cge:TPSR_Ref TObjectID="49954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-320871" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 841.000000 -781.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320871" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49954"/>
     <cge:Term_Ref ObjectID="50651"/>
    <cge:TPSR_Ref TObjectID="49954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-320868" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 841.000000 -781.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320868" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49954"/>
     <cge:Term_Ref ObjectID="50651"/>
    <cge:TPSR_Ref TObjectID="49954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-320872" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 841.000000 -781.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320872" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49954"/>
     <cge:Term_Ref ObjectID="50651"/>
    <cge:TPSR_Ref TObjectID="49954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-320873" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 65.000000 -141.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49955"/>
     <cge:Term_Ref ObjectID="50652"/>
    <cge:TPSR_Ref TObjectID="49955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-320874" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 65.000000 -141.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49955"/>
     <cge:Term_Ref ObjectID="50652"/>
    <cge:TPSR_Ref TObjectID="49955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-320875" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 65.000000 -141.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320875" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49955"/>
     <cge:Term_Ref ObjectID="50652"/>
    <cge:TPSR_Ref TObjectID="49955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-320879" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 65.000000 -141.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320879" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49955"/>
     <cge:Term_Ref ObjectID="50652"/>
    <cge:TPSR_Ref TObjectID="49955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-320876" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 65.000000 -141.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320876" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49955"/>
     <cge:Term_Ref ObjectID="50652"/>
    <cge:TPSR_Ref TObjectID="49955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-320880" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 65.000000 -141.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320880" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49955"/>
     <cge:Term_Ref ObjectID="50652"/>
    <cge:TPSR_Ref TObjectID="49955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-320881" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3007.000000 -130.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49956"/>
     <cge:Term_Ref ObjectID="50653"/>
    <cge:TPSR_Ref TObjectID="49956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-320882" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3007.000000 -130.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320882" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49956"/>
     <cge:Term_Ref ObjectID="50653"/>
    <cge:TPSR_Ref TObjectID="49956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-320883" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3007.000000 -130.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320883" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49956"/>
     <cge:Term_Ref ObjectID="50653"/>
    <cge:TPSR_Ref TObjectID="49956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-320887" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3007.000000 -130.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320887" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49956"/>
     <cge:Term_Ref ObjectID="50653"/>
    <cge:TPSR_Ref TObjectID="49956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-320884" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3007.000000 -130.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320884" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49956"/>
     <cge:Term_Ref ObjectID="50653"/>
    <cge:TPSR_Ref TObjectID="49956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-320888" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3007.000000 -130.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="320888" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49956"/>
     <cge:Term_Ref ObjectID="50653"/>
    <cge:TPSR_Ref TObjectID="49956"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_省调直调电厂_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="175" x="-255" y="-1095"/></g>
   <g href="cx_索引_接线图_省调直调电厂_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-304" y="-1112"/></g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="1762" cy="641" fill="rgb(60,120,255)" fillStyle="1" r="3" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1762" cy="607" fill="rgb(60,120,255)" fillStyle="1" r="3" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1211" cy="644" fill="rgb(60,120,255)" fillStyle="1" r="3" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1211" cy="610" fill="rgb(60,120,255)" fillStyle="1" r="3" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2aad490">
    <use class="BV-220KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 2039.000000 -1061.000000)" xlink:href="#voltageTransformer:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_296a580">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1626.000000 -891.000000)" xlink:href="#voltageTransformer:shape71"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a3cd20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1708.000000 -236.000000)" xlink:href="#voltageTransformer:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c49700">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 955.000000 -239.000000)" xlink:href="#voltageTransformer:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -267.000000 -1036.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="10" stroke="rgb(255,255,0)" stroke-width="1" width="20" x="1106" y="-318"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="10" stroke="rgb(255,255,0)" stroke-width="1" width="20" x="1846" y="-343"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="26" stroke="rgb(255,255,0)" stroke-width="1" width="12" x="1729" y="431"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="6" stroke="rgb(60,120,255)" stroke-width="1" width="39" x="1834" y="617"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="26" stroke="rgb(255,255,0)" stroke-width="1" width="12" x="1178" y="434"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="6" stroke="rgb(60,120,255)" stroke-width="1" width="39" x="1283" y="620"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="175" x="-255" y="-1095"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="175" x="-255" y="-1095"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-304" y="-1112"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-304" y="-1112"/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1883.000000 316.000000)" xlink:href="#transformer2:shape77_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1883.000000 316.000000)" xlink:href="#transformer2:shape77_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_LWZ"/>
</svg>