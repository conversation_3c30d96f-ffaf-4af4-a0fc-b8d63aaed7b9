<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-135" aopId="3940870" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1200 1359 1323">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.7" x1="19" x2="10" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="25" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="16" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="20" x2="11" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="11" x2="2" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="11" x2="11" y1="9" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="66"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="25" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="16" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="11" x2="11" y1="9" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="11" x2="2" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="20" x2="11" y1="17" y2="8"/>
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="40"/>
   </symbol>
   <symbol id="capacitor:shape18">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="52" x2="52" y1="45" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="16" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="26" x2="26" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="43" x2="43" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="8" x2="8" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="7" x2="7" y1="54" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="8" x2="42" y1="55" y2="55"/>
    <rect height="23" stroke-width="0.398039" width="12" x="20" y="27"/>
    <rect height="24" stroke-width="0.398039" width="12" x="1" y="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="41" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="26" x2="26" y1="88" y2="21"/>
    <polyline arcFlag="1" points="43,36 44,36 45,36 45,37 46,37 46,37 47,38 47,38 48,39 48,39 48,40 48,40 49,41 49,42 49,42 48,43 48,44 48,44 48,45 47,45 47,46 46,46 46,47 45,47 45,47 44,47 43,47 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,25 44,25 45,25 45,25 46,26 46,26 47,26 47,27 48,27 48,28 48,29 48,29 49,30 49,31 49,31 48,32 48,33 48,33 48,34 47,34 47,35 46,35 46,35 45,36 45,36 44,36 43,36 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,15 44,15 45,15 45,15 46,15 46,16 47,16 47,17 48,17 48,18 48,18 48,19 49,20 49,20 49,21 48,22 48,22 48,23 48,23 47,24 47,24 46,25 46,25 45,25 45,26 44,26 43,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="25" x2="25" y1="101" y2="109"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="38" x2="26" y1="88" y2="88"/>
    <polyline arcFlag="1" points="25,101 23,101 21,100 20,100 18,99 17,98 15,97 14,95 13,93 13,92 12,90 12,88 12,86 13,84 13,83 14,81 15,80 17,78 18,77 20,76 21,76 23,75 25,75 27,75 29,76 30,76 32,77 33,78 35,80 36,81 37,83 37,84 38,86 38,88 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape163">
    <ellipse cx="19" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <polyline points="34,21 34,9 19,9 " stroke-width="1"/>
    <polyline points="35,33 35,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="29" y1="28" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="44" y1="28" y2="28"/>
    <rect height="9" stroke-width="1" width="5" x="32" y="21"/>
    <ellipse cx="8" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="32" x2="38" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="34" x2="36" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="31" x2="39" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="7" x2="7" y1="20" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="10" x2="7" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="4" x2="7" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="10" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="9" x2="9" y1="12" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="19" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="19" y2="21"/>
   </symbol>
   <symbol id="lightningRod:shape188">
    <polyline DF8003:Layer="PUBLIC" points="19,18 10,0 1,19 19,18 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape190">
    <ellipse cx="24" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="25" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="25" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="33" x2="39" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="35" x2="33" y1="29" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="36" x2="39" y1="29" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="40" x2="37" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="34" x2="37" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="37" x2="37" y1="20" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="25" y1="34" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="32" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="25" y1="34" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="13" x2="5" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="10" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="12" x2="6" y1="4" y2="4"/>
    <ellipse cx="35" cy="30" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="24" cy="30" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="35" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <rect height="9" stroke-width="1" width="5" x="7" y="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="0" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="15" y1="12" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="19" y2="19"/>
    <polyline points="9,7 9,10 " stroke-width="1"/>
    <polyline points="10,19 10,31 25,31 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape191">
    <circle cx="32" cy="80" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="31" y1="81" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="35" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="27" y1="87" y2="81"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,59 6,59 6,30 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,16 38,28 26,28 32,16 32,16 32,16 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="43" y2="1"/>
    <circle cx="32" cy="58" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="28" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="36" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="59" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="23" y2="23"/>
   </symbol>
   <symbol id="lightningRod:shape199">
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <polyline points="15,10 6,10 " stroke-width="1"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <polyline points="16,30 7,30 " stroke-width="1"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="74" y2="66"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1585fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1578750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_153f3a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_14f3470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1541b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1543420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1543d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_14ecfc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_14f8930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1550ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1561160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1561a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_154fde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1567330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_14e9c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_14ea3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15ad3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15a1280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15a1a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15400a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15410d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_155f620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1570b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15a2e30" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15a3060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19ef000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19f8380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    
   </symbol>
   <symbol id="Tag:shape33">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_19f7a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape34">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_19f4640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape36">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
   </symbol>
   <symbol id="Tag:shape37">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1333" width="1369" x="3112" y="-1205"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3215" y="-477"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-83926">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3920.000000 -1068.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18534" ObjectName="SW-CX_TF.CX_TF_3716SW"/>
     <cge:Meas_Ref ObjectId="83926"/>
    <cge:TPSR_Ref TObjectID="18534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83927">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3968.000000 -1087.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18535" ObjectName="SW-CX_TF.CX_TF_37167SW"/>
     <cge:Meas_Ref ObjectId="83927"/>
    <cge:TPSR_Ref TObjectID="18535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3828.000000 -1087.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83963">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4193.000000 -945.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18540" ObjectName="SW-CX_TF.CX_TF_3901XC1"/>
     <cge:Meas_Ref ObjectId="83963"/>
    <cge:TPSR_Ref TObjectID="18540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83963">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4193.000000 -904.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18539" ObjectName="SW-CX_TF.CX_TF_3901XC"/>
     <cge:Meas_Ref ObjectId="83963"/>
    <cge:TPSR_Ref TObjectID="18539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83894">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3628.000000 -161.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18523" ObjectName="SW-CX_TF.CX_TF_0716SW"/>
     <cge:Meas_Ref ObjectId="83894"/>
    <cge:TPSR_Ref TObjectID="18523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83895">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3596.000000 -235.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18524" ObjectName="SW-CX_TF.CX_TF_07127SW"/>
     <cge:Meas_Ref ObjectId="83895"/>
    <cge:TPSR_Ref TObjectID="18524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83904">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3878.000000 -164.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18526" ObjectName="SW-CX_TF.CX_TF_0726SW"/>
     <cge:Meas_Ref ObjectId="83904"/>
    <cge:TPSR_Ref TObjectID="18526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83905">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3846.000000 -238.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18527" ObjectName="SW-CX_TF.CX_TF_07227SW"/>
     <cge:Meas_Ref ObjectId="83905"/>
    <cge:TPSR_Ref TObjectID="18527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83965">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -356.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18543" ObjectName="SW-CX_TF.CX_TF_0901XC"/>
     <cge:Meas_Ref ObjectId="83965"/>
    <cge:TPSR_Ref TObjectID="18543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83965">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -294.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18544" ObjectName="SW-CX_TF.CX_TF_0901XC1"/>
     <cge:Meas_Ref ObjectId="83965"/>
    <cge:TPSR_Ref TObjectID="18544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83938">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4125.000000 -720.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18537" ObjectName="SW-CX_TF.CX_TF_30167SW"/>
     <cge:Meas_Ref ObjectId="83938"/>
    <cge:TPSR_Ref TObjectID="18537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83964">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3780.000000 -850.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18541" ObjectName="SW-CX_TF.CX_TF_35ZybXC"/>
     <cge:Meas_Ref ObjectId="83964"/>
    <cge:TPSR_Ref TObjectID="18541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83964">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3780.000000 -776.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18542" ObjectName="SW-CX_TF.CX_TF_35ZybXC1"/>
     <cge:Meas_Ref ObjectId="83964"/>
    <cge:TPSR_Ref TObjectID="18542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83917">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4063.000000 -240.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18532" ObjectName="SW-CX_TF.CX_TF_07327SW"/>
     <cge:Meas_Ref ObjectId="83917"/>
    <cge:TPSR_Ref TObjectID="18532"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_TF.CX_TF_1C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4079.000000 -82.000000)" xlink:href="#capacitor:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18547" ObjectName="CB-CX_TF.CX_TF_1C"/>
    <cge:TPSR_Ref TObjectID="18547"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2f78f60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3971.000000 -1060.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ad2380" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3599.000000 -208.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_271bfc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3849.000000 -211.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3469250" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4128.000000 -692.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fd9fe0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4066.000000 -213.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_350f3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-1059 3929,-1073 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2556ec0@1" ObjectIDZND0="18534@0" Pin0InfoVect0LinkObjId="SW-83926_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2556ec0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-1059 3929,-1073 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3259500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-1109 3929,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="18534@1" ObjectIDZND0="0@x" ObjectIDZND1="18535@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-83927_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83926_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-1109 3929,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3534d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-1137 3977,-1137 3977,-1128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="18534@x" ObjectIDZND0="18535@1" Pin0InfoVect0LinkObjId="SW-83927_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-83926_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-1137 3977,-1137 3977,-1128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f79bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3977,-1092 3977,-1076 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18535@0" ObjectIDZND0="g_2f78f60@0" Pin0InfoVect0LinkObjId="g_2f78f60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83927_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3977,-1092 3977,-1076 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f55980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-1157 3833,-1157 3833,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="18535@x" ObjectIDND1="18534@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-83927_0" Pin1InfoVect1LinkObjId="SW-83926_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-1157 3833,-1157 3833,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25782b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-1137 3929,-1157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="18535@x" ObjectIDND1="18534@x" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-83927_0" Pin1InfoVect1LinkObjId="SW-83926_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-1137 3929,-1157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a1a2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-1157 3929,-1176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="18535@x" ObjectIDND1="18534@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-83927_0" Pin1InfoVect1LinkObjId="SW-83926_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-1157 3929,-1176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_309a500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4203,-1033 4203,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_34ca480@0" ObjectIDZND0="g_2fd1990@0" Pin0InfoVect0LinkObjId="g_2fd1990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34ca480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4203,-1033 4203,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e980a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4203,-951 4203,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="18540@1" ObjectIDZND0="18539@1" Pin0InfoVect0LinkObjId="SW-83963_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83963_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4203,-951 4203,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33b8070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4203,-910 4203,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="18539@0" ObjectIDZND0="18519@0" Pin0InfoVect0LinkObjId="g_2e63b30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83963_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4203,-910 4203,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_301dc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4051,-682 4051,-705 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="18546@1" ObjectIDZND0="g_3850a60@0" Pin0InfoVect0LinkObjId="g_3850a60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4051,-682 4051,-705 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ea9c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-604 4050,-583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="18546@0" ObjectIDZND0="g_359a250@1" Pin0InfoVect0LinkObjId="g_359a250_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-604 4050,-583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fd8fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3605,-240 3605,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18524@0" ObjectIDZND0="g_1ad2380@0" Pin0InfoVect0LinkObjId="g_1ad2380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83895_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3605,-240 3605,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33aebc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3672,-133 3672,-149 3637,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_264bd10@0" ObjectIDZND0="g_2521080@0" ObjectIDZND1="18523@x" Pin0InfoVect0LinkObjId="g_2521080_0" Pin0InfoVect1LinkObjId="SW-83894_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_264bd10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3672,-133 3672,-149 3637,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c2bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3637,-62 3637,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2521080@0" ObjectIDZND0="g_264bd10@0" ObjectIDZND1="18523@x" Pin0InfoVect0LinkObjId="g_264bd10_0" Pin0InfoVect1LinkObjId="SW-83894_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2521080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3637,-62 3637,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2079a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3637,-149 3637,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_264bd10@0" ObjectIDND1="g_2521080@0" ObjectIDZND0="18523@0" Pin0InfoVect0LinkObjId="SW-83894_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_264bd10_0" Pin1InfoVect1LinkObjId="g_2521080_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3637,-149 3637,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e05a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3637,-202 3637,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="18523@1" ObjectIDZND0="g_2f9d330@0" Pin0InfoVect0LinkObjId="g_2f9d330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83894_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3637,-202 3637,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2522b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3938,-270 3938,-291 3855,-291 3855,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1ad35c0@0" ObjectIDZND0="18527@1" Pin0InfoVect0LinkObjId="SW-83905_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ad35c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3938,-270 3938,-291 3855,-291 3855,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26c5570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-243 3855,-229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18527@0" ObjectIDZND0="g_271bfc0@0" Pin0InfoVect0LinkObjId="g_271bfc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83905_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-243 3855,-229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_264ee50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3922,-136 3922,-152 3887,-152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1fbe0c0@0" ObjectIDZND0="g_2613440@0" ObjectIDZND1="18526@x" Pin0InfoVect0LinkObjId="g_2613440_0" Pin0InfoVect1LinkObjId="SW-83904_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fbe0c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3922,-136 3922,-152 3887,-152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2527180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3887,-65 3887,-152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2613440@0" ObjectIDZND0="g_1fbe0c0@0" ObjectIDZND1="18526@x" Pin0InfoVect0LinkObjId="g_1fbe0c0_0" Pin0InfoVect1LinkObjId="SW-83904_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2613440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3887,-65 3887,-152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ad8d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3887,-152 3887,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1fbe0c0@0" ObjectIDND1="g_2613440@0" ObjectIDZND0="18526@0" Pin0InfoVect0LinkObjId="SW-83904_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fbe0c0_0" Pin1InfoVect1LinkObjId="g_2613440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3887,-152 3887,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2be7430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3887,-205 3887,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="18526@1" ObjectIDZND0="g_2fd8880@0" Pin0InfoVect0LinkObjId="g_2fd8880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83904_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3887,-205 3887,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c3660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4346,-408 4346,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18520@0" ObjectIDZND0="18543@0" Pin0InfoVect0LinkObjId="SW-83965_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e1b7c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4346,-408 4346,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34633c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4346,-362 4346,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="18543@1" ObjectIDZND0="18544@1" Pin0InfoVect0LinkObjId="SW-83965_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83965_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4346,-362 4346,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32393e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4409,-283 4409,-289 4346,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_32b0070@0" ObjectIDZND0="18544@x" ObjectIDZND1="g_20c9920@0" Pin0InfoVect0LinkObjId="SW-83965_0" Pin0InfoVect1LinkObjId="g_20c9920_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32b0070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4409,-283 4409,-289 4346,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27145d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4346,-300 4346,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="18544@0" ObjectIDZND0="g_32b0070@0" ObjectIDZND1="g_20c9920@0" Pin0InfoVect0LinkObjId="g_32b0070_0" Pin0InfoVect1LinkObjId="g_20c9920_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83965_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4346,-300 4346,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3496b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4346,-289 4346,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_32b0070@0" ObjectIDND1="18544@x" ObjectIDZND0="g_20c9920@0" Pin0InfoVect0LinkObjId="g_20c9920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32b0070_0" Pin1InfoVect1LinkObjId="SW-83965_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4346,-289 4346,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3537bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4346,-237 4346,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_20c9920@1" ObjectIDZND0="g_35383d0@0" Pin0InfoVect0LinkObjId="g_35383d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20c9920_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4346,-237 4346,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ebbaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4098,-756 4098,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2091b10@0" ObjectIDZND0="18537@x" ObjectIDZND1="g_2091b10@0" ObjectIDZND2="18537@x" Pin0InfoVect0LinkObjId="SW-83938_0" Pin0InfoVect1LinkObjId="g_2091b10_0" Pin0InfoVect2LinkObjId="SW-83938_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2091b10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4098,-756 4098,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3385290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4098,-768 4134,-768 4134,-761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2091b10@0" ObjectIDND1="g_2091b10@0" ObjectIDND2="18537@x" ObjectIDZND0="18537@1" Pin0InfoVect0LinkObjId="SW-83938_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2091b10_0" Pin1InfoVect1LinkObjId="g_2091b10_0" Pin1InfoVect2LinkObjId="SW-83938_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4098,-768 4134,-768 4134,-761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ebb1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4134,-725 4134,-710 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18537@0" ObjectIDZND0="g_3469250@0" Pin0InfoVect0LinkObjId="g_3469250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83938_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4134,-725 4134,-710 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26a5e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-886 3790,-874 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18519@0" ObjectIDZND0="18541@0" Pin0InfoVect0LinkObjId="SW-83964_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33b8070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-886 3790,-874 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26a3c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-856 3790,-843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="18541@1" ObjectIDZND0="g_2eb7720@0" Pin0InfoVect0LinkObjId="g_2eb7720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83964_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-856 3790,-843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eec820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-812 3790,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2eb7720@1" ObjectIDZND0="18542@1" Pin0InfoVect0LinkObjId="SW-83964_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2eb7720_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-812 3790,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3889fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-782 3790,-734 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="18542@0" ObjectIDZND0="g_34e38b0@0" Pin0InfoVect0LinkObjId="g_34e38b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83964_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-782 3790,-734 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eb8020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-758 4050,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3850a60@1" ObjectIDZND0="g_2091b10@0" ObjectIDZND1="18537@x" ObjectIDZND2="g_2091b10@0" Pin0InfoVect0LinkObjId="g_2091b10_0" Pin0InfoVect1LinkObjId="SW-83938_0" Pin0InfoVect2LinkObjId="g_2091b10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3850a60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-758 4050,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3453030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-768 4098,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2091b10@0" ObjectIDND1="18537@x" ObjectIDND2="18536@x" ObjectIDZND0="g_2091b10@0" ObjectIDZND1="18537@x" Pin0InfoVect0LinkObjId="g_2091b10_0" Pin0InfoVect1LinkObjId="SW-83938_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2091b10_0" Pin1InfoVect1LinkObjId="SW-83938_0" Pin1InfoVect2LinkObjId="SW-83937_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-768 4098,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ebaf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-768 4050,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_2091b10@0" ObjectIDND1="18537@x" ObjectIDND2="g_3850a60@0" ObjectIDZND0="g_2091b10@0" ObjectIDZND1="18537@x" ObjectIDZND2="18536@x" Pin0InfoVect0LinkObjId="g_2091b10_0" Pin0InfoVect1LinkObjId="SW-83938_0" Pin0InfoVect2LinkObjId="SW-83937_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2091b10_0" Pin1InfoVect1LinkObjId="SW-83938_0" Pin1InfoVect2LinkObjId="g_3850a60_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-768 4050,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37ebbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-887 3929,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="18519@0" ObjectIDZND0="18533@0" Pin0InfoVect0LinkObjId="SW-83925_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33b8070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-887 3929,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32c9950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-769 4050,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2091b10@0" ObjectIDND1="18537@x" ObjectIDND2="g_2091b10@0" ObjectIDZND0="g_2091b10@0" ObjectIDZND1="18537@x" ObjectIDZND2="g_3850a60@0" Pin0InfoVect0LinkObjId="g_2091b10_0" Pin0InfoVect1LinkObjId="SW-83938_0" Pin0InfoVect2LinkObjId="g_3850a60_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2091b10_0" Pin1InfoVect1LinkObjId="SW-83938_0" Pin1InfoVect2LinkObjId="g_2091b10_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-769 4050,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_271ff20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-777 4050,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2091b10@0" ObjectIDND1="18537@x" ObjectIDND2="g_3850a60@0" ObjectIDZND0="18536@0" Pin0InfoVect0LinkObjId="SW-83937_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2091b10_0" Pin1InfoVect1LinkObjId="SW-83938_0" Pin1InfoVect2LinkObjId="g_3850a60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-777 4050,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e63b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-875 4050,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="18536@1" ObjectIDZND0="18519@0" Pin0InfoVect0LinkObjId="g_33b8070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83937_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-875 4050,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3857c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-530 4050,-517 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_359a250@0" ObjectIDZND0="18538@1" Pin0InfoVect0LinkObjId="SW-83939_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_359a250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-530 4050,-517 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e1b7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-426 4050,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="18538@0" ObjectIDZND0="18520@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83939_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-426 4050,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ad1aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3637,-274 3637,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_2f9d330@1" ObjectIDZND0="g_2f9d330@0" ObjectIDZND1="18522@x" ObjectIDZND2="g_2f9d330@0" Pin0InfoVect0LinkObjId="g_2f9d330_0" Pin0InfoVect1LinkObjId="SW-83893_0" Pin0InfoVect2LinkObjId="g_2f9d330_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f9d330_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3637,-274 3637,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e18810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3637,-285 3637,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_2f9d330@0" ObjectIDND1="g_2f9d330@0" ObjectIDND2="18522@x" ObjectIDZND0="g_2f9d330@0" ObjectIDZND1="18522@x" ObjectIDZND2="g_2f9d330@0" Pin0InfoVect0LinkObjId="g_2f9d330_0" Pin0InfoVect1LinkObjId="SW-83893_0" Pin0InfoVect2LinkObjId="g_2f9d330_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f9d330_0" Pin1InfoVect1LinkObjId="g_2f9d330_0" Pin1InfoVect2LinkObjId="SW-83893_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3637,-285 3637,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37ef580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3637,-294 3637,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="g_2f9d330@0" ObjectIDND1="18522@x" ObjectIDND2="g_2f9d330@0" ObjectIDZND0="g_2f9d330@0" ObjectIDZND1="g_2f9d330@0" ObjectIDZND2="18522@x" Pin0InfoVect0LinkObjId="g_2f9d330_0" Pin0InfoVect1LinkObjId="g_2f9d330_0" Pin0InfoVect2LinkObjId="SW-83893_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f9d330_0" Pin1InfoVect1LinkObjId="SW-83893_0" Pin1InfoVect2LinkObjId="g_2f9d330_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3637,-294 3637,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ee0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3688,-267 3688,-288 3637,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="g_25248e0@0" ObjectIDZND0="g_2f9d330@0" ObjectIDZND1="g_2f9d330@0" ObjectIDZND2="18522@x" Pin0InfoVect0LinkObjId="g_2f9d330_0" Pin0InfoVect1LinkObjId="g_2f9d330_0" Pin0InfoVect2LinkObjId="SW-83893_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25248e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3688,-267 3688,-288 3637,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26a1a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3637,-288 3605,-288 3605,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_2f9d330@0" ObjectIDND1="g_2f9d330@0" ObjectIDND2="18522@x" ObjectIDZND0="18524@1" Pin0InfoVect0LinkObjId="SW-83895_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f9d330_0" Pin1InfoVect1LinkObjId="g_2f9d330_0" Pin1InfoVect2LinkObjId="SW-83893_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3637,-288 3605,-288 3605,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f27280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3887,-408 3887,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="18520@0" ObjectIDZND0="18525@1" Pin0InfoVect0LinkObjId="SW-83903_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e1b7c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3887,-408 3887,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37ec4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3887,-302 3887,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="18525@0" ObjectIDZND0="g_2fd8880@1" Pin0InfoVect0LinkObjId="g_2fd8880_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83903_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3887,-302 3887,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32ad740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3637,-408 3637,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="18520@0" ObjectIDZND0="18522@1" Pin0InfoVect0LinkObjId="SW-83893_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e1b7c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3637,-408 3637,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ec5c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3637,-305 3637,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="18522@0" ObjectIDZND0="g_2f9d330@0" ObjectIDZND1="g_2f9d330@0" ObjectIDZND2="18522@x" Pin0InfoVect0LinkObjId="g_2f9d330_0" Pin0InfoVect1LinkObjId="g_2f9d330_0" Pin0InfoVect2LinkObjId="SW-83893_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83893_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3637,-305 3637,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35915c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-408 4104,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="18520@0" ObjectIDZND0="18528@1" Pin0InfoVect0LinkObjId="SW-83913_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e1b7c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-408 4104,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f27dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-279 4104,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_3236fe0@1" ObjectIDZND0="18528@0" Pin0InfoVect0LinkObjId="SW-83913_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3236fe0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-279 4104,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fde800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-272 4155,-293 4072,-293 4072,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_25882a0@0" ObjectIDZND0="18532@1" Pin0InfoVect0LinkObjId="SW-83917_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25882a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-272 4155,-293 4072,-293 4072,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2718dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4072,-245 4072,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18532@0" ObjectIDZND0="g_2fd9fe0@0" Pin0InfoVect0LinkObjId="g_2fd9fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83917_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4072,-245 4072,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1acfd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-191 4104,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="18547@0" ObjectIDZND0="g_3236fe0@0" Pin0InfoVect0LinkObjId="g_3236fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-CX_TF.CX_TF_1C_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-191 4104,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ad4c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4241,-1019 4241,-988 4203,-988 4203,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_35f9fd0@0" ObjectIDZND0="g_34ca480@1" Pin0InfoVect0LinkObjId="g_34ca480_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35f9fd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4241,-1019 4241,-988 4203,-988 4203,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3202390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4203,-969 4203,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="18540@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83963_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4203,-969 4203,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3237b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3833,-1092 3833,-1065 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3857a30@0" Pin0InfoVect0LinkObjId="g_3857a30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3833,-1092 3833,-1065 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b12d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-991 3966,-997 3928,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_34a28d0@0" ObjectIDZND0="18533@x" ObjectIDZND1="g_2556ec0@0" Pin0InfoVect0LinkObjId="SW-83925_0" Pin0InfoVect1LinkObjId="g_2556ec0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34a28d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-991 3966,-997 3928,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32aedb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-990 3929,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="18533@1" ObjectIDZND0="g_34a28d0@0" ObjectIDZND1="g_2556ec0@0" Pin0InfoVect0LinkObjId="g_34a28d0_0" Pin0InfoVect1LinkObjId="g_2556ec0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83925_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-990 3929,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33ad060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-997 3929,-1006 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_34a28d0@0" ObjectIDND1="18533@x" ObjectIDZND0="g_2556ec0@0" Pin0InfoVect0LinkObjId="g_2556ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_34a28d0_0" Pin1InfoVect1LinkObjId="SW-83925_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-997 3929,-1006 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="18520" cx="4346" cy="-408" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18519" cx="4203" cy="-886" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18519" cx="3790" cy="-886" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18519" cx="3929" cy="-886" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18519" cx="4050" cy="-886" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18520" cx="4050" cy="-408" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18520" cx="3887" cy="-408" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18520" cx="4104" cy="-408" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-83551" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3435.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18413" ObjectName="DYN-CX_TF"/>
     <cge:Meas_Ref ObjectId="83551"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f57b00" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3514.000000 -400.000000) translate(0,15)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3537e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4228.000000 -951.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f67980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3962.000000 -667.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fcd280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3889.500000 -958.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b9960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4061.000000 -485.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e63260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4067.000000 -831.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b9000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4358.000000 -355.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37e8cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3641.000000 -871.000000) translate(0,12)">35kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3538680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3538680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3538680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3538680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3538680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3538680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3538680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3538680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3538680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3538680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3538680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3538680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3538680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3538680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3538680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3538680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3538680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3538680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2839650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2839650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2839650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2839650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2839650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2839650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2839650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_33b3750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3278.000000 -1167.500000) translate(0,16)">田房变电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be5480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4092.000000 -686.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be5480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4092.000000 -686.000000) translate(0,27)">SZ11-6300/35/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be5480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4092.000000 -686.000000) translate(0,42)">35±3×2.5%/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be5480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4092.000000 -686.000000) translate(0,57)">U%=7.5  Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35ad0c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4308.000000 -168.000000) translate(0,15)">10kV线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_356d270" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4053.000000 -67.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1acc080" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3556.000000 -39.000000) translate(0,15)">10kV永广铁路Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ad0420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4181.000000 -1099.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25261a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4146.000000 -745.000000) translate(0,12)">30167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3762c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3743.000000 -632.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_309d200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3985.500000 -1116.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16cb510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3884.500000 -1095.000000) translate(0,12)">3716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e74500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3809.500000 -1024.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b5ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.500000 -1187.000000) translate(0,12)">龙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b5ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.500000 -1187.000000) translate(0,27)">他</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b5ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.500000 -1187.000000) translate(0,42)">猛</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b5ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.500000 -1187.000000) translate(0,57)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b5ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.500000 -1187.000000) translate(0,72)">T</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b5ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.500000 -1187.000000) translate(0,87)">田</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b5ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.500000 -1187.000000) translate(0,102)">房</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b5ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.500000 -1187.000000) translate(0,117)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b5ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.500000 -1187.000000) translate(0,132)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b5ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.500000 -1187.000000) translate(0,147)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b5ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.500000 -1187.000000) translate(0,162)">支</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b5ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.500000 -1187.000000) translate(0,177)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32a1650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3653.500000 -348.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339f6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3813.500000 -835.000000) translate(0,12)">3801</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251c1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3552.500000 -274.000000) translate(0,12)">07127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2055af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3649.500000 -193.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be8ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3902.500000 -197.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251ea20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3905.500000 -350.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25322d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3802.500000 -264.000000) translate(0,12)">07227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14c6e80" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3802.000000 -39.000000) translate(0,15)">10kV永广铁路Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3239620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.500000 -355.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ac2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4019.500000 -266.000000) translate(0,12)">07327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_20bb4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3249.000000 -217.000000) translate(0,13)">6726761</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-83925">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3919.000000 -891.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18533" ObjectName="SW-CX_TF.CX_TF_371BK"/>
     <cge:Meas_Ref ObjectId="83925"/>
    <cge:TPSR_Ref TObjectID="18533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83937">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4040.000000 -776.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18536" ObjectName="SW-CX_TF.CX_TF_301BK"/>
     <cge:Meas_Ref ObjectId="83937"/>
    <cge:TPSR_Ref TObjectID="18536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83939">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4040.000000 -418.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18538" ObjectName="SW-CX_TF.CX_TF_001BK"/>
     <cge:Meas_Ref ObjectId="83939"/>
    <cge:TPSR_Ref TObjectID="18538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83893">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3627.000000 -297.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18522" ObjectName="SW-CX_TF.CX_TF_071BK"/>
     <cge:Meas_Ref ObjectId="83893"/>
    <cge:TPSR_Ref TObjectID="18522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83903">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3877.000000 -294.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18525" ObjectName="SW-CX_TF.CX_TF_072BK"/>
     <cge:Meas_Ref ObjectId="83903"/>
    <cge:TPSR_Ref TObjectID="18525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-83913">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4094.000000 -291.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18528" ObjectName="SW-CX_TF.CX_TF_073BK"/>
     <cge:Meas_Ref ObjectId="83913"/>
    <cge:TPSR_Ref TObjectID="18528"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_TF.CX_TF_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="25871"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4013.000000 -599.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4013.000000 -599.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="18546" ObjectName="TF-CX_TF.CX_TF_1T"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3234.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-83885" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3256.538462 -1017.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83885" ObjectName="CX_TF:CX_TF_001BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-83888" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4189.000000 -592.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83888" ObjectName="CX_TF:CX_TF_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-83890" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4188.000000 -609.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83890" ObjectName="CX_TF:CX_TF_1T_Tp"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="42" qtmmishow="hidden" width="164" x="3246" y="-1176"/>
    </a>
   <metadata/><rect fill="white" height="42" opacity="0" stroke="white" transform="" width="164" x="3246" y="-1176"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3197" y="-1194"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a9dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4134.000000 502.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25395f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.000000 487.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2526880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4148.000000 472.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37f1610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 981.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35aa760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4001.000000 966.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26fa090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4026.000000 951.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e02a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3612.000000 943.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ad420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3612.000000 958.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e74740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3604.000000 915.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e03680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3612.000000 929.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b3940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3543.000000 477.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ad2a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3543.000000 492.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35135d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3535.000000 449.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e74080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3543.000000 463.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3386210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4068.000000 33.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ad640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4057.000000 18.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ad40c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4082.000000 3.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35d60b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3841.000000 5.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebbce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3830.000000 -10.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3859770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3855.000000 -25.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e620f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3589.000000 5.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ef2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3578.000000 -10.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3762ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3603.000000 -25.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3599c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4124.000000 849.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_315d230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4113.000000 834.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37e7f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4138.000000 819.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3151730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4093.000000 610.000000) translate(0,12)">档位（档）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20cd250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4094.000000 591.000000) translate(0,12)">油温（℃）：</text>
   <metadata/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_35f9fd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4234.000000 -1014.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2556ec0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3924.000000 -1001.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34a28d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3959.000000 -933.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34ca480">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4194.000000 -997.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fd1990">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4184.000000 -1041.000000)" xlink:href="#lightningRod:shape163"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3850a60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4046.000000 -700.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_359a250">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4045.000000 -525.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f9d330">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3632.000000 -216.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25248e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3681.000000 -209.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_264bd10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3665.000000 -75.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2521080">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3627.000000 -43.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fd8880">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3882.000000 -219.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ad35c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3931.000000 -212.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fbe0c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3915.000000 -78.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2613440">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3877.000000 -46.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32b0070">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4402.000000 -225.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20c9920">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4337.000000 -232.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35383d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4322.000000 -171.000000)" xlink:href="#lightningRod:shape190"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2091b10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4091.000000 -698.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eb7720">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3781.000000 -807.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34e38b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3759.000000 -640.000000)" xlink:href="#lightningRod:shape191"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3857a30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3821.000000 -1029.000000)" xlink:href="#lightningRod:shape199"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3236fe0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4099.000000 -221.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25882a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4148.000000 -214.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="yr_索引_接线图.svg" style="fill-opacity:0"><rect height="42" qtmmishow="hidden" width="164" x="3246" y="-1176"/></g>
   <g href="yr_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_TF.CX_TF_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3537,-408 4476,-408 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18520" ObjectName="BS-CX_TF.CX_TF_9IM"/>
    <cge:TPSR_Ref TObjectID="18520"/></metadata>
   <polyline fill="none" opacity="0" points="3537,-408 4476,-408 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TF.CX_TF_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3638,-886 4285,-886 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18519" ObjectName="BS-CX_TF.CX_TF_3IM"/>
    <cge:TPSR_Ref TObjectID="18519"/></metadata>
   <polyline fill="none" opacity="0" points="3638,-886 4285,-886 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-83862" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3702.000000 -955.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83862" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18519"/>
     <cge:Term_Ref ObjectID="25817"/>
    <cge:TPSR_Ref TObjectID="18519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-83863" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3702.000000 -955.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83863" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18519"/>
     <cge:Term_Ref ObjectID="25817"/>
    <cge:TPSR_Ref TObjectID="18519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-83864" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3702.000000 -955.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83864" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18519"/>
     <cge:Term_Ref ObjectID="25817"/>
    <cge:TPSR_Ref TObjectID="18519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-83865" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3702.000000 -955.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83865" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18519"/>
     <cge:Term_Ref ObjectID="25817"/>
    <cge:TPSR_Ref TObjectID="18519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-83855" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4073.000000 -980.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83855" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18533"/>
     <cge:Term_Ref ObjectID="25843"/>
    <cge:TPSR_Ref TObjectID="18533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-83856" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4073.000000 -980.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83856" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18533"/>
     <cge:Term_Ref ObjectID="25843"/>
    <cge:TPSR_Ref TObjectID="18533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-83852" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4073.000000 -980.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83852" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18533"/>
     <cge:Term_Ref ObjectID="25843"/>
    <cge:TPSR_Ref TObjectID="18533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-83879" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4184.000000 -850.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83879" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18536"/>
     <cge:Term_Ref ObjectID="25849"/>
    <cge:TPSR_Ref TObjectID="18536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-83880" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4184.000000 -850.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83880" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18536"/>
     <cge:Term_Ref ObjectID="25849"/>
    <cge:TPSR_Ref TObjectID="18536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-83876" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4184.000000 -850.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83876" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18536"/>
     <cge:Term_Ref ObjectID="25849"/>
    <cge:TPSR_Ref TObjectID="18536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-83869" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3628.000000 -490.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83869" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18520"/>
     <cge:Term_Ref ObjectID="25818"/>
    <cge:TPSR_Ref TObjectID="18520"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-83870" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3628.000000 -490.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83870" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18520"/>
     <cge:Term_Ref ObjectID="25818"/>
    <cge:TPSR_Ref TObjectID="18520"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-83871" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3628.000000 -490.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83871" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18520"/>
     <cge:Term_Ref ObjectID="25818"/>
    <cge:TPSR_Ref TObjectID="18520"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-83872" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3628.000000 -490.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83872" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18520"/>
     <cge:Term_Ref ObjectID="25818"/>
    <cge:TPSR_Ref TObjectID="18520"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-83885" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4195.000000 -504.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83885" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18538"/>
     <cge:Term_Ref ObjectID="25853"/>
    <cge:TPSR_Ref TObjectID="18538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-83886" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4195.000000 -504.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83886" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18538"/>
     <cge:Term_Ref ObjectID="25853"/>
    <cge:TPSR_Ref TObjectID="18538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-83882" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4195.000000 -504.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83882" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18538"/>
     <cge:Term_Ref ObjectID="25853"/>
    <cge:TPSR_Ref TObjectID="18538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-83837" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3646.000000 -5.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83837" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18522"/>
     <cge:Term_Ref ObjectID="25821"/>
    <cge:TPSR_Ref TObjectID="18522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-83838" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3646.000000 -5.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83838" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18522"/>
     <cge:Term_Ref ObjectID="25821"/>
    <cge:TPSR_Ref TObjectID="18522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-83834" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3646.000000 -5.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83834" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18522"/>
     <cge:Term_Ref ObjectID="25821"/>
    <cge:TPSR_Ref TObjectID="18522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-83843" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3899.000000 -5.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83843" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18525"/>
     <cge:Term_Ref ObjectID="25827"/>
    <cge:TPSR_Ref TObjectID="18525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-83844" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3899.000000 -5.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83844" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18525"/>
     <cge:Term_Ref ObjectID="25827"/>
    <cge:TPSR_Ref TObjectID="18525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-83840" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3899.000000 -5.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83840" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18525"/>
     <cge:Term_Ref ObjectID="25827"/>
    <cge:TPSR_Ref TObjectID="18525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-83849" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4125.000000 -33.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83849" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18528"/>
     <cge:Term_Ref ObjectID="25833"/>
    <cge:TPSR_Ref TObjectID="18528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-83850" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4125.000000 -33.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83850" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18528"/>
     <cge:Term_Ref ObjectID="25833"/>
    <cge:TPSR_Ref TObjectID="18528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-83846" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4125.000000 -33.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83846" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18528"/>
     <cge:Term_Ref ObjectID="25833"/>
    <cge:TPSR_Ref TObjectID="18528"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_TF"/>
</svg>