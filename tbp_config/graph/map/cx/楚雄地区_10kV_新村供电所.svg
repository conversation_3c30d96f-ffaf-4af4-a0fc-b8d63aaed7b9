<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" aopId="0" id="thSvg" viewBox="-2 -2972 4241 2974">
 
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    
   </symbol>
   <symbol id="Tag:shape27">
    
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="0.5" width="111" x="0" y="0"/>
    <line stroke="rgb(50,205,50)" stroke-width="1.5" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)"/>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(127,127,127);fill:none}
.BKBV-0KV { stroke:rgb(127,127,127);fill:rgb(127,127,127)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(0,72,216);fill:none}
.BKBV-10KV { stroke:rgb(0,72,216);fill:rgb(0,72,216)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(0,255,0);fill:none}
.BKBV-20KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(213,0,0);fill:none}
.BKBV-110KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-220KV { stroke:rgb(255,0,255);fill:none}
.BKBV-220KV { stroke:rgb(255,0,255);fill:rgb(255,0,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(255,0,0);fill:none}
.BKBV-500KV { stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.BV-750KV { stroke:rgb(255,0,0);fill:none}
.BKBV-750KV { stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.BV-22KV { stroke:rgb(255,255,255);fill:none}
.BKBV-22KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-380KV { stroke:rgb(255,255,255);fill:none}
.BKBV-380KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="2984" width="4251" x="-7" y="-2977"/>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="0" fill="none" points="1377,-2266 1377,-2263 1377,-2260 1377,-2258 1378,-2255 1379,-2252 1380,-2250 1382,-2248 1384,-2246 1386,-2244 1389,-2243 1391,-2242 1394,-2241 1397,-2241 1399,-2241 1402,-2241 1405,-2242 1407,-2243 1410,-2244 1412,-2246 1414,-2248 1416,-2250 1417,-2252 1418,-2255 1419,-2258 1419,-2260 1419,-2263 1419,-2266 " stroke="rgb(255,255,255)" stroke-width="0.07375"/>
   <polyline DF8003:Layer="0" fill="none" points="2623,-1929 2623,-1926 2622,-1923 2623,-1921 2623,-1918 2624,-1916 2626,-1913 2627,-1911 2629,-1909 2631,-1907 2634,-1905 2636,-1904 2639,-1903 2641,-1903 2644,-1903 2647,-1903 2649,-1903 2652,-1904 2655,-1905 2657,-1907 2659,-1909 2661,-1911 2662,-1913 2664,-1916 2665,-1918 2665,-1921 2666,-1923 2665,-1926 2665,-1929 " stroke="rgb(255,255,255)" stroke-width="0.075"/>
   <polyline DF8003:Layer="0" fill="none" points="3032,-1927 3032,-1924 3033,-1922 3033,-1919 3034,-1916 3036,-1914 3038,-1912 3040,-1910 3042,-1908 3044,-1907 3047,-1906 3049,-1905 3052,-1905 3055,-1905 3058,-1905 3060,-1906 3063,-1907 3065,-1908 3067,-1910 3069,-1912 3071,-1914 3073,-1916 3074,-1919 3075,-1922 3075,-1924 3075,-1927 " stroke="rgb(255,255,255)" stroke-width="0.07"/>
   <polyline DF8003:Layer="0" fill="none" points="3385,-1929 3385,-1926 3384,-1923 3385,-1921 3385,-1918 3386,-1916 3388,-1913 3389,-1911 3391,-1909 3393,-1907 3396,-1905 3398,-1904 3401,-1903 3403,-1903 3406,-1903 3409,-1903 3411,-1903 3414,-1904 3417,-1905 3419,-1907 3421,-1909 3423,-1911 3424,-1913 3426,-1916 3427,-1918 3427,-1921 3428,-1923 3427,-1926 3427,-1929 " stroke="rgb(255,255,255)" stroke-width="0.075"/>
   <polyline DF8003:Layer="0" fill="none" points="3455,-26 3455,-25 3455,-24 3455,-23 3456,-22 3457,-21 3457,-20 3458,-19 3459,-18 3460,-18 3461,-17 3462,-17 3463,-17 3464,-17 3465,-17 3466,-17 3467,-17 3468,-18 3469,-18 3470,-19 3471,-20 3472,-21 3472,-22 3473,-23 3473,-24 3473,-25 3473,-26 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3473,-26 3473,-25 3473,-24 3473,-23 3474,-22 3475,-21 3475,-20 3476,-19 3477,-18 3478,-18 3479,-17 3480,-17 3481,-17 3482,-17 3483,-17 3484,-17 3485,-17 3486,-18 3487,-18 3488,-19 3489,-20 3490,-21 3490,-22 3491,-23 3491,-24 3491,-25 3491,-26 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3062,-1695 3062,-1694 3062,-1693 3062,-1692 3063,-1691 3064,-1690 3064,-1689 3065,-1688 3066,-1687 3067,-1687 3068,-1686 3069,-1686 3070,-1686 3071,-1686 3072,-1686 3073,-1686 3074,-1686 3075,-1687 3076,-1687 3077,-1688 3078,-1689 3079,-1690 3079,-1691 3080,-1692 3080,-1693 3080,-1694 3080,-1695 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3080,-1695 3080,-1694 3080,-1693 3080,-1692 3081,-1691 3082,-1690 3082,-1689 3083,-1688 3084,-1687 3085,-1687 3086,-1686 3087,-1686 3088,-1686 3089,-1686 3090,-1686 3091,-1686 3092,-1686 3093,-1687 3094,-1687 3095,-1688 3096,-1689 3097,-1690 3097,-1691 3098,-1692 3098,-1693 3098,-1694 3098,-1695 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2978,-1749 2978,-1748 2978,-1747 2979,-1746 2979,-1745 2980,-1744 2981,-1743 2982,-1742 2983,-1742 2984,-1741 2985,-1741 2986,-1741 2987,-1741 2988,-1741 2989,-1741 2990,-1741 2991,-1742 2992,-1742 2993,-1743 2994,-1744 2995,-1745 2995,-1746 2996,-1747 2996,-1748 2996,-1749 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2996,-1749 2996,-1748 2996,-1747 2997,-1746 2997,-1745 2998,-1744 2999,-1743 3000,-1742 3001,-1742 3002,-1741 3003,-1741 3004,-1741 3005,-1741 3006,-1741 3007,-1741 3008,-1741 3009,-1742 3010,-1742 3011,-1743 3012,-1744 3013,-1745 3013,-1746 3014,-1747 3014,-1748 3014,-1749 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3061,-1620 3061,-1619 3061,-1618 3061,-1616 3061,-1615 3062,-1614 3062,-1613 3063,-1612 3064,-1611 3065,-1611 3066,-1610 3067,-1610 3068,-1609 3069,-1609 3071,-1609 3072,-1609 3073,-1610 3074,-1610 3075,-1611 3076,-1611 3077,-1612 3078,-1613 3078,-1614 3079,-1615 3079,-1616 3079,-1618 3079,-1619 3079,-1620 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3079,-1620 3079,-1619 3079,-1618 3079,-1616 3079,-1615 3080,-1614 3080,-1613 3081,-1612 3082,-1611 3083,-1611 3084,-1610 3085,-1610 3086,-1609 3087,-1609 3089,-1609 3090,-1609 3091,-1610 3092,-1610 3093,-1611 3094,-1611 3095,-1612 3096,-1613 3096,-1614 3097,-1615 3097,-1616 3097,-1618 3097,-1619 3097,-1620 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2976,-1518 2976,-1517 2976,-1516 2977,-1515 2977,-1514 2978,-1513 2979,-1512 2980,-1511 2981,-1511 2982,-1510 2983,-1510 2984,-1510 2985,-1510 2986,-1510 2987,-1510 2988,-1510 2989,-1511 2990,-1511 2991,-1512 2992,-1513 2993,-1514 2993,-1515 2994,-1516 2994,-1517 2994,-1518 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2994,-1518 2994,-1517 2994,-1516 2995,-1515 2995,-1514 2996,-1513 2997,-1512 2998,-1511 2999,-1511 3000,-1510 3001,-1510 3002,-1510 3003,-1510 3004,-1510 3005,-1510 3006,-1510 3007,-1511 3008,-1511 3009,-1512 3010,-1513 3011,-1514 3011,-1515 3012,-1516 3012,-1517 3012,-1518 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2977,-293 2977,-292 2977,-291 2977,-290 2978,-288 2978,-287 2979,-287 2980,-286 2981,-285 2982,-284 2983,-284 2984,-283 2985,-283 2986,-283 2987,-283 2988,-283 2989,-284 2990,-284 2991,-285 2992,-286 2993,-287 2994,-287 2994,-288 2995,-290 2995,-291 2995,-292 2995,-293 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2995,-293 2995,-292 2995,-291 2995,-290 2996,-288 2996,-287 2997,-287 2998,-286 2999,-285 3000,-284 3001,-284 3002,-283 3003,-283 3004,-283 3005,-283 3006,-283 3007,-284 3008,-284 3009,-285 3010,-286 3011,-287 3012,-287 3012,-288 3013,-290 3013,-291 3013,-292 3013,-293 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2977,-627 2977,-626 2977,-625 2977,-624 2978,-622 2978,-621 2979,-621 2980,-620 2981,-619 2982,-618 2983,-618 2984,-617 2985,-617 2986,-617 2987,-617 2988,-617 2989,-618 2990,-618 2991,-619 2992,-620 2993,-621 2994,-621 2994,-622 2995,-624 2995,-625 2995,-626 2995,-627 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2996,-627 2996,-626 2996,-625 2997,-624 2997,-623 2998,-622 2999,-621 3000,-620 3001,-620 3002,-619 3003,-619 3004,-619 3005,-619 3006,-619 3007,-619 3008,-619 3009,-620 3010,-620 3011,-621 3012,-622 3013,-623 3013,-624 3014,-625 3014,-626 3014,-627 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2978,-1212 2978,-1211 2978,-1210 2978,-1209 2979,-1208 2980,-1207 2980,-1206 2981,-1205 2982,-1204 2983,-1204 2984,-1203 2985,-1203 2986,-1203 2987,-1203 2988,-1203 2989,-1203 2990,-1203 2991,-1204 2992,-1204 2993,-1205 2994,-1206 2995,-1207 2995,-1208 2996,-1209 2996,-1210 2996,-1211 2996,-1212 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2996,-1212 2996,-1211 2996,-1210 2996,-1209 2997,-1208 2998,-1207 2998,-1206 2999,-1205 3000,-1204 3001,-1204 3002,-1203 3003,-1203 3004,-1203 3005,-1203 3006,-1203 3007,-1203 3008,-1203 3009,-1204 3010,-1204 3011,-1205 3012,-1206 3013,-1207 3013,-1208 3014,-1209 3014,-1210 3014,-1211 3014,-1212 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2978,-887 2978,-886 2978,-885 2979,-884 2979,-883 2980,-882 2981,-881 2982,-880 2983,-880 2984,-879 2985,-879 2986,-879 2987,-879 2988,-879 2989,-879 2990,-879 2991,-880 2992,-880 2993,-881 2994,-882 2995,-883 2995,-884 2996,-885 2996,-886 2996,-887 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2996,-887 2996,-886 2996,-885 2997,-884 2997,-883 2998,-882 2999,-881 3000,-880 3001,-880 3002,-879 3003,-879 3004,-879 3005,-879 3006,-879 3007,-879 3008,-879 3009,-880 3010,-880 3011,-881 3012,-882 3013,-883 3013,-884 3014,-885 3014,-886 3014,-887 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3089,-1298 3089,-1297 3089,-1296 3089,-1295 3090,-1294 3091,-1293 3091,-1292 3092,-1291 3093,-1290 3094,-1290 3095,-1289 3096,-1289 3097,-1289 3098,-1289 3099,-1289 3100,-1289 3101,-1289 3102,-1290 3103,-1290 3104,-1291 3105,-1292 3106,-1293 3106,-1294 3107,-1295 3107,-1296 3107,-1297 3107,-1298 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3107,-1298 3107,-1297 3107,-1296 3107,-1295 3108,-1294 3109,-1293 3109,-1292 3110,-1291 3111,-1290 3112,-1290 3113,-1289 3114,-1289 3115,-1289 3116,-1289 3117,-1289 3118,-1289 3119,-1289 3120,-1290 3121,-1290 3122,-1291 3123,-1292 3124,-1293 3124,-1294 3125,-1295 3125,-1296 3125,-1297 3125,-1298 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3089,-783 3089,-782 3089,-781 3090,-780 3090,-779 3091,-778 3092,-777 3093,-776 3094,-776 3095,-775 3096,-775 3097,-775 3098,-775 3099,-775 3100,-775 3101,-775 3102,-776 3103,-776 3104,-777 3105,-778 3106,-779 3106,-780 3107,-781 3107,-782 3107,-783 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3107,-783 3107,-782 3107,-781 3108,-780 3108,-779 3109,-778 3110,-777 3111,-776 3112,-776 3113,-775 3114,-775 3115,-775 3116,-775 3117,-775 3118,-775 3119,-775 3120,-776 3121,-776 3122,-777 3123,-778 3124,-779 3124,-780 3125,-781 3125,-782 3125,-783 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2798,-1189 2799,-1189 2800,-1189 2801,-1189 2803,-1190 2804,-1190 2804,-1191 2805,-1192 2806,-1193 2807,-1194 2807,-1195 2808,-1196 2808,-1197 2808,-1198 2808,-1199 2808,-1200 2807,-1201 2807,-1202 2806,-1203 2805,-1204 2804,-1205 2804,-1206 2803,-1206 2801,-1207 2800,-1207 2799,-1207 2798,-1207 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2798,-1171 2799,-1171 2800,-1171 2801,-1171 2803,-1172 2804,-1172 2804,-1173 2805,-1174 2806,-1175 2807,-1176 2807,-1177 2808,-1178 2808,-1179 2808,-1180 2808,-1181 2808,-1182 2807,-1183 2807,-1184 2806,-1185 2805,-1186 2804,-1187 2804,-1188 2803,-1188 2801,-1189 2800,-1189 2799,-1189 2798,-1189 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1714,-994 1714,-993 1714,-992 1714,-991 1715,-990 1716,-989 1716,-988 1717,-987 1718,-986 1719,-986 1720,-985 1721,-985 1722,-985 1723,-985 1724,-985 1725,-985 1726,-985 1727,-986 1728,-986 1729,-987 1730,-988 1731,-989 1731,-990 1732,-991 1732,-992 1732,-993 1732,-994 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1732,-994 1732,-993 1732,-992 1732,-991 1733,-990 1734,-989 1734,-988 1735,-987 1736,-986 1737,-986 1738,-985 1739,-985 1740,-985 1741,-985 1742,-985 1743,-985 1744,-985 1745,-986 1746,-986 1747,-987 1748,-988 1749,-989 1749,-990 1750,-991 1750,-992 1750,-993 1750,-994 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1142,-1712 1142,-1711 1142,-1710 1142,-1709 1143,-1707 1143,-1706 1144,-1706 1145,-1705 1146,-1704 1147,-1703 1148,-1703 1149,-1702 1150,-1702 1151,-1702 1152,-1702 1153,-1702 1154,-1703 1155,-1703 1156,-1704 1157,-1705 1158,-1706 1159,-1706 1159,-1707 1160,-1709 1160,-1710 1160,-1711 1160,-1712 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1161,-1712 1161,-1711 1161,-1710 1162,-1709 1162,-1708 1163,-1707 1164,-1706 1165,-1705 1166,-1705 1167,-1704 1168,-1704 1169,-1704 1170,-1704 1171,-1704 1172,-1704 1173,-1704 1174,-1705 1175,-1705 1176,-1706 1177,-1707 1178,-1708 1178,-1709 1179,-1710 1179,-1711 1179,-1712 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1419,-358 1419,-357 1419,-356 1420,-355 1420,-354 1421,-353 1422,-352 1423,-351 1424,-351 1425,-350 1426,-350 1427,-350 1428,-350 1429,-350 1430,-350 1431,-350 1432,-351 1433,-351 1434,-352 1435,-353 1436,-354 1436,-355 1437,-356 1437,-357 1437,-358 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1437,-358 1437,-357 1437,-356 1438,-355 1438,-354 1439,-353 1440,-352 1441,-351 1442,-351 1443,-350 1444,-350 1445,-350 1446,-350 1447,-350 1448,-350 1449,-350 1450,-351 1451,-351 1452,-352 1453,-353 1454,-354 1454,-355 1455,-356 1455,-357 1455,-358 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1067,-676 1067,-675 1067,-674 1067,-673 1068,-671 1068,-670 1069,-670 1070,-669 1071,-668 1072,-667 1073,-667 1074,-666 1075,-666 1076,-666 1077,-666 1078,-666 1079,-667 1080,-667 1081,-668 1082,-669 1083,-670 1084,-670 1084,-671 1085,-673 1085,-674 1085,-675 1085,-676 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1085,-676 1085,-675 1085,-674 1085,-673 1086,-671 1086,-670 1087,-670 1088,-669 1089,-668 1090,-667 1091,-667 1092,-666 1093,-666 1094,-666 1095,-666 1096,-666 1097,-667 1098,-667 1099,-668 1100,-669 1101,-670 1102,-670 1102,-671 1103,-673 1103,-674 1103,-675 1103,-676 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="528,-1828 528,-1827 528,-1826 528,-1825 529,-1824 530,-1823 530,-1822 531,-1821 532,-1820 533,-1820 534,-1819 535,-1819 536,-1819 537,-1819 538,-1819 539,-1819 540,-1819 541,-1820 542,-1820 543,-1821 544,-1822 545,-1823 545,-1824 546,-1825 546,-1826 546,-1827 546,-1828 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="546,-1828 546,-1827 546,-1826 546,-1825 547,-1824 548,-1823 548,-1822 549,-1821 550,-1820 551,-1820 552,-1819 553,-1819 554,-1819 555,-1819 556,-1819 557,-1819 558,-1819 559,-1820 560,-1820 561,-1821 562,-1822 563,-1823 563,-1824 564,-1825 564,-1826 564,-1827 564,-1828 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="539,-485 539,-484 539,-483 540,-482 540,-481 541,-480 542,-479 543,-478 544,-478 545,-477 546,-477 547,-477 548,-477 549,-477 550,-477 551,-477 552,-478 553,-478 554,-479 555,-480 556,-481 556,-482 557,-483 557,-484 557,-485 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="557,-485 557,-484 557,-483 558,-482 558,-481 559,-480 560,-479 561,-478 562,-478 563,-477 564,-477 565,-477 566,-477 567,-477 568,-477 569,-477 570,-478 571,-478 572,-479 573,-480 574,-481 574,-482 575,-483 575,-484 575,-485 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="302,-1477 302,-1476 302,-1475 302,-1474 303,-1473 304,-1472 304,-1471 305,-1470 306,-1469 307,-1469 308,-1468 309,-1468 310,-1468 311,-1468 312,-1468 313,-1468 314,-1468 315,-1469 316,-1469 317,-1470 318,-1471 319,-1472 319,-1473 320,-1474 320,-1475 320,-1476 320,-1477 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="320,-1477 320,-1476 320,-1475 320,-1474 321,-1473 322,-1472 322,-1471 323,-1470 324,-1469 325,-1469 326,-1468 327,-1468 328,-1468 329,-1468 330,-1468 331,-1468 332,-1468 333,-1469 334,-1469 335,-1470 336,-1471 337,-1472 337,-1473 338,-1474 338,-1475 338,-1476 338,-1477 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="192,-1477 192,-1476 192,-1475 192,-1474 193,-1473 194,-1472 194,-1471 195,-1470 196,-1469 197,-1469 198,-1468 199,-1468 200,-1468 201,-1468 202,-1468 203,-1468 204,-1468 205,-1469 206,-1469 207,-1470 208,-1471 209,-1472 209,-1473 210,-1474 210,-1475 210,-1476 210,-1477 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="210,-1477 210,-1476 210,-1475 210,-1474 211,-1473 212,-1472 212,-1471 213,-1470 214,-1469 215,-1469 216,-1468 217,-1468 218,-1468 219,-1468 220,-1468 221,-1468 222,-1468 223,-1469 224,-1469 225,-1470 226,-1471 227,-1472 227,-1473 228,-1474 228,-1475 228,-1476 228,-1477 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1344,-1724 1344,-1723 1344,-1722 1345,-1721 1345,-1720 1346,-1719 1347,-1718 1348,-1717 1349,-1717 1350,-1716 1351,-1716 1352,-1716 1353,-1716 1354,-1716 1355,-1716 1356,-1716 1357,-1717 1358,-1717 1359,-1718 1360,-1719 1361,-1720 1361,-1721 1362,-1722 1362,-1723 1362,-1724 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1362,-1724 1362,-1723 1362,-1722 1363,-1721 1363,-1720 1364,-1719 1365,-1718 1366,-1717 1367,-1717 1368,-1716 1369,-1716 1370,-1716 1371,-1716 1372,-1716 1373,-1716 1374,-1716 1375,-1717 1376,-1717 1377,-1718 1378,-1719 1379,-1720 1379,-1721 1380,-1722 1380,-1723 1380,-1724 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1144,-1611 1144,-1610 1144,-1609 1145,-1608 1145,-1607 1146,-1606 1147,-1605 1148,-1604 1149,-1604 1150,-1603 1151,-1603 1152,-1603 1153,-1603 1154,-1603 1155,-1603 1156,-1603 1157,-1604 1158,-1604 1159,-1605 1160,-1606 1161,-1607 1161,-1608 1162,-1609 1162,-1610 1162,-1611 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1162,-1611 1162,-1610 1162,-1609 1163,-1608 1163,-1607 1164,-1606 1165,-1605 1166,-1604 1167,-1604 1168,-1603 1169,-1603 1170,-1603 1171,-1603 1172,-1603 1173,-1603 1174,-1603 1175,-1604 1176,-1604 1177,-1605 1178,-1606 1179,-1607 1179,-1608 1180,-1609 1180,-1610 1180,-1611 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1409,-1457 1409,-1456 1409,-1455 1410,-1454 1410,-1453 1411,-1452 1412,-1451 1413,-1450 1414,-1450 1415,-1449 1416,-1449 1417,-1449 1418,-1449 1419,-1449 1420,-1449 1421,-1449 1422,-1450 1423,-1450 1424,-1451 1425,-1452 1426,-1453 1426,-1454 1427,-1455 1427,-1456 1427,-1457 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1426,-1457 1426,-1456 1426,-1455 1426,-1454 1427,-1452 1427,-1451 1428,-1451 1429,-1450 1430,-1449 1431,-1448 1432,-1448 1433,-1447 1434,-1447 1435,-1447 1436,-1447 1437,-1447 1438,-1448 1439,-1448 1440,-1449 1441,-1450 1442,-1451 1443,-1451 1443,-1452 1444,-1454 1444,-1455 1444,-1456 1444,-1457 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1401,-617 1402,-617 1403,-617 1404,-618 1405,-618 1406,-619 1407,-620 1408,-621 1408,-622 1409,-623 1409,-624 1409,-625 1409,-626 1409,-627 1409,-628 1409,-629 1408,-630 1408,-631 1407,-632 1406,-633 1405,-634 1404,-634 1403,-635 1402,-635 1401,-635 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1401,-599 1402,-599 1403,-599 1404,-600 1405,-600 1406,-601 1407,-602 1408,-603 1408,-604 1409,-605 1409,-606 1409,-607 1409,-608 1409,-609 1409,-610 1409,-611 1408,-612 1408,-613 1407,-614 1406,-615 1405,-616 1404,-616 1403,-617 1402,-617 1401,-617 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1790,-1531 1790,-1530 1790,-1529 1791,-1528 1791,-1527 1792,-1526 1793,-1525 1794,-1524 1795,-1524 1796,-1523 1797,-1523 1798,-1523 1799,-1523 1800,-1523 1801,-1523 1802,-1523 1803,-1524 1804,-1524 1805,-1525 1806,-1526 1807,-1527 1807,-1528 1808,-1529 1808,-1530 1808,-1531 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1808,-1531 1808,-1530 1808,-1529 1809,-1528 1809,-1527 1810,-1526 1811,-1525 1812,-1524 1813,-1524 1814,-1523 1815,-1523 1816,-1523 1817,-1523 1818,-1523 1819,-1523 1820,-1523 1821,-1524 1822,-1524 1823,-1525 1824,-1526 1825,-1527 1825,-1528 1826,-1529 1826,-1530 1826,-1531 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1115,-722 1116,-722 1117,-722 1118,-723 1119,-723 1120,-724 1121,-725 1122,-726 1122,-727 1123,-728 1123,-729 1123,-730 1123,-731 1123,-732 1123,-733 1123,-734 1122,-735 1122,-736 1121,-737 1120,-738 1119,-739 1118,-739 1117,-740 1116,-740 1115,-740 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1115,-704 1116,-704 1117,-704 1118,-705 1119,-705 1120,-706 1121,-707 1122,-708 1122,-709 1123,-710 1123,-711 1123,-712 1123,-713 1123,-714 1123,-715 1123,-716 1122,-717 1122,-718 1121,-719 1120,-720 1119,-721 1118,-721 1117,-722 1116,-722 1115,-722 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="473,-1782 474,-1782 475,-1782 476,-1783 477,-1783 478,-1784 479,-1785 480,-1785 480,-1786 481,-1787 481,-1788 482,-1789 482,-1790 482,-1792 482,-1793 481,-1794 481,-1795 480,-1796 480,-1797 479,-1797 478,-1798 477,-1799 476,-1799 475,-1800 474,-1800 473,-1800 472,-1800 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="472,-1766 473,-1766 474,-1766 475,-1766 476,-1766 477,-1766 478,-1767 479,-1767 480,-1768 481,-1769 481,-1769 482,-1770 482,-1771 483,-1772 483,-1774 483,-1775 483,-1776 483,-1777 482,-1778 482,-1779 481,-1780 481,-1780 480,-1781 479,-1782 478,-1782 477,-1783 476,-1783 " stroke="rgb(255,255,255)" stroke-width="0.0325"/>
   <polyline DF8003:Layer="0" fill="none" points="350,-1774 351,-1774 352,-1774 353,-1775 354,-1775 355,-1776 356,-1777 357,-1777 357,-1778 358,-1779 358,-1780 359,-1781 359,-1782 359,-1784 359,-1785 358,-1786 358,-1787 357,-1788 357,-1789 356,-1789 355,-1790 354,-1791 353,-1791 352,-1792 351,-1792 350,-1792 349,-1792 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="347,-1758 348,-1758 349,-1758 350,-1758 352,-1758 353,-1758 354,-1758 355,-1759 356,-1760 356,-1760 357,-1761 358,-1762 358,-1763 359,-1764 359,-1765 359,-1767 359,-1768 359,-1769 358,-1770 358,-1771 357,-1772 357,-1773 356,-1774 355,-1774 354,-1775 " stroke="rgb(255,255,255)" stroke-width="0.035"/>
   <polyline DF8003:Layer="0" fill="none" points="240,-1775 241,-1775 242,-1775 243,-1776 244,-1776 245,-1777 246,-1778 247,-1779 247,-1780 248,-1781 248,-1782 248,-1783 248,-1784 248,-1785 248,-1786 248,-1787 247,-1788 247,-1789 246,-1790 245,-1791 244,-1792 243,-1792 242,-1793 241,-1793 240,-1793 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="240,-1759 241,-1759 242,-1759 243,-1760 244,-1760 245,-1761 246,-1762 247,-1763 247,-1764 248,-1765 248,-1766 248,-1767 248,-1768 248,-1769 248,-1770 248,-1771 247,-1772 247,-1773 246,-1774 245,-1775 244,-1776 243,-1776 242,-1777 241,-1777 240,-1777 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3909,-1932 3909,-1931 3909,-1930 3910,-1929 3910,-1928 3911,-1927 3912,-1926 3913,-1925 3914,-1925 3915,-1924 3916,-1924 3917,-1924 3918,-1924 3919,-1924 3920,-1924 3921,-1924 3922,-1925 3923,-1925 3924,-1926 3925,-1927 3926,-1928 3926,-1929 3927,-1930 3927,-1931 3927,-1932 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3927,-1932 3927,-1931 3927,-1930 3928,-1929 3928,-1928 3929,-1927 3930,-1926 3931,-1925 3932,-1925 3933,-1924 3934,-1924 3935,-1924 3936,-1924 3937,-1924 3938,-1924 3939,-1924 3940,-1925 3941,-1925 3942,-1926 3943,-1927 3944,-1928 3944,-1929 3945,-1930 3945,-1931 3945,-1932 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3909,-1869 3909,-1868 3909,-1867 3909,-1866 3910,-1864 3910,-1863 3911,-1863 3912,-1862 3913,-1861 3914,-1860 3915,-1860 3916,-1859 3917,-1859 3918,-1859 3919,-1859 3920,-1859 3921,-1860 3922,-1860 3923,-1861 3924,-1862 3925,-1863 3926,-1863 3926,-1864 3927,-1866 3927,-1867 3927,-1868 3927,-1869 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3928,-1870 3928,-1869 3928,-1868 3928,-1867 3929,-1865 3929,-1864 3930,-1864 3931,-1863 3932,-1862 3933,-1861 3934,-1861 3935,-1860 3936,-1860 3937,-1860 3938,-1860 3939,-1860 3940,-1861 3941,-1861 3942,-1862 3943,-1863 3944,-1864 3945,-1864 3945,-1865 3946,-1867 3946,-1868 3946,-1869 3946,-1870 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3882,-1802 3882,-1801 3882,-1800 3882,-1799 3883,-1798 3884,-1797 3884,-1796 3885,-1795 3886,-1794 3887,-1794 3888,-1793 3889,-1793 3890,-1793 3891,-1793 3892,-1793 3893,-1793 3894,-1793 3895,-1794 3896,-1794 3897,-1795 3898,-1796 3899,-1797 3899,-1798 3900,-1799 3900,-1800 3900,-1801 3900,-1802 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3899,-1802 3899,-1801 3899,-1800 3900,-1799 3900,-1798 3901,-1797 3902,-1796 3903,-1795 3904,-1795 3905,-1794 3906,-1794 3907,-1794 3908,-1794 3909,-1794 3910,-1794 3911,-1794 3912,-1795 3913,-1795 3914,-1796 3915,-1797 3916,-1798 3916,-1799 3917,-1800 3917,-1801 3917,-1802 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3743,-1727 3743,-1726 3743,-1725 3744,-1724 3744,-1723 3745,-1722 3746,-1721 3747,-1720 3748,-1720 3749,-1719 3750,-1719 3751,-1719 3752,-1719 3753,-1719 3754,-1719 3755,-1719 3756,-1720 3757,-1720 3758,-1721 3759,-1722 3760,-1723 3760,-1724 3761,-1725 3761,-1726 3761,-1727 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3761,-1728 3761,-1727 3761,-1726 3762,-1725 3762,-1724 3763,-1723 3764,-1722 3765,-1721 3766,-1721 3767,-1720 3768,-1720 3769,-1720 3770,-1720 3771,-1720 3772,-1720 3773,-1720 3774,-1721 3775,-1721 3776,-1722 3777,-1723 3778,-1724 3778,-1725 3779,-1726 3779,-1727 3779,-1728 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3741,-1592 3741,-1591 3741,-1590 3741,-1589 3742,-1588 3743,-1587 3743,-1586 3744,-1585 3745,-1584 3746,-1584 3747,-1583 3748,-1583 3749,-1583 3750,-1583 3751,-1583 3752,-1583 3753,-1583 3754,-1584 3755,-1584 3756,-1585 3757,-1586 3758,-1587 3758,-1588 3759,-1589 3759,-1590 3759,-1591 3759,-1592 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3757,-1592 3757,-1591 3757,-1590 3757,-1589 3758,-1587 3758,-1586 3759,-1586 3760,-1585 3761,-1584 3762,-1583 3763,-1583 3764,-1582 3765,-1582 3766,-1582 3767,-1582 3768,-1582 3769,-1583 3770,-1583 3771,-1584 3772,-1585 3773,-1586 3774,-1586 3774,-1587 3775,-1589 3775,-1590 3775,-1591 3775,-1592 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3314,-1837 3314,-1836 3314,-1835 3314,-1833 3314,-1832 3315,-1831 3315,-1830 3316,-1829 3317,-1828 3318,-1828 3319,-1827 3320,-1827 3321,-1826 3322,-1826 3324,-1826 3325,-1826 3326,-1827 3327,-1827 3328,-1828 3329,-1828 3330,-1829 3331,-1830 3331,-1831 3332,-1832 3332,-1833 3332,-1835 3332,-1836 3332,-1837 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3331,-1835 3331,-1834 3331,-1833 3332,-1832 3332,-1831 3333,-1830 3334,-1829 3335,-1828 3336,-1828 3337,-1827 3338,-1827 3339,-1827 3340,-1827 3341,-1827 3342,-1827 3343,-1827 3344,-1828 3345,-1828 3346,-1829 3347,-1830 3348,-1831 3348,-1832 3349,-1833 3349,-1834 3349,-1835 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3405,-1640 3406,-1640 3407,-1640 3408,-1641 3409,-1641 3410,-1642 3411,-1643 3412,-1644 3412,-1645 3413,-1646 3413,-1647 3413,-1648 3413,-1649 3413,-1650 3413,-1651 3413,-1652 3412,-1653 3412,-1654 3411,-1655 3410,-1656 3409,-1657 3408,-1657 3407,-1658 3406,-1658 3405,-1658 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3404,-1657 3405,-1657 3406,-1657 3407,-1658 3408,-1658 3409,-1659 3410,-1660 3411,-1661 3411,-1662 3412,-1663 3412,-1664 3412,-1665 3412,-1666 3412,-1667 3412,-1668 3412,-1669 3411,-1670 3411,-1671 3410,-1672 3409,-1673 3408,-1674 3407,-1674 3406,-1675 3405,-1675 3404,-1675 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3110,-1695 3110,-1694 3110,-1693 3110,-1692 3111,-1691 3112,-1690 3112,-1689 3113,-1688 3114,-1687 3115,-1687 3116,-1686 3117,-1686 3118,-1686 3119,-1686 3120,-1686 3121,-1686 3122,-1686 3123,-1687 3124,-1687 3125,-1688 3126,-1689 3127,-1690 3127,-1691 3128,-1692 3128,-1693 3128,-1694 3128,-1695 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3126,-1694 3126,-1693 3126,-1692 3127,-1691 3127,-1690 3128,-1689 3129,-1688 3130,-1687 3131,-1687 3132,-1686 3133,-1686 3134,-1686 3135,-1686 3136,-1686 3137,-1686 3138,-1686 3139,-1687 3140,-1687 3141,-1688 3142,-1689 3143,-1690 3143,-1691 3144,-1692 3144,-1693 3144,-1694 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3111,-1621 3111,-1620 3111,-1619 3111,-1618 3112,-1617 3113,-1616 3113,-1615 3114,-1614 3115,-1613 3116,-1613 3117,-1612 3118,-1612 3119,-1612 3120,-1612 3121,-1612 3122,-1612 3123,-1612 3124,-1613 3125,-1613 3126,-1614 3127,-1615 3128,-1616 3128,-1617 3129,-1618 3129,-1619 3129,-1620 3129,-1621 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3129,-1620 3129,-1619 3129,-1618 3129,-1617 3130,-1616 3131,-1615 3131,-1614 3132,-1613 3133,-1612 3134,-1612 3135,-1611 3136,-1611 3137,-1611 3138,-1611 3139,-1611 3140,-1611 3141,-1611 3142,-1612 3143,-1612 3144,-1613 3145,-1614 3146,-1615 3146,-1616 3147,-1617 3147,-1618 3147,-1619 3147,-1620 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3111,-1559 3112,-1559 3113,-1559 3115,-1559 3116,-1559 3117,-1560 3118,-1560 3119,-1561 3120,-1562 3120,-1563 3121,-1564 3121,-1565 3122,-1566 3122,-1567 3122,-1569 3122,-1570 3121,-1571 3121,-1572 3120,-1573 3120,-1574 3119,-1575 3118,-1576 3117,-1576 3116,-1577 3115,-1577 3113,-1577 3112,-1577 3111,-1577 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3118,-1575 3119,-1575 3120,-1576 3121,-1576 3122,-1577 3123,-1578 3123,-1579 3124,-1580 3124,-1581 3125,-1582 3125,-1583 3125,-1584 3125,-1585 3124,-1586 3124,-1588 3124,-1589 3123,-1589 3122,-1590 3121,-1591 3120,-1592 3119,-1592 3118,-1593 3117,-1593 3116,-1593 3115,-1593 3114,-1593 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3126,-1481 3126,-1480 3126,-1479 3127,-1478 3127,-1477 3128,-1476 3129,-1475 3130,-1474 3131,-1474 3132,-1473 3133,-1473 3134,-1473 3135,-1473 3136,-1473 3137,-1473 3138,-1473 3139,-1474 3140,-1474 3141,-1475 3142,-1476 3143,-1477 3143,-1478 3144,-1479 3144,-1480 3144,-1481 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3108,-1482 3108,-1481 3108,-1480 3109,-1479 3109,-1478 3110,-1477 3111,-1476 3112,-1475 3113,-1475 3114,-1474 3115,-1474 3116,-1474 3117,-1474 3118,-1474 3119,-1474 3120,-1474 3121,-1475 3122,-1475 3123,-1476 3124,-1477 3125,-1478 3125,-1479 3126,-1480 3126,-1481 3126,-1482 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3124,-1409 3124,-1408 3124,-1407 3125,-1406 3125,-1405 3126,-1404 3127,-1403 3128,-1402 3129,-1402 3130,-1401 3131,-1401 3132,-1401 3133,-1401 3134,-1401 3135,-1401 3136,-1401 3137,-1402 3138,-1402 3139,-1403 3140,-1404 3141,-1405 3141,-1406 3142,-1407 3142,-1408 3142,-1409 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3105,-1412 3105,-1411 3105,-1410 3105,-1408 3105,-1407 3106,-1406 3106,-1405 3107,-1404 3108,-1403 3109,-1403 3110,-1402 3111,-1402 3112,-1401 3113,-1401 3115,-1401 3116,-1401 3117,-1402 3118,-1402 3119,-1403 3120,-1403 3121,-1404 3122,-1405 3122,-1406 3123,-1407 3123,-1408 3123,-1410 3123,-1411 3123,-1412 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3124,-1031 3124,-1030 3124,-1029 3125,-1028 3125,-1027 3126,-1026 3127,-1025 3128,-1024 3129,-1024 3130,-1023 3131,-1023 3132,-1023 3133,-1023 3134,-1023 3135,-1023 3136,-1023 3137,-1024 3138,-1024 3139,-1025 3140,-1026 3141,-1027 3141,-1028 3142,-1029 3142,-1030 3142,-1031 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3105,-1034 3105,-1033 3105,-1032 3105,-1030 3105,-1029 3106,-1028 3106,-1027 3107,-1026 3108,-1025 3109,-1025 3110,-1024 3111,-1024 3112,-1023 3113,-1023 3115,-1023 3116,-1023 3117,-1024 3118,-1024 3119,-1025 3120,-1025 3121,-1026 3122,-1027 3122,-1028 3123,-1029 3123,-1030 3123,-1032 3123,-1033 3123,-1034 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3124,-961 3124,-960 3124,-959 3124,-958 3125,-957 3126,-956 3126,-955 3127,-954 3128,-953 3129,-953 3130,-952 3131,-952 3132,-952 3133,-952 3134,-952 3135,-952 3136,-952 3137,-953 3138,-953 3139,-954 3140,-955 3141,-956 3141,-957 3142,-958 3142,-959 3142,-960 3142,-961 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3105,-963 3105,-962 3105,-961 3105,-960 3106,-958 3106,-957 3107,-957 3108,-956 3109,-955 3110,-954 3111,-954 3112,-953 3113,-953 3114,-953 3115,-953 3116,-953 3117,-954 3118,-954 3119,-955 3120,-956 3121,-957 3122,-957 3122,-958 3123,-960 3123,-961 3123,-962 3123,-963 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3230,-1233 3231,-1233 3232,-1233 3233,-1233 3235,-1234 3236,-1234 3236,-1235 3237,-1236 3238,-1237 3239,-1238 3239,-1239 3240,-1240 3240,-1241 3240,-1242 3240,-1243 3240,-1244 3239,-1245 3239,-1246 3238,-1247 3237,-1248 3236,-1249 3236,-1250 3235,-1250 3233,-1251 3232,-1251 3231,-1251 3230,-1251 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3231,-1251 3232,-1251 3233,-1251 3234,-1251 3236,-1252 3237,-1252 3237,-1253 3238,-1254 3239,-1255 3240,-1256 3240,-1257 3241,-1258 3241,-1259 3241,-1260 3241,-1261 3241,-1262 3240,-1263 3240,-1264 3239,-1265 3238,-1266 3237,-1267 3237,-1268 3236,-1268 3234,-1269 3233,-1269 3232,-1269 3231,-1269 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3337,-1232 3338,-1232 3339,-1232 3340,-1233 3341,-1233 3342,-1234 3343,-1235 3344,-1236 3344,-1237 3345,-1238 3345,-1239 3345,-1240 3345,-1241 3345,-1242 3345,-1243 3345,-1244 3344,-1245 3344,-1246 3343,-1247 3342,-1248 3341,-1249 3340,-1249 3339,-1250 3338,-1250 3337,-1250 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3337,-1250 3338,-1250 3339,-1250 3340,-1251 3341,-1251 3342,-1252 3343,-1253 3344,-1254 3344,-1255 3345,-1256 3345,-1257 3345,-1258 3345,-1259 3345,-1260 3345,-1261 3345,-1262 3344,-1263 3344,-1264 3343,-1265 3342,-1266 3341,-1267 3340,-1267 3339,-1268 3338,-1268 3337,-1268 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3481,-1232 3482,-1232 3483,-1232 3484,-1233 3485,-1233 3486,-1234 3487,-1235 3488,-1236 3488,-1237 3489,-1238 3489,-1239 3489,-1240 3489,-1241 3489,-1242 3489,-1243 3489,-1244 3488,-1245 3488,-1246 3487,-1247 3486,-1248 3485,-1249 3484,-1249 3483,-1250 3482,-1250 3481,-1250 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3481,-1250 3482,-1250 3483,-1250 3484,-1251 3485,-1251 3486,-1252 3487,-1253 3488,-1254 3488,-1255 3489,-1256 3489,-1257 3489,-1258 3489,-1259 3489,-1260 3489,-1261 3489,-1262 3488,-1263 3488,-1264 3487,-1265 3486,-1266 3485,-1267 3484,-1267 3483,-1268 3482,-1268 3481,-1268 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2914,-1146 2915,-1146 2916,-1146 2917,-1146 2918,-1147 2919,-1148 2920,-1148 2921,-1149 2922,-1150 2922,-1151 2923,-1152 2923,-1153 2923,-1154 2923,-1155 2923,-1156 2923,-1157 2923,-1158 2922,-1159 2922,-1160 2921,-1161 2920,-1162 2919,-1163 2918,-1163 2917,-1164 2916,-1164 2915,-1164 2914,-1164 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2915,-1164 2916,-1164 2917,-1164 2918,-1164 2919,-1165 2920,-1166 2921,-1166 2922,-1167 2923,-1168 2923,-1169 2924,-1170 2924,-1171 2924,-1172 2924,-1173 2924,-1174 2924,-1175 2924,-1176 2923,-1177 2923,-1178 2922,-1179 2921,-1180 2920,-1181 2919,-1181 2918,-1182 2917,-1182 2916,-1182 2915,-1182 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2590,-1212 2590,-1211 2590,-1210 2591,-1209 2591,-1208 2592,-1207 2593,-1206 2594,-1205 2595,-1205 2596,-1204 2597,-1204 2598,-1204 2599,-1204 2600,-1204 2601,-1204 2602,-1204 2603,-1205 2604,-1205 2605,-1206 2606,-1207 2607,-1208 2607,-1209 2608,-1210 2608,-1211 2608,-1212 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2607,-1211 2607,-1210 2607,-1209 2607,-1208 2608,-1207 2609,-1206 2609,-1205 2610,-1204 2611,-1203 2612,-1203 2613,-1202 2614,-1202 2615,-1202 2616,-1202 2617,-1202 2618,-1202 2619,-1202 2620,-1203 2621,-1203 2622,-1204 2623,-1205 2624,-1206 2624,-1207 2625,-1208 2625,-1209 2625,-1210 2625,-1211 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2738,-1049 2739,-1049 2740,-1049 2741,-1050 2742,-1050 2743,-1051 2744,-1052 2745,-1053 2745,-1054 2746,-1055 2746,-1056 2746,-1057 2746,-1058 2746,-1059 2746,-1060 2746,-1061 2745,-1062 2745,-1063 2744,-1064 2743,-1065 2742,-1066 2741,-1066 2740,-1067 2739,-1067 2738,-1067 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2738,-1067 2739,-1067 2740,-1067 2741,-1068 2742,-1068 2743,-1069 2744,-1070 2745,-1071 2745,-1072 2746,-1073 2746,-1074 2746,-1075 2746,-1076 2746,-1077 2746,-1078 2746,-1079 2745,-1080 2745,-1081 2744,-1082 2743,-1083 2742,-1084 2741,-1084 2740,-1085 2739,-1085 2738,-1085 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2570,-1142 2571,-1142 2572,-1142 2573,-1143 2574,-1143 2575,-1144 2576,-1145 2577,-1146 2577,-1147 2578,-1148 2578,-1149 2578,-1150 2578,-1151 2578,-1152 2578,-1153 2578,-1154 2577,-1155 2577,-1156 2576,-1157 2575,-1158 2574,-1159 2573,-1159 2572,-1160 2571,-1160 2570,-1160 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2571,-1160 2572,-1160 2573,-1160 2574,-1160 2575,-1161 2576,-1162 2577,-1162 2578,-1163 2579,-1164 2579,-1165 2580,-1166 2580,-1167 2580,-1168 2580,-1169 2580,-1170 2580,-1171 2580,-1172 2579,-1173 2579,-1174 2578,-1175 2577,-1176 2576,-1177 2575,-1177 2574,-1178 2573,-1178 2572,-1178 2571,-1178 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2487,-1142 2488,-1142 2489,-1142 2490,-1143 2491,-1143 2492,-1144 2493,-1145 2494,-1146 2494,-1147 2495,-1148 2495,-1149 2495,-1150 2495,-1151 2495,-1152 2495,-1153 2495,-1154 2494,-1155 2494,-1156 2493,-1157 2492,-1158 2491,-1159 2490,-1159 2489,-1160 2488,-1160 2487,-1160 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2487,-1160 2488,-1160 2489,-1160 2490,-1161 2491,-1161 2492,-1162 2493,-1163 2494,-1164 2494,-1165 2495,-1166 2495,-1167 2495,-1168 2495,-1169 2495,-1170 2495,-1171 2495,-1172 2494,-1173 2494,-1174 2493,-1175 2492,-1176 2491,-1177 2490,-1177 2489,-1178 2488,-1178 2487,-1178 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2597,-817 2598,-817 2599,-817 2600,-817 2602,-818 2603,-818 2603,-819 2604,-820 2605,-821 2606,-822 2606,-823 2607,-824 2607,-825 2607,-826 2607,-827 2607,-828 2606,-829 2606,-830 2605,-831 2604,-832 2603,-833 2603,-834 2602,-834 2600,-835 2599,-835 2598,-835 2597,-835 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2597,-835 2598,-835 2599,-835 2600,-835 2602,-836 2603,-836 2603,-837 2604,-838 2605,-839 2606,-840 2606,-841 2607,-842 2607,-843 2607,-844 2607,-845 2607,-846 2606,-847 2606,-848 2605,-849 2604,-850 2603,-851 2603,-852 2602,-852 2600,-853 2599,-853 2598,-853 2597,-853 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2739,-794 2740,-794 2741,-794 2742,-795 2743,-795 2744,-796 2745,-797 2746,-798 2746,-799 2747,-800 2747,-801 2747,-802 2747,-803 2747,-804 2747,-805 2747,-806 2746,-807 2746,-808 2745,-809 2744,-810 2743,-811 2742,-811 2741,-812 2740,-812 2739,-812 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2739,-812 2740,-812 2741,-812 2742,-813 2743,-813 2744,-814 2745,-815 2746,-816 2746,-817 2747,-818 2747,-819 2747,-820 2747,-821 2747,-822 2747,-823 2747,-824 2746,-825 2746,-826 2745,-827 2744,-828 2743,-829 2742,-829 2741,-830 2740,-830 2739,-830 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2915,-819 2916,-819 2917,-819 2918,-820 2919,-820 2920,-821 2921,-822 2922,-823 2922,-824 2923,-825 2923,-826 2923,-827 2923,-828 2923,-829 2923,-830 2923,-831 2922,-832 2922,-833 2921,-834 2920,-835 2919,-836 2918,-836 2917,-837 2916,-837 2915,-837 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2915,-837 2916,-837 2917,-837 2918,-838 2919,-838 2920,-839 2921,-840 2922,-841 2922,-842 2923,-843 2923,-844 2923,-845 2923,-846 2923,-847 2923,-848 2923,-849 2922,-850 2922,-851 2921,-852 2920,-853 2919,-854 2918,-854 2917,-855 2916,-855 2915,-855 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3258,-715 3259,-715 3260,-715 3261,-716 3262,-716 3263,-717 3264,-718 3265,-719 3265,-720 3266,-721 3266,-722 3266,-723 3266,-724 3266,-725 3266,-726 3266,-727 3265,-728 3265,-729 3264,-730 3263,-731 3262,-732 3261,-732 3260,-733 3259,-733 3258,-733 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3258,-733 3259,-733 3260,-733 3261,-734 3262,-734 3263,-735 3264,-736 3265,-737 3265,-738 3266,-739 3266,-740 3266,-741 3266,-742 3266,-743 3266,-744 3266,-745 3265,-746 3265,-747 3264,-748 3263,-749 3262,-750 3261,-750 3260,-751 3259,-751 3258,-751 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3390,-667 3391,-667 3392,-667 3393,-667 3394,-668 3395,-669 3396,-669 3397,-670 3398,-671 3398,-672 3399,-673 3399,-674 3399,-675 3399,-676 3399,-677 3399,-678 3399,-679 3398,-680 3398,-681 3397,-682 3396,-683 3395,-684 3394,-684 3393,-685 3392,-685 3391,-685 3390,-685 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3392,-685 3393,-685 3394,-685 3395,-686 3396,-686 3397,-687 3398,-688 3399,-689 3399,-690 3400,-691 3400,-692 3400,-693 3400,-694 3400,-695 3400,-696 3400,-697 3399,-698 3399,-699 3398,-700 3397,-701 3396,-702 3395,-702 3394,-703 3393,-703 3392,-703 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3447,-783 3447,-782 3447,-781 3448,-780 3448,-779 3449,-778 3450,-777 3451,-776 3452,-776 3453,-775 3454,-775 3455,-775 3456,-775 3457,-775 3458,-775 3459,-775 3460,-776 3461,-776 3462,-777 3463,-778 3464,-779 3464,-780 3465,-781 3465,-782 3465,-783 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3428,-783 3428,-782 3428,-781 3429,-780 3429,-779 3430,-778 3431,-777 3432,-776 3433,-776 3434,-775 3435,-775 3436,-775 3437,-775 3438,-775 3439,-775 3440,-775 3441,-776 3442,-776 3443,-777 3444,-778 3445,-779 3445,-780 3446,-781 3446,-782 3446,-783 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2946,-557 2947,-557 2948,-557 2949,-557 2951,-558 2952,-558 2952,-559 2953,-560 2954,-561 2955,-562 2955,-563 2956,-564 2956,-565 2956,-566 2956,-567 2956,-568 2955,-569 2955,-570 2954,-571 2953,-572 2952,-573 2952,-574 2951,-574 2949,-575 2948,-575 2947,-575 2946,-575 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2946,-575 2947,-575 2948,-575 2949,-575 2951,-576 2952,-576 2952,-577 2953,-578 2954,-579 2955,-580 2955,-581 2956,-582 2956,-583 2956,-584 2956,-585 2956,-586 2955,-587 2955,-588 2954,-589 2953,-590 2952,-591 2952,-592 2951,-592 2949,-593 2948,-593 2947,-593 2946,-593 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2830,-534 2831,-534 2832,-534 2833,-534 2834,-535 2835,-536 2836,-536 2837,-537 2838,-538 2838,-539 2839,-540 2839,-541 2839,-542 2839,-543 2839,-544 2839,-545 2839,-546 2838,-547 2838,-548 2837,-549 2836,-550 2835,-551 2834,-551 2833,-552 2832,-552 2831,-552 2830,-552 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2830,-552 2831,-552 2832,-552 2833,-552 2834,-553 2835,-554 2836,-554 2837,-555 2838,-556 2838,-557 2839,-558 2839,-559 2839,-560 2839,-561 2839,-562 2839,-563 2839,-564 2838,-565 2838,-566 2837,-567 2836,-568 2835,-569 2834,-569 2833,-570 2832,-570 2831,-570 2830,-570 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2747,-559 2748,-559 2749,-559 2750,-559 2751,-560 2752,-561 2753,-561 2754,-562 2755,-563 2755,-564 2756,-565 2756,-566 2756,-567 2756,-568 2756,-569 2756,-570 2756,-571 2755,-572 2755,-573 2754,-574 2753,-575 2752,-576 2751,-576 2750,-577 2749,-577 2748,-577 2747,-577 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2748,-577 2749,-577 2750,-577 2751,-578 2752,-578 2753,-579 2754,-580 2755,-581 2755,-582 2756,-583 2756,-584 2756,-585 2756,-586 2756,-587 2756,-588 2756,-589 2755,-590 2755,-591 2754,-592 2753,-593 2752,-594 2751,-594 2750,-595 2749,-595 2748,-595 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2645,-559 2646,-559 2647,-559 2648,-560 2649,-560 2650,-561 2651,-562 2652,-563 2652,-564 2653,-565 2653,-566 2653,-567 2653,-568 2653,-569 2653,-570 2653,-571 2652,-572 2652,-573 2651,-574 2650,-575 2649,-576 2648,-576 2647,-577 2646,-577 2645,-577 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2645,-577 2646,-577 2647,-577 2648,-578 2649,-578 2650,-579 2651,-580 2652,-581 2652,-582 2653,-583 2653,-584 2653,-585 2653,-586 2653,-587 2653,-588 2653,-589 2652,-590 2652,-591 2651,-592 2650,-593 2649,-594 2648,-594 2647,-595 2646,-595 2645,-595 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2543,-560 2544,-560 2545,-560 2546,-560 2547,-561 2548,-562 2549,-562 2550,-563 2551,-564 2551,-565 2552,-566 2552,-567 2552,-568 2552,-569 2552,-570 2552,-571 2552,-572 2551,-573 2551,-574 2550,-575 2549,-576 2548,-577 2547,-577 2546,-578 2545,-578 2544,-578 2543,-578 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2543,-578 2544,-578 2545,-578 2546,-578 2547,-579 2548,-580 2549,-580 2550,-581 2551,-582 2551,-583 2552,-584 2552,-585 2552,-586 2552,-587 2552,-588 2552,-589 2552,-590 2551,-591 2551,-592 2550,-593 2549,-594 2548,-595 2547,-595 2546,-596 2545,-596 2544,-596 2543,-596 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2427,-560 2428,-560 2429,-560 2430,-560 2431,-561 2432,-562 2433,-562 2434,-563 2435,-564 2435,-565 2436,-566 2436,-567 2436,-568 2436,-569 2436,-570 2436,-571 2436,-572 2435,-573 2435,-574 2434,-575 2433,-576 2432,-577 2431,-577 2430,-578 2429,-578 2428,-578 2427,-578 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2427,-578 2428,-578 2429,-578 2430,-578 2431,-579 2432,-580 2433,-580 2434,-581 2435,-582 2435,-583 2436,-584 2436,-585 2436,-586 2436,-587 2436,-588 2436,-589 2436,-590 2435,-591 2435,-592 2434,-593 2433,-594 2432,-595 2431,-595 2430,-596 2429,-596 2428,-596 2427,-596 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2703,-176 2704,-176 2705,-176 2706,-177 2707,-177 2708,-178 2709,-179 2710,-180 2710,-181 2711,-182 2711,-183 2711,-184 2711,-185 2711,-186 2711,-187 2711,-188 2710,-189 2710,-190 2709,-191 2708,-192 2707,-193 2706,-193 2705,-194 2704,-194 2703,-194 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2703,-194 2704,-194 2705,-194 2706,-195 2707,-195 2708,-196 2709,-197 2710,-198 2710,-199 2711,-200 2711,-201 2711,-202 2711,-203 2711,-204 2711,-205 2711,-206 2710,-207 2710,-208 2709,-209 2708,-210 2707,-211 2706,-211 2705,-212 2704,-212 2703,-212 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2533,-177 2534,-177 2535,-177 2536,-177 2537,-178 2538,-179 2539,-179 2540,-180 2541,-181 2541,-182 2542,-183 2542,-184 2542,-185 2542,-186 2542,-187 2542,-188 2542,-189 2541,-190 2541,-191 2540,-192 2539,-193 2538,-194 2537,-194 2536,-195 2535,-195 2534,-195 2533,-195 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2533,-195 2534,-195 2535,-195 2536,-195 2537,-196 2538,-197 2539,-197 2540,-198 2541,-199 2541,-200 2542,-201 2542,-202 2542,-203 2542,-204 2542,-205 2542,-206 2542,-207 2541,-208 2541,-209 2540,-210 2539,-211 2538,-212 2537,-212 2536,-213 2535,-213 2534,-213 2533,-213 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2221,-224 2222,-224 2223,-224 2224,-224 2225,-225 2226,-226 2227,-226 2228,-227 2229,-228 2229,-229 2230,-230 2230,-231 2230,-232 2230,-233 2230,-234 2230,-235 2230,-236 2229,-237 2229,-238 2228,-239 2227,-240 2226,-241 2225,-241 2224,-242 2223,-242 2222,-242 2221,-242 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2221,-242 2222,-242 2223,-242 2224,-242 2225,-243 2226,-244 2227,-244 2228,-245 2229,-246 2229,-247 2230,-248 2230,-249 2230,-250 2230,-251 2230,-252 2230,-253 2230,-254 2229,-255 2229,-256 2228,-257 2227,-258 2226,-259 2225,-259 2224,-260 2223,-260 2222,-260 2221,-260 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2259,-343 2260,-343 2261,-343 2262,-344 2263,-344 2264,-345 2265,-346 2266,-347 2266,-348 2267,-349 2267,-350 2267,-351 2267,-352 2267,-353 2267,-354 2267,-355 2266,-356 2266,-357 2265,-358 2264,-359 2263,-360 2262,-360 2261,-361 2260,-361 2259,-361 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2258,-325 2259,-325 2260,-325 2261,-325 2262,-326 2263,-327 2264,-327 2265,-328 2266,-329 2266,-330 2267,-331 2267,-332 2267,-333 2267,-334 2267,-335 2267,-336 2267,-337 2266,-338 2266,-339 2265,-340 2264,-341 2263,-342 2262,-342 2261,-343 2260,-343 2259,-343 2258,-343 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3124,-291 3124,-290 3124,-289 3125,-288 3125,-287 3126,-286 3127,-285 3128,-284 3129,-284 3130,-283 3131,-283 3132,-283 3133,-283 3134,-283 3135,-283 3136,-283 3137,-284 3138,-284 3139,-285 3140,-286 3141,-287 3141,-288 3142,-289 3142,-290 3142,-291 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3106,-292 3106,-291 3106,-290 3107,-289 3107,-288 3108,-287 3109,-286 3110,-285 3111,-285 3112,-284 3113,-284 3114,-284 3115,-284 3116,-284 3117,-284 3118,-284 3119,-285 3120,-285 3121,-286 3122,-287 3123,-288 3123,-289 3124,-290 3124,-291 3124,-292 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2784,-1433 2785,-1433 2786,-1433 2787,-1433 2788,-1434 2789,-1435 2790,-1435 2791,-1436 2792,-1437 2792,-1438 2793,-1439 2793,-1440 2793,-1441 2793,-1442 2793,-1443 2793,-1444 2793,-1445 2792,-1446 2792,-1447 2791,-1448 2790,-1449 2789,-1450 2788,-1450 2787,-1451 2786,-1451 2785,-1451 2784,-1451 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2781,-1415 2782,-1415 2783,-1415 2784,-1416 2785,-1416 2786,-1417 2787,-1418 2788,-1419 2788,-1420 2789,-1421 2789,-1422 2789,-1423 2789,-1424 2789,-1425 2789,-1426 2789,-1427 2788,-1428 2788,-1429 2787,-1430 2786,-1431 2785,-1432 2784,-1432 2783,-1433 2782,-1433 2781,-1433 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2894,-1449 2895,-1449 2896,-1449 2897,-1449 2899,-1450 2900,-1450 2900,-1451 2901,-1452 2902,-1453 2903,-1454 2903,-1455 2904,-1456 2904,-1457 2904,-1458 2904,-1459 2904,-1460 2903,-1461 2903,-1462 2902,-1463 2901,-1464 2900,-1465 2900,-1466 2899,-1466 2897,-1467 2896,-1467 2895,-1467 2894,-1467 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2896,-1467 2897,-1467 2898,-1467 2899,-1467 2901,-1468 2902,-1468 2902,-1469 2903,-1470 2904,-1471 2905,-1472 2905,-1473 2906,-1474 2906,-1475 2906,-1476 2906,-1477 2906,-1478 2905,-1479 2905,-1480 2904,-1481 2903,-1482 2902,-1483 2902,-1484 2901,-1484 2899,-1485 2898,-1485 2897,-1485 2896,-1485 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2716,-1683 2717,-1683 2718,-1683 2719,-1684 2720,-1684 2721,-1685 2722,-1686 2723,-1687 2723,-1688 2724,-1689 2724,-1690 2724,-1691 2724,-1692 2724,-1693 2724,-1694 2724,-1695 2723,-1696 2723,-1697 2722,-1698 2721,-1699 2720,-1700 2719,-1700 2718,-1701 2717,-1701 2716,-1701 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2716,-1701 2717,-1701 2718,-1701 2719,-1702 2720,-1702 2721,-1703 2722,-1704 2723,-1705 2723,-1706 2724,-1707 2724,-1708 2724,-1709 2724,-1710 2724,-1711 2724,-1712 2724,-1713 2723,-1714 2723,-1715 2722,-1716 2721,-1717 2720,-1718 2719,-1718 2718,-1719 2717,-1719 2716,-1719 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2824,-1644 2825,-1644 2826,-1644 2828,-1644 2829,-1644 2830,-1645 2831,-1645 2832,-1646 2833,-1647 2833,-1648 2834,-1649 2834,-1650 2835,-1651 2835,-1652 2835,-1654 2835,-1655 2834,-1656 2834,-1657 2833,-1658 2833,-1659 2832,-1660 2831,-1661 2830,-1661 2829,-1662 2828,-1662 2826,-1662 2825,-1662 2824,-1662 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2824,-1662 2825,-1662 2826,-1662 2828,-1662 2829,-1662 2830,-1663 2831,-1663 2832,-1664 2833,-1665 2833,-1666 2834,-1667 2834,-1668 2835,-1669 2835,-1670 2835,-1672 2835,-1673 2834,-1674 2834,-1675 2833,-1676 2833,-1677 2832,-1678 2831,-1679 2830,-1679 2829,-1680 2828,-1680 2826,-1680 2825,-1680 2824,-1680 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2914,-1645 2915,-1645 2916,-1645 2917,-1645 2918,-1646 2919,-1647 2920,-1647 2921,-1648 2922,-1649 2922,-1650 2923,-1651 2923,-1652 2923,-1653 2923,-1654 2923,-1655 2923,-1656 2923,-1657 2922,-1658 2922,-1659 2921,-1660 2920,-1661 2919,-1662 2918,-1662 2917,-1663 2916,-1663 2915,-1663 2914,-1663 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2914,-1663 2915,-1663 2916,-1663 2917,-1663 2918,-1664 2919,-1665 2920,-1665 2921,-1666 2922,-1667 2922,-1668 2923,-1669 2923,-1670 2923,-1671 2923,-1672 2923,-1673 2923,-1674 2923,-1675 2922,-1676 2922,-1677 2921,-1678 2920,-1679 2919,-1680 2918,-1680 2917,-1681 2916,-1681 2915,-1681 2914,-1681 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1852,-1614 1852,-1613 1852,-1612 1852,-1610 1852,-1609 1853,-1608 1853,-1607 1854,-1606 1855,-1605 1856,-1605 1857,-1604 1858,-1604 1859,-1603 1860,-1603 1862,-1603 1863,-1603 1864,-1604 1865,-1604 1866,-1605 1867,-1605 1868,-1606 1869,-1607 1869,-1608 1870,-1609 1870,-1610 1870,-1612 1870,-1613 1870,-1614 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1834,-1613 1834,-1612 1834,-1611 1835,-1610 1835,-1609 1836,-1608 1837,-1607 1838,-1606 1839,-1606 1840,-1605 1841,-1605 1842,-1605 1843,-1605 1844,-1605 1845,-1605 1846,-1605 1847,-1606 1848,-1606 1849,-1607 1850,-1608 1851,-1609 1851,-1610 1852,-1611 1852,-1612 1852,-1613 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1855,-1530 1855,-1529 1855,-1528 1856,-1527 1856,-1526 1857,-1525 1858,-1524 1859,-1523 1860,-1523 1861,-1522 1862,-1522 1863,-1522 1864,-1522 1865,-1522 1866,-1522 1867,-1522 1868,-1523 1869,-1523 1870,-1524 1871,-1525 1872,-1526 1872,-1527 1873,-1528 1873,-1529 1873,-1530 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1838,-1531 1838,-1530 1838,-1529 1839,-1528 1839,-1527 1840,-1526 1841,-1525 1842,-1524 1843,-1524 1844,-1523 1845,-1523 1846,-1523 1847,-1523 1848,-1523 1849,-1523 1850,-1523 1851,-1524 1852,-1524 1853,-1525 1854,-1526 1855,-1527 1855,-1528 1856,-1529 1856,-1530 1856,-1531 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2083,-1544 2084,-1544 2085,-1544 2086,-1545 2087,-1545 2088,-1546 2089,-1547 2090,-1548 2090,-1549 2091,-1550 2091,-1551 2091,-1552 2091,-1553 2091,-1554 2091,-1555 2091,-1556 2090,-1557 2090,-1558 2089,-1559 2088,-1560 2087,-1561 2086,-1561 2085,-1562 2084,-1562 2083,-1562 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2084,-1525 2085,-1525 2086,-1525 2087,-1525 2089,-1526 2090,-1526 2090,-1527 2091,-1528 2092,-1529 2093,-1530 2093,-1531 2094,-1532 2094,-1533 2094,-1534 2094,-1535 2094,-1536 2093,-1537 2093,-1538 2092,-1539 2091,-1540 2090,-1541 2090,-1542 2089,-1542 2087,-1543 2086,-1543 2085,-1543 2084,-1543 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2144,-1457 2144,-1456 2144,-1455 2145,-1454 2145,-1453 2146,-1452 2147,-1451 2148,-1450 2149,-1450 2150,-1449 2151,-1449 2152,-1449 2153,-1449 2154,-1449 2155,-1449 2156,-1449 2157,-1450 2158,-1450 2159,-1451 2160,-1452 2161,-1453 2161,-1454 2162,-1455 2162,-1456 2162,-1457 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2127,-1457 2127,-1456 2127,-1455 2128,-1454 2128,-1453 2129,-1452 2130,-1451 2131,-1450 2132,-1450 2133,-1449 2134,-1449 2135,-1449 2136,-1449 2137,-1449 2138,-1449 2139,-1449 2140,-1450 2141,-1450 2142,-1451 2143,-1452 2144,-1453 2144,-1454 2145,-1455 2145,-1456 2145,-1457 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2076,-1315 2077,-1315 2078,-1315 2079,-1316 2080,-1316 2081,-1317 2082,-1318 2083,-1319 2083,-1320 2084,-1321 2084,-1322 2084,-1323 2084,-1324 2084,-1325 2084,-1326 2084,-1327 2083,-1328 2083,-1329 2082,-1330 2081,-1331 2080,-1332 2079,-1332 2078,-1333 2077,-1333 2076,-1333 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2076,-1333 2077,-1333 2078,-1333 2079,-1334 2080,-1334 2081,-1335 2082,-1336 2083,-1337 2083,-1338 2084,-1339 2084,-1340 2084,-1341 2084,-1342 2084,-1343 2084,-1344 2084,-1345 2083,-1346 2083,-1347 2082,-1348 2081,-1349 2080,-1350 2079,-1350 2078,-1351 2077,-1351 2076,-1351 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2171,-1312 2172,-1312 2173,-1312 2174,-1313 2175,-1313 2176,-1314 2177,-1315 2178,-1316 2178,-1317 2179,-1318 2179,-1319 2179,-1320 2179,-1321 2179,-1322 2179,-1323 2179,-1324 2178,-1325 2178,-1326 2177,-1327 2176,-1328 2175,-1329 2174,-1329 2173,-1330 2172,-1330 2171,-1330 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2171,-1330 2172,-1330 2173,-1330 2174,-1331 2175,-1331 2176,-1332 2177,-1333 2178,-1334 2178,-1335 2179,-1336 2179,-1337 2179,-1338 2179,-1339 2179,-1340 2179,-1341 2179,-1342 2178,-1343 2178,-1344 2177,-1345 2176,-1346 2175,-1347 2174,-1347 2173,-1348 2172,-1348 2171,-1348 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1925,-1102 1926,-1102 1927,-1102 1928,-1103 1929,-1103 1930,-1104 1931,-1105 1932,-1106 1932,-1107 1933,-1108 1933,-1109 1933,-1110 1933,-1111 1933,-1112 1933,-1113 1933,-1114 1932,-1115 1932,-1116 1931,-1117 1930,-1118 1929,-1119 1928,-1119 1927,-1120 1926,-1120 1925,-1120 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1925,-1120 1926,-1120 1927,-1120 1928,-1121 1929,-1121 1930,-1122 1931,-1123 1932,-1124 1932,-1125 1933,-1126 1933,-1127 1933,-1128 1933,-1129 1933,-1130 1933,-1131 1933,-1132 1932,-1133 1932,-1134 1931,-1135 1930,-1136 1929,-1137 1928,-1137 1927,-1138 1926,-1138 1925,-1138 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2105,-1073 2106,-1073 2107,-1073 2108,-1073 2109,-1074 2110,-1075 2111,-1075 2112,-1076 2113,-1077 2113,-1078 2114,-1079 2114,-1080 2114,-1081 2114,-1082 2114,-1083 2114,-1084 2114,-1085 2113,-1086 2113,-1087 2112,-1088 2111,-1089 2110,-1090 2109,-1090 2108,-1091 2107,-1091 2106,-1091 2105,-1091 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2105,-1091 2106,-1091 2107,-1091 2108,-1091 2109,-1092 2110,-1093 2111,-1093 2112,-1094 2113,-1095 2113,-1096 2114,-1097 2114,-1098 2114,-1099 2114,-1100 2114,-1101 2114,-1102 2114,-1103 2113,-1104 2113,-1105 2112,-1106 2111,-1107 2110,-1108 2109,-1108 2108,-1109 2107,-1109 2106,-1109 2105,-1109 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2186,-1100 2187,-1100 2188,-1100 2189,-1100 2190,-1101 2191,-1102 2192,-1102 2193,-1103 2194,-1104 2194,-1105 2195,-1106 2195,-1107 2195,-1108 2195,-1109 2195,-1110 2195,-1111 2195,-1112 2194,-1113 2194,-1114 2193,-1115 2192,-1116 2191,-1117 2190,-1117 2189,-1118 2188,-1118 2187,-1118 2186,-1118 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2186,-1118 2187,-1118 2188,-1118 2189,-1118 2190,-1119 2191,-1120 2192,-1120 2193,-1121 2194,-1122 2194,-1123 2195,-1124 2195,-1125 2195,-1126 2195,-1127 2195,-1128 2195,-1129 2195,-1130 2194,-1131 2194,-1132 2193,-1133 2192,-1134 2191,-1135 2190,-1135 2189,-1136 2188,-1136 2187,-1136 2186,-1136 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2302,-1102 2303,-1102 2304,-1102 2305,-1102 2307,-1103 2308,-1103 2308,-1104 2309,-1105 2310,-1106 2311,-1107 2311,-1108 2312,-1109 2312,-1110 2312,-1111 2312,-1112 2312,-1113 2311,-1114 2311,-1115 2310,-1116 2309,-1117 2308,-1118 2308,-1119 2307,-1119 2305,-1120 2304,-1120 2303,-1120 2302,-1120 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2302,-1120 2303,-1120 2304,-1120 2305,-1120 2307,-1121 2308,-1121 2308,-1122 2309,-1123 2310,-1124 2311,-1125 2311,-1126 2312,-1127 2312,-1128 2312,-1129 2312,-1130 2312,-1131 2311,-1132 2311,-1133 2310,-1134 2309,-1135 2308,-1136 2308,-1137 2307,-1137 2305,-1138 2304,-1138 2303,-1138 2302,-1138 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1852,-1245 1852,-1244 1852,-1243 1853,-1242 1853,-1241 1854,-1240 1855,-1239 1856,-1238 1857,-1238 1858,-1237 1859,-1237 1860,-1237 1861,-1237 1862,-1237 1863,-1237 1864,-1237 1865,-1238 1866,-1238 1867,-1239 1868,-1240 1869,-1241 1869,-1242 1870,-1243 1870,-1244 1870,-1245 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1834,-1245 1834,-1244 1834,-1243 1835,-1242 1835,-1241 1836,-1240 1837,-1239 1838,-1238 1839,-1238 1840,-1237 1841,-1237 1842,-1237 1843,-1237 1844,-1237 1845,-1237 1846,-1237 1847,-1238 1848,-1238 1849,-1239 1850,-1240 1851,-1241 1851,-1242 1852,-1243 1852,-1244 1852,-1245 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1639,-902 1640,-902 1641,-902 1643,-902 1644,-902 1645,-903 1646,-903 1647,-904 1648,-905 1648,-906 1649,-907 1649,-908 1650,-909 1650,-910 1650,-912 1650,-913 1649,-914 1649,-915 1648,-916 1648,-917 1647,-918 1646,-919 1645,-919 1644,-920 1643,-920 1641,-920 1640,-920 1639,-920 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1639,-920 1640,-920 1641,-920 1643,-920 1644,-920 1645,-921 1646,-921 1647,-922 1648,-923 1648,-924 1649,-925 1649,-926 1650,-927 1650,-928 1650,-930 1650,-931 1649,-932 1649,-933 1648,-934 1648,-935 1647,-936 1646,-937 1645,-937 1644,-938 1643,-938 1641,-938 1640,-938 1639,-938 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1505,-927 1506,-927 1507,-927 1508,-927 1509,-928 1510,-929 1511,-929 1512,-930 1513,-931 1513,-932 1514,-933 1514,-934 1514,-935 1514,-936 1514,-937 1514,-938 1514,-939 1513,-940 1513,-941 1512,-942 1511,-943 1510,-944 1509,-944 1508,-945 1507,-945 1506,-945 1505,-945 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1505,-945 1506,-945 1507,-945 1508,-945 1509,-946 1510,-947 1511,-947 1512,-948 1513,-949 1513,-950 1514,-951 1514,-952 1514,-953 1514,-954 1514,-955 1514,-956 1514,-957 1513,-958 1513,-959 1512,-960 1511,-961 1510,-962 1509,-962 1508,-963 1507,-963 1506,-963 1505,-963 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2112,-898 2112,-897 2112,-896 2112,-895 2113,-894 2114,-893 2114,-892 2115,-891 2116,-890 2117,-890 2118,-889 2119,-889 2120,-889 2121,-889 2122,-889 2123,-889 2124,-889 2125,-890 2126,-890 2127,-891 2128,-892 2129,-893 2129,-894 2130,-895 2130,-896 2130,-897 2130,-898 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2094,-898 2094,-897 2094,-896 2094,-895 2095,-894 2096,-893 2096,-892 2097,-891 2098,-890 2099,-890 2100,-889 2101,-889 2102,-889 2103,-889 2104,-889 2105,-889 2106,-889 2107,-890 2108,-890 2109,-891 2110,-892 2111,-893 2111,-894 2112,-895 2112,-896 2112,-897 2112,-898 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1989,-614 1990,-614 1991,-614 1992,-614 1993,-615 1994,-616 1995,-616 1996,-617 1997,-618 1997,-619 1998,-620 1998,-621 1998,-622 1998,-623 1998,-624 1998,-625 1998,-626 1997,-627 1997,-628 1996,-629 1995,-630 1994,-631 1993,-631 1992,-632 1991,-632 1990,-632 1989,-632 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1989,-632 1990,-632 1991,-632 1992,-632 1993,-633 1994,-634 1995,-634 1996,-635 1997,-636 1997,-637 1998,-638 1998,-639 1998,-640 1998,-641 1998,-642 1998,-643 1998,-644 1997,-645 1997,-646 1996,-647 1995,-648 1994,-649 1993,-649 1992,-650 1991,-650 1990,-650 1989,-650 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1474,-2112 1474,-2111 1474,-2110 1475,-2109 1475,-2108 1476,-2107 1477,-2106 1478,-2105 1479,-2105 1480,-2104 1481,-2104 1482,-2104 1483,-2104 1484,-2104 1485,-2104 1486,-2104 1487,-2105 1488,-2105 1489,-2106 1490,-2107 1491,-2108 1491,-2109 1492,-2110 1492,-2111 1492,-2112 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1456,-2113 1456,-2112 1456,-2111 1456,-2110 1457,-2108 1457,-2107 1458,-2107 1459,-2106 1460,-2105 1461,-2104 1462,-2104 1463,-2103 1464,-2103 1465,-2103 1466,-2103 1467,-2103 1468,-2104 1469,-2104 1470,-2105 1471,-2106 1472,-2107 1473,-2107 1473,-2108 1474,-2110 1474,-2111 1474,-2112 1474,-2113 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1470,-2000 1470,-1999 1470,-1998 1470,-1997 1471,-1996 1472,-1995 1472,-1994 1473,-1993 1474,-1992 1475,-1992 1476,-1991 1477,-1991 1478,-1991 1479,-1991 1480,-1991 1481,-1991 1482,-1991 1483,-1992 1484,-1992 1485,-1993 1486,-1994 1487,-1995 1487,-1996 1488,-1997 1488,-1998 1488,-1999 1488,-2000 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1453,-2000 1453,-1999 1453,-1998 1453,-1997 1454,-1996 1455,-1995 1455,-1994 1456,-1993 1457,-1992 1458,-1992 1459,-1991 1460,-1991 1461,-1991 1462,-1991 1463,-1991 1464,-1991 1465,-1991 1466,-1992 1467,-1992 1468,-1993 1469,-1994 1470,-1995 1470,-1996 1471,-1997 1471,-1998 1471,-1999 1471,-2000 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1472,-1456 1472,-1455 1472,-1454 1472,-1453 1473,-1451 1473,-1450 1474,-1450 1475,-1449 1476,-1448 1477,-1447 1478,-1447 1479,-1446 1480,-1446 1481,-1446 1482,-1446 1483,-1446 1484,-1447 1485,-1447 1486,-1448 1487,-1449 1488,-1450 1489,-1450 1489,-1451 1490,-1453 1490,-1454 1490,-1455 1490,-1456 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1454,-1457 1454,-1456 1454,-1455 1454,-1454 1455,-1452 1455,-1451 1456,-1451 1457,-1450 1458,-1449 1459,-1448 1460,-1448 1461,-1447 1462,-1447 1463,-1447 1464,-1447 1465,-1447 1466,-1448 1467,-1448 1468,-1449 1469,-1450 1470,-1451 1471,-1451 1471,-1452 1472,-1454 1472,-1455 1472,-1456 1472,-1457 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1472,-1546 1472,-1545 1472,-1544 1472,-1542 1472,-1541 1473,-1540 1473,-1539 1474,-1538 1475,-1537 1476,-1537 1477,-1536 1478,-1536 1479,-1535 1480,-1535 1482,-1535 1483,-1535 1484,-1536 1485,-1536 1486,-1537 1487,-1537 1488,-1538 1489,-1539 1489,-1540 1490,-1541 1490,-1542 1490,-1544 1490,-1545 1490,-1546 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1454,-1547 1454,-1546 1454,-1545 1454,-1544 1455,-1543 1456,-1542 1456,-1541 1457,-1540 1458,-1539 1459,-1539 1460,-1538 1461,-1538 1462,-1538 1463,-1538 1464,-1538 1465,-1538 1466,-1538 1467,-1539 1468,-1539 1469,-1540 1470,-1541 1471,-1542 1471,-1543 1472,-1544 1472,-1545 1472,-1546 1472,-1547 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1310,-1368 1310,-1367 1310,-1366 1311,-1365 1311,-1364 1312,-1363 1313,-1362 1314,-1361 1315,-1361 1316,-1360 1317,-1360 1318,-1360 1319,-1360 1320,-1360 1321,-1360 1322,-1360 1323,-1361 1324,-1361 1325,-1362 1326,-1363 1327,-1364 1327,-1365 1328,-1366 1328,-1367 1328,-1368 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1328,-1368 1328,-1367 1328,-1366 1329,-1365 1329,-1364 1330,-1363 1331,-1362 1332,-1361 1333,-1361 1334,-1360 1335,-1360 1336,-1360 1337,-1360 1338,-1360 1339,-1360 1340,-1360 1341,-1361 1342,-1361 1343,-1362 1344,-1363 1345,-1364 1345,-1365 1346,-1366 1346,-1367 1346,-1368 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1470,-1218 1470,-1217 1470,-1216 1470,-1215 1471,-1213 1471,-1212 1472,-1212 1473,-1211 1474,-1210 1475,-1209 1476,-1209 1477,-1208 1478,-1208 1479,-1208 1480,-1208 1481,-1208 1482,-1209 1483,-1209 1484,-1210 1485,-1211 1486,-1212 1487,-1212 1487,-1213 1488,-1215 1488,-1216 1488,-1217 1488,-1218 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1453,-1219 1453,-1218 1453,-1217 1454,-1216 1454,-1215 1455,-1214 1456,-1213 1457,-1212 1458,-1212 1459,-1211 1460,-1211 1461,-1211 1462,-1211 1463,-1211 1464,-1211 1465,-1211 1466,-1212 1467,-1212 1468,-1213 1469,-1214 1470,-1215 1470,-1216 1471,-1217 1471,-1218 1471,-1219 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1314,-1129 1314,-1128 1314,-1127 1314,-1126 1315,-1125 1316,-1124 1316,-1123 1317,-1122 1318,-1121 1319,-1121 1320,-1120 1321,-1120 1322,-1120 1323,-1120 1324,-1120 1325,-1120 1326,-1120 1327,-1121 1328,-1121 1329,-1122 1330,-1123 1331,-1124 1331,-1125 1332,-1126 1332,-1127 1332,-1128 1332,-1129 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1332,-1129 1332,-1128 1332,-1127 1332,-1126 1333,-1125 1334,-1124 1334,-1123 1335,-1122 1336,-1121 1337,-1121 1338,-1120 1339,-1120 1340,-1120 1341,-1120 1342,-1120 1343,-1120 1344,-1120 1345,-1121 1346,-1121 1347,-1122 1348,-1123 1349,-1124 1349,-1125 1350,-1126 1350,-1127 1350,-1128 1350,-1129 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1186,-937 1187,-937 1188,-937 1189,-938 1190,-938 1191,-939 1192,-940 1193,-941 1193,-942 1194,-943 1194,-944 1194,-945 1194,-946 1194,-947 1194,-948 1194,-949 1193,-950 1193,-951 1192,-952 1191,-953 1190,-954 1189,-954 1188,-955 1187,-955 1186,-955 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1186,-955 1187,-955 1188,-955 1189,-956 1190,-956 1191,-957 1192,-958 1193,-959 1193,-960 1194,-961 1194,-962 1194,-963 1194,-964 1194,-965 1194,-966 1194,-967 1193,-968 1193,-969 1192,-970 1191,-971 1190,-972 1189,-972 1188,-973 1187,-973 1186,-973 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1166,-874 1166,-873 1166,-872 1167,-871 1167,-870 1168,-869 1169,-868 1170,-867 1171,-867 1172,-866 1173,-866 1174,-866 1175,-866 1176,-866 1177,-866 1178,-866 1179,-867 1180,-867 1181,-868 1182,-869 1183,-870 1183,-871 1184,-872 1184,-873 1184,-874 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1148,-874 1148,-873 1148,-872 1149,-871 1149,-870 1150,-869 1151,-868 1152,-867 1153,-867 1154,-866 1155,-866 1156,-866 1157,-866 1158,-866 1159,-866 1160,-866 1161,-867 1162,-867 1163,-868 1164,-869 1165,-870 1165,-871 1166,-872 1166,-873 1166,-874 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1188,-807 1188,-806 1188,-805 1189,-804 1189,-803 1190,-802 1191,-801 1192,-800 1193,-800 1194,-799 1195,-799 1196,-799 1197,-799 1198,-799 1199,-799 1200,-799 1201,-800 1202,-800 1203,-801 1204,-802 1205,-803 1205,-804 1206,-805 1206,-806 1206,-807 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1170,-807 1170,-806 1170,-805 1170,-804 1171,-802 1171,-801 1172,-801 1173,-800 1174,-799 1175,-798 1176,-798 1177,-797 1178,-797 1179,-797 1180,-797 1181,-797 1182,-798 1183,-798 1184,-799 1185,-800 1186,-801 1187,-801 1187,-802 1188,-804 1188,-805 1188,-806 1188,-807 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="965,-924 966,-924 967,-924 968,-924 970,-925 971,-925 971,-926 972,-927 973,-928 974,-929 974,-930 975,-931 975,-932 975,-933 975,-934 975,-935 974,-936 974,-937 973,-938 972,-939 971,-940 971,-941 970,-941 968,-942 967,-942 966,-942 965,-942 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="965,-942 966,-942 967,-942 968,-942 970,-943 971,-943 971,-944 972,-945 973,-946 974,-947 974,-948 975,-949 975,-950 975,-951 975,-952 975,-953 974,-954 974,-955 973,-956 972,-957 971,-958 971,-959 970,-959 968,-960 967,-960 966,-960 965,-960 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="854,-921 855,-921 856,-921 857,-921 858,-922 859,-923 860,-923 861,-924 862,-925 862,-926 863,-927 863,-928 863,-929 863,-930 863,-931 863,-932 863,-933 862,-934 862,-935 861,-936 860,-937 859,-938 858,-938 857,-939 856,-939 855,-939 854,-939 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="854,-939 855,-939 856,-939 857,-939 858,-940 859,-941 860,-941 861,-942 862,-943 862,-944 863,-945 863,-946 863,-947 863,-948 863,-949 863,-950 863,-951 862,-952 862,-953 861,-954 860,-955 859,-956 858,-956 857,-957 856,-957 855,-957 854,-957 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="943,-676 943,-675 943,-674 943,-673 944,-672 945,-671 945,-670 946,-669 947,-668 948,-668 949,-667 950,-667 951,-667 952,-667 953,-667 954,-667 955,-667 956,-668 957,-668 958,-669 959,-670 960,-671 960,-672 961,-673 961,-674 961,-675 961,-676 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="961,-676 961,-675 961,-674 962,-673 962,-672 963,-671 964,-670 965,-669 966,-669 967,-668 968,-668 969,-668 970,-668 971,-668 972,-668 973,-668 974,-669 975,-669 976,-670 977,-671 978,-672 978,-673 979,-674 979,-675 979,-676 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1166,-756 1166,-755 1166,-754 1167,-753 1167,-752 1168,-751 1169,-750 1170,-749 1171,-749 1172,-748 1173,-748 1174,-748 1175,-748 1176,-748 1177,-748 1178,-748 1179,-749 1180,-749 1181,-750 1182,-751 1183,-752 1183,-753 1184,-754 1184,-755 1184,-756 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1148,-758 1148,-757 1148,-756 1149,-755 1149,-754 1150,-753 1151,-752 1152,-751 1153,-751 1154,-750 1155,-750 1156,-750 1157,-750 1158,-750 1159,-750 1160,-750 1161,-751 1162,-751 1163,-752 1164,-753 1165,-754 1165,-755 1166,-756 1166,-757 1166,-758 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1165,-646 1165,-645 1165,-644 1166,-643 1166,-642 1167,-641 1168,-640 1169,-639 1170,-639 1171,-638 1172,-638 1173,-638 1174,-638 1175,-638 1176,-638 1177,-638 1178,-639 1179,-639 1180,-640 1181,-641 1182,-642 1182,-643 1183,-644 1183,-645 1183,-646 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1148,-647 1148,-646 1148,-645 1149,-644 1149,-643 1150,-642 1151,-641 1152,-640 1153,-640 1154,-639 1155,-639 1156,-639 1157,-639 1158,-639 1159,-639 1160,-639 1161,-640 1162,-640 1163,-641 1164,-642 1165,-643 1165,-644 1166,-645 1166,-646 1166,-647 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="968,-502 969,-502 970,-502 971,-503 972,-503 973,-504 974,-505 975,-506 975,-507 976,-508 976,-509 976,-510 976,-511 976,-512 976,-513 976,-514 975,-515 975,-516 974,-517 973,-518 972,-519 971,-519 970,-520 969,-520 968,-520 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="968,-520 969,-520 970,-520 971,-521 972,-521 973,-522 974,-523 975,-524 975,-525 976,-526 976,-527 976,-528 976,-529 976,-530 976,-531 976,-532 975,-533 975,-534 974,-535 973,-536 972,-537 971,-537 970,-538 969,-538 968,-538 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="857,-603 857,-602 857,-601 857,-599 857,-598 858,-597 858,-596 859,-595 860,-594 861,-594 862,-593 863,-593 864,-592 865,-592 867,-592 868,-592 869,-593 870,-593 871,-594 872,-594 873,-595 874,-596 874,-597 875,-598 875,-599 875,-601 875,-602 875,-603 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="875,-603 875,-602 875,-601 875,-599 875,-598 876,-597 876,-596 877,-595 878,-594 879,-594 880,-593 881,-593 882,-592 883,-592 885,-592 886,-592 887,-593 888,-593 889,-594 890,-594 891,-595 892,-596 892,-597 893,-598 893,-599 893,-601 893,-602 893,-603 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="946,-374 946,-373 946,-372 947,-371 947,-370 948,-369 949,-368 950,-367 951,-367 952,-366 953,-366 954,-366 955,-366 956,-366 957,-366 958,-366 959,-367 960,-367 961,-368 962,-369 963,-370 963,-371 964,-372 964,-373 964,-374 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="964,-375 964,-374 964,-373 964,-372 965,-371 966,-370 966,-369 967,-368 968,-367 969,-367 970,-366 971,-366 972,-366 973,-366 974,-366 975,-366 976,-366 977,-367 978,-367 979,-368 980,-369 981,-370 981,-371 982,-372 982,-373 982,-374 982,-375 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1164,-565 1164,-564 1164,-563 1165,-562 1165,-561 1166,-560 1167,-559 1168,-558 1169,-558 1170,-557 1171,-557 1172,-557 1173,-557 1174,-557 1175,-557 1176,-557 1177,-558 1178,-558 1179,-559 1180,-560 1181,-561 1181,-562 1182,-563 1182,-564 1182,-565 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1147,-566 1147,-565 1147,-564 1148,-563 1148,-562 1149,-561 1150,-560 1151,-559 1152,-559 1153,-558 1154,-558 1155,-558 1156,-558 1157,-558 1158,-558 1159,-558 1160,-559 1161,-559 1162,-560 1163,-561 1164,-562 1164,-563 1165,-564 1165,-565 1165,-566 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1164,-483 1164,-482 1164,-481 1164,-479 1164,-478 1165,-477 1165,-476 1166,-475 1167,-474 1168,-474 1169,-473 1170,-473 1171,-472 1172,-472 1174,-472 1175,-472 1176,-473 1177,-473 1178,-474 1179,-474 1180,-475 1181,-476 1181,-477 1182,-478 1182,-479 1182,-481 1182,-482 1182,-483 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1147,-484 1147,-483 1147,-482 1147,-481 1148,-480 1149,-479 1149,-478 1150,-477 1151,-476 1152,-476 1153,-475 1154,-475 1155,-475 1156,-475 1157,-475 1158,-475 1159,-475 1160,-476 1161,-476 1162,-477 1163,-478 1164,-479 1164,-480 1165,-481 1165,-482 1165,-483 1165,-484 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1183,-299 1183,-298 1183,-297 1183,-296 1184,-295 1185,-294 1185,-293 1186,-292 1187,-291 1188,-291 1189,-290 1190,-290 1191,-290 1192,-290 1193,-290 1194,-290 1195,-290 1196,-291 1197,-291 1198,-292 1199,-293 1200,-294 1200,-295 1201,-296 1201,-297 1201,-298 1201,-299 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1165,-299 1165,-298 1165,-297 1165,-296 1166,-295 1167,-294 1167,-293 1168,-292 1169,-291 1170,-291 1171,-290 1172,-290 1173,-290 1174,-290 1175,-290 1176,-290 1177,-290 1178,-291 1179,-291 1180,-292 1181,-293 1182,-294 1182,-295 1183,-296 1183,-297 1183,-298 1183,-299 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1440,-679 1441,-679 1442,-679 1443,-679 1445,-680 1446,-680 1446,-681 1447,-682 1448,-683 1449,-684 1449,-685 1450,-686 1450,-687 1450,-688 1450,-689 1450,-690 1449,-691 1449,-692 1448,-693 1447,-694 1446,-695 1446,-696 1445,-696 1443,-697 1442,-697 1441,-697 1440,-697 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1440,-697 1441,-697 1442,-697 1443,-697 1445,-698 1446,-698 1446,-699 1447,-700 1448,-701 1449,-702 1449,-703 1450,-704 1450,-705 1450,-706 1450,-707 1450,-708 1449,-709 1449,-710 1448,-711 1447,-712 1446,-713 1446,-714 1445,-714 1443,-715 1442,-715 1441,-715 1440,-715 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1523,-768 1523,-767 1523,-766 1523,-765 1524,-764 1525,-763 1525,-762 1526,-761 1527,-760 1528,-760 1529,-759 1530,-759 1531,-759 1532,-759 1533,-759 1534,-759 1535,-759 1536,-760 1537,-760 1538,-761 1539,-762 1540,-763 1540,-764 1541,-765 1541,-766 1541,-767 1541,-768 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1505,-767 1505,-766 1505,-765 1505,-764 1506,-763 1507,-762 1507,-761 1508,-760 1509,-759 1510,-759 1511,-758 1512,-758 1513,-758 1514,-758 1515,-758 1516,-758 1517,-758 1518,-759 1519,-759 1520,-760 1521,-761 1522,-762 1522,-763 1523,-764 1523,-765 1523,-766 1523,-767 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1472,-447 1472,-446 1472,-445 1473,-444 1473,-443 1474,-442 1475,-441 1476,-440 1477,-440 1478,-439 1479,-439 1480,-439 1481,-439 1482,-439 1483,-439 1484,-439 1485,-440 1486,-440 1487,-441 1488,-442 1489,-443 1489,-444 1490,-445 1490,-446 1490,-447 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1454,-449 1454,-448 1454,-447 1454,-446 1455,-444 1455,-443 1456,-443 1457,-442 1458,-441 1459,-440 1460,-440 1461,-439 1462,-439 1463,-439 1464,-439 1465,-439 1466,-440 1467,-440 1468,-441 1469,-442 1470,-443 1471,-443 1471,-444 1472,-446 1472,-447 1472,-448 1472,-449 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1471,-287 1471,-286 1471,-285 1472,-284 1472,-283 1473,-282 1474,-281 1475,-280 1476,-280 1477,-279 1478,-279 1479,-279 1480,-279 1481,-279 1482,-279 1483,-279 1484,-280 1485,-280 1486,-281 1487,-282 1488,-283 1488,-284 1489,-285 1489,-286 1489,-287 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1454,-289 1454,-288 1454,-287 1454,-286 1455,-285 1456,-284 1456,-283 1457,-282 1458,-281 1459,-281 1460,-280 1461,-280 1462,-280 1463,-280 1464,-280 1465,-280 1466,-280 1467,-281 1468,-281 1469,-282 1470,-283 1471,-284 1471,-285 1472,-286 1472,-287 1472,-288 1472,-289 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1568,-289 1569,-289 1570,-289 1571,-289 1573,-290 1574,-290 1574,-291 1575,-292 1576,-293 1577,-294 1577,-295 1578,-296 1578,-297 1578,-298 1578,-299 1578,-300 1577,-301 1577,-302 1576,-303 1575,-304 1574,-305 1574,-306 1573,-306 1571,-307 1570,-307 1569,-307 1568,-307 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1568,-307 1569,-307 1570,-307 1571,-307 1573,-308 1574,-308 1574,-309 1575,-310 1576,-311 1577,-312 1577,-313 1578,-314 1578,-315 1578,-316 1578,-317 1578,-318 1577,-319 1577,-320 1576,-321 1575,-322 1574,-323 1574,-324 1573,-324 1571,-325 1570,-325 1569,-325 1568,-325 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1641,-175 1642,-175 1643,-175 1644,-176 1645,-176 1646,-177 1647,-178 1648,-179 1648,-180 1649,-181 1649,-182 1649,-183 1649,-184 1649,-185 1649,-186 1649,-187 1648,-188 1648,-189 1647,-190 1646,-191 1645,-192 1644,-192 1643,-193 1642,-193 1641,-193 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1641,-193 1642,-193 1643,-193 1644,-194 1645,-194 1646,-195 1647,-196 1648,-197 1648,-198 1649,-199 1649,-200 1649,-201 1649,-202 1649,-203 1649,-204 1649,-205 1648,-206 1648,-207 1647,-208 1646,-209 1645,-210 1644,-210 1643,-211 1642,-211 1641,-211 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1687,-291 1688,-291 1689,-291 1690,-292 1691,-292 1692,-293 1693,-294 1694,-295 1694,-296 1695,-297 1695,-298 1695,-299 1695,-300 1695,-301 1695,-302 1695,-303 1694,-304 1694,-305 1693,-306 1692,-307 1691,-308 1690,-308 1689,-309 1688,-309 1687,-309 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1687,-309 1688,-309 1689,-309 1690,-310 1691,-310 1692,-311 1693,-312 1694,-313 1694,-314 1695,-315 1695,-316 1695,-317 1695,-318 1695,-319 1695,-320 1695,-321 1694,-322 1694,-323 1693,-324 1692,-325 1691,-326 1690,-326 1689,-327 1688,-327 1687,-327 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1401,-202 1402,-202 1403,-202 1404,-202 1405,-203 1406,-204 1407,-204 1408,-205 1409,-206 1409,-207 1410,-208 1410,-209 1410,-210 1410,-211 1410,-212 1410,-213 1410,-214 1409,-215 1409,-216 1408,-217 1407,-218 1406,-219 1405,-219 1404,-220 1403,-220 1402,-220 1401,-220 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1401,-220 1402,-220 1403,-220 1404,-220 1405,-221 1406,-222 1407,-222 1408,-223 1409,-224 1409,-225 1410,-226 1410,-227 1410,-228 1410,-229 1410,-230 1410,-231 1410,-232 1409,-233 1409,-234 1408,-235 1407,-236 1406,-237 1405,-237 1404,-238 1403,-238 1402,-238 1401,-238 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="925,-1068 925,-1067 925,-1066 926,-1065 926,-1064 927,-1063 928,-1062 929,-1061 930,-1061 931,-1060 932,-1060 933,-1060 934,-1060 935,-1060 936,-1060 937,-1060 938,-1061 939,-1061 940,-1062 941,-1063 942,-1064 942,-1065 943,-1066 943,-1067 943,-1068 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="907,-1070 907,-1069 907,-1068 907,-1067 908,-1066 909,-1065 909,-1064 910,-1063 911,-1062 912,-1062 913,-1061 914,-1061 915,-1061 916,-1061 917,-1061 918,-1061 919,-1061 920,-1062 921,-1062 922,-1063 923,-1064 924,-1065 924,-1066 925,-1067 925,-1068 925,-1069 925,-1070 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="975,-1132 975,-1131 975,-1130 975,-1129 976,-1128 977,-1127 977,-1126 978,-1125 979,-1124 980,-1124 981,-1123 982,-1123 983,-1123 984,-1123 985,-1123 986,-1123 987,-1123 988,-1124 989,-1124 990,-1125 991,-1126 992,-1127 992,-1128 993,-1129 993,-1130 993,-1131 993,-1132 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="957,-1131 957,-1130 957,-1129 958,-1128 958,-1127 959,-1126 960,-1125 961,-1124 962,-1124 963,-1123 964,-1123 965,-1123 966,-1123 967,-1123 968,-1123 969,-1123 970,-1124 971,-1124 972,-1125 973,-1126 974,-1127 974,-1128 975,-1129 975,-1130 975,-1131 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1200,-1193 1200,-1192 1200,-1191 1201,-1190 1201,-1189 1202,-1188 1203,-1187 1204,-1186 1205,-1186 1206,-1185 1207,-1185 1208,-1185 1209,-1185 1210,-1185 1211,-1185 1212,-1185 1213,-1186 1214,-1186 1215,-1187 1216,-1188 1217,-1189 1217,-1190 1218,-1191 1218,-1192 1218,-1193 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1181,-1194 1181,-1193 1181,-1192 1181,-1191 1182,-1190 1183,-1189 1183,-1188 1184,-1187 1185,-1186 1186,-1186 1187,-1185 1188,-1185 1189,-1185 1190,-1185 1191,-1185 1192,-1185 1193,-1185 1194,-1186 1195,-1186 1196,-1187 1197,-1188 1198,-1189 1198,-1190 1199,-1191 1199,-1192 1199,-1193 1199,-1194 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="933,-1246 934,-1246 935,-1246 936,-1246 938,-1247 939,-1247 939,-1248 940,-1249 941,-1250 942,-1251 942,-1252 943,-1253 943,-1254 943,-1255 943,-1256 943,-1257 942,-1258 942,-1259 941,-1260 940,-1261 939,-1262 939,-1263 938,-1263 936,-1264 935,-1264 934,-1264 933,-1264 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="933,-1229 934,-1229 935,-1229 936,-1230 937,-1230 938,-1231 939,-1232 940,-1233 940,-1234 941,-1235 941,-1236 941,-1237 941,-1238 941,-1239 941,-1240 941,-1241 940,-1242 940,-1243 939,-1244 938,-1245 937,-1246 936,-1246 935,-1247 934,-1247 933,-1247 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="853,-1350 854,-1350 855,-1350 856,-1350 857,-1351 858,-1352 859,-1352 860,-1353 861,-1354 861,-1355 862,-1356 862,-1357 862,-1358 862,-1359 862,-1360 862,-1361 862,-1362 861,-1363 861,-1364 860,-1365 859,-1366 858,-1367 857,-1367 856,-1368 855,-1368 854,-1368 853,-1368 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="855,-1331 856,-1331 857,-1331 858,-1331 860,-1332 861,-1332 861,-1333 862,-1334 863,-1335 864,-1336 864,-1337 865,-1338 865,-1339 865,-1340 865,-1341 865,-1342 864,-1343 864,-1344 863,-1345 862,-1346 861,-1347 861,-1348 860,-1348 858,-1349 857,-1349 856,-1349 855,-1349 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1072,-1611 1072,-1610 1072,-1609 1072,-1608 1073,-1606 1073,-1605 1074,-1605 1075,-1604 1076,-1603 1077,-1602 1078,-1602 1079,-1601 1080,-1601 1081,-1601 1082,-1601 1083,-1601 1084,-1602 1085,-1602 1086,-1603 1087,-1604 1088,-1605 1089,-1605 1089,-1606 1090,-1608 1090,-1609 1090,-1610 1090,-1611 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1090,-1611 1090,-1610 1090,-1609 1091,-1608 1091,-1607 1092,-1606 1093,-1605 1094,-1604 1095,-1604 1096,-1603 1097,-1603 1098,-1603 1099,-1603 1100,-1603 1101,-1603 1102,-1603 1103,-1604 1104,-1604 1105,-1605 1106,-1606 1107,-1607 1107,-1608 1108,-1609 1108,-1610 1108,-1611 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="928,-1714 928,-1713 928,-1712 929,-1711 929,-1710 930,-1709 931,-1708 932,-1707 933,-1707 934,-1706 935,-1706 936,-1706 937,-1706 938,-1706 939,-1706 940,-1706 941,-1707 942,-1707 943,-1708 944,-1709 945,-1710 945,-1711 946,-1712 946,-1713 946,-1714 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="946,-1714 946,-1713 946,-1712 947,-1711 947,-1710 948,-1709 949,-1708 950,-1707 951,-1707 952,-1706 953,-1706 954,-1706 955,-1706 956,-1706 957,-1706 958,-1706 959,-1707 960,-1707 961,-1708 962,-1709 963,-1710 963,-1711 964,-1712 964,-1713 964,-1714 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1117,-1546 1117,-1545 1117,-1544 1118,-1543 1118,-1542 1119,-1541 1120,-1540 1121,-1539 1122,-1539 1123,-1538 1124,-1538 1125,-1538 1126,-1538 1127,-1538 1128,-1538 1129,-1538 1130,-1539 1131,-1539 1132,-1540 1133,-1541 1134,-1542 1134,-1543 1135,-1544 1135,-1545 1135,-1546 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1134,-1547 1134,-1546 1134,-1545 1134,-1543 1134,-1542 1135,-1541 1135,-1540 1136,-1539 1137,-1538 1138,-1538 1139,-1537 1140,-1537 1141,-1536 1142,-1536 1144,-1536 1145,-1536 1146,-1537 1147,-1537 1148,-1538 1149,-1538 1150,-1539 1151,-1540 1151,-1541 1152,-1542 1152,-1543 1152,-1545 1152,-1546 1152,-1547 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1117,-1463 1117,-1462 1117,-1461 1118,-1460 1118,-1459 1119,-1458 1120,-1457 1121,-1456 1122,-1456 1123,-1455 1124,-1455 1125,-1455 1126,-1455 1127,-1455 1128,-1455 1129,-1455 1130,-1456 1131,-1456 1132,-1457 1133,-1458 1134,-1459 1134,-1460 1135,-1461 1135,-1462 1135,-1463 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1134,-1462 1134,-1461 1134,-1460 1134,-1459 1135,-1457 1135,-1456 1136,-1456 1137,-1455 1138,-1454 1139,-1453 1140,-1453 1141,-1452 1142,-1452 1143,-1452 1144,-1452 1145,-1452 1146,-1453 1147,-1453 1148,-1454 1149,-1455 1150,-1456 1151,-1456 1151,-1457 1152,-1459 1152,-1460 1152,-1461 1152,-1462 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="989,-1823 990,-1823 991,-1823 993,-1823 994,-1823 995,-1824 996,-1824 997,-1825 998,-1826 998,-1827 999,-1828 999,-1829 1000,-1830 1000,-1831 1000,-1833 1000,-1834 999,-1835 999,-1836 998,-1837 998,-1838 997,-1839 996,-1840 995,-1840 994,-1841 993,-1841 991,-1841 990,-1841 989,-1841 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="990,-1806 991,-1806 992,-1806 993,-1806 994,-1807 995,-1808 996,-1808 997,-1809 998,-1810 998,-1811 999,-1812 999,-1813 999,-1814 999,-1815 999,-1816 999,-1817 999,-1818 998,-1819 998,-1820 997,-1821 996,-1822 995,-1823 994,-1823 993,-1824 992,-1824 991,-1824 990,-1824 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2688,-1111 2689,-1111 2690,-1111 2691,-1112 2692,-1112 2693,-1113 2694,-1114 2695,-1115 2695,-1116 2696,-1117 2696,-1118 2696,-1119 2696,-1120 2696,-1121 2696,-1122 2696,-1123 2695,-1124 2695,-1125 2694,-1126 2693,-1127 2692,-1128 2691,-1128 2690,-1129 2689,-1129 2688,-1129 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2688,-1129 2689,-1129 2690,-1129 2691,-1130 2692,-1130 2693,-1131 2694,-1132 2695,-1133 2695,-1134 2696,-1135 2696,-1136 2696,-1137 2696,-1138 2696,-1139 2696,-1140 2696,-1141 2695,-1142 2695,-1143 2694,-1144 2693,-1145 2692,-1146 2691,-1146 2690,-1147 2689,-1147 2688,-1147 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2797,-1093 2798,-1093 2799,-1093 2800,-1094 2801,-1094 2802,-1095 2803,-1096 2804,-1097 2804,-1098 2805,-1099 2805,-1100 2805,-1101 2805,-1102 2805,-1103 2805,-1104 2805,-1105 2804,-1106 2804,-1107 2803,-1108 2802,-1109 2801,-1110 2800,-1110 2799,-1111 2798,-1111 2797,-1111 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2797,-1111 2798,-1111 2799,-1111 2800,-1112 2801,-1112 2802,-1113 2803,-1114 2804,-1115 2804,-1116 2805,-1117 2805,-1118 2805,-1119 2805,-1120 2805,-1121 2805,-1122 2805,-1123 2804,-1124 2804,-1125 2803,-1126 2802,-1127 2801,-1128 2800,-1128 2799,-1129 2798,-1129 2797,-1129 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2817,-124 2818,-124 2819,-124 2820,-124 2821,-125 2822,-126 2823,-126 2824,-127 2825,-128 2825,-129 2826,-130 2826,-131 2826,-132 2826,-133 2826,-134 2826,-135 2826,-136 2825,-137 2825,-138 2824,-139 2823,-140 2822,-141 2821,-141 2820,-142 2819,-142 2818,-142 2817,-142 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2817,-142 2818,-142 2819,-142 2820,-142 2821,-143 2822,-144 2823,-144 2824,-145 2825,-146 2825,-147 2826,-148 2826,-149 2826,-150 2826,-151 2826,-152 2826,-153 2826,-154 2825,-155 2825,-156 2824,-157 2823,-158 2822,-159 2821,-159 2820,-160 2819,-160 2818,-160 2817,-160 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2908,-114 2909,-114 2910,-114 2911,-115 2912,-115 2913,-116 2914,-117 2915,-118 2915,-119 2916,-120 2916,-121 2916,-122 2916,-123 2916,-124 2916,-125 2916,-126 2915,-127 2915,-128 2914,-129 2913,-130 2912,-131 2911,-131 2910,-132 2909,-132 2908,-132 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2908,-132 2909,-132 2910,-132 2911,-133 2912,-133 2913,-134 2914,-135 2915,-136 2915,-137 2916,-138 2916,-139 2916,-140 2916,-141 2916,-142 2916,-143 2916,-144 2915,-145 2915,-146 2914,-147 2913,-148 2912,-149 2911,-149 2910,-150 2909,-150 2908,-150 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="503,-1626 503,-1625 503,-1624 504,-1623 504,-1622 505,-1621 506,-1620 507,-1619 508,-1619 509,-1618 510,-1618 511,-1618 512,-1618 513,-1618 514,-1618 515,-1618 516,-1619 517,-1619 518,-1620 519,-1621 520,-1622 520,-1623 521,-1624 521,-1625 521,-1626 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="521,-1627 521,-1626 521,-1625 521,-1624 522,-1623 523,-1622 523,-1621 524,-1620 525,-1619 526,-1619 527,-1618 528,-1618 529,-1618 530,-1618 531,-1618 532,-1618 533,-1618 534,-1619 535,-1619 536,-1620 537,-1621 538,-1622 538,-1623 539,-1624 539,-1625 539,-1626 539,-1627 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="731,-1579 731,-1578 731,-1577 731,-1576 732,-1575 733,-1574 733,-1573 734,-1572 735,-1571 736,-1571 737,-1570 738,-1570 739,-1570 740,-1570 741,-1570 742,-1570 743,-1570 744,-1571 745,-1571 746,-1572 747,-1573 748,-1574 748,-1575 749,-1576 749,-1577 749,-1578 749,-1579 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="713,-1581 713,-1580 713,-1579 713,-1578 714,-1577 715,-1576 715,-1575 716,-1574 717,-1573 718,-1573 719,-1572 720,-1572 721,-1572 722,-1572 723,-1572 724,-1572 725,-1572 726,-1573 727,-1573 728,-1574 729,-1575 730,-1576 730,-1577 731,-1578 731,-1579 731,-1580 731,-1581 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="720,-1441 720,-1440 720,-1439 721,-1438 721,-1437 722,-1436 723,-1435 724,-1434 725,-1434 726,-1433 727,-1433 728,-1433 729,-1433 730,-1433 731,-1433 732,-1433 733,-1434 734,-1434 735,-1435 736,-1436 737,-1437 737,-1438 738,-1439 738,-1440 738,-1441 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="702,-1442 702,-1441 702,-1440 702,-1439 703,-1437 703,-1436 704,-1436 705,-1435 706,-1434 707,-1433 708,-1433 709,-1432 710,-1432 711,-1432 712,-1432 713,-1432 714,-1433 715,-1433 716,-1434 717,-1435 718,-1436 719,-1436 719,-1437 720,-1439 720,-1440 720,-1441 720,-1442 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="727,-1333 727,-1334 727,-1335 727,-1336 726,-1338 726,-1339 725,-1339 724,-1340 723,-1341 722,-1342 721,-1342 720,-1343 719,-1343 718,-1343 717,-1343 716,-1343 715,-1342 714,-1342 713,-1341 712,-1340 711,-1339 710,-1339 710,-1338 709,-1336 709,-1335 709,-1334 709,-1333 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="691,-1334 691,-1333 691,-1332 691,-1331 692,-1330 693,-1329 693,-1328 694,-1327 695,-1326 696,-1326 697,-1325 698,-1325 699,-1325 700,-1325 701,-1325 702,-1325 703,-1325 704,-1326 705,-1326 706,-1327 707,-1328 708,-1329 708,-1330 709,-1331 709,-1332 709,-1333 709,-1334 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="734,-1203 734,-1204 734,-1205 733,-1206 733,-1207 732,-1208 731,-1209 730,-1210 729,-1210 728,-1211 727,-1211 726,-1211 725,-1211 724,-1211 723,-1211 722,-1211 721,-1210 720,-1210 719,-1209 718,-1208 717,-1207 717,-1206 716,-1205 716,-1204 716,-1203 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="699,-1204 699,-1203 699,-1202 699,-1201 700,-1200 701,-1199 701,-1198 702,-1197 703,-1196 704,-1196 705,-1195 706,-1195 707,-1195 708,-1195 709,-1195 710,-1195 711,-1195 712,-1196 713,-1196 714,-1197 715,-1198 716,-1199 716,-1200 717,-1201 717,-1202 717,-1203 717,-1204 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="737,-994 737,-995 737,-996 736,-997 736,-998 735,-999 734,-1000 733,-1001 732,-1001 731,-1002 730,-1002 729,-1002 728,-1002 727,-1002 726,-1002 725,-1002 724,-1001 723,-1001 722,-1000 721,-999 720,-998 720,-997 719,-996 719,-995 719,-994 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="701,-995 701,-994 701,-993 701,-992 702,-991 703,-990 703,-989 704,-988 705,-987 706,-987 707,-986 708,-986 709,-986 710,-986 711,-986 712,-986 713,-986 714,-987 715,-987 716,-988 717,-989 718,-990 718,-991 719,-992 719,-993 719,-994 719,-995 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="740,-882 740,-883 740,-884 739,-885 739,-886 738,-887 737,-888 736,-889 735,-889 734,-890 733,-890 732,-890 731,-890 730,-890 729,-890 728,-890 727,-889 726,-889 725,-888 724,-887 723,-886 723,-885 722,-884 722,-883 722,-882 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="705,-884 705,-883 705,-882 705,-881 706,-880 707,-879 707,-878 708,-877 709,-876 710,-876 711,-875 712,-875 713,-875 714,-875 715,-875 716,-875 717,-875 718,-876 719,-876 720,-877 721,-878 722,-879 722,-880 723,-881 723,-882 723,-883 723,-884 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="717,-721 717,-720 717,-719 718,-718 718,-717 719,-716 720,-715 721,-714 722,-714 723,-713 724,-713 725,-713 726,-713 727,-713 728,-713 729,-713 730,-714 731,-714 732,-715 733,-716 734,-717 734,-718 735,-719 735,-720 735,-721 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="716,-722 716,-723 716,-724 715,-725 715,-726 714,-727 713,-728 712,-729 711,-729 710,-730 709,-730 708,-730 707,-730 706,-730 705,-730 704,-730 703,-729 702,-729 701,-728 700,-727 699,-726 699,-725 698,-724 698,-723 698,-722 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="649,-361 649,-360 649,-359 649,-358 650,-357 651,-356 651,-355 652,-354 653,-353 654,-353 655,-352 656,-352 657,-352 658,-352 659,-352 660,-352 661,-352 662,-353 663,-353 664,-354 665,-355 666,-356 666,-357 667,-358 667,-359 667,-360 667,-361 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="631,-360 631,-359 631,-358 631,-357 632,-355 632,-354 633,-354 634,-353 635,-352 636,-351 637,-351 638,-350 639,-350 640,-350 641,-350 642,-350 643,-351 644,-351 645,-352 646,-353 647,-354 648,-354 648,-355 649,-357 649,-358 649,-359 649,-360 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="523,-415 524,-415 525,-415 527,-415 528,-415 529,-416 530,-416 531,-417 532,-418 532,-419 533,-420 533,-421 534,-422 534,-423 534,-425 534,-426 533,-427 533,-428 532,-429 532,-430 531,-431 530,-432 529,-432 528,-433 527,-433 525,-433 524,-433 523,-433 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="524,-434 525,-434 526,-434 527,-435 528,-435 529,-436 530,-437 531,-438 531,-439 532,-440 532,-441 532,-442 532,-443 532,-444 532,-445 532,-446 531,-447 531,-448 530,-449 529,-450 528,-451 527,-451 526,-452 525,-452 524,-452 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="511,-545 511,-544 511,-543 511,-542 512,-541 513,-540 513,-539 514,-538 515,-537 516,-537 517,-536 518,-536 519,-536 520,-536 521,-536 522,-536 523,-536 524,-537 525,-537 526,-538 527,-539 528,-540 528,-541 529,-542 529,-543 529,-544 529,-545 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="529,-545 529,-544 529,-543 529,-542 530,-541 531,-540 531,-539 532,-538 533,-537 534,-537 535,-536 536,-536 537,-536 538,-536 539,-536 540,-536 541,-536 542,-537 543,-537 544,-538 545,-539 546,-540 546,-541 547,-542 547,-543 547,-544 547,-545 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="407,-971 408,-971 409,-971 410,-971 411,-972 412,-973 413,-973 414,-974 415,-975 415,-976 416,-977 416,-978 416,-979 416,-980 416,-981 416,-982 416,-983 415,-984 415,-985 414,-986 413,-987 412,-988 411,-988 410,-989 409,-989 408,-989 407,-989 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="407,-989 408,-989 409,-989 410,-989 411,-990 412,-991 413,-991 414,-992 415,-993 415,-994 416,-995 416,-996 416,-997 416,-998 416,-999 416,-1000 416,-1001 415,-1002 415,-1003 414,-1004 413,-1005 412,-1006 411,-1006 410,-1007 409,-1007 408,-1007 407,-1007 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="396,-625 396,-624 396,-623 396,-622 397,-620 397,-619 398,-619 399,-618 400,-617 401,-616 402,-616 403,-615 404,-615 405,-615 406,-615 407,-615 408,-616 409,-616 410,-617 411,-618 412,-619 413,-619 413,-620 414,-622 414,-623 414,-624 414,-625 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="397,-624 397,-625 397,-626 396,-627 396,-628 395,-629 394,-630 393,-631 392,-631 391,-632 390,-632 389,-632 388,-632 387,-632 386,-632 385,-632 384,-631 383,-631 382,-630 381,-629 380,-628 380,-627 379,-626 379,-625 379,-624 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="433,-813 433,-814 433,-815 432,-816 432,-817 431,-818 430,-819 429,-820 428,-820 427,-821 426,-821 425,-821 424,-821 423,-821 422,-821 421,-821 420,-820 419,-820 418,-819 417,-818 416,-817 416,-816 415,-815 415,-814 415,-813 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="397,-813 397,-812 397,-811 397,-810 398,-808 398,-807 399,-807 400,-806 401,-805 402,-804 403,-804 404,-803 405,-803 406,-803 407,-803 408,-803 409,-804 410,-804 411,-805 412,-806 413,-807 414,-807 414,-808 415,-810 415,-811 415,-812 415,-813 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="215,-714 215,-713 215,-712 216,-711 216,-710 217,-709 218,-708 219,-707 220,-707 221,-706 222,-706 223,-706 224,-706 225,-706 226,-706 227,-706 228,-707 229,-707 230,-708 231,-709 232,-710 232,-711 233,-712 233,-713 233,-714 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="251,-714 251,-715 251,-716 251,-718 251,-719 250,-720 250,-721 249,-722 248,-723 247,-723 246,-724 245,-724 244,-725 243,-725 241,-725 240,-725 239,-724 238,-724 237,-723 236,-723 235,-722 234,-721 234,-720 233,-719 233,-718 233,-716 233,-715 233,-714 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="179,-535 179,-534 179,-533 180,-532 180,-531 181,-530 182,-529 183,-528 184,-528 185,-527 186,-527 187,-527 188,-527 189,-527 190,-527 191,-527 192,-528 193,-528 194,-529 195,-530 196,-531 196,-532 197,-533 197,-534 197,-535 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="198,-535 198,-534 198,-533 199,-532 199,-531 200,-530 201,-529 202,-528 203,-528 204,-527 205,-527 206,-527 207,-527 208,-527 209,-527 210,-527 211,-528 212,-528 213,-529 214,-530 215,-531 215,-532 216,-533 216,-534 216,-535 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="308,-483 309,-483 310,-483 311,-484 312,-484 313,-485 314,-486 315,-487 315,-488 316,-489 316,-490 316,-491 316,-492 316,-493 316,-494 316,-495 315,-496 315,-497 314,-498 313,-499 312,-500 311,-500 310,-501 309,-501 308,-501 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="308,-501 309,-501 310,-501 311,-502 312,-502 313,-503 314,-504 315,-505 315,-506 316,-507 316,-508 316,-509 316,-510 316,-511 316,-512 316,-513 315,-514 315,-515 314,-516 313,-517 312,-518 311,-518 310,-519 309,-519 308,-519 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="310,-1253 311,-1253 312,-1253 313,-1253 315,-1254 316,-1254 316,-1255 317,-1256 318,-1257 319,-1258 319,-1259 320,-1260 320,-1261 320,-1262 320,-1263 320,-1264 319,-1265 319,-1266 318,-1267 317,-1268 316,-1269 316,-1270 315,-1270 313,-1271 312,-1271 311,-1271 310,-1271 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="309,-1235 310,-1235 311,-1235 312,-1236 313,-1236 314,-1237 315,-1238 316,-1239 316,-1240 317,-1241 317,-1242 317,-1243 317,-1244 317,-1245 317,-1246 317,-1247 316,-1248 316,-1249 315,-1250 314,-1251 313,-1252 312,-1252 311,-1253 310,-1253 309,-1253 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="167,-1286 168,-1286 169,-1286 170,-1287 171,-1287 172,-1288 173,-1289 174,-1290 174,-1291 175,-1292 175,-1293 175,-1294 175,-1295 175,-1296 175,-1297 175,-1298 174,-1299 174,-1300 173,-1301 172,-1302 171,-1303 170,-1303 169,-1304 168,-1304 167,-1304 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="167,-1304 168,-1304 169,-1304 170,-1305 171,-1305 172,-1306 173,-1307 174,-1308 174,-1309 175,-1310 175,-1311 175,-1312 175,-1313 175,-1314 175,-1315 175,-1316 174,-1317 174,-1318 173,-1319 172,-1320 171,-1321 170,-1321 169,-1322 168,-1322 167,-1322 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="247,-1316 248,-1316 249,-1316 250,-1317 251,-1317 252,-1318 253,-1319 254,-1320 254,-1321 255,-1322 255,-1323 255,-1324 255,-1325 255,-1326 255,-1327 255,-1328 254,-1329 254,-1330 253,-1331 252,-1332 251,-1333 250,-1333 249,-1334 248,-1334 247,-1334 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="247,-1334 248,-1334 249,-1334 250,-1335 251,-1335 252,-1336 253,-1337 254,-1338 254,-1339 255,-1340 255,-1341 255,-1342 255,-1343 255,-1344 255,-1345 255,-1346 254,-1347 254,-1348 253,-1349 252,-1350 251,-1351 250,-1351 249,-1352 248,-1352 247,-1352 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="96,-1348 96,-1349 96,-1350 95,-1351 95,-1352 94,-1353 93,-1354 92,-1355 91,-1355 90,-1356 89,-1356 88,-1356 87,-1356 86,-1356 85,-1356 84,-1356 83,-1355 82,-1355 81,-1354 80,-1353 79,-1352 79,-1351 78,-1350 78,-1349 78,-1348 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="95,-1348 95,-1347 95,-1346 95,-1345 96,-1343 96,-1342 97,-1342 98,-1341 99,-1340 100,-1339 101,-1339 102,-1338 103,-1338 104,-1338 105,-1338 106,-1338 107,-1339 108,-1339 109,-1340 110,-1341 111,-1342 112,-1342 112,-1343 113,-1345 113,-1346 113,-1347 113,-1348 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="117,-1459 118,-1459 119,-1459 120,-1460 121,-1460 122,-1461 123,-1462 124,-1463 124,-1464 125,-1465 125,-1466 125,-1467 125,-1468 125,-1469 125,-1470 125,-1471 124,-1472 124,-1473 123,-1474 122,-1475 121,-1476 120,-1476 119,-1477 118,-1477 117,-1477 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="116,-1442 117,-1442 118,-1442 119,-1443 120,-1443 121,-1444 122,-1445 123,-1446 123,-1447 124,-1448 124,-1449 124,-1450 124,-1451 124,-1452 124,-1453 124,-1454 123,-1455 123,-1456 122,-1457 121,-1458 120,-1459 119,-1459 118,-1460 117,-1460 116,-1460 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="166,-1528 167,-1528 168,-1528 169,-1528 170,-1529 171,-1530 172,-1530 173,-1531 174,-1532 174,-1533 175,-1534 175,-1535 175,-1536 175,-1537 175,-1538 175,-1539 175,-1540 174,-1541 174,-1542 173,-1543 172,-1544 171,-1545 170,-1545 169,-1546 168,-1546 167,-1546 166,-1546 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="166,-1510 167,-1510 168,-1510 169,-1511 170,-1511 171,-1512 172,-1513 173,-1514 173,-1515 174,-1516 174,-1517 174,-1518 174,-1519 174,-1520 174,-1521 174,-1522 173,-1523 173,-1524 172,-1525 171,-1526 170,-1527 169,-1527 168,-1528 167,-1528 166,-1528 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="299,-1385 299,-1384 299,-1383 300,-1382 300,-1381 301,-1380 302,-1379 303,-1378 304,-1378 305,-1377 306,-1377 307,-1377 308,-1377 309,-1377 310,-1377 311,-1377 312,-1378 313,-1378 314,-1379 315,-1380 316,-1381 316,-1382 317,-1383 317,-1384 317,-1385 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="281,-1385 281,-1384 281,-1383 282,-1382 282,-1381 283,-1380 284,-1379 285,-1378 286,-1378 287,-1377 288,-1377 289,-1377 290,-1377 291,-1377 292,-1377 293,-1377 294,-1378 295,-1378 296,-1379 297,-1380 298,-1381 298,-1382 299,-1383 299,-1384 299,-1385 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="408,-1412 409,-1412 410,-1412 411,-1413 412,-1413 413,-1414 414,-1415 415,-1416 415,-1417 416,-1418 416,-1419 416,-1420 416,-1421 416,-1422 416,-1423 416,-1424 415,-1425 415,-1426 414,-1427 413,-1428 412,-1429 411,-1429 410,-1430 409,-1430 408,-1430 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="407,-1429 408,-1429 409,-1429 410,-1430 411,-1430 412,-1431 413,-1432 414,-1433 414,-1434 415,-1435 415,-1436 415,-1437 415,-1438 415,-1439 415,-1440 415,-1441 414,-1442 414,-1443 413,-1444 412,-1445 411,-1446 410,-1446 409,-1447 408,-1447 407,-1447 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="878,-2108 879,-2108 880,-2108 881,-2109 882,-2109 883,-2110 884,-2111 885,-2112 885,-2113 886,-2114 886,-2115 886,-2116 886,-2117 886,-2118 886,-2119 886,-2120 885,-2121 885,-2122 884,-2123 883,-2124 882,-2125 881,-2125 880,-2126 879,-2126 878,-2126 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="878,-2126 879,-2126 880,-2126 881,-2127 882,-2127 883,-2128 884,-2129 885,-2130 885,-2131 886,-2132 886,-2133 886,-2134 886,-2135 886,-2136 886,-2137 886,-2138 885,-2139 885,-2140 884,-2141 883,-2142 882,-2143 881,-2143 880,-2144 879,-2144 878,-2144 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3922,-1986 3922,-1985 3922,-1984 3923,-1983 3923,-1982 3924,-1981 3925,-1980 3926,-1979 3927,-1979 3928,-1978 3929,-1978 3930,-1978 3931,-1978 3932,-1978 3933,-1978 3934,-1978 3935,-1979 3936,-1979 3937,-1980 3938,-1981 3939,-1982 3939,-1983 3940,-1984 3940,-1985 3940,-1986 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3940,-1987 3940,-1986 3940,-1985 3941,-1984 3941,-1983 3942,-1982 3943,-1981 3944,-1980 3945,-1980 3946,-1979 3947,-1979 3948,-1979 3949,-1979 3950,-1979 3951,-1979 3952,-1979 3953,-1980 3954,-1980 3955,-1981 3956,-1982 3957,-1983 3957,-1984 3958,-1985 3958,-1986 3958,-1987 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1921,-1704 1922,-1704 1923,-1704 1924,-1705 1925,-1705 1926,-1706 1927,-1707 1928,-1708 1928,-1709 1929,-1710 1929,-1711 1929,-1712 1929,-1713 1929,-1714 1929,-1715 1929,-1716 1928,-1717 1928,-1718 1927,-1719 1926,-1720 1925,-1721 1924,-1721 1923,-1722 1922,-1722 1921,-1722 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1920,-1722 1921,-1722 1922,-1722 1923,-1722 1924,-1723 1925,-1724 1926,-1724 1927,-1725 1928,-1726 1928,-1727 1929,-1728 1929,-1729 1929,-1730 1929,-1731 1929,-1732 1929,-1733 1929,-1734 1928,-1735 1928,-1736 1927,-1737 1926,-1738 1925,-1739 1924,-1739 1923,-1740 1922,-1740 1921,-1740 1920,-1740 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="425,-1221 425,-1220 425,-1219 425,-1218 426,-1217 427,-1216 427,-1215 428,-1214 429,-1213 430,-1213 431,-1212 432,-1212 433,-1212 434,-1212 435,-1212 436,-1212 437,-1212 438,-1213 439,-1213 440,-1214 441,-1215 442,-1216 442,-1217 443,-1218 443,-1219 443,-1220 443,-1221 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="407,-1220 407,-1219 407,-1218 408,-1217 408,-1216 409,-1215 410,-1214 411,-1213 412,-1213 413,-1212 414,-1212 415,-1212 416,-1212 417,-1212 418,-1212 419,-1212 420,-1213 421,-1213 422,-1214 423,-1215 424,-1216 424,-1217 425,-1218 425,-1219 425,-1220 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="446,-1159 446,-1160 446,-1161 445,-1162 445,-1163 444,-1164 443,-1165 442,-1166 441,-1166 440,-1167 439,-1167 438,-1167 437,-1167 436,-1167 435,-1167 434,-1167 433,-1166 432,-1166 431,-1165 430,-1164 429,-1163 429,-1162 428,-1161 428,-1160 428,-1159 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="428,-1159 428,-1160 428,-1161 428,-1162 427,-1163 427,-1164 426,-1165 425,-1166 424,-1167 423,-1167 422,-1168 421,-1168 420,-1168 419,-1168 418,-1168 417,-1168 416,-1168 415,-1167 414,-1167 413,-1166 412,-1165 412,-1164 411,-1163 410,-1162 410,-1161 410,-1160 410,-1159 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="414,-714 414,-713 414,-712 415,-711 415,-710 416,-709 417,-708 418,-707 419,-707 420,-706 421,-706 422,-706 423,-706 424,-706 425,-706 426,-706 427,-707 428,-707 429,-708 430,-709 431,-710 431,-711 432,-712 432,-713 432,-714 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="432,-713 432,-712 432,-711 432,-710 433,-709 434,-708 434,-707 435,-706 436,-705 437,-705 438,-704 439,-704 440,-704 441,-704 442,-704 443,-704 444,-704 445,-705 446,-705 447,-706 448,-707 449,-708 449,-709 450,-710 450,-711 450,-712 450,-713 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3794,-1592 3794,-1591 3794,-1590 3795,-1589 3795,-1588 3796,-1587 3797,-1586 3798,-1585 3799,-1585 3800,-1584 3801,-1584 3802,-1584 3803,-1584 3804,-1584 3805,-1584 3806,-1584 3807,-1585 3808,-1585 3809,-1586 3810,-1587 3811,-1588 3811,-1589 3812,-1590 3812,-1591 3812,-1592 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3810,-1592 3810,-1591 3810,-1590 3810,-1589 3811,-1587 3811,-1586 3812,-1586 3813,-1585 3814,-1584 3815,-1583 3816,-1583 3817,-1582 3818,-1582 3819,-1582 3820,-1582 3821,-1582 3822,-1583 3823,-1583 3824,-1584 3825,-1585 3826,-1586 3827,-1586 3827,-1587 3828,-1589 3828,-1590 3828,-1591 3828,-1592 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2392,-1102 2393,-1102 2394,-1102 2396,-1102 2397,-1102 2398,-1103 2399,-1103 2400,-1104 2401,-1105 2401,-1106 2402,-1107 2402,-1108 2403,-1109 2403,-1110 2403,-1112 2403,-1113 2402,-1114 2402,-1115 2401,-1116 2401,-1117 2400,-1118 2399,-1119 2398,-1119 2397,-1120 2396,-1120 2394,-1120 2393,-1120 2392,-1120 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2392,-1120 2393,-1120 2394,-1120 2396,-1120 2397,-1120 2398,-1121 2399,-1121 2400,-1122 2401,-1123 2401,-1124 2402,-1125 2402,-1126 2403,-1127 2403,-1128 2403,-1130 2403,-1131 2402,-1132 2402,-1133 2401,-1134 2401,-1135 2400,-1136 2399,-1137 2398,-1137 2397,-1138 2396,-1138 2394,-1138 2393,-1138 2392,-1138 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="554,-544 554,-543 554,-542 555,-541 555,-540 556,-539 557,-538 558,-537 559,-537 560,-536 561,-536 562,-536 563,-536 564,-536 565,-536 566,-536 567,-537 568,-537 569,-538 570,-539 571,-540 571,-541 572,-542 572,-543 572,-544 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="572,-544 572,-543 572,-542 573,-541 573,-540 574,-539 575,-538 576,-537 577,-537 578,-536 579,-536 580,-536 581,-536 582,-536 583,-536 584,-536 585,-537 586,-537 587,-538 588,-539 589,-540 589,-541 590,-542 590,-543 590,-544 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3390,-1298 3390,-1297 3390,-1296 3390,-1295 3391,-1294 3392,-1293 3392,-1292 3393,-1291 3394,-1290 3395,-1290 3396,-1289 3397,-1289 3398,-1289 3399,-1289 3400,-1289 3401,-1289 3402,-1289 3403,-1290 3404,-1290 3405,-1291 3406,-1292 3407,-1293 3407,-1294 3408,-1295 3408,-1296 3408,-1297 3408,-1298 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3408,-1298 3408,-1297 3408,-1296 3408,-1295 3409,-1294 3410,-1293 3410,-1292 3411,-1291 3412,-1290 3413,-1290 3414,-1289 3415,-1289 3416,-1289 3417,-1289 3418,-1289 3419,-1289 3420,-1289 3421,-1290 3422,-1290 3423,-1291 3424,-1292 3425,-1293 3425,-1294 3426,-1295 3426,-1296 3426,-1297 3426,-1298 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3839,-1869 3839,-1868 3839,-1867 3839,-1866 3840,-1864 3840,-1863 3841,-1863 3842,-1862 3843,-1861 3844,-1860 3845,-1860 3846,-1859 3847,-1859 3848,-1859 3849,-1859 3850,-1859 3851,-1860 3852,-1860 3853,-1861 3854,-1862 3855,-1863 3856,-1863 3856,-1864 3857,-1866 3857,-1867 3857,-1868 3857,-1869 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3856,-1869 3856,-1868 3856,-1867 3856,-1866 3857,-1864 3857,-1863 3858,-1863 3859,-1862 3860,-1861 3861,-1860 3862,-1860 3863,-1859 3864,-1859 3865,-1859 3866,-1859 3867,-1859 3868,-1860 3869,-1860 3870,-1861 3871,-1862 3872,-1863 3873,-1863 3873,-1864 3874,-1866 3874,-1867 3874,-1868 3874,-1869 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2817,-250 2818,-250 2819,-250 2820,-250 2821,-251 2822,-252 2823,-252 2824,-253 2825,-254 2825,-255 2826,-256 2826,-257 2826,-258 2826,-259 2826,-260 2826,-261 2826,-262 2825,-263 2825,-264 2824,-265 2823,-266 2822,-267 2821,-267 2820,-268 2819,-268 2818,-268 2817,-268 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2817,-268 2818,-268 2819,-268 2820,-268 2821,-269 2822,-270 2823,-270 2824,-271 2825,-272 2825,-273 2826,-274 2826,-275 2826,-276 2826,-277 2826,-278 2826,-279 2826,-280 2825,-281 2825,-282 2824,-283 2823,-284 2822,-285 2821,-285 2820,-286 2819,-286 2818,-286 2817,-286 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2536,-250 2537,-250 2538,-250 2540,-250 2541,-250 2542,-251 2543,-251 2544,-252 2545,-253 2545,-254 2546,-255 2546,-256 2547,-257 2547,-258 2547,-260 2547,-261 2546,-262 2546,-263 2545,-264 2545,-265 2544,-266 2543,-267 2542,-267 2541,-268 2540,-268 2538,-268 2537,-268 2536,-268 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2536,-268 2537,-268 2538,-268 2540,-268 2541,-268 2542,-269 2543,-269 2544,-270 2545,-271 2545,-272 2546,-273 2546,-274 2547,-275 2547,-276 2547,-278 2547,-279 2546,-280 2546,-281 2545,-282 2545,-283 2544,-284 2543,-285 2542,-285 2541,-286 2540,-286 2538,-286 2537,-286 2536,-286 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2703,-248 2704,-248 2705,-248 2706,-249 2707,-249 2708,-250 2709,-251 2710,-252 2710,-253 2711,-254 2711,-255 2711,-256 2711,-257 2711,-258 2711,-259 2711,-260 2710,-261 2710,-262 2709,-263 2708,-264 2707,-265 2706,-265 2705,-266 2704,-266 2703,-266 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2703,-266 2704,-266 2705,-266 2706,-267 2707,-267 2708,-268 2709,-269 2710,-270 2710,-271 2711,-272 2711,-273 2711,-274 2711,-275 2711,-276 2711,-277 2711,-278 2710,-279 2710,-280 2709,-281 2708,-282 2707,-283 2706,-283 2705,-284 2704,-284 2703,-284 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2673,-628 2673,-627 2673,-626 2674,-625 2674,-624 2675,-623 2676,-622 2677,-621 2678,-621 2679,-620 2680,-620 2681,-620 2682,-620 2683,-620 2684,-620 2685,-620 2686,-621 2687,-621 2688,-622 2689,-623 2690,-624 2690,-625 2691,-626 2691,-627 2691,-628 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2691,-628 2691,-627 2691,-626 2691,-625 2692,-623 2692,-622 2693,-622 2694,-621 2695,-620 2696,-619 2697,-619 2698,-618 2699,-618 2700,-618 2701,-618 2702,-618 2703,-619 2704,-619 2705,-620 2706,-621 2707,-622 2708,-622 2708,-623 2709,-625 2709,-626 2709,-627 2709,-628 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2832,-587 2833,-587 2834,-587 2835,-588 2836,-588 2837,-589 2838,-590 2839,-591 2839,-592 2840,-593 2840,-594 2840,-595 2840,-596 2840,-597 2840,-598 2840,-599 2839,-600 2839,-601 2838,-602 2837,-603 2836,-604 2835,-604 2834,-605 2833,-605 2832,-605 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2832,-605 2833,-605 2834,-605 2835,-606 2836,-606 2837,-607 2838,-608 2839,-609 2839,-610 2840,-611 2840,-612 2840,-613 2840,-614 2840,-615 2840,-616 2840,-617 2839,-618 2839,-619 2838,-620 2837,-621 2836,-622 2835,-622 2834,-623 2833,-623 2832,-623 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1115,-808 1115,-807 1115,-806 1115,-805 1116,-804 1117,-803 1117,-802 1118,-801 1119,-800 1120,-800 1121,-799 1122,-799 1123,-799 1124,-799 1125,-799 1126,-799 1127,-799 1128,-800 1129,-800 1130,-801 1131,-802 1132,-803 1132,-804 1133,-805 1133,-806 1133,-807 1133,-808 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1133,-808 1133,-807 1133,-806 1133,-805 1134,-804 1135,-803 1135,-802 1136,-801 1137,-800 1138,-800 1139,-799 1140,-799 1141,-799 1142,-799 1143,-799 1144,-799 1145,-799 1146,-800 1147,-800 1148,-801 1149,-802 1150,-803 1150,-804 1151,-805 1151,-806 1151,-807 1151,-808 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="305,-663 306,-663 307,-663 308,-664 309,-664 310,-665 311,-666 312,-667 312,-668 313,-669 313,-670 313,-671 313,-672 313,-673 313,-674 313,-675 312,-676 312,-677 311,-678 310,-679 309,-680 308,-680 307,-681 306,-681 305,-681 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="305,-681 306,-681 307,-681 308,-682 309,-682 310,-683 311,-684 312,-685 312,-686 313,-687 313,-688 313,-689 313,-690 313,-691 313,-692 313,-693 312,-694 312,-695 311,-696 310,-697 309,-698 308,-698 307,-699 306,-699 305,-699 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2107,-1130 2108,-1130 2109,-1130 2110,-1131 2111,-1131 2112,-1132 2113,-1133 2114,-1134 2114,-1135 2115,-1136 2115,-1137 2115,-1138 2115,-1139 2115,-1140 2115,-1141 2115,-1142 2114,-1143 2114,-1144 2113,-1145 2112,-1146 2111,-1147 2110,-1147 2109,-1148 2108,-1148 2107,-1148 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2107,-1148 2108,-1148 2109,-1148 2110,-1149 2111,-1149 2112,-1150 2113,-1151 2114,-1152 2114,-1153 2115,-1154 2115,-1155 2115,-1156 2115,-1157 2115,-1158 2115,-1159 2115,-1160 2114,-1161 2114,-1162 2113,-1163 2112,-1164 2111,-1165 2110,-1165 2109,-1166 2108,-1166 2107,-1166 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="659,-1134 660,-1134 661,-1134 662,-1134 664,-1135 665,-1135 665,-1136 666,-1137 667,-1138 668,-1139 668,-1140 669,-1141 669,-1142 669,-1143 669,-1144 669,-1145 668,-1146 668,-1147 667,-1148 666,-1149 665,-1150 665,-1151 664,-1151 662,-1152 661,-1152 660,-1152 659,-1152 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="658,-1152 659,-1152 660,-1152 661,-1152 662,-1153 663,-1154 664,-1154 665,-1155 666,-1156 666,-1157 667,-1158 667,-1159 667,-1160 667,-1161 667,-1162 667,-1163 667,-1164 666,-1165 666,-1166 665,-1167 664,-1168 663,-1169 662,-1169 661,-1170 660,-1170 659,-1170 658,-1170 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2103,-587 2104,-587 2105,-587 2107,-587 2108,-587 2109,-588 2110,-588 2111,-589 2112,-590 2112,-591 2113,-592 2113,-593 2114,-594 2114,-595 2114,-597 2114,-598 2113,-599 2113,-600 2112,-601 2112,-602 2111,-603 2110,-604 2109,-604 2108,-605 2107,-605 2105,-605 2104,-605 2103,-605 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2103,-605 2104,-605 2105,-605 2107,-605 2108,-605 2109,-606 2110,-606 2111,-607 2112,-608 2112,-609 2113,-610 2113,-611 2114,-612 2114,-613 2114,-615 2114,-616 2113,-617 2113,-618 2112,-619 2112,-620 2111,-621 2110,-622 2109,-622 2108,-623 2107,-623 2105,-623 2104,-623 2103,-623 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2157,-680 2157,-679 2157,-678 2158,-677 2158,-676 2159,-675 2160,-674 2161,-673 2162,-673 2163,-672 2164,-672 2165,-672 2166,-672 2167,-672 2168,-672 2169,-672 2170,-673 2171,-673 2172,-674 2173,-675 2174,-676 2174,-677 2175,-678 2175,-679 2175,-680 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2139,-680 2139,-679 2139,-678 2140,-677 2140,-676 2141,-675 2142,-674 2143,-673 2144,-673 2145,-672 2146,-672 2147,-672 2148,-672 2149,-672 2150,-672 2151,-672 2152,-673 2153,-673 2154,-674 2155,-675 2156,-676 2156,-677 2157,-678 2157,-679 2157,-680 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2882,-346 2883,-346 2884,-346 2885,-346 2886,-347 2887,-348 2888,-348 2889,-349 2890,-350 2890,-351 2891,-352 2891,-353 2891,-354 2891,-355 2891,-356 2891,-357 2891,-358 2890,-359 2890,-360 2889,-361 2888,-362 2887,-363 2886,-363 2885,-364 2884,-364 2883,-364 2882,-364 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2884,-327 2885,-327 2886,-327 2887,-328 2888,-328 2889,-329 2890,-330 2891,-331 2891,-332 2892,-333 2892,-334 2892,-335 2892,-336 2892,-337 2892,-338 2892,-339 2891,-340 2891,-341 2890,-342 2889,-343 2888,-344 2887,-344 2886,-345 2885,-345 2884,-345 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2916,-1731 2917,-1731 2918,-1731 2919,-1732 2920,-1732 2921,-1733 2922,-1734 2923,-1735 2923,-1736 2924,-1737 2924,-1738 2924,-1739 2924,-1740 2924,-1741 2924,-1742 2924,-1743 2923,-1744 2923,-1745 2922,-1746 2921,-1747 2920,-1748 2919,-1748 2918,-1749 2917,-1749 2916,-1749 " stroke="rgb(255,0,0)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2917,-1713 2918,-1713 2919,-1713 2920,-1713 2922,-1714 2923,-1714 2923,-1715 2924,-1716 2925,-1717 2926,-1718 2926,-1719 2927,-1720 2927,-1721 2927,-1722 2927,-1723 2927,-1724 2926,-1725 2926,-1726 2925,-1727 2924,-1728 2923,-1729 2923,-1730 2922,-1730 2920,-1731 2919,-1731 2918,-1731 2917,-1731 " stroke="rgb(255,0,0)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2823,-1732 2824,-1732 2825,-1732 2826,-1733 2827,-1733 2828,-1734 2829,-1735 2830,-1736 2830,-1737 2831,-1738 2831,-1739 2831,-1740 2831,-1741 2831,-1742 2831,-1743 2831,-1744 2830,-1745 2830,-1746 2829,-1747 2828,-1748 2827,-1749 2826,-1749 2825,-1750 2824,-1750 2823,-1750 " stroke="rgb(255,0,0)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2824,-1714 2825,-1714 2826,-1714 2827,-1715 2828,-1715 2829,-1716 2830,-1717 2831,-1718 2831,-1719 2832,-1720 2832,-1721 2832,-1722 2832,-1723 2832,-1724 2832,-1725 2832,-1726 2831,-1727 2831,-1728 2830,-1729 2829,-1730 2828,-1731 2827,-1731 2826,-1732 2825,-1732 2824,-1732 " stroke="rgb(255,0,0)" stroke-width="0.02875"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="0" fill="none" points="3407,-228 3622,-228 3622,-10 3407,-10 3407,-228 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="216,-1806 264,-1806 264,-1715 216,-1715 216,-1806 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="328,-1797 373,-1797 373,-1711 328,-1711 328,-1797 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="449,-1806 498,-1806 498,-1718 449,-1718 449,-1806 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="3902,-1884 3984,-1884 3984,-1852 3902,-1852 3902,-1884 " stroke="rgb(255,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="3902,-1948 3987,-1948 3987,-1915 3902,-1915 3902,-1948 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="3688,-1613 3786,-1613 3786,-1575 3688,-1575 3688,-1613 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="3694,-1750 3781,-1750 3781,-1709 3694,-1709 3694,-1750 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="3873,-1820 3969,-1820 3969,-1782 3873,-1782 3873,-1820 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="3257,-1855 3357,-1855 3357,-1816 3257,-1816 3257,-1855 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="3380,-1680 3431,-1680 3431,-1590 3380,-1590 3380,-1680 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="3096,-1595 3127,-1595 3127,-1515 3096,-1515 3096,-1595 " stroke="rgb(255,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="3114,-1642 3190,-1642 3190,-1602 3114,-1602 3114,-1642 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="3110,-1713 3190,-1713 3190,-1670 3110,-1670 3110,-1713 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="3095,-1049 3183,-1049 3183,-1015 3095,-1015 3095,-1049 " stroke="rgb(255,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="3098,-978 3187,-978 3187,-941 3098,-941 3098,-978 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="3103,-1503 3188,-1503 3188,-1466 3103,-1466 3103,-1503 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="3103,-1430 3182,-1430 3182,-1389 3103,-1389 3103,-1430 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="3215,-1272 3249,-1272 3249,-1188 3215,-1188 3215,-1272 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="3321,-1273 3356,-1273 3356,-1189 3321,-1189 3321,-1273 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="3465,-1269 3497,-1269 3497,-1188 3465,-1188 3465,-1269 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2897,-1185 2934,-1185 2934,-1102 2897,-1102 2897,-1185 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2470,-1182 2510,-1182 2510,-1100 2470,-1100 2470,-1182 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2553,-1185 2591,-1185 2591,-1100 2553,-1100 2553,-1185 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2720,-1088 2758,-1088 2758,-1008 2720,-1008 2720,-1088 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2580,-860 2618,-860 2618,-776 2580,-776 2580,-860 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2720,-835 2763,-835 2763,-752 2720,-752 2720,-835 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2898,-859 2935,-859 2935,-776 2898,-776 2898,-859 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="3239,-751 3280,-751 3280,-672 3239,-672 3239,-751 " stroke="rgb(255,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="3365,-707 3418,-707 3418,-627 3365,-627 3365,-707 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="3425,-803 3508,-803 3508,-762 3425,-762 3425,-803 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2921,-598 2971,-598 2971,-520 2921,-520 2921,-598 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2806,-573 2853,-573 2853,-494 2806,-494 2806,-573 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2729,-598 2770,-598 2770,-515 2729,-515 2729,-598 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2624,-599 2663,-599 2663,-520 2624,-520 2624,-599 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2524,-599 2567,-599 2567,-517 2524,-517 2524,-599 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2409,-600 2452,-600 2452,-515 2409,-515 2409,-600 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2234,-400 2287,-400 2287,-319 2234,-319 2234,-400 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2204,-264 2245,-264 2245,-182 2204,-182 2204,-264 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2516,-220 2559,-220 2559,-134 2516,-134 2516,-220 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2685,-217 2724,-217 2724,-129 2685,-129 2685,-217 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="3106,-319 3187,-319 3187,-269 3106,-269 3106,-319 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2763,-1488 2806,-1488 2806,-1405 2763,-1405 2763,-1488 " stroke="rgb(255,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="2876,-1491 2919,-1491 2919,-1408 2876,-1408 2876,-1491 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2696,-1723 2738,-1723 2738,-1639 2696,-1639 2696,-1723 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2807,-1685 2847,-1685 2847,-1600 2807,-1600 2807,-1685 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2894,-1687 2937,-1687 2937,-1598 2894,-1598 2894,-1687 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1829,-1626 1919,-1626 1919,-1596 1829,-1596 1829,-1626 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1839,-1555 1915,-1555 1915,-1513 1839,-1513 1839,-1555 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2059,-1605 2100,-1605 2100,-1521 2059,-1521 2059,-1605 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2127,-1483 2205,-1483 2205,-1442 2127,-1442 2127,-1483 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2059,-1354 2099,-1354 2099,-1269 2059,-1269 2059,-1354 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2154,-1355 2192,-1355 2192,-1267 2154,-1267 2154,-1355 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1828,-1266 1912,-1266 1912,-1225 1828,-1225 1828,-1266 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1907,-1144 1947,-1144 1947,-1058 1907,-1058 1907,-1144 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2082,-1114 2131,-1114 2131,-1030 2082,-1030 2082,-1114 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2169,-1135 2203,-1135 2203,-1050 2169,-1050 2169,-1135 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2279,-1139 2316,-1139 2316,-1054 2279,-1054 2279,-1139 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1618,-940 1663,-940 1663,-858 1618,-858 1618,-940 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1481,-965 1531,-965 1531,-881 1481,-881 1481,-965 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2090,-920 2173,-920 2173,-875 2090,-875 2090,-920 " stroke="rgb(255,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="1974,-653 2012,-653 2012,-568 1974,-568 1974,-653 " stroke="rgb(255,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="1450,-2132 1536,-2132 1536,-2092 1450,-2092 1450,-2132 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1448,-2021 1537,-2021 1537,-1978 1448,-1978 1448,-2021 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1448,-1566 1533,-1566 1533,-1528 1448,-1528 1448,-1566 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1452,-1474 1534,-1474 1534,-1440 1452,-1440 1452,-1474 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1270,-1392 1350,-1392 1350,-1352 1270,-1352 1270,-1392 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1449,-1236 1538,-1236 1538,-1197 1449,-1197 1449,-1236 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1267,-1146 1359,-1146 1359,-1109 1267,-1109 1267,-1146 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1165,-974 1203,-974 1203,-899 1165,-899 1165,-974 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1141,-894 1230,-894 1230,-861 1141,-861 1141,-894 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1167,-820 1250,-820 1250,-786 1167,-786 1167,-820 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="899,-701 983,-701 983,-653 899,-653 899,-701 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="834,-966 874,-966 874,-877 834,-877 834,-966 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="944,-965 989,-965 989,-884 944,-884 944,-965 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="816,-627 900,-627 900,-583 816,-583 816,-627 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="954,-539 988,-539 988,-461 954,-461 954,-539 " stroke="rgb(255,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="900,-392 987,-392 987,-355 900,-355 900,-392 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1142,-773 1227,-773 1227,-738 1142,-738 1142,-773 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1144,-665 1227,-665 1227,-629 1144,-629 1144,-665 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1142,-584 1226,-584 1226,-547 1142,-547 1142,-584 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1145,-502 1227,-502 1227,-465 1145,-465 1145,-502 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1163,-320 1243,-320 1243,-278 1163,-278 1163,-320 " stroke="rgb(255,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="1093,-211 1139,-211 1139,-125 1093,-125 1093,-211 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1498,-784 1586,-784 1586,-752 1498,-752 1498,-784 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1422,-723 1454,-723 1454,-633 1422,-633 1422,-723 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1449,-470 1531,-470 1531,-424 1449,-424 1449,-470 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1452,-308 1537,-308 1537,-267 1452,-267 1452,-308 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1549,-330 1584,-330 1584,-248 1549,-248 1549,-330 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1380,-244 1423,-244 1423,-156 1380,-156 1380,-244 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1622,-223 1657,-223 1657,-125 1622,-125 1622,-223 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1665,-330 1709,-330 1709,-247 1665,-247 1665,-330 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="903,-1087 985,-1087 985,-1049 903,-1049 903,-1087 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="958,-1146 1036,-1146 1036,-1113 958,-1113 958,-1146 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1176,-1211 1269,-1211 1269,-1173 1176,-1173 1176,-1211 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="909,-1308 952,-1308 952,-1229 909,-1229 909,-1308 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="830,-1413 876,-1413 876,-1329 830,-1329 830,-1413 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="965,-1883 1019,-1883 1019,-1805 965,-1805 965,-1883 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="893,-1734 972,-1734 972,-1698 893,-1698 893,-1734 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1028,-1630 1115,-1630 1115,-1593 1028,-1593 1028,-1630 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1078,-1568 1159,-1568 1159,-1523 1078,-1523 1078,-1568 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1075,-1485 1163,-1485 1163,-1444 1075,-1444 1075,-1485 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2671,-1154 2707,-1154 2707,-1068 2671,-1068 2671,-1154 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2774,-1138 2823,-1138 2823,-1056 2774,-1056 2774,-1138 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2797,-169 2836,-169 2836,-81 2797,-81 2797,-169 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2890,-153 2928,-153 2928,-65 2890,-65 2890,-153 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="458,-1606 545,-1606 545,-1643 458,-1643 458,-1606 " stroke="rgb(255,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="700,-1563 790,-1563 790,-1595 700,-1595 700,-1563 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="701,-1424 794,-1424 794,-1458 701,-1458 701,-1424 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="691,-1319 783,-1319 783,-1350 691,-1350 691,-1319 " stroke="rgb(255,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="696,-1184 783,-1184 783,-1221 696,-1221 696,-1184 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="695,-979 798,-979 798,-1018 695,-1018 695,-979 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="703,-866 791,-866 791,-902 703,-902 703,-866 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="695,-705 782,-705 782,-744 695,-744 695,-705 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="624,-344 712,-344 712,-377 624,-377 624,-344 " stroke="rgb(255,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="497,-458 546,-458 546,-368 497,-368 497,-458 " stroke="rgb(255,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="466,-525 550,-525 550,-561 466,-561 466,-525 " stroke="rgb(255,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="291,-528 326,-528 326,-439 291,-439 291,-528 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="137,-552 220,-552 220,-513 137,-513 137,-552 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="371,-643 455,-643 455,-603 371,-603 371,-643 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="168,-732 251,-732 251,-698 168,-698 168,-732 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="386,-831 491,-831 491,-793 386,-793 386,-831 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="329,-1308 329,-1234 288,-1234 289,-1308 329,-1308 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="391,-1016 429,-1016 429,-925 391,-925 391,-1016 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="393,-1455 426,-1455 426,-1365 393,-1365 393,-1455 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="279,-1401 361,-1401 361,-1367 279,-1367 279,-1401 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="224,-1360 268,-1360 268,-1273 224,-1273 224,-1360 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="148,-1325 188,-1325 188,-1241 148,-1241 148,-1325 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="39,-1367 115,-1367 115,-1329 39,-1329 39,-1367 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="89,-1516 139,-1516 139,-1430 89,-1430 89,-1516 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="149,-1501 184,-1501 184,-1592 149,-1592 149,-1501 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="853,-2156 898,-2156 898,-2065 853,-2065 853,-2156 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1902,-1664 1902,-1742 1942,-1742 1942,-1664 1902,-1664 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="402,-1202 402,-1236 486,-1236 486,-1202 402,-1202 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="401,-1142 401,-1183 488,-1182 488,-1142 401,-1142 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="405,-691 405,-735 496,-735 496,-691 405,-691 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="3430,-299 3512,-299 3512,-248 3430,-248 3430,-299 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="3429,-357 3512,-357 3512,-312 3429,-312 3429,-357 " stroke="rgb(255,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="2222,-1179 2257,-1179 2257,-1161 2222,-1161 2222,-1179 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="2370,-1139 2407,-1139 2407,-1054 2370,-1054 2370,-1139 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="644,-1178 677,-1178 677,-1088 644,-1088 644,-1178 " stroke="rgb(255,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="2083,-625 2127,-625 2127,-543 2083,-543 2083,-625 " stroke="rgb(255,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="2135,-695 2223,-695 2223,-663 2135,-663 2135,-695 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2859,-407 2900,-407 2900,-322 2859,-322 2859,-407 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1022,-414 1036,-414 1036,-419 1022,-419 1022,-414 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="2352,-1296 2352,-1330 2370,-1330 2370,-1296 2352,-1296 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="2086,-838 2121,-838 2121,-820 2086,-820 2086,-838 " stroke="rgb(255,255,255)"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3831" x2="3831" y1="-2230" y2="-2172"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2645" x2="2645" y1="-2230" y2="-2172"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="603" x2="603" y1="-2502" y2="-2447"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1779" x2="1779" y1="-2495" y2="-2455"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1885" x2="1885" y1="-2496" y2="-2456"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1523" x2="1523" y1="-2496" y2="-2456"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1401" x2="1401" y1="-2495" y2="-2455"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="4038" x2="4038" y1="-944" y2="-880"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3831" x2="3831" y1="-942" y2="-880"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="3764" x2="4112" y1="-880" y2="-880"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4004" x2="4004" y1="-34" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3907" x2="3907" y1="-150" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3637" x2="3637" y1="-210" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3712" x2="3712" y1="-150" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3907" x2="3637" y1="-73" y2="-73"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3637" x2="4237" y1="0" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4237" x2="3637" y1="-34" y2="-34"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3907" x2="3637" y1="-115" y2="-115"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4237" x2="3637" y1="-210" y2="-210"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4237" x2="3637" y1="-149" y2="-150"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3831" x2="3831" y1="-2135" y2="-2044"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1885" x2="1885" y1="-2426" y2="-1867"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1779" x2="1779" y1="-2425" y2="-1352"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2645" x2="1779" y1="-1800" y2="-1800"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1523" x2="1523" y1="-2426" y2="-2266"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="599" x2="1377" y1="-2266" y2="-2266"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="599" x2="599" y1="-2417" y2="-142"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="4038" x2="4038" y1="-974" y2="-1135"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2511" x2="2623" y1="-1930" y2="-1930"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2665" x2="3031" y1="-1928" y2="-1928"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1419" x2="1523" y1="-2266" y2="-2266"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="599" x2="426" y1="-485" y2="-485"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3483" x2="3446" y1="-92" y2="-92"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3483" x2="3446" y1="-110" y2="-110"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3465" x2="3483" y1="-110" y2="-110"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3831" x2="3831" y1="-1523" y2="-972"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3425" x2="3831" y1="-1931" y2="-1931"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3075" x2="3384" y1="-1928" y2="-1928"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2825" x2="2825" y1="-1642" y2="-1750"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2917" x2="2917" y1="-1643" y2="-1750"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2896" x2="2896" y1="-1449" y2="-1518"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2782" x2="2782" y1="-1415" y2="-1484"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3053" x2="3481" y1="-1298" y2="-1298"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3053" x2="2488" y1="-1212" y2="-1212"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2915" x2="2915" y1="-1143" y2="-1212"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2571" x2="2571" y1="-1143" y2="-1212"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2488" x2="2488" y1="-1143" y2="-1212"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2798" x2="2798" y1="-1093" y2="-1212"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2687" x2="2687" y1="-1109" y2="-1160"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2687" x2="2798" y1="-1160" y2="-1160"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3053" x2="2597" y1="-887" y2="-887"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2915" x2="2915" y1="-818" y2="-887"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2740" x2="2740" y1="-794" y2="-887"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2597" x2="2597" y1="-818" y2="-887"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3053" x2="3465" y1="-783" y2="-783"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3259" x2="3259" y1="-714" y2="-783"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3053" x2="2428" y1="-627" y2="-627"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2947" x2="2947" y1="-557" y2="-627"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2544" x2="2544" y1="-558" y2="-627"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2428" x2="2428" y1="-558" y2="-627"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2830" x2="2830" y1="-533" y2="-627"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2748" x2="2748" y1="-559" y2="-628"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3053" x2="2223" y1="-293" y2="-293"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2817" x2="2817" y1="-119" y2="-293"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2223" x2="2223" y1="-224" y2="-293"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2534" x2="2534" y1="-176" y2="-293"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2172" x2="2172" y1="-1312" y2="-1381"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2076" x2="2076" y1="-1312" y2="-1381"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2302" x2="2302" y1="-1099" y2="-1168"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2186" x2="2186" y1="-1099" y2="-1168"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2107" x2="2107" y1="-1072" y2="-1168"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1925" x2="1925" y1="-1099" y2="-1168"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1506" x2="1506" y1="-924" y2="-994"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1639" x2="1641" y1="-902" y2="-994"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1991" x2="1991" y1="-611" y2="-680"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1207" x2="1207" y1="-1724" y2="-1463"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1401" x2="1322" y1="-990" y2="-990"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="853" x2="853" y1="-921" y2="-990"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="967" x2="967" y1="-924" y2="-990"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1186" x2="1186" y1="-934" y2="-990"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1115" x2="1115" y1="-989" y2="-169"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="853" x2="853" y1="-990" y2="-1373"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1401" x2="1686" y1="-358" y2="-358"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1401" x2="1401" y1="-198" y2="-358"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1568" x2="1568" y1="-289" y2="-358"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1686" x2="1686" y1="-289" y2="-358"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1114" x2="1027" y1="-676" y2="-676"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="998" x2="998" y1="-674" y2="-372"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="472" x2="472" y1="-1759" y2="-1828"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="350" x2="350" y1="-1759" y2="-1828"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="240" x2="240" y1="-1759" y2="-1828"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="408" x2="408" y1="-1408" y2="-1477"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="247" x2="247" y1="-1314" y2="-1478"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="166" x2="166" y1="-1285" y2="-1477"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="166" x2="166" y1="-1477" y2="-1546"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="408" x2="408" y1="-969" y2="-1038"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="305" x2="305" y1="-1038" y2="-961"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="305" x2="305" y1="-961" y2="-481"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="523" x2="523" y1="-415" y2="-485"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1207" x2="1402" y1="-1724" y2="-1724"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1292" x2="853" y1="-990" y2="-990"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2737" x2="2737" y1="-1160" y2="-1115"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3392" x2="3392" y1="-666" y2="-783"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="932" x2="932" y1="-1132" y2="-1268"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2644" x2="2644" y1="-559" y2="-628"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2084" x2="2084" y1="-1459" y2="-1564"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="879" x2="879" y1="-2106" y2="-2269"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="213" x2="213" y1="-714" y2="-714"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="117" x2="117" y1="-1348" y2="-1478"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2737" x2="2737" y1="-1049" y2="-1118"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2260" x2="2260" y1="-292" y2="-361"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1702" x2="1702" y1="-316" y2="-316"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1641" x2="1642" y1="-358" y2="-173"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="970" x2="970" y1="-502" y2="-603"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3111" x2="3111" y1="-1559" y2="-1620"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2703" x2="2703" y1="-176" y2="-293"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2909" x2="2909" y1="-113" y2="-222"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2909" x2="2817" y1="-222" y2="-222"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="239" x2="209" y1="-1030" y2="-1030"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="210" x2="240" y1="-1050" y2="-1050"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="240" x2="240" y1="-1050" y2="-1030"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="208" x2="208" y1="-1030" y2="-1050"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3831" x2="3831" y1="-2009" y2="-1523"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="444" x2="309" y1="-1220" y2="-1220"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="9" x2="9" y1="-1024" y2="-1024"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="440" x2="425" y1="-489" y2="-485"/>
   <line DF8003:Layer="0" stroke="rgb(255,0,0)" stroke-width="0.5" x1="445" x2="445" y1="-485" y2="-485"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="425" x2="440" y1="-485" y2="-479"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1637" x2="1637" y1="-966" y2="-956"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1643" x2="1643" y1="-966" y2="-956"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2392" x2="2392" y1="-1099" y2="-1168"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2265" x2="2291" y1="-1175" y2="-1175"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2266" x2="2291" y1="-1163" y2="-1163"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2733" x2="2746" y1="-883" y2="-870"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1136" x2="1120" y1="-308" y2="-289"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="979" x2="962" y1="-596" y2="-581"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="112" x2="138" y1="-1045" y2="-1045"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="113" x2="138" y1="-1034" y2="-1034"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="456" x2="482" y1="-491" y2="-491"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="456" x2="482" y1="-479" y2="-479"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="619" x2="605" y1="-1588" y2="-1573"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1785" x2="1785" y1="-1713" y2="-1687"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1773" x2="1773" y1="-1712" y2="-1687"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="659" x2="659" y1="-1131" y2="-1203"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1776" x2="1776" y1="-295" y2="-255"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1743" x2="1847" y1="-254" y2="-254"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="599" x2="599" y1="-107" y2="-58"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2176" x2="2176" y1="-679" y2="-679"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2103" x2="2105" y1="-587" y2="-679"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2101" x2="2101" y1="-651" y2="-641"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2107" x2="2107" y1="-651" y2="-641"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2884" x2="2884" y1="-293" y2="-365"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2877" x2="2890" y1="-297" y2="-307"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2897" x2="2897" y1="-302" y2="-302"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="998" x2="998" y1="-372" y2="-372"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="998" x2="1022" y1="-416" y2="-416"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1037" x2="1061" y1="-416" y2="-416"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1043" x2="1051" y1="-421" y2="-421"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1043" x2="1051" y1="-412" y2="-412"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1061" x2="1061" y1="-416" y2="-387"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1059" x2="1083" y1="-416" y2="-417"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1071" x2="1079" y1="-1237" y2="-1224"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1067" x2="1075" y1="-1222" y2="-1222"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1071" x2="1071" y1="-1237" y2="-1280"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1071" x2="1071" y1="-1311" y2="-1342"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1099" x2="1092" y1="-1283" y2="-1290"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1078" x2="1071" y1="-1305" y2="-1311"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1066" x2="1076" y1="-1280" y2="-1280"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1071" x2="1071" y1="-1222" y2="-1194"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2013" x2="2005" y1="-1125" y2="-1138"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2017" x2="2009" y1="-1140" y2="-1140"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2013" x2="2013" y1="-1125" y2="-1082"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2013" x2="2013" y1="-1051" y2="-1020"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1985" x2="1992" y1="-1079" y2="-1072"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2006" x2="2013" y1="-1057" y2="-1051"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2018" x2="2008" y1="-1082" y2="-1082"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2013" x2="2013" y1="-1140" y2="-1168"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2392" x2="2257" y1="-1168" y2="-1168"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2361" x2="2369" y1="-1212" y2="-1199"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2357" x2="2365" y1="-1197" y2="-1197"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2361" x2="2361" y1="-1280" y2="-1296"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2361" x2="2361" y1="-1388" y2="-1419"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2389" x2="2382" y1="-1361" y2="-1368"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2368" x2="2361" y1="-1382" y2="-1388"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2356" x2="2366" y1="-1357" y2="-1357"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2361" x2="2361" y1="-1197" y2="-1168"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2361" x2="2369" y1="-1280" y2="-1267"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2357" x2="2365" y1="-1265" y2="-1265"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2361" x2="2361" y1="-1265" y2="-1212"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2364" x2="2361" y1="-1355" y2="-1352"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2358" x2="2364" y1="-1355" y2="-1355"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2358" x2="2361" y1="-1355" y2="-1352"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2358" x2="2361" y1="-1336" y2="-1339"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2364" x2="2358" y1="-1336" y2="-1336"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2364" x2="2361" y1="-1336" y2="-1339"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2361" x2="2361" y1="-1330" y2="-1357"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1780" x2="1534" y1="-994" y2="-994"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1534" x2="1506" y1="-994" y2="-994"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2003" x2="1990" y1="-829" y2="-822"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1988" x2="1988" y1="-833" y2="-825"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2070" x2="2086" y1="-829" y2="-829"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2121" x2="2147" y1="-829" y2="-829"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2151" x2="2158" y1="-801" y2="-808"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2172" x2="2179" y1="-822" y2="-829"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2147" x2="2147" y1="-835" y2="-824"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1988" x2="1959" y1="-829" y2="-829"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2070" x2="2057" y1="-829" y2="-822"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2055" x2="2055" y1="-833" y2="-825"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2055" x2="2003" y1="-829" y2="-829"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2146" x2="2143" y1="-826" y2="-829"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2146" x2="2146" y1="-832" y2="-826"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2146" x2="2143" y1="-832" y2="-829"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2126" x2="2129" y1="-832" y2="-829"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2126" x2="2126" y1="-826" y2="-832"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2126" x2="2129" y1="-826" y2="-829"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2179" x2="2210" y1="-829" y2="-829"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1959" x2="1959" y1="-829" y2="-898"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1779" x2="1779" y1="-1322" y2="-661"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1779" x2="1779" y1="-631" y2="-327"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3821" x2="3821" y1="-2172" y2="-2135"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3841" x2="3841" y1="-2172" y2="-2135"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3841" x2="3821" y1="-2135" y2="-2135"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3824" x2="3838" y1="-2106" y2="-2094"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3821" x2="3841" y1="-2172" y2="-2172"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3415" x2="3415" y1="-2172" y2="-2135"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3395" x2="3395" y1="-2172" y2="-2135"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3415" x2="3395" y1="-2135" y2="-2135"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3397" x2="3412" y1="-2106" y2="-2094"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3395" x2="3415" y1="-2172" y2="-2172"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3063" x2="3063" y1="-2172" y2="-2135"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3043" x2="3043" y1="-2172" y2="-2135"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3063" x2="3043" y1="-2135" y2="-2135"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3045" x2="3060" y1="-2106" y2="-2094"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3043" x2="3063" y1="-2172" y2="-2172"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3045" x2="3060" y1="-2205" y2="-2194"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3053" x2="3053" y1="-2230" y2="-2172"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3397" x2="3412" y1="-2205" y2="-2194"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3405" x2="3405" y1="-2230" y2="-2172"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3824" x2="3838" y1="-2205" y2="-2194"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="2655" x2="2655" y1="-2172" y2="-2135"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="2635" x2="2635" y1="-2172" y2="-2135"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="2655" x2="2635" y1="-2135" y2="-2135"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="2637" x2="2652" y1="-2106" y2="-2094"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="2635" x2="2655" y1="-2172" y2="-2172"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="2637" x2="2652" y1="-2205" y2="-2194"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1513" x2="1533" y1="-2378" y2="-2358"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1875" x2="1895" y1="-2373" y2="-2353"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1769" x2="1789" y1="-2378" y2="-2358"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="593" x2="613" y1="-2407" y2="-2387"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="613" x2="593" y1="-2417" y2="-2417"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="593" x2="613" y1="-2447" y2="-2447"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="593" x2="593" y1="-2417" y2="-2447"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="613" x2="613" y1="-2447" y2="-2417"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="593" x2="613" y1="-2477" y2="-2457"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1895" x2="1895" y1="-2456" y2="-2426"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1875" x2="1875" y1="-2426" y2="-2456"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1895" x2="1875" y1="-2426" y2="-2426"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1875" x2="1895" y1="-2456" y2="-2456"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1875" x2="1895" y1="-2416" y2="-2396"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1769" x2="1789" y1="-2415" y2="-2395"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1789" x2="1769" y1="-2425" y2="-2425"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1769" x2="1789" y1="-2455" y2="-2455"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1769" x2="1769" y1="-2425" y2="-2455"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1789" x2="1789" y1="-2455" y2="-2425"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1769" x2="1789" y1="-2485" y2="-2465"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1875" x2="1895" y1="-2486" y2="-2466"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1513" x2="1533" y1="-2456" y2="-2456"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1533" x2="1513" y1="-2426" y2="-2426"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1513" x2="1513" y1="-2426" y2="-2456"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1533" x2="1533" y1="-2456" y2="-2426"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1411" x2="1411" y1="-2455" y2="-2425"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1391" x2="1391" y1="-2425" y2="-2455"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1411" x2="1391" y1="-2425" y2="-2425"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1513" x2="1533" y1="-2415" y2="-2395"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1391" x2="1411" y1="-2455" y2="-2455"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1513" x2="1533" y1="-2486" y2="-2466"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1391" x2="1411" y1="-2485" y2="-2465"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="4048" x2="4048" y1="-974" y2="-944"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="4048" x2="4028" y1="-934" y2="-914"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="4048" x2="4028" y1="-944" y2="-944"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="4028" x2="4048" y1="-974" y2="-974"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="4028" x2="4028" y1="-944" y2="-974"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3841" x2="3841" y1="-972" y2="-942"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3841" x2="3821" y1="-932" y2="-912"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3841" x2="3821" y1="-942" y2="-942"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3821" x2="3841" y1="-972" y2="-972"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3821" x2="3821" y1="-942" y2="-972"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3416" x2="3511" y1="-26" y2="-26"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3486" x2="3511" y1="-136" y2="-136"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3456" x2="3486" y1="-146" y2="-146"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3486" x2="3456" y1="-126" y2="-126"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3456" x2="3456" y1="-126" y2="-146"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3486" x2="3486" y1="-146" y2="-126"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3419" x2="3456" y1="-136" y2="-136"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3455" x2="3475" y1="-163" y2="-183"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3419" x2="3511" y1="-173" y2="-173"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3405" x2="3405" y1="-2135" y2="-1683"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3053" x2="3053" y1="-1942" y2="-1128"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3051" x2="2783" y1="-1518" y2="-1518"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3053" x2="2717" y1="-1750" y2="-1750"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2782" x2="2782" y1="-1518" y2="-1474"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3053" x2="3053" y1="-2135" y2="-1863"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2717" x2="2717" y1="-1750" y2="-1683"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="2645" x2="2645" y1="-2135" y2="-1800"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1401" x2="1401" y1="-2496" y2="-2455"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="589" x2="609" y1="-2244" y2="-2224"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1836" x2="1816" y1="-1178" y2="-1158"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1846" x2="1876" y1="-1158" y2="-1158"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1876" x2="1846" y1="-1178" y2="-1178"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1876" x2="1876" y1="-1158" y2="-1178"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1846" x2="1846" y1="-1178" y2="-1158"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1876" x2="2222" y1="-1168" y2="-1168"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1779" x2="1846" y1="-1168" y2="-1168"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1779" x2="1949" y1="-679" y2="-679"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1979" x2="2176" y1="-679" y2="-679"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1779" x2="2172" y1="-1381" y2="-1381"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1853" x2="1833" y1="-1391" y2="-1371"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1769" x2="1789" y1="-436" y2="-416"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="597" x2="526" y1="-1038" y2="-1038"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="526" x2="526" y1="-1048" y2="-1028"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="496" x2="496" y1="-1028" y2="-1048"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="526" x2="496" y1="-1028" y2="-1028"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="496" x2="526" y1="-1048" y2="-1048"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1885" x2="2645" y1="-1867" y2="-1867"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="240" x2="600" y1="-1828" y2="-1828"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3821" x2="3841" y1="-1341" y2="-1321"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="2511" x2="2510" y1="-1930" y2="-1867"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="2637" x2="2652" y1="-1835" y2="-1823"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="2576" x2="2591" y1="-1872" y2="-1861"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3825" x2="3839" y1="-1657" y2="-1646"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3405" x2="3405" y1="-1683" y2="-1640"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3825" x2="3825" y1="-1634" y2="-1605"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3839" x2="3839" y1="-1606" y2="-1635"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3397" x2="3412" y1="-1906" y2="-1894"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1401" x2="1401" y1="-2425" y2="-1869"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3415" x2="3507" y1="-100" y2="-100"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1787" x2="1787" y1="-494" y2="-476"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1787" x2="1787" y1="-476" y2="-512"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1769" x2="1769" y1="-476" y2="-513"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="608" x2="608" y1="-256" y2="-238"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="608" x2="608" y1="-238" y2="-274"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="589" x2="589" y1="-241" y2="-278"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1979" x2="1949" y1="-679" y2="-679"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1816" x2="1836" y1="-690" y2="-670"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1810" x2="1790" y1="-908" y2="-888"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1843" x2="1873" y1="-888" y2="-888"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1873" x2="1843" y1="-908" y2="-908"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1873" x2="1873" y1="-888" y2="-908"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1843" x2="1843" y1="-908" y2="-888"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1843" x2="1779" y1="-897" y2="-897"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3841" x2="3821" y1="-1007" y2="-987"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="589" x2="609" y1="-2164" y2="-2144"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="483" x2="503" y1="-495" y2="-475"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1411" x2="1411" y1="-1869" y2="-1839"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1391" x2="1391" y1="-1839" y2="-1869"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1411" x2="1391" y1="-1839" y2="-1839"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1391" x2="1411" y1="-1869" y2="-1869"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1401" x2="1401" y1="-1839" y2="-358"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1391" x2="1411" y1="-1828" y2="-1808"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3834" x2="3924" y1="-1802" y2="-1802"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3831" x2="3741" y1="-1592" y2="-1592"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3832" x2="3742" y1="-1728" y2="-1728"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3405" x2="3314" y1="-1837" y2="-1837"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3053" x2="3143" y1="-1695" y2="-1695"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3053" x2="3143" y1="-1620" y2="-1620"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3055" x2="3145" y1="-1482" y2="-1482"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3053" x2="3143" y1="-1411" y2="-1411"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3337" x2="3337" y1="-1298" y2="-1232"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3481" x2="3481" y1="-1298" y2="-1232"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3231" x2="3231" y1="-1298" y2="-1232"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3053" x2="3143" y1="-1032" y2="-1032"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3053" x2="3143" y1="-293" y2="-293"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3053" x2="3143" y1="-960" y2="-960"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1783" x2="1873" y1="-1531" y2="-1531"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1781" x2="2163" y1="-1458" y2="-1458"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1780" x2="1871" y1="-1245" y2="-1245"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1873" x2="2130" y1="-898" y2="-898"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1400" x2="1491" y1="-2112" y2="-2112"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1400" x2="1491" y1="-2000" y2="-2000"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1400" x2="1491" y1="-1546" y2="-1546"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1401" x2="1491" y1="-1457" y2="-1457"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1206" x2="928" y1="-1712" y2="-1712"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1207" x2="1072" y1="-1611" y2="-1611"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1207" x2="1117" y1="-1546" y2="-1546"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1207" x2="1117" y1="-1463" y2="-1463"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1400" x2="1310" y1="-1369" y2="-1369"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1401" x2="1491" y1="-1220" y2="-1220"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1400" x2="1310" y1="-1129" y2="-1129"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1369" x2="1349" y1="-1000" y2="-980"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1117" x2="1185" y1="-876" y2="-876"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1117" x2="1186" y1="-756" y2="-756"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1116" x2="1184" y1="-647" y2="-647"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1116" x2="1184" y1="-566" y2="-566"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1116" x2="1185" y1="-484" y2="-484"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1115" x2="1203" y1="-299" y2="-299"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1401" x2="1542" y1="-768" y2="-768"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1401" x2="1491" y1="-449" y2="-449"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="853" x2="944" y1="-1068" y2="-1068"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="853" x2="995" y1="-1132" y2="-1132"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1032" x2="942" y1="-676" y2="-676"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="998" x2="855" y1="-603" y2="-603"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="998" x2="942" y1="-375" y2="-375"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="599" x2="503" y1="-1626" y2="-1626"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="596" x2="526" y1="-1477" y2="-1477"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="526" x2="526" y1="-1487" y2="-1467"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="496" x2="496" y1="-1467" y2="-1487"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="526" x2="496" y1="-1467" y2="-1467"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="496" x2="526" y1="-1487" y2="-1487"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="496" x2="166" y1="-1477" y2="-1477"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="496" x2="305" y1="-1038" y2="-1038"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="305" x2="179" y1="-535" y2="-535"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="307" x2="415" y1="-626" y2="-626"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="307" x2="310" y1="-1038" y2="-1271"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="599" x2="509" y1="-544" y2="-544"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="599" x2="668" y1="-360" y2="-360"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="589" x2="609" y1="-314" y2="-294"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3483" x2="3507" y1="-57" y2="-57"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3421" x2="3446" y1="-62" y2="-62"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3063" x2="3063" y1="-1128" y2="-1091"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3043" x2="3043" y1="-1128" y2="-1091"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3063" x2="3043" y1="-1091" y2="-1091"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3043" x2="3063" y1="-1128" y2="-1128"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3045" x2="3060" y1="-1161" y2="-1150"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3053" x2="3053" y1="-1091" y2="-293"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3821" x2="3841" y1="-1523" y2="-1503"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1770" x2="1790" y1="-1735" y2="-1715"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="862" x2="842" y1="-1033" y2="-1013"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1061" x2="1041" y1="-1000" y2="-980"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="863" x2="843" y1="-1285" y2="-1265"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="558" x2="538" y1="-1046" y2="-1026"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1292" x2="1322" y1="-980" y2="-980"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1322" x2="1292" y1="-1000" y2="-1000"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1322" x2="1322" y1="-980" y2="-1000"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1292" x2="1292" y1="-1000" y2="-980"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3074" x2="3054" y1="-1492" y2="-1472"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2747" x2="2727" y1="-1151" y2="-1131"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1401" x2="1490" y1="-287" y2="-287"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1402" x2="1422" y1="-293" y2="-273"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="170" x2="78" y1="-1348" y2="-1348"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="247" x2="319" y1="-1385" y2="-1385"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="599" x2="742" y1="-882" y2="-882"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="601" x2="738" y1="-1441" y2="-1441"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="599" x2="739" y1="-996" y2="-996"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="749" x2="599" y1="-1581" y2="-1581"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="304" x2="213" y1="-714" y2="-714"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3833" x2="3947" y1="-1932" y2="-1932"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1782" x2="1873" y1="-1612" y2="-1612"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1810" x2="1790" y1="-1621" y2="-1601"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="165" x2="145" y1="-1358" y2="-1338"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="270" x2="250" y1="-1394" y2="-1374"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="631" x2="611" y1="-1453" y2="-1433"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="624" x2="604" y1="-893" y2="-873"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="655" x2="635" y1="-1005" y2="-985"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3841" x2="3856" y1="-1938" y2="-1926"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="308" x2="328" y1="-822" y2="-802"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="941" x2="921" y1="-1178" y2="-1158"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3401" x2="3381" y1="-778" y2="-758"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1440" x2="1440" y1="-678" y2="-769"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="599" x2="738" y1="-721" y2="-721"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="622" x2="606" y1="-729" y2="-714"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2655" x2="2635" y1="-623" y2="-603"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2093" x2="2073" y1="-1481" y2="-1461"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="599" x2="734" y1="-1203" y2="-1203"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="631" x2="611" y1="-1215" y2="-1195"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="888" x2="868" y1="-2247" y2="-2227"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="989" x2="989" y1="-1842" y2="-1711"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1001" x2="981" y1="-1742" y2="-1722"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="932" x2="1219" y1="-1194" y2="-1194"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1449" x2="1429" y1="-764" y2="-744"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1929" x2="1909" y1="-1793" y2="-1773"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2267" x2="2247" y1="-313" y2="-293"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1154" x2="1154" y1="-808" y2="-808"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1116" x2="1184" y1="-990" y2="-990"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1116" x2="1184" y1="-990" y2="-990"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1116" x2="1206" y1="-808" y2="-808"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3946" x2="3831" y1="-1869" y2="-1869"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="599" x2="728" y1="-1334" y2="-1334"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="627" x2="607" y1="-1346" y2="-1326"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3821" x2="3821" y1="-2044" y2="-2007"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3841" x2="3841" y1="-2044" y2="-2007"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3841" x2="3821" y1="-2007" y2="-2007"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3821" x2="3841" y1="-2044" y2="-2044"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3825" x2="3839" y1="-2066" y2="-2055"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="304" x2="240" y1="-1038" y2="-1038"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="275" x2="255" y1="-1049" y2="-1029"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="208" x2="71" y1="-1039" y2="-1039"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="157" x2="137" y1="-1048" y2="-1028"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="306" x2="433" y1="-812" y2="-813"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="339" x2="359" y1="-723" y2="-703"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="336" x2="316" y1="-1227" y2="-1207"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4031" x2="3832" y1="-1986" y2="-1986"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4031" x2="4056" y1="-1986" y2="-1969"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4060" x2="4049" y1="-1973" y2="-1962"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4064" x2="4052" y1="-1970" y2="-1958"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4060" x2="4087" y1="-1967" y2="-1948"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4033" x2="4062" y1="-1986" y2="-2001"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4068" x2="4087" y1="-2004" y2="-2014"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4087" x2="4087" y1="-1994" y2="-2015"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4082" x2="4097" y1="-1989" y2="-1989"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4081" x2="4098" y1="-1994" y2="-1994"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4087" x2="4087" y1="-1950" y2="-1987"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4070" x2="4064" y1="-1998" y2="-2011"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4067" x2="4059" y1="-1995" y2="-2010"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1921" x2="1921" y1="-1799" y2="-1704"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="308" x2="446" y1="-1160" y2="-1159"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="304" x2="451" y1="-714" y2="-713"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="3769" x2="3783" y1="-1937" y2="-1925"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="610" x2="610" y1="-2175" y2="-2212"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="589" x2="589" y1="-2175" y2="-2212"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1651" x2="1631" y1="-987" y2="-967"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2217" x2="2197" y1="-1178" y2="-1158"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="418" x2="398" y1="-1035" y2="-1015"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="275" x2="255" y1="-544" y2="-524"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1787" x2="1787" y1="-327" y2="-297"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1767" x2="1767" y1="-297" y2="-327"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1787" x2="1767" y1="-297" y2="-297"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1787" x2="1767" y1="-327" y2="-327"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1766" x2="1786" y1="-285" y2="-265"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1769" x2="1789" y1="-367" y2="-347"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="590" x2="610" y1="-173" y2="-153"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="609" x2="589" y1="-110" y2="-110"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="589" x2="589" y1="-110" y2="-140"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="609" x2="609" y1="-140" y2="-110"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="609" x2="589" y1="-140" y2="-140"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="590" x2="610" y1="-93" y2="-73"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2116" x2="2096" y1="-672" y2="-652"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="1394" x2="1414" y1="-2405" y2="-2385"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1411" x2="1411" y1="-2316" y2="-2352"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1390" x2="1390" y1="-2315" y2="-2352"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1014" x2="1005" y1="-420" y2="-411"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1098" x2="1098" y1="-1843" y2="-1712"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1107" x2="1087" y1="-1745" y2="-1725"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,0,0)" stroke-width="3" x1="1788" x2="1788" y1="-1352" y2="-1322"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,0,0)" stroke-width="3" x1="1768" x2="1768" y1="-1322" y2="-1352"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,0,0)" stroke-width="3" x1="1788" x2="1768" y1="-1322" y2="-1322"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,0,0)" stroke-width="3" x1="1768" x2="1788" y1="-1352" y2="-1352"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,0,0)" stroke-width="3" x1="1768" x2="1788" y1="-1310" y2="-1290"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,0,0)" stroke-width="3" x1="1788" x2="1788" y1="-661" y2="-631"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,0,0)" stroke-width="3" x1="1768" x2="1768" y1="-631" y2="-661"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,0,0)" stroke-width="3" x1="1788" x2="1768" y1="-631" y2="-631"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,0,0)" stroke-width="3" x1="1768" x2="1788" y1="-661" y2="-661"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,0,0)" stroke-width="3" x1="1768" x2="1788" y1="-619" y2="-599"/>
   <line DF8003:Layer="图框（粗实线）" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4237" x2="0" y1="0" y2="0"/>
   <line DF8003:Layer="图框（粗实线）" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4237" x2="4237" y1="-2970" y2="0"/>
   <line DF8003:Layer="图框（粗实线）" stroke="rgb(255,255,255)" stroke-width="0.5" x1="0" x2="0" y1="0" y2="-2970"/>
   <line DF8003:Layer="图框（粗实线）" stroke="rgb(255,255,255)" stroke-width="0.5" x1="0" x2="4237" y1="-2970" y2="-2970"/>
   <line DF8003:Layer="粗线" stroke="rgb(255,255,255)" stroke-width="4" x1="2482" x2="4004" y1="-2230" y2="-2230"/>
   <line DF8003:Layer="粗线" stroke="rgb(255,255,255)" stroke-width="4" x1="707" x2="425" y1="-2502" y2="-2502"/>
   <line DF8003:Layer="粗线" stroke="rgb(255,255,255)" stroke-width="4" x1="1996" x2="1304" y1="-2496" y2="-2496"/>
   <line DF8003:Layer="粗线" stroke="rgb(255,255,255)" stroke-width="4" x1="717" x2="435" y1="-56" y2="-56"/>
  </g><g id="Circle_Layer">
   <circle DF8003:Layer="0" cx="3457" cy="-57" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3470" cy="-57" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3936" cy="-1801" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3949" cy="-1801" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3728" cy="-1591" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3716" cy="-1591" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3729" cy="-1728" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3716" cy="-1728" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3301" cy="-1836" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3289" cy="-1836" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3405" cy="-1627" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3405" cy="-1614" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3155" cy="-1695" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3167" cy="-1695" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2916" cy="-1630" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2916" cy="-1618" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2825" cy="-1629" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2825" cy="-1617" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2716" cy="-1670" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2716" cy="-1657" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3155" cy="-1619" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3167" cy="-1619" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2895" cy="-1436" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2895" cy="-1424" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2782" cy="-1402" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2782" cy="-1389" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3157" cy="-1482" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3170" cy="-1482" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3155" cy="-1410" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3167" cy="-1410" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3336" cy="-1219" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3336" cy="-1206" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3481" cy="-1219" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3481" cy="-1206" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3230" cy="-1219" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3230" cy="-1206" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2915" cy="-1130" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2915" cy="-1117" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2571" cy="-1130" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2571" cy="-1117" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2487" cy="-1130" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2487" cy="-1117" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2799" cy="-1080" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2797" cy="-1067" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2688" cy="-1096" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2687" cy="-1081" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3155" cy="-1031" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3167" cy="-1031" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3155" cy="-292" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3167" cy="-292" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2915" cy="-805" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2915" cy="-793" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2740" cy="-781" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2740" cy="-768" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2597" cy="-805" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2597" cy="-793" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3477" cy="-783" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3490" cy="-783" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3258" cy="-701" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3258" cy="-689" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2946" cy="-544" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2946" cy="-532" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2544" cy="-545" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2544" cy="-533" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2428" cy="-545" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2428" cy="-533" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2830" cy="-520" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2830" cy="-508" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2747" cy="-546" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2747" cy="-533" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2222" cy="-211" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2222" cy="-198" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2703" cy="-162" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2703" cy="-149" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2534" cy="-163" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2534" cy="-150" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3155" cy="-960" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3167" cy="-960" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1885" cy="-1530" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1898" cy="-1530" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2175" cy="-1459" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2188" cy="-1459" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2171" cy="-1299" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2171" cy="-1287" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2075" cy="-1299" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2075" cy="-1287" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1883" cy="-1245" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1895" cy="-1245" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2301" cy="-1086" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2301" cy="-1074" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2185" cy="-1086" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2185" cy="-1074" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2106" cy="-1059" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2106" cy="-1046" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1925" cy="-1086" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1925" cy="-1074" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1505" cy="-911" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1505" cy="-899" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1640" cy="-888" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1640" cy="-875" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2142" cy="-897" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2155" cy="-897" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1991" cy="-598" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1991" cy="-585" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1503" cy="-2112" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1516" cy="-2112" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1503" cy="-1999" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1516" cy="-1999" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1503" cy="-1545" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1516" cy="-1545" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1503" cy="-1457" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1516" cy="-1457" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="915" cy="-1714" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="906" cy="-1716" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1060" cy="-1613" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1046" cy="-1615" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1104" cy="-1546" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1091" cy="-1546" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1104" cy="-1463" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1091" cy="-1463" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1297" cy="-1369" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1284" cy="-1369" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1503" cy="-1219" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1516" cy="-1219" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1297" cy="-1128" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1284" cy="-1128" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="853" cy="-908" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="853" cy="-895" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="966" cy="-911" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="966" cy="-899" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1185" cy="-921" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1185" cy="-910" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1198" cy="-878" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1211" cy="-878" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1198" cy="-756" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1210" cy="-756" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1196" cy="-647" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1209" cy="-647" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1196" cy="-566" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1209" cy="-566" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1197" cy="-484" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1209" cy="-484" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1215" cy="-298" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1227" cy="-298" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1554" cy="-767" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1567" cy="-767" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1503" cy="-449" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1516" cy="-449" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1400" cy="-185" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1400" cy="-172" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1567" cy="-276" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1567" cy="-264" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1686" cy="-276" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1686" cy="-264" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="853" cy="-1398" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="853" cy="-1385" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="956" cy="-1068" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="969" cy="-1068" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1007" cy="-1130" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1019" cy="-1130" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1118" cy="-157" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1117" cy="-145" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="929" cy="-676" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="916" cy="-676" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="829" cy="-605" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="929" cy="-374" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="916" cy="-374" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="472" cy="-1746" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="472" cy="-1733" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="350" cy="-1746" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="350" cy="-1733" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="239" cy="-1746" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="239" cy="-1733" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="490" cy="-1626" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="478" cy="-1626" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="408" cy="-1395" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="408" cy="-1383" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="246" cy="-1301" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="246" cy="-1289" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="167" cy="-1272" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="167" cy="-1259" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="166" cy="-1571" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="166" cy="-1558" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="408" cy="-956" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="408" cy="-943" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="166" cy="-533" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="153" cy="-533" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="427" cy="-625" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="439" cy="-625" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="308" cy="-1283" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="308" cy="-1296" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="496" cy="-543" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="484" cy="-543" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="523" cy="-402" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="523" cy="-390" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="680" cy="-359" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="692" cy="-359" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1502" cy="-287" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1515" cy="-287" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="65" cy="-1348" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="53" cy="-1348" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="331" cy="-1386" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="343" cy="-1386" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="306" cy="-468" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="306" cy="-456" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="754" cy="-881" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="766" cy="-881" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="751" cy="-995" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="763" cy="-995" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="774" cy="-1579" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="761" cy="-1579" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="200" cy="-714" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="187" cy="-714" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1885" cy="-1611" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1898" cy="-1611" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3391" cy="-653" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3391" cy="-641" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="932" cy="-1292" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="932" cy="-1280" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1438" cy="-665" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1438" cy="-652" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="750" cy="-721" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="762" cy="-721" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2644" cy="-546" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2644" cy="-534" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2084" cy="-1588" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2084" cy="-1576" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="746" cy="-1202" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="759" cy="-1202" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="878" cy="-2093" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="878" cy="-2080" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="115" cy="-1502" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="115" cy="-1490" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="750" cy="-1439" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="762" cy="-1439" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="987" cy="-1853" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="988" cy="-1868" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1231" cy="-1193" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1247" cy="-1193" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2736" cy="-1035" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2736" cy="-1022" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2259" cy="-385" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2259" cy="-373" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1220" cy="-804" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1232" cy="-804" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1640" cy="-158" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1640" cy="-146" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3959" cy="-1932" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3971" cy="-1932" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3957" cy="-1870" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3970" cy="-1870" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="740" cy="-1333" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="752" cy="-1333" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="969" cy="-489" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="969" cy="-477" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3111" cy="-1546" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3112" cy="-1534" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="843" cy="-607" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2817" cy="-107" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2817" cy="-94" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2908" cy="-100" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2908" cy="-87" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="445" cy="-812" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="458" cy="-813" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1921" cy="-1691" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1920" cy="-1681" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="456" cy="-1219" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="469" cy="-1219" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="458" cy="-1159" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="471" cy="-1160" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="463" cy="-715" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="475" cy="-715" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2188" cy="-679" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2200" cy="-679" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1637" cy="-961" fill="none" r="2" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1643" cy="-961" fill="none" r="2" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2392" cy="-1086" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2392" cy="-1074" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2279" cy="-1174" fill="none" r="4.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2279" cy="-1162" fill="none" r="4.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="126" cy="-1045" fill="none" r="4.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="126" cy="-1033" fill="none" r="4.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="470" cy="-491" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="470" cy="-478" fill="none" r="4.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1785" cy="-1699" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1773" cy="-1698" fill="none" r="4.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="659" cy="-1118" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="659" cy="-1106" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2105" cy="-573" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2105" cy="-560" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2101" cy="-646" fill="none" r="1.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2107" cy="-646" fill="none" r="2" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2883" cy="-390" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2883" cy="-377" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1044" cy="-411" fill="none" r="1.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1045" cy="-421" fill="none" r="2" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1060" cy="-381" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1060" cy="-374" fill="none" r="6.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1088" cy="-415" fill="none" r="6.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1095" cy="-415" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1096" cy="-1855" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1097" cy="-1869" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="3825" cy="-1620" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="3840" cy="-1621" fill="none" r="3" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="1788" cy="-494" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="1788" cy="-494" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="1769" cy="-494" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="1769" cy="-494" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="608" cy="-256" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="608" cy="-256" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="590" cy="-256" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="590" cy="-256" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="610" cy="-2189" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="588" cy="-2189" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="1411" cy="-2329" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="1390" cy="-2330" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="3464" cy="-91" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="3464" cy="-91" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="3464" cy="-110" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="3464" cy="-110" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3847.000000 -2159.000000) translate(0,20)">064</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3844.000000 -2108.000000) translate(0,20)">0646</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3844.000000 -2207.000000) translate(0,20)">0641</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2660.000000 -2159.000000) translate(0,20)">061</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2658.000000 -2108.000000) translate(0,20)">0616</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2558.000000 -2242.000000) translate(0,20)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2558.000000 -2242.000000) translate(0,45)">马</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2558.000000 -2242.000000) translate(0,70)">鞍</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2558.000000 -2242.000000) translate(0,95)">山</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2558.000000 -2242.000000) translate(0,120)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2658.000000 -2207.000000) translate(0,20)">0611</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(1.000000 0.000000 0.000000 1.000000 397.000000 -2725.000000) translate(0,40)">密者河一级站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(1.000000 0.000000 0.000000 1.000000 397.000000 -2725.000000) translate(0,90)"> 10kV母线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1915.000000 -2369.000000) translate(0,20)">0446</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1796.000000 -2374.000000) translate(0,20)">0436</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 621.000000 -2439.000000) translate(0,20)">031</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 622.000000 -2465.000000) translate(0,20)">0311</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1911.000000 -2448.000000) translate(0,20)">044</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1913.000000 -2411.000000) translate(0,20)">0443</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1797.000000 -2447.000000) translate(0,20)">043</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1796.000000 -2411.000000) translate(0,20)">0433</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1791.000000 -2325.000000) translate(0,20)">新团线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1794.000000 -2484.000000) translate(0,20)">0431</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1908.000000 -2485.000000) translate(0,20)">0441</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1905.000000 -2320.000000) translate(0,20)">马鞍山线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1417.000000 -2486.000000) translate(0,20)">0411</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(1.000000 0.000000 0.000000 1.000000 1446.000000 -2686.000000) translate(0,40)">密者河二级站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(1.000000 0.000000 0.000000 1.000000 1446.000000 -2686.000000) translate(0,90)">  10kV母线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(1.000000 0.000000 0.000000 1.000000 3749.000000 -858.000000) translate(0,40)">马龙河电站10kV母线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2240.000000 -699.000000) translate(0,12)">瓦场</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 498.000000 -827.000000) translate(0,12)">瓦喳拉</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 494.000000 -803.000000) translate(0,10)">S11-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 329.000000 -1280.000000) translate(0,8)">S11-80kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1659.000000 -222.000000) translate(0,12)">S11-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1539.000000 -220.000000) translate(0,12)">S11-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1671.000000 -247.000000) translate(0,12)">吴家坟</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1547.000000 -247.000000) translate(0,12)">菖蒲塘</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1379.000000 -154.000000) translate(0,12)">多依树</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1376.000000 -128.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 337.000000 -1298.000000) translate(0,8)">邑朵簸</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1422.000000 -392.000000) translate(0,12)">BZ099</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2117.000000 -1930.000000) translate(0,20)">10kV马鞍山线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1526.000000 -415.000000) translate(0,12)">10kV吴家坟分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 448.000000 -1278.000000) translate(0,12)">力窝山</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 444.000000 -1254.000000) translate(0,10)">S11-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 312.000000 -855.000000) translate(0,12)">10kV瓦喳拉分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 610.000000 -930.000000) translate(0,12)">10kV利么簸支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 609.000000 -1484.000000) translate(0,12)">10kV洒树咪新村支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 622.000000 -1035.000000) translate(0,12)">10kV依启么支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 612.000000 -775.000000) translate(0,12)">10kV俄旧米支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 652.000000 -1273.000000) translate(0,12)">10kV洒树咪多</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 652.000000 -1273.000000) translate(0,27)">依树支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 457.000000 -1140.000000) translate(0,8)">小坝田</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 450.000000 -1124.000000) translate(0,10)">D11-20kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1666.000000 -137.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1660.000000 -163.000000) translate(0,12)">大火塘</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1403.000000 -311.000000) translate(0,8)">Z0995</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2733.000000 -1981.000000) translate(0,20)">10kV马大联络线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 862.000000 -644.000000) translate(0,8)">10kV小麻树分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 266.000000 -695.000000) translate(0,8)">#10杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2119.000000 -1155.000000) translate(0,8)">#1杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1162.000000 -841.000000) translate(0,8)">10kV下峨苴分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1792.000000 -284.000000) translate(0,20)">0521</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1791.000000 -378.000000) translate(0,20)">0526</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1803.000000 -328.000000) translate(0,20)">052</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 626.000000 -136.000000) translate(0,20)">056</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 622.000000 -176.000000) translate(0,20)">0566</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2111.000000 -1139.000000) translate(0,8)">BZ074</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 314.000000 -680.000000) translate(0,8)">BZ093</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2369.000000 -1224.000000) translate(0,8)">#1杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2371.000000 -1296.000000) translate(0,8)">#3杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2349.000000 -1167.000000) translate(0,8)">#18杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3734.000000 -2209.000000) translate(0,20)">大</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3734.000000 -2209.000000) translate(0,45)">地</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3734.000000 -2209.000000) translate(0,70)">基</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3734.000000 -2209.000000) translate(0,95)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3420.000000 -2159.000000) translate(0,20)">063</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3418.000000 -2108.000000) translate(0,20)">0636</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3322.000000 -2202.000000) translate(0,20)">红</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3322.000000 -2202.000000) translate(0,45)">星</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3322.000000 -2202.000000) translate(0,70)">树</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3322.000000 -2202.000000) translate(0,95)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3068.000000 -2159.000000) translate(0,20)">062</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3066.000000 -2108.000000) translate(0,20)">0626</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2963.000000 -2198.000000) translate(0,20)">西</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2963.000000 -2198.000000) translate(0,45)">叉</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2963.000000 -2198.000000) translate(0,70)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3066.000000 -2207.000000) translate(0,20)">0621</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3418.000000 -2207.000000) translate(0,20)">0631</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1542.000000 -2484.000000) translate(0,20)">0421</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1541.000000 -2413.000000) translate(0,20)">0423</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1541.000000 -2376.000000) translate(0,20)">0426</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1542.000000 -2449.000000) translate(0,20)">042</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 621.000000 -2403.000000) translate(0,20)">0316</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 538.000000 -2489.000000) translate(0,20)">一</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 538.000000 -2489.000000) translate(0,45)">二</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 538.000000 -2489.000000) translate(0,70)">联</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 538.000000 -2489.000000) translate(0,95)">网</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 538.000000 -2489.000000) translate(0,120)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1415.000000 -2412.000000) translate(0,20)">0416</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1339.000000 -2435.000000) translate(0,20)">咪</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1339.000000 -2435.000000) translate(0,45)">么</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1339.000000 -2435.000000) translate(0,70)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 4051.000000 -1209.000000) translate(0,20)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 4051.000000 -1209.000000) translate(0,45)">马</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 4051.000000 -1209.000000) translate(0,70)">宜</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 4051.000000 -1209.000000) translate(0,95)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3752.000000 -1188.000000) translate(0,20)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3752.000000 -1188.000000) translate(0,45)">大</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3752.000000 -1188.000000) translate(0,70)">地</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3752.000000 -1188.000000) translate(0,95)">基</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3752.000000 -1188.000000) translate(0,120)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 4057.000000 -963.000000) translate(0,20)">072</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 4057.000000 -927.000000) translate(0,20)">0721</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3848.000000 -927.000000) translate(0,20)">0711</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3848.000000 -963.000000) translate(0,20)">071</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3362.000000 -1564.000000) translate(0,12)">S11-250kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3373.000000 -1585.000000) translate(0,12)">集镇2号变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3271.000000 -1815.000000) translate(0,12)">集镇1号变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3253.000000 -1794.000000) translate(0,12)">S11-M-250kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2566.000000 -770.000000) translate(0,12)">杨梅树</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2562.000000 -744.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2637.000000 -928.000000) translate(0,12)">10kV杨梅树分支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2885.000000 -748.000000) translate(0,12)">S7-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2519.000000 -132.000000) translate(0,12)">嘎苴</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2500.000000 -112.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2195.000000 -181.000000) translate(0,12)">大水井</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2191.000000 -157.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2781.000000 -80.000000) translate(0,12)">干海子</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2780.000000 -54.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2667.000000 -110.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3148.000000 -242.000000) translate(0,12)">S13-M-80kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3168.000000 -264.000000) translate(0,12)">西叉</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2729.000000 -508.000000) translate(0,12)">蛇务</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2514.000000 -511.000000) translate(0,12)">田房</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2714.000000 -484.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2417.000000 -514.000000) translate(0,12)">新村</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2399.000000 -493.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2496.000000 -487.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3510.000000 -800.000000) translate(0,12)">火平郎</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3511.000000 -777.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2512.000000 -1156.000000) translate(0,12)">三级站</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2459.000000 -1097.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2885.000000 -1076.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2905.000000 -1100.000000) translate(0,12)">峨苴</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3196.000000 -1044.000000) translate(0,12)">顾言寿</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3185.000000 -1021.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3185.000000 -1407.000000) translate(0,12)">S11-M-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3183.000000 -1429.000000) translate(0,12)">大龙滩</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3189.000000 -1615.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3200.000000 -1639.000000) translate(0,12)">七街</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3197.000000 -1709.000000) translate(0,12)">依扁啦</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3199.000000 -1686.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2794.000000 -1576.000000) translate(0,12)">S9-100kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2803.000000 -1599.000000) translate(0,12)">杞家</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2870.000000 -1404.000000) translate(0,12)">洒木口</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2866.000000 -1380.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2732.000000 -1345.000000) translate(0,12)">S9-80kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2721.000000 -1369.000000) translate(0,12)">三级站取水口</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2906.000000 -1598.000000) translate(0,12)">大村</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2685.000000 -1610.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2694.000000 -1634.000000) translate(0,12)">核桃树</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2898.000000 -1577.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 613.000000 -2117.000000) translate(0,20)">大</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 613.000000 -2117.000000) translate(0,45)">过</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 613.000000 -2117.000000) translate(0,70)">口</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 613.000000 -2117.000000) translate(0,95)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1956.000000 -729.000000) translate(0,12)">10kV瓦场支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1955.000000 -543.000000) translate(0,12)">D-5kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2082.000000 -1028.000000) translate(0,12)">利苏打</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2074.000000 -1005.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1879.000000 -1037.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1897.000000 -1058.000000) translate(0,12)">红梨树</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1489.000000 -876.000000) translate(0,12)">苏布</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1916.000000 -1270.000000) translate(0,12)">大山变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1917.000000 -1247.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2203.000000 -1487.000000) translate(0,12)">大路边公变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2216.000000 -1458.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1925.000000 -1524.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1911.000000 -1549.000000) translate(0,12)">诺掌公变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2059.000000 -1271.000000) translate(0,12)">上村</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2050.000000 -1250.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2152.000000 -1248.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2154.000000 -1271.000000) translate(0,12)">坝硬</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2232.000000 -677.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2155.000000 -1048.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2208.000000 -1097.000000) translate(0,12)">李家</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2275.000000 -1052.000000) translate(0,12)">S9-100kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2320.000000 -1137.000000) translate(0,12)">小水井金矿1号变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1791.000000 -433.000000) translate(0,20)">F0731</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1796.000000 -240.000000) translate(0,20)">至依沙河电站</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 696.000000 -600.000000) translate(0,12)">S11-M-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 743.000000 -622.000000) translate(0,12)">小麻树</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 811.000000 -690.000000) translate(0,12)">新地基</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1237.000000 -867.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 808.000000 -668.000000) translate(0,12)">S7-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1235.000000 -778.000000) translate(0,12)">大菜园</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1538.000000 -1543.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1545.000000 -1565.000000) translate(0,12)">大平掌</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1543.000000 -1451.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1542.000000 -1217.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1541.000000 -1239.000000) translate(0,12)">大瓦房</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1173.000000 -1349.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1216.000000 -1377.000000) translate(0,12)">新村</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1188.000000 -1125.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1198.000000 -1146.000000) translate(0,12)">红土坡</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1238.000000 -892.000000) translate(0,12)">庙房山</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1231.000000 -757.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1234.000000 -670.000000) translate(0,12)">小着么</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1228.000000 -477.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1235.000000 -648.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1227.000000 -500.000000) translate(0,12)">大田屋</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1081.000000 -130.000000) translate(0,12)">米糠备米</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1239.000000 -558.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1082.000000 -103.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1237.000000 -582.000000) translate(0,12)">新村领干</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 832.000000 -415.000000) translate(0,12)">洼木波</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 811.000000 -393.000000) translate(0,12)">S7-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1549.000000 -2019.000000) translate(0,12)">咪么集镇</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1547.000000 -1996.000000) translate(0,12)">S11-M-80kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 110.000000 -1214.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 115.000000 -1237.000000) translate(0,12)">小瓦桥</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 108.000000 -726.000000) translate(0,12)">梅子树</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 102.000000 -701.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 203.000000 -1265.000000) translate(0,12)">独松树</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 184.000000 -1244.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 394.000000 -1341.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 396.000000 -1364.000000) translate(0,12)">洒树咪</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 353.000000 -1615.000000) translate(0,12)">S8-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 330.000000 -1638.000000) translate(0,12)">一级站取水坝</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 453.000000 -1719.000000) translate(0,12)">洼舍子</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 446.000000 -1698.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 183.000000 -1715.000000) translate(0,12)">新村蚂蟥箐</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 199.000000 -1696.000000) translate(0,12)">S8-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 324.000000 -1705.000000) translate(0,12)">大旧村</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 317.000000 -1681.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 372.000000 -900.000000) translate(0,12)">S11-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 380.000000 -923.000000) translate(0,12)">大领干</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 383.000000 -571.000000) translate(0,12)">联通机站</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 386.000000 -545.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 684.000000 -48.000000) translate(0,12)"> 至35kV中山变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 827.000000 -1738.000000) translate(0,12)">杨梅树</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 817.000000 -1714.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 952.000000 -1634.000000) translate(0,12)">小西洼</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 955.000000 -1614.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 967.000000 -1565.000000) translate(0,12)">大西洼下村</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 981.000000 -1543.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1004.000000 -1482.000000) translate(0,12)">大西洼</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 991.000000 -1462.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3678.000000 -1687.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3696.000000 -1708.000000) translate(0,12)">坝口</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3975.000000 -1821.000000) translate(0,12)">米交定</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3977.000000 -1795.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3849.000000 -1686.000000) translate(0,12)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3849.000000 -1686.000000) translate(0,27)">F0431</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1987.000000 -1839.000000) translate(0,20)">10kV大坎子线联络线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3687.000000 -1572.000000) translate(0,12)">我地苴</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3674.000000 -1548.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2595.000000 -1164.000000) translate(0,12)">下着米</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2553.000000 -1099.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2776.000000 -1024.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2631.000000 -1039.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2658.000000 -1064.000000) translate(0,12)">纳广</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2774.000000 -1053.000000) translate(0,12)">三家村</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3312.000000 -1187.000000) translate(0,12)">许家</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3217.000000 -1182.000000) translate(0,12)">何家</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3461.000000 -1186.000000) translate(0,12)">树梅子</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3198.000000 -1155.000000) translate(0,12)">S11-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3301.000000 -1161.000000) translate(0,12)">S11-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3461.000000 -1161.000000) translate(0,12)">S11-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3191.000000 -976.000000) translate(0,12)">大火塘</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3192.000000 -952.000000) translate(0,12)">D-5kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2919.000000 -494.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2804.000000 -468.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2808.000000 -491.000000) translate(0,12)">平郎井</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2933.000000 -518.000000) translate(0,12)">马家</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2178.000000 -917.000000) translate(0,12)">大田领干1号变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2207.000000 -890.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1617.000000 -856.000000) translate(0,12)">肖家</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1601.000000 -833.000000) translate(0,12)">S11-M-160kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1551.000000 -2136.000000) translate(0,12)">大麦地</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1559.000000 -1473.000000) translate(0,12)">旧村山</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 826.000000 -1454.000000) translate(0,12)">海子山</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1044.000000 -1124.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 984.000000 -1095.000000) translate(0,12)">渣苴山公变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 993.000000 -1072.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1041.000000 -1147.000000) translate(0,12)">明家村</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 125.000000 -1635.000000) translate(0,12)">老木达</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 120.000000 -1612.000000) translate(0,12)">S11-M,R10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 277.000000 -434.000000) translate(0,12)">马转路</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 385.000000 -1103.000000) translate(0,12)">10kV中邑舍支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 275.000000 -412.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 614.000000 -271.000000) translate(0,12)"> 高压</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 614.000000 -271.000000) translate(0,27)">计量箱</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1203.000000 -915.000000) translate(0,12)">S7-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1205.000000 -938.000000) translate(0,12)">花红树</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3848.000000 -1002.000000) translate(0,20)">0716</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3989.000000 -1886.000000) translate(0,12)">李万泽专变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3992.000000 -1864.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 530.000000 -2241.000000) translate(0,12)">#132杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 470.000000 -614.000000) translate(0,12)">S11-80kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 455.000000 -640.000000) translate(0,12)">折苴簸大山</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3229.000000 -673.000000) translate(0,12)">移动机站</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3238.000000 -653.000000) translate(0,12)">D-5kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2707.000000 -749.000000) translate(0,12)">电信基站</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2709.000000 -722.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1330.000000 -1863.000000) translate(0,12)">#18杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1423.000000 -1862.000000) translate(0,20)">F093</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1423.000000 -1824.000000) translate(0,20)">F0931</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3203.000000 -1473.000000) translate(0,12)">D11-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3189.000000 -1496.000000) translate(0,12)">大多依树公变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3844.000000 -1740.000000) translate(0,12)">#10杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3843.000000 -1606.000000) translate(0,12)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3777.000000 -1814.000000) translate(0,12)">#08杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3417.000000 -1848.000000) translate(0,12)">#10杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3424.000000 -1913.000000) translate(0,20)">F0311</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2996.000000 -1707.000000) translate(0,12)">#20杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2996.000000 -1633.000000) translate(0,12)">#24杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3054.000000 -1534.000000) translate(0,8)">#28杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2999.000000 -1419.000000) translate(0,12)">#35杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2999.000000 -1310.000000) translate(0,12)">#38杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3060.000000 -1223.000000) translate(0,12)">#40杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2999.000000 -1041.000000) translate(0,12)">#42杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2999.000000 -980.000000) translate(0,12)">#44杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3060.000000 -898.000000) translate(0,12)">#49杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3060.000000 -639.000000) translate(0,12)">#82杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3015.000000 -280.000000) translate(0,12)">#83杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2994.000000 -795.000000) translate(0,12)">#69杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2969.000000 -1785.000000) translate(0,12)">BZ021</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3060.000000 -1725.000000) translate(0,12)">BZ031</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3063.000000 -1648.000000) translate(0,12)">BZ032</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2967.000000 -1552.000000) translate(0,12)">BZ022</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3089.000000 -1329.000000) translate(0,12)">BZ023</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2978.000000 -1244.000000) translate(0,12)">BZ024</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2977.000000 -924.000000) translate(0,12)">BZ026</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2986.000000 -659.000000) translate(0,12)">BZ028</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3089.000000 -814.000000) translate(0,12)">BZ027</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2966.000000 -328.000000) translate(0,12)">BZ029</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2813.000000 -1198.000000) translate(0,12)">BZ025</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1728.000000 -1531.000000) translate(0,12)">#9杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1719.000000 -1464.000000) translate(0,12)">#10杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1723.000000 -1256.000000) translate(0,12)">#23杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1716.000000 -1386.000000) translate(0,12)">#17杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1722.000000 -1177.000000) translate(0,12)">#30杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1717.000000 -1027.000000) translate(0,12)">BZ075</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1790.000000 -705.000000) translate(0,12)">Z0781</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1786.000000 -931.000000) translate(0,12)">Z0771</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1852.000000 -931.000000) translate(0,12)">Z077</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1783.000000 -1198.000000) translate(0,12)">Z0731</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1850.000000 -1198.000000) translate(0,12)">Z073</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1789.000000 -1003.000000) translate(0,12)">#32杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1722.000000 -908.000000) translate(0,12)">#34杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1722.000000 -690.000000) translate(0,12)">#37杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1825.000000 -1417.000000) translate(0,12)">Z0721</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1342.000000 -2120.000000) translate(0,12)">#11杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1342.000000 -2010.000000) translate(0,12)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1410.000000 -1738.000000) translate(0,12)">#26杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1350.000000 -1552.000000) translate(0,12)">#29杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1350.000000 -1467.000000) translate(0,12)">#31杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1407.000000 -1379.000000) translate(0,12)">#34杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1342.000000 -1232.000000) translate(0,12)">#46杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1407.000000 -1139.000000) translate(0,12)">#52杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1165.000000 -1021.000000) translate(0,12)">#3杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 945.000000 -1017.000000) translate(0,12)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 856.000000 -1016.000000) translate(0,12)">#5杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1341.000000 -1019.000000) translate(0,12)">Z0941</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1342.000000 -780.000000) translate(0,12)">#67杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1534.000000 -447.000000) translate(0,12)">S13-M-125kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1551.000000 -470.000000) translate(0,12)">蔡家</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1587.000000 -759.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1586.000000 -782.000000) translate(0,12)">五街</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1407.000000 -1004.000000) translate(0,12)">#62杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 835.000000 -879.000000) translate(0,12)">新村</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 823.000000 -853.000000) translate(0,12)">S11-80kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 931.000000 -852.000000) translate(0,12)">S11-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 949.000000 -878.000000) translate(0,12)">平掌</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 820.000000 -1431.000000) translate(0,12)">S11-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1246.000000 -287.000000) translate(0,12)">S9-100kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1245.000000 -310.000000) translate(0,8)">大坎子工段金矿私变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1051.000000 -735.000000) translate(0,12)">BZ094</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1059.000000 -894.000000) translate(0,12)">#12杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 519.000000 -1865.000000) translate(0,12)">BZ099</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 541.000000 -1588.000000) translate(0,8)">#115+1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 483.000000 -1501.000000) translate(0,8)">Z096(已坏）</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 395.000000 -1499.000000) translate(0,12)">#4杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1118.000000 -694.000000) translate(0,12)">#24杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 606.000000 -554.000000) translate(0,12)">#88杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 274.000000 -815.000000) translate(0,8)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 485.000000 -1073.000000) translate(0,12)">Z089</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 185.000000 -1074.000000) translate(0,12)">Z092</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 713.000000 -373.000000) translate(0,12)">林业局了望塔</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 740.000000 -350.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 481.000000 -362.000000) translate(0,12)">移动机站</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 482.000000 -339.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 606.000000 -499.000000) translate(0,12)">#87杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 565.000000 -375.000000) translate(0,8)">#87杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 533.000000 -311.000000) translate(0,12)">#87杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 613.000000 -311.000000) translate(0,20)">F0651</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 617.000000 -2245.000000) translate(0,20)">F0691</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 617.000000 -2162.000000) translate(0,20)">F0681</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1603.000000 -2351.000000) translate(0,20)">一</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1603.000000 -2351.000000) translate(0,45)">二</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1603.000000 -2351.000000) translate(0,70)">联</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1603.000000 -2351.000000) translate(0,95)">网</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1603.000000 -2351.000000) translate(0,120)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2657.000000 -1867.000000) translate(0,12)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2657.000000 -1867.000000) translate(0,27)">L0121</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 600.000000 -1840.000000) translate(0,12)">#120杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 557.000000 -1494.000000) translate(0,8)">#112杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2540.000000 -1930.000000) translate(0,12)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2540.000000 -1930.000000) translate(0,27)">F0121</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3068.000000 -1115.000000) translate(0,20)">F026</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3066.000000 -1163.000000) translate(0,20)">F0261</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2983.000000 -1136.000000) translate(0,12)">#41杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3349.000000 -1907.000000) translate(0,12)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3846.000000 -1369.000000) translate(0,12)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3846.000000 -1369.000000) translate(0,27)">F0461</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3846.000000 -1551.000000) translate(0,12)"> </text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3846.000000 -1551.000000) translate(0,27)">F0451</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3801.000000 -1516.000000) translate(0,12)">58</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3799.000000 -1338.000000) translate(0,12)">59</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1802.000000 -1732.000000) translate(0,20)">F0711</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1724.000000 -1730.000000) translate(0,12)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 808.000000 -1319.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 808.000000 -1319.000000) translate(0,27)">海</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 808.000000 -1319.000000) translate(0,42)">子</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 808.000000 -1319.000000) translate(0,57)">山</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 808.000000 -1319.000000) translate(0,72)">分</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 808.000000 -1319.000000) translate(0,87)">支</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 808.000000 -1319.000000) translate(0,102)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 868.000000 -1040.000000) translate(0,12)">Z0972</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1035.000000 -1017.000000) translate(0,12)">Z0971</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 855.000000 -1274.000000) translate(0,12)">Z0973</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 862.000000 -1300.000000) translate(0,12)">#11</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 322.000000 -1892.000000) translate(0,12)">10kV洼舍子支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 420.000000 -1540.000000) translate(0,12)">10kV洒树咪支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 318.000000 -1506.000000) translate(0,12)">#5杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 176.000000 -1508.000000) translate(0,12)">#7杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 235.000000 -486.000000) translate(0,12)">#26杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1335.000000 -1752.000000) translate(0,12)">BZ092</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1141.000000 -1738.000000) translate(0,12)">BZ093</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1140.000000 -1643.000000) translate(0,12)">BZ091</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1010.000000 -1699.000000) translate(0,12)">10kV杨梅树分支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 956.000000 -1660.000000) translate(0,12)">10kV小西洼分支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1196.000000 -1793.000000) translate(0,12)">10kV大西洼支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1407.000000 -1492.000000) translate(0,12)">BZ089</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1450.000000 -1428.000000) translate(0,12)">10kV旧村山支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1078.000000 -491.000000) translate(0,8)">#49杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1063.000000 -310.000000) translate(0,12)">#56杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1062.000000 -236.000000) translate(0,12)">#60杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1032.000000 -322.000000) translate(0,10)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1032.000000 -322.000000) translate(0,22)">米</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1032.000000 -322.000000) translate(0,34)">糠</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1032.000000 -322.000000) translate(0,46)">备</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1032.000000 -322.000000) translate(0,58)">米</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1032.000000 -322.000000) translate(0,70)">支</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1032.000000 -322.000000) translate(0,82)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1068.000000 -631.000000) translate(0,8)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1068.000000 -631.000000) translate(0,18)">米</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1068.000000 -631.000000) translate(0,28)">糠</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1068.000000 -631.000000) translate(0,38)">备</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1068.000000 -631.000000) translate(0,48)">米</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1068.000000 -631.000000) translate(0,58)">支</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1068.000000 -631.000000) translate(0,68)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 812.000000 -1078.000000) translate(0,12)">#8杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 777.000000 -1371.000000) translate(0,12)">#20杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1010.000000 -588.000000) translate(0,8)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1010.000000 -588.000000) translate(0,18)">洼</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1010.000000 -588.000000) translate(0,28)">木</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1010.000000 -588.000000) translate(0,38)">波</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1010.000000 -588.000000) translate(0,48)">分</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1010.000000 -588.000000) translate(0,58)">支</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1010.000000 -588.000000) translate(0,68)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1407.000000 -611.000000) translate(0,12)">BZ098</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1339.000000 -670.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1339.000000 -670.000000) translate(0,27)">多</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1339.000000 -670.000000) translate(0,42)">依</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1339.000000 -670.000000) translate(0,57)">树</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1339.000000 -670.000000) translate(0,72)">支</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1339.000000 -670.000000) translate(0,87)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1790.000000 -1508.000000) translate(0,12)">10kV诺掌支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1888.000000 -1417.000000) translate(0,12)">10kV坝硬支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1908.000000 -1193.000000) translate(0,12)">#4杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2082.000000 -1193.000000) translate(0,12)">#8杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2147.000000 -1190.000000) translate(0,12)">#12杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2303.000000 -1167.000000) translate(0,8)">#17杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1955.000000 -1214.000000) translate(0,12)">10kV小水井金矿支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1578.000000 -1019.000000) translate(0,12)">10kV苏布支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1584.000000 -992.000000) translate(0,12)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1498.000000 -1013.000000) translate(0,12)">#10杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1711.000000 -505.000000) translate(0,12)">#50杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2521.000000 -1861.000000) translate(0,12)">#11杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2768.000000 -1808.000000) translate(0,12)">10kV核桃树支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3128.000000 -1734.000000) translate(0,12)">10kV依扁啦支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2795.000000 -1559.000000) translate(0,12)">10kV洒木口支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3166.000000 -1357.000000) translate(0,12)">10kV树梅子支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2665.000000 -1270.000000) translate(0,12)">10kV峨苴支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2611.000000 -1199.000000) translate(0,12)">10kV纳广分支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3217.000000 -857.000000) translate(0,12)">10kV火平郎支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2797.000000 -690.000000) translate(0,12)">10kV新村支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2504.000000 -368.000000) translate(0,12)">10kV大水井支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2754.000000 -248.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2754.000000 -248.000000) translate(0,27)">干</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2754.000000 -248.000000) translate(0,42)">海</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2754.000000 -248.000000) translate(0,57)">子</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2754.000000 -248.000000) translate(0,72)">分</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2754.000000 -248.000000) translate(0,87)">支</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2754.000000 -248.000000) translate(0,102)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3770.000000 -1657.000000) translate(0,12)">#15杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3418.000000 -1700.000000) translate(0,12)">#20杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 528.000000 -1066.000000) translate(0,12)">Z0891</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1172.000000 -1062.000000) translate(0,12)">10kV米糠备米支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1282.000000 -1019.000000) translate(0,12)">Z094</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2592.000000 -1825.000000) translate(0,12)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2740.000000 -1136.000000) translate(0,12)">#1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2715.000000 -1176.000000) translate(0,8)">#14杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1003.000000 -1163.000000) translate(0,12)">#13</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1441.000000 -242.000000) translate(0,12)">S11-80kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1458.000000 -265.000000) translate(0,12)">陈家</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1346.000000 -283.000000) translate(0,12)">#19杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 176.000000 -1356.000000) translate(0,8)">#11杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 32.000000 -1302.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 37.000000 -1325.000000) translate(0,12)">三棵桩</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 300.000000 -1338.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 301.000000 -1362.000000) translate(0,12)">麻梨树</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 217.000000 -967.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 217.000000 -967.000000) translate(0,27)">马</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 217.000000 -967.000000) translate(0,42)">转</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 217.000000 -967.000000) translate(0,57)">路</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 217.000000 -967.000000) translate(0,72)">分</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 217.000000 -967.000000) translate(0,87)">支</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 217.000000 -967.000000) translate(0,102)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 77.000000 -551.000000) translate(0,12)">洋火塘</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 61.000000 -528.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 272.000000 -1223.000000) translate(0,8)">#14杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 732.000000 -854.000000) translate(0,12)">利么簸</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 728.000000 -830.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 542.000000 -890.000000) translate(0,12)">#98杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 660.000000 -1425.000000) translate(0,12)">洒树咪新村</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 679.000000 -1402.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 729.000000 -976.000000) translate(0,12)">依启么</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 725.000000 -952.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 610.000000 -982.000000) translate(0,8)">#101杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 781.000000 -1560.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 793.000000 -1580.000000) translate(0,12)">山尾巴</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 605.000000 -1625.000000) translate(0,12)">10kV山尾巴支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3988.000000 -1953.000000) translate(0,12)">假格力</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3991.000000 -1931.000000) translate(0,12)">D11-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3787.000000 -1955.000000) translate(0,12)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3841.000000 -1970.000000) translate(0,8)">10kV假格力支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1944.000000 -1681.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1953.000000 -1704.000000) translate(0,12)">红下村</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1807.000000 -1589.000000) translate(0,12)">10kV火石洞支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1726.000000 -1614.000000) translate(0,12)">#6杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1788.000000 -1636.000000) translate(0,12)">Z0711</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3399.000000 -778.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3399.000000 -778.000000) translate(0,27)">半山</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3399.000000 -778.000000) translate(0,42)">分支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3373.000000 -628.000000) translate(0,12)">半山</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3365.000000 -606.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3359.000000 -809.000000) translate(0,12)">#12杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 141.000000 -1368.000000) translate(0,8)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 216.000000 -1392.000000) translate(0,8)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3847.000000 -1928.000000) translate(0,12)">#1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1938.000000 -1605.000000) translate(0,12)">D11-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1919.000000 -1632.000000) translate(0,12)">火石洞公变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 862.000000 -1120.000000) translate(0,12)">明家分支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 888.000000 -1348.000000) translate(0,12)">下洒打</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 895.000000 -1326.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 936.000000 -1150.000000) translate(0,12)">#4</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 943.000000 -1184.000000) translate(0,12)">#1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1455.000000 -657.000000) translate(0,12)">S11-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1453.000000 -680.000000) translate(0,12)">袁家公变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1400.000000 -757.000000) translate(0,8)"> #1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 659.000000 -698.000000) translate(0,12)">俄旧米公变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 655.000000 -673.000000) translate(0,12)">S11-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 545.000000 -728.000000) translate(0,12)">#94杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 605.000000 -875.000000) translate(0,8)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 556.000000 -1001.000000) translate(0,8)">#101杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 605.000000 -712.000000) translate(0,8)">#94杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2582.000000 -496.000000) translate(0,12)">干巴村公变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2594.000000 -475.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2726.000000 -647.000000) translate(0,8)">#14杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2626.000000 -649.000000) translate(0,8)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2668.000000 -605.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2668.000000 -605.000000) translate(0,27)">干巴</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2668.000000 -605.000000) translate(0,42)">村分</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2668.000000 -605.000000) translate(0,57)">支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2056.000000 -1644.000000) translate(0,12)">田尾巴公变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2051.000000 -1622.000000) translate(0,12)">D11-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2079.000000 -1452.000000) translate(0,12)">#2</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2103.000000 -1562.000000) translate(0,12)">10kV田尾巴</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2103.000000 -1562.000000) translate(0,27)">分支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1911.000000 -1485.000000) translate(0,12)">10kV大路边支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 675.000000 -1179.000000) translate(0,12)">洒树咪多依树</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 698.000000 -1153.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 528.000000 -1203.000000) translate(0,12)">#108杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 600.000000 -1184.000000) translate(0,8)">#108杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1447.000000 -761.000000) translate(0,8)">Z0951</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1445.000000 -739.000000) translate(0,12)">10kV袁家支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 892.000000 -2260.000000) translate(0,12)">#1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 819.000000 -2244.000000) translate(0,12)">Z0531</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 897.000000 -2230.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 897.000000 -2230.000000) translate(0,27)">{\W0.446565; 野</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 897.000000 -2230.000000) translate(0,42)">\W0.516878; 利</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 897.000000 -2230.000000) translate(0,57)">\W0.415315; 渣</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 897.000000 -2230.000000) translate(0,72)">\W0.470003; 分</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 897.000000 -2230.000000) translate(0,87)">\W0.470003; 支</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 897.000000 -2230.000000) translate(0,102)">\W0.52469; 线}</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 826.000000 -2062.000000) translate(0,12)">野利渣</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 818.000000 -2042.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 202.000000 -1168.000000) translate(0,12)">       #1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 458.000000 -687.000000) translate(0,8)">土固力</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 445.000000 -667.000000) translate(0,8)">D11-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 59.000000 -1557.000000) translate(0,12)">洒树格</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 55.000000 -1534.000000) translate(0,12)">D11-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 605.000000 -1659.000000) translate(0,12)">#119杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 971.000000 -1921.000000) translate(0,12)">大丫口</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 957.000000 -1901.000000) translate(0,12)">D11-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1218.000000 -1230.000000) translate(0,12)">D11-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1222.000000 -1259.000000) translate(0,12)">齐堵河</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1344.000000 -463.000000) translate(0,12)">#05杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1344.000000 -362.000000) translate(0,12)">#13杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1896.000000 -1834.000000) translate(0,12)">#08杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1729.000000 -1814.000000) translate(0,12)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1718.000000 -455.000000) translate(0,12)">#52杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2996.000000 -1496.000000) translate(0,12)">#33杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2883.000000 -776.000000) translate(0,12)">  腊曲</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 849.000000 -2298.000000) translate(0,12)">#10杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2710.000000 -976.000000) translate(0,12)">D11-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2711.000000 -999.000000) translate(0,12)">石格拉</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2256.000000 -438.000000) translate(0,12)">泥洼得</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2238.000000 -416.000000) translate(0,12)">D11-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2242.000000 -285.000000) translate(0,12)">#30杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1257.000000 -826.000000) translate(0,12)">大麦地</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1258.000000 -797.000000) translate(0,12)">S11-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1059.000000 -828.000000) translate(0,12)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3782.000000 -1881.000000) translate(0,12)">#3杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 642.000000 -1317.000000) translate(0,12)">洒树咪电信基站</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 669.000000 -1297.000000) translate(0,12)">S11-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 532.000000 -1349.000000) translate(0,12)">#111杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 848.000000 -463.000000) translate(0,12)">小麻树电信基站</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 888.000000 -443.000000) translate(0,12)">S11-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1099.000000 -1019.000000) translate(0,12)">#6杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1125.000000 -728.000000) translate(0,12)">#23杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1075.000000 -699.000000) translate(0,8)">BZ096</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3132.000000 -1540.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3127.000000 -1567.000000) translate(0,12)">七街抽水站</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3848.000000 -2044.000000) translate(0,20)">F041</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3844.000000 -2075.000000) translate(0,20)">F0411</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3773.000000 -2037.000000) translate(0,12)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 466.000000 -510.000000) translate(0,8)">Z0852</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="7" transform="matrix(1.000000 0.000000 0.000000 1.000000 496.000000 -478.000000) translate(0,6)">#4杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 356.000000 -478.000000) translate(0,8)">10kV松毛利支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3062.000000 -1766.000000) translate(0,12)">#19杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2975.000000 -1740.000000) translate(0,8)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3067.000000 -1684.000000) translate(0,8)">#20杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3060.000000 -1610.000000) translate(0,8)">#24杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2979.000000 -1510.000000) translate(0,8)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3057.000000 -1474.000000) translate(0,8)">#33杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3080.000000 -1287.000000) translate(0,8)">#01杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3215.000000 -1319.000000) translate(0,8)">#08杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3319.000000 -1320.000000) translate(0,8)">#14杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3464.000000 -1327.000000) translate(0,8)">#27杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2980.000000 -879.000000) translate(0,8)">#49杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3086.000000 -769.000000) translate(0,12)">#69杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2989.000000 -620.000000) translate(0,8)">#82杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2975.000000 -280.000000) translate(0,8)">#01杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2764.000000 -1197.000000) translate(0,8)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2778.000000 -1229.000000) translate(0,8)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2594.000000 -1242.000000) translate(0,8)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2876.000000 -1534.000000) translate(0,8)">#10杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2747.000000 -1529.000000) translate(0,8)">#14杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2887.000000 -1774.000000) translate(0,12)">#17杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2794.000000 -1774.000000) translate(0,12)">#18杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2695.000000 -1772.000000) translate(0,12)">#21杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2803.000000 -1167.000000) translate(0,8)">#11杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2658.000000 -1175.000000) translate(0,8)">#14杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2550.000000 -1228.000000) translate(0,8)">#24杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2458.000000 -1233.000000) translate(0,8)">#32杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2984.000000 -1202.000000) translate(0,12)">#40杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2897.000000 -905.000000) translate(0,8)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2719.000000 -901.000000) translate(0,8)">#10杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2585.000000 -909.000000) translate(0,8)">#13杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3246.000000 -810.000000) translate(0,12)">#4杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2931.000000 -651.000000) translate(0,8)">#06杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2822.000000 -650.000000) translate(0,8)">#09杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2417.000000 -650.000000) translate(0,8)">#37杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2530.000000 -650.000000) translate(0,8)">#28杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2800.000000 -316.000000) translate(0,8)">#17杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2680.000000 -128.000000) translate(0,12)">坝塘</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2689.000000 -315.000000) translate(0,8)">#19杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2874.000000 -38.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2882.000000 -62.000000) translate(0,12)">自么苴</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2518.000000 -316.000000) translate(0,8)">#28杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2779.000000 -221.000000) translate(0,8)">#07杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2188.000000 -308.000000) translate(0,8)">#33杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1789.000000 -1608.000000) translate(0,8)">#6杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1843.000000 -1380.000000) translate(0,8)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2058.000000 -1403.000000) translate(0,8)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2156.000000 -1405.000000) translate(0,8)">#4杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1823.000000 -1151.000000) translate(0,12)">#01杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1702.000000 -992.000000) translate(0,8)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1798.000000 -892.000000) translate(0,12)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1979.000000 -704.000000) translate(0,12)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2130.000000 -720.000000) translate(0,12)">#08杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1336.000000 -1717.000000) translate(0,12)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1211.000000 -1722.000000) translate(0,12)">#9杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1208.000000 -1623.000000) translate(0,12)">#12杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1210.000000 -1558.000000) translate(0,12)">#14杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1209.000000 -1484.000000) translate(0,12)">#20杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1412.000000 -1445.000000) translate(0,8)">#31杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1409.000000 -784.000000) translate(0,8)"> #1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1364.000000 -632.000000) translate(0,8)">#68杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1418.000000 -347.000000) translate(0,12)">#01杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1542.000000 -379.000000) translate(0,12)">#10杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1688.000000 -379.000000) translate(0,12)">#18杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1613.000000 -381.000000) translate(0,12)">#17杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1305.000000 -974.000000) translate(0,12)">#62杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1000.000000 -617.000000) translate(0,8)">#7杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 936.000000 -625.000000) translate(0,8)">#14+1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 969.000000 -675.000000) translate(0,8)">#5杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1041.000000 -985.000000) translate(0,12)">#6杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 971.000000 -424.000000) translate(0,8)">#5杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 988.000000 -363.000000) translate(0,8)">#10杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1084.000000 -581.000000) translate(0,8)">#42杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1080.000000 -771.000000) translate(0,8)">#20杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1082.000000 -651.000000) translate(0,8)">#35杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 237.000000 -1072.000000) translate(0,12)">Z0921</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 311.000000 -1067.000000) translate(0,12)">#10杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 204.000000 -1023.000000) translate(0,12)">#10杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 115.000000 -1027.000000) translate(0,12)">#21杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 388.000000 -1067.000000) translate(0,12)">#6杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 492.000000 -1027.000000) translate(0,12)">#105杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 122.000000 -1066.000000) translate(0,12)">Z0922</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 278.000000 -731.000000) translate(0,8)">#9杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 234.000000 -1505.000000) translate(0,12)">#7杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 507.000000 -501.000000) translate(0,8)">#4杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 553.000000 -519.000000) translate(0,8)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 493.000000 -1466.000000) translate(0,12)">#112杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 522.000000 -1821.000000) translate(0,12)">#120杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 460.000000 -1846.000000) translate(0,8)">#4杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 338.000000 -1845.000000) translate(0,8)">#8杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 227.000000 -1847.000000) translate(0,8)">#9杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 140.000000 -1487.000000) translate(0,8)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 116.000000 -1343.000000) translate(0,8)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 260.000000 -1246.000000) translate(0,12)">       #2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 312.000000 -802.000000) translate(0,8)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 328.000000 -738.000000) translate(0,8)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 309.000000 -549.000000) translate(0,8)">#24杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3921.000000 -2017.000000) translate(0,12)">BZ061</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 4090.000000 -1971.000000) translate(0,8)">大地基线电容器组</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 977.000000 -1711.000000) translate(0,8)">#09杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1144.000000 -1603.000000) translate(0,12)">#12杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3835.000000 -1954.000000) translate(0,12)">Z0612</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3056.000000 -1512.000000) translate(0,12)">Z0331</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3325.000000 -772.000000) translate(0,12)">Z0276</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2588.000000 -626.000000) translate(0,12)">Z0286</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2739.000000 -1160.000000) translate(0,8)">Z0253</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2086.000000 -1494.000000) translate(0,8)">Z0795</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 993.000000 -1734.000000) translate(0,12)">Z0926</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1402.000000 -276.000000) translate(0,8)">#2杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 887.000000 -1174.000000) translate(0,8)">Z0978</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 604.000000 -747.000000) translate(0,8)">Z0941</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 614.000000 -905.000000) translate(0,8)">Z0942</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 607.000000 -1010.000000) translate(0,8)">Z0943</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 615.000000 -1230.000000) translate(0,8)">Z0951</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 611.000000 -1365.000000) translate(0,8)">Z0952</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 632.000000 -1455.000000) translate(0,8)">Z0953</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 319.000000 -824.000000) translate(0,8)">Z0925</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 348.000000 -727.000000) translate(0,8)">Z0926</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 250.000000 -1410.000000) translate(0,8)">Z0965</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 127.000000 -1380.000000) translate(0,8)">Z0968</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1926.000000 -1782.000000) translate(0,8)">Z0741</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 602.000000 -1045.000000) translate(0,8)">#105杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 243.000000 -1212.000000) translate(0,8)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 243.000000 -1212.000000) translate(0,18)">邑</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 243.000000 -1212.000000) translate(0,28)">朵</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 243.000000 -1212.000000) translate(0,38)">簸</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 243.000000 -1212.000000) translate(0,48)">分</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 243.000000 -1212.000000) translate(0,58)">支</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 243.000000 -1212.000000) translate(0,68)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11.000000 -1045.000000) translate(0,12)">中邑舍</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 270.000000 -632.000000) translate(0,8)">#15杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3693.000000 -1972.000000) translate(0,20)">L0411</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 543.000000 -506.000000) translate(0,12)">BZ085</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 617.000000 -2216.000000) translate(0,12)"> 高压</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 617.000000 -2216.000000) translate(0,27)">计量箱</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 520.000000 -2182.000000) translate(0,12)">#131杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2052.000000 -532.000000) translate(0,12)">小水井金矿专变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2054.000000 -505.000000) translate(0,12)">S11-M-200kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1918.000000 -563.000000) translate(0,12)">大田领干2号变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2044.000000 -673.000000) translate(0,12)">Z0772</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2381.000000 -1051.000000) translate(0,12)">S11-M-400kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2409.000000 -1135.000000) translate(0,12)">小水井金矿2号变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2375.000000 -1182.000000) translate(0,8)">#19杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2215.000000 -1199.000000) translate(0,12)">K074</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2194.000000 -1153.000000) translate(0,12)">K0741</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2268.000000 -1196.000000) translate(0,10)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 246.000000 -557.000000) translate(0,8)">#01杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 559.000000 -567.000000) translate(0,8)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3420.000000 -828.000000) translate(0,12)">#13杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3391.000000 -1320.000000) translate(0,8)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2618.000000 -1229.000000) translate(0,8)">BZ035</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1874.000000 -1790.000000) translate(0,12)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2267.000000 -312.000000) translate(0,8)">Z0296</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1792.000000 -1520.000000) translate(0,8)">BZ076</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3763.000000 -1999.000000) translate(0,12)">#1+1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 414.000000 -1031.000000) translate(0,8)">Z0911</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 193.000000 -1465.000000) translate(0,8)">BZ014</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 314.000000 -1464.000000) translate(0,8)">BZ015</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 543.000000 -581.000000) translate(0,12)">BZ016</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 327.000000 -1215.000000) translate(0,8)">Z0915</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 244.000000 -527.000000) translate(0,8)">Z0928</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3379.000000 -1288.000000) translate(0,12)">BZ036</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2837.000000 -284.000000) translate(0,8)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2554.000000 -282.000000) translate(0,8)">BZ045</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2721.000000 -274.000000) translate(0,8)">BZ044</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2674.000000 -655.000000) translate(0,8)">#15杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2841.000000 -621.000000) translate(0,8)">BZ042</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2847.000000 -928.000000) translate(0,12)">10kV腊曲支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2748.000000 -884.000000) translate(0,8)">K0271</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1120.000000 -842.000000) translate(0,8)">BZ097</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1124.000000 -284.000000) translate(0,8)">K0981</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 923.000000 -592.000000) translate(0,8)">K0971</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 619.000000 -1571.000000) translate(0,12)">Z0954</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1127.000000 -348.000000) translate(0,12)">10kV大坎子金矿分支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 864.000000 -628.000000) translate(0,8)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2635.000000 -267.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2635.000000 -267.000000) translate(0,27)">坝</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2635.000000 -267.000000) translate(0,42)">塘</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2635.000000 -267.000000) translate(0,57)">分</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2635.000000 -267.000000) translate(0,72)">支</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2635.000000 -267.000000) translate(0,87)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2470.000000 -263.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2470.000000 -263.000000) translate(0,27)">嘎</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2470.000000 -263.000000) translate(0,42)">苴</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2470.000000 -263.000000) translate(0,57)">分</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2470.000000 -263.000000) translate(0,72)">支</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2470.000000 -263.000000) translate(0,87)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 646.000000 -1064.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 621.000000 -1082.000000) translate(0,12)">洒树咪村委会</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 651.000000 -1215.000000) translate(0,8)">#2</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="7" transform="matrix(1.000000 0.000000 0.000000 1.000000 931.000000 -566.000000) translate(0,6)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="7" transform="matrix(1.000000 0.000000 0.000000 1.000000 931.000000 -566.000000) translate(0,13)">小麻</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="7" transform="matrix(1.000000 0.000000 0.000000 1.000000 931.000000 -566.000000) translate(0,20)">树电</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="7" transform="matrix(1.000000 0.000000 0.000000 1.000000 931.000000 -566.000000) translate(0,27)">信基</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="7" transform="matrix(1.000000 0.000000 0.000000 1.000000 931.000000 -566.000000) translate(0,34)">站分</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="7" transform="matrix(1.000000 0.000000 0.000000 1.000000 931.000000 -566.000000) translate(0,41)">支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2066.000000 -1161.000000) translate(0,8)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2066.000000 -1161.000000) translate(0,18)">利</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2066.000000 -1161.000000) translate(0,28)">苏</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2066.000000 -1161.000000) translate(0,38)">打</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2066.000000 -1161.000000) translate(0,48)">分</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2066.000000 -1161.000000) translate(0,58)">支</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2066.000000 -1161.000000) translate(0,68)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2786.000000 -865.000000) translate(0,8)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2786.000000 -865.000000) translate(0,18)">杨梅</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2786.000000 -865.000000) translate(0,28)">树电</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2786.000000 -865.000000) translate(0,38)">信基</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2786.000000 -865.000000) translate(0,48)">站分</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2786.000000 -865.000000) translate(0,58)">支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2864.000000 -603.000000) translate(0,8)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2864.000000 -603.000000) translate(0,18)">平</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2864.000000 -603.000000) translate(0,28)">郎</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2864.000000 -603.000000) translate(0,38)">井</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2864.000000 -603.000000) translate(0,48)">分</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2864.000000 -603.000000) translate(0,58)">支</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2864.000000 -603.000000) translate(0,68)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 625.000000 -100.000000) translate(0,20)">0561</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2084.000000 -704.000000) translate(0,12)">#04杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2825.000000 -445.000000) translate(0,12)">坝塘移动基站专变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2851.000000 -423.000000) translate(0,12)">D11-5kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2876.000000 -290.000000) translate(0,12)">#16杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2892.000000 -311.000000) translate(0,8)">K0294</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2663.000000 -621.000000) translate(0,8)">BZ041</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2828.000000 -263.000000) translate(0,8)">BZ043</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1421.000000 -2453.000000) translate(0,20)">041</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="7" transform="matrix(1.000000 0.000000 0.000000 1.000000 1068.000000 -409.000000) translate(0,6)">石场2号变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="7" transform="matrix(1.000000 0.000000 0.000000 1.000000 1039.000000 -368.000000) translate(0,6)">石场1号变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="7" transform="matrix(1.000000 0.000000 0.000000 1.000000 1066.000000 -397.000000) translate(0,6)">S11-400kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="7" transform="matrix(1.000000 0.000000 0.000000 1.000000 1040.000000 -355.000000) translate(0,6)">S7-125kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="7" transform="matrix(1.000000 0.000000 0.000000 1.000000 1002.000000 -433.000000) translate(0,6)">K0951</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="7" transform="matrix(1.000000 0.000000 0.000000 1.000000 1024.000000 -433.000000) translate(0,6)"> K095</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="7" transform="matrix(1.000000 0.000000 0.000000 1.000000 1006.000000 -407.000000) translate(0,6)">1号杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="7" transform="matrix(1.000000 0.000000 0.000000 1.000000 1011.000000 -451.000000) translate(0,6)">10kV石场支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="7" transform="matrix(1.000000 0.000000 0.000000 1.000000 1054.000000 -430.000000) translate(0,6)">5号杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="7" transform="matrix(1.000000 0.000000 0.000000 1.000000 1082.000000 -435.000000) translate(0,6)">6号杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2938.000000 -1699.000000) translate(0,8)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2938.000000 -1699.000000) translate(0,18)">大</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2938.000000 -1699.000000) translate(0,28)">村</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2938.000000 -1699.000000) translate(0,38)">分</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2938.000000 -1699.000000) translate(0,48)">支</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2938.000000 -1699.000000) translate(0,58)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2847.000000 -1705.000000) translate(0,8)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2847.000000 -1705.000000) translate(0,18)">田</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2847.000000 -1705.000000) translate(0,28)">口</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2847.000000 -1705.000000) translate(0,38)">分</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2847.000000 -1705.000000) translate(0,48)">支</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2847.000000 -1705.000000) translate(0,58)">线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2883.000000 -1685.000000) translate(0,8)">#6杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2791.000000 -1681.000000) translate(0,8)">#8杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 515.000000 -1439.000000) translate(0,12)">#111+1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1788.000000 -1560.000000) translate(0,12)">#1杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2923.000000 -1732.000000) translate(0,12)">BZ051</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2830.000000 -1733.000000) translate(0,12)">BZ052</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3869.000000 -1906.000000) translate(0,12)">10kV李万泽专变支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3860.000000 -1888.000000) translate(0,12)">#1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3843.000000 -1857.000000) translate(0,12)">BK062</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1028.000000 -1368.000000) translate(0,10)">张明有专变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1014.000000 -1384.000000) translate(0,10)">S11-M-100kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1026.000000 -1298.000000) translate(0,10)">10kV张明有专变支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1073.000000 -1248.000000) translate(0,10)">K0943</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1082.000000 -1227.000000) translate(0,8)">#1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1076.000000 -1343.000000) translate(0,8)">#2</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 915.000000 -1201.000000) translate(0,8)">#9</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1064.000000 -1192.000000) translate(0,8)">#10</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 989.000000 -1182.000000) translate(0,10)">10kV下洒打分支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1441.000000 -855.000000) translate(0,12)">S13-M-80kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1990.000000 -1188.000000) translate(0,10)">#6杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2018.000000 -1157.000000) translate(0,10)">10kV金矿生活支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1967.000000 -1154.000000) translate(0,10)">K0733</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2020.000000 -1046.000000) translate(0,10)">金矿生活专变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1953.000000 -990.000000) translate(0,10)">S11-M-160kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2324.000000 -1277.000000) translate(0,10)">10kV金矿抽水站支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2318.000000 -1447.000000) translate(0,10)">金矿抽水站专变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2317.000000 -1460.000000) translate(0,10)">S11-M-200kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2371.000000 -1207.000000) translate(0,10)">K0752</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2371.000000 -1319.000000) translate(0,10)">K075</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2371.000000 -1275.000000) translate(0,10)">K0751</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1918.000000 -932.000000) translate(0,12)">10kV大田领干支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1971.000000 -857.000000) translate(0,10)">10kV金矿破碎站支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2148.000000 -856.000000) translate(0,10)">金矿破碎站专变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2182.000000 -821.000000) translate(0,10)">S11-M-250kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2087.000000 -818.000000) translate(0,10)">K076</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1979.000000 -821.000000) translate(0,10)">K0762</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2060.000000 -821.000000) translate(0,10)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1991.000000 -841.000000) translate(0,8)">#1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 2070.000000 -841.000000) translate(0,8)">#2</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1952.000000 -910.000000) translate(0,8)">#5</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1005.000000 -671.000000) translate(0,8)">10kV洼木波分支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1544.000000 -2112.000000) translate(0,12)">S11-M-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1100.000000 -1763.000000) translate(0,12)">Z0924</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1095.000000 -1710.000000) translate(0,8)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1087.000000 -1745.000000) translate(0,8)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1068.000000 -1816.000000) translate(0,8)">10kV易先存支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1111.000000 -1882.000000) translate(0,12)">易先存专变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1068.000000 -1899.000000) translate(0,12)">S11-M-20kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1707.000000 -1346.000000) translate(0,12)">#21杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1800.000000 -1345.000000) translate(0,20)">F071</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1800.000000 -1307.000000) translate(0,20)">F0712</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1713.000000 -655.000000) translate(0,12)">#37杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1800.000000 -654.000000) translate(0,20)">F072</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1800.000000 -616.000000) translate(0,20)">F0721</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 4043.000000 -24.000000) translate(0,10)">2012年11月15日</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3529.000000 -114.000000) translate(0,10)">高压计量箱</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3540.000000 -35.000000) translate(0,10)">跌落保险</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3540.000000 -143.000000) translate(0,10)">断路器</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3539.000000 -183.000000) translate(0,10)">隔离开关</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3538.000000 -67.000000) translate(0,10)">变压器</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3521.000000 -338.000000) translate(0,10)">为私变</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3520.000000 -280.000000) translate(0,10)">为公变</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(1.000000 0.000000 0.000000 1.000000 3000.000000 -2348.000000) translate(0,40)">10kV母线</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3917.000000 -24.000000) translate(0,10)">更新日期</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3907.000000 -101.000000) translate(0,10)"> 新村供电所区域电网接线图</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 0.000000 0.000000 1.000000 2913.000000 -2497.000000) translate(0,14)"> </text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 0.000000 0.000000 1.000000 2913.000000 -2497.000000) translate(0,31)">35kV新村变电站</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1448.000000 -2884.000000) translate(0,10)"> 新村供电所电网接线图</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3660.000000 -291.000000) translate(0,10)">注：绿色框内及红色部份为本次更改内容。</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3475.000000 -220.000000) translate(0,10)"> 图  例</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3646.000000 -141.000000) translate(0,10)">审　定</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3648.000000 -64.000000) translate(0,10)">校　核</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3648.000000 -22.000000) translate(0,10)">制  图</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3647.000000 -98.000000) translate(0,10)">审　核</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3694.000000 -194.000000) translate(0,18)">楚 雄 市 供 电 有 限  公 司</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3776.000000 -62.000000) translate(0,12)">赵希云</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3775.000000 -25.000000) translate(0,12)">李文富</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3780.000000 -142.000000) translate(0,12)">刘福紧</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3780.000000 -99.000000) translate(0,12)">李晓清</text>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="0:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer10="标注、文字:0.000000 0.000000" layer11="图框（粗实线）:0.000000 0.000000" layer12="虚线0.2:0.000000 0.000000" layer13="粗线:0.000000 0.000000" layer14="细实线:0.000000 0.000000" layer15="填充层:0.000000 0.000000" layer16="轮廓:0.000000 0.000000" layer17="配电接线层:0.000000 0.000000" layer18="设备（实线）:0.000000 0.000000" layer19="标注线层:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer20="10kV母线:0.000000 0.000000" layer21="35kV母线:0.000000 0.000000" layer22="高压电缆0.35:0.000000 0.000000" layer23="实线:0.000000 0.000000" layer24="10KV线路:0.000000 0.000000" layer25="虚线:0.000000 0.000000" layer26="一二期农网:0.000000 0.000000" layer27="文字层:0.000000 0.000000" layer28="村委会、变电站、变压器:0.000000 0.000000" layer29="西部电网完善线路:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layer30="30万无电通电线路:0.000000 0.000000" layer31="未改造过的线路:0.000000 0.000000" layer32="0:0.000000 0.000000" layer33="主干线:0.000000 0.000000" layer34="次干线:0.000000 0.000000" layer35="支线:0.000000 0.000000" layer36="公变:0.000000 0.000000" layer37="自变:0.000000 0.000000" layer38="图框（细实线）:0.000000 0.000000" layer39="标注、文字:0.000000 0.000000" layer4="主干线:0.000000 0.000000" layer40="图框（粗实线）:0.000000 0.000000" layer41="虚线0.2:0.000000 0.000000" layer42="粗线:0.000000 0.000000" layer43="细实线:0.000000 0.000000" layer44="填充层:0.000000 0.000000" layer45="轮廓:0.000000 0.000000" layer46="配电接线层:0.000000 0.000000" layer47="设备（实线）:0.000000 0.000000" layer48="标注线层:0.000000 0.000000" layer49="10kV母线:0.000000 0.000000" layer5="次干线:0.000000 0.000000" layer50="35kV母线:0.000000 0.000000" layer51="高压电缆0.35:0.000000 0.000000" layer52="实线:0.000000 0.000000" layer53="10KV线路:0.000000 0.000000" layer54="虚线:0.000000 0.000000" layer55="一二期农网:0.000000 0.000000" layer56="文字层:0.000000 0.000000" layer57="村委会、变电站、变压器:0.000000 0.000000" layer58="西部电网完善线路:0.000000 0.000000" layer59="30万无电通电线路:0.000000 0.000000" layer6="支线:0.000000 0.000000" layer60="未改造过的线路:0.000000 0.000000" layer61="PUBLIC:0.000000 0.000000" layer7="公变:0.000000 0.000000" layer8="自变:0.000000 0.000000" layer9="图框（细实线）:0.000000 0.000000" layerN="62"/>
</svg>