<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-232" aopId="3934726" id="thSvg" product="E8000V2" version="1.0" viewBox="-743 -1269 2089 1195">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="switch2:shape30_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape14_0">
    <circle cx="37" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="84" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="70" x2="68" y1="84" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="45" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="28" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="28" x2="45" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape14_1">
    <ellipse cx="37" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="67" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="59" y2="67"/>
   </symbol>
   <symbol id="transformer2:shape35_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="16" y1="51" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="16,14 10,26 22,26 16,14 16,15 16,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="51"/>
   </symbol>
   <symbol id="transformer2:shape35_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="82" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="81" y2="76"/>
   </symbol>
   <symbol id="voltageTransformer:shape21">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="27" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="11" y2="5"/>
    <circle cx="27" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="13" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape57">
    <circle cx="18" cy="16" fillStyle="0" r="16.5" stroke-width="0.340267"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.374294" x1="45" x2="39" y1="30" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="45" x2="39" y1="23" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="39" x2="39" y1="33" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="16" x2="11" y1="14" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="21" x2="16" y1="19" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="16" x2="16" y1="9" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="17" x2="12" y1="40" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="22" x2="17" y1="45" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="17" x2="17" y1="34" y2="40"/>
    <ellipse cx="38" cy="28" fillStyle="0" rx="16.5" ry="16" stroke-width="0.340267"/>
    <circle cx="17" cy="37" fillStyle="0" r="16.5" stroke-width="0.340267"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="14"/>
   </symbol>
   <symbol id="voltageTransformer:shape11">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="11" y2="9"/>
    <circle cx="15" cy="19" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="24" cy="16" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="11" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="7" cy="16" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="16" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="19" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="9" y2="7"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_278c9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_278d3f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_278dd60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_278ea00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_278fc60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2790880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2790fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2791a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2792310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2792bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2792bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2794970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2794970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2795840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2797470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2798080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2798a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2799340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279ab20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279b320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279ba10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_279c430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279d610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279df90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_279ea80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_27a3d40" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27a49d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_27a07d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_27a1cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27a2750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_27b1230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_27a6880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1205" width="2099" x="-748" y="-1274"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="-405" y="-1190"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-181826">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 418.241796 -828.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27692" ObjectName="SW-DY_WB.DY_WB_3011SW"/>
     <cge:Meas_Ref ObjectId="181826"/>
    <cge:TPSR_Ref TObjectID="27692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181833">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 416.241796 -549.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27694" ObjectName="SW-DY_WB.DY_WB_0011SW"/>
     <cge:Meas_Ref ObjectId="181833"/>
    <cge:TPSR_Ref TObjectID="27694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181806">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 420.881701 -911.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27688" ObjectName="SW-DY_WB.DY_WB_3911SW"/>
     <cge:Meas_Ref ObjectId="181806"/>
    <cge:TPSR_Ref TObjectID="27688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181807">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 420.881701 -1045.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27689" ObjectName="SW-DY_WB.DY_WB_3916SW"/>
     <cge:Meas_Ref ObjectId="181807"/>
    <cge:TPSR_Ref TObjectID="27689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 496.203031 -1114.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181808">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 323.761290 -1063.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27690" ObjectName="SW-DY_WB.DY_WB_39167SW"/>
     <cge:Meas_Ref ObjectId="181808"/>
    <cge:TPSR_Ref TObjectID="27690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181883">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -201.238710 -307.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27703" ObjectName="SW-DY_WB.DY_WB_0926SW"/>
     <cge:Meas_Ref ObjectId="181883"/>
    <cge:TPSR_Ref TObjectID="27703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181882">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -201.238710 -449.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27702" ObjectName="SW-DY_WB.DY_WB_0921SW"/>
     <cge:Meas_Ref ObjectId="181882"/>
    <cge:TPSR_Ref TObjectID="27702"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181866">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 85.761290 -308.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27700" ObjectName="SW-DY_WB.DY_WB_0916SW"/>
     <cge:Meas_Ref ObjectId="181866"/>
    <cge:TPSR_Ref TObjectID="27700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181865">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 85.761290 -450.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27699" ObjectName="SW-DY_WB.DY_WB_0911SW"/>
     <cge:Meas_Ref ObjectId="181865"/>
    <cge:TPSR_Ref TObjectID="27699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181900">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 374.761290 -305.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27706" ObjectName="SW-DY_WB.DY_WB_0936SW"/>
     <cge:Meas_Ref ObjectId="181900"/>
    <cge:TPSR_Ref TObjectID="27706"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181899">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 374.761290 -447.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27705" ObjectName="SW-DY_WB.DY_WB_0931SW"/>
     <cge:Meas_Ref ObjectId="181899"/>
    <cge:TPSR_Ref TObjectID="27705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181917">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 661.761290 -307.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27709" ObjectName="SW-DY_WB.DY_WB_0946SW"/>
     <cge:Meas_Ref ObjectId="181917"/>
    <cge:TPSR_Ref TObjectID="27709"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181916">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 661.761290 -449.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27708" ObjectName="SW-DY_WB.DY_WB_0941SW"/>
     <cge:Meas_Ref ObjectId="181916"/>
    <cge:TPSR_Ref TObjectID="27708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181934">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 934.761290 -307.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27712" ObjectName="SW-DY_WB.DY_WB_0956SW"/>
     <cge:Meas_Ref ObjectId="181934"/>
    <cge:TPSR_Ref TObjectID="27712"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181933">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 934.761290 -449.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27711" ObjectName="SW-DY_WB.DY_WB_0951SW"/>
     <cge:Meas_Ref ObjectId="181933"/>
    <cge:TPSR_Ref TObjectID="27711"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1196.203031 -433.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1116.203031 -796.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -227.796969 -623.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181859">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -232.238710 -557.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27697" ObjectName="SW-DY_WB.DY_WB_0901SW"/>
     <cge:Meas_Ref ObjectId="181859"/>
    <cge:TPSR_Ref TObjectID="27697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181854">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1034.881701 -908.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27695" ObjectName="SW-DY_WB.DY_WB_3901SW"/>
     <cge:Meas_Ref ObjectId="181854"/>
    <cge:TPSR_Ref TObjectID="27695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181855">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 954.881701 -1003.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27696" ObjectName="SW-DY_WB.DY_WB_39017SW"/>
     <cge:Meas_Ref ObjectId="181855"/>
    <cge:TPSR_Ref TObjectID="27696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194237">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 42.544703 -977.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29499" ObjectName="SW-DY_WB.DY_WB_39317SW"/>
     <cge:Meas_Ref ObjectId="194237"/>
    <cge:TPSR_Ref TObjectID="29499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194233">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.881701 -910.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29495" ObjectName="SW-DY_WB.DY_WB_3931SW"/>
     <cge:Meas_Ref ObjectId="194233"/>
    <cge:TPSR_Ref TObjectID="29495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194234">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.881701 -1076.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29496" ObjectName="SW-DY_WB.DY_WB_3936SW"/>
     <cge:Meas_Ref ObjectId="194234"/>
    <cge:TPSR_Ref TObjectID="29496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 82.203031 -1118.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194235">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -76.455297 -1131.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29497" ObjectName="SW-DY_WB.DY_WB_39367SW"/>
     <cge:Meas_Ref ObjectId="194235"/>
    <cge:TPSR_Ref TObjectID="29497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194236">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -77.455297 -1049.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29498" ObjectName="SW-DY_WB.DY_WB_39360SW"/>
     <cge:Meas_Ref ObjectId="194236"/>
    <cge:TPSR_Ref TObjectID="29498"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216580">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 782.544703 -978.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32209" ObjectName="SW-DY_WB.DY_WB_39217SW"/>
     <cge:Meas_Ref ObjectId="216580"/>
    <cge:TPSR_Ref TObjectID="32209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216577">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 745.881701 -909.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32206" ObjectName="SW-DY_WB.DY_WB_3921SW"/>
     <cge:Meas_Ref ObjectId="216577"/>
    <cge:TPSR_Ref TObjectID="32206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216576">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 745.881701 -1077.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32205" ObjectName="SW-DY_WB.DY_WB_3926SW"/>
     <cge:Meas_Ref ObjectId="216576"/>
    <cge:TPSR_Ref TObjectID="32205"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 822.203031 -1119.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216578">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.544703 -1132.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32207" ObjectName="SW-DY_WB.DY_WB_39267SW"/>
     <cge:Meas_Ref ObjectId="216578"/>
    <cge:TPSR_Ref TObjectID="32207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216579">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 662.544703 -1050.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32208" ObjectName="SW-DY_WB.DY_WB_39260SW"/>
     <cge:Meas_Ref ObjectId="216579"/>
    <cge:TPSR_Ref TObjectID="32208"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-DY_WB.DY_WB_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-300,-891 1346,-891 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27684" ObjectName="BS-DY_WB.DY_WB_3IM"/>
    <cge:TPSR_Ref TObjectID="27684"/></metadata>
   <polyline fill="none" opacity="0" points="-300,-891 1346,-891 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_WB.DY_WB_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-301,-527 1346,-527 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27685" ObjectName="BS-DY_WB.DY_WB_9IM"/>
    <cge:TPSR_Ref TObjectID="27685"/></metadata>
   <polyline fill="none" opacity="0" points="-301,-527 1346,-527 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-DY_WB.092Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -201.238710 -224.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34246" ObjectName="EC-DY_WB.092Ld"/>
    <cge:TPSR_Ref TObjectID="34246"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_WB.091Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 85.761290 -225.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34245" ObjectName="EC-DY_WB.091Ld"/>
    <cge:TPSR_Ref TObjectID="34245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 374.761290 -222.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_WB.094Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 661.761290 -224.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34248" ObjectName="EC-DY_WB.094Ld"/>
    <cge:TPSR_Ref TObjectID="34248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_WB.095Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 934.761290 -224.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34247" ObjectName="EC-DY_WB.095Ld"/>
    <cge:TPSR_Ref TObjectID="34247"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_20ce3e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 327.000000 -1033.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb3e50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 958.000000 -1057.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2065b40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 93.544703 -976.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb9ea0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -109.000000 -1130.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f03500" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -110.000000 -1048.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fc4f50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 833.544703 -977.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f3b7c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 631.000000 -1131.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f3fb40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 630.000000 -1049.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2025550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="427,-763 427,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="27691@0" ObjectIDZND0="27715@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181824_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="427,-763 427,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_208cdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="425,-658 425,-637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="27715@0" ObjectIDZND0="27693@1" Pin0InfoVect0LinkObjId="SW-181831_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2025550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="425,-658 425,-637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_208cfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="425,-610 425,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27693@0" ObjectIDZND0="27694@1" Pin0InfoVect0LinkObjId="SW-181833_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181831_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="425,-610 425,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20acf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,-1180 429,-1180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_208e930@0" ObjectIDZND0="0@x" ObjectIDZND1="27689@x" ObjectIDZND2="27690@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-181807_0" Pin0InfoVect2LinkObjId="SW-181808_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_208e930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="395,-1180 429,-1180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ffa030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="430,-1019 430,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27687@1" ObjectIDZND0="27689@0" Pin0InfoVect0LinkObjId="SW-181807_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181804_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="430,-1019 430,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_208f550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="430,-952 430,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27688@1" ObjectIDZND0="27687@0" Pin0InfoVect0LinkObjId="SW-181804_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181806_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="430,-952 430,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20bab10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="430,-1207 430,-1180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="38087@1" ObjectIDZND0="g_208e930@0" ObjectIDZND1="0@x" ObjectIDZND2="27689@x" Pin0InfoVect0LinkObjId="g_208e930_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-181807_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="430,-1207 430,-1180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20bad00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="430,-1164 501,-1164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_208e930@0" ObjectIDND1="38087@1" ObjectIDND2="27689@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_208e930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="SW-181807_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="430,-1164 501,-1164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fe8150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="430,-1164 430,-1180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="0@x" ObjectIDND1="27689@x" ObjectIDND2="27690@x" ObjectIDZND0="g_208e930@0" ObjectIDZND1="38087@1" Pin0InfoVect0LinkObjId="g_208e930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-181807_0" Pin1InfoVect2LinkObjId="SW-181808_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="430,-1164 430,-1180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20a55c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="430,-1086 430,-1134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="27689@1" ObjectIDZND0="0@x" ObjectIDZND1="g_208e930@0" ObjectIDZND2="38087@1" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_208e930_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181807_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="430,-1086 430,-1134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20a57b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="430,-1134 430,-1164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="27689@x" ObjectIDND1="27690@x" ObjectIDZND0="0@x" ObjectIDZND1="g_208e930@0" ObjectIDZND2="38087@1" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_208e930_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-181807_0" Pin1InfoVect1LinkObjId="SW-181808_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="430,-1134 430,-1164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20a59a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="333,-1104 333,-1134 430,-1134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="27690@1" ObjectIDZND0="27689@x" ObjectIDZND1="0@x" ObjectIDZND2="g_208e930@0" Pin0InfoVect0LinkObjId="SW-181807_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_208e930_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181808_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="333,-1104 333,-1134 430,-1134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ce1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="333,-1068 333,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27690@0" ObjectIDZND0="g_20ce3e0@0" Pin0InfoVect0LinkObjId="g_20ce3e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181808_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="333,-1068 333,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ceca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="427,-833 427,-790 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27692@0" ObjectIDZND0="27691@1" Pin0InfoVect0LinkObjId="SW-181824_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181826_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="427,-833 427,-790 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20cee90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="430,-916 430,-891 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27688@0" ObjectIDZND0="27684@0" Pin0InfoVect0LinkObjId="g_1fb4d20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181806_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="430,-916 430,-891 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_207dce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-387 -192,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27701@0" ObjectIDZND0="27703@1" Pin0InfoVect0LinkObjId="SW-181883_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-192,-387 -192,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_204cfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-454 -192,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27702@0" ObjectIDZND0="27701@1" Pin0InfoVect0LinkObjId="SW-181880_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181882_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-192,-454 -192,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_204d1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-156,-279 -156,-294 -192,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_204c620@0" ObjectIDZND0="27703@x" ObjectIDZND1="34246@x" Pin0InfoVect0LinkObjId="SW-181883_0" Pin0InfoVect1LinkObjId="EC-DY_WB.092Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_204c620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-156,-279 -156,-294 -192,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_204d390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-312 -192,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27703@0" ObjectIDZND0="g_204c620@0" ObjectIDZND1="34246@x" Pin0InfoVect0LinkObjId="g_204c620_0" Pin0InfoVect1LinkObjId="EC-DY_WB.092Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181883_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-192,-312 -192,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_208a570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-294 -192,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_204c620@0" ObjectIDND1="27703@x" ObjectIDZND0="34246@0" Pin0InfoVect0LinkObjId="EC-DY_WB.092Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_204c620_0" Pin1InfoVect1LinkObjId="SW-181883_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-192,-294 -192,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_208bd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-490 -192,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27702@1" ObjectIDZND0="27685@0" Pin0InfoVect0LinkObjId="g_208c860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181882_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-192,-490 -192,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_208c670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="427,-891 427,-869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27684@0" ObjectIDZND0="27692@1" Pin0InfoVect0LinkObjId="SW-181826_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20cee90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="427,-891 427,-869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_208c860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="425,-554 425,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27694@0" ObjectIDZND0="27685@0" Pin0InfoVect0LinkObjId="g_208bd90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181833_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="425,-554 425,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_201bc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="95,-388 95,-349 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27698@0" ObjectIDZND0="27700@1" Pin0InfoVect0LinkObjId="SW-181866_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181863_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="95,-388 95,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2059fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="95,-455 95,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27699@0" ObjectIDZND0="27698@1" Pin0InfoVect0LinkObjId="SW-181863_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181865_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="95,-455 95,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_201c180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="131,-280 131,-295 95,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2059440@0" ObjectIDZND0="27700@x" ObjectIDZND1="34245@x" Pin0InfoVect0LinkObjId="SW-181866_0" Pin0InfoVect1LinkObjId="EC-DY_WB.091Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2059440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="131,-280 131,-295 95,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_201c370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="95,-313 95,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27700@0" ObjectIDZND0="g_2059440@0" ObjectIDZND1="34245@x" Pin0InfoVect0LinkObjId="g_2059440_0" Pin0InfoVect1LinkObjId="EC-DY_WB.091Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181866_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="95,-313 95,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_201caa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="95,-295 95,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="27700@x" ObjectIDND1="g_2059440@0" ObjectIDZND0="34245@0" Pin0InfoVect0LinkObjId="EC-DY_WB.091Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-181866_0" Pin1InfoVect1LinkObjId="g_2059440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="95,-295 95,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_201e770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="95,-491 95,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27699@1" ObjectIDZND0="27685@0" Pin0InfoVect0LinkObjId="g_208bd90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181865_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="95,-491 95,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20459c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-385 384,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27704@0" ObjectIDZND0="27706@1" Pin0InfoVect0LinkObjId="SW-181900_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181897_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="384,-385 384,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20078a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-452 384,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27705@0" ObjectIDZND0="27704@1" Pin0InfoVect0LinkObjId="SW-181897_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181899_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="384,-452 384,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2007ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="420,-277 420,-292 384,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2006d30@0" ObjectIDZND0="27706@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-181900_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2006d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="420,-277 420,-292 384,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2007ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-310 384,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27706@0" ObjectIDZND0="g_2006d30@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2006d30_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="384,-310 384,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2008410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-292 384,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="27706@x" ObjectIDND1="g_2006d30@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-181900_0" Pin1InfoVect1LinkObjId="g_2006d30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="384,-292 384,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f7e600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-488 384,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27705@1" ObjectIDZND0="27685@0" Pin0InfoVect0LinkObjId="g_208bd90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181899_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="384,-488 384,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2005510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="671,-387 671,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27707@0" ObjectIDZND0="27709@1" Pin0InfoVect0LinkObjId="SW-181917_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181914_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="671,-387 671,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fa4050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="671,-454 671,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27708@0" ObjectIDZND0="27707@1" Pin0InfoVect0LinkObjId="SW-181914_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181916_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="671,-454 671,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fa4270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="707,-279 707,-294 671,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1fa34e0@0" ObjectIDZND0="27709@x" ObjectIDZND1="34248@x" Pin0InfoVect0LinkObjId="SW-181917_0" Pin0InfoVect1LinkObjId="EC-DY_WB.094Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fa34e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="707,-279 707,-294 671,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fa4490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="671,-312 671,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27709@0" ObjectIDZND0="g_1fa34e0@0" ObjectIDZND1="34248@x" Pin0InfoVect0LinkObjId="g_1fa34e0_0" Pin0InfoVect1LinkObjId="EC-DY_WB.094Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181917_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="671,-312 671,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fa4bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="671,-294 671,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="27709@x" ObjectIDND1="g_1fa34e0@0" ObjectIDZND0="34248@0" Pin0InfoVect0LinkObjId="EC-DY_WB.094Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-181917_0" Pin1InfoVect1LinkObjId="g_1fa34e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="671,-294 671,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2085b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="671,-490 671,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27708@1" ObjectIDZND0="27685@0" Pin0InfoVect0LinkObjId="g_208bd90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181916_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="671,-490 671,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20129e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-387 944,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27710@0" ObjectIDZND0="27712@1" Pin0InfoVect0LinkObjId="SW-181934_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181931_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="944,-387 944,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20338a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-454 944,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27711@0" ObjectIDZND0="27710@1" Pin0InfoVect0LinkObjId="SW-181931_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181933_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="944,-454 944,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2033b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="980,-279 980,-294 944,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2032c30@0" ObjectIDZND0="27712@x" ObjectIDZND1="34247@x" Pin0InfoVect0LinkObjId="SW-181934_0" Pin0InfoVect1LinkObjId="EC-DY_WB.095Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2032c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="980,-279 980,-294 944,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2033d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-312 944,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27712@0" ObjectIDZND0="g_2032c30@0" ObjectIDZND1="34247@x" Pin0InfoVect0LinkObjId="g_2032c30_0" Pin0InfoVect1LinkObjId="EC-DY_WB.095Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181934_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="944,-312 944,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20345f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-294 944,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="27712@x" ObjectIDND1="g_2032c30@0" ObjectIDZND0="34247@0" Pin0InfoVect0LinkObjId="EC-DY_WB.095Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-181934_0" Pin1InfoVect1LinkObjId="g_2032c30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="944,-294 944,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2077200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-490 944,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27711@1" ObjectIDZND0="27685@0" Pin0InfoVect0LinkObjId="g_208bd90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181933_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="944,-490 944,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2078160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1201,-527 1201,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27685@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_208bd90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1201,-527 1201,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f82700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1201,-438 1201,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1201,-438 1201,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fe9100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1121,-891 1121,-846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27684@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20cee90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1121,-891 1121,-846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ffedd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1121,-801 1121,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1121,-801 1121,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20bcd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-223,-703 -223,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_20010d0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20010d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-223,-703 -223,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f4f090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-223,-562 -223,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27697@0" ObjectIDZND0="27685@0" Pin0InfoVect0LinkObjId="g_208bd90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181859_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-223,-562 -223,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f64460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-177,-640 -177,-608 -223,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1f4fe40@0" ObjectIDZND0="0@x" ObjectIDZND1="27697@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-181859_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f4fe40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-177,-640 -177,-608 -223,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f64e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-223,-628 -223,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1f4fe40@0" ObjectIDZND1="27697@x" Pin0InfoVect0LinkObjId="g_1f4fe40_0" Pin0InfoVect1LinkObjId="SW-181859_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-223,-628 -223,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f65090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-223,-608 -223,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1f4fe40@0" ObjectIDND1="0@x" ObjectIDZND0="27697@1" Pin0InfoVect0LinkObjId="SW-181859_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f4fe40_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-223,-608 -223,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f657e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1044,-1066 1044,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_1fdde30@0" ObjectIDZND0="g_1f659d0@1" Pin0InfoVect0LinkObjId="g_1f659d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fdde30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1044,-1066 1044,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2015f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1044,-990 1097,-990 1097,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1f659d0@0" ObjectIDND1="27695@x" ObjectIDND2="27696@x" ObjectIDZND0="g_2016e40@0" Pin0InfoVect0LinkObjId="g_2016e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f659d0_0" Pin1InfoVect1LinkObjId="SW-181854_0" Pin1InfoVect2LinkObjId="SW-181855_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1044,-990 1097,-990 1097,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2016be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1044,-1007 1044,-990 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1f659d0@0" ObjectIDZND0="g_2016e40@0" ObjectIDZND1="27695@x" ObjectIDZND2="27696@x" Pin0InfoVect0LinkObjId="g_2016e40_0" Pin0InfoVect1LinkObjId="SW-181854_0" Pin0InfoVect2LinkObjId="SW-181855_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f659d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1044,-1007 1044,-990 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb3bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="964,-1043 964,-1062 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27696@1" ObjectIDZND0="g_1fb3e50@0" Pin0InfoVect0LinkObjId="g_1fb3e50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181855_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="964,-1043 964,-1062 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb4860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1044,-949 1044,-988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27695@1" ObjectIDZND0="g_2016e40@0" ObjectIDZND1="g_1f659d0@0" ObjectIDZND2="27696@x" Pin0InfoVect0LinkObjId="g_2016e40_0" Pin0InfoVect1LinkObjId="g_1f659d0_0" Pin0InfoVect2LinkObjId="SW-181855_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181854_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1044,-949 1044,-988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb4ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1044,-990 964,-990 964,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2016e40@0" ObjectIDND1="g_1f659d0@0" ObjectIDND2="27695@x" ObjectIDZND0="27696@0" Pin0InfoVect0LinkObjId="SW-181855_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2016e40_0" Pin1InfoVect1LinkObjId="g_1f659d0_0" Pin1InfoVect2LinkObjId="SW-181854_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1044,-990 964,-990 964,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb4d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1044,-913 1044,-891 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27695@0" ObjectIDZND0="27684@0" Pin0InfoVect0LinkObjId="g_20cee90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-181854_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1044,-913 1044,-891 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20630d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="16,-982 48,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29494@x" ObjectIDND1="29495@x" ObjectIDZND0="29499@1" Pin0InfoVect0LinkObjId="SW-194237_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-194232_0" Pin1InfoVect1LinkObjId="SW-194233_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="16,-982 48,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20665d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="84,-982 98,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29499@0" ObjectIDZND0="g_2065b40@0" Pin0InfoVect0LinkObjId="g_2065b40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194237_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="84,-982 98,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff0f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="15,-951 15,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29495@1" ObjectIDZND0="29494@x" ObjectIDZND1="29499@x" Pin0InfoVect0LinkObjId="SW-194232_0" Pin0InfoVect1LinkObjId="SW-194237_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194233_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="15,-951 15,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff1160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="15,-982 15,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="29499@x" ObjectIDND1="29495@x" ObjectIDZND0="29494@0" Pin0InfoVect0LinkObjId="SW-194232_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-194237_0" Pin1InfoVect1LinkObjId="SW-194233_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="15,-982 15,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff1990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="15,-913 15,-891 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29495@0" ObjectIDZND0="27684@0" Pin0InfoVect0LinkObjId="g_20cee90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194233_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="15,-913 15,-891 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff1bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-19,-1184 15,-1184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_200a9c0@0" ObjectIDZND0="0@x" ObjectIDZND1="29497@x" ObjectIDZND2="29496@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-194235_0" Pin0InfoVect2LinkObjId="SW-194234_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_200a9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-19,-1184 15,-1184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff26e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="15,-1184 15,-1211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_200a9c0@0" ObjectIDND1="0@x" ObjectIDND2="29497@x" ObjectIDZND0="37857@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_200a9c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-194235_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="15,-1184 15,-1211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fba930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-71,-1136 -91,-1136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29497@1" ObjectIDZND0="g_1fb9ea0@0" Pin0InfoVect0LinkObjId="g_1fb9ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194235_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-71,-1136 -91,-1136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fbab90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-35,-1136 15,-1136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="29497@0" ObjectIDZND0="29496@x" ObjectIDZND1="0@x" ObjectIDZND2="g_200a9c0@0" Pin0InfoVect0LinkObjId="SW-194234_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_200a9c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194235_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-35,-1136 15,-1136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fbb680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="15,-1117 15,-1136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="29496@1" ObjectIDZND0="29497@x" ObjectIDZND1="0@x" ObjectIDZND2="g_200a9c0@0" Pin0InfoVect0LinkObjId="SW-194235_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_200a9c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194234_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="15,-1117 15,-1136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fbb8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="87,-1168 15,-1168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="29497@x" ObjectIDZND1="29496@x" ObjectIDZND2="g_200a9c0@0" Pin0InfoVect0LinkObjId="SW-194235_0" Pin0InfoVect1LinkObjId="SW-194234_0" Pin0InfoVect2LinkObjId="g_200a9c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="87,-1168 15,-1168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fbc3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="15,-1136 15,-1168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="29497@x" ObjectIDND1="29496@x" ObjectIDZND0="0@x" ObjectIDZND1="g_200a9c0@0" ObjectIDZND2="37857@1" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_200a9c0_0" Pin0InfoVect2LinkObjId="g_1ff26e0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-194235_0" Pin1InfoVect1LinkObjId="SW-194234_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="15,-1136 15,-1168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fbc630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="15,-1168 15,-1184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="0@x" ObjectIDND1="29497@x" ObjectIDND2="29496@x" ObjectIDZND0="g_200a9c0@0" ObjectIDZND1="37857@1" Pin0InfoVect0LinkObjId="g_200a9c0_0" Pin0InfoVect1LinkObjId="g_1ff26e0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-194235_0" Pin1InfoVect2LinkObjId="SW-194234_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="15,-1168 15,-1184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f03f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-72,-1054 -92,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29498@1" ObjectIDZND0="g_1f03500@0" Pin0InfoVect0LinkObjId="g_1f03500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194236_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-72,-1054 -92,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f041f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-36,-1054 14,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29498@0" ObjectIDZND0="29496@x" ObjectIDZND1="29494@x" Pin0InfoVect0LinkObjId="SW-194234_0" Pin0InfoVect1LinkObjId="SW-194232_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194236_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-36,-1054 14,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f04ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="15,-1081 15,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29496@0" ObjectIDZND0="29498@x" ObjectIDZND1="29494@x" Pin0InfoVect0LinkObjId="SW-194236_0" Pin0InfoVect1LinkObjId="SW-194232_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194234_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="15,-1081 15,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f04f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="15,-1054 15,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="29498@x" ObjectIDND1="29496@x" ObjectIDZND0="29494@1" Pin0InfoVect0LinkObjId="SW-194232_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-194236_0" Pin1InfoVect1LinkObjId="SW-194234_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="15,-1054 15,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc59e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="824,-983 838,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32209@0" ObjectIDZND0="g_1fc4f50@0" Pin0InfoVect0LinkObjId="g_1fc4f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="824,-983 838,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f13ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="755,-952 755,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="32206@1" ObjectIDZND0="32209@x" ObjectIDZND1="32204@x" Pin0InfoVect0LinkObjId="SW-216580_0" Pin0InfoVect1LinkObjId="SW-216575_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216577_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="755,-952 755,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f14140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="755,-983 755,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="32206@x" ObjectIDND1="32209@x" ObjectIDZND0="32204@0" Pin0InfoVect0LinkObjId="SW-216575_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-216577_0" Pin1InfoVect1LinkObjId="SW-216580_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="755,-983 755,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f385f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="755,-914 755,-891 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32206@0" ObjectIDZND0="27684@0" Pin0InfoVect0LinkObjId="g_20cee90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216577_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="755,-914 755,-891 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f38850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-1185 755,-1185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1f0a970@0" ObjectIDZND0="0@x" ObjectIDZND1="32207@x" ObjectIDZND2="32205@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-216578_0" Pin0InfoVect2LinkObjId="SW-216576_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f0a970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="721,-1185 755,-1185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f38ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="755,-1185 755,-1215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_1f0a970@0" ObjectIDND1="0@x" ObjectIDND2="32207@x" ObjectIDZND0="38088@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f0a970_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-216578_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="755,-1185 755,-1215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3c250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="669,-1137 649,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32207@1" ObjectIDZND0="g_1f3b7c0@0" Pin0InfoVect0LinkObjId="g_1f3b7c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216578_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="669,-1137 649,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3c4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-1137 755,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="32207@0" ObjectIDZND0="32205@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1f0a970@0" Pin0InfoVect0LinkObjId="SW-216576_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1f0a970_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216578_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="705,-1137 755,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3c710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="755,-1118 755,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="32205@1" ObjectIDZND0="32207@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1f0a970@0" Pin0InfoVect0LinkObjId="SW-216578_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1f0a970_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216576_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="755,-1118 755,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3c970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="827,-1169 755,-1169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="32207@x" ObjectIDZND1="32205@x" ObjectIDZND2="g_1f0a970@0" Pin0InfoVect0LinkObjId="SW-216578_0" Pin0InfoVect1LinkObjId="SW-216576_0" Pin0InfoVect2LinkObjId="g_1f0a970_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="827,-1169 755,-1169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3cbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="755,-1137 755,-1169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="32207@x" ObjectIDND1="32205@x" ObjectIDZND0="0@x" ObjectIDZND1="g_1f0a970@0" ObjectIDZND2="38088@1" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1f0a970_0" Pin0InfoVect2LinkObjId="g_1f38ab0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-216578_0" Pin1InfoVect1LinkObjId="SW-216576_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="755,-1137 755,-1169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3ce30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="755,-1169 755,-1185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="0@x" ObjectIDND1="32207@x" ObjectIDND2="32205@x" ObjectIDZND0="g_1f0a970@0" ObjectIDZND1="38088@1" Pin0InfoVect0LinkObjId="g_1f0a970_0" Pin0InfoVect1LinkObjId="g_1f38ab0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-216578_0" Pin1InfoVect2LinkObjId="SW-216576_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="755,-1169 755,-1185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f405d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="668,-1055 648,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32208@1" ObjectIDZND0="g_1f3fb40@0" Pin0InfoVect0LinkObjId="g_1f3fb40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216579_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="668,-1055 648,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f40830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="704,-1055 754,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="32208@0" ObjectIDZND0="32205@x" ObjectIDZND1="32204@x" Pin0InfoVect0LinkObjId="SW-216576_0" Pin0InfoVect1LinkObjId="SW-216575_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216579_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="704,-1055 754,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f40a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="755,-1082 755,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="32205@0" ObjectIDZND0="32208@x" ObjectIDZND1="32204@x" Pin0InfoVect0LinkObjId="SW-216579_0" Pin0InfoVect1LinkObjId="SW-216575_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216576_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="755,-1082 755,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f40cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="755,-1055 755,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="32208@x" ObjectIDND1="32205@x" ObjectIDZND0="32204@1" Pin0InfoVect0LinkObjId="SW-216575_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-216579_0" Pin1InfoVect1LinkObjId="SW-216576_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="755,-1055 755,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f422f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="756,-983 788,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="32206@x" ObjectIDND1="32204@x" ObjectIDZND0="32209@1" Pin0InfoVect0LinkObjId="SW-216580_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-216577_0" Pin1InfoVect1LinkObjId="SW-216575_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="756,-983 788,-983 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="27685" cx="425" cy="-528" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27685" cx="-192" cy="-527" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27685" cx="95" cy="-527" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27685" cx="384" cy="-527" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27685" cx="671" cy="-527" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27685" cx="944" cy="-527" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27685" cx="1201" cy="-527" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27684" cx="1121" cy="-891" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27685" cx="-223" cy="-527" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27684" cx="1044" cy="-891" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27684" cx="430" cy="-891" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27684" cx="15" cy="-891" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27684" cx="755" cy="-891" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27684" cx="427" cy="-891" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-153549" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -426.000000 -1100.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26040" ObjectName="DYN-DY_WB"/>
     <cge:Meas_Ref ObjectId="153549"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20f3ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20f3ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20f3ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,59)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20f3ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20f3ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,101)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20f3ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20f3ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,143)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20f3ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20f3ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,185)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20f3ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20f3ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,227)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d9d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d9d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d9d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d9d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d9d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d9d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d9d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d9d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d9d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d9d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d9d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d9d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d9d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d9d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d9d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d9d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d9d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d9d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,374)">联系方式：0878-6148335</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2025740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1145.096525 -290.000000) translate(0,12)">10kV2号站用变压器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2049790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 379.000000 -1260.000000) translate(0,12)">35kV桂湾线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2049a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -654.000000) translate(0,12)">35kV1号站用变压器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_20ba7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -568.000000 -1239.500000) translate(0,16)">湾碧变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204d580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -212.000000 -210.000000) translate(0,12)">灰拉表线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_201c590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 75.000000 -211.000000) translate(0,12)">咖啡厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2007f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -208.000000) translate(0,12)">备用线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa46b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.000000 -209.000000) translate(0,12)">湾陆联络线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2033fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 924.000000 -210.000000) translate(0,12)">文宜拉线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f4f810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -249.903475 -777.000000) translate(0,12)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f652f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1004.096525 -1138.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb6d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 449.000000 -1010.000000) translate(0,12)">391</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb7280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 443.000000 -933.000000) translate(0,12)">3911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2026d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 442.000000 -1066.000000) translate(0,12)">3916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2026f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 348.000000 -1087.000000) translate(0,12)">39167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20273c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1051.000000 -938.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2027800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 971.000000 -1033.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2027a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 437.000000 -784.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2027c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 435.000000 -858.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2027ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 435.000000 -631.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2028100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 432.000000 -579.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2028340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -213.000000 -586.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2028580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -183.000000 -408.000000) translate(0,12)">092</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20287c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -185.000000 -479.000000) translate(0,12)">0921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2028a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -185.000000 -337.000000) translate(0,12)">0926</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2028c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 105.000000 -409.000000) translate(0,12)">091</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2028e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 102.000000 -338.000000) translate(0,12)">0916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20290c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 102.000000 -480.000000) translate(0,12)">0911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2029300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 394.000000 -406.000000) translate(0,12)">093</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2029540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 391.000000 -477.000000) translate(0,12)">0931</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2029780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 391.000000 -335.000000) translate(0,12)">0936</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20299c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 681.000000 -408.000000) translate(0,12)">094</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2029c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 678.000000 -479.000000) translate(0,12)">0941</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_202a040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 678.000000 -337.000000) translate(0,12)">0946</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_202a280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 954.000000 -408.000000) translate(0,12)">095</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_202a4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 951.000000 -479.000000) translate(0,12)">0951</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_202a700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 951.000000 -337.000000) translate(0,12)">0956</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_202a940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -293.000000 -913.000000) translate(0,12)">Ⅰ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_202ab80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -305.000000 -547.000000) translate(0,12)">Ⅰ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc86b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 306.000000 -736.000000) translate(0,12)">1号主变压器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2066830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -35.000000 -1264.000000) translate(0,12)">35kV马湾线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f05770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 65.000000 -1068.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f05f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 25.000000 -1024.000000) translate(0,12)">393</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f061c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 58.000000 -1008.000000) translate(0,12)">39317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f06400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 28.000000 -937.000000) translate(0,12)">3931</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f06640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -74.000000 -1080.000000) translate(0,12)">39360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f06880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -73.000000 -1162.000000) translate(0,12)">39367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f06ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 22.000000 -1106.000000) translate(0,12)">3936</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f081c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 218.000000 -710.000000) translate(0,12)">SZ11-2000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f081c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 218.000000 -710.000000) translate(0,27)">35±3*2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f081c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 218.000000 -710.000000) translate(0,42)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f081c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 218.000000 -710.000000) translate(0,57)">6.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1fbf420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -839.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1fc08a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -440.000000 -1225.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2025090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -440.000000 -1260.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc5c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 705.000000 -1265.000000) translate(0,12)">35kV湾陆线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f40f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 805.000000 -1069.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fd7bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 765.000000 -1025.000000) translate(0,12)">392</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fd8060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 762.000000 -1107.000000) translate(0,12)">3926</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fd82a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 762.000000 -939.000000) translate(0,12)">3921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fd84e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 786.000000 -1009.000000) translate(0,12)">39217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fd8720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 665.000000 -1081.000000) translate(0,12)">39260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fd8960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 666.000000 -1163.000000) translate(0,12)">39267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1fda510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -743.000000 -265.000000) translate(0,17)">永仁巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1fdbe20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -603.000000 -275.500000) translate(0,17)">13638777384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1fdbe20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -603.000000 -275.500000) translate(0,38)">13987885824</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1fdfd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -388.500000 -1179.000000) translate(0,16)">AVC</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-181824">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 418.241796 -754.716049)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27691" ObjectName="SW-DY_WB.DY_WB_301BK"/>
     <cge:Meas_Ref ObjectId="181824"/>
    <cge:TPSR_Ref TObjectID="27691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181831">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 416.241796 -601.716049)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27693" ObjectName="SW-DY_WB.DY_WB_001BK"/>
     <cge:Meas_Ref ObjectId="181831"/>
    <cge:TPSR_Ref TObjectID="27693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181804">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 421.241796 -983.716049)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27687" ObjectName="SW-DY_WB.DY_WB_391BK"/>
     <cge:Meas_Ref ObjectId="181804"/>
    <cge:TPSR_Ref TObjectID="27687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181880">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -201.787729 -378.716049)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27701" ObjectName="SW-DY_WB.DY_WB_092BK"/>
     <cge:Meas_Ref ObjectId="181880"/>
    <cge:TPSR_Ref TObjectID="27701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181863">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 86.212271 -379.716049)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27698" ObjectName="SW-DY_WB.DY_WB_091BK"/>
     <cge:Meas_Ref ObjectId="181863"/>
    <cge:TPSR_Ref TObjectID="27698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181897">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 375.212271 -376.716049)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27704" ObjectName="SW-DY_WB.DY_WB_093BK"/>
     <cge:Meas_Ref ObjectId="181897"/>
    <cge:TPSR_Ref TObjectID="27704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181914">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 662.212271 -378.716049)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27707" ObjectName="SW-DY_WB.DY_WB_094BK"/>
     <cge:Meas_Ref ObjectId="181914"/>
    <cge:TPSR_Ref TObjectID="27707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-181931">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 935.212271 -378.716049)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27710" ObjectName="SW-DY_WB.DY_WB_095BK"/>
     <cge:Meas_Ref ObjectId="181931"/>
    <cge:TPSR_Ref TObjectID="27710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194232">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.241796 -994.716049)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29494" ObjectName="SW-DY_WB.DY_WB_393BK"/>
     <cge:Meas_Ref ObjectId="194232"/>
    <cge:TPSR_Ref TObjectID="29494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216575">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 746.241796 -995.716049)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32204" ObjectName="SW-DY_WB.DY_WB_392BK"/>
     <cge:Meas_Ref ObjectId="216575"/>
    <cge:TPSR_Ref TObjectID="32204"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="501,-1119 501,-1099 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="501,-1119 501,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="87,-1123 87,-1103 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="87,-1123 87,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="827,-1124 827,-1104 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="827,-1124 827,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_WB" endPointId="0" endStationName="DY_GH" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_guiwan" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="430,-1159 430,-1238 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38087" ObjectName="AC-35kV.LN_guiwan"/>
    <cge:TPSR_Ref TObjectID="38087_SS-232"/></metadata>
   <polyline fill="none" opacity="0" points="430,-1159 430,-1238 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_WB" endPointId="0" endStationName="DY_LJW" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_wanlu" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="755,-1165 755,-1238 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38088" ObjectName="AC-35kV.LN_wanlu"/>
    <cge:TPSR_Ref TObjectID="38088_SS-232"/></metadata>
   <polyline fill="none" opacity="0" points="755,-1165 755,-1238 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_WM" endPointId="0" endStationName="DY_WB" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_mawanxian" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="15,-1162 15,-1247 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37857" ObjectName="AC-35kV.LN_mawanxian"/>
    <cge:TPSR_Ref TObjectID="37857_SS-232"/></metadata>
   <polyline fill="none" opacity="0" points="15,-1162 15,-1247 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_208e930">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 388.000000 -1175.716049)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_204c620">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -163.323558 -225.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2059440">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 123.676442 -226.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2006d30">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 412.676442 -223.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fa34e0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 699.676442 -225.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2032c30">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 972.676442 -225.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f4fe40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -184.000000 -636.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f659d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1037.000000 -1002.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2016e40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1090.000000 -1023.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_200a9c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -26.000000 -1179.716049)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f0a970">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 714.000000 -1180.716049)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-181730" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 367.000000 -1014.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181730" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27687"/>
     <cge:Term_Ref ObjectID="39155"/>
    <cge:TPSR_Ref TObjectID="27687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-181731" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 367.000000 -1014.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181731" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27687"/>
     <cge:Term_Ref ObjectID="39155"/>
    <cge:TPSR_Ref TObjectID="27687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-181728" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 367.000000 -1014.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181728" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27687"/>
     <cge:Term_Ref ObjectID="39155"/>
    <cge:TPSR_Ref TObjectID="27687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-181729" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 367.000000 -1014.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181729" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27687"/>
     <cge:Term_Ref ObjectID="39155"/>
    <cge:TPSR_Ref TObjectID="27687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-181732" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 367.000000 -1014.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181732" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27687"/>
     <cge:Term_Ref ObjectID="39155"/>
    <cge:TPSR_Ref TObjectID="27687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-181766" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -164.000000 -167.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181766" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27701"/>
     <cge:Term_Ref ObjectID="39183"/>
    <cge:TPSR_Ref TObjectID="27701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-181767" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -164.000000 -167.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181767" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27701"/>
     <cge:Term_Ref ObjectID="39183"/>
    <cge:TPSR_Ref TObjectID="27701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-181764" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -164.000000 -167.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181764" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27701"/>
     <cge:Term_Ref ObjectID="39183"/>
    <cge:TPSR_Ref TObjectID="27701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-181765" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -164.000000 -167.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181765" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27701"/>
     <cge:Term_Ref ObjectID="39183"/>
    <cge:TPSR_Ref TObjectID="27701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-181768" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -164.000000 -167.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181768" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27701"/>
     <cge:Term_Ref ObjectID="39183"/>
    <cge:TPSR_Ref TObjectID="27701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-181761" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 111.000000 -164.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181761" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27698"/>
     <cge:Term_Ref ObjectID="39177"/>
    <cge:TPSR_Ref TObjectID="27698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-181762" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 111.000000 -164.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181762" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27698"/>
     <cge:Term_Ref ObjectID="39177"/>
    <cge:TPSR_Ref TObjectID="27698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-181759" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 111.000000 -164.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181759" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27698"/>
     <cge:Term_Ref ObjectID="39177"/>
    <cge:TPSR_Ref TObjectID="27698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-181760" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 111.000000 -164.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181760" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27698"/>
     <cge:Term_Ref ObjectID="39177"/>
    <cge:TPSR_Ref TObjectID="27698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-181763" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 111.000000 -164.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181763" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27698"/>
     <cge:Term_Ref ObjectID="39177"/>
    <cge:TPSR_Ref TObjectID="27698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-181771" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 409.000000 -167.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181771" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27704"/>
     <cge:Term_Ref ObjectID="39189"/>
    <cge:TPSR_Ref TObjectID="27704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-181772" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 409.000000 -167.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181772" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27704"/>
     <cge:Term_Ref ObjectID="39189"/>
    <cge:TPSR_Ref TObjectID="27704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-181769" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 409.000000 -167.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181769" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27704"/>
     <cge:Term_Ref ObjectID="39189"/>
    <cge:TPSR_Ref TObjectID="27704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-181770" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 409.000000 -167.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181770" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27704"/>
     <cge:Term_Ref ObjectID="39189"/>
    <cge:TPSR_Ref TObjectID="27704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-181773" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 409.000000 -167.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181773" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27704"/>
     <cge:Term_Ref ObjectID="39189"/>
    <cge:TPSR_Ref TObjectID="27704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-181776" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 693.000000 -168.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181776" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27707"/>
     <cge:Term_Ref ObjectID="39195"/>
    <cge:TPSR_Ref TObjectID="27707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-181777" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 693.000000 -168.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181777" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27707"/>
     <cge:Term_Ref ObjectID="39195"/>
    <cge:TPSR_Ref TObjectID="27707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-181774" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 693.000000 -168.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181774" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27707"/>
     <cge:Term_Ref ObjectID="39195"/>
    <cge:TPSR_Ref TObjectID="27707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-181775" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 693.000000 -168.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181775" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27707"/>
     <cge:Term_Ref ObjectID="39195"/>
    <cge:TPSR_Ref TObjectID="27707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-181778" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 693.000000 -168.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181778" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27707"/>
     <cge:Term_Ref ObjectID="39195"/>
    <cge:TPSR_Ref TObjectID="27707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-181781" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 976.000000 -167.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181781" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27710"/>
     <cge:Term_Ref ObjectID="39201"/>
    <cge:TPSR_Ref TObjectID="27710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-181782" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 976.000000 -167.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181782" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27710"/>
     <cge:Term_Ref ObjectID="39201"/>
    <cge:TPSR_Ref TObjectID="27710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-181779" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 976.000000 -167.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181779" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27710"/>
     <cge:Term_Ref ObjectID="39201"/>
    <cge:TPSR_Ref TObjectID="27710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-181780" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 976.000000 -167.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181780" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27710"/>
     <cge:Term_Ref ObjectID="39201"/>
    <cge:TPSR_Ref TObjectID="27710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-181783" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 976.000000 -167.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181783" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27710"/>
     <cge:Term_Ref ObjectID="39201"/>
    <cge:TPSR_Ref TObjectID="27710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-181736" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -866.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181736" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27691"/>
     <cge:Term_Ref ObjectID="39163"/>
    <cge:TPSR_Ref TObjectID="27691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-181737" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -866.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181737" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27691"/>
     <cge:Term_Ref ObjectID="39163"/>
    <cge:TPSR_Ref TObjectID="27691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-181733" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -866.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181733" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27691"/>
     <cge:Term_Ref ObjectID="39163"/>
    <cge:TPSR_Ref TObjectID="27691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-181734" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -866.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181734" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27691"/>
     <cge:Term_Ref ObjectID="39163"/>
    <cge:TPSR_Ref TObjectID="27691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-181735" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -866.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181735" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27691"/>
     <cge:Term_Ref ObjectID="39163"/>
    <cge:TPSR_Ref TObjectID="27691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-181738" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -866.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181738" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27691"/>
     <cge:Term_Ref ObjectID="39163"/>
    <cge:TPSR_Ref TObjectID="27691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-181742" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 358.000000 -646.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181742" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27693"/>
     <cge:Term_Ref ObjectID="39167"/>
    <cge:TPSR_Ref TObjectID="27693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-181743" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 358.000000 -646.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181743" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27693"/>
     <cge:Term_Ref ObjectID="39167"/>
    <cge:TPSR_Ref TObjectID="27693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-181739" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 358.000000 -646.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181739" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27693"/>
     <cge:Term_Ref ObjectID="39167"/>
    <cge:TPSR_Ref TObjectID="27693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-181740" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 358.000000 -646.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181740" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27693"/>
     <cge:Term_Ref ObjectID="39167"/>
    <cge:TPSR_Ref TObjectID="27693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-181741" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 358.000000 -646.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181741" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27693"/>
     <cge:Term_Ref ObjectID="39167"/>
    <cge:TPSR_Ref TObjectID="27693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-181744" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 358.000000 -646.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181744" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27693"/>
     <cge:Term_Ref ObjectID="39167"/>
    <cge:TPSR_Ref TObjectID="27693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-181747" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 -1028.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181747" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27684"/>
     <cge:Term_Ref ObjectID="39151"/>
    <cge:TPSR_Ref TObjectID="27684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-181748" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 -1028.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181748" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27684"/>
     <cge:Term_Ref ObjectID="39151"/>
    <cge:TPSR_Ref TObjectID="27684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-181749" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 -1028.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181749" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27684"/>
     <cge:Term_Ref ObjectID="39151"/>
    <cge:TPSR_Ref TObjectID="27684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-181752" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 -1028.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181752" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27684"/>
     <cge:Term_Ref ObjectID="39151"/>
    <cge:TPSR_Ref TObjectID="27684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-181750" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 -1028.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181750" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27684"/>
     <cge:Term_Ref ObjectID="39151"/>
    <cge:TPSR_Ref TObjectID="27684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-181751" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 -1028.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181751" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27684"/>
     <cge:Term_Ref ObjectID="39151"/>
    <cge:TPSR_Ref TObjectID="27684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-181753" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 -1028.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181753" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27684"/>
     <cge:Term_Ref ObjectID="39151"/>
    <cge:TPSR_Ref TObjectID="27684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-181754" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1272.000000 -640.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181754" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27685"/>
     <cge:Term_Ref ObjectID="39152"/>
    <cge:TPSR_Ref TObjectID="27685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-181755" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1272.000000 -640.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181755" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27685"/>
     <cge:Term_Ref ObjectID="39152"/>
    <cge:TPSR_Ref TObjectID="27685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-181756" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1272.000000 -640.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181756" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27685"/>
     <cge:Term_Ref ObjectID="39152"/>
    <cge:TPSR_Ref TObjectID="27685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-181973" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1272.000000 -640.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181973" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27685"/>
     <cge:Term_Ref ObjectID="39152"/>
    <cge:TPSR_Ref TObjectID="27685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-181757" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1272.000000 -640.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181757" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27685"/>
     <cge:Term_Ref ObjectID="39152"/>
    <cge:TPSR_Ref TObjectID="27685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-181758" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1272.000000 -640.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181758" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27685"/>
     <cge:Term_Ref ObjectID="39152"/>
    <cge:TPSR_Ref TObjectID="27685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-181745" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 547.000000 -720.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181745" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27715"/>
     <cge:Term_Ref ObjectID="39214"/>
    <cge:TPSR_Ref TObjectID="27715"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-181746" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 544.000000 -694.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181746" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27715"/>
     <cge:Term_Ref ObjectID="39214"/>
    <cge:TPSR_Ref TObjectID="27715"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-194229" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -59.000000 -1024.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194229" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29494"/>
     <cge:Term_Ref ObjectID="42016"/>
    <cge:TPSR_Ref TObjectID="29494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-194230" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -59.000000 -1024.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194230" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29494"/>
     <cge:Term_Ref ObjectID="42016"/>
    <cge:TPSR_Ref TObjectID="29494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-194226" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -59.000000 -1024.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194226" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29494"/>
     <cge:Term_Ref ObjectID="42016"/>
    <cge:TPSR_Ref TObjectID="29494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-194227" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -59.000000 -1024.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194227" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29494"/>
     <cge:Term_Ref ObjectID="42016"/>
    <cge:TPSR_Ref TObjectID="29494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-194228" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -59.000000 -1024.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194228" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29494"/>
     <cge:Term_Ref ObjectID="42016"/>
    <cge:TPSR_Ref TObjectID="29494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-194231" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -59.000000 -1024.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194231" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29494"/>
     <cge:Term_Ref ObjectID="42016"/>
    <cge:TPSR_Ref TObjectID="29494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-216615" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.093750 -0.000000 -0.000000 1.166667 648.000000 -1026.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216615" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32204"/>
     <cge:Term_Ref ObjectID="46490"/>
    <cge:TPSR_Ref TObjectID="32204"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-216616" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.093750 -0.000000 -0.000000 1.166667 648.000000 -1026.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216616" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32204"/>
     <cge:Term_Ref ObjectID="46490"/>
    <cge:TPSR_Ref TObjectID="32204"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-216612" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.093750 -0.000000 -0.000000 1.166667 648.000000 -1026.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216612" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32204"/>
     <cge:Term_Ref ObjectID="46490"/>
    <cge:TPSR_Ref TObjectID="32204"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-216613" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.093750 -0.000000 -0.000000 1.166667 648.000000 -1026.500000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216613" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32204"/>
     <cge:Term_Ref ObjectID="46490"/>
    <cge:TPSR_Ref TObjectID="32204"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-216614" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.093750 -0.000000 -0.000000 1.166667 648.000000 -1026.500000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216614" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32204"/>
     <cge:Term_Ref ObjectID="46490"/>
    <cge:TPSR_Ref TObjectID="32204"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-216617" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.093750 -0.000000 -0.000000 1.166667 648.000000 -1026.500000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216617" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32204"/>
     <cge:Term_Ref ObjectID="46490"/>
    <cge:TPSR_Ref TObjectID="32204"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图35_大姚.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-451" y="-1233"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-606" y="-1252"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/></g>
   <g href="35kV湾碧变35kV马湾线393间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="25" y="-1024"/></g>
   <g href="35kV湾碧变35kV桂湾线391间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="23" x="449" y="-1010"/></g>
   <g href="35kV湾碧变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="82" x="306" y="-736"/></g>
   <g href="35kV湾碧变10kV湾陆联络线094间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="681" y="-408"/></g>
   <g href="35kV湾碧变10kV文宜拉线095间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="954" y="-408"/></g>
   <g href="35kV湾碧变10kV备用线093间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="394" y="-406"/></g>
   <g href="35kV湾碧变10kV咖啡线091间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="105" y="-409"/></g>
   <g href="35kV湾碧变10kV灰拉表线092间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="-183" y="-408"/></g>
   <g href="35kV湾碧变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="23" qtmmishow="hidden" width="78" x="-702" y="-841"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-451" y="-1268"/></g>
   <g href="35kV湾碧变35kV湾陆线392间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="765" y="-1025"/></g>
   <g href="AVC湾碧站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="-406" y="-1191"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc88f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 317.000000 828.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc9300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 317.000000 811.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc9850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 317.000000 793.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc9db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 321.000000 776.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fca950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 303.000000 865.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fcb680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 291.500000 846.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fcbff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 491.000000 721.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fcc8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 461.500000 690.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ffddd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -210.000000 128.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ffe010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -210.000000 111.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ffe250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -206.000000 94.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ffe490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -224.000000 165.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ffe6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -235.500000 146.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2080350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 67.000000 128.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20805c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 67.000000 111.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2080800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 71.000000 94.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2080a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 53.000000 165.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2080c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 41.500000 146.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2080fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 365.000000 128.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2081220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 365.000000 111.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2081460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 369.000000 94.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20816a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 351.000000 165.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20818e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 339.500000 146.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2081c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 647.000000 128.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2081e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 647.000000 111.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20820c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 651.000000 94.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2082300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 633.000000 165.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2082540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 621.500000 146.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2082870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 930.000000 127.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2082ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 930.000000 110.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2082d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 934.000000 93.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2082f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 916.000000 164.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20831a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 904.500000 145.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20834d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1229.000000 972.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2083b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1223.000000 1025.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2083d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1231.000000 920.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205e0e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1223.000000 1006.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205e360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1215.000000 955.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205e5a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1223.000000 989.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205e7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1215.000000 937.000000) translate(0,12)">Ubc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205eb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1216.000000 638.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205ed90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1208.000000 550.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205efd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1216.000000 619.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205f210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1208.000000 568.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205f450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1216.000000 602.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205f690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1222.000000 585.000000) translate(0,12)">U0(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205f9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 312.000000 608.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205fc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 312.000000 591.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205fe80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 312.000000 573.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20600c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 316.000000 556.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2060300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 298.000000 645.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2060540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 286.500000 626.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20611d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 323.000000 976.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2061410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 323.000000 959.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2061650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 327.000000 942.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2061890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 309.000000 1013.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2061ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 297.500000 994.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f073f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -105.000000 985.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f07680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -105.000000 951.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f078c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -101.000000 935.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f07b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -119.000000 1022.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f07d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -105.000000 968.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f07f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -130.500000 1003.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f41530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 603.000000 987.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f417b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 603.000000 953.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f419f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 607.000000 937.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f41c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 589.000000 1024.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f41e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 603.000000 970.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f420b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 577.500000 1005.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-DY_WB.DY_WB_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="39213"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 389.418267 -653.000000)" xlink:href="#transformer2:shape14_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 389.418267 -653.000000)" xlink:href="#transformer2:shape14_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="27715" ObjectName="TF-DY_WB.DY_WB_1T"/>
    <cge:TPSR_Ref TObjectID="27715"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1185.000000 -301.000000)" xlink:href="#transformer2:shape35_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1185.000000 -301.000000)" xlink:href="#transformer2:shape35_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1105.000000 -664.000000)" xlink:href="#transformer2:shape35_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1105.000000 -664.000000)" xlink:href="#transformer2:shape35_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-451" y="-1233"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-451" y="-1233"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-606" y="-1252"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-606" y="-1252"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-655" y="-1269"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="25" y="-1024"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="25" y="-1024"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="23" x="449" y="-1010"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="23" x="449" y="-1010"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="82" x="306" y="-736"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="82" x="306" y="-736"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="681" y="-408"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="681" y="-408"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="954" y="-408"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="954" y="-408"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="394" y="-406"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="394" y="-406"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="105" y="-409"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="105" y="-409"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="-183" y="-408"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="-183" y="-408"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="23" qtmmishow="hidden" width="78" x="-702" y="-841"/>
    </a>
   <metadata/><rect fill="white" height="23" opacity="0" stroke="white" transform="" width="78" x="-702" y="-841"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-451" y="-1268"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-451" y="-1268"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="765" y="-1025"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="765" y="-1025"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="-406" y="-1191"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="-406" y="-1191"/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1fe8340">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 492.500000 -1104.500000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20010d0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 -205.000000 -757.000000)" xlink:href="#voltageTransformer:shape57"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fefa00">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 78.500000 -1108.500000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f13270">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 818.500000 -1109.500000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fdde30">
    <use class="BV-35KV" transform="matrix(2.082437 -0.000000 0.000000 -2.689560 1011.000000 -1054.857143)" xlink:href="#voltageTransformer:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -618.000000 -1176.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-181753" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -636.000000 -1097.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181753" ObjectName="DY_WB:DY_WB_3ⅠM_Hz"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-181742" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -595.000000 -975.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181742" ObjectName="DY_WB:DY_WB_001BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-181743" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -595.000000 -933.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181743" ObjectName="DY_WB:DY_WB_001BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-181736" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -598.000000 -1057.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181736" ObjectName="DY_WB:DY_WB_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-181736" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -597.000000 -1017.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="181736" ObjectName="DY_WB:DY_WB_301BK_P"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="DY_WB"/>
</svg>