<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-231" aopId="3934726" id="thSvg" product="E8000V2" version="1.0" viewBox="-709 -1269 2208 1206">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="hydroGenerator:shape2">
    <polyline arcFlag="1" points="24,29 24,28 24,27 24,26 25,25 25,24 26,23 26,23 27,22 28,22 29,21 30,21 31,21 32,20 33,21 34,21 35,21 36,22 36,22 37,23 38,23 38,24 39,25 39,26 39,27 40,28 39,29 " stroke-width="0.06"/>
    <circle cx="25" cy="30" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="9,29 8,30 9,30 9,31 9,32 10,33 10,34 11,35 12,35 12,36 13,36 14,37 15,37 16,37 17,37 18,37 19,36 20,36 21,35 22,35 22,34 23,33 23,32 24,31 24,30 24,30 24,29 " stroke-width="0.06"/>
   </symbol>
   <symbol id="hydroGenerator:shape3">
    <polyline arcFlag="1" points="25,25 25,26 25,27 25,28 24,29 24,30 23,31 23,31 22,32 21,32 20,33 19,33 18,33 17,34 16,33 15,33 14,33 13,32 13,32 12,31 11,31 11,30 10,29 10,28 10,27 9,26 10,25 " stroke-width="1.14"/>
    <circle cx="24" cy="24" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="40,25 41,24 40,24 40,23 40,22 39,21 39,20 38,19 37,19 37,18 36,18 35,17 34,17 33,17 32,17 31,17 30,18 29,18 28,19 27,19 27,20 26,21 26,22 25,23 25,24 25,24 25,25 " stroke-width="1.14"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape22">
    <polyline DF8003:Layer="PUBLIC" points="18,1 18,16 30,8 18,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="55" x2="60" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="46" x2="51" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="38" x2="43" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="30" x2="35" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="86" x2="72" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="72,1 72,16 60,8 72,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="4" x2="18" y1="9" y2="9"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape30_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape14_0">
    <circle cx="37" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="84" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="70" x2="68" y1="84" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="45" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="28" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="28" x2="45" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape14_1">
    <ellipse cx="37" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="67" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="59" y2="67"/>
   </symbol>
   <symbol id="transformer2:shape3_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape3_1">
    <circle cx="13" cy="18" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape21">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="27" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="11" y2="5"/>
    <circle cx="27" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="13" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape112">
    <circle cx="32" cy="16" fillStyle="0" r="8" stroke-width="0.570276"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="5" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="9" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="32" x2="35" y1="16" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="29" x2="32" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="32" x2="32" y1="16" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="20" x2="23" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="17" x2="20" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="20" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="17" x2="20" y1="26" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="20" x2="23" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="20" x2="20" y1="23" y2="20"/>
    <circle cx="8" cy="16" fillStyle="0" r="7.5" stroke-width="0.536731"/>
    <circle cx="20" cy="9" fillStyle="0" r="8" stroke-width="0.570276"/>
    <circle cx="20" cy="23" fillStyle="0" r="8" stroke-width="0.570276"/>
   </symbol>
   <symbol id="voltageTransformer:shape90">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="25" y1="18" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="12" x2="9" y1="26" y2="23"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <ellipse cx="21" cy="16" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="9" x2="6" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="12" x2="9" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="9" x2="9" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="9" x2="6" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="9" x2="9" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="21" y1="14" y2="18"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_36ad970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_36aeb20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_36af600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_36b01b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_36b1430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_36b1f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_36b27b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_36b30f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3246040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3246040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_36b6000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_36b6000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_36b7690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_36b7690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_36b8480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_36b9f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_36babd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_36bba40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_36bc190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_36bda10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_36be490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_36bec10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_36bf3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_36c04b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_36c0e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_36c1920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_36c22e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_36c38f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_36c4340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_36c54e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_36c6170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_36d4580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_36cc6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_36cd400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_36c8050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1216" width="2218" x="-714" y="-1274"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="1497" x2="1497" y1="-611" y2="-594"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-162380">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -199.758204 -1092.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26633" ObjectName="SW-DY_GH.DY_GH_3816SW"/>
     <cge:Meas_Ref ObjectId="162380"/>
    <cge:TPSR_Ref TObjectID="26633"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162381">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -199.758204 -936.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26634" ObjectName="SW-DY_GH.DY_GH_3811SW"/>
     <cge:Meas_Ref ObjectId="162381"/>
    <cge:TPSR_Ref TObjectID="26634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162382">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -136.758204 -1150.085366)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26635" ObjectName="SW-DY_GH.DY_GH_38167SW"/>
     <cge:Meas_Ref ObjectId="162382"/>
    <cge:TPSR_Ref TObjectID="26635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162482">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 952.241796 -1092.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26646" ObjectName="SW-DY_GH.DY_GH_3846SW"/>
     <cge:Meas_Ref ObjectId="162482"/>
    <cge:TPSR_Ref TObjectID="26646"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162483">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 952.241796 -936.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26647" ObjectName="SW-DY_GH.DY_GH_3841SW"/>
     <cge:Meas_Ref ObjectId="162483"/>
    <cge:TPSR_Ref TObjectID="26647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162484">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1015.241796 -1150.085366)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26648" ObjectName="SW-DY_GH.DY_GH_38467SW"/>
     <cge:Meas_Ref ObjectId="162484"/>
    <cge:TPSR_Ref TObjectID="26648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162414">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 152.241796 -1098.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26637" ObjectName="SW-DY_GH.DY_GH_3826SW"/>
     <cge:Meas_Ref ObjectId="162414"/>
    <cge:TPSR_Ref TObjectID="26637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162417">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 213.241796 -1077.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26640" ObjectName="SW-DY_GH.DY_GH_38260SW"/>
     <cge:Meas_Ref ObjectId="162417"/>
    <cge:TPSR_Ref TObjectID="26640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162415">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 152.241796 -942.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26638" ObjectName="SW-DY_GH.DY_GH_3821SW"/>
     <cge:Meas_Ref ObjectId="162415"/>
    <cge:TPSR_Ref TObjectID="26638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162418">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 213.241796 -999.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26641" ObjectName="SW-DY_GH.DY_GH_38217SW"/>
     <cge:Meas_Ref ObjectId="162418"/>
    <cge:TPSR_Ref TObjectID="26641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162416">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 215.241796 -1156.085366)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26639" ObjectName="SW-DY_GH.DY_GH_38267SW"/>
     <cge:Meas_Ref ObjectId="162416"/>
    <cge:TPSR_Ref TObjectID="26639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 87.203031 -1101.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162449">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 584.241796 -1098.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26643" ObjectName="SW-DY_GH.DY_GH_3836SW"/>
     <cge:Meas_Ref ObjectId="162449"/>
    <cge:TPSR_Ref TObjectID="26643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162450">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 584.241796 -942.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26644" ObjectName="SW-DY_GH.DY_GH_3831SW"/>
     <cge:Meas_Ref ObjectId="162450"/>
    <cge:TPSR_Ref TObjectID="26644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 519.203031 -1101.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162680">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1191.881701 -987.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26659" ObjectName="SW-DY_GH.DY_GH_3901SW"/>
     <cge:Meas_Ref ObjectId="162680"/>
    <cge:TPSR_Ref TObjectID="26659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162681">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1242.881701 -1063.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26660" ObjectName="SW-DY_GH.DY_GH_39017SW"/>
     <cge:Meas_Ref ObjectId="162681"/>
    <cge:TPSR_Ref TObjectID="26660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162526">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 85.241796 -857.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26651" ObjectName="SW-DY_GH.DY_GH_30117SW"/>
     <cge:Meas_Ref ObjectId="162526"/>
    <cge:TPSR_Ref TObjectID="26651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162525">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 24.241796 -868.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26650" ObjectName="SW-DY_GH.DY_GH_3011SW"/>
     <cge:Meas_Ref ObjectId="162525"/>
    <cge:TPSR_Ref TObjectID="26650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162604">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 916.241796 -855.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26656" ObjectName="SW-DY_GH.DY_GH_30217SW"/>
     <cge:Meas_Ref ObjectId="162604"/>
    <cge:TPSR_Ref TObjectID="26656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162603">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 855.241796 -877.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26655" ObjectName="SW-DY_GH.DY_GH_3021SW"/>
     <cge:Meas_Ref ObjectId="162603"/>
    <cge:TPSR_Ref TObjectID="26655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162612">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 853.241796 -558.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26658" ObjectName="SW-DY_GH.DY_GH_0022SW"/>
     <cge:Meas_Ref ObjectId="162612"/>
    <cge:TPSR_Ref TObjectID="26658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 362.203031 -850.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162694">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -169.238710 -436.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26663" ObjectName="SW-DY_GH.DY_GH_0811SW"/>
     <cge:Meas_Ref ObjectId="162694"/>
    <cge:TPSR_Ref TObjectID="26663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162695">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -169.238710 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26664" ObjectName="SW-DY_GH.DY_GH_0816SW"/>
     <cge:Meas_Ref ObjectId="162695"/>
    <cge:TPSR_Ref TObjectID="26664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162722">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 54.761290 -504.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26666" ObjectName="SW-DY_GH.DY_GH_0821SW"/>
     <cge:Meas_Ref ObjectId="162722"/>
    <cge:TPSR_Ref TObjectID="26666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162723">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 54.761290 -416.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26667" ObjectName="SW-DY_GH.DY_GH_0826SW"/>
     <cge:Meas_Ref ObjectId="162723"/>
    <cge:TPSR_Ref TObjectID="26667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228604">
    <use class="BV-38KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 53.761290 -192.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37949" ObjectName="SW-CX_SDZ.CX_SDZ_GH_4811"/>
     <cge:Meas_Ref ObjectId="228604"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228606">
    <use class="BV-38KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 181.761290 -211.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37951" ObjectName="SW-CX_SDZ.CX_SDZ_GH_4821"/>
     <cge:Meas_Ref ObjectId="228606"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162534">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.241796 -560.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26653" ObjectName="SW-DY_GH.DY_GH_0011SW"/>
     <cge:Meas_Ref ObjectId="162534"/>
    <cge:TPSR_Ref TObjectID="26653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162683">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -247.118299 -575.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26661" ObjectName="SW-DY_GH.DY_GH_0901SW"/>
     <cge:Meas_Ref ObjectId="162683"/>
    <cge:TPSR_Ref TObjectID="26661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -242.796969 -681.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162804">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -296.238710 -470.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26686" ObjectName="SW-DY_GH.DY_GH_0871SW"/>
     <cge:Meas_Ref ObjectId="162804"/>
    <cge:TPSR_Ref TObjectID="26686"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -291.796969 -393.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162750">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 325.761290 -438.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26669" ObjectName="SW-DY_GH.DY_GH_0841SW"/>
     <cge:Meas_Ref ObjectId="162750"/>
    <cge:TPSR_Ref TObjectID="26669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162751">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 325.761290 -296.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26670" ObjectName="SW-DY_GH.DY_GH_0846SW"/>
     <cge:Meas_Ref ObjectId="162751"/>
    <cge:TPSR_Ref TObjectID="26670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162778">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 505.761290 -430.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26672" ObjectName="SW-DY_GH.DY_GH_0851SW"/>
     <cge:Meas_Ref ObjectId="162778"/>
    <cge:TPSR_Ref TObjectID="26672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162779">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 505.761290 -288.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26673" ObjectName="SW-DY_GH.DY_GH_0856SW"/>
     <cge:Meas_Ref ObjectId="162779"/>
    <cge:TPSR_Ref TObjectID="26673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162807">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 885.761290 -439.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26675" ObjectName="SW-DY_GH.DY_GH_0912SW"/>
     <cge:Meas_Ref ObjectId="162807"/>
    <cge:TPSR_Ref TObjectID="26675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162808">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 885.761290 -297.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26676" ObjectName="SW-DY_GH.DY_GH_0916SW"/>
     <cge:Meas_Ref ObjectId="162808"/>
    <cge:TPSR_Ref TObjectID="26676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162917">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 761.881701 -571.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26685" ObjectName="SW-DY_GH.DY_GH_0902SW"/>
     <cge:Meas_Ref ObjectId="162917"/>
    <cge:TPSR_Ref TObjectID="26685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 766.203031 -677.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162835">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1159.761290 -498.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26678" ObjectName="SW-DY_GH.DY_GH_0922SW"/>
     <cge:Meas_Ref ObjectId="162835"/>
    <cge:TPSR_Ref TObjectID="26678"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162836">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1159.761290 -400.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26679" ObjectName="SW-DY_GH.DY_GH_0926SW"/>
     <cge:Meas_Ref ObjectId="162836"/>
    <cge:TPSR_Ref TObjectID="26679"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228605">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1159.761290 -295.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37950" ObjectName="SW-CX_SDZ.CX_SDZ_GH_0861"/>
     <cge:Meas_Ref ObjectId="228605"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162863">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 599.761290 -452.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26681" ObjectName="SW-DY_GH.DY_GH_0121SW"/>
     <cge:Meas_Ref ObjectId="162863"/>
    <cge:TPSR_Ref TObjectID="26681"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162864">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 720.761290 -457.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26682" ObjectName="SW-DY_GH.DY_GH_0122SW"/>
     <cge:Meas_Ref ObjectId="162864"/>
    <cge:TPSR_Ref TObjectID="26682"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="16,-186 231,-186 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="16,-186 231,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1023,-346 1335,-346 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1023,-346 1335,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_GH.DY_GH_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-337,-553 617,-553 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26629" ObjectName="BS-DY_GH.DY_GH_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="26629"/></metadata>
   <polyline fill="none" opacity="0" points="-337,-553 617,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_GH.DY_GH_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="708,-553 979,-553 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26630" ObjectName="BS-DY_GH.DY_GH_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="26630"/></metadata>
   <polyline fill="none" opacity="0" points="708,-553 979,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_GH.DY_GH_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-552 1428,-552 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26630" ObjectName="BS-DY_GH.DY_GH_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="26630"/></metadata>
   <polyline fill="none" opacity="0" points="1076,-552 1428,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_GH.DY_GH_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-336,-927 1430,-927 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26628" ObjectName="BS-DY_GH.DY_GH_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="26628"/></metadata>
   <polyline fill="none" opacity="0" points="-336,-927 1430,-927 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-DY_GH.081Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -169.238710 -211.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34225" ObjectName="EC-DY_GH.081Ld"/>
    <cge:TPSR_Ref TObjectID="34225"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_GH.084Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 325.761290 -213.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34226" ObjectName="EC-DY_GH.084Ld"/>
    <cge:TPSR_Ref TObjectID="34226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_GH.085Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 505.761290 -205.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34227" ObjectName="EC-DY_GH.085Ld"/>
    <cge:TPSR_Ref TObjectID="34227"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_GH.091Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 885.761290 -214.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34228" ObjectName="EC-DY_GH.091Ld"/>
    <cge:TPSR_Ref TObjectID="34228"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2fb9020" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -86.758204 -1149.085366)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30329e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1065.241796 -1149.085366)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3012580" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 263.241796 -1076.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3002cf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 263.241796 -998.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3005ae0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 265.241796 -1155.085366)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3026e10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1293.881701 -1062.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3070240" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 135.241796 -856.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30488d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 966.241796 -854.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2f88960">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -237.000000 -1175.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_300b3d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 915.000000 -1175.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fecf90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 85.000000 -1180.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3021980">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 517.000000 -1180.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_302df10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1194.000000 -1093.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_302ed90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1139.000000 -1103.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_308cbc0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -131.323558 -212.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fe1c60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 857.000000 -640.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f2cf70">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 54.000000 -274.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fdb320">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 186.000000 -335.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f3ac70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -281.000000 -676.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f96bd0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 363.676442 -214.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f65a90">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 543.676442 -206.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f4ba60">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 923.676442 -215.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fa1650">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 728.000000 -672.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fafda0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1159.000000 -355.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f7c120">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 599.000000 -497.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e8fa30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 981.000000 -544.000000)" xlink:href="#lightningRod:shape22"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e92260">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1287.000000 -287.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ef17f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 25.000000 -641.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ef26c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 54.000000 -370.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2efebb0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1164.000000 -134.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -618.000000 -1193.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-162288" ratioFlag="0">
    <text fill="rgb(0,255,127)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -629.000000 -1100.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162288" ObjectName="DY_GH:DY_GH_3ⅠM_Hz"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-215178" ratioFlag="0">
    <text fill="rgb(0,255,127)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -593.000000 -976.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215178" ObjectName="DY_GH:DY_GH_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218701" ratioFlag="0">
    <text fill="rgb(0,255,127)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -591.000000 -936.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218701" ObjectName="DY_GH:DY_GH_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218699" ratioFlag="0">
    <text fill="rgb(0,255,127)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -594.000000 -1058.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218699" ObjectName="DY_GH:DY_GH_sumP1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-218700" ratioFlag="0">
    <text fill="rgb(0,255,127)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -594.000000 -1018.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="218700" ObjectName="DY_GH:DY_GH_sumP2"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-162230" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -75.000000 -1079.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162230" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26632"/>
     <cge:Term_Ref ObjectID="20487"/>
    <cge:TPSR_Ref TObjectID="26632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-162231" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -75.000000 -1079.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162231" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26632"/>
     <cge:Term_Ref ObjectID="20487"/>
    <cge:TPSR_Ref TObjectID="26632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-162227" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -75.000000 -1079.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162227" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26632"/>
     <cge:Term_Ref ObjectID="20487"/>
    <cge:TPSR_Ref TObjectID="26632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-162228" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -75.000000 -1079.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162228" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26632"/>
     <cge:Term_Ref ObjectID="20487"/>
    <cge:TPSR_Ref TObjectID="26632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-162229" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -75.000000 -1079.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162229" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26632"/>
     <cge:Term_Ref ObjectID="20487"/>
    <cge:TPSR_Ref TObjectID="26632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-162232" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -75.000000 -1079.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162232" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26632"/>
     <cge:Term_Ref ObjectID="20487"/>
    <cge:TPSR_Ref TObjectID="26632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-162237" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 363.000000 -1083.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162237" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26636"/>
     <cge:Term_Ref ObjectID="37683"/>
    <cge:TPSR_Ref TObjectID="26636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-162238" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 363.000000 -1083.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162238" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26636"/>
     <cge:Term_Ref ObjectID="37683"/>
    <cge:TPSR_Ref TObjectID="26636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-162233" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 363.000000 -1083.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162233" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26636"/>
     <cge:Term_Ref ObjectID="37683"/>
    <cge:TPSR_Ref TObjectID="26636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-162234" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 363.000000 -1083.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162234" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26636"/>
     <cge:Term_Ref ObjectID="37683"/>
    <cge:TPSR_Ref TObjectID="26636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-162235" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 363.000000 -1083.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162235" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26636"/>
     <cge:Term_Ref ObjectID="37683"/>
    <cge:TPSR_Ref TObjectID="26636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-162239" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 363.000000 -1083.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162239" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26636"/>
     <cge:Term_Ref ObjectID="37683"/>
    <cge:TPSR_Ref TObjectID="26636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-162244" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 712.000000 -1077.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162244" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26642"/>
     <cge:Term_Ref ObjectID="37695"/>
    <cge:TPSR_Ref TObjectID="26642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-162245" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 712.000000 -1077.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162245" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26642"/>
     <cge:Term_Ref ObjectID="37695"/>
    <cge:TPSR_Ref TObjectID="26642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-162240" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 712.000000 -1077.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162240" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26642"/>
     <cge:Term_Ref ObjectID="37695"/>
    <cge:TPSR_Ref TObjectID="26642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-162241" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 712.000000 -1077.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162241" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26642"/>
     <cge:Term_Ref ObjectID="37695"/>
    <cge:TPSR_Ref TObjectID="26642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-162242" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 712.000000 -1077.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162242" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26642"/>
     <cge:Term_Ref ObjectID="37695"/>
    <cge:TPSR_Ref TObjectID="26642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-162246" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 712.000000 -1077.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162246" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26642"/>
     <cge:Term_Ref ObjectID="37695"/>
    <cge:TPSR_Ref TObjectID="26642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-162250" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1076.000000 -1079.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162250" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26645"/>
     <cge:Term_Ref ObjectID="37701"/>
    <cge:TPSR_Ref TObjectID="26645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-162251" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1076.000000 -1079.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162251" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26645"/>
     <cge:Term_Ref ObjectID="37701"/>
    <cge:TPSR_Ref TObjectID="26645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-162247" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1076.000000 -1079.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162247" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26645"/>
     <cge:Term_Ref ObjectID="37701"/>
    <cge:TPSR_Ref TObjectID="26645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-162248" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1076.000000 -1079.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162248" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26645"/>
     <cge:Term_Ref ObjectID="37701"/>
    <cge:TPSR_Ref TObjectID="26645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-162249" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1076.000000 -1079.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162249" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26645"/>
     <cge:Term_Ref ObjectID="37701"/>
    <cge:TPSR_Ref TObjectID="26645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-162252" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1076.000000 -1079.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162252" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26645"/>
     <cge:Term_Ref ObjectID="37701"/>
    <cge:TPSR_Ref TObjectID="26645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-162270" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 806.000000 -914.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162270" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26654"/>
     <cge:Term_Ref ObjectID="37719"/>
    <cge:TPSR_Ref TObjectID="26654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-162271" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 806.000000 -914.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162271" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26654"/>
     <cge:Term_Ref ObjectID="37719"/>
    <cge:TPSR_Ref TObjectID="26654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-162267" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 806.000000 -914.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162267" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26654"/>
     <cge:Term_Ref ObjectID="37719"/>
    <cge:TPSR_Ref TObjectID="26654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-162268" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 806.000000 -914.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162268" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26654"/>
     <cge:Term_Ref ObjectID="37719"/>
    <cge:TPSR_Ref TObjectID="26654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-162269" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 806.000000 -914.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162269" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26654"/>
     <cge:Term_Ref ObjectID="37719"/>
    <cge:TPSR_Ref TObjectID="26654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-162272" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 806.000000 -914.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162272" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26654"/>
     <cge:Term_Ref ObjectID="37719"/>
    <cge:TPSR_Ref TObjectID="26654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-162276" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 976.000000 -677.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162276" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26657"/>
     <cge:Term_Ref ObjectID="37725"/>
    <cge:TPSR_Ref TObjectID="26657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-162277" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 976.000000 -677.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162277" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26657"/>
     <cge:Term_Ref ObjectID="37725"/>
    <cge:TPSR_Ref TObjectID="26657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-162273" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 976.000000 -677.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162273" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26657"/>
     <cge:Term_Ref ObjectID="37725"/>
    <cge:TPSR_Ref TObjectID="26657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-162274" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 976.000000 -677.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162274" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26657"/>
     <cge:Term_Ref ObjectID="37725"/>
    <cge:TPSR_Ref TObjectID="26657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-162275" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 976.000000 -677.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162275" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26657"/>
     <cge:Term_Ref ObjectID="37725"/>
    <cge:TPSR_Ref TObjectID="26657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-162278" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 976.000000 -677.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162278" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26657"/>
     <cge:Term_Ref ObjectID="37725"/>
    <cge:TPSR_Ref TObjectID="26657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-162256" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -28.000000 -910.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162256" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26649"/>
     <cge:Term_Ref ObjectID="37709"/>
    <cge:TPSR_Ref TObjectID="26649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-162257" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -28.000000 -910.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162257" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26649"/>
     <cge:Term_Ref ObjectID="37709"/>
    <cge:TPSR_Ref TObjectID="26649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-162253" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -28.000000 -910.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162253" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26649"/>
     <cge:Term_Ref ObjectID="37709"/>
    <cge:TPSR_Ref TObjectID="26649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-162254" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -28.000000 -910.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162254" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26649"/>
     <cge:Term_Ref ObjectID="37709"/>
    <cge:TPSR_Ref TObjectID="26649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-162255" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -28.000000 -910.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162255" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26649"/>
     <cge:Term_Ref ObjectID="37709"/>
    <cge:TPSR_Ref TObjectID="26649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-162258" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -28.000000 -910.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162258" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26649"/>
     <cge:Term_Ref ObjectID="37709"/>
    <cge:TPSR_Ref TObjectID="26649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-162262" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -28.000000 -677.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162262" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26652"/>
     <cge:Term_Ref ObjectID="37715"/>
    <cge:TPSR_Ref TObjectID="26652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-162263" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -28.000000 -677.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162263" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26652"/>
     <cge:Term_Ref ObjectID="37715"/>
    <cge:TPSR_Ref TObjectID="26652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-162259" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -28.000000 -677.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162259" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26652"/>
     <cge:Term_Ref ObjectID="37715"/>
    <cge:TPSR_Ref TObjectID="26652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-162260" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -28.000000 -677.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162260" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26652"/>
     <cge:Term_Ref ObjectID="37715"/>
    <cge:TPSR_Ref TObjectID="26652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-162261" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -28.000000 -677.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162261" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26652"/>
     <cge:Term_Ref ObjectID="37715"/>
    <cge:TPSR_Ref TObjectID="26652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-162264" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -28.000000 -677.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162264" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26652"/>
     <cge:Term_Ref ObjectID="37715"/>
    <cge:TPSR_Ref TObjectID="26652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-162308" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -151.000000 -175.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162308" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26662"/>
     <cge:Term_Ref ObjectID="37735"/>
    <cge:TPSR_Ref TObjectID="26662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-162309" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -151.000000 -175.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162309" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26662"/>
     <cge:Term_Ref ObjectID="37735"/>
    <cge:TPSR_Ref TObjectID="26662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-162305" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -151.000000 -175.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162305" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26662"/>
     <cge:Term_Ref ObjectID="37735"/>
    <cge:TPSR_Ref TObjectID="26662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-162306" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -151.000000 -175.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162306" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26662"/>
     <cge:Term_Ref ObjectID="37735"/>
    <cge:TPSR_Ref TObjectID="26662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-162307" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -151.000000 -175.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162307" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26662"/>
     <cge:Term_Ref ObjectID="37735"/>
    <cge:TPSR_Ref TObjectID="26662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-162310" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -151.000000 -175.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162310" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26662"/>
     <cge:Term_Ref ObjectID="37735"/>
    <cge:TPSR_Ref TObjectID="26662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-162314" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 77.000000 -173.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162314" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26665"/>
     <cge:Term_Ref ObjectID="37741"/>
    <cge:TPSR_Ref TObjectID="26665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-162315" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 77.000000 -173.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162315" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26665"/>
     <cge:Term_Ref ObjectID="37741"/>
    <cge:TPSR_Ref TObjectID="26665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-162311" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 77.000000 -173.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162311" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26665"/>
     <cge:Term_Ref ObjectID="37741"/>
    <cge:TPSR_Ref TObjectID="26665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-162312" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 77.000000 -173.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162312" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26665"/>
     <cge:Term_Ref ObjectID="37741"/>
    <cge:TPSR_Ref TObjectID="26665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-162313" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 77.000000 -173.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162313" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26665"/>
     <cge:Term_Ref ObjectID="37741"/>
    <cge:TPSR_Ref TObjectID="26665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-162316" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 77.000000 -173.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162316" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26665"/>
     <cge:Term_Ref ObjectID="37741"/>
    <cge:TPSR_Ref TObjectID="26665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-162320" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 353.000000 -172.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162320" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26668"/>
     <cge:Term_Ref ObjectID="37747"/>
    <cge:TPSR_Ref TObjectID="26668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-162321" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 353.000000 -172.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162321" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26668"/>
     <cge:Term_Ref ObjectID="37747"/>
    <cge:TPSR_Ref TObjectID="26668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-162317" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 353.000000 -172.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162317" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26668"/>
     <cge:Term_Ref ObjectID="37747"/>
    <cge:TPSR_Ref TObjectID="26668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-162318" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 353.000000 -172.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162318" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26668"/>
     <cge:Term_Ref ObjectID="37747"/>
    <cge:TPSR_Ref TObjectID="26668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-162319" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 353.000000 -172.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162319" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26668"/>
     <cge:Term_Ref ObjectID="37747"/>
    <cge:TPSR_Ref TObjectID="26668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-162322" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 353.000000 -172.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162322" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26668"/>
     <cge:Term_Ref ObjectID="37747"/>
    <cge:TPSR_Ref TObjectID="26668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-162326" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 532.000000 -171.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162326" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26671"/>
     <cge:Term_Ref ObjectID="37753"/>
    <cge:TPSR_Ref TObjectID="26671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-162327" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 532.000000 -171.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162327" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26671"/>
     <cge:Term_Ref ObjectID="37753"/>
    <cge:TPSR_Ref TObjectID="26671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-162323" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 532.000000 -171.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162323" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26671"/>
     <cge:Term_Ref ObjectID="37753"/>
    <cge:TPSR_Ref TObjectID="26671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-162324" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 532.000000 -171.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162324" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26671"/>
     <cge:Term_Ref ObjectID="37753"/>
    <cge:TPSR_Ref TObjectID="26671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-162325" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 532.000000 -171.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162325" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26671"/>
     <cge:Term_Ref ObjectID="37753"/>
    <cge:TPSR_Ref TObjectID="26671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-162328" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 532.000000 -171.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162328" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26671"/>
     <cge:Term_Ref ObjectID="37753"/>
    <cge:TPSR_Ref TObjectID="26671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-162332" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 925.000000 -174.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162332" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26674"/>
     <cge:Term_Ref ObjectID="37759"/>
    <cge:TPSR_Ref TObjectID="26674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-162333" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 925.000000 -174.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162333" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26674"/>
     <cge:Term_Ref ObjectID="37759"/>
    <cge:TPSR_Ref TObjectID="26674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-162329" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 925.000000 -174.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162329" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26674"/>
     <cge:Term_Ref ObjectID="37759"/>
    <cge:TPSR_Ref TObjectID="26674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-162330" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 925.000000 -174.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162330" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26674"/>
     <cge:Term_Ref ObjectID="37759"/>
    <cge:TPSR_Ref TObjectID="26674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-162331" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 925.000000 -174.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162331" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26674"/>
     <cge:Term_Ref ObjectID="37759"/>
    <cge:TPSR_Ref TObjectID="26674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-162334" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 925.000000 -174.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162334" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26674"/>
     <cge:Term_Ref ObjectID="37759"/>
    <cge:TPSR_Ref TObjectID="26674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-162281" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.000000 -1075.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162281" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26628"/>
     <cge:Term_Ref ObjectID="20482"/>
    <cge:TPSR_Ref TObjectID="26628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-162282" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.000000 -1075.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162282" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26628"/>
     <cge:Term_Ref ObjectID="20482"/>
    <cge:TPSR_Ref TObjectID="26628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-162283" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.000000 -1075.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162283" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26628"/>
     <cge:Term_Ref ObjectID="20482"/>
    <cge:TPSR_Ref TObjectID="26628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-162287" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.000000 -1075.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162287" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26628"/>
     <cge:Term_Ref ObjectID="20482"/>
    <cge:TPSR_Ref TObjectID="26628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-162284" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.000000 -1075.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162284" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26628"/>
     <cge:Term_Ref ObjectID="20482"/>
    <cge:TPSR_Ref TObjectID="26628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-162285" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.000000 -1075.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162285" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26628"/>
     <cge:Term_Ref ObjectID="20482"/>
    <cge:TPSR_Ref TObjectID="26628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-162286" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.000000 -1075.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162286" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26628"/>
     <cge:Term_Ref ObjectID="20482"/>
    <cge:TPSR_Ref TObjectID="26628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-162288" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.000000 -1075.000000) translate(0,141)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162288" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26628"/>
     <cge:Term_Ref ObjectID="20482"/>
    <cge:TPSR_Ref TObjectID="26628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-162297" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1396.000000 -696.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162297" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26630"/>
     <cge:Term_Ref ObjectID="20484"/>
    <cge:TPSR_Ref TObjectID="26630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-162298" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1396.000000 -696.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162298" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26630"/>
     <cge:Term_Ref ObjectID="20484"/>
    <cge:TPSR_Ref TObjectID="26630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-162299" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1396.000000 -696.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162299" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26630"/>
     <cge:Term_Ref ObjectID="20484"/>
    <cge:TPSR_Ref TObjectID="26630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-162303" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1396.000000 -696.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162303" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26630"/>
     <cge:Term_Ref ObjectID="20484"/>
    <cge:TPSR_Ref TObjectID="26630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-162300" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1396.000000 -696.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162300" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26630"/>
     <cge:Term_Ref ObjectID="20484"/>
    <cge:TPSR_Ref TObjectID="26630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-162301" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1396.000000 -696.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162301" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26630"/>
     <cge:Term_Ref ObjectID="20484"/>
    <cge:TPSR_Ref TObjectID="26630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-162302" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1396.000000 -696.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162302" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26630"/>
     <cge:Term_Ref ObjectID="20484"/>
    <cge:TPSR_Ref TObjectID="26630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-162304" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1396.000000 -696.000000) translate(0,141)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162304" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26630"/>
     <cge:Term_Ref ObjectID="20484"/>
    <cge:TPSR_Ref TObjectID="26630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-162289" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 544.000000 -706.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162289" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26629"/>
     <cge:Term_Ref ObjectID="20483"/>
    <cge:TPSR_Ref TObjectID="26629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-162290" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 544.000000 -706.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162290" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26629"/>
     <cge:Term_Ref ObjectID="20483"/>
    <cge:TPSR_Ref TObjectID="26629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-162291" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 544.000000 -706.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162291" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26629"/>
     <cge:Term_Ref ObjectID="20483"/>
    <cge:TPSR_Ref TObjectID="26629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-162295" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 544.000000 -706.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162295" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26629"/>
     <cge:Term_Ref ObjectID="20483"/>
    <cge:TPSR_Ref TObjectID="26629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-162292" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 544.000000 -706.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162292" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26629"/>
     <cge:Term_Ref ObjectID="20483"/>
    <cge:TPSR_Ref TObjectID="26629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-162293" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 544.000000 -706.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162293" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26629"/>
     <cge:Term_Ref ObjectID="20483"/>
    <cge:TPSR_Ref TObjectID="26629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-162294" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 544.000000 -706.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162294" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26629"/>
     <cge:Term_Ref ObjectID="20483"/>
    <cge:TPSR_Ref TObjectID="26629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-162296" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 544.000000 -706.000000) translate(0,141)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162296" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26629"/>
     <cge:Term_Ref ObjectID="20483"/>
    <cge:TPSR_Ref TObjectID="26629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-162338" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1280.000000 -536.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162338" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26677"/>
     <cge:Term_Ref ObjectID="37765"/>
    <cge:TPSR_Ref TObjectID="26677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-162339" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1280.000000 -536.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162339" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26677"/>
     <cge:Term_Ref ObjectID="37765"/>
    <cge:TPSR_Ref TObjectID="26677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-162335" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1280.000000 -536.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162335" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26677"/>
     <cge:Term_Ref ObjectID="37765"/>
    <cge:TPSR_Ref TObjectID="26677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-162336" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1280.000000 -536.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162336" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26677"/>
     <cge:Term_Ref ObjectID="37765"/>
    <cge:TPSR_Ref TObjectID="26677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-162337" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1280.000000 -536.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162337" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26677"/>
     <cge:Term_Ref ObjectID="37765"/>
    <cge:TPSR_Ref TObjectID="26677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-162340" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1280.000000 -536.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162340" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26677"/>
     <cge:Term_Ref ObjectID="37765"/>
    <cge:TPSR_Ref TObjectID="26677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-162266" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 145.000000 -767.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162266" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26683"/>
     <cge:Term_Ref ObjectID="37777"/>
    <cge:TPSR_Ref TObjectID="26683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-162280" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 969.000000 -766.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162280" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26684"/>
     <cge:Term_Ref ObjectID="37784"/>
    <cge:TPSR_Ref TObjectID="26684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-162265" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 146.000000 -740.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162265" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26683"/>
     <cge:Term_Ref ObjectID="37777"/>
    <cge:TPSR_Ref TObjectID="26683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-162279" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 970.000000 -738.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162279" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26684"/>
     <cge:Term_Ref ObjectID="37781"/>
    <cge:TPSR_Ref TObjectID="26684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-162341" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 673.000000 -419.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162341" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26680"/>
     <cge:Term_Ref ObjectID="37771"/>
    <cge:TPSR_Ref TObjectID="26680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-162342" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 673.000000 -419.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162342" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26680"/>
     <cge:Term_Ref ObjectID="37771"/>
    <cge:TPSR_Ref TObjectID="26680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-162343" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 673.000000 -419.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="162343" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26680"/>
     <cge:Term_Ref ObjectID="37771"/>
    <cge:TPSR_Ref TObjectID="26680"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-606" y="-1252"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-606" y="-1252"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-655" y="-1269"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="-178" y="-1048"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="-178" y="-1048"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="171" y="-1054"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="171" y="-1054"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="603" y="-1054"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="603" y="-1054"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="971" y="-1048"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="971" y="-1048"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="82" x="-83" y="-772"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="82" x="-83" y="-772"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="83" x="904" y="-794"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="83" x="904" y="-794"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="-151" y="-395"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="-151" y="-395"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="74" y="-488"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="74" y="-488"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="345" y="-397"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="345" y="-397"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="525" y="-389"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="525" y="-389"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="665" y="-462"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="665" y="-462"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="905" y="-398"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="905" y="-398"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1179" y="-481"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1179" y="-481"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="26" qtmmishow="hidden" width="65" x="-704" y="-849"/>
    </a>
   <metadata/><rect fill="white" height="26" opacity="0" stroke="white" transform="" width="65" x="-704" y="-849"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-380" y="-1233"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-380" y="-1233"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-380" y="-1268"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-380" y="-1268"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="-441,-1255 -444,-1258 -444,-1209 -441,-1212 -441,-1255" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="-441,-1255 -444,-1258 -391,-1258 -394,-1255 -441,-1255" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="-441,-1212 -444,-1209 -391,-1209 -394,-1212 -441,-1212" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="-394,-1255 -391,-1258 -391,-1209 -394,-1212 -394,-1255" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="43" stroke="rgb(255,255,255)" width="47" x="-441" y="-1255"/>
     <rect fill="none" height="43" qtmmishow="hidden" stroke="rgb(0,0,0)" width="47" x="-441" y="-1255"/>
    </a>
   <metadata/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-606" y="-1252"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/></g>
   <g href="35kV桂花变桂干线381间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="-178" y="-1048"/></g>
   <g href="35kV桂花变碧桂线382间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="171" y="-1054"/></g>
   <g href="35kV桂花变天桂线383间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="603" y="-1054"/></g>
   <g href="35kV桂花变桂湾线384间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="971" y="-1048"/></g>
   <g href="35kV桂花变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="82" x="-83" y="-772"/></g>
   <g href="35kV桂花变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="83" x="904" y="-794"/></g>
   <g href="35kV桂花变矿山线081间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="-151" y="-395"/></g>
   <g href="35kV桂花变＃1发电机082间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="74" y="-488"/></g>
   <g href="35kV桂花变选厂线084间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="345" y="-397"/></g>
   <g href="35kV桂花变桂花线085间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="525" y="-389"/></g>
   <g href="35kV桂花变分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="665" y="-462"/></g>
   <g href="35kV桂花变冶基厂线091间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="905" y="-398"/></g>
   <g href="35kV桂花变＃2发电机092间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1179" y="-481"/></g>
   <g href="35kV桂花变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="26" qtmmishow="hidden" width="65" x="-704" y="-849"/></g>
   <g href="cx_配调_配网接线图35_大姚.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-380" y="-1233"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-380" y="-1268"/></g>
   <g href="AVC桂花站.svg" style="fill-opacity:0"><rect height="43" qtmmishow="hidden" stroke="rgb(0,0,0)" width="47" x="-441" y="-1255"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-162378">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -200.758204 -1019.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26632" ObjectName="SW-DY_GH.DY_GH_381BK"/>
     <cge:Meas_Ref ObjectId="162378"/>
    <cge:TPSR_Ref TObjectID="26632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162480">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 952.241796 -1019.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26645" ObjectName="SW-DY_GH.DY_GH_384BK"/>
     <cge:Meas_Ref ObjectId="162480"/>
    <cge:TPSR_Ref TObjectID="26645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162412">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 152.241796 -1025.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26636" ObjectName="SW-DY_GH.DY_GH_382BK"/>
     <cge:Meas_Ref ObjectId="162412"/>
    <cge:TPSR_Ref TObjectID="26636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162447">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 584.241796 -1025.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26642" ObjectName="SW-DY_GH.DY_GH_383BK"/>
     <cge:Meas_Ref ObjectId="162447"/>
    <cge:TPSR_Ref TObjectID="26642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162523">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 24.241796 -806.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26649" ObjectName="SW-DY_GH.DY_GH_301BK"/>
     <cge:Meas_Ref ObjectId="162523"/>
    <cge:TPSR_Ref TObjectID="26649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162532">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 21.241796 -602.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26652" ObjectName="SW-DY_GH.DY_GH_001BK"/>
     <cge:Meas_Ref ObjectId="162532"/>
    <cge:TPSR_Ref TObjectID="26652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162601">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 855.241796 -804.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26654" ObjectName="SW-DY_GH.DY_GH_302BK"/>
     <cge:Meas_Ref ObjectId="162601"/>
    <cge:TPSR_Ref TObjectID="26654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162610">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 853.241796 -601.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26657" ObjectName="SW-DY_GH.DY_GH_002BK"/>
     <cge:Meas_Ref ObjectId="162610"/>
    <cge:TPSR_Ref TObjectID="26657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162692">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -169.787729 -366.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26662" ObjectName="SW-DY_GH.DY_GH_081BK"/>
     <cge:Meas_Ref ObjectId="162692"/>
    <cge:TPSR_Ref TObjectID="26662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162720">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 55.212271 -459.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26665" ObjectName="SW-DY_GH.DY_GH_082BK"/>
     <cge:Meas_Ref ObjectId="162720"/>
    <cge:TPSR_Ref TObjectID="26665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228601">
    <use class="BV-38KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 54.212271 -235.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37946" ObjectName="SW-CX_SDZ.CX_SDZ_GH_481"/>
     <cge:Meas_Ref ObjectId="228601"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228602">
    <use class="BV-38KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 182.212271 -280.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37948" ObjectName="SW-CX_SDZ.CX_SDZ_GH_482"/>
     <cge:Meas_Ref ObjectId="228602"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162748">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 326.212271 -368.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26668" ObjectName="SW-DY_GH.DY_GH_084BK"/>
     <cge:Meas_Ref ObjectId="162748"/>
    <cge:TPSR_Ref TObjectID="26668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162776">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 506.212271 -360.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26671" ObjectName="SW-DY_GH.DY_GH_085BK"/>
     <cge:Meas_Ref ObjectId="162776"/>
    <cge:TPSR_Ref TObjectID="26671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162805">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 886.212271 -369.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26674" ObjectName="SW-DY_GH.DY_GH_091BK"/>
     <cge:Meas_Ref ObjectId="162805"/>
    <cge:TPSR_Ref TObjectID="26674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162833">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1160.212271 -452.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26677" ObjectName="SW-DY_GH.DY_GH_092BK"/>
     <cge:Meas_Ref ObjectId="162833"/>
    <cge:TPSR_Ref TObjectID="26677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228603">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1160.212271 -249.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37947" ObjectName="SW-CX_SDZ.CX_SDZ_GH_086"/>
     <cge:Meas_Ref ObjectId="228603"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-162861">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 655.000000 -430.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26680" ObjectName="SW-DY_GH.DY_GH_012BK"/>
     <cge:Meas_Ref ObjectId="162861"/>
    <cge:TPSR_Ref TObjectID="26680"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_BM" endPointId="0" endStationName="DY_GH" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_bigui" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="161,-1199 161,-1233 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38086" ObjectName="AC-35kV.LN_bigui"/>
    <cge:TPSR_Ref TObjectID="38086_SS-231"/></metadata>
   <polyline fill="none" opacity="0" points="161,-1199 161,-1233 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_ST" endPointId="0" endStationName="DY_GH" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_tiangui" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="593,-1192 593,-1225 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38089" ObjectName="AC-35kV.LN_tiangui"/>
    <cge:TPSR_Ref TObjectID="38089_SS-231"/></metadata>
   <polyline fill="none" opacity="0" points="593,-1192 593,-1225 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_WB" endPointId="0" endStationName="DY_GH" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_guiwan" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="961,-1191 961,-1227 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38087" ObjectName="AC-35kV.LN_guiwan"/>
    <cge:TPSR_Ref TObjectID="38087_SS-231"/></metadata>
   <polyline fill="none" opacity="0" points="961,-1191 961,-1227 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 166.000000 -415.000000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1145.000000 -78.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="63" cy="-186" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="191" cy="-186" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26629" cx="-160" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26629" cx="-287" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26629" cx="-238" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26629" cx="335" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26629" cx="64" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26629" cx="609" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26630" cx="895" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26630" cx="771" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26630" cx="730" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26630" cx="977" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1169" cy="-346" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1169" cy="-346" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1294" cy="-346" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26629" cx="515" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26630" cx="862" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26629" cx="31" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26630" cx="1169" cy="-552" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26630" cx="1080" cy="-552" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26628" cx="-191" cy="-927" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26628" cx="961" cy="-927" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26628" cx="161" cy="-927" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26628" cx="593" cy="-927" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26628" cx="367" cy="-927" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26628" cx="864" cy="-927" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26628" cx="33" cy="-927" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26628" cx="1201" cy="-927" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(64,64,64)" font-family="SimSun" font-size="20" graphid="g_2e7a3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -559.000000 -1241.500000) translate(0,16)">桂花变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2de3d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2de3d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2de3d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,59)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2de3d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2de3d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,101)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2de3d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2de3d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,143)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2de3d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2de3d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,185)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2de3d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2de3d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,227)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3095430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3095430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3095430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3095430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3095430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3095430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3095430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3095430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3095430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3095430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3095430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3095430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3095430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3095430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3095430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3095430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3095430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3095430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,374)">联系方式：0878-6148325</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3095430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,395)">                0878-6148326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30a0af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 318.000000 -788.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3031d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -226.000000 -1262.000000) translate(0,12)">35kV桂干线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_300c3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 916.000000 -1260.000000) translate(0,12)">35kV桂湾线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fecb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 118.000000 -1263.000000) translate(0,12)">35kV碧桂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f5d1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 73.000000 -1054.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3021370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 547.000000 -1261.000000) translate(0,12)">35kV天桂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f59d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 505.000000 -1054.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_302f980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1161.000000 -1209.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd0430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -186.000000 -208.000000) translate(0,12)">矿山线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fbde70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 72.761290 -263.000000) translate(0,12)">481</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f2ca80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 70.000000 -222.000000) translate(0,12)">4811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f30340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 198.000000 -241.000000) translate(0,12)">4821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fdac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 200.761290 -310.000000) translate(0,12)">482</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f3ba20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -290.000000 -799.000000) translate(0,12)">10kVⅠ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f9acd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 319.000000 -201.000000) translate(0,12)">选厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f69b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 491.000000 -198.000000) translate(0,12)">桂花线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f4fb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 872.000000 -203.000000) translate(0,12)">冶基厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fa2400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 707.000000 -789.000000) translate(0,12)">10kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f744c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1177.000000 -325.000000) translate(0,12)">0861</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f76a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1181.761290 -277.000000) translate(0,12)">086</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e94020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1216.000000 -368.000000) translate(0,12)">水电站10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e94b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1140.000000 -78.000000) translate(0,12)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e952d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1091.000000 -235.000000) translate(0,12)">2号发电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e952d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1091.000000 -235.000000) translate(0,27)">机升压变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e95c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 103.000000 -205.000000) translate(0,12)">0.4kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f02130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -17.000000 -360.000000) translate(0,12)">1号发电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f02130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -17.000000 -360.000000) translate(0,27)">机升压变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f023b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 161.000000 -490.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f025f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -337.000000 -311.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f02840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -178.000000 -1048.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f02a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -180.000000 -965.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f02cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -181.000000 -1119.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f02ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -129.000000 -1180.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f032b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 171.000000 -1054.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f03710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 168.000000 -1128.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f03950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 168.000000 -972.000000) translate(0,12)">3821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f03b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 216.000000 -1030.000000) translate(0,12)">38217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f03dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 215.000000 -1108.000000) translate(0,12)">38260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f04010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 217.000000 -1187.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f04250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 603.000000 -1054.000000) translate(0,12)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f04490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 600.000000 -1128.000000) translate(0,12)">3836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f046d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 600.000000 -972.000000) translate(0,12)">3831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f04910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 971.000000 -1048.000000) translate(0,12)">384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f04b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 968.000000 -966.000000) translate(0,12)">3841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f04d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 968.000000 -1122.000000) translate(0,12)">3846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f04fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1018.000000 -1181.000000) translate(0,12)">38467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f05210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1208.000000 -1017.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f05730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1246.000000 -1094.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f059b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 49.000000 -832.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f05bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -898.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f05e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 88.000000 -888.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f06070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -631.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f062b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 38.000000 -590.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f064f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 874.000000 -833.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f06730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 871.000000 -907.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f06970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 919.000000 -886.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f06bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 872.000000 -630.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f06df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 869.000000 -588.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f07030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 778.000000 -601.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f07270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 904.000000 -794.000000) translate(0,12)">2号主变压器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f079a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 199.000000 -575.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f07e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 665.000000 -462.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f08040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 617.000000 -483.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f08280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 737.000000 -487.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f08ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -231.000000 -605.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f08f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -151.000000 -395.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f09170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -153.000000 -466.000000) translate(0,12)">0811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f093b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -153.000000 -324.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f095f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 74.000000 -488.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f09830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 71.000000 -446.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f09a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 71.000000 -534.000000) translate(0,12)">0821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f09cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 -397.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f09ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 342.000000 -326.000000) translate(0,12)">0846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0a130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 342.000000 -468.000000) translate(0,12)">0841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0a370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 525.000000 -389.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0a5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 522.000000 -460.000000) translate(0,12)">0851</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0a7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 522.000000 -318.000000) translate(0,12)">0856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0aa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 905.000000 -398.000000) translate(0,12)">091</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0ac70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 902.000000 -469.000000) translate(0,12)">0912</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0aeb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 902.000000 -327.000000) translate(0,12)">0916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0b0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1179.000000 -481.000000) translate(0,12)">092</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0b330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1176.000000 -430.000000) translate(0,12)">0926</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0b570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1176.000000 -528.000000) translate(0,12)">0922</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef0010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -280.000000 -500.000000) translate(0,12)">0871</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef11e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -83.000000 -772.000000) translate(0,12)">1号主变压器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fd780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1108.000000 -574.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e97990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 412.000000 -947.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e99bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -130.000000 -751.000000) translate(0,12)">S7-2000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e99bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -130.000000 -751.000000) translate(0,27)">35±5%/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e99bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -130.000000 -751.000000) translate(0,42)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e99bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -130.000000 -751.000000) translate(0,57)">6.3%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9b4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1003.000000 -796.000000) translate(0,12)">SZ11-2500/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9b4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1003.000000 -796.000000) translate(0,27)">35±3*2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9b4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1003.000000 -796.000000) translate(0,42)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9b4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1003.000000 -796.000000) translate(0,57)">7.07%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9d570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -703.000000 -843.000000) translate(0,12)">公共信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2e9e6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -369.000000 -1225.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_30327e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -369.000000 -1260.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea1230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 198.000000 -241.000000) translate(0,12)">4821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea1470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 70.000000 -222.000000) translate(0,12)">4811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea16b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1176.000000 -325.000000) translate(0,12)">0861</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea18f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1312.000000 -281.000000) translate(0,12)">水电站10kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea18f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1312.000000 -281.000000) translate(0,27)">母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea1b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1190.761290 -242.000000) translate(0,12)">S7-630/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea1b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1190.761290 -242.000000) translate(0,27)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea1b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1190.761290 -242.000000) translate(0,42)">4.6%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea1da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1038.000000 -290.000000) translate(0,12)">2号发电机升压变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea1da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1038.000000 -290.000000) translate(0,27)">10kV侧086断路器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea2610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 87.761290 -364.000000) translate(0,12)">S7-560/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea2610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 87.761290 -364.000000) translate(0,27)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea2610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 87.761290 -364.000000) translate(0,42)">4.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2ea43a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -436.000000 -1244.000000) translate(0,16)">AVC</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef3410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -121.000000 1039.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef5470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -121.000000 1022.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef58c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -121.000000 1004.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef5c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -117.000000 987.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef6560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -135.000000 1076.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef6e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -146.500000 1057.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef76b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 316.000000 1045.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef7900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 316.000000 1028.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef7b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 316.000000 1010.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef7d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 320.000000 993.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef7f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 302.000000 1082.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef8140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 290.500000 1063.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef8440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.000000 1040.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef8690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.000000 1023.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef88a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.000000 1005.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef8ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 667.000000 988.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef8d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 649.000000 1077.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef8f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 637.500000 1058.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef9290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1028.000000 1039.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef9510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1028.000000 1022.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef9750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1028.000000 1004.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef9990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1032.000000 987.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef9bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1014.000000 1076.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef9e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1002.500000 1057.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efa140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -73.000000 873.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efa3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -73.000000 856.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efa600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -73.000000 838.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efa840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -69.000000 821.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efaa80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -87.000000 910.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efacc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -98.500000 891.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efaff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -76.000000 638.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efb270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -76.000000 621.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efb4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -76.000000 603.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efb6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -72.000000 586.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efb930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -90.000000 675.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efbb70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -101.500000 656.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efbea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 764.000000 875.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efc120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 764.000000 858.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efc360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 764.000000 840.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efc5a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 768.000000 823.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efc7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 750.000000 912.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 738.500000 893.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efcd50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 933.000000 637.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efcfd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 933.000000 620.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efd210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 933.000000 602.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efd450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 937.000000 585.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efd690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 919.000000 674.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efd8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.500000 655.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efdc00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -195.000000 139.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efde80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -195.000000 122.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efe0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -195.000000 104.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efe300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -191.000000 87.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efe540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -209.000000 176.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2efe780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -220.500000 157.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f00c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 32.000000 134.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f01130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 32.000000 117.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f01370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 32.000000 99.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f015b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 36.000000 82.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f017f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 18.000000 171.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f01a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.500000 152.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e81d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 309.000000 135.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e8450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 309.000000 118.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e8690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 309.000000 100.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e88d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 313.000000 83.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e8b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 295.000000 172.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e8d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 283.500000 153.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e9080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 484.000000 131.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e9300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 484.000000 114.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e9540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 484.000000 96.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e9780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 488.000000 79.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e99c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 470.000000 168.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e9c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 458.500000 149.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e9f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 875.000000 137.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ea1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 875.000000 120.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ea3f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 875.000000 102.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ea630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 879.000000 85.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ea870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 861.000000 174.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30eaab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 849.500000 155.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30eade0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1237.000000 496.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30eb060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1237.000000 479.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30eb2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1237.000000 461.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30eb4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1241.000000 444.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30eb720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1223.000000 533.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30eb960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1211.500000 514.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ebc90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1341.000000 1021.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ec1a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1335.000000 1074.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ec420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1327.000000 986.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ec660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1327.000000 968.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ec8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1343.000000 951.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ed3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1335.000000 1055.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ed640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1327.000000 1004.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ed880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1335.000000 1038.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30edbb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1342.000000 644.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ede40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1336.000000 697.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ee080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1328.000000 609.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ee2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1328.000000 591.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ee500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1344.000000 574.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ee740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1336.000000 678.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ee980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1328.000000 627.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30eebc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1336.000000 661.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f7830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 489.000000 649.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f7d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 483.000000 702.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f7f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 475.000000 614.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f81b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 475.000000 596.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f83f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 491.000000 579.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f8630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 483.000000 683.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f8870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 475.000000 632.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f8ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 483.000000 666.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f8de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99.000000 768.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f9650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 69.500000 737.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fad60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 927.000000 766.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fafc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 897.500000 735.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fc330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 631.000000 401.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fc550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 631.000000 383.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30fc790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 631.000000 418.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-DY_GH.DY_GH_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="37783"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 826.418267 -702.000000)" xlink:href="#transformer2:shape14_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 826.418267 -702.000000)" xlink:href="#transformer2:shape14_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="26684" ObjectName="TF-DY_GH.DY_GH_2T"/>
    <cge:TPSR_Ref TObjectID="26684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 354.000000 -791.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 354.000000 -791.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 51.000000 -322.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-2KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 51.000000 -322.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -300.000000 -323.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -300.000000 -323.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1156.000000 -200.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1156.000000 -200.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-DY_GH.DY_GH_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="37779"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7.000000 -704.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7.000000 -704.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="26683" ObjectName="TF-DY_GH.DY_GH_1T"/>
    <cge:TPSR_Ref TObjectID="26683"/></metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-153548" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -418.000000 -1163.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26039" ObjectName="DYN-DY_GH"/>
     <cge:Meas_Ref ObjectId="153548"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2f5cab0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 82.500000 -1094.500000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f59460">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 515.500000 -1094.500000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3027da0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1182.000000 -1153.000000)" xlink:href="#voltageTransformer:shape112"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fd60c0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 -228.000000 -777.000000)" xlink:href="#voltageTransformer:shape90"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fa6390">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 781.000000 -773.000000)" xlink:href="#voltageTransformer:shape90"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e935f0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1285.500000 -286.500000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_3033da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-96,-1155 -82,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26635@0" ObjectIDZND0="g_2fb9020@0" Pin0InfoVect0LinkObjId="g_2fb9020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162382_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-96,-1155 -82,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3033f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-132,-1155 -189,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="26635@1" ObjectIDZND0="26633@x" ObjectIDZND1="g_2f88960@0" Pin0InfoVect0LinkObjId="SW-162380_0" Pin0InfoVect1LinkObjId="g_2f88960_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162382_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-132,-1155 -189,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f88770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-191,-1133 -191,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="26633@1" ObjectIDZND0="26635@x" ObjectIDZND1="g_2f88960@0" Pin0InfoVect0LinkObjId="SW-162382_0" Pin0InfoVect1LinkObjId="g_2f88960_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162380_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-191,-1133 -191,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_300d530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-191,-1167 -191,-1236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="26635@x" ObjectIDND1="26633@x" ObjectIDND2="g_2f88960@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-162382_0" Pin1InfoVect1LinkObjId="SW-162380_0" Pin1InfoVect2LinkObjId="g_2f88960_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="-191,-1167 -191,-1236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_300d720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-230,-1179 -230,-1167 -191,-1167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2f88960@0" ObjectIDZND0="26635@x" ObjectIDZND1="26633@x" Pin0InfoVect0LinkObjId="SW-162382_0" Pin0InfoVect1LinkObjId="SW-162380_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f88960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-230,-1179 -230,-1167 -191,-1167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3031b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-191,-1167 -191,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2f88960@0" ObjectIDZND0="26635@x" ObjectIDZND1="26633@x" Pin0InfoVect0LinkObjId="SW-162382_0" Pin0InfoVect1LinkObjId="SW-162380_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f88960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-191,-1167 -191,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fb7bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-191,-941 -191,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26634@0" ObjectIDZND0="26628@0" Pin0InfoVect0LinkObjId="g_300c6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162381_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-191,-941 -191,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fb7de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-191,-1027 -191,-977 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26632@0" ObjectIDZND0="26634@1" Pin0InfoVect0LinkObjId="SW-162381_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162378_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-191,-1027 -191,-977 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fb7fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-191,-1097 -191,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26633@0" ObjectIDZND0="26632@1" Pin0InfoVect0LinkObjId="SW-162378_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-191,-1097 -191,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3033150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1056,-1155 1070,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26648@0" ObjectIDZND0="g_30329e0@0" Pin0InfoVect0LinkObjId="g_30329e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162484_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1056,-1155 1070,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3033340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1020,-1155 963,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="26648@1" ObjectIDZND0="26646@x" ObjectIDZND1="g_300b3d0@0" ObjectIDZND2="38087@1" Pin0InfoVect0LinkObjId="SW-162482_0" Pin0InfoVect1LinkObjId="g_300b3d0_0" Pin0InfoVect2LinkObjId="g_300bde0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162484_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1020,-1155 963,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3033530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="961,-1133 961,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="26646@1" ObjectIDZND0="26648@x" ObjectIDZND1="g_300b3d0@0" ObjectIDZND2="38087@1" Pin0InfoVect0LinkObjId="SW-162484_0" Pin0InfoVect1LinkObjId="g_300b3d0_0" Pin0InfoVect2LinkObjId="g_300bde0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162482_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="961,-1133 961,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_300bde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="961,-1167 961,-1192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="26648@x" ObjectIDND1="26646@x" ObjectIDND2="g_300b3d0@0" ObjectIDZND0="38087@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-162484_0" Pin1InfoVect1LinkObjId="SW-162482_0" Pin1InfoVect2LinkObjId="g_300b3d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="961,-1167 961,-1192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_300bfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="922,-1179 922,-1167 961,-1167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_300b3d0@0" ObjectIDZND0="26648@x" ObjectIDZND1="26646@x" ObjectIDZND2="38087@1" Pin0InfoVect0LinkObjId="SW-162484_0" Pin0InfoVect1LinkObjId="SW-162482_0" Pin0InfoVect2LinkObjId="g_300bde0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_300b3d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="922,-1179 922,-1167 961,-1167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_300c1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="961,-1167 961,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_300b3d0@0" ObjectIDND1="38087@1" ObjectIDZND0="26648@x" ObjectIDZND1="26646@x" Pin0InfoVect0LinkObjId="SW-162484_0" Pin0InfoVect1LinkObjId="SW-162482_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_300b3d0_0" Pin1InfoVect1LinkObjId="g_300bde0_1" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="961,-1167 961,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_300c6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="961,-941 961,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26647@0" ObjectIDZND0="26628@0" Pin0InfoVect0LinkObjId="g_2fb7bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162483_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="961,-941 961,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_300c8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="961,-1027 961,-977 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26645@0" ObjectIDZND0="26647@1" Pin0InfoVect0LinkObjId="SW-162483_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="961,-1027 961,-977 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_300cac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="961,-1097 961,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26646@0" ObjectIDZND0="26645@1" Pin0InfoVect0LinkObjId="SW-162480_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162482_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="961,-1097 961,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3012d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="254,-1082 268,-1082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26640@0" ObjectIDZND0="g_3012580@0" Pin0InfoVect0LinkObjId="g_3012580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162417_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="254,-1082 268,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3012f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="218,-1082 161,-1082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26640@1" ObjectIDZND0="26637@x" ObjectIDZND1="26636@x" Pin0InfoVect0LinkObjId="SW-162414_0" Pin0InfoVect1LinkObjId="SW-162412_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162417_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="218,-1082 161,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3013110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="161,-1103 161,-1082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26637@0" ObjectIDZND0="26640@x" ObjectIDZND1="26636@x" Pin0InfoVect0LinkObjId="SW-162417_0" Pin0InfoVect1LinkObjId="SW-162412_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162414_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="161,-1103 161,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fefaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="161,-1082 161,-1060 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26637@x" ObjectIDND1="26640@x" ObjectIDZND0="26636@1" Pin0InfoVect0LinkObjId="SW-162412_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-162414_0" Pin1InfoVect1LinkObjId="SW-162417_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="161,-1082 161,-1060 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30034a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="254,-1004 268,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26641@0" ObjectIDZND0="g_3002cf0@0" Pin0InfoVect0LinkObjId="g_3002cf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162418_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="254,-1004 268,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3003690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="218,-1004 161,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26641@1" ObjectIDZND0="26636@x" ObjectIDZND1="26638@x" Pin0InfoVect0LinkObjId="SW-162412_0" Pin0InfoVect1LinkObjId="SW-162415_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162418_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="218,-1004 161,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3003880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="161,-1033 161,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26636@0" ObjectIDZND0="26641@x" ObjectIDZND1="26638@x" Pin0InfoVect0LinkObjId="SW-162418_0" Pin0InfoVect1LinkObjId="SW-162415_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162412_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="161,-1033 161,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3003a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="161,-1004 161,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26636@x" ObjectIDND1="26641@x" ObjectIDZND0="26638@1" Pin0InfoVect0LinkObjId="SW-162415_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-162412_0" Pin1InfoVect1LinkObjId="SW-162418_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="161,-1004 161,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fec590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="256,-1161 270,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26639@0" ObjectIDZND0="g_3005ae0@0" Pin0InfoVect0LinkObjId="g_3005ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162416_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="256,-1161 270,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fec780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="220,-1161 163,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26639@1" ObjectIDZND0="26637@x" ObjectIDZND1="g_2fecf90@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-162414_0" Pin0InfoVect1LinkObjId="g_2fecf90_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162416_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="220,-1161 163,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fec970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="161,-1139 161,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26637@1" ObjectIDZND0="26639@x" ObjectIDZND1="g_2fecf90@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-162416_0" Pin0InfoVect1LinkObjId="g_2fecf90_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162414_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="161,-1139 161,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fecda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="161,-947 161,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26638@0" ObjectIDZND0="26628@0" Pin0InfoVect0LinkObjId="g_2fb7bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162415_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="161,-947 161,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fed960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="161,-1161 161,-1172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="26639@x" ObjectIDND1="26637@x" ObjectIDZND0="g_2fecf90@0" ObjectIDZND1="0@x" ObjectIDZND2="38086@1" Pin0InfoVect0LinkObjId="g_2fecf90_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2fedb50_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-162416_0" Pin1InfoVect1LinkObjId="SW-162414_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="161,-1161 161,-1172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fedb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="161,-1172 161,-1202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="26639@x" ObjectIDND1="26637@x" ObjectIDND2="g_2fecf90@0" ObjectIDZND0="38086@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-162416_0" Pin1InfoVect1LinkObjId="SW-162414_0" Pin1InfoVect2LinkObjId="g_2fecf90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="161,-1172 161,-1202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fedd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="92,-1172 92,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2fecf90@0" ObjectIDND1="26639@x" ObjectIDND2="26637@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2fecf90_0" Pin1InfoVect1LinkObjId="SW-162416_0" Pin1InfoVect2LinkObjId="SW-162414_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="92,-1172 92,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fedf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="92,-1184 92,-1172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2fecf90@0" ObjectIDZND0="0@x" ObjectIDZND1="26639@x" ObjectIDZND2="26637@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-162416_0" Pin0InfoVect2LinkObjId="SW-162414_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fecf90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="92,-1184 92,-1172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fee120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="92,-1172 161,-1172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_2fecf90@0" ObjectIDND1="0@x" ObjectIDZND0="26639@x" ObjectIDZND1="26637@x" ObjectIDZND2="38086@1" Pin0InfoVect0LinkObjId="SW-162416_0" Pin0InfoVect1LinkObjId="SW-162414_0" Pin0InfoVect2LinkObjId="g_2fedb50_1" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2fecf90_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="92,-1172 161,-1172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f5c8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="92,-1106 92,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_2f5cab0@0" Pin0InfoVect0LinkObjId="g_2f5cab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="92,-1106 92,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3021790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="593,-947 593,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26644@0" ObjectIDZND0="26628@0" Pin0InfoVect0LinkObjId="g_2fb7bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="593,-947 593,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30223f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="593,-1172 593,-1196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="26643@x" ObjectIDND1="g_3021980@0" ObjectIDND2="0@x" ObjectIDZND0="38089@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-162449_0" Pin1InfoVect1LinkObjId="g_3021980_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="593,-1172 593,-1196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3022610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,-1172 524,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="26643@x" ObjectIDND1="38089@1" ObjectIDND2="g_3021980@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-162449_0" Pin1InfoVect1LinkObjId="g_30223f0_1" Pin1InfoVect2LinkObjId="g_3021980_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="524,-1172 524,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3022830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,-1184 524,-1172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_3021980@0" ObjectIDZND0="26643@x" ObjectIDZND1="38089@1" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-162449_0" Pin0InfoVect1LinkObjId="g_30223f0_1" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3021980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="524,-1184 524,-1172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3022a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,-1172 593,-1172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="g_3021980@0" ObjectIDND1="0@x" ObjectIDZND0="26643@x" ObjectIDZND1="38089@1" Pin0InfoVect0LinkObjId="SW-162449_0" Pin0InfoVect1LinkObjId="g_30223f0_1" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3021980_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="524,-1172 593,-1172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f59240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,-1106 524,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_2f59460@0" Pin0InfoVect0LinkObjId="g_2f59460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="524,-1106 524,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f5bbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="593,-1139 593,-1172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="26643@1" ObjectIDZND0="g_3021980@0" ObjectIDZND1="0@x" ObjectIDZND2="38089@1" Pin0InfoVect0LinkObjId="g_3021980_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_30223f0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162449_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="593,-1139 593,-1172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f5bdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="593,-1103 593,-1060 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26643@0" ObjectIDZND0="26642@1" Pin0InfoVect0LinkObjId="SW-162447_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162449_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="593,-1103 593,-1060 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f5bff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="593,-1033 593,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26642@0" ObjectIDZND0="26644@1" Pin0InfoVect0LinkObjId="SW-162450_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162447_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="593,-1033 593,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3027740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1284,-1068 1298,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26660@0" ObjectIDZND0="g_3026e10@0" Pin0InfoVect0LinkObjId="g_3026e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162681_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1284,-1068 1298,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3027960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-1068 1200,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="26660@1" ObjectIDZND0="26659@x" ObjectIDZND1="g_302ed90@0" ObjectIDZND2="g_302df10@0" Pin0InfoVect0LinkObjId="SW-162680_0" Pin0InfoVect1LinkObjId="g_302ed90_0" Pin0InfoVect2LinkObjId="g_302df10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162681_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-1068 1200,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3027b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1201,-1028 1201,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="26659@1" ObjectIDZND0="26660@x" ObjectIDZND1="g_302ed90@0" ObjectIDZND2="g_302df10@0" Pin0InfoVect0LinkObjId="SW-162681_0" Pin0InfoVect1LinkObjId="g_302ed90_0" Pin0InfoVect2LinkObjId="g_302df10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162680_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1201,-1028 1201,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_302dcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1202,-1158 1202,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_3027da0@0" ObjectIDZND0="g_302df10@1" Pin0InfoVect0LinkObjId="g_302df10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3027da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1202,-1158 1202,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_302e730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1201,-1082 1146,-1082 1146,-1107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26660@x" ObjectIDND1="26659@x" ObjectIDND2="g_302df10@0" ObjectIDZND0="g_302ed90@0" Pin0InfoVect0LinkObjId="g_302ed90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-162681_0" Pin1InfoVect1LinkObjId="SW-162680_0" Pin1InfoVect2LinkObjId="g_302df10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1201,-1082 1146,-1082 1146,-1107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_302e950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1201,-1068 1201,-1082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26660@x" ObjectIDND1="26659@x" ObjectIDZND0="g_302ed90@0" ObjectIDZND1="g_302df10@0" Pin0InfoVect0LinkObjId="g_302ed90_0" Pin0InfoVect1LinkObjId="g_302df10_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-162681_0" Pin1InfoVect1LinkObjId="SW-162680_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1201,-1068 1201,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_302eb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1201,-1082 1201,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_302ed90@0" ObjectIDND1="26660@x" ObjectIDND2="26659@x" ObjectIDZND0="g_302df10@0" Pin0InfoVect0LinkObjId="g_302df10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_302ed90_0" Pin1InfoVect1LinkObjId="SW-162681_0" Pin1InfoVect2LinkObjId="SW-162680_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1201,-1082 1201,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3014ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="33,-814 33,-790 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="26649@0" ObjectIDZND0="26683@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162523_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="33,-814 33,-790 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3070b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="126,-862 140,-862 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26651@0" ObjectIDZND0="g_3070240@0" Pin0InfoVect0LinkObjId="g_3070240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162526_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="126,-862 140,-862 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3072fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="31,-709 30,-699 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="26683@0" ObjectIDZND0="g_2ef17f0@0" Pin0InfoVect0LinkObjId="g_2ef17f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3014ea0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="31,-709 30,-699 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3047c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="864,-812 864,-788 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="26654@0" ObjectIDZND0="26684@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162601_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="864,-812 864,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3049300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="957,-860 971,-860 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26656@0" ObjectIDZND0="g_30488d0@0" Pin0InfoVect0LinkObjId="g_30488d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162604_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="957,-860 971,-860 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3049560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="921,-860 864,-860 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26656@1" ObjectIDZND0="26655@x" ObjectIDZND1="26654@x" Pin0InfoVect0LinkObjId="SW-162603_0" Pin0InfoVect1LinkObjId="SW-162601_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162604_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="921,-860 864,-860 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30497c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="864,-882 864,-860 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26655@0" ObjectIDZND0="26656@x" ObjectIDZND1="26654@x" Pin0InfoVect0LinkObjId="SW-162604_0" Pin0InfoVect1LinkObjId="SW-162601_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162603_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="864,-882 864,-860 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3049a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="864,-860 864,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26656@x" ObjectIDND1="26655@x" ObjectIDZND0="26654@1" Pin0InfoVect0LinkObjId="SW-162601_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-162604_0" Pin1InfoVect1LinkObjId="SW-162603_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="864,-860 864,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fe5db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-927 367,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26628@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fb7bf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="367,-927 367,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fe6f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-855 367,-838 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="367,-855 367,-838 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fcd060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-160,-441 -160,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26663@0" ObjectIDZND0="26662@1" Pin0InfoVect0LinkObjId="SW-162692_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162694_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-160,-441 -160,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fcd2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-160,-374 -160,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26662@0" ObjectIDZND0="26664@1" Pin0InfoVect0LinkObjId="SW-162695_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162692_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-160,-374 -160,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fcfd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-124,-266 -124,-281 -160,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_308cbc0@0" ObjectIDZND0="26664@x" ObjectIDZND1="34225@x" Pin0InfoVect0LinkObjId="SW-162695_0" Pin0InfoVect1LinkObjId="EC-DY_GH.081Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_308cbc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-124,-266 -124,-281 -160,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fcff70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-160,-299 -160,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26664@0" ObjectIDZND0="g_308cbc0@0" ObjectIDZND1="34225@x" Pin0InfoVect0LinkObjId="g_308cbc0_0" Pin0InfoVect1LinkObjId="EC-DY_GH.081Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162695_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-160,-299 -160,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fd01d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-160,-281 -160,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_308cbc0@0" ObjectIDND1="26664@x" ObjectIDZND0="34225@0" Pin0InfoVect0LinkObjId="EC-DY_GH.081Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_308cbc0_0" Pin1InfoVect1LinkObjId="SW-162695_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-160,-281 -160,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fe27d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="862,-707 862,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="26684@0" ObjectIDZND0="g_2fe1c60@0" Pin0InfoVect0LinkObjId="g_2fe1c60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3047c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="862,-707 862,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fe2a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="862,-645 862,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2fe1c60@1" ObjectIDZND0="26657@1" Pin0InfoVect0LinkObjId="SW-162610_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fe1c60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="862,-645 862,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fe2c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="862,-599 862,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26658@1" ObjectIDZND0="26657@0" Pin0InfoVect0LinkObjId="SW-162610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162612_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="862,-599 862,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fe56f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-509 64,-493 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26666@0" ObjectIDZND0="26665@1" Pin0InfoVect0LinkObjId="SW-162720_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162722_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="64,-509 64,-493 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fbb750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-375 64,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2ef26c0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ef26c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="64,-375 64,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_2fbb970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-327 64,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2f2cf70@1" Pin0InfoVect0LinkObjId="g_2f2cf70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="64,-327 64,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_2fbbbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-279 64,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2f2cf70@0" ObjectIDZND0="37946@1" Pin0InfoVect0LinkObjId="SW-228601_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f2cf70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="64,-279 64,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_2fbe9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="63,-243 63,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37946@0" ObjectIDZND0="37949@1" Pin0InfoVect0LinkObjId="SW-228604_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228601_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="63,-243 63,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_2f2d8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="63,-197 63,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37949@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228604_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="63,-197 63,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_2f30b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="191,-216 191,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37951@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228606_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="191,-216 191,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_2f30d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="191,-252 191,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37951@1" ObjectIDZND0="37948@0" Pin0InfoVect0LinkObjId="SW-228602_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228606_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="191,-252 191,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_2fdb130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="191,-315 191,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="37948@1" ObjectIDZND0="g_2fdb320@1" Pin0InfoVect0LinkObjId="g_2fdb320_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228602_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="191,-315 191,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_2fdbe30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="191,-393 191,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_2fdb320@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fdb320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="191,-393 191,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f3aa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-237,-746 -237,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_2fd60c0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fd60c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-237,-746 -237,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fd5110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-238,-686 -238,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2f3ac70@0" ObjectIDZND1="26661@x" Pin0InfoVect0LinkObjId="g_2f3ac70_0" Pin0InfoVect1LinkObjId="SW-162683_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-238,-686 -238,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fd5c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-274,-680 -274,-661 -238,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2f3ac70@0" ObjectIDZND0="0@x" ObjectIDZND1="26661@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-162683_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f3ac70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-274,-680 -274,-661 -238,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fd5e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-238,-661 -238,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2f3ac70@0" ObjectIDZND0="26661@1" Pin0InfoVect0LinkObjId="SW-162683_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2f3ac70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-238,-661 -238,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fd77b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-160,-477 -160,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26663@1" ObjectIDZND0="26629@0" Pin0InfoVect0LinkObjId="g_2f37140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162694_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-160,-477 -160,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fd7a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-287,-553 -287,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26629@0" ObjectIDZND0="26686@1" Pin0InfoVect0LinkObjId="SW-162804_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fd77b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-287,-553 -287,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f326f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-287,-475 -287,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="26686@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162804_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-287,-475 -287,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f36ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-287,-398 -287,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-287,-398 -287,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f37140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-238,-580 -238,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26661@0" ObjectIDZND0="26629@0" Pin0InfoVect0LinkObjId="g_2fd77b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162683_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-238,-580 -238,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f97900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="335,-443 335,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26669@0" ObjectIDZND0="26668@1" Pin0InfoVect0LinkObjId="SW-162748_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="335,-443 335,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f97b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="335,-376 335,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26668@0" ObjectIDZND0="26670@1" Pin0InfoVect0LinkObjId="SW-162751_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162748_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="335,-376 335,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f9a5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="371,-268 371,-283 335,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2f96bd0@0" ObjectIDZND0="26670@x" ObjectIDZND1="34226@x" Pin0InfoVect0LinkObjId="SW-162751_0" Pin0InfoVect1LinkObjId="EC-DY_GH.084Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f96bd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="371,-268 371,-283 335,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f9a810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="335,-301 335,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26670@0" ObjectIDZND0="g_2f96bd0@0" ObjectIDZND1="34226@x" Pin0InfoVect0LinkObjId="g_2f96bd0_0" Pin0InfoVect1LinkObjId="EC-DY_GH.084Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162751_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="335,-301 335,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f9aa70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="335,-283 335,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2f96bd0@0" ObjectIDND1="26670@x" ObjectIDZND0="34226@0" Pin0InfoVect0LinkObjId="EC-DY_GH.084Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f96bd0_0" Pin1InfoVect1LinkObjId="SW-162751_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="335,-283 335,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f60210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="335,-479 335,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26669@1" ObjectIDZND0="26629@0" Pin0InfoVect0LinkObjId="g_2fd77b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162750_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="335,-479 335,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f667c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="515,-435 515,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26672@0" ObjectIDZND0="26671@1" Pin0InfoVect0LinkObjId="SW-162776_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162778_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="515,-435 515,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f66a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="515,-368 515,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26671@0" ObjectIDZND0="26673@1" Pin0InfoVect0LinkObjId="SW-162779_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162776_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="515,-368 515,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f69470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="551,-260 551,-275 515,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2f65a90@0" ObjectIDZND0="26673@x" ObjectIDZND1="34227@x" Pin0InfoVect0LinkObjId="SW-162779_0" Pin0InfoVect1LinkObjId="EC-DY_GH.085Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f65a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="551,-260 551,-275 515,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f696d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="515,-293 515,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26673@0" ObjectIDZND0="g_2f65a90@0" ObjectIDZND1="34227@x" Pin0InfoVect0LinkObjId="g_2f65a90_0" Pin0InfoVect1LinkObjId="EC-DY_GH.085Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162779_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="515,-293 515,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f69930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="515,-275 515,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2f65a90@0" ObjectIDND1="26673@x" ObjectIDZND0="34227@0" Pin0InfoVect0LinkObjId="EC-DY_GH.085Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f65a90_0" Pin1InfoVect1LinkObjId="SW-162779_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="515,-275 515,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f46700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="864,-918 864,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26655@1" ObjectIDZND0="26628@0" Pin0InfoVect0LinkObjId="g_2fb7bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162603_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="864,-918 864,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f468f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="862,-563 862,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26658@0" ObjectIDZND0="26630@0" Pin0InfoVect0LinkObjId="g_2f9e370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162612_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="862,-563 862,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f4c790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="895,-444 895,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26675@0" ObjectIDZND0="26674@1" Pin0InfoVect0LinkObjId="SW-162805_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162807_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="895,-444 895,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f4c9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="895,-377 895,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26674@0" ObjectIDZND0="26676@1" Pin0InfoVect0LinkObjId="SW-162808_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162805_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="895,-377 895,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f4f440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="931,-269 931,-284 895,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2f4ba60@0" ObjectIDZND0="26676@x" ObjectIDZND1="34228@x" Pin0InfoVect0LinkObjId="SW-162808_0" Pin0InfoVect1LinkObjId="EC-DY_GH.091Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f4ba60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="931,-269 931,-284 895,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f4f6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="895,-302 895,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26676@0" ObjectIDZND0="g_2f4ba60@0" ObjectIDZND1="34228@x" Pin0InfoVect0LinkObjId="g_2f4ba60_0" Pin0InfoVect1LinkObjId="EC-DY_GH.091Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162808_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="895,-302 895,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f4f900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="895,-284 895,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="26676@x" ObjectIDND1="g_2f4ba60@0" ObjectIDZND0="34228@0" Pin0InfoVect0LinkObjId="EC-DY_GH.091Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-162808_0" Pin1InfoVect1LinkObjId="g_2f4ba60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="895,-284 895,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f9e370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="895,-480 895,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26675@1" ObjectIDZND0="26630@0" Pin0InfoVect0LinkObjId="g_2f468f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162807_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="895,-480 895,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fa13f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="772,-742 772,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_2fa6390@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fa6390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="772,-742 772,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fa5c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="771,-682 771,-657 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2fa1650@0" ObjectIDZND1="26685@x" Pin0InfoVect0LinkObjId="g_2fa1650_0" Pin0InfoVect1LinkObjId="SW-162917_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="771,-682 771,-657 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fa5ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="735,-676 735,-657 771,-657 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2fa1650@0" ObjectIDZND0="0@x" ObjectIDZND1="26685@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-162917_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fa1650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="735,-676 735,-657 771,-657 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fa6130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="771,-657 771,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2fa1650@0" ObjectIDZND0="26685@1" Pin0InfoVect0LinkObjId="SW-162917_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2fa1650_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="771,-657 771,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fa7a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="771,-576 771,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26685@0" ObjectIDZND0="26630@0" Pin0InfoVect0LinkObjId="g_2f468f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162917_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="771,-576 771,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2faadb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1169,-503 1169,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26678@0" ObjectIDZND0="26677@1" Pin0InfoVect0LinkObjId="SW-162833_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162835_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1169,-503 1169,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fad0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1169,-460 1169,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26677@0" ObjectIDZND0="26679@1" Pin0InfoVect0LinkObjId="SW-162836_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162833_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1169,-460 1169,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fafb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1169,-405 1169,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="26679@0" ObjectIDZND0="g_2fafda0@1" Pin0InfoVect0LinkObjId="g_2fafda0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162836_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1169,-405 1169,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fb09c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1169,-360 1169,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_2fafda0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fafda0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1169,-360 1169,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f749b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1169,-300 1169,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37950@0" ObjectIDZND0="37947@1" Pin0InfoVect0LinkObjId="SW-228603_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228605_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1169,-300 1169,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f76f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1169,-336 1169,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37950@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228605_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1169,-336 1169,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f77110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1169,-257 1169,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="37947@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228603_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1169,-257 1169,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f78350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="33,-862 33,-873 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26651@x" ObjectIDND1="26649@x" ObjectIDZND0="26650@0" Pin0InfoVect0LinkObjId="SW-162525_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-162526_0" Pin1InfoVect1LinkObjId="SW-162523_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="33,-862 33,-873 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f785b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="90,-862 33,-862 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26651@1" ObjectIDZND0="26650@x" ObjectIDZND1="26649@x" Pin0InfoVect0LinkObjId="SW-162525_0" Pin0InfoVect1LinkObjId="SW-162523_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162526_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="90,-862 33,-862 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f78810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="33,-862 33,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26651@x" ObjectIDND1="26650@x" ObjectIDZND0="26649@1" Pin0InfoVect0LinkObjId="SW-162523_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-162526_0" Pin1InfoVect1LinkObjId="SW-162525_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="33,-862 33,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f78a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="33,-909 33,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26650@1" ObjectIDZND0="26628@0" Pin0InfoVect0LinkObjId="g_2fb7bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162525_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="33,-909 33,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f78cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="31,-565 31,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26653@0" ObjectIDZND0="26629@0" Pin0InfoVect0LinkObjId="g_2fd77b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162534_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="31,-565 31,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f78f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-457 64,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26667@1" ObjectIDZND0="26665@0" Pin0InfoVect0LinkObjId="SW-162720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162723_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="64,-457 64,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f79d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-545 64,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26666@1" ObjectIDZND0="26629@0" Pin0InfoVect0LinkObjId="g_2fd77b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162722_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="64,-545 64,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f7bec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="609,-553 609,-541 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="26629@0" ObjectIDZND0="g_2f7c120@1" Pin0InfoVect0LinkObjId="g_2f7c120_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fd77b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="609,-553 609,-541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f7cd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="609,-502 609,-493 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2f7c120@0" ObjectIDZND0="26681@1" Pin0InfoVect0LinkObjId="SW-162863_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f7c120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="609,-502 609,-493 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e8b780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="609,-457 609,-440 664,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26681@0" ObjectIDZND0="26680@1" Pin0InfoVect0LinkObjId="SW-162861_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162863_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="609,-457 609,-440 664,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e8e1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="691,-440 730,-440 730,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26680@0" ObjectIDZND0="26682@0" Pin0InfoVect0LinkObjId="SW-162864_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162861_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="691,-440 730,-440 730,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e8e440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="730,-498 730,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26682@1" ObjectIDZND0="26630@0" Pin0InfoVect0LinkObjId="g_2f468f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162864_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="730,-498 730,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e90c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="977,-553 986,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="26630@0" ObjectIDZND0="g_2e8fa30@0" Pin0InfoVect0LinkObjId="g_2e8fa30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f468f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="977,-553 986,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e91460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1169,-539 1169,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26678@1" ObjectIDZND0="26630@0" Pin0InfoVect0LinkObjId="g_2f468f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162835_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1169,-539 1169,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e92b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1294,-336 1294,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_2e92260@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e92260_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1294,-336 1294,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e93390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1294,-292 1294,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2e92260@0" ObjectIDZND0="g_2e935f0@0" Pin0InfoVect0LinkObjId="g_2e935f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e92260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1294,-292 1294,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f084c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="515,-471 515,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26672@1" ObjectIDZND0="26629@0" Pin0InfoVect0LinkObjId="g_2fd77b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162778_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="515,-471 515,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ef2260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="30,-646 30,-637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2ef17f0@1" ObjectIDZND0="26652@1" Pin0InfoVect0LinkObjId="SW-162532_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ef17f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="30,-646 30,-637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ef2490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="30,-610 31,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26652@0" ObjectIDZND0="26653@1" Pin0InfoVect0LinkObjId="SW-162534_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162532_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="30,-610 31,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2efe9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1169,-205 1169,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2efebb0@0" Pin0InfoVect0LinkObjId="g_2efebb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1169,-205 1169,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2eff6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1169,-139 1169,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_2efebb0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2efebb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1169,-139 1169,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30fd010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1065,-553 1080,-553 1080,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_2e8fa30@1" ObjectIDZND0="26630@0" Pin0InfoVect0LinkObjId="g_2f468f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e8fa30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1065,-553 1080,-553 1080,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e97f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1201,-992 1201,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26659@0" ObjectIDZND0="26628@0" Pin0InfoVect0LinkObjId="g_2fb7bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1201,-992 1201,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e9c6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-421 64,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="26667@0" ObjectIDZND0="g_2ef26c0@1" Pin0InfoVect0LinkObjId="g_2ef26c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-162723_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="64,-421 64,-414 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="DY_GH"/>
</svg>