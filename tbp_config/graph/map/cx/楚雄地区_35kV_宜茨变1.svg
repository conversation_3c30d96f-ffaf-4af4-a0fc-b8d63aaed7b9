<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-68" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1199 2104 1262">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="32" stroke-width="0.5" width="15" x="1" y="6"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="32" stroke-width="0.5" width="15" x="2" y="6"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="33" stroke-width="0.5" width="16" x="1" y="5"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="17,5 1,38 " stroke-width="0.509653"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="18,38 2,5 " stroke-width="0.5"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <rect height="33" stroke-width="0.5" width="16" x="1" y="5"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="17,5 1,38 " stroke-width="0.509653"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="18,38 2,5 " stroke-width="0.5"/>
   </symbol>
   <symbol id="capacitor:shape59">
    <polyline arcFlag="1" points="21,93 19,93 17,92 15,92 13,91 12,90 10,89 9,88 8,86 8,85 7,83 7,81 7,80 8,78 8,77 9,75 10,74 12,73 13,72 15,71 17,70 19,70 21,70 23,70 25,70 27,71 28,72 30,73 31,74 33,75 33,77 34,78 35,80 35,82 " stroke-width="0.0864"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.426667" x1="35" x2="20" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.514286" x1="21" x2="21" y1="93" y2="106"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="16" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="53" y2="46"/>
    <polyline arcFlag="1" points="40,16 40,16 41,17 41,17 42,17 43,17 43,18 44,18 44,18 44,19 45,19 45,20 45,21 45,21 45,22 45,22 45,23 44,24 44,24 44,25 43,25 43,25 42,26 41,26 41,26 40,26 40,26 " stroke-width="1"/>
    <polyline arcFlag="1" points="40,36 40,36 41,36 41,37 42,37 43,37 43,37 44,38 44,38 44,39 45,39 45,40 45,41 45,41 45,42 45,42 45,43 44,44 44,44 44,45 43,45 43,45 42,46 41,46 41,46 40,46 40,46 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37647" x1="21" x2="21" y1="81" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395164" x1="3" x2="38" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.473186" x1="20" x2="39" y1="11" y2="11"/>
    <rect height="21" stroke-width="0.396008" width="12" x="15" y="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.382413" x1="0" x2="39" y1="53" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.209877" x1="3" x2="3" y1="2" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.209877" x1="38" x2="38" y1="2" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.889674" x1="21" x2="21" y1="16" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.309754" x1="11" x2="28" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.309754" x1="12" x2="28" y1="23" y2="23"/>
    <polyline arcFlag="1" points="40,27 40,26 41,27 41,27 42,27 43,27 43,28 44,28 44,28 44,29 45,30 45,30 45,31 45,31 45,32 45,33 45,33 44,34 44,34 44,35 43,35 43,35 42,36 41,36 41,36 40,36 40,36 " stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape192">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="26" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="5,19 17,9 5,0 5,19 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="38,1 26,10 38,19 38,1 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape16">
    <circle cx="15" cy="80" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="14" y1="81" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="18" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="10" y1="87" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="53" y2="28"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,59 40,59 40,53 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="15,16 21,28 9,28 15,16 15,16 15,16 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="43" y2="1"/>
    <circle cx="15" cy="58" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="59" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="19" y1="59" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="55" y2="59"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape205">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,27 38,5 50,5 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="25" y="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="13" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="2" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape7_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape7_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape37_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="22" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="21" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="4" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="4" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape54_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <rect height="28" stroke-width="1" width="14" x="90" y="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="95" x2="98" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="93" x2="101" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="97" x2="97" y1="28" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="103" x2="91" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="97" x2="97" y1="75" y2="40"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="39" y1="75" y2="75"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape54_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape113">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="10" x2="15" y1="34" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="27" x2="22" y1="18" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="32" x2="27" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="22" x2="27" y1="14" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="41" x2="36" y1="38" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="46" x2="41" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="36" x2="41" y1="34" y2="38"/>
    <circle cx="39" cy="39" fillStyle="0" r="15" stroke-width="0.306122"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="10" y1="38" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="20" x2="15" y1="38" y2="38"/>
    <circle cx="15" cy="39" fillStyle="0" r="15" stroke-width="0.306122"/>
    <circle cx="27" cy="19" fillStyle="0" r="15" stroke-width="0.306122"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3475490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_344c5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_326bb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_325a2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e6fe50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_324da50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3212190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2e21c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_343dfa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_343dfa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3222420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3222420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_327b670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_327b670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2ea5610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b6f280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2e44fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_344d9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3202ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e32cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_327a7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e20530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e5e250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e4d5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e986e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e68970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2e64f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3231600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3245360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3465e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e4ec70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_325c480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b71e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2e2e600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_32115e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1272" width="2114" x="3112" y="-1204"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3875" x2="3858" y1="-662" y2="-662"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3875" x2="3875" y1="-658" y2="-666"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3882" x2="3882" y1="-660" y2="-663"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3878" x2="3878" y1="-659" y2="-664"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3838" x2="3853" y1="-668" y2="-656"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3852" x2="3857" y1="-656" y2="-656"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-38662">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4266.000000 -767.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6196" ObjectName="SW-CX_YC.CX_YC_301BK"/>
     <cge:Meas_Ref ObjectId="38662"/>
    <cge:TPSR_Ref TObjectID="6196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38639">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.333333 -353.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6187" ObjectName="SW-CX_YC.CX_YC_071BK"/>
     <cge:Meas_Ref ObjectId="38639"/>
    <cge:TPSR_Ref TObjectID="6187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38655">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3830.333333 -351.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6191" ObjectName="SW-CX_YC.CX_YC_072BK"/>
     <cge:Meas_Ref ObjectId="38655"/>
    <cge:TPSR_Ref TObjectID="6191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38647">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4229.333333 -347.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6189" ObjectName="SW-CX_YC.CX_YC_074BK"/>
     <cge:Meas_Ref ObjectId="38647"/>
    <cge:TPSR_Ref TObjectID="6189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38670">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4022.333333 -352.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6197" ObjectName="SW-CX_YC.CX_YC_073BK"/>
     <cge:Meas_Ref ObjectId="38670"/>
    <cge:TPSR_Ref TObjectID="6197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38631">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4439.333333 -351.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6185" ObjectName="SW-CX_YC.CX_YC_075BK"/>
     <cge:Meas_Ref ObjectId="38631"/>
    <cge:TPSR_Ref TObjectID="6185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280194">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.333333 -352.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44502" ObjectName="SW-CX_YC.CX_YC_076BK"/>
     <cge:Meas_Ref ObjectId="280194"/>
    <cge:TPSR_Ref TObjectID="44502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280247">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4798.333333 -351.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44507" ObjectName="SW-CX_YC.CX_YC_077BK"/>
     <cge:Meas_Ref ObjectId="280247"/>
    <cge:TPSR_Ref TObjectID="44507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38623">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5155.333333 -352.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6183" ObjectName="SW-CX_YC.CX_YC_079BK"/>
     <cge:Meas_Ref ObjectId="38623"/>
    <cge:TPSR_Ref TObjectID="6183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38613">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4265.333333 -490.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6182" ObjectName="SW-CX_YC.CX_YC_001BK"/>
     <cge:Meas_Ref ObjectId="38613"/>
    <cge:TPSR_Ref TObjectID="6182"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_37742f0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4389.000000 -997.000000)" xlink:href="#voltageTransformer:shape113"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3abd3c0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3830.000000 -677.000000)" xlink:href="#voltageTransformer:shape113"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YC" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_ycT" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4275,-1109 4275,-1130 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38060" ObjectName="AC-35kV.LN_ycT"/>
    <cge:TPSR_Ref TObjectID="38060_SS-68"/></metadata>
   <polyline fill="none" opacity="0" points="4275,-1109 4275,-1130 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_YC.083Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3647.000000 -76.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34521" ObjectName="EC-CX_YC.083Ld"/>
    <cge:TPSR_Ref TObjectID="34521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YC.081Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3831.000000 -72.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34519" ObjectName="EC-CX_YC.081Ld"/>
    <cge:TPSR_Ref TObjectID="34519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YC.082Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4230.000000 -72.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34520" ObjectName="EC-CX_YC.082Ld"/>
    <cge:TPSR_Ref TObjectID="34520"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YC.084Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4440.000000 -74.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34522" ObjectName="EC-CX_YC.084Ld"/>
    <cge:TPSR_Ref TObjectID="34522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YC.076Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4617.000000 -73.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49503" ObjectName="EC-CX_YC.076Ld"/>
    <cge:TPSR_Ref TObjectID="49503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YC.077Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4799.000000 -79.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49501" ObjectName="EC-CX_YC.077Ld"/>
    <cge:TPSR_Ref TObjectID="49501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YC.085Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5156.000000 -75.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34523" ObjectName="EC-CX_YC.085Ld"/>
    <cge:TPSR_Ref TObjectID="34523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4968.000000 -79.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3abbb40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4185.000000 -960.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3abc790" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4996.000000 -113.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a5f920" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3894.000000 -465.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2b618e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4275,-771 4275,-723 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="6196@1" ObjectIDZND0="6200@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38662_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4275,-771 4275,-723 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cada00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-348 3656,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44477@1" ObjectIDZND0="6187@1" Pin0InfoVect0LinkObjId="SW-38639_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38656_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-348 3656,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b61680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-392 3656,-405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6187@0" ObjectIDZND0="6188@1" Pin0InfoVect0LinkObjId="SW-38656_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38639_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-392 3656,-405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c376e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3613,-284 3656,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="44480@0" ObjectIDZND0="18598@x" ObjectIDZND1="44477@x" Pin0InfoVect0LinkObjId="SW-84505_0" Pin0InfoVect1LinkObjId="SW-38656_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3613,-284 3656,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_323f1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-198 3656,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="18598@1" ObjectIDZND0="44480@x" ObjectIDZND1="44477@x" Pin0InfoVect0LinkObjId="SW-38640_0" Pin0InfoVect1LinkObjId="SW-38656_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84505_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-198 3656,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c205e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-346 3840,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44492@1" ObjectIDZND0="6191@1" Pin0InfoVect0LinkObjId="SW-38655_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280179_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-346 3840,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c73400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3797,-282 3840,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="44493@0" ObjectIDZND0="g_2b78270@0" ObjectIDZND1="44492@x" Pin0InfoVect0LinkObjId="g_2b78270_0" Pin0InfoVect1LinkObjId="SW-280179_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3797,-282 3840,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c53320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4239,-416 4239,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6190@0" ObjectIDZND0="6179@0" Pin0InfoVect0LinkObjId="g_2c68800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38648_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4239,-416 4239,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bc2bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4239,-342 4239,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44478@1" ObjectIDZND0="6189@1" Pin0InfoVect0LinkObjId="SW-38647_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38648_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4239,-342 4239,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a4770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4239,-386 4239,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6189@0" ObjectIDZND0="6190@1" Pin0InfoVect0LinkObjId="SW-38648_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38647_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4239,-386 4239,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_329ecf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4196,-278 4239,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="44494@0" ObjectIDZND0="g_2bd8eb0@0" ObjectIDZND1="44478@x" Pin0InfoVect0LinkObjId="g_2bd8eb0_0" Pin0InfoVect1LinkObjId="SW-38648_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280181_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4196,-278 4239,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c68800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-422 3656,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6188@0" ObjectIDZND0="6179@0" Pin0InfoVect0LinkObjId="g_2c53320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38656_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-422 3656,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb57f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-420 3840,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6192@0" ObjectIDZND0="6179@0" Pin0InfoVect0LinkObjId="g_2c53320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280179_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-420 3840,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cabb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-443 4032,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6179@0" ObjectIDZND0="6198@0" Pin0InfoVect0LinkObjId="SW-38671_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c53320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-443 4032,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cb8c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-253 3840,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b78270@0" ObjectIDZND0="44493@x" ObjectIDZND1="44492@x" Pin0InfoVect0LinkObjId="SW-280180_0" Pin0InfoVect1LinkObjId="SW-280179_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b78270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-253 3840,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf1b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-199 3840,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="10570@1" ObjectIDZND0="g_2b78270@1" Pin0InfoVect0LinkObjId="g_2b78270_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84482_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-199 3840,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf1dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3879,-133 3840,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2b75900@0" ObjectIDZND0="10570@x" ObjectIDZND1="34519@x" Pin0InfoVect0LinkObjId="SW-84482_0" Pin0InfoVect1LinkObjId="EC-CX_YC.081Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b75900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3879,-133 3840,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cba470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-100 3840,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34519@0" ObjectIDZND0="g_2b75900@0" ObjectIDZND1="10570@x" Pin0InfoVect0LinkObjId="g_2b75900_0" Pin0InfoVect1LinkObjId="SW-84482_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_YC.081Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-100 3840,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cba6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-133 3840,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2b75900@0" ObjectIDND1="34519@x" ObjectIDZND0="10570@0" Pin0InfoVect0LinkObjId="SW-84482_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b75900_0" Pin1InfoVect1LinkObjId="EC-CX_YC.081Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-133 3840,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb6c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-128 3656,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2b9a050@0" ObjectIDZND0="18598@x" ObjectIDZND1="34521@x" Pin0InfoVect0LinkObjId="SW-84505_0" Pin0InfoVect1LinkObjId="EC-CX_YC.083Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b9a050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-128 3656,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c1e5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-162 3656,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="18598@0" ObjectIDZND0="g_2b9a050@0" ObjectIDZND1="34521@x" Pin0InfoVect0LinkObjId="g_2b9a050_0" Pin0InfoVect1LinkObjId="EC-CX_YC.083Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84505_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-162 3656,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c1e800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-128 3656,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2b9a050@0" ObjectIDND1="18598@x" ObjectIDZND0="34521@0" Pin0InfoVect0LinkObjId="EC-CX_YC.083Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b9a050_0" Pin1InfoVect1LinkObjId="SW-84505_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-128 3656,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2adf0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4239,-195 4239,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="18597@1" ObjectIDZND0="g_2bd8eb0@1" Pin0InfoVect0LinkObjId="g_2bd8eb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84483_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4239,-195 4239,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2adf340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4239,-253 4239,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2bd8eb0@0" ObjectIDZND0="44494@x" ObjectIDZND1="44478@x" Pin0InfoVect0LinkObjId="SW-280181_0" Pin0InfoVect1LinkObjId="SW-38648_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd8eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4239,-253 4239,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c99040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4279,-129 4239,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2c1e270@0" ObjectIDZND0="34520@x" ObjectIDZND1="18597@x" Pin0InfoVect0LinkObjId="EC-CX_YC.082Ld_0" Pin0InfoVect1LinkObjId="SW-84483_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c1e270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4279,-129 4239,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bfe480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4239,-99 4239,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34520@0" ObjectIDZND0="g_2c1e270@0" ObjectIDZND1="18597@x" Pin0InfoVect0LinkObjId="g_2c1e270_0" Pin0InfoVect1LinkObjId="SW-84483_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_YC.082Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4239,-99 4239,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c67cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4239,-129 4239,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2c1e270@0" ObjectIDND1="34520@x" ObjectIDZND0="18597@0" Pin0InfoVect0LinkObjId="SW-84483_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c1e270_0" Pin1InfoVect1LinkObjId="EC-CX_YC.082Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4239,-129 4239,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c06e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,-390 4449,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6185@0" ObjectIDZND0="6186@1" Pin0InfoVect0LinkObjId="SW-38632_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38631_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4449,-390 4449,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2addb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4406,-282 4449,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="44495@0" ObjectIDZND0="18599@x" ObjectIDZND1="44481@x" Pin0InfoVect0LinkObjId="SW-84731_0" Pin0InfoVect1LinkObjId="SW-38632_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280182_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4406,-282 4449,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2adddc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,-196 4449,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="18599@1" ObjectIDZND0="44495@x" ObjectIDZND1="44481@x" Pin0InfoVect0LinkObjId="SW-280182_0" Pin0InfoVect1LinkObjId="SW-38632_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84731_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4449,-196 4449,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ade020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,-420 4449,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6186@0" ObjectIDZND0="6179@0" Pin0InfoVect0LinkObjId="g_2c53320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38632_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4449,-420 4449,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ade280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4491,-126 4449,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2bc2480@0" ObjectIDZND0="18599@x" ObjectIDZND1="34522@x" Pin0InfoVect0LinkObjId="SW-84731_0" Pin0InfoVect1LinkObjId="EC-CX_YC.084Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bc2480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4491,-126 4449,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c3c9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,-160 4449,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="18599@0" ObjectIDZND0="g_2bc2480@0" ObjectIDZND1="34522@x" Pin0InfoVect0LinkObjId="g_2bc2480_0" Pin0InfoVect1LinkObjId="EC-CX_YC.084Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84731_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4449,-160 4449,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c3cc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,-126 4449,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="18599@x" ObjectIDND1="g_2bc2480@0" ObjectIDZND0="34522@0" Pin0InfoVect0LinkObjId="EC-CX_YC.084Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-84731_0" Pin1InfoVect1LinkObjId="g_2bc2480_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4449,-126 4449,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf2b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-347 4626,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44504@1" ObjectIDZND0="44502@1" Pin0InfoVect0LinkObjId="SW-280194_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280196_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-347 4626,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf2d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-391 4626,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="44502@0" ObjectIDZND0="44503@1" Pin0InfoVect0LinkObjId="SW-280196_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280194_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-391 4626,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c153f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4583,-283 4626,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="44506@0" ObjectIDZND0="g_2b28040@0" ObjectIDZND1="44504@x" Pin0InfoVect0LinkObjId="g_2b28040_0" Pin0InfoVect1LinkObjId="SW-280196_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4583,-283 4626,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c15650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-421 4626,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44503@0" ObjectIDZND0="6179@0" Pin0InfoVect0LinkObjId="g_2c53320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280196_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-421 4626,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c158b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4668,-127 4626,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2c319e0@0" ObjectIDZND0="44505@x" ObjectIDZND1="49503@x" Pin0InfoVect0LinkObjId="SW-280198_0" Pin0InfoVect1LinkObjId="EC-CX_YC.076Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c319e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4668,-127 4626,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c15b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-161 4626,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="44505@0" ObjectIDZND0="g_2c319e0@0" ObjectIDZND1="49503@x" Pin0InfoVect0LinkObjId="g_2c319e0_0" Pin0InfoVect1LinkObjId="EC-CX_YC.076Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280198_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-161 4626,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c15d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-127 4626,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="44505@x" ObjectIDND1="g_2c319e0@0" ObjectIDZND0="49503@0" Pin0InfoVect0LinkObjId="EC-CX_YC.076Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-280198_0" Pin1InfoVect1LinkObjId="g_2c319e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-127 4626,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c1a190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4808,-344 4808,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44509@1" ObjectIDZND0="44507@1" Pin0InfoVect0LinkObjId="SW-280247_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280249_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4808,-344 4808,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b96b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4808,-390 4808,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="44507@0" ObjectIDZND0="44508@1" Pin0InfoVect0LinkObjId="SW-280249_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280247_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4808,-390 4808,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b97a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4845,-305 4807,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_2b96d70@0" ObjectIDZND0="44509@x" ObjectIDZND1="44510@x" ObjectIDZND2="49501@x" Pin0InfoVect0LinkObjId="SW-280249_0" Pin0InfoVect1LinkObjId="SW-280251_0" Pin0InfoVect2LinkObjId="EC-CX_YC.077Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b96d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4845,-305 4807,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c22540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4765,-282 4808,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="44510@0" ObjectIDZND0="44509@x" ObjectIDZND1="g_2b96d70@0" ObjectIDZND2="49501@x" Pin0InfoVect0LinkObjId="SW-280249_0" Pin0InfoVect1LinkObjId="g_2b96d70_0" Pin0InfoVect2LinkObjId="EC-CX_YC.077Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280251_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4765,-282 4808,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c7f960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4808,-420 4808,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44508@0" ObjectIDZND0="6179@0" Pin0InfoVect0LinkObjId="g_2c53320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280249_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4808,-420 4808,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c258b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5013,-308 4976,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2c24c30@0" ObjectIDZND0="44490@x" ObjectIDZND1="44491@x" ObjectIDZND2="g_2b28db0@0" Pin0InfoVect0LinkObjId="SW-280177_0" Pin0InfoVect1LinkObjId="SW-280178_0" Pin0InfoVect2LinkObjId="g_2b28db0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c24c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5013,-308 4976,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c25b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4976,-308 4976,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c24c30@0" ObjectIDND1="44491@x" ObjectIDND2="g_2b28db0@0" ObjectIDZND0="44490@0" Pin0InfoVect0LinkObjId="SW-280177_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c24c30_0" Pin1InfoVect1LinkObjId="SW-280178_0" Pin1InfoVect2LinkObjId="g_2b28db0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4976,-308 4976,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c74a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4933,-285 4976,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="44491@0" ObjectIDZND0="44490@x" ObjectIDZND1="g_2c24c30@0" ObjectIDZND2="g_2b28db0@0" Pin0InfoVect0LinkObjId="SW-280177_0" Pin0InfoVect1LinkObjId="g_2c24c30_0" Pin0InfoVect2LinkObjId="g_2b28db0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280178_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4933,-285 4976,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c74ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4976,-285 4976,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="44491@x" ObjectIDND1="g_2b28db0@0" ObjectIDZND0="44490@x" ObjectIDZND1="g_2c24c30@0" Pin0InfoVect0LinkObjId="SW-280177_0" Pin0InfoVect1LinkObjId="g_2c24c30_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-280178_0" Pin1InfoVect1LinkObjId="g_2b28db0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4976,-285 4976,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c74f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4976,-422 4976,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44489@0" ObjectIDZND0="6179@0" Pin0InfoVect0LinkObjId="g_2c53320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280177_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4976,-422 4976,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d4db20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5165,-347 5165,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44482@1" ObjectIDZND0="6183@1" Pin0InfoVect0LinkObjId="SW-38623_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38624_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5165,-347 5165,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d4dd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5165,-391 5165,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6183@0" ObjectIDZND0="6184@1" Pin0InfoVect0LinkObjId="SW-38624_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38623_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5165,-391 5165,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b26270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5122,-283 5165,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="44496@0" ObjectIDZND0="18600@x" ObjectIDZND1="44482@x" Pin0InfoVect0LinkObjId="SW-84546_0" Pin0InfoVect1LinkObjId="SW-38624_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280183_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5122,-283 5165,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b264d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5165,-197 5165,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="18600@1" ObjectIDZND0="44496@x" ObjectIDZND1="44482@x" Pin0InfoVect0LinkObjId="SW-280183_0" Pin0InfoVect1LinkObjId="SW-38624_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84546_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5165,-197 5165,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b26730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5165,-421 5165,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6184@0" ObjectIDZND0="6179@0" Pin0InfoVect0LinkObjId="g_2c53320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38624_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5165,-421 5165,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b26990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5207,-127 5165,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_3d50520@0" ObjectIDZND0="18600@x" ObjectIDZND1="34523@x" Pin0InfoVect0LinkObjId="SW-84546_0" Pin0InfoVect1LinkObjId="EC-CX_YC.085Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d50520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5207,-127 5165,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b26bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5165,-161 5165,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="18600@0" ObjectIDZND0="g_3d50520@0" ObjectIDZND1="34523@x" Pin0InfoVect0LinkObjId="g_3d50520_0" Pin0InfoVect1LinkObjId="EC-CX_YC.085Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84546_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5165,-161 5165,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b26e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5165,-127 5165,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="18600@x" ObjectIDND1="g_3d50520@0" ObjectIDZND0="34523@0" Pin0InfoVect0LinkObjId="EC-CX_YC.085Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-84546_0" Pin1InfoVect1LinkObjId="g_3d50520_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5165,-127 5165,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b28920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-197 4626,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="44505@1" ObjectIDZND0="g_2b28040@1" Pin0InfoVect0LinkObjId="g_2b28040_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280198_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-197 4626,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b28b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-251 4626,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b28040@0" ObjectIDZND0="44506@x" ObjectIDZND1="44504@x" Pin0InfoVect0LinkObjId="SW-280200_0" Pin0InfoVect1LinkObjId="SW-280196_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b28040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-251 4626,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c6c7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4976,-199 4976,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2c6ccb0@0" ObjectIDZND0="g_2b28db0@1" Pin0InfoVect0LinkObjId="g_2b28db0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c6ccb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4976,-199 4976,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c6ca50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4976,-252 4976,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2b28db0@0" ObjectIDZND0="44491@x" ObjectIDZND1="44490@x" ObjectIDZND2="g_2c24c30@0" Pin0InfoVect0LinkObjId="SW-280178_0" Pin0InfoVect1LinkObjId="SW-280177_0" Pin0InfoVect2LinkObjId="g_2c24c30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b28db0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4976,-252 4976,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39ac470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4275,-467 4275,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6181@0" ObjectIDZND0="6179@0" Pin0InfoVect0LinkObjId="g_2c53320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38612_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4275,-467 4275,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39ac6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4275,-495 4275,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6182@1" ObjectIDZND0="6181@1" Pin0InfoVect0LinkObjId="SW-38612_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38613_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4275,-495 4275,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39ac930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4275,-541 4275,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44479@1" ObjectIDZND0="6182@0" Pin0InfoVect0LinkObjId="SW-38613_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38612_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4275,-541 4275,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39ad470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4275,-576 4275,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_39acb90@1" ObjectIDZND0="44479@0" Pin0InfoVect0LinkObjId="SW-38612_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39acb90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4275,-576 4275,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39b02c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4234,-620 4275,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_39af5d0@0" ObjectIDZND0="g_39acb90@0" ObjectIDZND1="6200@x" Pin0InfoVect0LinkObjId="g_39acb90_0" Pin0InfoVect1LinkObjId="g_2b618e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39af5d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4234,-620 4275,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39b0cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4275,-631 4275,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="6200@0" ObjectIDZND0="g_39af5d0@0" ObjectIDZND1="g_39acb90@0" Pin0InfoVect0LinkObjId="g_39af5d0_0" Pin0InfoVect1LinkObjId="g_39acb90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b618e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4275,-631 4275,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39b0f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4275,-620 4275,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="g_39af5d0@0" ObjectIDND1="6200@x" ObjectIDZND0="g_39acb90@0" Pin0InfoVect0LinkObjId="g_39acb90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_39af5d0_0" Pin1InfoVect1LinkObjId="g_2b618e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4275,-620 4275,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3773e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4275,-940 4275,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6194@0" ObjectIDZND0="49502@0" Pin0InfoVect0LinkObjId="g_3bf6920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38658_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4275,-940 4275,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3775830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4362,-992 4362,-1013 4275,-1013 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="g_37742f0@0" ObjectIDZND0="g_37778d0@0" ObjectIDZND1="g_3778640@0" ObjectIDZND2="38060@1" Pin0InfoVect0LinkObjId="g_37778d0_0" Pin0InfoVect1LinkObjId="g_3778640_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37742f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4362,-992 4362,-1013 4275,-1013 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37783e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4165,-1088 4165,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_37778d0@0" ObjectIDZND0="g_3776240@0" Pin0InfoVect0LinkObjId="g_3776240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37778d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4165,-1088 4165,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b87bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-403 3840,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6192@1" ObjectIDZND0="6191@0" Pin0InfoVect0LinkObjId="SW-38655_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280179_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-403 3840,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b87de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,-346 4449,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44481@1" ObjectIDZND0="6185@1" Pin0InfoVect0LinkObjId="SW-38631_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38632_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4449,-346 4449,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ab7ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4976,-349 4976,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="44490@1" ObjectIDZND0="g_3ab7560@1" Pin0InfoVect0LinkObjId="g_3ab7560_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280177_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4976,-349 4976,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3abb8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4976,-393 4976,-406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3ab7560@0" ObjectIDZND0="44489@1" Pin0InfoVect0LinkObjId="SW-280177_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ab7560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4976,-393 4976,-406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3abc530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-1011 4191,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_3abbb40@0" Pin0InfoVect0LinkObjId="g_3abbb40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-1011 4191,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3abd160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5002,-164 5002,-131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_3abc790@0" Pin0InfoVect0LinkObjId="g_3abc790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5002,-164 5002,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3abf430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-623 3790,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDZND0="g_2c6a5f0@0" ObjectIDZND1="g_3ab6db0@0" Pin0InfoVect0LinkObjId="g_2c6a5f0_0" Pin0InfoVect1LinkObjId="g_3ab6db0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-623 3790,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3abfe70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3734,-591 3790,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2c6a5f0@0" ObjectIDZND0="g_3ab6db0@0" Pin0InfoVect0LinkObjId="g_3ab6db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c6a5f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3734,-591 3790,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ac00d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-591 3790,-583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2c6a5f0@0" ObjectIDZND0="g_3ab6db0@0" Pin0InfoVect0LinkObjId="g_3ab6db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c6a5f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-591 3790,-583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ac0330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3837,-662 3817,-662 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3837,-662 3817,-662 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ac0590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-331 3656,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="44477@0" ObjectIDZND0="44480@x" ObjectIDZND1="18598@x" Pin0InfoVect0LinkObjId="SW-38640_0" Pin0InfoVect1LinkObjId="SW-84505_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38656_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-331 3656,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ac07f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-329 3840,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="44492@0" ObjectIDZND0="44493@x" ObjectIDZND1="g_2b78270@0" Pin0InfoVect0LinkObjId="SW-280180_0" Pin0InfoVect1LinkObjId="g_2b78270_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280179_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-329 3840,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ac0a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4239,-325 4239,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="44478@0" ObjectIDZND0="44494@x" ObjectIDZND1="g_2bd8eb0@0" Pin0InfoVect0LinkObjId="SW-280181_0" Pin0InfoVect1LinkObjId="g_2bd8eb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38648_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4239,-325 4239,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ac0cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,-329 4449,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="44481@0" ObjectIDZND0="18599@x" ObjectIDZND1="44495@x" Pin0InfoVect0LinkObjId="SW-84731_0" Pin0InfoVect1LinkObjId="SW-280182_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38632_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4449,-329 4449,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ac0f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-330 4626,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="44504@0" ObjectIDZND0="44506@x" ObjectIDZND1="g_2b28040@0" Pin0InfoVect0LinkObjId="SW-280200_0" Pin0InfoVect1LinkObjId="g_2b28040_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280196_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-330 4626,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ac1170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5165,-330 5165,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="44482@0" ObjectIDZND0="18600@x" ObjectIDZND1="44496@x" Pin0InfoVect0LinkObjId="SW-84546_0" Pin0InfoVect1LinkObjId="SW-280183_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38624_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5165,-330 5165,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a57bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-552 3790,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3ab6db0@1" ObjectIDZND0="44486@0" Pin0InfoVect0LinkObjId="SW-280175_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ab6db0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-552 3790,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a57e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-526 3790,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="44486@1" ObjectIDZND0="44485@1" Pin0InfoVect0LinkObjId="SW-280175_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280175_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-526 3790,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a5e190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3865,-471 3831,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="44488@0" ObjectIDZND0="44487@1" Pin0InfoVect0LinkObjId="SW-280176_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280176_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3865,-471 3831,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a5e3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-471 3790,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="44487@0" ObjectIDZND0="44485@x" ObjectIDZND1="6179@0" Pin0InfoVect0LinkObjId="SW-280175_0" Pin0InfoVect1LinkObjId="g_2c53320_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280176_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-471 3790,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a5ee30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-498 3790,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="44485@0" ObjectIDZND0="44487@x" ObjectIDZND1="6179@0" Pin0InfoVect0LinkObjId="SW-280176_0" Pin0InfoVect1LinkObjId="g_2c53320_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280175_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-498 3790,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a5f090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-471 3790,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="44487@x" ObjectIDND1="44485@x" ObjectIDZND0="6179@0" Pin0InfoVect0LinkObjId="g_2c53320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-280176_0" Pin1InfoVect1LinkObjId="SW-280175_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-471 3790,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a60100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3898,-471 3882,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3a5f920@0" ObjectIDZND0="44488@1" Pin0InfoVect0LinkObjId="SW-280176_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a5f920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3898,-471 3882,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a60330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4977,-106 4976,-147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2c6ccb0@1" Pin0InfoVect0LinkObjId="g_2c6ccb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37742f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4977,-106 4976,-147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a659a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-404 4032,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6198@1" ObjectIDZND0="6197@0" Pin0InfoVect0LinkObjId="SW-38670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38671_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-404 4032,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a65c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-357 4032,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6197@1" ObjectIDZND0="44497@1" Pin0InfoVect0LinkObjId="SW-38671_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38670_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-357 4032,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a65e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-279 4032,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="6199@0" ObjectIDZND0="44497@x" ObjectIDZND1="g_2adc950@0" Pin0InfoVect0LinkObjId="SW-38671_0" Pin0InfoVect1LinkObjId="g_2adc950_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38672_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-279 4032,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a668a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-330 4032,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="44497@0" ObjectIDZND0="6199@x" ObjectIDZND1="g_2adc950@0" Pin0InfoVect0LinkObjId="SW-38672_0" Pin0InfoVect1LinkObjId="g_2adc950_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38671_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-330 4032,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a66b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-279 4032,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6199@x" ObjectIDND1="44497@x" ObjectIDZND0="g_2adc950@0" Pin0InfoVect0LinkObjId="g_2adc950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38672_0" Pin1InfoVect1LinkObjId="SW-38671_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-279 4032,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a66d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3994,-154 3994,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_3ab6520@0" ObjectIDZND0="44499@x" ObjectIDZND1="44498@x" ObjectIDZND2="40086@x" Pin0InfoVect0LinkObjId="SW-280185_0" Pin0InfoVect1LinkObjId="SW-280184_0" Pin0InfoVect2LinkObjId="CB-CX_YC.CX_YC_Cb1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ab6520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3994,-154 3994,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a677a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-187 4032,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="44498@0" ObjectIDZND0="40086@x" ObjectIDZND1="g_3ab6520@0" ObjectIDZND2="44499@x" Pin0InfoVect0LinkObjId="CB-CX_YC.CX_YC_Cb1_0" Pin0InfoVect1LinkObjId="g_3ab6520_0" Pin0InfoVect2LinkObjId="SW-280185_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280184_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-187 4032,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a67a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-173 4032,-152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="capacitor" ObjectIDND0="44498@x" ObjectIDND1="g_3ab6520@0" ObjectIDND2="44499@x" ObjectIDZND0="40086@0" Pin0InfoVect0LinkObjId="CB-CX_YC.CX_YC_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-280184_0" Pin1InfoVect1LinkObjId="g_3ab6520_0" Pin1InfoVect2LinkObjId="SW-280185_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-173 4032,-152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a68440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3986,-173 3994,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="44499@0" ObjectIDZND0="g_3ab6520@0" ObjectIDZND1="44498@x" ObjectIDZND2="40086@x" Pin0InfoVect0LinkObjId="g_3ab6520_0" Pin0InfoVect1LinkObjId="SW-280184_0" Pin0InfoVect2LinkObjId="CB-CX_YC.CX_YC_Cb1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280185_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3986,-173 3994,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a68680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3994,-173 4032,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="g_3ab6520@0" ObjectIDND1="44499@x" ObjectIDZND0="44498@x" ObjectIDZND1="40086@x" Pin0InfoVect0LinkObjId="SW-280184_0" Pin0InfoVect1LinkObjId="CB-CX_YC.CX_YC_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3ab6520_0" Pin1InfoVect1LinkObjId="SW-280185_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3994,-173 4032,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a688e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-223 4032,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="44498@1" ObjectIDZND0="g_2adc950@1" Pin0InfoVect0LinkObjId="g_2adc950_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280184_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-223 4032,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a68b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-1088 4275,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_37778d0@1" ObjectIDZND0="g_37742f0@0" ObjectIDZND1="6194@x" ObjectIDZND2="g_3778640@0" Pin0InfoVect0LinkObjId="g_37742f0_0" Pin0InfoVect1LinkObjId="SW-38658_0" Pin0InfoVect2LinkObjId="g_3778640_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37778d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-1088 4275,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a697a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4275,-1111 4275,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="38060@1" ObjectIDZND0="g_37778d0@0" ObjectIDZND1="g_37742f0@0" ObjectIDZND2="6194@x" Pin0InfoVect0LinkObjId="g_37778d0_0" Pin0InfoVect1LinkObjId="g_37742f0_0" Pin0InfoVect2LinkObjId="SW-38658_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4275,-1111 4275,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a69a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4275,-1088 4275,-1013 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="g_37778d0@0" ObjectIDND1="g_3778640@0" ObjectIDND2="38060@1" ObjectIDZND0="g_37742f0@0" ObjectIDZND1="6194@x" Pin0InfoVect0LinkObjId="g_37742f0_0" Pin0InfoVect1LinkObjId="SW-38658_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_37778d0_0" Pin1InfoVect1LinkObjId="g_3778640_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4275,-1088 4275,-1013 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a69c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4319,-1088 4275,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="g_3778640@0" ObjectIDZND0="g_37778d0@0" ObjectIDZND1="g_37742f0@0" ObjectIDZND2="6194@x" Pin0InfoVect0LinkObjId="g_37778d0_0" Pin0InfoVect1LinkObjId="g_37742f0_0" Pin0InfoVect2LinkObjId="SW-38658_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3778640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4319,-1088 4275,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a6a6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4275,-1013 4275,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_37742f0@0" ObjectIDND1="g_37778d0@0" ObjectIDND2="g_3778640@0" ObjectIDZND0="6194@1" Pin0InfoVect0LinkObjId="SW-38658_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_37742f0_0" Pin1InfoVect1LinkObjId="g_37778d0_0" Pin1InfoVect2LinkObjId="g_3778640_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4275,-1013 4275,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf6920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4233,-842 4275,-842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="breaker" ObjectIDND0="6195@0" ObjectIDZND0="49502@0" ObjectIDZND1="6196@x" Pin0InfoVect0LinkObjId="g_3773e30_0" Pin0InfoVect1LinkObjId="SW-38662_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-38659_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4233,-842 4275,-842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ba9b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4275,-889 4275,-842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49502@0" ObjectIDZND0="6195@x" ObjectIDZND1="6196@x" Pin0InfoVect0LinkObjId="SW-38659_0" Pin0InfoVect1LinkObjId="SW-38662_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3773e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4275,-889 4275,-842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ba9d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4275,-842 4275,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="breaker" ObjectIDND0="6195@x" ObjectIDND1="49502@0" ObjectIDZND0="6196@0" Pin0InfoVect0LinkObjId="SW-38662_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-38659_0" Pin1InfoVect1LinkObjId="g_3773e30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4275,-842 4275,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_364b460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4808,-305 4808,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_2b96d70@0" ObjectIDND1="44510@x" ObjectIDND2="49501@x" ObjectIDZND0="44509@0" Pin0InfoVect0LinkObjId="SW-280249_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b96d70_0" Pin1InfoVect1LinkObjId="SW-280251_0" Pin1InfoVect2LinkObjId="EC-CX_YC.077Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4808,-305 4808,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_364f150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4808,-110 4808,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="49501@0" ObjectIDZND0="44510@x" ObjectIDZND1="g_2b96d70@0" ObjectIDZND2="44509@x" Pin0InfoVect0LinkObjId="SW-280251_0" Pin0InfoVect1LinkObjId="g_2b96d70_0" Pin0InfoVect2LinkObjId="SW-280249_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_YC.077Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4808,-110 4808,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3664950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4808,-282 4808,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="44510@x" ObjectIDND1="49501@x" ObjectIDZND0="g_2b96d70@0" ObjectIDZND1="44509@x" Pin0InfoVect0LinkObjId="g_2b96d70_0" Pin0InfoVect1LinkObjId="SW-280249_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-280251_0" Pin1InfoVect1LinkObjId="EC-CX_YC.077Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4808,-282 4808,-305 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="6179" cx="4976" cy="-443" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6179" cx="4239" cy="-443" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6179" cx="3656" cy="-443" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6179" cx="3840" cy="-443" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6179" cx="4032" cy="-443" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6179" cx="4449" cy="-443" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6179" cx="4626" cy="-443" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6179" cx="4808" cy="-443" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6179" cx="5165" cy="-443" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6179" cx="4275" cy="-443" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6179" cx="3790" cy="-443" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49502" cx="4275" cy="-889" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49502" cx="4275" cy="-889" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37337" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3444.000000 -1085.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5919" ObjectName="DYN-CX_YC"/>
     <cge:Meas_Ref ObjectId="37337"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bc3e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4109.000000 -945.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c69b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4797.000000 -21.000000) translate(0,15)">备用二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ad4f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4441.000000 -22.000000) translate(0,15)">宜苴线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bf2500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5141.000000 -33.000000) translate(0,15)">宜打线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c53630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4216.000000 -24.000000) translate(0,15)">邑多么线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b8f3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4313.000000 -1053.000000) translate(0,15)">宜茨T接线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ade890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3712.000000 -697.000000) translate(0,15)">10kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c0ed40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4301.000000 -790.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bc2ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4368.000000 -729.000000) translate(0,15)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bc2ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4368.000000 -729.000000) translate(0,33)">SZ20-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bc2ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4368.000000 -729.000000) translate(0,51)">YNd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c1e0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3541.000000 -440.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2bdb4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3280.500000 -1166.500000) translate(0,16)">宜茨变(新)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ad4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3398a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1026.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3398a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1026.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3398a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1026.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3398a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1026.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3398a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1026.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3398a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1026.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3398a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1026.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_38897c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3402.000000 -1152.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2c0f7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3402.000000 -1187.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ade510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -838.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2bfdb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -195.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2bfdb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -195.000000) translate(0,38)">心变运三班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c996f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3258.000000 -205.500000) translate(0,17)">18787878955</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c996f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3258.000000 -205.500000) translate(0,38)">18787878953</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c996f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3258.000000 -205.500000) translate(0,59)">18787878979</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c52f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3258.000000 -230.000000) translate(0,17)">3818264</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2c52800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3411.500000 -1050.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_327e2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3641.000000 -22.000000) translate(0,15)">宜白线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b850e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4934.000000 -71.000000) translate(0,15)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b856d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3812.000000 -25.000000) translate(0,15)">马宜线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b85900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4628.000000 -21.000000) translate(0,15)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b87fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3672.000000 -381.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b882f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3596.000000 -308.000000) translate(0,12)">07167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b88530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3663.000000 -187.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b886f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3850.000000 -380.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b88930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3765.000000 -308.000000) translate(0,12)">07267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b88b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3847.000000 -188.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b88db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4042.000000 -381.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b88ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4249.000000 -376.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b89230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4246.000000 -184.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b89640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4164.000000 -304.000000) translate(0,12)">07467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b89880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4459.000000 -380.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b89ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4456.000000 -185.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b89ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4374.000000 -308.000000) translate(0,12)">07567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b8a110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5175.000000 -381.000000) translate(0,12)">079</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b8a350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5090.000000 -309.000000) translate(0,12)">07967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b8a7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5172.000000 -186.000000) translate(0,12)">0796</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b8a9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4286.000000 -519.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b8ac30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3960.000000 -305.000000) translate(0,12)">07360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b8ae70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4039.000000 -195.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b8b0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3956.000000 -200.000000) translate(0,12)">07367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b8b2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4649.000000 -376.000000) translate(0,12)">076</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b8b530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4633.000000 -186.000000) translate(0,12)">0766</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b8b770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4551.000000 -309.000000) translate(0,12)">07667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b8b9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4828.000000 -376.000000) translate(0,12)">077</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b8bbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4733.000000 -308.000000) translate(0,12)">07767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b8be30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4993.000000 -395.000000) translate(0,12)">0911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b8c070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.000000 -965.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b8c2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4189.000000 -863.000000) translate(0,12)">36260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b8c4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4897.000000 -316.000000) translate(0,12)">09117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab51e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4170.000000 -693.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab5f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3972.000000 -41.000000) translate(0,12)">10kV1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a585b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3740.000000 -530.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a5f2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3829.000000 -499.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_3a60560" transform="matrix(0.791667 -0.000000 -0.000000 0.941176 3141.583333 -793.647059) translate(0,20)">隔刀远控</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3119" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1078"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-598"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3395" y="-1061"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="7" stroke="rgb(50,205,50)" stroke-width="1" width="21" x="3837" y="-665"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YC.CX_YC_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3617,-443 5221,-443 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6179" ObjectName="BS-CX_YC.CX_YC_9IM"/>
    <cge:TPSR_Ref TObjectID="6179"/></metadata>
   <polyline fill="none" opacity="0" points="3617,-443 5221,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YC.XM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4260,-889 4290,-889 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49502" ObjectName="BS-CX_YC.XM"/>
    <cge:TPSR_Ref TObjectID="49502"/></metadata>
   <polyline fill="none" opacity="0" points="4260,-889 4290,-889 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_YC.CX_YC_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="8984"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4236.000000 -626.000000)" xlink:href="#transformer2:shape54_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4236.000000 -626.000000)" xlink:href="#transformer2:shape54_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="6200" ObjectName="TF-CX_YC.CX_YC_1T"/>
    <cge:TPSR_Ref TObjectID="6200"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3228.500000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3240" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3240" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3192" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3192" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3391" y="-1160"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3391" y="-1160"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3391" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3391" y="-1195"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="22" qtmmishow="hidden" width="72" x="3143" y="-841"/>
    </a>
   <metadata/><rect fill="white" height="22" opacity="0" stroke="white" transform="" width="72" x="3143" y="-841"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="3394" y="-1062"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="3394" y="-1062"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3850" y="-380"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3850" y="-380"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4042" y="-381"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4042" y="-381"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4249" y="-376"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4249" y="-376"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="4170" y="-693"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="4170" y="-693"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3672" y="-381"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3672" y="-381"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4459" y="-380"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4459" y="-380"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4649" y="-376"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4649" y="-376"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4828" y="-376"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4828" y="-376"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5175" y="-381"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5175" y="-381"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="23" qtmmishow="hidden" width="76" x="3140" y="-795"/>
    </a>
   <metadata/><rect fill="white" height="23" opacity="0" stroke="white" transform="" width="76" x="3140" y="-795"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c4e7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3525.000000 502.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c4a390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3526.000000 520.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c17290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3518.000000 469.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c39db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3525.000000 485.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c12c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3799.000000 -21.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b22530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3824.000000 -36.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c14210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3810.000000 -6.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b77310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5124.000000 -28.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b765f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5149.000000 -43.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c21910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5135.000000 -13.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c02880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4217.000000 -24.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c14c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4242.000000 -39.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cd3f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4228.000000 -9.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c670e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4424.000000 -28.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ecd550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4449.000000 -43.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc4010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4435.000000 -13.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_340c1d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3971.000000 -13.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c66f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3996.000000 -28.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ada120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3621.000000 -21.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3431fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 -36.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c736a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3632.000000 -6.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b85bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4596.000000 -29.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b86000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4621.000000 -44.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b86240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4607.000000 -14.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b86570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4775.000000 -32.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b867d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4800.000000 -47.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b86a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4786.000000 -17.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b86d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4116.000000 782.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b86fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4102.000000 812.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b871e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4091.000000 797.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b87510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4147.000000 501.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b87770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4133.000000 531.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b879b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4122.000000 516.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ac14c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4366.000000 651.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ac1b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4366.000000 666.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_YC.CX_YC_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4011.000000 -48.000000)" xlink:href="#capacitor:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40086" ObjectName="CB-CX_YC.CX_YC_Cb1"/>
    <cge:TPSR_Ref TObjectID="40086"/></metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38588" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3865.000000 6.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38588" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6191"/>
     <cge:Term_Ref ObjectID="8964"/>
    <cge:TPSR_Ref TObjectID="6191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38589" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3865.000000 6.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38589" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6191"/>
     <cge:Term_Ref ObjectID="8964"/>
    <cge:TPSR_Ref TObjectID="6191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38586" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3865.000000 6.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38586" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6191"/>
     <cge:Term_Ref ObjectID="8964"/>
    <cge:TPSR_Ref TObjectID="6191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38579" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3685.000000 7.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38579" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6187"/>
     <cge:Term_Ref ObjectID="8956"/>
    <cge:TPSR_Ref TObjectID="6187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38580" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3685.000000 7.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38580" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6187"/>
     <cge:Term_Ref ObjectID="8956"/>
    <cge:TPSR_Ref TObjectID="6187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38576" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3685.000000 7.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38576" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6187"/>
     <cge:Term_Ref ObjectID="8956"/>
    <cge:TPSR_Ref TObjectID="6187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38598" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4036.000000 12.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38598" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6197"/>
     <cge:Term_Ref ObjectID="8976"/>
    <cge:TPSR_Ref TObjectID="6197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38595" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4036.000000 12.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38595" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6197"/>
     <cge:Term_Ref ObjectID="8976"/>
    <cge:TPSR_Ref TObjectID="6197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38584" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.000000 9.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38584" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6189"/>
     <cge:Term_Ref ObjectID="8960"/>
    <cge:TPSR_Ref TObjectID="6189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38585" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.000000 9.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38585" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6189"/>
     <cge:Term_Ref ObjectID="8960"/>
    <cge:TPSR_Ref TObjectID="6189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38581" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.000000 9.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38581" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6189"/>
     <cge:Term_Ref ObjectID="8960"/>
    <cge:TPSR_Ref TObjectID="6189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38574" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4488.000000 14.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38574" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6185"/>
     <cge:Term_Ref ObjectID="8952"/>
    <cge:TPSR_Ref TObjectID="6185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38575" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4488.000000 14.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38575" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6185"/>
     <cge:Term_Ref ObjectID="8952"/>
    <cge:TPSR_Ref TObjectID="6185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38571" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4488.000000 14.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38571" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6185"/>
     <cge:Term_Ref ObjectID="8952"/>
    <cge:TPSR_Ref TObjectID="6185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-280396" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4658.000000 15.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="280396" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44502"/>
     <cge:Term_Ref ObjectID="21910"/>
    <cge:TPSR_Ref TObjectID="44502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-280397" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4658.000000 15.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="280397" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44502"/>
     <cge:Term_Ref ObjectID="21910"/>
    <cge:TPSR_Ref TObjectID="44502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-280393" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4658.000000 15.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="280393" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44502"/>
     <cge:Term_Ref ObjectID="21910"/>
    <cge:TPSR_Ref TObjectID="44502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-280402" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4839.000000 18.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="280402" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44507"/>
     <cge:Term_Ref ObjectID="21920"/>
    <cge:TPSR_Ref TObjectID="44507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-280403" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4839.000000 18.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="280403" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44507"/>
     <cge:Term_Ref ObjectID="21920"/>
    <cge:TPSR_Ref TObjectID="44507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-280399" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4839.000000 18.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="280399" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44507"/>
     <cge:Term_Ref ObjectID="21920"/>
    <cge:TPSR_Ref TObjectID="44507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38569" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5188.000000 13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38569" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6183"/>
     <cge:Term_Ref ObjectID="8948"/>
    <cge:TPSR_Ref TObjectID="6183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38570" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5188.000000 13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38570" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6183"/>
     <cge:Term_Ref ObjectID="8948"/>
    <cge:TPSR_Ref TObjectID="6183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38566" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5188.000000 13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38566" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6183"/>
     <cge:Term_Ref ObjectID="8948"/>
    <cge:TPSR_Ref TObjectID="6183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-38559" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4187.000000 -533.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6182"/>
     <cge:Term_Ref ObjectID="8946"/>
    <cge:TPSR_Ref TObjectID="6182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-38560" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4187.000000 -533.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38560" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6182"/>
     <cge:Term_Ref ObjectID="8946"/>
    <cge:TPSR_Ref TObjectID="6182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-38556" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4187.000000 -533.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38556" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6182"/>
     <cge:Term_Ref ObjectID="8946"/>
    <cge:TPSR_Ref TObjectID="6182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-84447" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4157.000000 -814.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84447" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6196"/>
     <cge:Term_Ref ObjectID="8974"/>
    <cge:TPSR_Ref TObjectID="6196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-84448" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4157.000000 -814.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84448" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6196"/>
     <cge:Term_Ref ObjectID="8974"/>
    <cge:TPSR_Ref TObjectID="6196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-84438" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4157.000000 -814.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84438" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6196"/>
     <cge:Term_Ref ObjectID="8974"/>
    <cge:TPSR_Ref TObjectID="6196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-84431" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3605.000000 -516.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84431" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6179"/>
     <cge:Term_Ref ObjectID="8941"/>
    <cge:TPSR_Ref TObjectID="6179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-84432" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3605.000000 -516.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84432" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6179"/>
     <cge:Term_Ref ObjectID="8941"/>
    <cge:TPSR_Ref TObjectID="6179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-84433" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3605.000000 -516.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84433" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6179"/>
     <cge:Term_Ref ObjectID="8941"/>
    <cge:TPSR_Ref TObjectID="6179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-84434" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3605.000000 -516.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84434" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6179"/>
     <cge:Term_Ref ObjectID="8941"/>
    <cge:TPSR_Ref TObjectID="6179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-38565" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4461.000000 -667.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38565" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6200"/>
     <cge:Term_Ref ObjectID="8985"/>
    <cge:TPSR_Ref TObjectID="6200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-38593" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4461.000000 -667.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="38593" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6200"/>
     <cge:Term_Ref ObjectID="8985"/>
    <cge:TPSR_Ref TObjectID="6200"/></metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-38658">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4266.000000 -935.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6194" ObjectName="SW-CX_YC.CX_YC_3626SW"/>
     <cge:Meas_Ref ObjectId="38658"/>
    <cge:TPSR_Ref TObjectID="6194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38656">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 -398.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6188" ObjectName="SW-CX_YC.CX_YC_071XC"/>
     <cge:Meas_Ref ObjectId="38656"/>
    <cge:TPSR_Ref TObjectID="6188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38656">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 -324.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44477" ObjectName="SW-CX_YC.CX_YC_071XC1"/>
     <cge:Meas_Ref ObjectId="38656"/>
    <cge:TPSR_Ref TObjectID="44477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84505">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3647.000000 -157.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18598" ObjectName="SW-CX_YC.CX_YC_0716SW"/>
     <cge:Meas_Ref ObjectId="84505"/>
    <cge:TPSR_Ref TObjectID="18598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38640">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3562.000000 -277.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44480" ObjectName="SW-CX_YC.CX_YC_07167SW"/>
     <cge:Meas_Ref ObjectId="38640"/>
    <cge:TPSR_Ref TObjectID="44480"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280179">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3830.000000 -396.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6192" ObjectName="SW-CX_YC.CX_YC_072XC"/>
     <cge:Meas_Ref ObjectId="280179"/>
    <cge:TPSR_Ref TObjectID="6192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280179">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3830.000000 -322.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44492" ObjectName="SW-CX_YC.CX_YC_072XC1"/>
     <cge:Meas_Ref ObjectId="280179"/>
    <cge:TPSR_Ref TObjectID="44492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280180">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3746.000000 -275.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44493" ObjectName="SW-CX_YC.CX_YC_07267SW"/>
     <cge:Meas_Ref ObjectId="280180"/>
    <cge:TPSR_Ref TObjectID="44493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38648">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4229.000000 -392.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6190" ObjectName="SW-CX_YC.CX_YC_074XC"/>
     <cge:Meas_Ref ObjectId="38648"/>
    <cge:TPSR_Ref TObjectID="6190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38648">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4229.000000 -318.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44478" ObjectName="SW-CX_YC.CX_YC_074XC1"/>
     <cge:Meas_Ref ObjectId="38648"/>
    <cge:TPSR_Ref TObjectID="44478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84483">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4230.000000 -154.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18597" ObjectName="SW-CX_YC.CX_YC_0746SW"/>
     <cge:Meas_Ref ObjectId="84483"/>
    <cge:TPSR_Ref TObjectID="18597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280181">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4145.000000 -271.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44494" ObjectName="SW-CX_YC.CX_YC_07467SW"/>
     <cge:Meas_Ref ObjectId="280181"/>
    <cge:TPSR_Ref TObjectID="44494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38671">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4022.000000 -397.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6198" ObjectName="SW-CX_YC.CX_YC_073XC"/>
     <cge:Meas_Ref ObjectId="38671"/>
    <cge:TPSR_Ref TObjectID="6198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38671">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4022.000000 -323.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44497" ObjectName="SW-CX_YC.CX_YC_073XC1"/>
     <cge:Meas_Ref ObjectId="38671"/>
    <cge:TPSR_Ref TObjectID="44497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38672">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3941.000000 -272.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6199" ObjectName="SW-CX_YC.CX_YC_07360SW"/>
     <cge:Meas_Ref ObjectId="38672"/>
    <cge:TPSR_Ref TObjectID="6199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280184">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4023.000000 -182.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44498" ObjectName="SW-CX_YC.CX_YC_0736SW"/>
     <cge:Meas_Ref ObjectId="280184"/>
    <cge:TPSR_Ref TObjectID="44498"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84482">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3831.000000 -158.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10570" ObjectName="SW-CX_YC.CX_YC_0726SW"/>
     <cge:Meas_Ref ObjectId="84482"/>
    <cge:TPSR_Ref TObjectID="10570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38632">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4439.000000 -396.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6186" ObjectName="SW-CX_YC.CX_YC_075XC"/>
     <cge:Meas_Ref ObjectId="38632"/>
    <cge:TPSR_Ref TObjectID="6186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38632">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4439.000000 -322.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44481" ObjectName="SW-CX_YC.CX_YC_075XC1"/>
     <cge:Meas_Ref ObjectId="38632"/>
    <cge:TPSR_Ref TObjectID="44481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84731">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4440.000000 -155.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18599" ObjectName="SW-CX_YC.CX_YC_0756SW"/>
     <cge:Meas_Ref ObjectId="84731"/>
    <cge:TPSR_Ref TObjectID="18599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280182">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4355.000000 -275.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44495" ObjectName="SW-CX_YC.CX_YC_07567SW"/>
     <cge:Meas_Ref ObjectId="280182"/>
    <cge:TPSR_Ref TObjectID="44495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280196">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.000000 -397.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44503" ObjectName="SW-CX_YC.CX_YC_076XC"/>
     <cge:Meas_Ref ObjectId="280196"/>
    <cge:TPSR_Ref TObjectID="44503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280196">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.000000 -323.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44504" ObjectName="SW-CX_YC.CX_YC_076XC1"/>
     <cge:Meas_Ref ObjectId="280196"/>
    <cge:TPSR_Ref TObjectID="44504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280198">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4617.000000 -156.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44505" ObjectName="SW-CX_YC.CX_YC_0766SW"/>
     <cge:Meas_Ref ObjectId="280198"/>
    <cge:TPSR_Ref TObjectID="44505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280200">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4532.000000 -276.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44506" ObjectName="SW-CX_YC.CX_YC_07667SW"/>
     <cge:Meas_Ref ObjectId="280200"/>
    <cge:TPSR_Ref TObjectID="44506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280249">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4798.000000 -396.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44508" ObjectName="SW-CX_YC.CX_YC_077XC"/>
     <cge:Meas_Ref ObjectId="280249"/>
    <cge:TPSR_Ref TObjectID="44508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280249">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4798.000000 -320.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44509" ObjectName="SW-CX_YC.CX_YC_077XC1"/>
     <cge:Meas_Ref ObjectId="280249"/>
    <cge:TPSR_Ref TObjectID="44509"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280251">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4714.000000 -275.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44510" ObjectName="SW-CX_YC.CX_YC_07767SW"/>
     <cge:Meas_Ref ObjectId="280251"/>
    <cge:TPSR_Ref TObjectID="44510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280177">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4966.000000 -398.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44489" ObjectName="SW-CX_YC.CX_YC_0911XC"/>
     <cge:Meas_Ref ObjectId="280177"/>
    <cge:TPSR_Ref TObjectID="44489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280177">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4966.000000 -325.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44490" ObjectName="SW-CX_YC.CX_YC_0911XC1"/>
     <cge:Meas_Ref ObjectId="280177"/>
    <cge:TPSR_Ref TObjectID="44490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280178">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4882.000000 -278.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44491" ObjectName="SW-CX_YC.CX_YC_09117SW"/>
     <cge:Meas_Ref ObjectId="280178"/>
    <cge:TPSR_Ref TObjectID="44491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38624">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5155.000000 -397.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6184" ObjectName="SW-CX_YC.CX_YC_079XC"/>
     <cge:Meas_Ref ObjectId="38624"/>
    <cge:TPSR_Ref TObjectID="6184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38624">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5155.000000 -323.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44482" ObjectName="SW-CX_YC.CX_YC_079XC1"/>
     <cge:Meas_Ref ObjectId="38624"/>
    <cge:TPSR_Ref TObjectID="44482"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84546">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5156.000000 -156.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18600" ObjectName="SW-CX_YC.CX_YC_0796SW"/>
     <cge:Meas_Ref ObjectId="84546"/>
    <cge:TPSR_Ref TObjectID="18600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280183">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5071.000000 -276.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44496" ObjectName="SW-CX_YC.CX_YC_07967SW"/>
     <cge:Meas_Ref ObjectId="280183"/>
    <cge:TPSR_Ref TObjectID="44496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38612">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4265.000000 -534.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44479" ObjectName="SW-CX_YC.CX_YC_001XC1"/>
     <cge:Meas_Ref ObjectId="38612"/>
    <cge:TPSR_Ref TObjectID="44479"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38612">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4265.000000 -460.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6181" ObjectName="SW-CX_YC.CX_YC_001XC"/>
     <cge:Meas_Ref ObjectId="38612"/>
    <cge:TPSR_Ref TObjectID="6181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-38659">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4182.000000 -835.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6195" ObjectName="SW-CX_YC.CX_YC_36260SW"/>
     <cge:Meas_Ref ObjectId="38659"/>
    <cge:TPSR_Ref TObjectID="6195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280175">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3780.000000 -519.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44486" ObjectName="SW-CX_YC.CX_YC_0901XC1"/>
     <cge:Meas_Ref ObjectId="280175"/>
    <cge:TPSR_Ref TObjectID="44486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280175">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3780.000000 -491.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44485" ObjectName="SW-CX_YC.CX_YC_0901XC"/>
     <cge:Meas_Ref ObjectId="280175"/>
    <cge:TPSR_Ref TObjectID="44485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280176">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3810.000000 -462.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44487" ObjectName="SW-CX_YC.CX_YC_09017XC"/>
     <cge:Meas_Ref ObjectId="280176"/>
    <cge:TPSR_Ref TObjectID="44487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280176">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3861.000000 -462.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44488" ObjectName="SW-CX_YC.CX_YC_09017XC1"/>
     <cge:Meas_Ref ObjectId="280176"/>
    <cge:TPSR_Ref TObjectID="44488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280185">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3935.000000 -166.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44499" ObjectName="SW-CX_YC.CX_YC_07367SW"/>
     <cge:Meas_Ref ObjectId="280185"/>
    <cge:TPSR_Ref TObjectID="44499"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3240" y="-1177"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3192" y="-1194"/></g>
   <g href="cx_配调_配网接线图35_楚雄.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3391" y="-1160"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3391" y="-1195"/></g>
   <g href="35kV宜茨变GG虚设备间隔接线图_0.svg" style="fill-opacity:0"><rect height="22" qtmmishow="hidden" width="72" x="3143" y="-841"/></g>
   <g href="AVC宜茨站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="3394" y="-1062"/></g>
   <g href="35kV宜茨变10kV马宜线072断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3850" y="-380"/></g>
   <g href="35kV宜茨变10kV1号电容器组073断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4042" y="-381"/></g>
   <g href="35kV宜茨变10kV邑多么线074断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4249" y="-376"/></g>
   <g href="35kV宜茨变1号主变间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="4170" y="-693"/></g>
   <g href="35kV宜茨变10kV宜白线071断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3672" y="-381"/></g>
   <g href="35kV宜茨变10kV宜苴线075断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4459" y="-380"/></g>
   <g href="35kV宜茨变10kV备用一076断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4649" y="-376"/></g>
   <g href="35kV宜茨变10kV备用二077断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4828" y="-376"/></g>
   <g href="35kV宜茨变10kV集镇线079断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5175" y="-381"/></g>
   <g href="35kV宜茨变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="23" qtmmishow="hidden" width="76" x="3140" y="-795"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2c6a5f0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3741.500000 -649.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b9a050">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3691.000000 -70.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b75900">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3872.000000 -75.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c1e270">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4272.000000 -71.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2adc950">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4041.500000 -231.500000)" xlink:href="#lightningRod:shape192"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b78270">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3849.500000 -215.500000)" xlink:href="#lightningRod:shape192"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bd8eb0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4248.500000 -215.500000)" xlink:href="#lightningRod:shape192"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bc2480">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4484.000000 -68.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c319e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4661.000000 -69.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b96d70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4838.000000 -247.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c24c30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5006.000000 -250.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d50520">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5200.000000 -69.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b28040">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4635.500000 -213.500000)" xlink:href="#lightningRod:shape192"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b28db0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4985.500000 -214.500000)" xlink:href="#lightningRod:shape192"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c6ccb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4962.000000 -105.000000)" xlink:href="#lightningRod:shape16"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39acb90">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4284.500000 -571.500000)" xlink:href="#lightningRod:shape192"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39af5d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 -613.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3776240">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4151.000000 -955.000000)" xlink:href="#lightningRod:shape16"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37778d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4160.000000 -1083.000000)" xlink:href="#lightningRod:shape205"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3778640">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4315.000000 -1080.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ab6520">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3987.000000 -96.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ab6db0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3781.000000 -547.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ab7560">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4967.000000 -357.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_YC"/>
</svg>