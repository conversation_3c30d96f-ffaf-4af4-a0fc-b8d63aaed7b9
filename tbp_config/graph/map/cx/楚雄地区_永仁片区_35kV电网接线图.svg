<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-8" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-246 -5241 3353 1770">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape9_0">
    <rect height="14" stroke-width="0.833219" width="27" x="39" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="89" x2="80" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="88" x2="97" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="97" x2="88" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="80" x2="89" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="89" x2="66" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="13" x2="39" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="13" x2="22" y1="10" y2="1"/>
   </symbol>
   <symbol id="breaker2:shape9_1">
    <rect height="14" stroke-width="0.833219" width="27" x="39" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="39" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="97" x2="66" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="97" x2="88" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="88" x2="97" y1="19" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape9-UnNor1">
    <rect height="14" stroke-width="0.833219" width="27" x="39" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="13" x2="22" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="13" x2="39" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="89" x2="66" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="80" x2="89" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="97" x2="88" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="88" x2="97" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="89" x2="80" y1="10" y2="1"/>
   </symbol>
   <symbol id="breaker2:shape9-UnNor2">
    <rect height="14" stroke-width="0.833219" width="27" x="39" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="39" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="97" x2="66" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="97" x2="88" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="88" x2="97" y1="19" y2="10"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape29">
    <ellipse cx="11" cy="15" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="28" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape116">
    <circle cx="8" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="14" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="18" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="8" x2="8" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="0" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="0" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="0" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="0" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape59">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="72" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="23" y2="23"/>
    <circle cx="9" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="63" y2="63"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,61 9,39 9,30 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="48"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="0" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-16,39 6,17 6,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-16,39 6,17 6,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape18_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="13" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape18_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor1">
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="14" y2="65"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="66"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape16_0">
    <circle cx="29" cy="25" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="23" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="23" x2="15" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="15" x2="23" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape16_1">
    <ellipse cx="60" cy="25" fillStyle="0" rx="24.5" ry="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="74" x2="66" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="66" x2="58" y1="25" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="58" x2="66" y1="17" y2="25"/>
   </symbol>
   <symbol id="transformer2:shape48_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape48_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
   </symbol>
   <symbol id="transformer2:shape20_0">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <polyline DF8003:Layer="0" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="13" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="11" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape20_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape77_0">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,61 6,61 6,32 " stroke-width="1"/>
    <circle cx="31" cy="64" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="19" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="49" y2="31"/>
    <polyline DF8003:Layer="0" points="31,18 25,31 37,31 31,18 31,19 31,18 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="31" y1="56" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="61" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="61" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="61" y2="56"/>
   </symbol>
   <symbol id="transformer2:shape77_1">
    <circle cx="31" cy="86" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="31" y1="90" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="31" y1="90" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="26" y1="90" y2="90"/>
   </symbol>
   <symbol id="voltageTransformer:shape7">
    <circle cx="7" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="7" cy="6" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="16" cy="6" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="16" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="voltageTransformer:shape50">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="8" y2="11"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="23" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="7" y2="11"/>
   </symbol>
   <symbol id="voltageTransformer:shape100">
    <circle cx="7" cy="6" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="16" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="7" cy="16" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="voltageTransformer:shape99">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="34" x2="34" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="21" x2="24" y1="30" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="30" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="24" x2="24" y1="32" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="42" x2="39" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="39" x2="39" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="36" x2="39" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="23" y2="26"/>
    <circle cx="24" cy="32" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="19" y2="19"/>
    <circle cx="39" cy="23" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="19" y2="19"/>
    <circle cx="34" cy="32" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="32" x2="29" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="29" x2="29" y1="23" y2="26"/>
    <circle cx="19" cy="23" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="26" x2="29" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="8" x2="8" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="31" x2="37" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.755439" x1="32" x2="31" y1="31" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7538" x1="36" x2="37" y1="31" y2="34"/>
    <circle cx="29" cy="23" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="9" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="41" x2="41" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="41" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="17" x2="17" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="24" x2="24" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="24" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="17" y1="8" y2="8"/>
   </symbol>
   <symbol id="voltageTransformer:shape21">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="27" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="11" y2="5"/>
    <circle cx="27" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="13" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape80">
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="67" y2="23"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4236a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4237340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4237ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4238980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4239be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_423a800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_423af40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_423b7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_423bf20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_423c860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_423d360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_423d940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_423f360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_423ff10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4240800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4240f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_42424c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4242f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_42436d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4243e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4244f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_42458f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_42463e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_424b7d0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_424c4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_42480e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4249550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_424a090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_4257db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_424e500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1780" width="3363" x="-251" y="-5246"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="819" x2="837" y1="-4775" y2="-4756"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="709" x2="746" y1="-4764" y2="-4801"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1739" x2="1757" y1="-4020" y2="-4002"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1757" x2="1739" y1="-4020" y2="-4002"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1820" x2="1802" y1="-4020" y2="-4002"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1802" x2="1820" y1="-4020" y2="-4002"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(65,105,225)" stroke-width="1" x1="394" x2="394" y1="-4051" y2="-4051"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="532" x2="550" y1="-4301" y2="-4283"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="550" x2="532" y1="-4301" y2="-4283"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="2824" x2="2829" y1="-4240" y2="-4240"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="2801" x2="2796" y1="-4240" y2="-4240"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="0" fill="none" height="21" stroke="rgb(255,255,0)" stroke-width="1" width="10" x="824" y="-4775"/>
   <rect DF8003:Layer="0" fill="none" height="13" stroke="rgb(255,255,0)" stroke-width="0.371482" width="23" x="2801" y="-4247"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="0" id="SW-19281">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2829.000000 -4910.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2945" ObjectName="SW-CX_YR.CX_YR_3611SW"/>
     <cge:Meas_Ref ObjectId="19281"/>
    <cge:TPSR_Ref TObjectID="2945"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-19282">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2829.000000 -4820.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2946" ObjectName="SW-CX_YR.CX_YR_3616SW"/>
     <cge:Meas_Ref ObjectId="19282"/>
    <cge:TPSR_Ref TObjectID="2946"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-19293">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2503.000000 -4974.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2957" ObjectName="SW-CX_YR.CX_YR_3122SW"/>
     <cge:Meas_Ref ObjectId="19293"/>
    <cge:TPSR_Ref TObjectID="2957"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-19294">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2602.000000 -4972.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2958" ObjectName="SW-CX_YR.CX_YR_3121SW"/>
     <cge:Meas_Ref ObjectId="19294"/>
    <cge:TPSR_Ref TObjectID="2958"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-19284">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2666.000000 -4911.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2948" ObjectName="SW-CX_YR.CX_YR_3621SW"/>
     <cge:Meas_Ref ObjectId="19284"/>
    <cge:TPSR_Ref TObjectID="2948"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-19285">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2666.000000 -4813.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2949" ObjectName="SW-CX_YR.CX_YR_3626SW"/>
     <cge:Meas_Ref ObjectId="19285"/>
    <cge:TPSR_Ref TObjectID="2949"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-139799">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2829.000000 -4663.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25062" ObjectName="SW-YR_WD.YR_WD_3311SW"/>
     <cge:Meas_Ref ObjectId="139799"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-139800">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2851.000000 -4706.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25063" ObjectName="SW-YR_WD.YR_WD_33117SW"/>
     <cge:Meas_Ref ObjectId="139800"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-171111">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2847.000000 -4252.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27250" ObjectName="SW-YM_WM.YM_WM_3716SW"/>
     <cge:Meas_Ref ObjectId="171111"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-171110">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2990.000000 -4252.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27249" ObjectName="SW-YM_WM.YM_WM_3711SW"/>
     <cge:Meas_Ref ObjectId="171110"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-19291">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2453.633028 -4808.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2955" ObjectName="SW-CX_YR.CX_YR_3646SW"/>
     <cge:Meas_Ref ObjectId="19291"/>
    <cge:TPSR_Ref TObjectID="2955"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-19290">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2453.633028 -4906.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2954" ObjectName="SW-CX_YR.CX_YR_3642SW"/>
     <cge:Meas_Ref ObjectId="19290"/>
    <cge:TPSR_Ref TObjectID="2954"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2453.633028 -4471.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2477.633028 -4516.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-19288">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2289.000000 -4807.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2952" ObjectName="SW-CX_YR.CX_YR_3636SW"/>
     <cge:Meas_Ref ObjectId="19288"/>
    <cge:TPSR_Ref TObjectID="2952"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-19287">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2289.000000 -4905.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2951" ObjectName="SW-CX_YR.CX_YR_3632SW"/>
     <cge:Meas_Ref ObjectId="19287"/>
    <cge:TPSR_Ref TObjectID="2951"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-141473">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2311.000000 -3918.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25266" ObjectName="SW-YR_LTS.YR_LTS_32367SW"/>
     <cge:Meas_Ref ObjectId="141473"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-141471">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2289.000000 -3788.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25264" ObjectName="SW-YR_LTS.YR_LTS_3231SW"/>
     <cge:Meas_Ref ObjectId="141471"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-141472">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2289.000000 -3873.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25265" ObjectName="SW-YR_LTS.YR_LTS_3236SW"/>
     <cge:Meas_Ref ObjectId="141472"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-138805">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1867.000000 -4925.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24967" ObjectName="SW-YR_ZH.YR_ZH_3916SW"/>
     <cge:Meas_Ref ObjectId="138805"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-138804">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1867.000000 -5016.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24966" ObjectName="SW-YR_ZH.YR_ZH_3911SW"/>
     <cge:Meas_Ref ObjectId="138804"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-138808">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1817.000000 -4906.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24970" ObjectName="SW-YR_ZH.YR_ZH_39167SW"/>
     <cge:Meas_Ref ObjectId="138808"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-138858">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2101.000000 -5011.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24972" ObjectName="SW-YR_ZH.YR_ZH_3921SW"/>
     <cge:Meas_Ref ObjectId="138858"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-138859">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2101.000000 -4913.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24973" ObjectName="SW-YR_ZH.YR_ZH_3926SW"/>
     <cge:Meas_Ref ObjectId="138859"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-140281">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1920.000000 -4640.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25120" ObjectName="SW-YR_TPL.YR_TPL_3811SW"/>
     <cge:Meas_Ref ObjectId="140281"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-140282">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1920.000000 -4558.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25121" ObjectName="SW-YR_TPL.YR_TPL_3816SW"/>
     <cge:Meas_Ref ObjectId="140282"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-140333">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1739.000000 -4634.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25125" ObjectName="SW-YR_TPL.YR_TPL_3821SW"/>
     <cge:Meas_Ref ObjectId="140333"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-140334">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1739.000000 -4536.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25126" ObjectName="SW-YR_TPL.YR_TPL_3826SW"/>
     <cge:Meas_Ref ObjectId="140334"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-140335">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1684.000000 -4502.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25127" ObjectName="SW-YR_TPL.YR_TPL_38267SW"/>
     <cge:Meas_Ref ObjectId="140335"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-141454">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1739.000000 -3786.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25261" ObjectName="SW-YR_LTS.YR_LTS_3221SW"/>
     <cge:Meas_Ref ObjectId="141454"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-141455">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1739.000000 -3873.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25262" ObjectName="SW-YR_LTS.YR_LTS_3226SW"/>
     <cge:Meas_Ref ObjectId="141455"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-140384">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1560.000000 -4636.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25129" ObjectName="SW-YR_TPL.YR_TPL_3831SW"/>
     <cge:Meas_Ref ObjectId="140384"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-140385">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1560.000000 -4538.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25130" ObjectName="SW-YR_TPL.YR_TPL_3836SW"/>
     <cge:Meas_Ref ObjectId="140385"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-140386">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1508.000000 -4524.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25131" ObjectName="SW-YR_TPL.YR_TPL_38367SW"/>
     <cge:Meas_Ref ObjectId="140386"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-106523">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1650.000000 -4289.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20759" ObjectName="SW-CX_HSY.CX_HSY_36197SW"/>
     <cge:Meas_Ref ObjectId="106523"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-106522">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1577.000000 -4336.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20758" ObjectName="SW-CX_HSY.CX_HSY_3619SW"/>
     <cge:Meas_Ref ObjectId="106522"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-106526">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1508.000000 -4327.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20762" ObjectName="SW-CX_HSY.CX_HSY_36167SW"/>
     <cge:Meas_Ref ObjectId="106526"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-106524">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1560.000000 -4282.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20760" ObjectName="SW-CX_HSY.CX_HSY_3616SW"/>
     <cge:Meas_Ref ObjectId="106524"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-106525">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1560.000000 -4183.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20761" ObjectName="SW-CX_HSY.CX_HSY_3611SW"/>
     <cge:Meas_Ref ObjectId="106525"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-106527">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1507.000000 -4232.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20763" ObjectName="SW-CX_HSY.CX_HSY_36117SW"/>
     <cge:Meas_Ref ObjectId="106527"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-28391">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1329.000000 -5127.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4596" ObjectName="SW-CX_WM.CX_WM_3741SW"/>
     <cge:Meas_Ref ObjectId="28391"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-28392">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1329.000000 -5029.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4597" ObjectName="SW-CX_WM.CX_WM_3746SW"/>
     <cge:Meas_Ref ObjectId="28392"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1347.000000 -4309.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1347.000000 -4211.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1277.000000 -4307.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1252.000000 -4247.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-193559">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1107.000000 -5127.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29440" ObjectName="SW-CX_WM.CX_WM_3721SW"/>
     <cge:Meas_Ref ObjectId="193559"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-193560">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1107.000000 -5029.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29441" ObjectName="SW-CX_WM.CX_WM_3726SW"/>
     <cge:Meas_Ref ObjectId="193560"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1130.000000 -4719.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1107.000000 -4670.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-183006">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 708.000000 -4335.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27849" ObjectName="SW-YR_YJ.YR_YJ_35167SW"/>
     <cge:Meas_Ref ObjectId="183006"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-141436">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1342.000000 -3789.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25257" ObjectName="SW-YR_LTS.YR_LTS_3211SW"/>
     <cge:Meas_Ref ObjectId="141436"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-141437">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1342.000000 -3876.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25258" ObjectName="SW-YR_LTS.YR_LTS_3216SW"/>
     <cge:Meas_Ref ObjectId="141437"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-141438">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1290.000000 -3927.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25259" ObjectName="SW-YR_LTS.YR_LTS_32167SW"/>
     <cge:Meas_Ref ObjectId="141438"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-138862">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2050.000000 -4902.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24976" ObjectName="SW-YR_ZH.YR_ZH_39267SW"/>
     <cge:Meas_Ref ObjectId="138862"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-28404">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1601.000000 -5036.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4600" ObjectName="SW-CX_WM.CX_WM_3756SW"/>
     <cge:Meas_Ref ObjectId="28404"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-28403">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1601.000000 -5134.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4599" ObjectName="SW-CX_WM.CX_WM_3751SW"/>
     <cge:Meas_Ref ObjectId="28403"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-141456">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1751.000000 -3922.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25263" ObjectName="SW-YR_LTS.YR_LTS_32267SW"/>
     <cge:Meas_Ref ObjectId="141456"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-55771">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 367.000000 -4298.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10147" ObjectName="SW-CX_RH.CX_RH_3111SW"/>
     <cge:Meas_Ref ObjectId="55771"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-55772">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 302.000000 -4392.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10148" ObjectName="SW-CX_RH.CX_RH_31167SW"/>
     <cge:Meas_Ref ObjectId="55772"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-61724">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2850.000000 -4515.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11813" ObjectName="SW-CX_DLTY.CX_DLTY_38167SW"/>
     <cge:Meas_Ref ObjectId="61724"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-61722">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2885.000000 -4501.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11812" ObjectName="SW-CX_DLTY.CX_DLTY_3816SW"/>
     <cge:Meas_Ref ObjectId="61722"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-83926">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1865.000000 -4006.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18534" ObjectName="SW-CX_TF.CX_TF_3716SW"/>
     <cge:Meas_Ref ObjectId="83926"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-83927">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1852.000000 -4013.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18535" ObjectName="SW-CX_TF.CX_TF_37167SW"/>
     <cge:Meas_Ref ObjectId="83927"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-116067">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2576.633028 -3542.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21913" ObjectName="SW-CX_FS.CX_FS_3651SW"/>
     <cge:Meas_Ref ObjectId="116067"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-116068">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2576.633028 -3633.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21914" ObjectName="SW-CX_FS.CX_FS_3656SW"/>
     <cge:Meas_Ref ObjectId="116068"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-116070">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2589.633028 -3695.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21916" ObjectName="SW-CX_FS.CX_FS_36567SW"/>
     <cge:Meas_Ref ObjectId="116070"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-125519">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3011.125382 -3537.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22985" ObjectName="SW-CX_FS.CX_FS_3671SW"/>
     <cge:Meas_Ref ObjectId="125519"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-125520">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2973.125382 -3634.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22984" ObjectName="SW-CX_FS.CX_FS_36767SW"/>
     <cge:Meas_Ref ObjectId="125520"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-125727">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2974.125382 -3964.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22999" ObjectName="SW-CX_BX.CX_BX_34167SW"/>
     <cge:Meas_Ref ObjectId="125727"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-125726">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3010.125382 -4035.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22998" ObjectName="SW-CX_BX.CX_BX_341XC"/>
     <cge:Meas_Ref ObjectId="125726"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-125726">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3010.125382 -3975.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23021" ObjectName="SW-CX_BX.CX_BX_341XC1"/>
     <cge:Meas_Ref ObjectId="125726"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-117465">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2767.379205 -3538.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21992" ObjectName="SW-CX_FS.CX_FS_3661SW"/>
     <cge:Meas_Ref ObjectId="117465"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-117466">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2767.379205 -3618.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21993" ObjectName="SW-CX_FS.CX_FS_3666SW"/>
     <cge:Meas_Ref ObjectId="117466"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-117468">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2729.379205 -3664.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21995" ObjectName="SW-CX_FS.CX_FS_36667SW"/>
     <cge:Meas_Ref ObjectId="117468"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 2811.379205 -3638.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2767.379205 -3939.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2781.379205 -3923.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1370.000000 -4932.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1582.000000 -4932.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1073.000000 -4944.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-146704">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 754.000000 -3560.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25683" ObjectName="SW-CX_LC.CX_LC_3121SW"/>
     <cge:Meas_Ref ObjectId="146704"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-146705">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 837.000000 -3558.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25684" ObjectName="SW-CX_LC.CX_LC_3122SW"/>
     <cge:Meas_Ref ObjectId="146705"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-146735">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 701.000000 -3675.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25687" ObjectName="SW-CX_LC.CX_LC_3523SW"/>
     <cge:Meas_Ref ObjectId="146735"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-146734">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 699.000000 -3581.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25686" ObjectName="SW-CX_LC.CX_LC_3521SW"/>
     <cge:Meas_Ref ObjectId="146734"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-146781">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 897.000000 -3672.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25695" ObjectName="SW-CX_LC.CX_LC_3543SW"/>
     <cge:Meas_Ref ObjectId="146781"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-146780">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 897.000000 -3583.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25694" ObjectName="SW-CX_LC.CX_LC_3542SW"/>
     <cge:Meas_Ref ObjectId="146780"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-138151">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.645570 860.572414 -4612.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24884" ObjectName="SW-YR_YJ.YR_YJ_3901SW"/>
     <cge:Meas_Ref ObjectId="138151"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-183096">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 684.000000 -4687.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27851" ObjectName="SW-YR_YJ.YR_YJ_3536SW"/>
     <cge:Meas_Ref ObjectId="183096"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-146736">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 701.000000 -3778.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25688" ObjectName="SW-CX_LC.CX_LC_3526SW"/>
     <cge:Meas_Ref ObjectId="146736"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-146782">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 897.000000 -3770.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25696" ObjectName="SW-CX_LC.CX_LC_3546SW"/>
     <cge:Meas_Ref ObjectId="146782"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-186630">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2885.000000 -4384.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28265" ObjectName="SW-CX_TG.CX_TG_3516SW"/>
     <cge:Meas_Ref ObjectId="186630"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-28379">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 941.000000 -5125.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4593" ObjectName="SW-CX_WM.CX_WM_3731SW"/>
     <cge:Meas_Ref ObjectId="28379"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-28380">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 941.000000 -5027.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4594" ObjectName="SW-CX_WM.CX_WM_3736SW"/>
     <cge:Meas_Ref ObjectId="28380"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-140980">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 352.000000 -4045.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25193" ObjectName="SW-YR_BB.YR_BB_3416SW"/>
     <cge:Meas_Ref ObjectId="140980"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-140979">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 246.000000 -4045.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25192" ObjectName="SW-YR_BB.YR_BB_3411SW"/>
     <cge:Meas_Ref ObjectId="140979"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-171113">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2787.000000 -4270.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27252" ObjectName="SW-YM_WM.YM_WM_37167SW"/>
     <cge:Meas_Ref ObjectId="171113"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-194234">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 295.000000 -4735.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29496" ObjectName="SW-DY_WB.DY_WB_3936SW"/>
     <cge:Meas_Ref ObjectId="194234"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-194233">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 458.000000 -4735.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29495" ObjectName="SW-DY_WB.DY_WB_3931SW"/>
     <cge:Meas_Ref ObjectId="194233"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-194237">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 428.000000 -4678.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29499" ObjectName="SW-DY_WB.DY_WB_39317SW"/>
     <cge:Meas_Ref ObjectId="194237"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-194236">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 350.000000 -4674.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29498" ObjectName="SW-DY_WB.DY_WB_39360SW"/>
     <cge:Meas_Ref ObjectId="194236"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-194235">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 252.000000 -4674.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29497" ObjectName="SW-DY_WB.DY_WB_39367SW"/>
     <cge:Meas_Ref ObjectId="194235"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 318.203031 -4822.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-140981">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 409.000000 -3997.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25194" ObjectName="SW-YR_BB.YR_BB_34167SW"/>
     <cge:Meas_Ref ObjectId="140981"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-226823">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1771.000000 -4295.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37593" ObjectName="SW-YR_MH.YR_MH_36167SW"/>
     <cge:Meas_Ref ObjectId="226823"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-226861">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2032.000000 -4326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37598" ObjectName="SW-YR_MH.YR_MH_3646SW"/>
     <cge:Meas_Ref ObjectId="226861"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-226860">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1950.000000 -4326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37600" ObjectName="SW-YR_MH.YR_MH_3641SW"/>
     <cge:Meas_Ref ObjectId="226860"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-226898">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1959.000000 -4372.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37601" ObjectName="SW-YR_MH.YR_MH_3621SW"/>
     <cge:Meas_Ref ObjectId="226898"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-226862">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2076.000000 -4277.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37599" ObjectName="SW-YR_MH.YR_MH_36467SW"/>
     <cge:Meas_Ref ObjectId="226862"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-226822">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1789.000000 -4344.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37594" ObjectName="SW-YR_MH.YR_MH_3616SW"/>
     <cge:Meas_Ref ObjectId="226822"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-139439">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1890.000000 -4344.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25022" ObjectName="SW-YR_MH.YR_MH_3611SW"/>
     <cge:Meas_Ref ObjectId="139439"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-226824">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1739.500000 -4279.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37592" ObjectName="SW-YR_MH.YR_MH_3619SW"/>
     <cge:Meas_Ref ObjectId="226824"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-226863">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2140.500000 -4278.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37597" ObjectName="SW-YR_MH.YR_MH_3649SW"/>
     <cge:Meas_Ref ObjectId="226863"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="0" id="BS-CX_YR.CX_YR_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="2583,-4966 2910,-4966 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="2891" ObjectName="BS-CX_YR.CX_YR_3IM"/>
    <cge:TPSR_Ref TObjectID="2891"/></metadata>
   <polyline fill="none" opacity="0" points="2583,-4966 2910,-4966 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_YR.CX_YR_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="2534,-4966 2238,-4966 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3021" ObjectName="BS-CX_YR.CX_YR_3IIM"/>
    <cge:TPSR_Ref TObjectID="3021"/></metadata>
   <polyline fill="none" opacity="0" points="2534,-4966 2238,-4966 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-YR_WD.YR_WD_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="2800,-4658 2874,-4658 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25059" ObjectName="BS-YR_WD.YR_WD_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2800,-4658 2874,-4658 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-YR_BB.YR_BB_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="214,-4004 214,-4096 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25188" ObjectName="BS-YR_BB.YR_BB_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="214,-4004 214,-4096 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-YR_LTS.YR_LTS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1306,-3781 2366,-3781 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25315" ObjectName="BS-YR_LTS.YR_LTS_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1306,-3781 2366,-3781 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="0" fill="none" points="2402,-4424 2519,-4424 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2402,-4424 2519,-4424 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-YR_ZH.YR_ZH_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="2158,-5070 1829,-5070 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24962" ObjectName="BS-YR_ZH.YR_ZH_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2158,-5070 1829,-5070 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_WM.CX_WM_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1699,-5190 911,-5190 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4549" ObjectName="BS-CX_WM.CX_WM_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1699,-5190 911,-5190 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-YR_TPL.YR_TPL_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1520,-4690 1974,-4690 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25116" ObjectName="BS-YR_TPL.YR_TPL_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1520,-4690 1974,-4690 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_HSY.CX_HSY_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1536,-4177 1602,-4177 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20744" ObjectName="BS-CX_HSY.CX_HSY_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1536,-4177 1602,-4177 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="0" fill="none" points="1388,-4363 1269,-4363 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1388,-4363 1269,-4363 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="0" fill="none" points="1063,-4643 1163,-4643 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1063,-4643 1163,-4643 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-YR_YJ.YR_YJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="670,-4594 902,-4594 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24878" ObjectName="BS-YR_YJ.YR_YJ_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="670,-4594 902,-4594 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_DLTY.CX_DLTY_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="3050,-4539 3050,-4468 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11821" ObjectName="BS-CX_DLTY.CX_DLTY_3IIM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3050,-4539 3050,-4468 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_TF.CX_TF_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="2038,-4046 2038,-3978 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18519" ObjectName="BS-CX_TF.CX_TF_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2038,-4046 2038,-3978 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_FS.CX_FS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="2512,-3530 3106,-3530 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="21923" ObjectName="BS-CX_FS.CX_FS_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2512,-3530 3106,-3530 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_BX.CX_BX_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="2963,-4068 3077,-4068 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22996" ObjectName="BS-CX_BX.CX_BX_3ⅠM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2963,-4068 3077,-4068 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_LC.CX_LC_3ⅡM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="809,-3569 928,-3569 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25627" ObjectName="BS-CX_LC.CX_LC_3ⅡM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="809,-3569 928,-3569 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_LC.CX_LC_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="648,-3569 774,-3569 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25626" ObjectName="BS-CX_LC.CX_LC_3ⅠM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="648,-3569 774,-3569 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-YM_WM.YM_WM_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="3050,-4301 3050,-4222 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27244" ObjectName="BS-YM_WM.YM_WM_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3050,-4301 3050,-4222 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_TG.CX_TG_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="3050,-4422 3050,-4355 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28262" ObjectName="BS-CX_TG.CX_TG_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3050,-4422 3050,-4355 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-DY_WB.DY_WB_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="528,-4680 528,-4794 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27684" ObjectName="BS-DY_WB.DY_WB_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="528,-4680 528,-4794 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-YR_MH.YR_MH_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1945,-4384 1945,-4316 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25019" ObjectName="BS-YR_MH.YR_MH_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1945,-4384 1945,-4316 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="0" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 536.000000 -4500.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="0" id="g_3d474e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 2899.000000 -4702.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_42bb1e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 2524.633028 -4512.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_32b8fc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 2358.000000 -3914.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_42b0a40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 1785.000000 -4902.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d66be0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 1652.000000 -4498.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d7ad20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 1476.000000 -4520.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d7e470" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1649.000000 -4256.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d87220" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 1476.000000 -4323.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d91bb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 1475.000000 -4228.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3da88b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1251.000000 -4213.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3db6a40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1184.000000 -4715.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3dc6560" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 1254.000000 -3923.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3dcc760" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 2018.000000 -4898.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ddd700" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1797.000000 -3918.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e004c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1855.000000 -4058.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e07600" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 274.000000 -4391.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e13a00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2633.633028 -3694.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e26930" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2953.125382 -3633.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e2d9c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2954.125382 -3963.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e49b90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2709.379205 -3663.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e5ae00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2824.379205 -3922.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e99420" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 835.500000 -4799.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e9ed20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.347826 -0.000000 0.000000 -1.583333 651.000000 -4683.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ea4b70" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 714.500000 -4805.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3eb5ee0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2841.000000 -4561.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3eded80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 760.000000 -4331.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3eea1b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2778.000000 -4316.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ef8db0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 431.000000 -4655.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3efc000" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 353.000000 -4651.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3eff970" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 255.000000 -4651.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3f2a5e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 412.000000 -3972.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3f451f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1774.000000 -4270.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3f523e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2079.000000 -4252.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_3cad8d0">
     <polyline DF8003:Layer="0" fill="none" points="2838,-4966 2838,-4951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2891@0" ObjectIDZND0="2945@1" Pin0InfoVect0LinkObjId="SW-19281_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_83fc2b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2838,-4966 2838,-4951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_83fc050">
     <polyline DF8003:Layer="0" fill="none" points="2581,-5025 2611,-5025 2611,-5013 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2959@0" ObjectIDZND0="2958@1" Pin0InfoVect0LinkObjId="SW-19294_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19295_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2581,-5025 2611,-5025 2611,-5013 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_83fc2b0">
     <polyline DF8003:Layer="0" fill="none" points="2611,-4977 2611,-4966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2958@0" ObjectIDZND0="2891@0" Pin0InfoVect0LinkObjId="g_3d40400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19294_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2611,-4977 2611,-4966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d40400">
     <polyline DF8003:Layer="0" fill="none" points="2675,-4952 2675,-4966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2948@1" ObjectIDZND0="2891@0" Pin0InfoVect0LinkObjId="g_83fc2b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19284_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2675,-4952 2675,-4966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d40660">
     <polyline DF8003:Layer="0" fill="none" points="2675,-4854 2675,-4870 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2949@1" ObjectIDZND0="2950@0" Pin0InfoVect0LinkObjId="SW-19286_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19285_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2675,-4854 2675,-4870 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d408c0">
     <polyline DF8003:Layer="0" fill="none" points="2675,-4897 2675,-4916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2950@1" ObjectIDZND0="2948@0" Pin0InfoVect0LinkObjId="SW-19284_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19286_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2675,-4897 2675,-4916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d41d60">
     <polyline DF8003:Layer="0" fill="none" points="2789,-4711 2780,-4711 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3d46540@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d46540_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2789,-4711 2780,-4711 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d46dc0">
     <polyline DF8003:Layer="0" fill="none" points="2838,-4902 2838,-4915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2947@1" ObjectIDZND0="2945@0" Pin0InfoVect0LinkObjId="SW-19281_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19283_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2838,-4902 2838,-4915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d47020">
     <polyline DF8003:Layer="0" fill="none" points="2838,-4861 2838,-4875 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2946@1" ObjectIDZND0="2947@0" Pin0InfoVect0LinkObjId="SW-19283_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19282_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2838,-4861 2838,-4875 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d47280">
     <polyline DF8003:Layer="0" fill="none" points="2892,-4711 2906,-4711 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25063@1" ObjectIDZND0="g_3d474e0@0" Pin0InfoVect0LinkObjId="g_3d474e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139800_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2892,-4711 2906,-4711 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d47f70">
     <polyline DF8003:Layer="0" fill="none" points="2838,-4658 2838,-4668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25059@0" ObjectIDZND0="25062@0" Pin0InfoVect0LinkObjId="SW-139799_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2838,-4658 2838,-4668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_52ab280">
     <polyline DF8003:Layer="0" fill="none" points="2512,-4966 2512,-4979 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3021@0" ObjectIDZND0="2957@0" Pin0InfoVect0LinkObjId="SW-19293_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_42bc130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2512,-4966 2512,-4979 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_52ab4e0">
     <polyline DF8003:Layer="0" fill="none" points="2512,-5015 2512,-5025 2554,-5025 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2957@1" ObjectIDZND0="2959@1" Pin0InfoVect0LinkObjId="SW-19295_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19293_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2512,-5015 2512,-5025 2554,-5025 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_42b85f0">
     <polyline DF8003:Layer="0" fill="none" points="2463,-4424 2463,-4437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2463,-4424 2463,-4437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_42b8850">
     <polyline DF8003:Layer="0" fill="none" points="2463,-4464 2463,-4476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2463,-4464 2463,-4476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_42baf80">
     <polyline DF8003:Layer="0" fill="none" points="2519,-4521 2532,-4521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_42bb1e0@0" Pin0InfoVect0LinkObjId="g_42bb1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2519,-4521 2532,-4521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42bc130">
     <polyline DF8003:Layer="0" fill="none" points="2463,-4947 2463,-4966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2954@1" ObjectIDZND0="3021@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19290_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2463,-4947 2463,-4966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42bc390">
     <polyline DF8003:Layer="0" fill="none" points="2463,-4849 2463,-4869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2955@1" ObjectIDZND0="2956@0" Pin0InfoVect0LinkObjId="SW-19292_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19291_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2463,-4849 2463,-4869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42bc5f0">
     <polyline DF8003:Layer="0" fill="none" points="2463,-4896 2463,-4911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2956@1" ObjectIDZND0="2954@0" Pin0InfoVect0LinkObjId="SW-19290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19292_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2463,-4896 2463,-4911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4231f30">
     <polyline DF8003:Layer="0" fill="none" points="2298,-4966 2298,-4946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3021@0" ObjectIDZND0="2951@1" Pin0InfoVect0LinkObjId="SW-19287_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_42bc130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2298,-4966 2298,-4946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4232190">
     <polyline DF8003:Layer="0" fill="none" points="2298,-4910 2298,-4895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2951@0" ObjectIDZND0="2953@1" Pin0InfoVect0LinkObjId="SW-19289_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19287_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2298,-4910 2298,-4895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42323f0">
     <polyline DF8003:Layer="0" fill="none" points="2298,-4868 2298,-4848 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2953@0" ObjectIDZND0="2952@1" Pin0InfoVect0LinkObjId="SW-19288_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19289_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2298,-4868 2298,-4848 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4233610">
     <polyline DF8003:Layer="0" fill="none" points="2269,-4725 2269,-4714 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_4232d90@0" ObjectIDZND0="g_4232650@0" Pin0InfoVect0LinkObjId="g_4232650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4232d90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2269,-4725 2269,-4714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4233fb0">
     <polyline DF8003:Layer="0" fill="none" points="2342,-3969 2353,-3969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_4234210@0" ObjectIDZND0="g_4233870@0" Pin0InfoVect0LinkObjId="g_4233870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4234210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2342,-3969 2353,-3969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b8d60">
     <polyline DF8003:Layer="0" fill="none" points="2352,-3923 2359,-3923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25266@1" ObjectIDZND0="g_32b8fc0@0" Pin0InfoVect0LinkObjId="g_32b8fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141473_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2352,-3923 2359,-3923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42a3a50">
     <polyline DF8003:Layer="0" fill="none" points="2298,-3793 2298,-3781 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25264@0" ObjectIDZND0="25315@0" Pin0InfoVect0LinkObjId="g_3d713e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141471_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2298,-3793 2298,-3781 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42a3cb0">
     <polyline DF8003:Layer="0" fill="none" points="2298,-3840 2298,-3829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25267@0" ObjectIDZND0="25264@1" Pin0InfoVect0LinkObjId="SW-141471_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141476_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2298,-3840 2298,-3829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42a3f10">
     <polyline DF8003:Layer="0" fill="none" points="2298,-3878 2298,-3867 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25265@0" ObjectIDZND0="25267@1" Pin0InfoVect0LinkObjId="SW-141476_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141472_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2298,-3878 2298,-3867 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42a61c0">
     <polyline DF8003:Layer="0" fill="none" points="1610,-5097 1610,-5077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4601@0" ObjectIDZND0="4600@1" Pin0InfoVect0LinkObjId="SW-28404_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28405_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1610,-5097 1610,-5077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42a6420">
     <polyline DF8003:Layer="0" fill="none" points="1610,-5190 1610,-5175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4549@0" ObjectIDZND0="4599@1" Pin0InfoVect0LinkObjId="SW-28403_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1610,-5190 1610,-5175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42a6680">
     <polyline DF8003:Layer="0" fill="none" points="1610,-5139 1610,-5124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4599@0" ObjectIDZND0="4601@1" Pin0InfoVect0LinkObjId="SW-28405_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28403_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1610,-5139 1610,-5124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42ad930">
     <polyline DF8003:Layer="0" fill="none" points="1876,-5057 1876,-5070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24966@1" ObjectIDZND0="24962@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138804_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1876,-5057 1876,-5070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42adb90">
     <polyline DF8003:Layer="0" fill="none" points="1876,-4966 1876,-4979 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24967@1" ObjectIDZND0="24965@0" Pin0InfoVect0LinkObjId="SW-138802_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138805_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1876,-4966 1876,-4979 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42addf0">
     <polyline DF8003:Layer="0" fill="none" points="1876,-5006 1876,-5021 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24965@1" ObjectIDZND0="24966@0" Pin0InfoVect0LinkObjId="SW-138804_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138802_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1876,-5006 1876,-5021 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42b0580">
     <polyline DF8003:Layer="0" fill="none" points="1876,-4911 1858,-4911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="24967@x" ObjectIDND1="g_42b14d0@0" ObjectIDND2="0@x" ObjectIDZND0="24970@1" Pin0InfoVect0LinkObjId="SW-138808_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-138805_0" Pin1InfoVect1LinkObjId="g_42b14d0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1876,-4911 1858,-4911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42b07e0">
     <polyline DF8003:Layer="0" fill="none" points="1822,-4911 1808,-4911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24970@0" ObjectIDZND0="g_42b0a40@0" Pin0InfoVect0LinkObjId="g_42b0a40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138808_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1822,-4911 1808,-4911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42b2280">
     <polyline DF8003:Layer="0" fill="none" points="1860,-4872 1876,-4872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_42b14d0@0" ObjectIDZND0="24970@x" ObjectIDZND1="24967@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-138808_0" Pin0InfoVect1LinkObjId="SW-138805_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_42b14d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1860,-4872 1876,-4872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d4cc40">
     <polyline DF8003:Layer="0" fill="none" points="1920,-4872 1931,-4872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3d4cea0@0" ObjectIDZND0="g_42b24e0@0" Pin0InfoVect0LinkObjId="g_42b24e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d4cea0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1920,-4872 1931,-4872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d4d720">
     <polyline DF8003:Layer="0" fill="none" points="1876,-4872 1889,-4872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_42b14d0@0" ObjectIDND1="24970@x" ObjectIDND2="24967@x" ObjectIDZND0="g_3d4cea0@1" Pin0InfoVect0LinkObjId="g_3d4cea0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_42b14d0_0" Pin1InfoVect1LinkObjId="SW-138808_0" Pin1InfoVect2LinkObjId="SW-138805_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1876,-4872 1889,-4872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d54c30">
     <polyline DF8003:Layer="0" fill="none" points="2110,-4974 2110,-4954 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24971@0" ObjectIDZND0="24973@1" Pin0InfoVect0LinkObjId="SW-138859_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138856_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2110,-4974 2110,-4954 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d54e90">
     <polyline DF8003:Layer="0" fill="none" points="2110,-5070 2110,-5052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24962@0" ObjectIDZND0="24972@1" Pin0InfoVect0LinkObjId="SW-138858_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_42ad930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2110,-5070 2110,-5052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d550f0">
     <polyline DF8003:Layer="0" fill="none" points="2110,-5016 2110,-5001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24972@0" ObjectIDZND0="24971@1" Pin0InfoVect0LinkObjId="SW-138856_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138858_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2110,-5016 2110,-5001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d55a90">
     <polyline DF8003:Layer="0" fill="none" points="1970,-4549 1981,-4549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3d55cf0@0" ObjectIDZND0="g_3d55350@0" Pin0InfoVect0LinkObjId="g_3d55350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d55cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1970,-4549 1981,-4549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d66980">
     <polyline DF8003:Layer="0" fill="none" points="1690,-4507 1675,-4507 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25127@0" ObjectIDZND0="g_3d66be0@0" Pin0InfoVect0LinkObjId="g_3d66be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140335_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1690,-4507 1675,-4507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d69c70">
     <polyline DF8003:Layer="0" fill="none" points="1737,-3944 1748,-3944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3d68ec0@0" ObjectIDZND0="25263@x" ObjectIDZND1="25262@x" ObjectIDZND2="g_3d67670@0" Pin0InfoVect0LinkObjId="SW-141456_0" Pin0InfoVect1LinkObjId="SW-141455_0" Pin0InfoVect2LinkObjId="g_3d67670_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d68ec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1737,-3944 1748,-3944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d70f20">
     <polyline DF8003:Layer="0" fill="none" points="1748,-3878 1748,-3866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25262@0" ObjectIDZND0="25260@1" Pin0InfoVect0LinkObjId="SW-141453_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141455_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1748,-3878 1748,-3866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d71180">
     <polyline DF8003:Layer="0" fill="none" points="1748,-3839 1748,-3827 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25260@0" ObjectIDZND0="25261@1" Pin0InfoVect0LinkObjId="SW-141454_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141453_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1748,-3839 1748,-3827 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d713e0">
     <polyline DF8003:Layer="0" fill="none" points="1748,-3791 1748,-3781 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25261@0" ObjectIDZND0="25315@0" Pin0InfoVect0LinkObjId="g_42a3a50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141454_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1748,-3791 1748,-3781 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d7aac0">
     <polyline DF8003:Layer="0" fill="none" points="1513,-4529 1499,-4529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25131@0" ObjectIDZND0="g_3d7ad20@0" Pin0InfoVect0LinkObjId="g_3d7ad20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140386_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1513,-4529 1499,-4529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d7e210">
     <polyline DF8003:Layer="0" fill="none" points="1659,-4294 1659,-4282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20759@0" ObjectIDZND0="g_3d7e470@0" Pin0InfoVect0LinkObjId="g_3d7e470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106523_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1659,-4294 1659,-4282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d813f0">
     <polyline DF8003:Layer="0" fill="none" points="1569,-4341 1582,-4341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="20762@x" ObjectIDND1="20760@x" ObjectIDND2="g_3d83820@0" ObjectIDZND0="20758@0" Pin0InfoVect0LinkObjId="SW-106522_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-106526_0" Pin1InfoVect1LinkObjId="SW-106524_0" Pin1InfoVect2LinkObjId="g_3d83820_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1569,-4341 1582,-4341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d82610">
     <polyline DF8003:Layer="0" fill="none" points="1628,-4341 1628,-4328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="20758@x" ObjectIDND1="20759@x" ObjectIDZND0="g_3d81d90@1" Pin0InfoVect0LinkObjId="g_3d81d90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106522_0" Pin1InfoVect1LinkObjId="SW-106523_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1628,-4341 1628,-4328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d82870">
     <polyline DF8003:Layer="0" fill="none" points="1628,-4297 1628,-4286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3d81d90@0" ObjectIDZND0="g_3d81650@0" Pin0InfoVect0LinkObjId="g_3d81650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d81d90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1628,-4297 1628,-4286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d83360">
     <polyline DF8003:Layer="0" fill="none" points="1618,-4341 1628,-4341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="20758@1" ObjectIDZND0="g_3d81d90@0" ObjectIDZND1="20759@x" Pin0InfoVect0LinkObjId="g_3d81d90_0" Pin0InfoVect1LinkObjId="SW-106523_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106522_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1618,-4341 1628,-4341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d835c0">
     <polyline DF8003:Layer="0" fill="none" points="1628,-4341 1659,-4341 1659,-4330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3d81d90@0" ObjectIDND1="20758@x" ObjectIDZND0="20759@1" Pin0InfoVect0LinkObjId="SW-106523_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3d81d90_0" Pin1InfoVect1LinkObjId="SW-106522_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1628,-4341 1659,-4341 1659,-4330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d845d0">
     <polyline DF8003:Layer="0" fill="none" points="1549,-4355 1569,-4355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3d83820@0" ObjectIDZND0="25131@x" ObjectIDZND1="25130@x" ObjectIDZND2="g_3d9a010@0" Pin0InfoVect0LinkObjId="SW-140386_0" Pin0InfoVect1LinkObjId="SW-140385_0" Pin0InfoVect2LinkObjId="g_3d9a010_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d83820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1549,-4355 1569,-4355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d86d60">
     <polyline DF8003:Layer="0" fill="none" points="1569,-4332 1549,-4332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="20760@x" ObjectIDND1="20758@x" ObjectIDND2="g_3d83820@0" ObjectIDZND0="20762@1" Pin0InfoVect0LinkObjId="SW-106526_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-106524_0" Pin1InfoVect1LinkObjId="SW-106522_0" Pin1InfoVect2LinkObjId="g_3d83820_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1569,-4332 1549,-4332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d86fc0">
     <polyline DF8003:Layer="0" fill="none" points="1513,-4332 1499,-4332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20762@0" ObjectIDZND0="g_3d87220@0" Pin0InfoVect0LinkObjId="g_3d87220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106526_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1513,-4332 1499,-4332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d8c500">
     <polyline DF8003:Layer="0" fill="none" points="1569,-4287 1569,-4274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20760@0" ObjectIDZND0="20754@1" Pin0InfoVect0LinkObjId="SW-106518_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106524_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1569,-4287 1569,-4274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d8ef60">
     <polyline DF8003:Layer="0" fill="none" points="1569,-4188 1569,-4177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20761@0" ObjectIDZND0="20744@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106525_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1569,-4188 1569,-4177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d916f0">
     <polyline DF8003:Layer="0" fill="none" points="1569,-4237 1548,-4237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="20761@x" ObjectIDND1="20754@x" ObjectIDZND0="20763@1" Pin0InfoVect0LinkObjId="SW-106527_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106525_0" Pin1InfoVect1LinkObjId="SW-106518_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1569,-4237 1548,-4237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d91950">
     <polyline DF8003:Layer="0" fill="none" points="1512,-4237 1498,-4237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20763@0" ObjectIDZND0="g_3d91bb0@0" Pin0InfoVect0LinkObjId="g_3d91bb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106527_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1512,-4237 1498,-4237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d99690">
     <polyline DF8003:Layer="0" fill="none" points="1338,-5090 1338,-5070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4598@0" ObjectIDZND0="4597@1" Pin0InfoVect0LinkObjId="SW-28392_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28393_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1338,-5090 1338,-5070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d998f0">
     <polyline DF8003:Layer="0" fill="none" points="1338,-5190 1338,-5168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4549@0" ObjectIDZND0="4596@1" Pin0InfoVect0LinkObjId="SW-28391_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1338,-5190 1338,-5168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d99b50">
     <polyline DF8003:Layer="0" fill="none" points="1338,-5132 1338,-5117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4596@0" ObjectIDZND0="4598@1" Pin0InfoVect0LinkObjId="SW-28393_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28391_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1338,-5132 1338,-5117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d99db0">
     <polyline DF8003:Layer="0" fill="none" points="1393,-5020 1418,-5020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3d9a010@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d9a010_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1393,-5020 1418,-5020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3da19c0">
     <polyline DF8003:Layer="0" fill="none" points="1356,-4363 1356,-4350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1356,-4363 1356,-4350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3da1c20">
     <polyline DF8003:Layer="0" fill="none" points="1356,-4314 1356,-4299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1356,-4314 1356,-4299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3da1e80">
     <polyline DF8003:Layer="0" fill="none" points="1356,-4272 1356,-4252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1356,-4272 1356,-4252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3da20e0">
     <polyline DF8003:Layer="0" fill="none" points="1356,-4216 1356,-4203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3da2340@0" Pin0InfoVect0LinkObjId="g_3da2340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1356,-4216 1356,-4203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3da53c0">
     <polyline DF8003:Layer="0" fill="none" points="1286,-4363 1286,-4348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1286,-4363 1286,-4348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3da5eb0">
     <polyline DF8003:Layer="0" fill="none" points="1286,-4312 1286,-4300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_3da97c0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_3da97c0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1286,-4312 1286,-4300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3da9300">
     <polyline DF8003:Layer="0" fill="none" points="1286,-4300 1261,-4300 1261,-4288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3da97c0@0" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3da97c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1286,-4300 1261,-4300 1261,-4288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3da9560">
     <polyline DF8003:Layer="0" fill="none" points="1261,-4252 1261,-4239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_3da88b0@0" Pin0InfoVect0LinkObjId="g_3da88b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1261,-4252 1261,-4239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3daa780">
     <polyline DF8003:Layer="0" fill="none" points="1286,-4300 1286,-4285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_3da97c0@1" Pin0InfoVect0LinkObjId="g_3da97c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1286,-4300 1286,-4285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3daa9e0">
     <polyline DF8003:Layer="0" fill="none" points="1286,-4254 1286,-4244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3da97c0@0" ObjectIDZND0="g_3daa040@0" Pin0InfoVect0LinkObjId="g_3daa040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3da97c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1286,-4254 1286,-4244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3db1e90">
     <polyline DF8003:Layer="0" fill="none" points="1116,-5090 1116,-5070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29442@0" ObjectIDZND0="29441@1" Pin0InfoVect0LinkObjId="SW-193560_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193562_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1116,-5090 1116,-5070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3db20f0">
     <polyline DF8003:Layer="0" fill="none" points="1116,-5190 1116,-5168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4549@0" ObjectIDZND0="29440@1" Pin0InfoVect0LinkObjId="SW-193559_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1116,-5190 1116,-5168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3db2350">
     <polyline DF8003:Layer="0" fill="none" points="1116,-5132 1116,-5117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29440@0" ObjectIDZND0="29442@1" Pin0InfoVect0LinkObjId="SW-193562_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193559_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1116,-5132 1116,-5117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3db25b0">
     <polyline DF8003:Layer="0" fill="none" points="1116,-5011 1134,-5011 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="29441@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_3db2810@0" Pin0InfoVect0LinkObjId="g_3db2810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193560_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1116,-5011 1134,-5011 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3db3e50">
     <polyline DF8003:Layer="0" fill="none" points="1116,-5034 1116,-5010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="29441@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1116,-5034 1116,-5010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3db6580">
     <polyline DF8003:Layer="0" fill="none" points="1116,-4724 1135,-4724 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="29441@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-193560_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1116,-4724 1135,-4724 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3db67e0">
     <polyline DF8003:Layer="0" fill="none" points="1171,-4724 1191,-4724 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_3db6a40@0" Pin0InfoVect0LinkObjId="g_3db6a40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1171,-4724 1191,-4724 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3db9c70">
     <polyline DF8003:Layer="0" fill="none" points="1116,-4675 1116,-4643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1116,-4675 1116,-4643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dc36b0">
     <polyline DF8003:Layer="0" fill="none" points="1351,-3869 1351,-3881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25256@1" ObjectIDZND0="25258@0" Pin0InfoVect0LinkObjId="SW-141437_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141435_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-3869 1351,-3881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dc3910">
     <polyline DF8003:Layer="0" fill="none" points="1351,-3781 1351,-3794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25315@0" ObjectIDZND0="25257@0" Pin0InfoVect0LinkObjId="SW-141436_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_42a3a50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-3781 1351,-3794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dc3b70">
     <polyline DF8003:Layer="0" fill="none" points="1351,-3830 1351,-3842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25257@1" ObjectIDZND0="25256@0" Pin0InfoVect0LinkObjId="SW-141435_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141436_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-3830 1351,-3842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dc6300">
     <polyline DF8003:Layer="0" fill="none" points="1291,-3932 1277,-3932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25259@0" ObjectIDZND0="g_3dc6560@0" Pin0InfoVect0LinkObjId="g_3dc6560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141438_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1291,-3932 1277,-3932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dc8d60">
     <polyline DF8003:Layer="0" fill="none" points="737,-4315 748,-4315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3dc7da0@0" ObjectIDZND0="g_3dc8620@0" Pin0InfoVect0LinkObjId="g_3dc8620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3dc7da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="737,-4315 748,-4315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dc9d70">
     <polyline DF8003:Layer="0" fill="none" points="214,-4050 251,-4050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25188@0" ObjectIDZND0="25192@0" Pin0InfoVect0LinkObjId="SW-140979_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="214,-4050 251,-4050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dcc500">
     <polyline DF8003:Layer="0" fill="none" points="2110,-4907 2091,-4907 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="24973@x" ObjectIDND1="g_3dcd910@0" ObjectIDND2="g_3d55cf0@0" ObjectIDZND0="24976@1" Pin0InfoVect0LinkObjId="SW-138862_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-138859_0" Pin1InfoVect1LinkObjId="g_3dcd910_0" Pin1InfoVect2LinkObjId="g_3d55cf0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2110,-4907 2091,-4907 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dcd1f0">
     <polyline DF8003:Layer="0" fill="none" points="2055,-4907 2041,-4907 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24976@0" ObjectIDZND0="g_3dcc760@0" Pin0InfoVect0LinkObjId="g_3dcc760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138862_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2055,-4907 2041,-4907 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dcd450">
     <polyline DF8003:Layer="0" fill="none" points="2110,-4918 2110,-4907 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24973@0" ObjectIDZND0="g_3dcd910@0" ObjectIDZND1="g_3d55cf0@0" ObjectIDZND2="g_3d55cf0@0" Pin0InfoVect0LinkObjId="g_3dcd910_0" Pin0InfoVect1LinkObjId="g_3d55cf0_0" Pin0InfoVect2LinkObjId="g_3d55cf0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138859_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2110,-4918 2110,-4907 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dcd6b0">
     <polyline DF8003:Layer="0" fill="none" points="2110,-4876 2127,-4876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="24973@x" ObjectIDND1="24976@x" ObjectIDND2="g_3d55cf0@0" ObjectIDZND0="g_3dcd910@0" Pin0InfoVect0LinkObjId="g_3dcd910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-138859_0" Pin1InfoVect1LinkObjId="SW-138862_0" Pin1InfoVect2LinkObjId="g_3d55cf0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2110,-4876 2127,-4876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dce680">
     <polyline DF8003:Layer="0" fill="none" points="2110,-4907 2110,-4876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24973@x" ObjectIDND1="24976@x" ObjectIDZND0="g_3dcd910@0" ObjectIDZND1="g_3d55cf0@0" ObjectIDZND2="g_3d55cf0@0" Pin0InfoVect0LinkObjId="g_3dcd910_0" Pin0InfoVect1LinkObjId="g_3d55cf0_0" Pin0InfoVect2LinkObjId="g_3d55cf0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-138859_0" Pin1InfoVect1LinkObjId="SW-138862_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2110,-4907 2110,-4876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ddce80">
     <polyline DF8003:Layer="0" fill="none" points="1748,-3944 1748,-3927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3d67670@0" ObjectIDND1="g_3d68ec0@0" ObjectIDZND0="25263@x" ObjectIDZND1="25262@x" Pin0InfoVect0LinkObjId="SW-141456_0" Pin0InfoVect1LinkObjId="SW-141455_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3d67670_0" Pin1InfoVect1LinkObjId="g_3d68ec0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1748,-3944 1748,-3927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ddd070">
     <polyline DF8003:Layer="0" fill="none" points="1748,-3927 1748,-3914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3d67670@0" ObjectIDND1="g_3d68ec0@0" ObjectIDND2="25263@x" ObjectIDZND0="25262@1" Pin0InfoVect0LinkObjId="SW-141455_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d67670_0" Pin1InfoVect1LinkObjId="g_3d68ec0_0" Pin1InfoVect2LinkObjId="SW-141456_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1748,-3927 1748,-3914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ddd2a0">
     <polyline DF8003:Layer="0" fill="none" points="1748,-3927 1756,-3927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3d67670@0" ObjectIDND1="g_3d68ec0@0" ObjectIDND2="25262@x" ObjectIDZND0="25263@0" Pin0InfoVect0LinkObjId="SW-141456_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d67670_0" Pin1InfoVect1LinkObjId="g_3d68ec0_0" Pin1InfoVect2LinkObjId="SW-141455_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1748,-3927 1756,-3927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ddd4d0">
     <polyline DF8003:Layer="0" fill="none" points="1792,-3927 1804,-3927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25263@1" ObjectIDZND0="g_3ddd700@0" Pin0InfoVect0LinkObjId="g_3ddd700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141456_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1792,-3927 1804,-3927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dde9a0">
     <polyline DF8003:Layer="0" fill="none" points="1748,-3957 1748,-3944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3d67670@0" ObjectIDZND0="25263@x" ObjectIDZND1="25262@x" ObjectIDZND2="g_3d68ec0@0" Pin0InfoVect0LinkObjId="SW-141456_0" Pin0InfoVect1LinkObjId="SW-141455_0" Pin0InfoVect2LinkObjId="g_3d68ec0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d67670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1748,-3957 1748,-3944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ddec00">
     <polyline DF8003:Layer="0" fill="none" points="1748,-3957 1757,-3957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="25263@x" ObjectIDND1="25262@x" ObjectIDND2="g_3d68ec0@0" ObjectIDZND0="g_3d67670@1" Pin0InfoVect0LinkObjId="g_3d67670_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-141456_0" Pin1InfoVect1LinkObjId="SW-141455_0" Pin1InfoVect2LinkObjId="g_3d68ec0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1748,-3957 1757,-3957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ddee60">
     <polyline DF8003:Layer="0" fill="none" points="1787,-3957 1799,-3957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3d67670@0" ObjectIDZND0="g_3d67ef0@0" Pin0InfoVect0LinkObjId="g_3d67ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d67670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1787,-3957 1799,-3957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3de4040">
     <polyline DF8003:Layer="0" fill="none" points="376,-4355 376,-4339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10146@0" ObjectIDZND0="10147@1" Pin0InfoVect0LinkObjId="SW-55771_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="376,-4355 376,-4339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3de44e0">
     <polyline DF8003:Layer="0" fill="none" points="292,-4397 307,-4397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3e07600@0" ObjectIDZND0="10148@0" Pin0InfoVect0LinkObjId="SW-55772_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e07600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="292,-4397 307,-4397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3de68b0">
     <polyline DF8003:Layer="0" fill="none" points="307,-4237 291,-4237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_3de82f0@1" ObjectIDZND0="g_3de6b10@0" Pin0InfoVect0LinkObjId="g_3de6b10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3de82f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="307,-4237 291,-4237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dec1a0">
     <polyline DF8003:Layer="0" fill="none" points="2859,-4506 2859,-4520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="11812@x" ObjectIDND1="g_3ed72b0@0" ObjectIDND2="27252@x" ObjectIDZND0="11813@0" Pin0InfoVect0LinkObjId="SW-61724_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-61722_0" Pin1InfoVect1LinkObjId="g_3ed72b0_0" Pin1InfoVect2LinkObjId="SW-171113_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2859,-4506 2859,-4520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dec400">
     <polyline DF8003:Layer="0" fill="none" points="2859,-4556 2859,-4567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11813@1" ObjectIDZND0="g_3eb5ee0@0" Pin0InfoVect0LinkObjId="g_3eb5ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-61724_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2859,-4556 2859,-4567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3df1b00">
     <polyline DF8003:Layer="0" fill="none" points="2057,-4840 2067,-4840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3df0b40@0" ObjectIDZND0="g_3df1280@0" Pin0InfoVect0LinkObjId="g_3df1280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3df0b40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2057,-4840 2067,-4840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3df1d60">
     <polyline DF8003:Layer="0" fill="none" points="2984,-4506 3050,-4506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="11811@0" ObjectIDZND0="11821@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-61876_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2984,-4506 3050,-4506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3df1fc0">
     <polyline DF8003:Layer="0" fill="none" points="2926,-4506 2957,-4506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11812@1" ObjectIDZND0="11811@1" Pin0InfoVect0LinkObjId="SW-61876_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-61722_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2926,-4506 2957,-4506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3df3430">
     <polyline DF8003:Layer="0" fill="none" points="2643,-4764 2643,-4753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3df2d40@0" ObjectIDZND0="g_3df2860@0" Pin0InfoVect0LinkObjId="g_3df2860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3df2d40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2643,-4764 2643,-4753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3df3690">
     <polyline DF8003:Layer="0" fill="none" points="2675,-4803 2643,-4803 2643,-4795 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="2949@x" ObjectIDND1="11813@x" ObjectIDND2="11812@x" ObjectIDZND0="g_3df2d40@1" Pin0InfoVect0LinkObjId="g_3df2d40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-19285_0" Pin1InfoVect1LinkObjId="SW-61724_0" Pin1InfoVect2LinkObjId="SW-61722_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2675,-4803 2643,-4803 2643,-4795 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3df5240">
     <polyline DF8003:Layer="0" fill="none" points="2503,-4747 2503,-4736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_3df49c0@0" ObjectIDZND0="g_3df38f0@0" Pin0InfoVect0LinkObjId="g_3df38f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3df49c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2503,-4747 2503,-4736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3df5d20">
     <polyline DF8003:Layer="0" fill="none" points="2432,-4498 2432,-4482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_3df54a0@0" ObjectIDZND0="g_3e10700@0" Pin0InfoVect0LinkObjId="g_3e10700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3df54a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2432,-4498 2432,-4482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3df6810">
     <polyline DF8003:Layer="0" fill="none" points="2859,-4506 2890,-4506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="11813@x" ObjectIDND1="g_3ed72b0@0" ObjectIDND2="27252@x" ObjectIDZND0="11812@0" Pin0InfoVect0LinkObjId="SW-61722_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-61724_0" Pin1InfoVect1LinkObjId="g_3ed72b0_0" Pin1InfoVect2LinkObjId="SW-171113_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2859,-4506 2890,-4506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3df7620">
     <polyline DF8003:Layer="0" fill="none" points="1362,-5020 1338,-5020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="busSection" ObjectIDND0="g_3d9a010@0" ObjectIDZND0="g_3e617b0@0" ObjectIDZND1="4597@x" ObjectIDZND2="0@0" Pin0InfoVect0LinkObjId="g_3e617b0_0" Pin0InfoVect1LinkObjId="SW-28392_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d9a010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1362,-5020 1338,-5020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3df8110">
     <polyline DF8003:Layer="0" fill="none" points="1549,-4529 1569,-4529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="25131@1" ObjectIDZND0="25130@x" ObjectIDZND1="g_3d9a010@0" ObjectIDZND2="g_3e617b0@0" Pin0InfoVect0LinkObjId="SW-140385_0" Pin0InfoVect1LinkObjId="g_3d9a010_0" Pin0InfoVect2LinkObjId="g_3e617b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140386_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1549,-4529 1569,-4529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3df8370">
     <polyline DF8003:Layer="0" fill="none" points="1569,-4529 1569,-4543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="25131@x" ObjectIDND1="g_3d9a010@0" ObjectIDND2="g_3e617b0@0" ObjectIDZND0="25130@0" Pin0InfoVect0LinkObjId="SW-140385_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-140386_0" Pin1InfoVect1LinkObjId="g_3d9a010_0" Pin1InfoVect2LinkObjId="g_3e617b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1569,-4529 1569,-4543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dfc5e0">
     <polyline DF8003:Layer="0" fill="none" points="2269,-4757 2269,-4774 2298,-4774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_4232d90@1" ObjectIDZND0="2952@x" ObjectIDZND1="25266@x" ObjectIDZND2="25265@x" Pin0InfoVect0LinkObjId="SW-19288_0" Pin0InfoVect1LinkObjId="SW-141473_0" Pin0InfoVect2LinkObjId="SW-141472_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4232d90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2269,-4757 2269,-4774 2298,-4774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dfc840">
     <polyline DF8003:Layer="0" fill="none" points="2298,-4774 2298,-4812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_4232d90@0" ObjectIDND1="25266@x" ObjectIDND2="25265@x" ObjectIDZND0="2952@0" Pin0InfoVect0LinkObjId="SW-19288_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4232d90_0" Pin1InfoVect1LinkObjId="SW-141473_0" Pin1InfoVect2LinkObjId="SW-141472_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2298,-4774 2298,-4812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dfd320">
     <polyline DF8003:Layer="0" fill="none" points="1830,-4050 1830,-4058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3dfcaa0@1" ObjectIDZND0="g_3dfd580@0" Pin0InfoVect0LinkObjId="g_3dfd580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3dfcaa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1830,-4050 1830,-4058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e01cd0">
     <polyline DF8003:Layer="0" fill="none" points="2038,-4011 2028,-4011 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" ObjectIDND0="18519@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2038,-4011 2028,-4011 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e01ec0">
     <polyline DF8003:Layer="0" fill="none" points="1830,-4019 1830,-4011 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3dfcaa0@0" ObjectIDZND0="18534@x" ObjectIDZND1="18535@x" Pin0InfoVect0LinkObjId="SW-83926_0" Pin0InfoVect1LinkObjId="SW-83927_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3dfcaa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1830,-4019 1830,-4011 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e028f0">
     <polyline DF8003:Layer="0" fill="none" points="1830,-4011 1800,-4011 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_3dfcaa0@0" ObjectIDND1="18534@x" ObjectIDND2="18535@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3dfcaa0_0" Pin1InfoVect1LinkObjId="SW-83926_0" Pin1InfoVect2LinkObjId="SW-83927_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1830,-4011 1800,-4011 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e033b0">
     <polyline DF8003:Layer="0" fill="none" points="1870,-4011 1861,-4011 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="18534@0" ObjectIDZND0="g_3dfcaa0@0" ObjectIDZND1="18535@x" Pin0InfoVect0LinkObjId="g_3dfcaa0_0" Pin0InfoVect1LinkObjId="SW-83927_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83926_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1870,-4011 1861,-4011 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e035f0">
     <polyline DF8003:Layer="0" fill="none" points="1861,-4011 1830,-4011 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="18534@x" ObjectIDND1="18535@x" ObjectIDZND0="g_3dfcaa0@0" Pin0InfoVect0LinkObjId="g_3dfcaa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-83926_0" Pin1InfoVect1LinkObjId="SW-83927_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1861,-4011 1830,-4011 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e03850">
     <polyline DF8003:Layer="0" fill="none" points="1861,-4011 1861,-4018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="18534@x" ObjectIDND1="g_3dfcaa0@0" ObjectIDZND0="18535@0" Pin0InfoVect0LinkObjId="SW-83927_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-83926_0" Pin1InfoVect1LinkObjId="g_3dfcaa0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1861,-4011 1861,-4018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e03ab0">
     <polyline DF8003:Layer="0" fill="none" points="1861,-4054 1861,-4063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18535@1" ObjectIDZND0="g_3e004c0@0" Pin0InfoVect0LinkObjId="g_3e004c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83927_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1861,-4054 1861,-4063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e03d10">
     <polyline DF8003:Layer="0" fill="none" points="1917,-4011 1917,-4020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="18534@x" ObjectIDZND0="g_3dfafa0@0" Pin0InfoVect0LinkObjId="g_3dfafa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83926_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1917,-4011 1917,-4020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e047b0">
     <polyline DF8003:Layer="0" fill="none" points="1937,-4011 1917,-4011 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="lightningRod" ObjectIDZND0="18534@x" ObjectIDZND1="g_3dfafa0@0" Pin0InfoVect0LinkObjId="SW-83926_0" Pin0InfoVect1LinkObjId="g_3dfafa0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1937,-4011 1917,-4011 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e04a10">
     <polyline DF8003:Layer="0" fill="none" points="1917,-4011 1906,-4011 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3dfafa0@0" ObjectIDZND0="18534@1" Pin0InfoVect0LinkObjId="SW-83926_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3dfafa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1917,-4011 1906,-4011 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e054e0">
     <polyline DF8003:Layer="0" fill="none" points="2284,-3968 2298,-3968 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_32b4050@0" ObjectIDZND0="25266@x" ObjectIDZND1="25265@x" ObjectIDZND2="g_32b4d70@0" Pin0InfoVect0LinkObjId="SW-141473_0" Pin0InfoVect1LinkObjId="SW-141472_0" Pin0InfoVect2LinkObjId="g_32b4d70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32b4050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2284,-3968 2298,-3968 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e061d0">
     <polyline DF8003:Layer="0" fill="none" points="2298,-3938 2298,-3968 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="25266@x" ObjectIDND1="25265@x" ObjectIDND2="g_32b4d70@0" ObjectIDZND0="g_32b4050@0" ObjectIDZND1="g_4234210@0" ObjectIDZND2="g_4232d90@0" Pin0InfoVect0LinkObjId="g_32b4050_0" Pin0InfoVect1LinkObjId="g_4234210_0" Pin0InfoVect2LinkObjId="g_4232d90_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-141473_0" Pin1InfoVect1LinkObjId="SW-141472_0" Pin1InfoVect2LinkObjId="g_32b4d70_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2298,-3938 2298,-3968 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e06410">
     <polyline DF8003:Layer="0" fill="none" points="2311,-3968 2298,-3968 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_4234210@1" ObjectIDZND0="25266@x" ObjectIDZND1="25265@x" ObjectIDZND2="g_32b4d70@0" Pin0InfoVect0LinkObjId="SW-141473_0" Pin0InfoVect1LinkObjId="SW-141472_0" Pin0InfoVect2LinkObjId="g_32b4d70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4234210_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2311,-3968 2298,-3968 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e06670">
     <polyline DF8003:Layer="0" fill="none" points="2316,-3923 2298,-3923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="25266@0" ObjectIDZND0="25265@x" ObjectIDZND1="g_32b4050@0" ObjectIDZND2="g_4234210@0" Pin0InfoVect0LinkObjId="SW-141472_0" Pin0InfoVect1LinkObjId="g_32b4050_0" Pin0InfoVect2LinkObjId="g_4234210_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141473_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2316,-3923 2298,-3923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e07140">
     <polyline DF8003:Layer="0" fill="none" points="2298,-3914 2298,-3923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="25265@1" ObjectIDZND0="25266@x" ObjectIDZND1="g_32b4050@0" ObjectIDZND2="g_4234210@0" Pin0InfoVect0LinkObjId="SW-141473_0" Pin0InfoVect1LinkObjId="g_32b4050_0" Pin0InfoVect2LinkObjId="g_4234210_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141472_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2298,-3914 2298,-3923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e073a0">
     <polyline DF8003:Layer="0" fill="none" points="2298,-3923 2298,-3938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="25266@x" ObjectIDND1="25265@x" ObjectIDZND0="g_32b4050@0" ObjectIDZND1="g_4234210@0" ObjectIDZND2="g_4232d90@0" Pin0InfoVect0LinkObjId="g_32b4050_0" Pin0InfoVect1LinkObjId="g_4234210_0" Pin0InfoVect2LinkObjId="g_4232d90_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-141473_0" Pin1InfoVect1LinkObjId="SW-141472_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2298,-3923 2298,-3938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e0caf0">
     <polyline DF8003:Layer="0" fill="none" points="2586,-3638 2586,-3626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21914@0" ObjectIDZND0="21912@1" Pin0InfoVect0LinkObjId="SW-116066_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116068_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2586,-3638 2586,-3626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e0f550">
     <polyline DF8003:Layer="0" fill="none" points="2586,-3547 2586,-3530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21913@0" ObjectIDZND0="21923@0" Pin0InfoVect0LinkObjId="g_3e195d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116067_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2586,-3547 2586,-3530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e0f7b0">
     <polyline DF8003:Layer="0" fill="none" points="2586,-3599 2586,-3583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21912@0" ObjectIDZND0="21913@1" Pin0InfoVect0LinkObjId="SW-116067_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116066_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2586,-3599 2586,-3583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e195d0">
     <polyline DF8003:Layer="0" fill="none" points="3020,-3542 3020,-3530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22985@0" ObjectIDZND0="21923@0" Pin0InfoVect0LinkObjId="g_3e0f550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125519_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3020,-3542 3020,-3530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e243e0">
     <polyline DF8003:Layer="0" fill="none" points="3020,-3586 3020,-3578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22986@0" ObjectIDZND0="22985@1" Pin0InfoVect0LinkObjId="SW-125519_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125516_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3020,-3586 3020,-3578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e37f90">
     <polyline DF8003:Layer="0" fill="none" points="3020,-4059 3020,-4068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22998@0" ObjectIDZND0="22996@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125726_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3020,-4059 3020,-4068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e381f0">
     <polyline DF8003:Layer="0" fill="none" points="3020,-3999 3020,-4007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23021@1" ObjectIDZND0="22997@0" Pin0InfoVect0LinkObjId="SW-125725_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125726_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3020,-3999 3020,-4007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e38450">
     <polyline DF8003:Layer="0" fill="none" points="3020,-4034 3020,-4042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22997@1" ObjectIDZND0="22998@1" Pin0InfoVect0LinkObjId="SW-125726_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125725_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3020,-4034 3020,-4042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e386b0">
     <polyline DF8003:Layer="0" fill="none" points="2972,-3969 2978,-3969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3e2d9c0@0" ObjectIDZND0="22999@0" Pin0InfoVect0LinkObjId="SW-125727_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e2d9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2972,-3969 2978,-3969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e38910">
     <polyline DF8003:Layer="0" fill="none" points="3015,-3969 3020,-3969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="22999@1" ObjectIDZND0="23021@x" ObjectIDZND1="g_3e2e450@0" ObjectIDZND2="g_3e37960@0" Pin0InfoVect0LinkObjId="SW-125726_0" Pin0InfoVect1LinkObjId="g_3e2e450_0" Pin0InfoVect2LinkObjId="g_3e37960_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125727_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3015,-3969 3020,-3969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e39600">
     <polyline DF8003:Layer="0" fill="none" points="3020,-3969 3020,-3982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="22999@x" ObjectIDND1="g_3e2e450@0" ObjectIDND2="g_3e37960@0" ObjectIDZND0="23021@0" Pin0InfoVect0LinkObjId="SW-125726_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-125727_0" Pin1InfoVect1LinkObjId="g_3e2e450_0" Pin1InfoVect2LinkObjId="g_3e37960_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3020,-3969 3020,-3982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e39860">
     <polyline DF8003:Layer="0" fill="none" points="3048,-3963 3048,-3969 3020,-3969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_3e2e450@0" ObjectIDZND0="22999@x" ObjectIDZND1="23021@x" ObjectIDZND2="g_3e37960@0" Pin0InfoVect0LinkObjId="SW-125727_0" Pin0InfoVect1LinkObjId="SW-125726_0" Pin0InfoVect2LinkObjId="g_3e37960_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e2e450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3048,-3963 3048,-3969 3020,-3969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e39ac0">
     <polyline DF8003:Layer="0" fill="none" points="2983,-3946 3020,-3946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3e37960@0" ObjectIDZND0="g_3e2a870@0" ObjectIDZND1="22999@x" ObjectIDZND2="23021@x" Pin0InfoVect0LinkObjId="g_3e2a870_0" Pin0InfoVect1LinkObjId="SW-125727_0" Pin0InfoVect2LinkObjId="SW-125726_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e37960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2983,-3946 3020,-3946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e3a590">
     <polyline DF8003:Layer="0" fill="none" points="3020,-3933 3020,-3946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3e2a870@1" ObjectIDZND0="g_3e37960@0" ObjectIDZND1="22999@x" ObjectIDZND2="23021@x" Pin0InfoVect0LinkObjId="g_3e37960_0" Pin0InfoVect1LinkObjId="SW-125727_0" Pin0InfoVect2LinkObjId="SW-125726_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e2a870_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3020,-3933 3020,-3946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e3a7f0">
     <polyline DF8003:Layer="0" fill="none" points="3020,-3946 3020,-3969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3e37960@0" ObjectIDND1="g_3e2a870@0" ObjectIDZND0="22999@x" ObjectIDZND1="23021@x" ObjectIDZND2="g_3e2e450@0" Pin0InfoVect0LinkObjId="SW-125727_0" Pin0InfoVect1LinkObjId="SW-125726_0" Pin0InfoVect2LinkObjId="g_3e2e450_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3e37960_0" Pin1InfoVect1LinkObjId="g_3e2a870_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3020,-3946 3020,-3969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e3b4e0">
     <polyline DF8003:Layer="0" fill="none" points="3020,-3614 3020,-3639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="22986@1" ObjectIDZND0="g_3e280f0@0" ObjectIDZND1="22984@x" ObjectIDZND2="g_3e273c0@0" Pin0InfoVect0LinkObjId="g_3e280f0_0" Pin0InfoVect1LinkObjId="SW-125520_0" Pin0InfoVect2LinkObjId="g_3e273c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125516_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3020,-3614 3020,-3639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e3b730">
     <polyline DF8003:Layer="0" fill="none" points="3020,-3639 3020,-3661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="22986@x" ObjectIDND1="22984@x" ObjectIDND2="g_3e273c0@0" ObjectIDZND0="g_3e280f0@0" Pin0InfoVect0LinkObjId="g_3e280f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-125516_0" Pin1InfoVect1LinkObjId="SW-125520_0" Pin1InfoVect2LinkObjId="g_3e273c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3020,-3639 3020,-3661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e3b990">
     <polyline DF8003:Layer="0" fill="none" points="2971,-3639 2978,-3639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3e26930@0" ObjectIDZND0="22984@0" Pin0InfoVect0LinkObjId="SW-125520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e26930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2971,-3639 2978,-3639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e3bbf0">
     <polyline DF8003:Layer="0" fill="none" points="3014,-3639 3020,-3639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="22984@1" ObjectIDZND0="22986@x" ObjectIDZND1="g_3e280f0@0" ObjectIDZND2="g_3e273c0@0" Pin0InfoVect0LinkObjId="SW-125516_0" Pin0InfoVect1LinkObjId="g_3e280f0_0" Pin0InfoVect2LinkObjId="g_3e273c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125520_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3014,-3639 3020,-3639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e3be50">
     <polyline DF8003:Layer="0" fill="none" points="2638,-3700 2631,-3700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3e13a00@0" ObjectIDZND0="21916@1" Pin0InfoVect0LinkObjId="SW-116070_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e13a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2638,-3700 2631,-3700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e3c0b0">
     <polyline DF8003:Layer="0" fill="none" points="3013,-3880 3020,-3880 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_3e28d10@0" ObjectIDZND0="g_3e2a870@0" ObjectIDZND1="g_3e29ac0@0" ObjectIDZND2="g_3e280f0@0" Pin0InfoVect0LinkObjId="g_3e2a870_0" Pin0InfoVect1LinkObjId="g_3e29ac0_0" Pin0InfoVect2LinkObjId="g_3e280f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e28d10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3013,-3880 3020,-3880 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e3cbe0">
     <polyline DF8003:Layer="0" fill="none" points="3020,-3894 3020,-3880 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_3e2a870@0" ObjectIDZND0="g_3e28d10@0" ObjectIDZND1="g_3e29ac0@0" ObjectIDZND2="g_3e280f0@0" Pin0InfoVect0LinkObjId="g_3e28d10_0" Pin0InfoVect1LinkObjId="g_3e29ac0_0" Pin0InfoVect2LinkObjId="g_3e280f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e2a870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3020,-3894 3020,-3880 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e3ce40">
     <polyline DF8003:Layer="0" fill="none" points="3020,-3880 3020,-3710 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_3e28d10@0" ObjectIDND1="g_3e2a870@0" ObjectIDZND0="g_3e29ac0@0" ObjectIDZND1="g_3e280f0@0" Pin0InfoVect0LinkObjId="g_3e29ac0_0" Pin0InfoVect1LinkObjId="g_3e280f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3e28d10_0" Pin1InfoVect1LinkObjId="g_3e2a870_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3020,-3880 3020,-3710 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e3d970">
     <polyline DF8003:Layer="0" fill="none" points="2483,-4521 2463,-4521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_3df54a0@0" ObjectIDZND2="g_3df49c0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3df54a0_0" Pin0InfoVect2LinkObjId="g_3df49c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2483,-4521 2463,-4521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e3dbd0">
     <polyline DF8003:Layer="0" fill="none" points="2463,-4521 2463,-4512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_3df54a0@0" ObjectIDND2="g_3df49c0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3df54a0_0" Pin1InfoVect2LinkObjId="g_3df49c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2463,-4521 2463,-4512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e3f1c0">
     <polyline DF8003:Layer="0" fill="none" points="3048,-3631 3048,-3639 3020,-3639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3e273c0@0" ObjectIDZND0="22986@x" ObjectIDZND1="g_3e280f0@0" ObjectIDZND2="22984@x" Pin0InfoVect0LinkObjId="SW-125516_0" Pin0InfoVect1LinkObjId="g_3e280f0_0" Pin0InfoVect2LinkObjId="SW-125520_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e273c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3048,-3631 3048,-3639 3020,-3639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e437c0">
     <polyline DF8003:Layer="0" fill="none" points="2776,-3543 2776,-3530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21992@0" ObjectIDZND0="21923@0" Pin0InfoVect0LinkObjId="g_3e0f550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-117465_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2776,-3543 2776,-3530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e43a20">
     <polyline DF8003:Layer="0" fill="none" points="2776,-3588 2776,-3579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21991@0" ObjectIDZND0="21992@1" Pin0InfoVect0LinkObjId="SW-117465_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-117459_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2776,-3588 2776,-3579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e46910">
     <polyline DF8003:Layer="0" fill="none" points="2776,-3622 2776,-3615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21993@0" ObjectIDZND0="21991@1" Pin0InfoVect0LinkObjId="SW-117459_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-117466_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2776,-3622 2776,-3615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e490a0">
     <polyline DF8003:Layer="0" fill="none" points="2727,-3669 2734,-3669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3e49b90@0" ObjectIDZND0="21995@0" Pin0InfoVect0LinkObjId="SW-117468_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e49b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2727,-3669 2734,-3669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e49300">
     <polyline DF8003:Layer="0" fill="none" points="2770,-3669 2776,-3669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="21995@1" ObjectIDZND0="21993@x" ObjectIDZND1="g_3e4e950@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-117466_0" Pin0InfoVect1LinkObjId="g_3e4e950_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-117468_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2770,-3669 2776,-3669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e4aff0">
     <polyline DF8003:Layer="0" fill="none" points="2805,-3643 2805,-3628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3e4dbe0@0" Pin0InfoVect0LinkObjId="g_3e4dbe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2805,-3643 2805,-3628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e5cf90">
     <polyline DF8003:Layer="0" fill="none" points="2785,-3928 2776,-3928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_3e4e950@0" ObjectIDZND2="g_3e52110@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3e4e950_0" Pin0InfoVect2LinkObjId="g_3e52110_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2785,-3928 2776,-3928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e5d180">
     <polyline DF8003:Layer="0" fill="none" points="2829,-3928 2822,-3928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3e5ae00@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e5ae00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2829,-3928 2822,-3928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e5d370">
     <polyline DF8003:Layer="0" fill="none" points="2776,-3981 2776,-3990 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2776,-3981 2776,-3990 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e5d580">
     <polyline DF8003:Layer="0" fill="none" points="2776,-4027 2776,-4017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2776,-4027 2776,-4017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e5e250">
     <polyline DF8003:Layer="0" fill="none" points="2776,-3659 2776,-3669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="21993@1" ObjectIDZND0="21995@x" ObjectIDZND1="g_3e4e950@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-117468_0" Pin0InfoVect1LinkObjId="g_3e4e950_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-117466_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2776,-3659 2776,-3669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e5ec60">
     <polyline DF8003:Layer="0" fill="none" points="2776,-3928 2776,-3944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_3e4e950@0" ObjectIDND2="g_3e52110@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3e4e950_0" Pin1InfoVect2LinkObjId="g_3e52110_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2776,-3928 2776,-3944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e5ee80">
     <polyline DF8003:Layer="0" fill="none" points="2776,-3902 2767,-3902 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_3e52110@0" ObjectIDZND0="g_3e4e950@0" Pin0InfoVect0LinkObjId="g_3e4e950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_3e52110_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2776,-3902 2767,-3902 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e5fb40">
     <polyline DF8003:Layer="0" fill="none" points="2776,-3928 2776,-3902 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_3e4e950@0" ObjectIDZND1="g_3e52110@0" ObjectIDZND2="21995@x" Pin0InfoVect0LinkObjId="g_3e4e950_0" Pin0InfoVect1LinkObjId="g_3e52110_0" Pin0InfoVect2LinkObjId="SW-117468_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2776,-3928 2776,-3902 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e5fda0">
     <polyline DF8003:Layer="0" fill="none" points="2781,-3902 2776,-3902 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3e52110@0" ObjectIDZND0="g_3e4e950@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3e4e950_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e52110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2781,-3902 2776,-3902 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e60a60">
     <polyline DF8003:Layer="0" fill="none" points="2776,-3902 2776,-3711 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3e4e950@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="21995@x" ObjectIDZND1="21993@x" ObjectIDZND2="g_3e4a370@0" Pin0InfoVect0LinkObjId="SW-117468_0" Pin0InfoVect1LinkObjId="SW-117466_0" Pin0InfoVect2LinkObjId="g_3e4a370_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3e4e950_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2776,-3902 2776,-3711 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e60cc0">
     <polyline DF8003:Layer="0" fill="none" points="2776,-3711 2776,-3669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3e4e950@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="21995@x" ObjectIDZND1="21993@x" Pin0InfoVect0LinkObjId="SW-117468_0" Pin0InfoVect1LinkObjId="SW-117466_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3e4e950_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2776,-3711 2776,-3669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e60f20">
     <polyline DF8003:Layer="0" fill="none" points="2768,-3711 2776,-3711 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3e4a370@0" ObjectIDZND0="g_3e4e950@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3e4e950_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e4a370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2768,-3711 2776,-3711 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e620f0">
     <polyline DF8003:Layer="0" fill="none" points="1319,-5027 1338,-5027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="busSection" ObjectIDND0="g_3e617b0@0" ObjectIDZND0="4597@x" ObjectIDZND1="g_3d9a010@0" ObjectIDZND2="0@0" Pin0InfoVect0LinkObjId="SW-28392_0" Pin0InfoVect1LinkObjId="g_3d9a010_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e617b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1319,-5027 1338,-5027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e62330">
     <polyline DF8003:Layer="0" fill="none" points="1376,-4937 1376,-4926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3e62590@0" Pin0InfoVect0LinkObjId="g_3e62590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1376,-4937 1376,-4926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e65ef0">
     <polyline DF8003:Layer="0" fill="none" points="1338,-5034 1338,-5027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="busSection" ObjectIDND0="4597@0" ObjectIDZND0="g_3e617b0@0" ObjectIDZND1="g_3d9a010@0" ObjectIDZND2="0@0" Pin0InfoVect0LinkObjId="g_3e617b0_0" Pin0InfoVect1LinkObjId="g_3d9a010_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28392_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1338,-5034 1338,-5027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e66150">
     <polyline DF8003:Layer="0" fill="none" points="1338,-5027 1338,-5020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="busSection" EndDevType2="switch" ObjectIDND0="g_3e617b0@0" ObjectIDND1="4597@x" ObjectIDZND0="g_3d9a010@0" ObjectIDZND1="0@0" ObjectIDZND2="25131@x" Pin0InfoVect0LinkObjId="g_3d9a010_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-140386_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3e617b0_0" Pin1InfoVect1LinkObjId="SW-28392_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1338,-5027 1338,-5020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e67650">
     <polyline DF8003:Layer="0" fill="none" points="1588,-4938 1588,-4927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3e678b0@0" Pin0InfoVect0LinkObjId="g_3e678b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1588,-4938 1588,-4927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e6b6f0">
     <polyline DF8003:Layer="0" fill="none" points="1876,-4911 1876,-4930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="24970@x" ObjectIDND1="g_42b14d0@0" ObjectIDND2="0@x" ObjectIDZND0="24967@0" Pin0InfoVect0LinkObjId="SW-138805_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-138808_0" Pin1InfoVect1LinkObjId="g_42b14d0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1876,-4911 1876,-4930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e6b950">
     <polyline DF8003:Layer="0" fill="none" points="1610,-4990 1610,-4830 1876,-4830 1876,-4872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="4600@x" ObjectIDND2="g_3e6a980@0" ObjectIDZND0="g_42b14d0@0" ObjectIDZND1="24970@x" ObjectIDZND2="24967@x" Pin0InfoVect0LinkObjId="g_42b14d0_0" Pin0InfoVect1LinkObjId="SW-138808_0" Pin0InfoVect2LinkObjId="SW-138805_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-28404_0" Pin1InfoVect2LinkObjId="g_3e6a980_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1610,-4990 1610,-4830 1876,-4830 1876,-4872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e6bbc0">
     <polyline DF8003:Layer="0" fill="none" points="1876,-4872 1876,-4911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_42b14d0@0" ObjectIDND1="0@x" ObjectIDND2="4600@x" ObjectIDZND0="24970@x" ObjectIDZND1="24967@x" Pin0InfoVect0LinkObjId="SW-138808_0" Pin0InfoVect1LinkObjId="SW-138805_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_42b14d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-28404_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1876,-4872 1876,-4911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e6be20">
     <polyline DF8003:Layer="0" fill="none" points="1616,-5008 1610,-5008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3e6a980@0" ObjectIDZND0="4600@x" ObjectIDZND1="0@x" ObjectIDZND2="g_42b14d0@0" Pin0InfoVect0LinkObjId="SW-28404_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_42b14d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e6a980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1616,-5008 1610,-5008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e6c080">
     <polyline DF8003:Layer="0" fill="none" points="1610,-5041 1610,-5008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="4600@0" ObjectIDZND0="g_3e6a980@0" ObjectIDZND1="0@x" ObjectIDZND2="g_42b14d0@0" Pin0InfoVect0LinkObjId="g_3e6a980_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_42b14d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28404_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1610,-5041 1610,-5008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e6c2e0">
     <polyline DF8003:Layer="0" fill="none" points="1079,-4950 1079,-4939 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3e6c540@0" Pin0InfoVect0LinkObjId="g_3e6c540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1079,-4950 1079,-4939 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e70720">
     <polyline DF8003:Layer="0" fill="none" points="1116,-4724 1116,-4711 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="29441@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-193560_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1116,-4724 1116,-4711 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e70980">
     <polyline DF8003:Layer="0" fill="none" points="1116,-5003 1116,-4724 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="29441@x" ObjectIDND2="g_3db2810@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-193560_0" Pin1InfoVect2LinkObjId="g_3db2810_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1116,-5003 1116,-4724 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e74470">
     <polyline DF8003:Layer="0" fill="none" points="749,-3569 749,-3555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25626@0" ObjectIDZND0="25683@0" Pin0InfoVect0LinkObjId="SW-146704_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="749,-3569 749,-3555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e76bc0">
     <polyline DF8003:Layer="0" fill="none" points="832,-3553 832,-3569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25684@0" ObjectIDZND0="25627@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-146705_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="832,-3553 832,-3569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e78eb0">
     <polyline DF8003:Layer="0" fill="none" points="749,-3519 749,-3509 779,-3509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25683@1" ObjectIDZND0="25682@1" Pin0InfoVect0LinkObjId="SW-146703_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-146704_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="749,-3519 749,-3509 779,-3509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e79110">
     <polyline DF8003:Layer="0" fill="none" points="806,-3509 832,-3509 832,-3517 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25682@0" ObjectIDZND0="25684@1" Pin0InfoVect0LinkObjId="SW-146705_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-146703_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="806,-3509 832,-3509 832,-3517 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e79370">
     <polyline DF8003:Layer="0" fill="none" points="694,-3569 694,-3586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25626@0" ObjectIDZND0="25686@0" Pin0InfoVect0LinkObjId="SW-146734_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="694,-3569 694,-3586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e7b3e0">
     <polyline DF8003:Layer="0" fill="none" points="694,-3622 694,-3636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25686@1" ObjectIDZND0="25685@1" Pin0InfoVect0LinkObjId="SW-146733_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-146734_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="694,-3622 694,-3636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e7db70">
     <polyline DF8003:Layer="0" fill="none" points="695,-3665 695,-3677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25685@0" ObjectIDZND0="25687@0" Pin0InfoVect0LinkObjId="SW-146735_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-146733_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="695,-3665 695,-3677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e85c70">
     <polyline DF8003:Layer="0" fill="none" points="487,-4084 487,-4075 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_3e853f0@0" Pin0InfoVect0LinkObjId="g_3e853f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="487,-4084 487,-4075 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e877c0">
     <polyline DF8003:Layer="0" fill="none" points="714,-3875 714,-3866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3e87a20@0" ObjectIDZND0="g_3e86500@0" Pin0InfoVect0LinkObjId="g_3e86500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e87a20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="714,-3875 714,-3866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e89720">
     <polyline DF8003:Layer="0" fill="none" points="909,-3856 909,-3847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3e89980@0" ObjectIDZND0="g_3e880f0@0" Pin0InfoVect0LinkObjId="g_3e880f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e89980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="909,-3856 909,-3847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e90870">
     <polyline DF8003:Layer="0" fill="none" points="892,-3569 892,-3586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25627@0" ObjectIDZND0="25694@0" Pin0InfoVect0LinkObjId="SW-146780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e76bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="892,-3569 892,-3586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e90ad0">
     <polyline DF8003:Layer="0" fill="none" points="892,-3624 892,-3638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25694@1" ObjectIDZND0="25693@1" Pin0InfoVect0LinkObjId="SW-146779_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-146780_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="892,-3624 892,-3638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e90d30">
     <polyline DF8003:Layer="0" fill="none" points="892,-3665 892,-3677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25693@0" ObjectIDZND0="25695@0" Pin0InfoVect0LinkObjId="SW-146781_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-146779_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="892,-3665 892,-3677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e91a00">
     <polyline DF8003:Layer="0" fill="none" points="871,-4614 871,-4593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24884@0" ObjectIDZND0="24878@0" Pin0InfoVect0LinkObjId="g_3e99eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-138151_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="871,-4614 871,-4593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e97c80">
     <polyline DF8003:Layer="0" fill="none" points="861,-4675 871,-4675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3e96ed0@0" ObjectIDZND0="g_3e96650@0" ObjectIDZND1="24884@x" Pin0InfoVect0LinkObjId="g_3e96650_0" Pin0InfoVect1LinkObjId="SW-138151_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e96ed0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="861,-4675 871,-4675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e987b0">
     <polyline DF8003:Layer="0" fill="none" points="871,-4691 871,-4675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3e96650@1" ObjectIDZND0="g_3e96ed0@0" ObjectIDZND1="24884@x" Pin0InfoVect0LinkObjId="g_3e96ed0_0" Pin0InfoVect1LinkObjId="SW-138151_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e96650_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="871,-4691 871,-4675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e98a10">
     <polyline DF8003:Layer="0" fill="none" points="871,-4675 871,-4660 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_3e96ed0@0" ObjectIDND1="g_3e96650@0" ObjectIDZND0="24884@1" Pin0InfoVect0LinkObjId="SW-138151_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3e96ed0_0" Pin1InfoVect1LinkObjId="g_3e96650_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="871,-4675 871,-4660 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e98c70">
     <polyline DF8003:Layer="0" fill="none" points="871,-4735 871,-4722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_3e90f90@0" ObjectIDZND0="g_3e96650@0" Pin0InfoVect0LinkObjId="g_3e96650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e90f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="871,-4735 871,-4722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e99070">
     <polyline DF8003:Layer="0" fill="none" points="829,-4781 829,-4775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" ObjectIDND0="g_3e99420@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e99420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="829,-4781 829,-4775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e99eb0">
     <polyline DF8003:Layer="0" fill="none" points="746,-4611 746,-4593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="27850@1" ObjectIDZND0="24878@0" Pin0InfoVect0LinkObjId="g_3e91a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183092_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="746,-4611 746,-4593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e9eac0">
     <polyline DF8003:Layer="0" fill="none" points="689,-4692 675,-4692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27851@0" ObjectIDZND0="g_3e9ed20@0" Pin0InfoVect0LinkObjId="g_3e9ed20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183096_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="689,-4692 675,-4692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e9f7b0">
     <polyline DF8003:Layer="0" fill="none" points="746,-4692 725,-4692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="27850@x" ObjectIDND1="0@x" ObjectIDND2="g_3ea2400@0" ObjectIDZND0="27851@1" Pin0InfoVect0LinkObjId="SW-183096_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-183092_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_3ea2400_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="746,-4692 725,-4692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ea1f40">
     <polyline DF8003:Layer="0" fill="none" points="746,-4692 746,-4676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="27851@x" ObjectIDND1="0@x" ObjectIDND2="g_3ea2400@0" ObjectIDZND0="27850@0" Pin0InfoVect0LinkObjId="SW-183092_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-183096_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_3ea2400_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="746,-4692 746,-4676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ea21a0">
     <polyline DF8003:Layer="0" fill="none" points="736,-4714 746,-4714 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_3ea2400@0" ObjectIDZND0="0@x" ObjectIDZND1="27851@x" ObjectIDZND2="27850@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-183096_0" Pin0InfoVect2LinkObjId="SW-183092_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ea2400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="736,-4714 746,-4714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ea31b0">
     <polyline DF8003:Layer="0" fill="none" points="746,-4732 746,-4714 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="0@0" ObjectIDZND0="g_3ea2400@0" ObjectIDZND1="27851@x" ObjectIDZND2="27850@x" Pin0InfoVect0LinkObjId="g_3ea2400_0" Pin0InfoVect1LinkObjId="SW-183096_0" Pin0InfoVect2LinkObjId="SW-183092_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="746,-4732 746,-4714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ea3410">
     <polyline DF8003:Layer="0" fill="none" points="746,-4714 746,-4692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@x" ObjectIDND1="g_3ea2400@0" ObjectIDZND0="27851@x" ObjectIDZND1="27850@x" Pin0InfoVect0LinkObjId="SW-183096_0" Pin0InfoVect1LinkObjId="SW-183092_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3ea2400_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="746,-4714 746,-4692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ea4910">
     <polyline DF8003:Layer="0" fill="none" points="746,-4803 746,-4777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="746,-4803 746,-4777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ea9d40">
     <polyline DF8003:Layer="0" fill="none" points="358,-4237 338,-4237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3de7580@0" ObjectIDND1="g_3df6a70@0" ObjectIDND2="10147@x" ObjectIDZND0="g_3de82f0@0" Pin0InfoVect0LinkObjId="g_3de82f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3de7580_0" Pin1InfoVect1LinkObjId="g_3df6a70_0" Pin1InfoVect2LinkObjId="SW-55771_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="358,-4237 338,-4237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eafd80">
     <polyline DF8003:Layer="0" fill="none" points="706,-3727 696,-3727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3eaf3b0@0" ObjectIDZND0="g_3eb0ff0@0" Pin0InfoVect0LinkObjId="g_3eb0ff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3eaf3b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="706,-3727 696,-3727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eb0d90">
     <polyline DF8003:Layer="0" fill="none" points="902,-3724 892,-3724 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3eaffe0@0" ObjectIDZND0="25695@x" ObjectIDZND1="g_3eb2460@0" Pin0InfoVect0LinkObjId="SW-146781_0" Pin0InfoVect1LinkObjId="g_3eb2460_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3eaffe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="902,-3724 892,-3724 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eb1d40">
     <polyline DF8003:Layer="0" fill="none" points="695,-3727 694,-3728 694,-3718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" ObjectIDND0="g_3eaf3b0@0" ObjectIDND1="g_3eb0ff0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3eaf3b0_0" Pin1InfoVect1LinkObjId="g_3eb0ff0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="695,-3727 694,-3728 694,-3718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eb1fa0">
     <polyline DF8003:Layer="0" fill="none" points="695,-3727 695,-3744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3eaf3b0@0" ObjectIDZND0="g_3eb0ff0@0" Pin0InfoVect0LinkObjId="g_3eb0ff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3eaf3b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="695,-3727 695,-3744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eb2200">
     <polyline DF8003:Layer="0" fill="none" points="695,-3770 695,-3783 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3eb0ff0@1" ObjectIDZND0="25688@0" Pin0InfoVect0LinkObjId="SW-146736_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3eb0ff0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="695,-3770 695,-3783 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eb3a40">
     <polyline DF8003:Layer="0" fill="none" points="892,-3713 892,-3724 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="25695@1" ObjectIDZND0="g_3eaffe0@0" ObjectIDZND1="g_3eb2460@0" Pin0InfoVect0LinkObjId="g_3eaffe0_0" Pin0InfoVect1LinkObjId="g_3eb2460_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-146781_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="892,-3713 892,-3724 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eb3ca0">
     <polyline DF8003:Layer="0" fill="none" points="892,-3724 892,-3738 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3eaffe0@0" ObjectIDND1="25695@x" ObjectIDZND0="g_3eb2460@0" Pin0InfoVect0LinkObjId="g_3eb2460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3eaffe0_0" Pin1InfoVect1LinkObjId="SW-146781_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="892,-3724 892,-3738 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eb3f00">
     <polyline DF8003:Layer="0" fill="none" points="892,-3764 892,-3777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_3eb2460@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3eb2460_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="892,-3764 892,-3777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eb4c10">
     <polyline DF8003:Layer="0" fill="none" points="892,-3825 892,-3811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3e88970@0" ObjectIDND1="g_3e880f0@0" ObjectIDND2="g_3dc6ff0@0" ObjectIDZND0="25696@1" Pin0InfoVect0LinkObjId="SW-146782_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3e88970_0" Pin1InfoVect1LinkObjId="g_3e880f0_0" Pin1InfoVect2LinkObjId="g_3dc6ff0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="892,-3825 892,-3811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eb5c80">
     <polyline DF8003:Layer="0" fill="none" points="2675,-4818 2675,-4803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="2949@0" ObjectIDZND0="g_3df2d40@0" ObjectIDZND1="11813@x" ObjectIDZND2="11812@x" Pin0InfoVect0LinkObjId="g_3df2d40_0" Pin0InfoVect1LinkObjId="SW-61724_0" Pin0InfoVect2LinkObjId="SW-61722_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19285_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2675,-4818 2675,-4803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ebc5f0">
     <polyline DF8003:Layer="0" fill="none" points="1569,-4677 1569,-4690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25129@1" ObjectIDZND0="25116@0" Pin0InfoVect0LinkObjId="g_3ebcc80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140384_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1569,-4677 1569,-4690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ebc7e0">
     <polyline DF8003:Layer="0" fill="none" points="1569,-4626 1569,-4641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25128@1" ObjectIDZND0="25129@0" Pin0InfoVect0LinkObjId="SW-140384_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140382_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1569,-4626 1569,-4641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ebca20">
     <polyline DF8003:Layer="0" fill="none" points="1569,-4579 1569,-4599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25130@1" ObjectIDZND0="25128@0" Pin0InfoVect0LinkObjId="SW-140382_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140385_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1569,-4579 1569,-4599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ebcc80">
     <polyline DF8003:Layer="0" fill="none" points="1748,-4675 1748,-4690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25125@1" ObjectIDZND0="25116@0" Pin0InfoVect0LinkObjId="g_3ebc5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140333_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1748,-4675 1748,-4690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ebcee0">
     <polyline DF8003:Layer="0" fill="none" points="1748,-4577 1748,-4597 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25126@1" ObjectIDZND0="25124@0" Pin0InfoVect0LinkObjId="SW-140331_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140334_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1748,-4577 1748,-4597 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ebd140">
     <polyline DF8003:Layer="0" fill="none" points="1748,-4624 1748,-4639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25124@1" ObjectIDZND0="25125@0" Pin0InfoVect0LinkObjId="SW-140333_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140331_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1748,-4624 1748,-4639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ebd3a0">
     <polyline DF8003:Layer="0" fill="none" points="1725,-4507 1748,-4507 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25127@1" ObjectIDZND0="25126@x" ObjectIDZND1="37592@x" ObjectIDZND2="37593@x" Pin0InfoVect0LinkObjId="SW-140334_0" Pin0InfoVect1LinkObjId="SW-226824_0" Pin0InfoVect2LinkObjId="SW-226823_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140335_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1725,-4507 1748,-4507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ebd600">
     <polyline DF8003:Layer="0" fill="none" points="1929,-4681 1929,-4690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25120@1" ObjectIDZND0="25116@0" Pin0InfoVect0LinkObjId="g_3ebc5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140281_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1929,-4681 1929,-4690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ebd860">
     <polyline DF8003:Layer="0" fill="none" points="1929,-4645 1929,-4635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25120@0" ObjectIDZND0="25119@1" Pin0InfoVect0LinkObjId="SW-140279_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140281_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1929,-4645 1929,-4635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ebdac0">
     <polyline DF8003:Layer="0" fill="none" points="1929,-4599 1929,-4608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25121@1" ObjectIDZND0="25119@0" Pin0InfoVect0LinkObjId="SW-140279_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140282_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1929,-4599 1929,-4608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ec4c70">
     <polyline DF8003:Layer="0" fill="none" points="950,-5190 950,-5166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4549@0" ObjectIDZND0="4593@1" Pin0InfoVect0LinkObjId="SW-28379_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="950,-5190 950,-5166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ec4ed0">
     <polyline DF8003:Layer="0" fill="none" points="950,-5130 950,-5115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4593@0" ObjectIDZND0="4595@1" Pin0InfoVect0LinkObjId="SW-28381_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28379_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="950,-5130 950,-5115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ec5be0">
     <polyline DF8003:Layer="0" fill="none" points="950,-5088 950,-5068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4595@0" ObjectIDZND0="4594@1" Pin0InfoVect0LinkObjId="SW-28380_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-28381_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="950,-5088 950,-5068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ec6a60">
     <polyline DF8003:Layer="0" fill="none" points="949,-5012 968,-5012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3ec77b0@0" ObjectIDND1="0@x" ObjectIDND2="29497@x" ObjectIDZND0="g_3ec6080@0" Pin0InfoVect0LinkObjId="g_3ec6080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ec77b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-194235_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="949,-5012 968,-5012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ec7550">
     <polyline DF8003:Layer="0" fill="none" points="950,-5038 950,-5012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDZND0="g_3ec6080@0" ObjectIDZND1="g_3ec77b0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3ec6080_0" Pin0InfoVect1LinkObjId="g_3ec77b0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="950,-5038 950,-5012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ecfdc0">
     <polyline DF8003:Layer="0" fill="none" points="287,-4050 312,-4050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25192@1" ObjectIDZND0="25191@1" Pin0InfoVect0LinkObjId="SW-140978_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140979_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="287,-4050 312,-4050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ed4370">
     <polyline DF8003:Layer="0" fill="none" points="696,-4369 696,-4328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27848@1" ObjectIDZND0="g_3df6a70@0" ObjectIDZND1="g_3edd4c0@0" ObjectIDZND2="25688@x" Pin0InfoVect0LinkObjId="g_3df6a70_0" Pin0InfoVect1LinkObjId="g_3edd4c0_0" Pin0InfoVect2LinkObjId="SW-146736_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183002_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="696,-4369 696,-4328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ed4ce0">
     <polyline DF8003:Layer="0" fill="none" points="1748,-4507 1748,-4541 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="25127@x" ObjectIDND1="37592@x" ObjectIDND2="37593@x" ObjectIDZND0="25126@0" Pin0InfoVect0LinkObjId="SW-140334_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-140335_0" Pin1InfoVect1LinkObjId="SW-226824_0" Pin1InfoVect2LinkObjId="SW-226823_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1748,-4507 1748,-4541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ed4ed0">
     <polyline DF8003:Layer="0" fill="none" points="343,-4397 376,-4397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="transformer2" ObjectIDND0="10148@1" ObjectIDZND0="10146@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-55770_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55772_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="343,-4397 376,-4397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ed50e0">
     <polyline DF8003:Layer="0" fill="none" points="892,-3825 869,-3825 869,-3837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="25696@x" ObjectIDND1="g_3e880f0@0" ObjectIDND2="g_3dc6ff0@0" ObjectIDZND0="g_3e88970@0" Pin0InfoVect0LinkObjId="g_3e88970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-146782_0" Pin1InfoVect1LinkObjId="g_3e880f0_0" Pin1InfoVect2LinkObjId="g_3dc6ff0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="892,-3825 869,-3825 869,-3837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ed5310">
     <polyline DF8003:Layer="0" fill="none" points="892,-3825 910,-3825 910,-3831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="25696@x" ObjectIDND1="g_3e88970@0" ObjectIDND2="g_3dc6ff0@0" ObjectIDZND0="g_3e880f0@1" Pin0InfoVect0LinkObjId="g_3e880f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-146782_0" Pin1InfoVect1LinkObjId="g_3e88970_0" Pin1InfoVect2LinkObjId="g_3dc6ff0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="892,-3825 910,-3825 910,-3831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ed5570">
     <polyline DF8003:Layer="0" fill="none" points="2675,-4506 2859,-4506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3ed72b0@0" ObjectIDND1="27252@x" ObjectIDND2="28265@x" ObjectIDZND0="11813@x" ObjectIDZND1="11812@x" Pin0InfoVect0LinkObjId="SW-61724_0" Pin0InfoVect1LinkObjId="SW-61722_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ed72b0_0" Pin1InfoVect1LinkObjId="SW-171113_0" Pin1InfoVect2LinkObjId="SW-186630_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2675,-4506 2859,-4506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ed57d0">
     <polyline DF8003:Layer="0" fill="none" points="696,-4434 696,-4593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="27848@0" ObjectIDZND0="24878@0" Pin0InfoVect0LinkObjId="g_3e91a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183002_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="696,-4434 696,-4593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ed5a30">
     <polyline DF8003:Layer="0" fill="none" points="706,-4315 689,-4315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3dc7da0@1" ObjectIDZND0="g_3dc8fc0@0" Pin0InfoVect0LinkObjId="g_3dc8fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3dc7da0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="706,-4315 689,-4315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ed5c90">
     <polyline DF8003:Layer="0" fill="none" points="681,-3856 681,-3844 715,-3844 715,-3850 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3e86ad0@0" ObjectIDZND0="g_3e86500@1" Pin0InfoVect0LinkObjId="g_3e86500_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e86ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="681,-3856 681,-3844 715,-3844 715,-3850 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ed5ee0">
     <polyline DF8003:Layer="0" fill="none" points="856,-4741 829,-4741 829,-4753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="856,-4741 829,-4741 829,-4753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ed6110">
     <polyline DF8003:Layer="0" fill="none" points="732,-4764 708,-4764 708,-4787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_3ea4b70@0" Pin0InfoVect0LinkObjId="g_3ea4b70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="732,-4764 708,-4764 708,-4787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ed6370">
     <polyline DF8003:Layer="0" fill="none" points="339,-4268 358,-4268 358,-4237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3de7580@0" ObjectIDZND0="g_3de82f0@0" ObjectIDZND1="g_3df6a70@0" ObjectIDZND2="10147@x" Pin0InfoVect0LinkObjId="g_3de82f0_0" Pin0InfoVect1LinkObjId="g_3df6a70_0" Pin0InfoVect2LinkObjId="SW-55771_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3de7580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="339,-4268 358,-4268 358,-4237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ed65d0">
     <polyline DF8003:Layer="0" fill="none" points="696,-4328 696,-4340 712,-4340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" ObjectIDND0="27848@x" ObjectIDND1="g_3df6a70@0" ObjectIDND2="g_3edd4c0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-183002_0" Pin1InfoVect1LinkObjId="g_3df6a70_0" Pin1InfoVect2LinkObjId="g_3edd4c0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="696,-4328 696,-4340 712,-4340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ed6830">
     <polyline DF8003:Layer="0" fill="none" points="1939,-4549 1929,-4549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_3d55cf0@1" ObjectIDZND0="g_3ed2cd0@0" ObjectIDZND1="g_3df1280@0" ObjectIDZND2="g_3dcd910@0" Pin0InfoVect0LinkObjId="g_3ed2cd0_0" Pin0InfoVect1LinkObjId="g_3df1280_0" Pin0InfoVect2LinkObjId="g_3dcd910_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d55cf0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1939,-4549 1929,-4549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ed8fb0">
     <polyline DF8003:Layer="0" fill="none" points="2796,-4257 2850,-4257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_3ed72b0@0" ObjectIDND1="27252@x" ObjectIDND2="28265@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ed72b0_0" Pin1InfoVect1LinkObjId="SW-171113_0" Pin1InfoVect2LinkObjId="SW-186630_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2796,-4257 2850,-4257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ed91f0">
     <polyline DF8003:Layer="0" fill="none" points="3031,-4257 3050,-4257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27249@1" ObjectIDZND0="27244@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171110_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3031,-4257 3050,-4257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ed9450">
     <polyline DF8003:Layer="0" fill="none" points="2796,-4257 2796,-4217 2802,-4217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="27252@x" ObjectIDND1="28265@x" ObjectIDND2="11813@x" ObjectIDZND0="g_3ed72b0@0" Pin0InfoVect0LinkObjId="g_3ed72b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-171113_0" Pin1InfoVect1LinkObjId="SW-186630_0" Pin1InfoVect2LinkObjId="SW-61724_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2796,-4257 2796,-4217 2802,-4217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ed96b0">
     <polyline DF8003:Layer="0" fill="none" points="2806,-3688 2806,-3711 2776,-3711 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_3e4e950@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3e4e950_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2806,-3688 2806,-3711 2776,-3711 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3eda5c0">
     <polyline DF8003:Layer="0" fill="none" points="541,-4407 541,-4479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="load" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="541,-4407 541,-4479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3edeb90">
     <polyline DF8003:Layer="0" fill="none" points="747,-4340 767,-4340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27849@1" ObjectIDZND0="g_3eded80@0" Pin0InfoVect0LinkObjId="g_3eded80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183006_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="747,-4340 767,-4340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ee9dd0">
     <polyline DF8003:Layer="0" fill="none" points="2796,-4257 2796,-4275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3ed72b0@0" ObjectIDND1="28265@x" ObjectIDND2="11813@x" ObjectIDZND0="27252@0" Pin0InfoVect0LinkObjId="SW-171113_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ed72b0_0" Pin1InfoVect1LinkObjId="SW-186630_0" Pin1InfoVect2LinkObjId="SW-61724_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2796,-4257 2796,-4275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ee9fc0">
     <polyline DF8003:Layer="0" fill="none" points="2796,-4311 2796,-4322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27252@1" ObjectIDZND0="g_3eea1b0@0" Pin0InfoVect0LinkObjId="g_3eea1b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171113_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2796,-4311 2796,-4322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3eef3d0">
     <polyline DF8003:Layer="0" fill="none" points="721,-4515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="721,-4515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eef5c0">
     <polyline DF8003:Layer="0" fill="none" points="251,-4881 251,-4985 922,-4985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="29497@x" ObjectIDND2="29496@x" ObjectIDZND0="g_3ec77b0@0" ObjectIDZND1="g_3ec6080@0" Pin0InfoVect0LinkObjId="g_3ec77b0_0" Pin0InfoVect1LinkObjId="g_3ec6080_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-194235_0" Pin1InfoVect2LinkObjId="SW-194234_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="251,-4881 251,-4985 922,-4985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3efca50">
     <polyline DF8003:Layer="0" fill="none" points="359,-4669 359,-4679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3efc000@0" ObjectIDZND0="29498@0" Pin0InfoVect0LinkObjId="SW-194236_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3efc000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="359,-4669 359,-4679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3efccb0">
     <polyline DF8003:Layer="0" fill="none" points="359,-4715 359,-4740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29498@1" ObjectIDZND0="29496@x" ObjectIDZND1="29494@x" Pin0InfoVect0LinkObjId="SW-194234_0" Pin0InfoVect1LinkObjId="SW-194232_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194236_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="359,-4715 359,-4740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3efcf10">
     <polyline DF8003:Layer="0" fill="none" points="437,-4673 437,-4683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3ef8db0@0" ObjectIDZND0="29499@0" Pin0InfoVect0LinkObjId="SW-194237_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ef8db0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="437,-4673 437,-4683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f003c0">
     <polyline DF8003:Layer="0" fill="none" points="261,-4669 261,-4679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3eff970@0" ObjectIDZND0="29497@0" Pin0InfoVect0LinkObjId="SW-194235_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3eff970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="261,-4669 261,-4679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f013d0">
     <polyline DF8003:Layer="0" fill="none" points="323,-4872 251,-4872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="29497@x" ObjectIDZND1="29496@x" ObjectIDZND2="g_3f00620@0" Pin0InfoVect0LinkObjId="SW-194235_0" Pin0InfoVect1LinkObjId="SW-194234_0" Pin0InfoVect2LinkObjId="g_3f00620_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="323,-4872 251,-4872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f070b0">
     <polyline DF8003:Layer="0" fill="none" points="526,-4740 499,-4740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27684@0" ObjectIDZND0="29495@1" Pin0InfoVect0LinkObjId="SW-194233_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="526,-4740 499,-4740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f07310">
     <polyline DF8003:Layer="0" fill="none" points="359,-4740 336,-4740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="29498@x" ObjectIDND1="29494@x" ObjectIDZND0="29496@1" Pin0InfoVect0LinkObjId="SW-194234_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-194236_0" Pin1InfoVect1LinkObjId="SW-194232_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="359,-4740 336,-4740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f07570">
     <polyline DF8003:Layer="0" fill="none" points="381,-4740 359,-4740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29494@1" ObjectIDZND0="29498@x" ObjectIDZND1="29496@x" Pin0InfoVect0LinkObjId="SW-194236_0" Pin0InfoVect1LinkObjId="SW-194234_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194232_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="381,-4740 359,-4740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f08060">
     <polyline DF8003:Layer="0" fill="none" points="261,-4715 261,-4740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="29497@1" ObjectIDZND0="29496@x" ObjectIDZND1="0@x" ObjectIDZND2="g_3f00620@0" Pin0InfoVect0LinkObjId="SW-194234_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_3f00620_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194235_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="261,-4715 261,-4740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f08b50">
     <polyline DF8003:Layer="0" fill="none" points="300,-4740 261,-4740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="29496@0" ObjectIDZND0="29497@x" ObjectIDZND1="0@x" ObjectIDZND2="g_3f00620@0" Pin0InfoVect0LinkObjId="SW-194235_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_3f00620_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194234_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="300,-4740 261,-4740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f08db0">
     <polyline DF8003:Layer="0" fill="none" points="261,-4740 251,-4740 251,-4872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29497@x" ObjectIDND1="29496@x" ObjectIDZND0="0@x" ObjectIDZND1="g_3f00620@0" ObjectIDZND2="g_3ec77b0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3f00620_0" Pin0InfoVect2LinkObjId="g_3ec77b0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-194235_0" Pin1InfoVect1LinkObjId="SW-194234_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="261,-4740 251,-4740 251,-4872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f09010">
     <polyline DF8003:Layer="0" fill="none" points="437,-4719 437,-4740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29499@1" ObjectIDZND0="29494@x" ObjectIDZND1="29495@x" Pin0InfoVect0LinkObjId="SW-194232_0" Pin0InfoVect1LinkObjId="SW-194233_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194237_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="437,-4719 437,-4740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f09b00">
     <polyline DF8003:Layer="0" fill="none" points="408,-4740 437,-4740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29494@0" ObjectIDZND0="29499@x" ObjectIDZND1="29495@x" Pin0InfoVect0LinkObjId="SW-194237_0" Pin0InfoVect1LinkObjId="SW-194233_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194232_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="408,-4740 437,-4740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f09d60">
     <polyline DF8003:Layer="0" fill="none" points="437,-4740 463,-4740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="29499@x" ObjectIDND1="29494@x" ObjectIDZND0="29495@0" Pin0InfoVect0LinkObjId="SW-194233_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-194237_0" Pin1InfoVect1LinkObjId="SW-194232_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="437,-4740 463,-4740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f0ad10">
     <polyline DF8003:Layer="0" fill="none" points="2959,-4406 2959,-4389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="g_3eb94d0@0" ObjectIDZND0="28265@x" ObjectIDZND1="28262@0" Pin0InfoVect0LinkObjId="SW-186630_0" Pin0InfoVect1LinkObjId="g_3f0ba60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3eb94d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2959,-4406 2959,-4389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f0b800">
     <polyline DF8003:Layer="0" fill="none" points="2926,-4389 2959,-4389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="busSection" ObjectIDND0="28265@1" ObjectIDZND0="g_3eb94d0@0" ObjectIDZND1="28262@0" Pin0InfoVect0LinkObjId="g_3eb94d0_0" Pin0InfoVect1LinkObjId="g_3f0ba60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186630_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2926,-4389 2959,-4389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f0ba60">
     <polyline DF8003:Layer="0" fill="none" points="2959,-4389 3050,-4389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="g_3eb94d0@0" ObjectIDND1="28265@x" ObjectIDZND0="28262@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3eb94d0_0" Pin1InfoVect1LinkObjId="SW-186630_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2959,-4389 3050,-4389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f0bcc0">
     <polyline DF8003:Layer="0" fill="none" points="2995,-4257 2957,-4257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27249@0" ObjectIDZND0="27248@0" Pin0InfoVect0LinkObjId="SW-171109_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2995,-4257 2957,-4257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f0bf20">
     <polyline DF8003:Layer="0" fill="none" points="2888,-4257 2930,-4257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27250@1" ObjectIDZND0="27248@1" Pin0InfoVect0LinkObjId="SW-171109_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171111_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2888,-4257 2930,-4257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f0c180">
     <polyline DF8003:Layer="0" fill="none" points="2796,-4257 2675,-4257 2675,-4389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3ed72b0@0" ObjectIDND1="27252@x" ObjectIDZND0="28265@x" ObjectIDZND1="11813@x" ObjectIDZND2="11812@x" Pin0InfoVect0LinkObjId="SW-186630_0" Pin0InfoVect1LinkObjId="SW-61724_0" Pin0InfoVect2LinkObjId="SW-61722_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3ed72b0_0" Pin1InfoVect1LinkObjId="SW-171113_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2796,-4257 2675,-4257 2675,-4389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f0cc70">
     <polyline DF8003:Layer="0" fill="none" points="2675,-4506 2675,-4803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="11813@x" ObjectIDND1="11812@x" ObjectIDND2="g_3ed72b0@0" ObjectIDZND0="g_3df2d40@0" ObjectIDZND1="2949@x" Pin0InfoVect0LinkObjId="g_3df2d40_0" Pin0InfoVect1LinkObjId="SW-19285_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-61724_0" Pin1InfoVect1LinkObjId="SW-61722_0" Pin1InfoVect2LinkObjId="g_3ed72b0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2675,-4506 2675,-4803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f0d4a0">
     <polyline DF8003:Layer="0" fill="none" points="2856,-4711 2838,-4711 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="25063@0" ObjectIDZND0="25062@x" ObjectIDZND1="g_3d46540@0" ObjectIDZND2="g_3558550@0" Pin0InfoVect0LinkObjId="SW-139799_0" Pin0InfoVect1LinkObjId="g_3d46540_0" Pin0InfoVect2LinkObjId="g_3558550_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2856,-4711 2838,-4711 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f0e1b0">
     <polyline DF8003:Layer="0" fill="none" points="2838,-4711 2838,-4704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="25063@x" ObjectIDND1="g_3d46540@0" ObjectIDND2="g_3558550@0" ObjectIDZND0="25062@1" Pin0InfoVect0LinkObjId="SW-139799_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-139800_0" Pin1InfoVect1LinkObjId="g_3d46540_0" Pin1InfoVect2LinkObjId="g_3558550_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2838,-4711 2838,-4704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f0e410">
     <polyline DF8003:Layer="0" fill="none" points="2820,-4711 2838,-4711 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3d46540@0" ObjectIDZND0="25063@x" ObjectIDZND1="25062@x" ObjectIDZND2="g_3558550@0" Pin0InfoVect0LinkObjId="SW-139800_0" Pin0InfoVect1LinkObjId="SW-139799_0" Pin0InfoVect2LinkObjId="g_3558550_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d46540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2820,-4711 2838,-4711 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f0e670">
     <polyline DF8003:Layer="0" fill="none" points="2820,-4814 2838,-4814 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3558550@0" ObjectIDZND0="2946@x" ObjectIDZND1="25063@x" ObjectIDZND2="25062@x" Pin0InfoVect0LinkObjId="SW-19282_0" Pin0InfoVect1LinkObjId="SW-139800_0" Pin0InfoVect2LinkObjId="SW-139799_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3558550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2820,-4814 2838,-4814 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f0f160">
     <polyline DF8003:Layer="0" fill="none" points="2838,-4825 2838,-4814 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="2946@0" ObjectIDZND0="g_3558550@0" ObjectIDZND1="25063@x" ObjectIDZND2="25062@x" Pin0InfoVect0LinkObjId="g_3558550_0" Pin0InfoVect1LinkObjId="SW-139800_0" Pin0InfoVect2LinkObjId="SW-139799_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19282_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2838,-4825 2838,-4814 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f0f3c0">
     <polyline DF8003:Layer="0" fill="none" points="2838,-4814 2838,-4711 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3558550@0" ObjectIDND1="2946@x" ObjectIDZND0="25063@x" ObjectIDZND1="25062@x" ObjectIDZND2="g_3d46540@0" Pin0InfoVect0LinkObjId="SW-139800_0" Pin0InfoVect1LinkObjId="SW-139799_0" Pin0InfoVect2LinkObjId="g_3d46540_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3558550_0" Pin1InfoVect1LinkObjId="SW-19282_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2838,-4814 2838,-4711 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f11ed0">
     <polyline DF8003:Layer="0" fill="none" points="2432,-4529 2432,-4539 2463,-4539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3df54a0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_3df49c0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_3df49c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3df54a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2432,-4529 2432,-4539 2463,-4539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f129c0">
     <polyline DF8003:Layer="0" fill="none" points="2503,-4778 2503,-4798 2463,-4798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3df49c0@1" ObjectIDZND0="2955@x" ObjectIDZND1="g_3df54a0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-19291_0" Pin0InfoVect1LinkObjId="g_3df54a0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3df49c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2503,-4778 2503,-4798 2463,-4798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f12c20">
     <polyline DF8003:Layer="0" fill="none" points="2463,-4798 2463,-4810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3df49c0@0" ObjectIDND1="g_3df54a0@0" ObjectIDND2="0@x" ObjectIDZND0="2955@0" Pin0InfoVect0LinkObjId="SW-19291_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3df49c0_0" Pin1InfoVect1LinkObjId="g_3df54a0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2463,-4798 2463,-4810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f13710">
     <polyline DF8003:Layer="0" fill="none" points="2463,-4539 2463,-4521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3df54a0@0" ObjectIDND1="g_3df49c0@0" ObjectIDND2="2955@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3df54a0_0" Pin1InfoVect1LinkObjId="g_3df49c0_0" Pin1InfoVect2LinkObjId="SW-19291_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2463,-4539 2463,-4521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f147d0">
     <polyline DF8003:Layer="0" fill="none" points="3013,-3710 3020,-3710 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_3e29ac0@0" ObjectIDZND0="g_3e28d10@0" ObjectIDZND1="g_3e2a870@0" ObjectIDZND2="g_3e280f0@0" Pin0InfoVect0LinkObjId="g_3e28d10_0" Pin0InfoVect1LinkObjId="g_3e2a870_0" Pin0InfoVect2LinkObjId="g_3e280f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e29ac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3013,-3710 3020,-3710 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f14a30">
     <polyline DF8003:Layer="0" fill="none" points="3020,-3710 3020,-3700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3e28d10@0" ObjectIDND1="g_3e2a870@0" ObjectIDND2="g_3e29ac0@0" ObjectIDZND0="g_3e280f0@1" Pin0InfoVect0LinkObjId="g_3e280f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3e28d10_0" Pin1InfoVect1LinkObjId="g_3e2a870_0" Pin1InfoVect2LinkObjId="g_3e29ac0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3020,-3710 3020,-3700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f15520">
     <polyline DF8003:Layer="0" fill="none" points="2890,-4389 2675,-4389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="28265@0" ObjectIDZND0="g_3ed72b0@0" ObjectIDZND1="27252@x" ObjectIDZND2="11813@x" Pin0InfoVect0LinkObjId="g_3ed72b0_0" Pin0InfoVect1LinkObjId="SW-171113_0" Pin0InfoVect2LinkObjId="SW-61724_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2890,-4389 2675,-4389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f15780">
     <polyline DF8003:Layer="0" fill="none" points="2675,-4389 2675,-4506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3ed72b0@0" ObjectIDND1="27252@x" ObjectIDND2="28265@x" ObjectIDZND0="11813@x" ObjectIDZND1="11812@x" ObjectIDZND2="g_3df2d40@0" Pin0InfoVect0LinkObjId="SW-61724_0" Pin0InfoVect1LinkObjId="SW-61722_0" Pin0InfoVect2LinkObjId="g_3df2d40_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ed72b0_0" Pin1InfoVect1LinkObjId="SW-171113_0" Pin1InfoVect2LinkObjId="SW-186630_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2675,-4389 2675,-4506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f16580">
     <polyline DF8003:Layer="0" fill="none" points="2463,-4565 2586,-4565 2586,-3701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3df49c0@0" ObjectIDND1="2955@x" ObjectIDND2="g_3df54a0@0" ObjectIDZND0="21914@x" ObjectIDZND1="21916@x" Pin0InfoVect0LinkObjId="SW-116068_0" Pin0InfoVect1LinkObjId="SW-116070_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3df49c0_0" Pin1InfoVect1LinkObjId="SW-19291_0" Pin1InfoVect2LinkObjId="g_3df54a0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2463,-4565 2586,-4565 2586,-3701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f17070">
     <polyline DF8003:Layer="0" fill="none" points="1331,-3932 1351,-3932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="25259@1" ObjectIDZND0="25258@x" ObjectIDZND1="25696@x" ObjectIDZND2="g_3e88970@0" Pin0InfoVect0LinkObjId="SW-141437_0" Pin0InfoVect1LinkObjId="SW-146782_0" Pin0InfoVect2LinkObjId="g_3e88970_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141438_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1331,-3932 1351,-3932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f172d0">
     <polyline DF8003:Layer="0" fill="none" points="1351,-3932 1351,-3917 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="25259@x" ObjectIDND1="25696@x" ObjectIDND2="g_3e88970@0" ObjectIDZND0="25258@1" Pin0InfoVect0LinkObjId="SW-141437_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-141438_0" Pin1InfoVect1LinkObjId="SW-146782_0" Pin1InfoVect2LinkObjId="g_3e88970_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-3932 1351,-3917 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f17530">
     <polyline DF8003:Layer="0" fill="none" points="1351,-3975 1351,-4050 892,-4050 892,-3825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_3dc6ff0@0" ObjectIDND1="25259@x" ObjectIDND2="25258@x" ObjectIDZND0="25696@x" ObjectIDZND1="g_3e88970@0" ObjectIDZND2="g_3e880f0@0" Pin0InfoVect0LinkObjId="SW-146782_0" Pin0InfoVect1LinkObjId="g_3e88970_0" Pin0InfoVect2LinkObjId="g_3e880f0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3dc6ff0_0" Pin1InfoVect1LinkObjId="SW-141438_0" Pin1InfoVect2LinkObjId="SW-141437_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-3975 1351,-4050 892,-4050 892,-3825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f177a0">
     <polyline DF8003:Layer="0" fill="none" points="1918,-4533 1929,-4533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_3ed2cd0@0" ObjectIDZND0="g_3d55cf0@0" ObjectIDZND1="g_3d55cf0@0" ObjectIDZND2="g_3ed2cd0@0" Pin0InfoVect0LinkObjId="g_3d55cf0_0" Pin0InfoVect1LinkObjId="g_3d55cf0_0" Pin0InfoVect2LinkObjId="g_3ed2cd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ed2cd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1918,-4533 1929,-4533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f17a00">
     <polyline DF8003:Layer="0" fill="none" points="1929,-4547 1929,-4533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_3d55cf0@0" ObjectIDND1="g_3d55cf0@0" ObjectIDND2="g_3ed2cd0@0" ObjectIDZND0="g_3ed2cd0@0" ObjectIDZND1="g_3df1280@0" ObjectIDZND2="g_3dcd910@0" Pin0InfoVect0LinkObjId="g_3ed2cd0_0" Pin0InfoVect1LinkObjId="g_3df1280_0" Pin0InfoVect2LinkObjId="g_3dcd910_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d55cf0_0" Pin1InfoVect1LinkObjId="g_3d55cf0_0" Pin1InfoVect2LinkObjId="g_3ed2cd0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1929,-4547 1929,-4533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f17c60">
     <polyline DF8003:Layer="0" fill="none" points="1929,-4533 1929,-4498 2110,-4498 2110,-4840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3d55cf0@0" ObjectIDND1="g_3d55cf0@0" ObjectIDND2="g_3ed2cd0@0" ObjectIDZND0="g_3df1280@0" ObjectIDZND1="g_3dcd910@0" ObjectIDZND2="24973@x" Pin0InfoVect0LinkObjId="g_3df1280_0" Pin0InfoVect1LinkObjId="g_3dcd910_0" Pin0InfoVect2LinkObjId="SW-138859_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d55cf0_0" Pin1InfoVect1LinkObjId="g_3d55cf0_0" Pin1InfoVect2LinkObjId="g_3ed2cd0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1929,-4533 1929,-4498 2110,-4498 2110,-4840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f17ed0">
     <polyline DF8003:Layer="0" fill="none" points="1929,-4543 1929,-4549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_3d55cf0@0" ObjectIDND1="g_3ed2cd0@0" ObjectIDND2="g_3df1280@0" ObjectIDZND0="g_3d55cf0@0" ObjectIDZND1="g_3ed2cd0@0" ObjectIDZND2="g_3df1280@0" Pin0InfoVect0LinkObjId="g_3d55cf0_0" Pin0InfoVect1LinkObjId="g_3ed2cd0_0" Pin0InfoVect2LinkObjId="g_3df1280_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d55cf0_0" Pin1InfoVect1LinkObjId="g_3ed2cd0_0" Pin1InfoVect2LinkObjId="g_3df1280_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1929,-4543 1929,-4549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f18130">
     <polyline DF8003:Layer="0" fill="none" points="1929,-4549 1929,-4563 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3d55cf0@0" ObjectIDND1="g_3ed2cd0@0" ObjectIDND2="g_3df1280@0" ObjectIDZND0="25121@0" Pin0InfoVect0LinkObjId="SW-140282_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d55cf0_0" Pin1InfoVect1LinkObjId="g_3ed2cd0_0" Pin1InfoVect2LinkObjId="g_3df1280_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1929,-4549 1929,-4563 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f1c3c0">
     <polyline DF8003:Layer="0" fill="none" points="2247,-3938 2257,-3938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_32b4d70@1" Pin0InfoVect0LinkObjId="g_32b4d70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2247,-3938 2257,-3938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f1c620">
     <polyline DF8003:Layer="0" fill="none" points="2288,-3938 2298,-3938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_32b4d70@0" ObjectIDZND0="g_32b4050@0" ObjectIDZND1="g_4234210@0" ObjectIDZND2="g_4232d90@0" Pin0InfoVect0LinkObjId="g_32b4050_0" Pin0InfoVect1LinkObjId="g_4234210_0" Pin0InfoVect2LinkObjId="g_4232d90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32b4d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2288,-3938 2298,-3938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f1d110">
     <polyline DF8003:Layer="0" fill="none" points="2463,-4798 2463,-4565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3df49c0@0" ObjectIDND1="2955@x" ObjectIDZND0="g_3df54a0@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3df54a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3df49c0_0" Pin1InfoVect1LinkObjId="SW-19291_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2463,-4798 2463,-4565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f1d370">
     <polyline DF8003:Layer="0" fill="none" points="2463,-4565 2463,-4539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3df49c0@0" ObjectIDND1="2955@x" ObjectIDND2="21914@x" ObjectIDZND0="g_3df54a0@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3df54a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3df49c0_0" Pin1InfoVect1LinkObjId="SW-19291_0" Pin1InfoVect2LinkObjId="SW-116068_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2463,-4565 2463,-4539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f1f2e0">
     <polyline DF8003:Layer="0" fill="none" points="541,-4252 541,-4341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="g_3edd4c0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_3ed9910@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3ed9910_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3edd4c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="541,-4252 541,-4341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f1f540">
     <polyline DF8003:Layer="0" fill="none" points="696,-4328 696,-4184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27848@x" ObjectIDZND0="g_3df6a70@0" ObjectIDZND1="g_3edd4c0@0" ObjectIDZND2="25688@x" Pin0InfoVect0LinkObjId="g_3df6a70_0" Pin0InfoVect1LinkObjId="g_3edd4c0_0" Pin0InfoVect2LinkObjId="SW-146736_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-183002_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="696,-4328 696,-4184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f1f7a0">
     <polyline DF8003:Layer="0" fill="none" points="696,-4184 540,-4184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="27848@x" ObjectIDND1="25688@x" ObjectIDND2="25193@x" ObjectIDZND0="g_3df6a70@0" ObjectIDZND1="g_3edd4c0@0" Pin0InfoVect0LinkObjId="g_3df6a70_0" Pin0InfoVect1LinkObjId="g_3edd4c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-183002_0" Pin1InfoVect1LinkObjId="SW-146736_0" Pin1InfoVect2LinkObjId="SW-140980_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="696,-4184 540,-4184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f216c0">
     <polyline DF8003:Layer="0" fill="none" points="541,-4355 541,-4341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3edd4c0@0" ObjectIDZND1="g_3ed9910@0" Pin0InfoVect0LinkObjId="g_3edd4c0_0" Pin0InfoVect1LinkObjId="g_3ed9910_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="541,-4355 541,-4341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f21920">
     <polyline DF8003:Layer="0" fill="none" points="541,-4341 555,-4341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_3edd4c0@0" ObjectIDZND0="g_3ed9910@0" Pin0InfoVect0LinkObjId="g_3ed9910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3edd4c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="541,-4341 555,-4341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f26400">
     <polyline DF8003:Layer="0" fill="none" points="1338,-4362 1338,-4444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_3d9a010@0" ObjectIDZND1="g_3e617b0@0" ObjectIDZND2="4597@x" Pin0InfoVect0LinkObjId="g_3d9a010_0" Pin0InfoVect1LinkObjId="g_3e617b0_0" Pin0InfoVect2LinkObjId="SW-28392_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1338,-4362 1338,-4444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f26660">
     <polyline DF8003:Layer="0" fill="none" points="1338,-4444 1338,-4989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDND1="25131@x" ObjectIDND2="25130@x" ObjectIDZND0="g_3d9a010@0" ObjectIDZND1="g_3e617b0@0" ObjectIDZND2="4597@x" Pin0InfoVect0LinkObjId="g_3d9a010_0" Pin0InfoVect1LinkObjId="g_3e617b0_0" Pin0InfoVect2LinkObjId="SW-28392_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-140386_0" Pin1InfoVect2LinkObjId="SW-140385_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1338,-4444 1338,-4989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f268c0">
     <polyline DF8003:Layer="0" fill="none" points="376,-4397 376,-4382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="10148@x" ObjectIDND1="0@x" ObjectIDZND0="10146@1" Pin0InfoVect0LinkObjId="SW-55770_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-55772_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="376,-4397 376,-4382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f26b20">
     <polyline DF8003:Layer="0" fill="none" points="376,-4490 376,-4397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="10148@x" ObjectIDZND1="10146@x" Pin0InfoVect0LinkObjId="SW-55772_0" Pin0InfoVect1LinkObjId="SW-55770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="376,-4490 376,-4397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f26d80">
     <polyline DF8003:Layer="0" fill="none" points="376,-4303 376,-4237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="10147@0" ObjectIDZND0="g_3df6a70@0" ObjectIDZND1="g_3de7580@0" ObjectIDZND2="g_3de82f0@0" Pin0InfoVect0LinkObjId="g_3df6a70_0" Pin0InfoVect1LinkObjId="g_3de7580_0" Pin0InfoVect2LinkObjId="g_3de82f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55771_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="376,-4303 376,-4237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f26fe0">
     <polyline DF8003:Layer="0" fill="none" points="358,-4237 376,-4237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3de7580@0" ObjectIDND1="g_3de82f0@0" ObjectIDZND0="g_3df6a70@0" ObjectIDZND1="10147@x" Pin0InfoVect0LinkObjId="g_3df6a70_0" Pin0InfoVect1LinkObjId="SW-55771_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3de7580_0" Pin1InfoVect1LinkObjId="g_3de82f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="358,-4237 376,-4237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f27240">
     <polyline DF8003:Layer="0" fill="none" points="376,-4237 376,-4185 485,-4185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="10147@x" ObjectIDND1="g_3de7580@0" ObjectIDND2="g_3de82f0@0" ObjectIDZND0="g_3df6a70@0" Pin0InfoVect0LinkObjId="g_3df6a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-55771_0" Pin1InfoVect1LinkObjId="g_3de7580_0" Pin1InfoVect2LinkObjId="g_3de82f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="376,-4237 376,-4185 485,-4185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f274a0">
     <polyline DF8003:Layer="0" fill="none" points="526,-4184 540,-4184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_3df6a70@1" ObjectIDZND0="g_3edd4c0@0" ObjectIDZND1="27848@x" ObjectIDZND2="25688@x" Pin0InfoVect0LinkObjId="g_3edd4c0_0" Pin0InfoVect1LinkObjId="SW-183002_0" Pin0InfoVect2LinkObjId="SW-146736_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3df6a70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="526,-4184 540,-4184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f27700">
     <polyline DF8003:Layer="0" fill="none" points="540,-4184 540,-4209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3df6a70@0" ObjectIDND1="27848@x" ObjectIDND2="25688@x" ObjectIDZND0="g_3edd4c0@0" Pin0InfoVect0LinkObjId="g_3edd4c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3df6a70_0" Pin1InfoVect1LinkObjId="SW-183002_0" Pin1InfoVect2LinkObjId="SW-146736_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="540,-4184 540,-4209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f27960">
     <polyline DF8003:Layer="0" fill="none" points="696,-4050 696,-4039 696,-3819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="25193@x" ObjectIDND1="25194@x" ObjectIDND2="g_3e853f0@0" ObjectIDZND0="25688@1" Pin0InfoVect0LinkObjId="SW-146736_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-140980_0" Pin1InfoVect1LinkObjId="SW-140981_0" Pin1InfoVect2LinkObjId="g_3e853f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="696,-4050 696,-4039 696,-3819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f27bc0">
     <polyline DF8003:Layer="0" fill="none" points="339,-4050 357,-4050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25191@0" ObjectIDZND0="25193@0" Pin0InfoVect0LinkObjId="SW-140980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140978_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="339,-4050 357,-4050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f2b030">
     <polyline DF8003:Layer="0" fill="none" points="393,-4050 418,-4050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="25193@1" ObjectIDZND0="25194@x" ObjectIDZND1="25688@x" ObjectIDZND2="27848@x" Pin0InfoVect0LinkObjId="SW-140981_0" Pin0InfoVect1LinkObjId="SW-146736_0" Pin0InfoVect2LinkObjId="SW-183002_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140980_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="393,-4050 418,-4050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f2b290">
     <polyline DF8003:Layer="0" fill="none" points="418,-3990 418,-4002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3f2a5e0@0" ObjectIDZND0="25194@0" Pin0InfoVect0LinkObjId="SW-140981_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f2a5e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="418,-3990 418,-4002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f2b4f0">
     <polyline DF8003:Layer="0" fill="none" points="418,-4038 418,-4050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="25194@1" ObjectIDZND0="25193@x" ObjectIDZND1="25688@x" ObjectIDZND2="27848@x" Pin0InfoVect0LinkObjId="SW-140980_0" Pin0InfoVect1LinkObjId="SW-146736_0" Pin0InfoVect2LinkObjId="SW-183002_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140981_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="418,-4038 418,-4050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f31ff0">
     <polyline DF8003:Layer="0" fill="none" points="487,-4059 487,-4050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3e853f0@1" ObjectIDZND0="25193@x" ObjectIDZND1="25194@x" ObjectIDZND2="25688@x" Pin0InfoVect0LinkObjId="SW-140980_0" Pin0InfoVect1LinkObjId="SW-140981_0" Pin0InfoVect2LinkObjId="SW-146736_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e853f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="487,-4059 487,-4050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f32d00">
     <polyline DF8003:Layer="0" fill="none" points="418,-4050 487,-4050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="25193@x" ObjectIDND1="25194@x" ObjectIDZND0="25688@x" ObjectIDZND1="27848@x" ObjectIDZND2="g_3df6a70@0" Pin0InfoVect0LinkObjId="SW-146736_0" Pin0InfoVect1LinkObjId="SW-183002_0" Pin0InfoVect2LinkObjId="g_3df6a70_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-140980_0" Pin1InfoVect1LinkObjId="SW-140981_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="418,-4050 487,-4050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f32f60">
     <polyline DF8003:Layer="0" fill="none" points="487,-4050 696,-4050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="25193@x" ObjectIDND1="25194@x" ObjectIDND2="g_3e853f0@0" ObjectIDZND0="25688@x" ObjectIDZND1="27848@x" ObjectIDZND2="g_3df6a70@0" Pin0InfoVect0LinkObjId="SW-146736_0" Pin0InfoVect1LinkObjId="SW-183002_0" Pin0InfoVect2LinkObjId="g_3df6a70_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-140980_0" Pin1InfoVect1LinkObjId="SW-140981_0" Pin1InfoVect2LinkObjId="g_3e853f0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="487,-4050 696,-4050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f331c0">
     <polyline DF8003:Layer="0" fill="none" points="487,-4036 487,-4050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3f312c0@0" ObjectIDZND0="25193@x" ObjectIDZND1="25194@x" ObjectIDZND2="25688@x" Pin0InfoVect0LinkObjId="SW-140980_0" Pin0InfoVect1LinkObjId="SW-140981_0" Pin0InfoVect2LinkObjId="SW-146736_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f312c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="487,-4036 487,-4050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f33420">
     <polyline DF8003:Layer="0" fill="none" points="696,-4184 696,-4050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="27848@x" ObjectIDND1="g_3df6a70@0" ObjectIDND2="g_3edd4c0@0" ObjectIDZND0="25688@x" ObjectIDZND1="25193@x" ObjectIDZND2="25194@x" Pin0InfoVect0LinkObjId="SW-146736_0" Pin0InfoVect1LinkObjId="SW-140980_0" Pin0InfoVect2LinkObjId="SW-140981_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-183002_0" Pin1InfoVect1LinkObjId="g_3df6a70_0" Pin1InfoVect2LinkObjId="g_3edd4c0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="696,-4184 696,-4050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f33f10">
     <polyline DF8003:Layer="0" fill="none" points="1078,-4994 1078,-5002 1116,-5002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="29441@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-193560_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1078,-4994 1078,-5002 1116,-5002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f34170">
     <polyline DF8003:Layer="0" fill="none" points="1116,-5002 1116,-5011 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="29441@x" ObjectIDZND1="g_3db2810@0" Pin0InfoVect0LinkObjId="SW-193560_0" Pin0InfoVect1LinkObjId="g_3db2810_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1116,-5002 1116,-5011 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f34c60">
     <polyline DF8003:Layer="0" fill="none" points="1338,-5020 1338,-4989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="busSection" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3d9a010@0" ObjectIDND1="g_3e617b0@0" ObjectIDND2="4597@x" ObjectIDZND0="0@0" ObjectIDZND1="25131@x" ObjectIDZND2="25130@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-140386_0" Pin0InfoVect2LinkObjId="SW-140385_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d9a010_0" Pin1InfoVect1LinkObjId="g_3e617b0_0" Pin1InfoVect2LinkObjId="SW-28392_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1338,-5020 1338,-4989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f34ec0">
     <polyline DF8003:Layer="0" fill="none" points="1338,-4989 1375,-4989 1375,-4981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDND1="25131@x" ObjectIDND2="25130@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-140386_0" Pin1InfoVect2LinkObjId="SW-140385_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1338,-4989 1375,-4989 1375,-4981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f359b0">
     <polyline DF8003:Layer="0" fill="none" points="1587,-4982 1587,-4990 1610,-4990 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="4600@x" ObjectIDZND1="g_3e6a980@0" ObjectIDZND2="g_42b14d0@0" Pin0InfoVect0LinkObjId="SW-28404_0" Pin0InfoVect1LinkObjId="g_3e6a980_0" Pin0InfoVect2LinkObjId="g_42b14d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1587,-4982 1587,-4990 1610,-4990 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f35c10">
     <polyline DF8003:Layer="0" fill="none" points="1610,-4990 1610,-5008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_42b14d0@0" ObjectIDND2="24970@x" ObjectIDZND0="4600@x" ObjectIDZND1="g_3e6a980@0" Pin0InfoVect0LinkObjId="SW-28404_0" Pin0InfoVect1LinkObjId="g_3e6a980_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_42b14d0_0" Pin1InfoVect2LinkObjId="SW-138808_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1610,-4990 1610,-5008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f36700">
     <polyline DF8003:Layer="0" fill="none" points="2098,-4840 2110,-4840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_3df1280@1" ObjectIDZND0="g_3d55cf0@0" ObjectIDZND1="g_3d55cf0@0" ObjectIDZND2="g_3ed2cd0@0" Pin0InfoVect0LinkObjId="g_3d55cf0_0" Pin0InfoVect1LinkObjId="g_3d55cf0_0" Pin0InfoVect2LinkObjId="g_3ed2cd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3df1280_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2098,-4840 2110,-4840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f36960">
     <polyline DF8003:Layer="0" fill="none" points="2110,-4840 2110,-4876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3d55cf0@0" ObjectIDND1="g_3d55cf0@0" ObjectIDND2="g_3ed2cd0@0" ObjectIDZND0="g_3dcd910@0" ObjectIDZND1="24973@x" ObjectIDZND2="24976@x" Pin0InfoVect0LinkObjId="g_3dcd910_0" Pin0InfoVect1LinkObjId="SW-138859_0" Pin0InfoVect2LinkObjId="SW-138862_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d55cf0_0" Pin1InfoVect1LinkObjId="g_3d55cf0_0" Pin1InfoVect2LinkObjId="g_3ed2cd0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2110,-4840 2110,-4876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f37a20">
     <polyline DF8003:Layer="0" fill="none" points="1569,-4529 1569,-4444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="25131@x" ObjectIDND1="25130@x" ObjectIDZND0="g_3d9a010@0" ObjectIDZND1="g_3e617b0@0" ObjectIDZND2="4597@x" Pin0InfoVect0LinkObjId="g_3d9a010_0" Pin0InfoVect1LinkObjId="g_3e617b0_0" Pin0InfoVect2LinkObjId="SW-28392_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-140386_0" Pin1InfoVect1LinkObjId="SW-140385_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1569,-4529 1569,-4444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f37c80">
     <polyline DF8003:Layer="0" fill="none" points="1569,-4444 1339,-4444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="25131@x" ObjectIDND1="25130@x" ObjectIDND2="g_3d83820@0" ObjectIDZND0="g_3d9a010@0" ObjectIDZND1="g_3e617b0@0" ObjectIDZND2="4597@x" Pin0InfoVect0LinkObjId="g_3d9a010_0" Pin0InfoVect1LinkObjId="g_3e617b0_0" Pin0InfoVect2LinkObjId="SW-28392_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-140386_0" Pin1InfoVect1LinkObjId="SW-140385_0" Pin1InfoVect2LinkObjId="g_3d83820_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1569,-4444 1339,-4444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f38d40">
     <polyline DF8003:Layer="0" fill="none" points="1569,-4237 1569,-4224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="20763@x" ObjectIDND1="20754@x" ObjectIDZND0="20761@1" Pin0InfoVect0LinkObjId="SW-106525_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106527_0" Pin1InfoVect1LinkObjId="SW-106518_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1569,-4237 1569,-4224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f38fa0">
     <polyline DF8003:Layer="0" fill="none" points="1569,-4247 1569,-4237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20754@0" ObjectIDZND0="20763@x" ObjectIDZND1="20761@x" Pin0InfoVect0LinkObjId="SW-106527_0" Pin0InfoVect1LinkObjId="SW-106525_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106518_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1569,-4247 1569,-4237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f39a90">
     <polyline DF8003:Layer="0" fill="none" points="1569,-4332 1569,-4323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="20762@x" ObjectIDND1="20758@x" ObjectIDND2="g_3d83820@0" ObjectIDZND0="20760@1" Pin0InfoVect0LinkObjId="SW-106524_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-106526_0" Pin1InfoVect1LinkObjId="SW-106522_0" Pin1InfoVect2LinkObjId="g_3d83820_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1569,-4332 1569,-4323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f3a580">
     <polyline DF8003:Layer="0" fill="none" points="1569,-4341 1569,-4332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20758@x" ObjectIDND1="g_3d83820@0" ObjectIDND2="25131@x" ObjectIDZND0="20762@x" ObjectIDZND1="20760@x" Pin0InfoVect0LinkObjId="SW-106526_0" Pin0InfoVect1LinkObjId="SW-106524_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-106522_0" Pin1InfoVect1LinkObjId="g_3d83820_0" Pin1InfoVect2LinkObjId="SW-140386_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1569,-4341 1569,-4332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f3b070">
     <polyline DF8003:Layer="0" fill="none" points="1569,-4444 1569,-4355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25131@x" ObjectIDND1="25130@x" ObjectIDND2="g_3d9a010@0" ObjectIDZND0="g_3d83820@0" ObjectIDZND1="20758@x" ObjectIDZND2="20762@x" Pin0InfoVect0LinkObjId="g_3d83820_0" Pin0InfoVect1LinkObjId="SW-106522_0" Pin0InfoVect2LinkObjId="SW-106526_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-140386_0" Pin1InfoVect1LinkObjId="SW-140385_0" Pin1InfoVect2LinkObjId="g_3d9a010_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1569,-4444 1569,-4355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f3b2d0">
     <polyline DF8003:Layer="0" fill="none" points="1569,-4355 1569,-4341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3d83820@0" ObjectIDND1="25131@x" ObjectIDND2="25130@x" ObjectIDZND0="20758@x" ObjectIDZND1="20762@x" ObjectIDZND2="20760@x" Pin0InfoVect0LinkObjId="SW-106522_0" Pin0InfoVect1LinkObjId="SW-106526_0" Pin0InfoVect2LinkObjId="SW-106524_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d83820_0" Pin1InfoVect1LinkObjId="SW-140386_0" Pin1InfoVect2LinkObjId="SW-140385_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1569,-4355 1569,-4341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f3bdc0">
     <polyline DF8003:Layer="0" fill="none" points="251,-4872 251,-4881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="29497@x" ObjectIDND2="29496@x" ObjectIDZND0="g_3f00620@0" ObjectIDZND1="g_3ec77b0@0" ObjectIDZND2="g_3ec6080@0" Pin0InfoVect0LinkObjId="g_3f00620_0" Pin0InfoVect1LinkObjId="g_3ec77b0_0" Pin0InfoVect2LinkObjId="g_3ec6080_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-194235_0" Pin1InfoVect2LinkObjId="SW-194234_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="251,-4872 251,-4881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f3c020">
     <polyline DF8003:Layer="0" fill="none" points="251,-4881 217,-4881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="29497@x" ObjectIDND2="29496@x" ObjectIDZND0="g_3f00620@0" Pin0InfoVect0LinkObjId="g_3f00620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-194235_0" Pin1InfoVect2LinkObjId="SW-194234_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="251,-4881 217,-4881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f3cb10">
     <polyline DF8003:Layer="0" fill="none" points="922,-4956 922,-4985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3ec77b0@0" ObjectIDZND0="g_3ec6080@0" ObjectIDZND1="0@x" ObjectIDZND2="29497@x" Pin0InfoVect0LinkObjId="g_3ec6080_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-194235_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ec77b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="922,-4956 922,-4985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f3cd70">
     <polyline DF8003:Layer="0" fill="none" points="922,-4985 950,-4985 950,-5012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3ec77b0@0" ObjectIDND1="0@x" ObjectIDND2="29497@x" ObjectIDZND0="g_3ec6080@0" Pin0InfoVect0LinkObjId="g_3ec6080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ec77b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-194235_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="922,-4985 950,-4985 950,-5012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f3d860">
     <polyline DF8003:Layer="0" fill="none" points="2586,-3674 2586,-3700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="21914@1" ObjectIDZND0="21916@x" ObjectIDZND1="g_3df49c0@0" ObjectIDZND2="2955@x" Pin0InfoVect0LinkObjId="SW-116070_0" Pin0InfoVect1LinkObjId="g_3df49c0_0" Pin0InfoVect2LinkObjId="SW-19291_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116068_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2586,-3674 2586,-3700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f3dac0">
     <polyline DF8003:Layer="0" fill="none" points="2586,-3700 2595,-3700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="21914@x" ObjectIDND1="g_3df49c0@0" ObjectIDND2="2955@x" ObjectIDZND0="21916@0" Pin0InfoVect0LinkObjId="SW-116070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-116068_0" Pin1InfoVect1LinkObjId="g_3df49c0_0" Pin1InfoVect2LinkObjId="SW-19291_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2586,-3700 2595,-3700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f40e60">
     <polyline DF8003:Layer="0" fill="none" points="1341,-3975 1351,-3975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_3dc6ff0@0" ObjectIDZND0="25696@x" ObjectIDZND1="g_3e88970@0" ObjectIDZND2="g_3e880f0@0" Pin0InfoVect0LinkObjId="SW-146782_0" Pin0InfoVect1LinkObjId="g_3e88970_0" Pin0InfoVect2LinkObjId="g_3e880f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3dc6ff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1341,-3975 1351,-3975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f410c0">
     <polyline DF8003:Layer="0" fill="none" points="1351,-3975 1351,-3931 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25696@x" ObjectIDND1="g_3e88970@0" ObjectIDND2="g_3e880f0@0" ObjectIDZND0="25259@x" ObjectIDZND1="25258@x" Pin0InfoVect0LinkObjId="SW-141438_0" Pin0InfoVect1LinkObjId="SW-141437_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-146782_0" Pin1InfoVect1LinkObjId="g_3e88970_0" Pin1InfoVect2LinkObjId="g_3e880f0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-3975 1351,-3931 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f4d0f0">
     <polyline DF8003:Layer="0" fill="none" points="1991,-4331 2000,-4331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37600@1" ObjectIDZND0="37596@1" Pin0InfoVect0LinkObjId="SW-226859_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226860_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1991,-4331 2000,-4331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f4d2e0">
     <polyline DF8003:Layer="0" fill="none" points="2037,-4331 2027,-4331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37598@0" ObjectIDZND0="37596@0" Pin0InfoVect0LinkObjId="SW-226859_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226861_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2037,-4331 2027,-4331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f4d7f0">
     <polyline DF8003:Layer="0" fill="none" points="1955,-4331 1945,-4331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37600@0" ObjectIDZND0="25019@0" Pin0InfoVect0LinkObjId="g_3f51b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1955,-4331 1945,-4331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f51690">
     <polyline DF8003:Layer="0" fill="none" points="2054,-4377 2043,-4376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3f4d9e0@0" Pin0InfoVect0LinkObjId="g_3f4d9e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2054,-4377 2043,-4376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f518f0">
     <polyline DF8003:Layer="0" fill="none" points="2000,-4377 2012,-4376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="37601@1" ObjectIDZND0="g_3f4d9e0@1" Pin0InfoVect0LinkObjId="g_3f4d9e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226898_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2000,-4377 2012,-4376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f51b50">
     <polyline DF8003:Layer="0" fill="none" points="1964,-4377 1945,-4377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37601@0" ObjectIDZND0="25019@0" Pin0InfoVect0LinkObjId="g_3f4d7f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226898_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1964,-4377 1945,-4377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f55210">
     <polyline DF8003:Layer="0" fill="none" points="2085,-4282 2085,-4270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="37599@0" ObjectIDZND0="g_3f523e0@0" Pin0InfoVect0LinkObjId="g_3f523e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226862_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2085,-4282 2085,-4270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f55470">
     <polyline DF8003:Layer="0" fill="none" points="2085,-4318 2085,-4331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="37599@1" ObjectIDZND0="37598@x" ObjectIDZND1="37597@x" ObjectIDZND2="25266@x" Pin0InfoVect0LinkObjId="SW-226861_0" Pin0InfoVect1LinkObjId="SW-226863_0" Pin0InfoVect2LinkObjId="SW-141473_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226862_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2085,-4318 2085,-4331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f556d0">
     <polyline DF8003:Layer="0" fill="none" points="2073,-4331 2085,-4331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="37598@1" ObjectIDZND0="37599@x" ObjectIDZND1="37597@x" ObjectIDZND2="25266@x" Pin0InfoVect0LinkObjId="SW-226862_0" Pin0InfoVect1LinkObjId="SW-226863_0" Pin0InfoVect2LinkObjId="SW-141473_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226861_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2073,-4331 2085,-4331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f5c5a0">
     <polyline DF8003:Layer="0" fill="none" points="1931,-4349 1945,-4349 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25022@1" ObjectIDZND0="25019@0" Pin0InfoVect0LinkObjId="g_3f4d7f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139439_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1931,-4349 1945,-4349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f5c800">
     <polyline DF8003:Layer="0" fill="none" points="1853,-4349 1830,-4349 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37595@1" ObjectIDZND0="37594@1" Pin0InfoVect0LinkObjId="SW-226822_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226821_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1853,-4349 1830,-4349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f5ca60">
     <polyline DF8003:Layer="0" fill="none" points="1895,-4349 1880,-4349 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25022@0" ObjectIDZND0="37595@0" Pin0InfoVect0LinkObjId="SW-226821_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139439_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1895,-4349 1880,-4349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f5da70">
     <polyline DF8003:Layer="0" fill="none" points="1748,-4011 1748,-3957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDZND0="25263@x" ObjectIDZND1="25262@x" ObjectIDZND2="g_3d68ec0@0" Pin0InfoVect0LinkObjId="SW-141456_0" Pin0InfoVect1LinkObjId="SW-141455_0" Pin0InfoVect2LinkObjId="g_3d68ec0_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1748,-4011 1748,-3957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f5ece0">
     <polyline DF8003:Layer="0" fill="none" points="1780,-4336 1780,-4349 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="37593@1" ObjectIDZND0="37594@x" ObjectIDZND1="37592@x" ObjectIDZND2="25127@x" Pin0InfoVect0LinkObjId="SW-226822_0" Pin0InfoVect1LinkObjId="SW-226824_0" Pin0InfoVect2LinkObjId="SW-140335_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226823_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1780,-4336 1780,-4349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f5f7b0">
     <polyline DF8003:Layer="0" fill="none" points="1794,-4349 1780,-4349 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="37594@0" ObjectIDZND0="37593@x" ObjectIDZND1="37592@x" ObjectIDZND2="25127@x" Pin0InfoVect0LinkObjId="SW-226823_0" Pin0InfoVect1LinkObjId="SW-226824_0" Pin0InfoVect2LinkObjId="SW-140335_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226822_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1794,-4349 1780,-4349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f60490">
     <polyline DF8003:Layer="0" fill="none" points="2298,-3967 2298,-4331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25266@x" ObjectIDND1="25265@x" ObjectIDND2="g_32b4d70@0" ObjectIDZND0="g_4232d90@0" ObjectIDZND1="2952@x" ObjectIDZND2="37597@x" Pin0InfoVect0LinkObjId="g_4232d90_0" Pin0InfoVect1LinkObjId="SW-19288_0" Pin0InfoVect2LinkObjId="SW-226863_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-141473_0" Pin1InfoVect1LinkObjId="SW-141472_0" Pin1InfoVect2LinkObjId="g_32b4d70_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2298,-3967 2298,-4331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f606f0">
     <polyline DF8003:Layer="0" fill="none" points="2298,-4331 2298,-4774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="25266@x" ObjectIDND1="25265@x" ObjectIDND2="g_32b4d70@0" ObjectIDZND0="g_4232d90@0" ObjectIDZND1="2952@x" Pin0InfoVect0LinkObjId="g_4232d90_0" Pin0InfoVect1LinkObjId="SW-19288_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-141473_0" Pin1InfoVect1LinkObjId="SW-141472_0" Pin1InfoVect2LinkObjId="g_32b4d70_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2298,-4331 2298,-4774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f60950">
     <polyline DF8003:Layer="0" fill="none" points="1780,-4300 1780,-4288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="37593@0" ObjectIDZND0="g_3f451f0@0" Pin0InfoVect0LinkObjId="g_3f451f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226823_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1780,-4300 1780,-4288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_cf5c10">
     <polyline DF8003:Layer="0" fill="none" points="1748,-4284 1748,-4270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="37592@0" ObjectIDZND0="g_3f37870@0" Pin0InfoVect0LinkObjId="g_3f37870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226824_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1748,-4284 1748,-4270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fc4a80">
     <polyline DF8003:Layer="0" fill="none" points="1748,-4349 1748,-4320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="37593@x" ObjectIDND1="37594@x" ObjectIDND2="25127@x" ObjectIDZND0="37592@1" Pin0InfoVect0LinkObjId="SW-226824_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-226823_0" Pin1InfoVect1LinkObjId="SW-226822_0" Pin1InfoVect2LinkObjId="SW-140335_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1748,-4349 1748,-4320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d1b390">
     <polyline DF8003:Layer="0" fill="none" points="1780,-4349 1748,-4349 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="37593@x" ObjectIDND1="37594@x" ObjectIDZND0="37592@x" ObjectIDZND1="25127@x" ObjectIDZND2="25126@x" Pin0InfoVect0LinkObjId="SW-226824_0" Pin0InfoVect1LinkObjId="SW-140335_0" Pin0InfoVect2LinkObjId="SW-140334_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-226823_0" Pin1InfoVect1LinkObjId="SW-226822_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1780,-4349 1748,-4349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d36bf0">
     <polyline DF8003:Layer="0" fill="none" points="1748,-4349 1748,-4507 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="37592@x" ObjectIDND1="37593@x" ObjectIDND2="37594@x" ObjectIDZND0="25127@x" ObjectIDZND1="25126@x" Pin0InfoVect0LinkObjId="SW-140335_0" Pin0InfoVect1LinkObjId="SW-140334_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-226824_0" Pin1InfoVect1LinkObjId="SW-226823_0" Pin1InfoVect2LinkObjId="SW-226822_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1748,-4349 1748,-4507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f5fdd0">
     <polyline DF8003:Layer="0" fill="none" points="2149,-4283 2149,-4269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="37597@0" ObjectIDZND0="g_2c56cd0@0" Pin0InfoVect0LinkObjId="g_2c56cd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226863_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2149,-4283 2149,-4269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_408e670">
     <polyline DF8003:Layer="0" fill="none" points="2149,-4319 2149,-4331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="37597@1" ObjectIDZND0="37599@x" ObjectIDZND1="37598@x" ObjectIDZND2="25266@x" Pin0InfoVect0LinkObjId="SW-226862_0" Pin0InfoVect1LinkObjId="SW-226861_0" Pin0InfoVect2LinkObjId="SW-141473_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-226863_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2149,-4319 2149,-4331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_344fe70">
     <polyline DF8003:Layer="0" fill="none" points="2085,-4331 2149,-4331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="37599@x" ObjectIDND1="37598@x" ObjectIDZND0="37597@x" ObjectIDZND1="25266@x" ObjectIDZND2="25265@x" Pin0InfoVect0LinkObjId="SW-226863_0" Pin0InfoVect1LinkObjId="SW-141473_0" Pin0InfoVect2LinkObjId="SW-141472_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-226862_0" Pin1InfoVect1LinkObjId="SW-226861_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2085,-4331 2149,-4331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39e9010">
     <polyline DF8003:Layer="0" fill="none" points="2149,-4331 2298,-4331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="37597@x" ObjectIDND1="37599@x" ObjectIDND2="37598@x" ObjectIDZND0="25266@x" ObjectIDZND1="25265@x" ObjectIDZND2="g_32b4d70@0" Pin0InfoVect0LinkObjId="SW-141473_0" Pin0InfoVect1LinkObjId="SW-141472_0" Pin0InfoVect2LinkObjId="g_32b4d70_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-226863_0" Pin1InfoVect1LinkObjId="SW-226862_0" Pin1InfoVect2LinkObjId="SW-226861_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2149,-4331 2298,-4331 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="0" busDevId="18519" cx="2038" cy="-4011" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="25059" cx="2838" cy="-4658" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="11821" cx="3050" cy="-4506" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="2891" cx="2838" cy="-4966" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="2891" cx="2611" cy="-4966" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="2891" cx="2675" cy="-4966" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="3021" cx="2512" cy="-4966" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="3021" cx="2463" cy="-4966" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="3021" cx="2298" cy="-4966" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="2463" cy="-4424" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="27244" cx="3050" cy="-4257" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="28262" cx="3050" cy="-4389" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24962" cx="1876" cy="-5070" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24962" cx="2110" cy="-5070" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="1116" cy="-4643" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="25627" cx="832" cy="-3569" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="25627" cx="892" cy="-3569" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24878" cx="746" cy="-4593" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24878" cx="871" cy="-4593" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="25626" cx="749" cy="-3569" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="25626" cx="694" cy="-3569" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="4549" cx="1610" cy="-5190" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="4549" cx="1338" cy="-5190" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="4549" cx="1116" cy="-5190" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="4549" cx="950" cy="-5190" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="1356" cy="-4363" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="1286" cy="-4363" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="25116" cx="1569" cy="-4690" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="25116" cx="1748" cy="-4690" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="25116" cx="1929" cy="-4690" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="25188" cx="214" cy="-4050" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="1338" cy="-4363" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="20744" cx="1569" cy="-4177" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="22996" cx="3020" cy="-4068" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="21923" cx="2586" cy="-3530" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="21923" cx="3020" cy="-3530" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="21923" cx="2776" cy="-3530" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="25315" cx="2298" cy="-3781" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="25315" cx="1748" cy="-3781" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="25315" cx="1351" cy="-3781" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24878" cx="696" cy="-4593" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="27684" cx="527" cy="-4740" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="25019" cx="1945" cy="-4331" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="25019" cx="1945" cy="-4377" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="25019" cx="1945" cy="-4349" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1153.000000 -5226.000000) translate(0,20)">110kV万马变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1066.000000 -4694.000000) translate(0,17)">3711</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1125.000000 -4757.000000) translate(0,17)">37117</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1081.000000 -4637.000000) translate(0,20)">永兴变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 753.000000 -4587.000000) translate(0,20)">宜就变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 187.000000 -4083.000000) translate(0,17)">班</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 187.000000 -4083.000000) translate(0,38)">别</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 187.000000 -4083.000000) translate(0,59)">变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 710.000000 -4366.000000) translate(0,13)">35167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 656.000000 -4410.000000) translate(0,14)">351</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1375.000000 -5010.000000) translate(0,17)">2号站用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1374.000000 -4349.000000) translate(0,17)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1370.000000 -4306.000000) translate(0,17)">302</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1375.000000 -4265.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1288.000000 -4179.000000) translate(0,17)">万马电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1268.000000 -4153.000000) translate(0,17)">35kV2号主变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1518.000000 -4172.000000) translate(0,17)">红石岩电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1491.000000 -4565.000000) translate(0,17)">38367</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1673.000000 -4539.000000) translate(0,17)">38267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1682.000000 -4714.000000) translate(0,17)">他普里电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1862.000000 -3766.000000) translate(0,17)">龙头山变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1588.000000 -4623.000000) translate(0,17)">383</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1765.000000 -4623.000000) translate(0,17)">382</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1941.000000 -4633.000000) translate(0,17)">381</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1588.000000 -4665.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1588.000000 -4571.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1765.000000 -4665.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1765.000000 -4571.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1938.000000 -4675.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1940.000000 -4592.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1947.000000 -5099.000000) translate(0,20)">中和变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3008.000000 -4258.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2285.000000 -5037.000000) translate(0,17)">110kV永仁变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2809.000000 -4654.000000) translate(0,17)">维的变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2412.000000 -4419.000000) translate(0,17)">永仁盛源变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2494.000000 -4518.000000) translate(0,15)">37167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2474.000000 -4463.000000) translate(0,17)">371</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2465.000000 -4501.000000) translate(0,17)">3716</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 715.000000 -4304.000000) translate(0,9)">TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1253.000000 -4337.000000) translate(0,9)">3901</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1277.000000 -4200.000000) translate(0,9)">TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1247.000000 -4314.000000) translate(0,9)">39017</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1607.000000 -4291.000000) translate(0,9)">TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1992.000000 -4576.000000) translate(0,9)">TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1836.000000 -3960.000000) translate(0,9)">TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2366.000000 -3993.000000) translate(0,9)">TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2189.000000 -3918.000000) translate(0,9)">35kV1号站用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2735.000000 -4695.000000) translate(0,9)">35kV1号</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2738.000000 -4682.000000) translate(0,9)">站用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3550090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3550090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,38)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3550090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3550090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,80)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3550090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3550090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,122)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3550090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39c3730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39c3730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,38)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39c3730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,59)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39c3730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,80)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39c3730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,101)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39c3730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,122)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39c3730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,143)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39c3730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,164)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39c3730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,185)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39c3730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,206)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39c3730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,227)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39c3730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,248)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39c3730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,269)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39c3730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,290)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39c3730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,311)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39c3730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,332)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39c3730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,353)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_39c3730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dce8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 891.000000 -5213.000000) translate(0,17)">Ⅰ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dcef10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1125.000000 -5114.000000) translate(0,17)">372</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dcf150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1123.000000 -5062.000000) translate(0,17)">3726</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dcf390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1123.000000 -5160.000000) translate(0,17)">3721</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dcf5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -5114.000000) translate(0,17)">374</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dcfa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1345.000000 -5062.000000) translate(0,17)">3746</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dcff00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1345.000000 -5160.000000) translate(0,17)">3741</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dd0140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1619.000000 -5121.000000) translate(0,17)">375</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dd0380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1617.000000 -5069.000000) translate(0,17)">3756</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dd05c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1617.000000 -5167.000000) translate(0,17)">3751</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dd52a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2867.000000 -4990.000000) translate(0,17)">Ⅰ段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dd58d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2238.000000 -4992.000000) translate(0,17)">IIM段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dd5b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2794.000000 -4899.000000) translate(0,17)">361</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dd5d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2782.000000 -4853.000000) translate(0,17)">3616</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dd5f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2782.000000 -4943.000000) translate(0,17)">3611</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dd61d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2629.000000 -4894.000000) translate(0,17)">362</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dd6410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2617.000000 -4846.000000) translate(0,17)">3626</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dd6650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2617.000000 -4944.000000) translate(0,17)">3621</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dd6890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2471.633028 -4893.000000) translate(0,17)">364</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dd6ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2469.633028 -4939.000000) translate(0,17)">3642</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dd6d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2468.633028 -4848.000000) translate(0,17)">3646</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dd6f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2251.000000 -4893.000000) translate(0,17)">363</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dd7190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2242.000000 -4933.000000) translate(0,17)">3632</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dd73d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2238.000000 -4852.000000) translate(0,17)">3636</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dd7610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2552.000000 -5056.000000) translate(0,17)">312</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dd7850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2519.000000 -5011.000000) translate(0,17)">3122</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3dd7a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2618.000000 -5005.000000) translate(0,17)">3121</text>
   <text DF8003:Layer="0" fill="rgb(38,38,38)" font-family="SimHei" font-size="20" graphid="g_3dd9e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -86.000000 -5212.000000) translate(0,16)">永仁片区35kV电网图</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3de37d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 337.000000 -4377.000000) translate(0,17)">311</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3de3e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 334.000000 -4325.000000) translate(0,13)">3111</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3de4230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 285.000000 -4428.000000) translate(0,17)">31167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3de8b70" transform="matrix(0.753623 -0.000000 -0.000000 1.000000 462.971014 -4174.000000) translate(0,16)">35kV仁和2号站用变支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3de91b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 304.000000 -4606.000000) translate(0,17)">35kV2号站用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3de93f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2863.000000 -4547.000000) translate(0,13)">38167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3de99a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2905.000000 -4532.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3de9bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2953.000000 -4535.000000) translate(0,17)">381</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3df2220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -4555.000000) translate(0,16)">多</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3df2220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -4555.000000) translate(0,36)">凌</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3df2220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -4555.000000) translate(0,56)">钛</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3df2220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -4555.000000) translate(0,76)">业</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3df2220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -4555.000000) translate(0,96)">变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3df8830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2047.000000 -4044.000000) translate(0,16)">田</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3df8830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2047.000000 -4044.000000) translate(0,36)">房</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3df8830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2047.000000 -4044.000000) translate(0,56)">变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_3e00f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1803.000000 -4079.000000) translate(0,9)">TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3e01580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1912.000000 -3999.000000) translate(0,13)">龙他猛线T田</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3e01580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1912.000000 -3999.000000) translate(0,29)">房变电站支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3e0fa10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2596.000000 -3622.000000) translate(0,17)">365</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3e10040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2590.000000 -3569.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3e10280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2593.000000 -3664.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_3e104c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2749.000000 -3523.000000) translate(0,20)">220kV方山变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e14490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2603.633028 -3723.000000) translate(0,12)">36567</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3e37330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2965.000000 -4096.000000) translate(0,17)">班幸开关站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e3de30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2987.125382 -3607.000000) translate(0,12)">367</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e3e460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2979.125382 -3566.000000) translate(0,12)">3671</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e3e6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2967.125382 -3660.000000) translate(0,12)">36767</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e3e8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3029.125382 -4028.000000) translate(0,12)">341</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e3ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2968.125382 -3992.000000) translate(0,12)">34167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e43c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2743.379205 -3608.000000) translate(0,12)">366</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e442b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2761.379205 -3565.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e49560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2721.379205 -3691.000000) translate(0,12)">36667</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3e4e320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2801.000000 -3591.000000) translate(0,13)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3e4e320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2801.000000 -3591.000000) translate(0,29)">路</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3e4e320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2801.000000 -3591.000000) translate(0,45)">TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5b890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2781.000000 -3971.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5bec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2787.000000 -4010.000000) translate(0,12)">311</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5c100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2797.000000 -3950.000000) translate(0,12)">31117</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3e5c340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2782.000000 -3863.000000) translate(0,16)">永</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3e5c340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2782.000000 -3863.000000) translate(0,36)">仁</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3e5c340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2782.000000 -3863.000000) translate(0,56)">换</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3e5c340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2782.000000 -3863.000000) translate(0,76)">流</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3e5c340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2782.000000 -3863.000000) translate(0,96)">站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3e5c340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2782.000000 -3863.000000) translate(0,116)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3e5cd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2803.000000 -4076.000000) translate(0,13)">±500kV永</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3e5cd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2803.000000 -4076.000000) translate(0,29)">仁换流站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3e5cd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2803.000000 -4076.000000) translate(0,45)">35kV3号站用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e5d7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2760.379205 -3648.000000) translate(0,12)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3e61180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3026.000000 -3832.000000) translate(0,16)">方</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3e61180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3026.000000 -3832.000000) translate(0,36)">班</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3e61180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3026.000000 -3832.000000) translate(0,56)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3e70be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1513.000000 -4517.000000) translate(0,17)">N29</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3e71710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1894.000000 -4562.000000) translate(0,17)">N1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3e71ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2683.000000 -4830.000000) translate(0,16)">永</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3e71ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2683.000000 -4830.000000) translate(0,36)">物</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3e71ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2683.000000 -4830.000000) translate(0,56)">T</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3e71ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2683.000000 -4830.000000) translate(0,76)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3e71df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2908.000000 -4495.000000) translate(0,11)">永物T线多凌钛业支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3e85ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 526.000000 -4046.000000) translate(0,14)">班别T接线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ea5750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 755.000000 -4651.000000) translate(0,14)">353</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ea5d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 688.000000 -4685.000000) translate(0,14)">35367</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ea6450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 843.000000 -4646.000000) translate(0,14)">3901</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ea6bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 844.000000 -4775.000000) translate(0,14)">母线TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ea6e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 688.000000 -4824.000000) translate(0,14)">1号站用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ea7040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 707.000000 -3659.000000) translate(0,14)">352</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ea7400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 905.000000 -3658.000000) translate(0,14)">354</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ea7890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 699.000000 -3609.000000) translate(0,14)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ea7ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 901.000000 -3610.000000) translate(0,14)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ea7d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 704.000000 -3703.000000) translate(0,14)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ea7f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 901.000000 -3703.000000) translate(0,14)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ea8190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 779.000000 -3535.000000) translate(0,14)">312</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ea83d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 730.000000 -3538.000000) translate(0,14)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ea8610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 838.000000 -3540.000000) translate(0,14)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_3ea8850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 725.000000 -3495.000000) translate(0,20)">110kV莲池变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ea8d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 648.000000 -3589.000000) translate(0,14)">Ⅰ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ea9260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 901.000000 -3590.000000) translate(0,14)">Ⅱ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ea97d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 704.000000 -3841.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ea9b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 919.000000 -3861.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3ea9f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 674.000000 -4566.000000) translate(0,17)">莲</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3ea9f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 674.000000 -4566.000000) translate(0,38)">宜</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3ea9f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 674.000000 -4566.000000) translate(0,59)">T</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3ea9f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 674.000000 -4566.000000) translate(0,80)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3eaa1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1096.000000 -4045.000000) translate(0,17)">莲龙线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3eaa420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 497.000000 -4093.000000) translate(0,12)">TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3eaeb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 704.000000 -3788.000000) translate(0,14)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3eaf170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 899.000000 -3789.000000) translate(0,14)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3eb8ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2892.000000 -4415.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3eba3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3055.000000 -4418.000000) translate(0,16)">提</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3eba3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3055.000000 -4418.000000) translate(0,36)">灌</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3eba3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3055.000000 -4418.000000) translate(0,56)">变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3ebaa10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2729.000000 -4378.000000) translate(0,11)">永物T线提灌支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3ebaf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1797.000000 -4006.000000) translate(0,10)">N2＋1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3ebaf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1797.000000 -4006.000000) translate(0,22)">T1号杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3ec5130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 959.000000 -5112.000000) translate(0,17)">373</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3ec5760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 957.000000 -5060.000000) translate(0,17)">3736</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3ec59a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 957.000000 -5158.000000) translate(0,17)">3731</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3ec5dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 919.000000 -5128.000000) translate(0,16)">马</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3ec5dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 919.000000 -5128.000000) translate(0,36)">湾</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3ec5dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 919.000000 -5128.000000) translate(0,56)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3eca5a0" transform="matrix(0.753623 -0.000000 -0.000000 1.000000 391.971014 -4584.000000) translate(0,16)">至0.4kV系统</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ecbb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 313.000000 -4076.000000) translate(0,14)">341</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3ed26a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 424.000000 -4027.000000) translate(0,13)">34167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_3ed36b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 699.000000 -4246.000000) translate(0,9)">N89</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ed6a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2931.000000 -4281.000000) translate(0,12)">371</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ed7070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2865.000000 -4260.000000) translate(0,12)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_3ed7bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2854.000000 -4240.000000) translate(0,8)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ede070" transform="matrix(1.000000 0.000000 0.000000 1.000000 567.000000 -4487.000000) translate(0,12)">35</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ede070" transform="matrix(1.000000 0.000000 0.000000 1.000000 567.000000 -4487.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ede070" transform="matrix(1.000000 0.000000 0.000000 1.000000 567.000000 -4487.000000) translate(0,42)">仁</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ede070" transform="matrix(1.000000 0.000000 0.000000 1.000000 567.000000 -4487.000000) translate(0,57)">和</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ede070" transform="matrix(1.000000 0.000000 0.000000 1.000000 567.000000 -4487.000000) translate(0,72)">施</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ede070" transform="matrix(1.000000 0.000000 0.000000 1.000000 567.000000 -4487.000000) translate(0,87)">工</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ede070" transform="matrix(1.000000 0.000000 0.000000 1.000000 567.000000 -4487.000000) translate(0,102)">变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ede070" transform="matrix(1.000000 0.000000 0.000000 1.000000 567.000000 -4487.000000) translate(0,117)">支</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ede070" transform="matrix(1.000000 0.000000 0.000000 1.000000 567.000000 -4487.000000) translate(0,132)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ede6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 501.799862 -4520.000000) translate(0,15)">至0.4kV系统</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ede940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 489.000000 -4389.000000) translate(0,12)">35kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ede940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 489.000000 -4389.000000) translate(0,27)">仁和</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ede940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 489.000000 -4389.000000) translate(0,42)">施工</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ede940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 489.000000 -4389.000000) translate(0,57)">变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3edf5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 700.000000 -4057.000000) translate(0,12)">N14</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3edfdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 254.000000 -4076.000000) translate(0,13)">3411</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee0120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 359.000000 -4076.000000) translate(0,12)">3416</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee0360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 216.000000 -4033.000000) translate(0,12)">Ⅰ段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ee05a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1883.000000 -5046.000000) translate(0,14)">3911</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ee07e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1885.000000 -5000.000000) translate(0,14)">391</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ee0a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1883.000000 -4955.000000) translate(0,14)">3916</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ee0c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1820.000000 -4937.000000) translate(0,14)">39167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ee0ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2117.000000 -5041.000000) translate(0,14)">3921</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ee10e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2119.000000 -4995.000000) translate(0,14)">392</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ee1320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2117.000000 -4943.000000) translate(0,14)">3926</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3ee1560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2052.000000 -4933.000000) translate(0,14)">39267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee17a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1833.000000 -5088.000000) translate(0,12)">Ⅰ段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee5f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1972.000000 -4038.000000) translate(0,12)">371</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee6590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1870.000000 -4006.000000) translate(0,12)">3716</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee67d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1868.000000 -4043.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee6a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1578.000000 -4268.000000) translate(0,12)">361</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee6c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1574.000000 -4214.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee6e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1510.000000 -4263.000000) translate(0,12)">36117</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee70d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1571.000000 -4314.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee7310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1504.000000 -4325.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee7550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1584.000000 -4367.000000) translate(0,12)">3619</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee7790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1662.000000 -4320.000000) translate(0,12)">36197</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee79d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1524.000000 -4197.000000) translate(0,12)">Ⅰ段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee7c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1283.000000 -3776.000000) translate(0,12)">Ⅰ段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee7e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -3819.000000) translate(0,12)">3211</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee8090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1360.000000 -3863.000000) translate(0,12)">321</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee82d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -3906.000000) translate(0,12)">3216</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee8510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1293.000000 -3958.000000) translate(0,12)">32167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee8750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1755.000000 -3816.000000) translate(0,12)">3221</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee8990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1757.000000 -3860.000000) translate(0,12)">322</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee8bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1755.000000 -3903.000000) translate(0,12)">3226</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee8e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1791.000000 -3919.000000) translate(0,12)">32267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee9050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2305.000000 -3818.000000) translate(0,12)">3231</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee9290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2307.000000 -3860.000000) translate(0,12)">323</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee94d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2305.000000 -3903.000000) translate(0,12)">3236</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee9710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2316.000000 -3946.000000) translate(0,12)">32367</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee9950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2799.000000 -4688.000000) translate(0,12)">3311</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ee9b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2856.000000 -4733.000000) translate(0,12)">33117</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3eed140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2803.000000 -4300.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3eed770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3015.000000 -4233.000000) translate(0,12)">Ⅰ段</text>
   <text DF8003:Layer="0" fill="rgb(0,0,0)" font-family="SimSun" font-size="25" graphid="g_3eee330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 152.000000 -5216.000000) translate(0,20)">配调返回</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_3eef7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 534.000000 -4775.000000) translate(0,20)">湾</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_3eef7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 534.000000 -4775.000000) translate(0,44)">碧</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_3eef7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 534.000000 -4775.000000) translate(0,68)">变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3eefe50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 226.000000 -4805.000000) translate(0,16)">马</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3eefe50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 226.000000 -4805.000000) translate(0,36)">湾</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3eefe50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 226.000000 -4805.000000) translate(0,56)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f01630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 494.000000 -4691.000000) translate(0,12)">Ⅰ段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f01c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 465.000000 -4766.000000) translate(0,12)">3931</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f01ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -4708.000000) translate(0,12)">39317</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f020e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -4764.000000) translate(0,12)">393</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f02320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 366.000000 -4704.000000) translate(0,12)">39360</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f02560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 301.000000 -4766.000000) translate(0,12)">3936</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f027a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 268.000000 -4704.000000) translate(0,12)">39367</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f02b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 270.000000 -4798.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3f41ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1738.000000 -4198.000000) translate(0,16)">TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f42640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1957.000000 -4361.000000) translate(0,12)">I段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f42990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1798.000000 -4346.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f42bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1895.000000 -4343.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f45c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1784.000000 -4319.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f46270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1855.000000 -4337.000000) translate(0,12)">361</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f4c880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1954.000000 -4321.000000) translate(0,12)">3641</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f4ceb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2036.000000 -4319.000000) translate(0,12)">3646</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f4d4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1999.000000 -4319.000000) translate(0,12)">364</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f51db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1970.000000 -4402.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f55b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2090.000000 -4304.000000) translate(0,12)">36467</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3f5ccc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1768.000000 -4387.000000) translate(0,16)">猛他线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f60bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2025.000000 -4408.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34de280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1711.000000 -4302.000000) translate(0,12)">3619</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3fd0c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1924.000000 -4333.000000) translate(0,16)">猛</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3fd0c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1924.000000 -4333.000000) translate(0,36)">虎</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3fd0c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1924.000000 -4333.000000) translate(0,56)">变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3e05bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2139.000000 -4197.000000) translate(0,16)">TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39e1d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2157.000000 -4310.000000) translate(0,12)">3649</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1083.000000 -5135.000000) translate(0,16)">万</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1083.000000 -5135.000000) translate(0,36)">兴</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1083.000000 -5135.000000) translate(0,56)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1314.000000 -4686.000000) translate(0,16)">万</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1314.000000 -4686.000000) translate(0,36)">他</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1314.000000 -4686.000000) translate(0,56)">马</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1314.000000 -4686.000000) translate(0,76)">红</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1314.000000 -4686.000000) translate(0,96)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1579.000000 -5161.000000) translate(0,16)">万</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1579.000000 -5161.000000) translate(0,36)">中</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1579.000000 -5161.000000) translate(0,56)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2089.000000 -4709.000000) translate(0,16)">中</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2089.000000 -4709.000000) translate(0,36)">他</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2089.000000 -4709.000000) translate(0,56)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2277.000000 -4552.000000) translate(0,16)">永</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2277.000000 -4552.000000) translate(0,36)">猛</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2277.000000 -4552.000000) translate(0,56)">龙</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2277.000000 -4552.000000) translate(0,76)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2303.000000 -4791.000000) translate(0,16)">2</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2303.000000 -4791.000000) translate(0,36)">号</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2303.000000 -4791.000000) translate(0,56)">站</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2303.000000 -4791.000000) translate(0,76)">用</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2303.000000 -4791.000000) translate(0,96)">变</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3053.000000 -4293.000000) translate(0,16)">物</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3053.000000 -4293.000000) translate(0,36)">茂</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3053.000000 -4293.000000) translate(0,56)">变</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2852.000000 -4907.000000) translate(0,16)">维</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2852.000000 -4907.000000) translate(0,36)">的</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2852.000000 -4907.000000) translate(0,56)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3f62450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2562.000000 -4262.000000) translate(0,16)">永</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3f62450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2562.000000 -4262.000000) translate(0,36)">方</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3f62450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2562.000000 -4262.000000) translate(0,56)">盛</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3f62450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2562.000000 -4262.000000) translate(0,76)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3f626a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2591.000000 -3841.000000) translate(0,16)">永</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3f626a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2591.000000 -3841.000000) translate(0,36)">方</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3f626a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2591.000000 -3841.000000) translate(0,56)">盛</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3f626a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2591.000000 -3841.000000) translate(0,76)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f628e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2978.000000 -3940.000000) translate(0,12)">TV</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3f62b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2468.000000 -4700.000000) translate(0,16)">永</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3f62b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2468.000000 -4700.000000) translate(0,36)">方</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3f62b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2468.000000 -4700.000000) translate(0,56)">盛</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3f62b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2468.000000 -4700.000000) translate(0,76)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3f62d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 681.000000 -5004.000000) translate(0,16)">马湾线</text>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="0" fill="none" points="3002,-4389 3013,-4382 3013,-4395 3002,-4389 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="2985,-4389 2974,-4395 2974,-4382 2985,-4389 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="541,-4450 535,-4463 547,-4463 541,-4450 541,-4451 541,-4450 " stroke="rgb(60,120,255)"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="0" id="g_3de6b10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 269.000000 -4252.000000)" xlink:href="#voltageTransformer:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3df38f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2495.000000 -4705.000000)" xlink:href="#voltageTransformer:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e10700">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2423.633028 -4451.000000)" xlink:href="#voltageTransformer:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e37960">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2961.125382 -3935.000000)" xlink:href="#voltageTransformer:shape100"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e4e950">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2714.379205 -3894.000000)" xlink:href="#voltageTransformer:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e90f90">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 1.000000 -0.000000 855.572414 -4757.000000)" xlink:href="#voltageTransformer:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3f05df0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 314.500000 -4812.500000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2c56cd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2141.000000 -4203.000000)" xlink:href="#voltageTransformer:shape80"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3f37870">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1740.000000 -4204.000000)" xlink:href="#voltageTransformer:shape80"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.566667 -0.000000 0.000000 -0.560000 2732.000000 -4697.000000)" xlink:href="#transformer2:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.566667 -0.000000 0.000000 -0.560000 2732.000000 -4697.000000)" xlink:href="#transformer2:shape16_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.566667 -0.000000 0.000000 -0.560000 2198.000000 -3924.000000)" xlink:href="#transformer2:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.566667 -0.000000 0.000000 -0.560000 2198.000000 -3924.000000)" xlink:href="#transformer2:shape16_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2751.379205 -4022.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2751.379205 -4022.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.467633 -0.000000 0.000000 -0.460000 1415.956522 -5008.000000)" xlink:href="#transformer2:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.467633 -0.000000 0.000000 -0.460000 1415.956522 -5008.000000)" xlink:href="#transformer2:shape16_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.000000 -0.566667 -0.560000 -0.000000 760.500000 -4729.500000)" xlink:href="#transformer2:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -0.566667 -0.560000 -0.000000 760.500000 -4729.500000)" xlink:href="#transformer2:shape16_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 391.000000 -4485.000000)" xlink:href="#transformer2:shape20_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 391.000000 -4485.000000)" xlink:href="#transformer2:shape20_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 509.000000 -4455.000000)" xlink:href="#transformer2:shape77_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 509.000000 -4455.000000)" xlink:href="#transformer2:shape77_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.400000 -0.000000 0.000000 -0.300000 2052.000000 -4369.000000)" xlink:href="#transformer2:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.400000 -0.000000 0.000000 -0.300000 2052.000000 -4369.000000)" xlink:href="#transformer2:shape16_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="0" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -117.000000 -5164.013514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图.svg" style="fill-opacity:0"><rect height="40" qtmmishow="hidden" width="226" x="-117" y="-5223"/></g>
   <g href="cx_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-166" y="-5240"/></g>
   <g href="cx_配调_配网接线图35_永仁.svg" style="fill-opacity:0"><rect height="39" qtmmishow="hidden" width="127" x="138" y="-5223"/></g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="0" cx="1817" cy="-4010" fill="rgb(255,255,255)" fillStyle="1" r="3.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="2837" cy="-4240" fill="none" fillStyle="0" r="8.5" stroke="rgb(255,255,0)" stroke-width="0.501742"/>
   <ellipse DF8003:Layer="0" cx="2847" cy="-4240" fill="none" fillStyle="0" rx="8" ry="8.5" stroke="rgb(255,255,0)" stroke-width="0.501742"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="0" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="323,-4827 323,-4807 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="323,-4827 323,-4807 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="0" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="40" qtmmishow="hidden" width="226" x="-117" y="-5223"/>
    </a>
   <metadata/><rect fill="white" height="40" opacity="0" stroke="white" transform="" width="226" x="-117" y="-5223"/></g>
   <g DF8003:Layer="0" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-166" y="-5240"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-166" y="-5240"/></g>
   <g DF8003:Layer="0" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="39" qtmmishow="hidden" width="127" x="138" y="-5223"/>
    </a>
   <metadata/><rect fill="white" height="39" opacity="0" stroke="white" transform="" width="127" x="138" y="-5223"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="0" id="SW-19283">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2829.000000 -4867.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2947" ObjectName="SW-CX_YR.CX_YR_361BK"/>
     <cge:Meas_Ref ObjectId="19283"/>
    <cge:TPSR_Ref TObjectID="2947"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-19295">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2545.000000 -5015.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2959" ObjectName="SW-CX_YR.CX_YR_312BK"/>
     <cge:Meas_Ref ObjectId="19295"/>
    <cge:TPSR_Ref TObjectID="2959"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-19286">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2666.000000 -4862.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2950" ObjectName="SW-CX_YR.CX_YR_362BK"/>
     <cge:Meas_Ref ObjectId="19286"/>
    <cge:TPSR_Ref TObjectID="2950"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-171109">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2921.000000 -4247.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27248" ObjectName="SW-YM_WM.YM_WM_371BK"/>
     <cge:Meas_Ref ObjectId="171109"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-19292">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2453.633028 -4861.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2956" ObjectName="SW-CX_YR.CX_YR_364BK"/>
     <cge:Meas_Ref ObjectId="19292"/>
    <cge:TPSR_Ref TObjectID="2956"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2453.633028 -4429.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-19289">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2289.000000 -4860.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2953" ObjectName="SW-CX_YR.CX_YR_363BK"/>
     <cge:Meas_Ref ObjectId="19289"/>
    <cge:TPSR_Ref TObjectID="2953"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-141476">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2289.000000 -3831.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25267" ObjectName="SW-YR_LTS.YR_LTS_323BK"/>
     <cge:Meas_Ref ObjectId="141476"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-28405">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1601.000000 -5089.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4601" ObjectName="SW-CX_WM.CX_WM_375BK"/>
     <cge:Meas_Ref ObjectId="28405"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-138802">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1867.000000 -4971.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24965" ObjectName="SW-YR_ZH.YR_ZH_391BK"/>
     <cge:Meas_Ref ObjectId="138802"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-138856">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2101.000000 -4966.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24971" ObjectName="SW-YR_ZH.YR_ZH_392BK"/>
     <cge:Meas_Ref ObjectId="138856"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-140279">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1920.000000 -4600.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25119" ObjectName="SW-YR_TPL.YR_TPL_381BK"/>
     <cge:Meas_Ref ObjectId="140279"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-140331">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1739.000000 -4589.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25124" ObjectName="SW-YR_TPL.YR_TPL_382BK"/>
     <cge:Meas_Ref ObjectId="140331"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-141453">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1739.000000 -3831.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25260" ObjectName="SW-YR_LTS.YR_LTS_322BK"/>
     <cge:Meas_Ref ObjectId="141453"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-140382">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1560.000000 -4591.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25128" ObjectName="SW-YR_TPL.YR_TPL_383BK"/>
     <cge:Meas_Ref ObjectId="140382"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-106518">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1560.000000 -4239.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20754" ObjectName="SW-CX_HSY.CX_HSY_361BK"/>
     <cge:Meas_Ref ObjectId="106518"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-28393">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1329.000000 -5082.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4598" ObjectName="SW-CX_WM.CX_WM_374BK"/>
     <cge:Meas_Ref ObjectId="28393"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1347.000000 -4264.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-193562">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1107.000000 -5082.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29442" ObjectName="SW-CX_WM.CX_WM_372BK"/>
     <cge:Meas_Ref ObjectId="193562"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-141435">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1342.000000 -3834.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25256" ObjectName="SW-YR_LTS.YR_LTS_321BK"/>
     <cge:Meas_Ref ObjectId="141435"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-55770">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 367.000000 -4347.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10146" ObjectName="SW-CX_RH.CX_RH_311BK"/>
     <cge:Meas_Ref ObjectId="55770"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-61876">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2948.000000 -4496.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11811" ObjectName="SW-CX_DLTY.CX_DLTY_381BK"/>
     <cge:Meas_Ref ObjectId="61876"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-116066">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2576.633028 -3591.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21912" ObjectName="SW-CX_FS.CX_FS_365BK"/>
     <cge:Meas_Ref ObjectId="116066"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-125516">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3011.125382 -3579.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22986" ObjectName="SW-CX_FS.CX_FS_367BK"/>
     <cge:Meas_Ref ObjectId="125516"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-125725">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3011.125382 -3999.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22997" ObjectName="SW-CX_BX.CX_BX_341BK"/>
     <cge:Meas_Ref ObjectId="125725"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-117459">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2767.379205 -3580.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21991" ObjectName="SW-CX_FS.CX_FS_366BK"/>
     <cge:Meas_Ref ObjectId="117459"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2767.379205 -3982.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-146703">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 815.000000 -3519.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25682" ObjectName="SW-CX_LC.CX_LC_312BK"/>
     <cge:Meas_Ref ObjectId="146703"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-146733">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 705.000000 -3629.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25685" ObjectName="SW-CX_LC.CX_LC_352BK"/>
     <cge:Meas_Ref ObjectId="146733"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-183002">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.705882 686.000000 -4364.588235)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27848" ObjectName="SW-YR_YJ.YR_YJ_351BK"/>
     <cge:Meas_Ref ObjectId="183002"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-146779">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 902.000000 -3629.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25693" ObjectName="SW-CX_LC.CX_LC_354BK"/>
     <cge:Meas_Ref ObjectId="146779"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-183092">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.705882 736.000000 -4606.588235)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27850" ObjectName="SW-YR_YJ.YR_YJ_353BK"/>
     <cge:Meas_Ref ObjectId="183092"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-28381">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 941.000000 -5080.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4595" ObjectName="SW-CX_WM.CX_WM_373BK"/>
     <cge:Meas_Ref ObjectId="28381"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-140978">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 303.000000 -4040.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25191" ObjectName="SW-YR_BB.YR_BB_341BK"/>
     <cge:Meas_Ref ObjectId="140978"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-83925">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1933.000000 -4001.000000)" xlink:href="#breaker2:shape9_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18533" ObjectName="SW-CX_TF.CX_TF_371BK"/>
     <cge:Meas_Ref ObjectId="83925"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-194232">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 372.000000 -4730.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29494" ObjectName="SW-DY_WB.DY_WB_393BK"/>
     <cge:Meas_Ref ObjectId="194232"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-226859">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1991.000000 -4321.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37596" ObjectName="SW-YR_MH.YR_MH_364BK"/>
     <cge:Meas_Ref ObjectId="226859"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-226821">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1844.000000 -4339.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37595" ObjectName="SW-YR_MH.YR_MH_361BK"/>
     <cge:Meas_Ref ObjectId="226821"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="0" id="g_3558550">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2824.000000 -4808.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d46540">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2825.500000 -4702.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_4232650">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2281.000000 -4719.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_4232d90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2260.000000 -4720.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_4233870">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2348.000000 -3981.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_4234210">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2347.500000 -3959.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_32b4050">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2288.000000 -3962.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_32b4d70">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2293.500000 -3929.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_42b14d0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1864.000000 -4866.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_42b24e0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1926.000000 -4884.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d4cea0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1925.500000 -4863.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d55350">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1976.000000 -4561.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d55cf0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1975.500000 -4540.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d67670">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1793.500000 -3948.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d67ef0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1794.000000 -3969.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d68ec0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1741.000000 -3938.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d81650">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1640.000000 -4291.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d81d90">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1619.500000 -4292.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d83820">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1553.000000 -4349.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d9a010">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1357.500000 -5029.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3da2340">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1342.000000 -4179.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3da97c0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1277.500000 -4249.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3daa040">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1298.000000 -4249.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3db2810">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1130.000000 -5005.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3dc6ff0">
    <use class="BV-35KV" transform="matrix(-0.734375 -0.000000 0.000000 -1.000000 1344.265625 -3969.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3dc7da0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 742.500000 -4306.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3dc8620">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 743.000000 -4327.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3dc8fc0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 693.000000 -4309.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3dcd910">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.933333 2123.000000 -4868.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3de7580">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 281.500000 -4275.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3de82f0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 343.500000 -4228.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3df0b40">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2062.000000 -4828.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3df1280">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2062.000000 -4849.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3df2860">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2655.000000 -4758.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3df2d40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2634.000000 -4759.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3df49c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2494.000000 -4742.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3df54a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2422.633028 -4493.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3df6a70">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -0.913043 -0.000000 530.565217 -4179.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3dfafa0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1910.000000 -4015.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3dfcaa0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1821.000000 -4014.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3dfd580">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1818.000000 -4053.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e273c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3041.125382 -3577.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e280f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3010.125382 -3656.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e28d10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2955.125382 -3873.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e29ac0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2955.125382 -3703.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e2a870">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3010.125382 -3889.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e2e450">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3041.125382 -3909.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e4a370">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2710.379205 -3704.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e4dbe0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 2793.379205 -3633.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e52110">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2776.379205 -3894.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e617b0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1323.000000 -5021.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e62590">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1388.000000 -4931.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e678b0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1600.000000 -4932.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e6a980">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1612.000000 -5000.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e6c540">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1091.000000 -4944.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e84cb0">
    <use class="BV-0KV" transform="matrix(0.833333 0.000000 0.000000 -0.536585 476.750000 -4079.960600)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e853f0">
    <use class="BV-35KV" transform="matrix(-0.833333 -0.000000 -0.000000 0.536585 495.000000 -4078.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e86500">
    <use class="BV-0KV" transform="matrix(-0.833333 -0.000000 -0.000000 0.536585 723.000000 -3869.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e86ad0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 0.650794 689.000000 -3894.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e87a20">
    <use class="BV-0KV" transform="matrix(0.833333 0.000000 0.000000 -0.536585 704.750000 -3872.960600)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e880f0">
    <use class="BV-35KV" transform="matrix(-0.833333 -0.000000 -0.000000 0.536585 918.000000 -3850.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e88970">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 0.650794 877.000000 -3875.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e89980">
    <use class="BV-35KV" transform="matrix(0.833333 0.000000 0.000000 -0.536585 899.750000 -3853.960600)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e96650">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 879.572414 -4727.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e96ed0">
    <use class="BV-35KV" transform="matrix(-0.734375 -0.000000 0.000000 -1.000000 864.838039 -4669.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ea2400">
    <use class="BV-35KV" transform="matrix(-0.734375 -0.000000 0.000000 -1.000000 739.265625 -4708.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3eaf3b0">
    <use class="BV-35KV" transform="matrix(0.734375 -0.000000 0.000000 -1.000000 702.734375 -3721.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3eaffe0">
    <use class="BV-35KV" transform="matrix(0.734375 -0.000000 0.000000 -1.000000 898.734375 -3718.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3eb0ff0">
    <use class="BV-35KV" transform="matrix(-0.636364 -0.000000 -0.000000 0.507937 699.000000 -3773.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3eb2460">
    <use class="BV-35KV" transform="matrix(-0.636364 -0.000000 -0.000000 0.507937 896.000000 -3767.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3eb94d0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3017.500000 -4398.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ec6080">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 964.000000 -5006.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ec77b0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 994.000000 -4946.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ed2cd0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1922.000000 -4527.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ed72b0">
    <use class="BV-35KV" transform="matrix(0.000000 -0.826087 -0.731707 -0.000000 2844.860310 -4211.021739)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ed9910">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 551.000000 -4350.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3edd4c0">
    <use class="BV-35KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 546.000000 -4259.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3f00620">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 210.000000 -4876.716049)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3f312c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 480.000000 -3982.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3f4d9e0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2048.500000 -4367.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="0:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer10="$AUDIT-BAD-LAYER:0.000000 0.000000" layer11="图层2:0.000000 0.000000" layer12="GDXT:0.000000 0.000000" layer13="Defpoints:0.000000 0.000000" layer14="yc:0.000000 0.000000" layer15="PUBLIC:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layer4="$AUDIT-BAD-LAYER:0.000000 0.000000" layer5="图层2:0.000000 0.000000" layer6="GDXT:0.000000 0.000000" layer7="Defpoints:0.000000 0.000000" layer8="yc:0.000000 0.000000" layer9="0:0.000000 0.000000" layerN="16" moveAndZoomFlag="1"/>
</svg>