<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-173" aopId="3949070" id="thSvg" product="E8000V2" version="1.0" viewBox="3115 -1199 2170 1247">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape50">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="42" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="11" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="36" x2="21" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="47" x2="28" y1="60" y2="60"/>
    <polyline points="29,89 31,89 33,88 34,88 36,87 37,86 39,85 40,83 41,81 41,80 42,78 42,76 42,74 41,72 41,71 40,69 39,68 37,66 36,65 34,64 33,64 31,63 29,63 27,63 25,64 24,64 22,65 21,66 19,68 18,69 17,71 17,72 16,74 16,76 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="16" x2="28" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="29" x2="29" y1="89" y2="97"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="55" y2="47"/>
    <polyline arcFlag="1" points="11,15 10,15 9,15 9,15 8,15 8,16 7,16 7,17 6,17 6,18 6,18 6,19 5,20 5,20 5,21 6,22 6,22 6,23 6,23 7,24 7,24 8,25 8,25 9,25 9,26 10,26 11,26 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,25 10,25 9,25 9,25 8,26 8,26 7,26 7,27 6,27 6,28 6,29 6,29 5,30 5,31 5,31 6,32 6,33 6,33 6,34 7,34 7,35 8,35 8,35 9,36 9,36 10,36 11,36 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,36 10,36 9,36 9,37 8,37 8,37 7,38 7,38 6,39 6,39 6,40 6,40 5,41 5,42 5,42 6,43 6,44 6,44 6,45 7,45 7,46 8,46 8,47 9,47 9,47 10,47 11,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="76" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="2" y2="2"/>
    <rect height="23" stroke-width="0.369608" width="12" x="41" y="29"/>
    <rect height="23" stroke-width="0.369608" width="12" x="22" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="28" x2="11" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="45" x2="45" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="12" x2="12" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="36" x2="21" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="48" x2="45" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="51" x2="43" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="41" x2="53" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="47" y1="21" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="47" x2="47" y1="60" y2="35"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="load:shape18">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,5 5,52 " stroke-width="3"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape49_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="41" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
   </symbol>
   <symbol id="switch2:shape49_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="36" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
   </symbol>
   <symbol id="switch2:shape49-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="41" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
   </symbol>
   <symbol id="switch2:shape49-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="36" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
   </symbol>
   <symbol id="transformer2:shape55_0">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,44 6,44 6,73 " stroke-width="1"/>
    <circle cx="31" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="56" y2="98"/>
    <polyline DF8003:Layer="PUBLIC" points="31,87 25,74 37,74 31,87 31,86 31,87 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="79" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="31" y1="49" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="44" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="44" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="44" y2="39"/>
   </symbol>
   <symbol id="transformer2:shape55_1">
    <circle cx="31" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="20" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="20" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="20" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape56_0">
    <circle cx="16" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,43 41,43 41,72 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="57" y2="99"/>
    <polyline DF8003:Layer="PUBLIC" points="16,84 22,71 9,71 16,84 16,83 16,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="35" x2="47" y1="72" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="45" x2="37" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="39" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="16" y1="54" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="43" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="43" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="43" y2="38"/>
   </symbol>
   <symbol id="transformer2:shape56_1">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="14" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="20" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="16" y1="20" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape54_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <rect height="28" stroke-width="1" width="14" x="90" y="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="95" x2="98" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="93" x2="101" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="97" x2="97" y1="28" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="103" x2="91" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="97" x2="97" y1="75" y2="40"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="39" y1="75" y2="75"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape54_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape91">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="1" y1="59" y2="52"/>
    <ellipse cx="22" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="22" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="62" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="38" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="22" y1="60" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="13" y1="52" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="20" y1="39" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="20" y1="59" y2="46"/>
   </symbol>
   <symbol id="voltageTransformer:shape92">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.992459" x1="12" x2="13" y1="34" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="16" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="27" y1="16" y2="19"/>
    <circle cx="23" cy="16" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="23" cy="30" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="16" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="27" y1="31" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="31" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="31" y2="27"/>
    <circle cx="9" cy="16" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="30" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="16" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.960801" x1="6" x2="13" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.989747" x1="7" x2="6" y1="34" y2="30"/>
    <polyline points="32,31 43,31 43,27 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="48" y1="24" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="14" y2="6"/>
    <rect height="13" stroke-width="1" width="7" x="39" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="45" x2="40" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="41" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="38" y1="6" y2="6"/>
   </symbol>
   <symbol id="voltageTransformer:shape89">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.992459" x1="27" x2="28" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.989747" x1="22" x2="21" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.960801" x1="21" x2="28" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="8" y2="11"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="23" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="7" y2="11"/>
    <circle cx="23" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="23" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="27" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="7" y2="11"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_36274a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3628630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3628fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3629cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_362af10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_362bb30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_362c590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_362d050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_362e5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_362e5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_362fc50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_362fc50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3631210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3631210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_3631f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3633b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3634700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_36352c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3635bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3637360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3637b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3638250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_36389e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3639ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_363a4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_363af90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_363b950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_363cef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_363d9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_363e9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_363f620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_364ddf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3640bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3641950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3642e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1257" width="2180" x="3110" y="-1204"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4333" x2="4333" y1="-135" y2="-34"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4333" x2="4289" y1="-34" y2="-34"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4289" x2="4289" y1="-42" y2="-34"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5003" x2="5003" y1="-134" y2="-33"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5003" x2="4959" y1="-33" y2="-33"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4959" x2="4959" y1="-41" y2="-33"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5272" x2="5281" y1="-447" y2="-447"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3422" y="-1006"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-121997">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4601.600000 -872.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22657" ObjectName="SW-WD_CD.WD_CD_3511SW"/>
     <cge:Meas_Ref ObjectId="121997"/>
    <cge:TPSR_Ref TObjectID="22657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121998">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4601.600000 -955.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22658" ObjectName="SW-WD_CD.WD_CD_3516SW"/>
     <cge:Meas_Ref ObjectId="121998"/>
    <cge:TPSR_Ref TObjectID="22658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122351">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4680.000000 -775.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22691" ObjectName="SW-WD_CD.WD_CD_3021SW"/>
     <cge:Meas_Ref ObjectId="122351"/>
    <cge:TPSR_Ref TObjectID="22691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122632">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4361.000000 -795.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22699" ObjectName="SW-WD_CD.WD_CD_3901SW"/>
     <cge:Meas_Ref ObjectId="122632"/>
    <cge:TPSR_Ref TObjectID="22699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121999">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.043478 4694.600000 -965.304348)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22659" ObjectName="SW-WD_CD.WD_CD_35167SW"/>
     <cge:Meas_Ref ObjectId="121999"/>
    <cge:TPSR_Ref TObjectID="22659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122049">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4360.600000 -872.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22665" ObjectName="SW-WD_CD.WD_CD_3521SW"/>
     <cge:Meas_Ref ObjectId="122049"/>
    <cge:TPSR_Ref TObjectID="22665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122050">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4360.600000 -955.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22666" ObjectName="SW-WD_CD.WD_CD_3526SW"/>
     <cge:Meas_Ref ObjectId="122050"/>
    <cge:TPSR_Ref TObjectID="22666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122051">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4434.600000 -968.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22667" ObjectName="SW-WD_CD.WD_CD_35267SW"/>
     <cge:Meas_Ref ObjectId="122051"/>
    <cge:TPSR_Ref TObjectID="22667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122024">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4035.600000 -956.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22662" ObjectName="SW-WD_CD.WD_CD_3536SW"/>
     <cge:Meas_Ref ObjectId="122024"/>
    <cge:TPSR_Ref TObjectID="22662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122025">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4114.600000 -969.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22663" ObjectName="SW-WD_CD.WD_CD_35367SW"/>
     <cge:Meas_Ref ObjectId="122025"/>
    <cge:TPSR_Ref TObjectID="22663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122023">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4035.600000 -873.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22661" ObjectName="SW-WD_CD.WD_CD_3531SW"/>
     <cge:Meas_Ref ObjectId="122023"/>
    <cge:TPSR_Ref TObjectID="22661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122352">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4620.000000 -773.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22692" ObjectName="SW-WD_CD.WD_CD_30217SW"/>
     <cge:Meas_Ref ObjectId="122352"/>
    <cge:TPSR_Ref TObjectID="22692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122214">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4100.714286 -778.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22687" ObjectName="SW-WD_CD.WD_CD_3011SW"/>
     <cge:Meas_Ref ObjectId="122214"/>
    <cge:TPSR_Ref TObjectID="22687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122215">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4041.714286 -775.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22688" ObjectName="SW-WD_CD.WD_CD_30117SW"/>
     <cge:Meas_Ref ObjectId="122215"/>
    <cge:TPSR_Ref TObjectID="22688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122639">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4411.000000 -728.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22704" ObjectName="SW-WD_CD.WD_CD_39017SW"/>
     <cge:Meas_Ref ObjectId="122639"/>
    <cge:TPSR_Ref TObjectID="22704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3820.000000 -894.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122700">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3563.195792 -222.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22717" ObjectName="SW-WD_CD.WD_CD_05717SW"/>
     <cge:Meas_Ref ObjectId="122700"/>
    <cge:TPSR_Ref TObjectID="22717"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122724">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3711.987375 -228.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22721" ObjectName="SW-WD_CD.WD_CD_05617SW"/>
     <cge:Meas_Ref ObjectId="122724"/>
    <cge:TPSR_Ref TObjectID="22721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122651">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4316.000000 -226.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22707" ObjectName="SW-WD_CD.WD_CD_05117SW"/>
     <cge:Meas_Ref ObjectId="122651"/>
    <cge:TPSR_Ref TObjectID="22707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122652">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4281.136951 -164.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22708" ObjectName="SW-WD_CD.WD_CD_0516SW"/>
     <cge:Meas_Ref ObjectId="122652"/>
    <cge:TPSR_Ref TObjectID="22708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122192">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3939.657143 -226.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22684" ObjectName="SW-WD_CD.WD_CD_05417SW"/>
     <cge:Meas_Ref ObjectId="122192"/>
    <cge:TPSR_Ref TObjectID="22684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122193">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3908.747582 -128.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22685" ObjectName="SW-WD_CD.WD_CD_0546SW"/>
     <cge:Meas_Ref ObjectId="122193"/>
    <cge:TPSR_Ref TObjectID="22685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122169">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4031.100775 -123.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22682" ObjectName="SW-WD_CD.WD_CD_0536SW"/>
     <cge:Meas_Ref ObjectId="122169"/>
    <cge:TPSR_Ref TObjectID="22682"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122168">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4063.000000 -224.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22681" ObjectName="SW-WD_CD.WD_CD_05317SW"/>
     <cge:Meas_Ref ObjectId="122168"/>
    <cge:TPSR_Ref TObjectID="22681"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122145">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4154.211222 -124.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22679" ObjectName="SW-WD_CD.WD_CD_0526SW"/>
     <cge:Meas_Ref ObjectId="122145"/>
    <cge:TPSR_Ref TObjectID="22679"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122144">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4187.371429 -224.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22678" ObjectName="SW-WD_CD.WD_CD_05217SW"/>
     <cge:Meas_Ref ObjectId="122144"/>
    <cge:TPSR_Ref TObjectID="22678"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122748">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5104.000000 -223.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22725" ObjectName="SW-WD_CD.WD_CD_06527SW"/>
     <cge:Meas_Ref ObjectId="122748"/>
    <cge:TPSR_Ref TObjectID="22725"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122772">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5225.000000 -226.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22729" ObjectName="SW-WD_CD.WD_CD_06627SW"/>
     <cge:Meas_Ref ObjectId="122772"/>
    <cge:TPSR_Ref TObjectID="22729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122722">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3684.057143 -370.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22720" ObjectName="SW-WD_CD.WD_CD_056XC"/>
     <cge:Meas_Ref ObjectId="122722"/>
    <cge:TPSR_Ref TObjectID="22720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122698">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3534.885714 -364.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22716" ObjectName="SW-WD_CD.WD_CD_057XC"/>
     <cge:Meas_Ref ObjectId="122698"/>
    <cge:TPSR_Ref TObjectID="22716"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122722">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3684.057143 -290.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22817" ObjectName="SW-WD_CD.WD_CD_056XC1"/>
     <cge:Meas_Ref ObjectId="122722"/>
    <cge:TPSR_Ref TObjectID="22817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122698">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3534.885714 -284.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22818" ObjectName="SW-WD_CD.WD_CD_057XC1"/>
     <cge:Meas_Ref ObjectId="122698"/>
    <cge:TPSR_Ref TObjectID="22818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122120">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4617.000000 -225.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22675" ObjectName="SW-WD_CD.WD_CD_06127SW"/>
     <cge:Meas_Ref ObjectId="122120"/>
    <cge:TPSR_Ref TObjectID="22675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122121">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4586.090439 -133.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22676" ObjectName="SW-WD_CD.WD_CD_0616SW"/>
     <cge:Meas_Ref ObjectId="122121"/>
    <cge:TPSR_Ref TObjectID="22676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122101">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4708.100775 -122.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22673" ObjectName="SW-WD_CD.WD_CD_0626SW"/>
     <cge:Meas_Ref ObjectId="122101"/>
    <cge:TPSR_Ref TObjectID="22673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122100">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4740.000000 -226.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22672" ObjectName="SW-WD_CD.WD_CD_06227SW"/>
     <cge:Meas_Ref ObjectId="122100"/>
    <cge:TPSR_Ref TObjectID="22672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122076">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4830.839793 -124.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22670" ObjectName="SW-WD_CD.WD_CD_0636SW"/>
     <cge:Meas_Ref ObjectId="122076"/>
    <cge:TPSR_Ref TObjectID="22670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122075">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4864.000000 -226.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22669" ObjectName="SW-WD_CD.WD_CD_06327SW"/>
     <cge:Meas_Ref ObjectId="122075"/>
    <cge:TPSR_Ref TObjectID="22669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122648">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 -370.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22706" ObjectName="SW-WD_CD.WD_CD_051XC"/>
     <cge:Meas_Ref ObjectId="122648"/>
    <cge:TPSR_Ref TObjectID="22706"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122648">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 -293.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22816" ObjectName="SW-WD_CD.WD_CD_051XC1"/>
     <cge:Meas_Ref ObjectId="122648"/>
    <cge:TPSR_Ref TObjectID="22816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122677">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4986.000000 -225.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22712" ObjectName="SW-WD_CD.WD_CD_06427SW"/>
     <cge:Meas_Ref ObjectId="122677"/>
    <cge:TPSR_Ref TObjectID="22712"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122678">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4951.136951 -163.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22713" ObjectName="SW-WD_CD.WD_CD_0646SW"/>
     <cge:Meas_Ref ObjectId="122678"/>
    <cge:TPSR_Ref TObjectID="22713"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122675">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4950.000000 -369.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22711" ObjectName="SW-WD_CD.WD_CD_064XC"/>
     <cge:Meas_Ref ObjectId="122675"/>
    <cge:TPSR_Ref TObjectID="22711"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122675">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4950.000000 -292.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22819" ObjectName="SW-WD_CD.WD_CD_064XC1"/>
     <cge:Meas_Ref ObjectId="122675"/>
    <cge:TPSR_Ref TObjectID="22819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122746">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5076.000000 -370.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22724" ObjectName="SW-WD_CD.WD_CD_065XC"/>
     <cge:Meas_Ref ObjectId="122746"/>
    <cge:TPSR_Ref TObjectID="22724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122770">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5197.000000 -371.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22728" ObjectName="SW-WD_CD.WD_CD_066XC"/>
     <cge:Meas_Ref ObjectId="122770"/>
    <cge:TPSR_Ref TObjectID="22728"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122746">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5076.000000 -293.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22820" ObjectName="SW-WD_CD.WD_CD_065XC1"/>
     <cge:Meas_Ref ObjectId="122746"/>
    <cge:TPSR_Ref TObjectID="22820"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122770">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5197.000000 -293.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22823" ObjectName="SW-WD_CD.WD_CD_066XC1"/>
     <cge:Meas_Ref ObjectId="122770"/>
    <cge:TPSR_Ref TObjectID="22823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122997">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3722.000000 -483.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22731" ObjectName="SW-WD_CD.WD_CD_0551XC"/>
     <cge:Meas_Ref ObjectId="122997"/>
    <cge:TPSR_Ref TObjectID="22731"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122997">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3722.000000 -417.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22824" ObjectName="SW-WD_CD.WD_CD_0551XC1"/>
     <cge:Meas_Ref ObjectId="122997"/>
    <cge:TPSR_Ref TObjectID="22824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122482">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4338.914286 -487.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22697" ObjectName="SW-WD_CD.WD_CD_0121XC"/>
     <cge:Meas_Ref ObjectId="122482"/>
    <cge:TPSR_Ref TObjectID="22697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122480">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4430.914286 -487.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22695" ObjectName="SW-WD_CD.WD_CD_012XC"/>
     <cge:Meas_Ref ObjectId="122480"/>
    <cge:TPSR_Ref TObjectID="22695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122482">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4338.914286 -417.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22815" ObjectName="SW-WD_CD.WD_CD_0121XC1"/>
     <cge:Meas_Ref ObjectId="122482"/>
    <cge:TPSR_Ref TObjectID="22815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122480">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4430.914286 -415.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22814" ObjectName="SW-WD_CD.WD_CD_012XC1"/>
     <cge:Meas_Ref ObjectId="122480"/>
    <cge:TPSR_Ref TObjectID="22814"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122637">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4468.000000 -374.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22702" ObjectName="SW-WD_CD.WD_CD_0902XC"/>
     <cge:Meas_Ref ObjectId="122637"/>
    <cge:TPSR_Ref TObjectID="22702"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122637">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4468.000000 -276.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22822" ObjectName="SW-WD_CD.WD_CD_0902XC1"/>
     <cge:Meas_Ref ObjectId="122637"/>
    <cge:TPSR_Ref TObjectID="22822"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122635">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3812.000000 -374.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22700" ObjectName="SW-WD_CD.WD_CD_0901XC"/>
     <cge:Meas_Ref ObjectId="122635"/>
    <cge:TPSR_Ref TObjectID="22700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122635">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3812.000000 -276.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22821" ObjectName="SW-WD_CD.WD_CD_0901XC1"/>
     <cge:Meas_Ref ObjectId="122635"/>
    <cge:TPSR_Ref TObjectID="22821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122190">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3908.000000 -374.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31742" ObjectName="SW-WD_CD.WD_CD_054XC"/>
     <cge:Meas_Ref ObjectId="122190"/>
    <cge:TPSR_Ref TObjectID="31742"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122190">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3908.000000 -297.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31743" ObjectName="SW-WD_CD.WD_CD_054XC1"/>
     <cge:Meas_Ref ObjectId="122190"/>
    <cge:TPSR_Ref TObjectID="31743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122166">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4030.000000 -298.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31745" ObjectName="SW-WD_CD.WD_CD_053XC1"/>
     <cge:Meas_Ref ObjectId="122166"/>
    <cge:TPSR_Ref TObjectID="31745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122166">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4030.000000 -375.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31744" ObjectName="SW-WD_CD.WD_CD_053XC"/>
     <cge:Meas_Ref ObjectId="122166"/>
    <cge:TPSR_Ref TObjectID="31744"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122142">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4153.000000 -375.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31746" ObjectName="SW-WD_CD.WD_CD_052XC"/>
     <cge:Meas_Ref ObjectId="122142"/>
    <cge:TPSR_Ref TObjectID="31746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122142">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4153.000000 -298.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31747" ObjectName="SW-WD_CD.WD_CD_052XC1"/>
     <cge:Meas_Ref ObjectId="122142"/>
    <cge:TPSR_Ref TObjectID="31747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122118">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4585.000000 -298.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31749" ObjectName="SW-WD_CD.WD_CD_061XC1"/>
     <cge:Meas_Ref ObjectId="122118"/>
    <cge:TPSR_Ref TObjectID="31749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122118">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4585.000000 -375.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31748" ObjectName="SW-WD_CD.WD_CD_061XC"/>
     <cge:Meas_Ref ObjectId="122118"/>
    <cge:TPSR_Ref TObjectID="31748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122098">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4707.000000 -375.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31750" ObjectName="SW-WD_CD.WD_CD_062XC"/>
     <cge:Meas_Ref ObjectId="122098"/>
    <cge:TPSR_Ref TObjectID="31750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122098">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4707.000000 -298.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31751" ObjectName="SW-WD_CD.WD_CD_062XC1"/>
     <cge:Meas_Ref ObjectId="122098"/>
    <cge:TPSR_Ref TObjectID="31751"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122073">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4830.000000 -376.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31752" ObjectName="SW-WD_CD.WD_CD_063XC"/>
     <cge:Meas_Ref ObjectId="122073"/>
    <cge:TPSR_Ref TObjectID="31752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122073">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4830.000000 -299.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31753" ObjectName="SW-WD_CD.WD_CD_063XC1"/>
     <cge:Meas_Ref ObjectId="122073"/>
    <cge:TPSR_Ref TObjectID="31753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122250">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4100.000000 -518.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31754" ObjectName="SW-WD_CD.WD_CD_001XC"/>
     <cge:Meas_Ref ObjectId="122250"/>
    <cge:TPSR_Ref TObjectID="31754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122250">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4100.000000 -440.863636)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31755" ObjectName="SW-WD_CD.WD_CD_001XC1"/>
     <cge:Meas_Ref ObjectId="122250"/>
    <cge:TPSR_Ref TObjectID="31755"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122387">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4679.000000 -518.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31756" ObjectName="SW-WD_CD.WD_CD_002XC"/>
     <cge:Meas_Ref ObjectId="122387"/>
    <cge:TPSR_Ref TObjectID="31756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122387">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4679.000000 -440.863636)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31757" ObjectName="SW-WD_CD.WD_CD_002XC1"/>
     <cge:Meas_Ref ObjectId="122387"/>
    <cge:TPSR_Ref TObjectID="31757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122653">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4328.000000 -127.000000)" xlink:href="#switch2:shape49_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22709" ObjectName="SW-WD_CD.WD_CD_05167SW"/>
     <cge:Meas_Ref ObjectId="122653"/>
    <cge:TPSR_Ref TObjectID="22709"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122679">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4998.000000 -126.000000)" xlink:href="#switch2:shape49_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22714" ObjectName="SW-WD_CD.WD_CD_06467SW"/>
     <cge:Meas_Ref ObjectId="122679"/>
    <cge:TPSR_Ref TObjectID="22714"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-WD_CD.WD_CD_9II">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4419,-413 5248,-413 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22655" ObjectName="BS-WD_CD.WD_CD_9II"/>
    <cge:TPSR_Ref TObjectID="22655"/></metadata>
   <polyline fill="none" opacity="0" points="4419,-413 5248,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-WD_CD.WD_CD_3I">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3667,-859 4881,-859 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22653" ObjectName="BS-WD_CD.WD_CD_3I"/>
    <cge:TPSR_Ref TObjectID="22653"/></metadata>
   <polyline fill="none" opacity="0" points="3667,-859 4881,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-WD_CD.WD_CD_9I">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3522,-414 4372,-414 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22654" ObjectName="BS-WD_CD.WD_CD_9I"/>
    <cge:TPSR_Ref TObjectID="22654"/></metadata>
   <polyline fill="none" opacity="0" points="3522,-414 4372,-414 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-WD_CD.WD_CD_1C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4261.000000 -40.000000)" xlink:href="#capacitor:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22825" ObjectName="CB-WD_CD.WD_CD_1C"/>
    <cge:TPSR_Ref TObjectID="22825"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-WD_CD.WD_CD_2C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4931.000000 -39.000000)" xlink:href="#capacitor:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22826" ObjectName="CB-WD_CD.WD_CD_2C"/>
    <cge:TPSR_Ref TObjectID="22826"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.000000 -997.000000)" xlink:href="#transformer2:shape55_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.000000 -997.000000)" xlink:href="#transformer2:shape55_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3716.000000 -633.000000)" xlink:href="#transformer2:shape56_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3716.000000 -633.000000)" xlink:href="#transformer2:shape56_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-WD_CD.WD_CD_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="31958"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4650.000000 -602.000000)" xlink:href="#transformer2:shape54_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4650.000000 -602.000000)" xlink:href="#transformer2:shape54_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="22733" ObjectName="TF-WD_CD.WD_CD_2T"/>
    <cge:TPSR_Ref TObjectID="22733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-WD_CD.WD_CD_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="31954"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4071.000000 -605.000000)" xlink:href="#transformer2:shape54_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4071.000000 -605.000000)" xlink:href="#transformer2:shape54_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="22732" ObjectName="TF-WD_CD.WD_CD_1T"/>
    <cge:TPSR_Ref TObjectID="22732"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2c02050">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4321.000000 -711.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ba6330">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4529.600000 -1028.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f31800">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4289.600000 -1028.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f07dc0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3962.600000 -1028.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c15c40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4741.000000 -485.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f02250">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4138.714286 -495.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dfa8e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3501.195792 -209.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bfd510">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3649.987375 -215.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c016f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.000000 -215.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b88b50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3877.657143 -215.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2de81b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3873.747582 -54.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dea800">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4001.000000 -213.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f225b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3996.100775 -49.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f27a70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4125.371429 -213.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b8e350">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4119.211222 -50.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c091a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5052.000000 -215.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c0d380">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5175.000000 -213.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2efa340">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3781.000000 -550.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eecce0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4555.000000 -214.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ef0930">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4551.090439 -56.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f579a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4678.000000 -215.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f5b5f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4673.100775 -51.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f601e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4802.000000 -215.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f63e30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4795.839793 -52.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_332fae0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4924.000000 -214.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_333fc70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3723.000000 -444.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3345fe0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4469.000000 -319.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_334dc30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4432.000000 -304.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3351020">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3813.000000 -319.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32f9ef0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3776.000000 -304.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3254.000000 -1081.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-145370" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3268.538462 -927.966362) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145370" ObjectName="WD_CD:WD_CD_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-145371" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3268.538462 -888.966362) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145371" ObjectName="WD_CD:WD_CD_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-121988" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4298.000000 -603.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121988" ObjectName="WD_CD:WD_CD_1T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-121990" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4874.000000 -599.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121990" ObjectName="WD_CD:WD_CD_2T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-145370" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3265.538462 -1007.966362) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145370" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-145370" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3267.538462 -968.966362) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145370" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-121743" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5193.000000 -564.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121743" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22655"/>
     <cge:Term_Ref ObjectID="31799"/>
    <cge:TPSR_Ref TObjectID="22655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-121744" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5193.000000 -564.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121744" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22655"/>
     <cge:Term_Ref ObjectID="31799"/>
    <cge:TPSR_Ref TObjectID="22655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-121745" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5193.000000 -564.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121745" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22655"/>
     <cge:Term_Ref ObjectID="31799"/>
    <cge:TPSR_Ref TObjectID="22655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-121746" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5193.000000 -564.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121746" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22655"/>
     <cge:Term_Ref ObjectID="31799"/>
    <cge:TPSR_Ref TObjectID="22655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-121747" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5193.000000 -564.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121747" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22655"/>
     <cge:Term_Ref ObjectID="31799"/>
    <cge:TPSR_Ref TObjectID="22655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-121748" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5193.000000 -564.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121748" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22655"/>
     <cge:Term_Ref ObjectID="31799"/>
    <cge:TPSR_Ref TObjectID="22655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-121749" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5193.000000 -564.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121749" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22655"/>
     <cge:Term_Ref ObjectID="31799"/>
    <cge:TPSR_Ref TObjectID="22655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-121735" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -581.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121735" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22654"/>
     <cge:Term_Ref ObjectID="31798"/>
    <cge:TPSR_Ref TObjectID="22654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-121736" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -581.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121736" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22654"/>
     <cge:Term_Ref ObjectID="31798"/>
    <cge:TPSR_Ref TObjectID="22654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-121737" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -581.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121737" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22654"/>
     <cge:Term_Ref ObjectID="31798"/>
    <cge:TPSR_Ref TObjectID="22654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-121738" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -581.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121738" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22654"/>
     <cge:Term_Ref ObjectID="31798"/>
    <cge:TPSR_Ref TObjectID="22654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-121739" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -581.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121739" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22654"/>
     <cge:Term_Ref ObjectID="31798"/>
    <cge:TPSR_Ref TObjectID="22654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-121740" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -581.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121740" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22654"/>
     <cge:Term_Ref ObjectID="31798"/>
    <cge:TPSR_Ref TObjectID="22654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-121741" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -581.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121741" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22654"/>
     <cge:Term_Ref ObjectID="31798"/>
    <cge:TPSR_Ref TObjectID="22654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-121727" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5052.000000 -980.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121727" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22653"/>
     <cge:Term_Ref ObjectID="31797"/>
    <cge:TPSR_Ref TObjectID="22653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-121728" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5052.000000 -980.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121728" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22653"/>
     <cge:Term_Ref ObjectID="31797"/>
    <cge:TPSR_Ref TObjectID="22653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-121729" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5052.000000 -980.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121729" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22653"/>
     <cge:Term_Ref ObjectID="31797"/>
    <cge:TPSR_Ref TObjectID="22653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-121730" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5052.000000 -980.000000) translate(0,76)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121730" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22653"/>
     <cge:Term_Ref ObjectID="31797"/>
    <cge:TPSR_Ref TObjectID="22653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-121731" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5052.000000 -980.000000) translate(0,96)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121731" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22653"/>
     <cge:Term_Ref ObjectID="31797"/>
    <cge:TPSR_Ref TObjectID="22653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-121734" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5052.000000 -980.000000) translate(0,116)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121734" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22653"/>
     <cge:Term_Ref ObjectID="31797"/>
    <cge:TPSR_Ref TObjectID="22653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121762" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3545.000000 -109.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121762" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22715"/>
     <cge:Term_Ref ObjectID="31918"/>
    <cge:TPSR_Ref TObjectID="22715"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121763" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3545.000000 -109.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121763" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22715"/>
     <cge:Term_Ref ObjectID="31918"/>
    <cge:TPSR_Ref TObjectID="22715"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121754" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3545.000000 -109.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121754" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22715"/>
     <cge:Term_Ref ObjectID="31918"/>
    <cge:TPSR_Ref TObjectID="22715"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121776" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3697.000000 -108.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121776" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22719"/>
     <cge:Term_Ref ObjectID="31926"/>
    <cge:TPSR_Ref TObjectID="22719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121777" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3697.000000 -108.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121777" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22719"/>
     <cge:Term_Ref ObjectID="31926"/>
    <cge:TPSR_Ref TObjectID="22719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121768" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3697.000000 -108.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121768" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22719"/>
     <cge:Term_Ref ObjectID="31926"/>
    <cge:TPSR_Ref TObjectID="22719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121790" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3922.000000 -18.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121790" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22683"/>
     <cge:Term_Ref ObjectID="31854"/>
    <cge:TPSR_Ref TObjectID="22683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121791" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3922.000000 -18.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121791" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22683"/>
     <cge:Term_Ref ObjectID="31854"/>
    <cge:TPSR_Ref TObjectID="22683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121782" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3922.000000 -18.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121782" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22683"/>
     <cge:Term_Ref ObjectID="31854"/>
    <cge:TPSR_Ref TObjectID="22683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121804" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4058.000000 -15.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121804" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22680"/>
     <cge:Term_Ref ObjectID="31848"/>
    <cge:TPSR_Ref TObjectID="22680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121805" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4058.000000 -15.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121805" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22680"/>
     <cge:Term_Ref ObjectID="31848"/>
    <cge:TPSR_Ref TObjectID="22680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121796" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4058.000000 -15.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121796" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22680"/>
     <cge:Term_Ref ObjectID="31848"/>
    <cge:TPSR_Ref TObjectID="22680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121818" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4187.000000 -16.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121818" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22677"/>
     <cge:Term_Ref ObjectID="31842"/>
    <cge:TPSR_Ref TObjectID="22677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121819" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4187.000000 -16.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121819" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22677"/>
     <cge:Term_Ref ObjectID="31842"/>
    <cge:TPSR_Ref TObjectID="22677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121810" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4187.000000 -16.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121810" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22677"/>
     <cge:Term_Ref ObjectID="31842"/>
    <cge:TPSR_Ref TObjectID="22677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121902" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4335.000000 -18.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121902" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22705"/>
     <cge:Term_Ref ObjectID="31898"/>
    <cge:TPSR_Ref TObjectID="22705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121903" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4335.000000 -18.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121903" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22705"/>
     <cge:Term_Ref ObjectID="31898"/>
    <cge:TPSR_Ref TObjectID="22705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121894" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4335.000000 -18.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121894" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22705"/>
     <cge:Term_Ref ObjectID="31898"/>
    <cge:TPSR_Ref TObjectID="22705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121832" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4593.000000 -17.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121832" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22674"/>
     <cge:Term_Ref ObjectID="31836"/>
    <cge:TPSR_Ref TObjectID="22674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121833" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4593.000000 -17.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121833" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22674"/>
     <cge:Term_Ref ObjectID="31836"/>
    <cge:TPSR_Ref TObjectID="22674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121824" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4593.000000 -17.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121824" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22674"/>
     <cge:Term_Ref ObjectID="31836"/>
    <cge:TPSR_Ref TObjectID="22674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121846" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4718.000000 -17.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121846" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22671"/>
     <cge:Term_Ref ObjectID="31830"/>
    <cge:TPSR_Ref TObjectID="22671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121847" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4718.000000 -17.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121847" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22671"/>
     <cge:Term_Ref ObjectID="31830"/>
    <cge:TPSR_Ref TObjectID="22671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121838" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4718.000000 -17.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121838" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22671"/>
     <cge:Term_Ref ObjectID="31830"/>
    <cge:TPSR_Ref TObjectID="22671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121860" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4858.000000 -21.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121860" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22668"/>
     <cge:Term_Ref ObjectID="31824"/>
    <cge:TPSR_Ref TObjectID="22668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121861" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4858.000000 -21.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121861" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22668"/>
     <cge:Term_Ref ObjectID="31824"/>
    <cge:TPSR_Ref TObjectID="22668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121852" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4858.000000 -21.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121852" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22668"/>
     <cge:Term_Ref ObjectID="31824"/>
    <cge:TPSR_Ref TObjectID="22668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121916" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4996.000000 -23.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121916" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22710"/>
     <cge:Term_Ref ObjectID="31908"/>
    <cge:TPSR_Ref TObjectID="22710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121917" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4996.000000 -23.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121917" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22710"/>
     <cge:Term_Ref ObjectID="31908"/>
    <cge:TPSR_Ref TObjectID="22710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4996.000000 -23.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22710"/>
     <cge:Term_Ref ObjectID="31908"/>
    <cge:TPSR_Ref TObjectID="22710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121874" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5124.000000 -39.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22723"/>
     <cge:Term_Ref ObjectID="31934"/>
    <cge:TPSR_Ref TObjectID="22723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121875" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5124.000000 -39.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121875" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22723"/>
     <cge:Term_Ref ObjectID="31934"/>
    <cge:TPSR_Ref TObjectID="22723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121866" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5124.000000 -39.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121866" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22723"/>
     <cge:Term_Ref ObjectID="31934"/>
    <cge:TPSR_Ref TObjectID="22723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121888" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5244.000000 -118.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121888" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22727"/>
     <cge:Term_Ref ObjectID="31942"/>
    <cge:TPSR_Ref TObjectID="22727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121889" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5244.000000 -118.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121889" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22727"/>
     <cge:Term_Ref ObjectID="31942"/>
    <cge:TPSR_Ref TObjectID="22727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121880" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5244.000000 -118.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121880" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22727"/>
     <cge:Term_Ref ObjectID="31942"/>
    <cge:TPSR_Ref TObjectID="22727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121696" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -509.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121696" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22689"/>
     <cge:Term_Ref ObjectID="31866"/>
    <cge:TPSR_Ref TObjectID="22689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121697" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -509.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121697" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22689"/>
     <cge:Term_Ref ObjectID="31866"/>
    <cge:TPSR_Ref TObjectID="22689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121688" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -509.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121688" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22689"/>
     <cge:Term_Ref ObjectID="31866"/>
    <cge:TPSR_Ref TObjectID="22689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121724" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4853.000000 -509.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121724" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22693"/>
     <cge:Term_Ref ObjectID="31874"/>
    <cge:TPSR_Ref TObjectID="22693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121725" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4853.000000 -509.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121725" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22693"/>
     <cge:Term_Ref ObjectID="31874"/>
    <cge:TPSR_Ref TObjectID="22693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121716" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4853.000000 -509.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121716" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22693"/>
     <cge:Term_Ref ObjectID="31874"/>
    <cge:TPSR_Ref TObjectID="22693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121654" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3968.000000 -957.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121654" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22660"/>
     <cge:Term_Ref ObjectID="31808"/>
    <cge:TPSR_Ref TObjectID="22660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121655" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3968.000000 -957.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121655" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22660"/>
     <cge:Term_Ref ObjectID="31808"/>
    <cge:TPSR_Ref TObjectID="22660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121646" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3968.000000 -957.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121646" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22660"/>
     <cge:Term_Ref ObjectID="31808"/>
    <cge:TPSR_Ref TObjectID="22660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121668" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4282.000000 -961.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121668" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22664"/>
     <cge:Term_Ref ObjectID="31816"/>
    <cge:TPSR_Ref TObjectID="22664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121669" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4282.000000 -961.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121669" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22664"/>
     <cge:Term_Ref ObjectID="31816"/>
    <cge:TPSR_Ref TObjectID="22664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121660" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4282.000000 -961.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121660" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22664"/>
     <cge:Term_Ref ObjectID="31816"/>
    <cge:TPSR_Ref TObjectID="22664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121640" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4551.000000 -960.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121640" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22656"/>
     <cge:Term_Ref ObjectID="31800"/>
    <cge:TPSR_Ref TObjectID="22656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121641" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4551.000000 -960.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121641" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22656"/>
     <cge:Term_Ref ObjectID="31800"/>
    <cge:TPSR_Ref TObjectID="22656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121632" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4551.000000 -960.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121632" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22656"/>
     <cge:Term_Ref ObjectID="31800"/>
    <cge:TPSR_Ref TObjectID="22656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121682" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4234.000000 -773.000000) translate(0,13)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121682" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22686"/>
     <cge:Term_Ref ObjectID="31860"/>
    <cge:TPSR_Ref TObjectID="22686"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121683" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4234.000000 -773.000000) translate(0,29)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121683" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22686"/>
     <cge:Term_Ref ObjectID="31860"/>
    <cge:TPSR_Ref TObjectID="22686"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121674" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4234.000000 -773.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121674" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22686"/>
     <cge:Term_Ref ObjectID="31860"/>
    <cge:TPSR_Ref TObjectID="22686"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-121684" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4234.000000 -773.000000) translate(0,61)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121684" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22686"/>
     <cge:Term_Ref ObjectID="31860"/>
    <cge:TPSR_Ref TObjectID="22686"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121710" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4820.000000 -773.000000) translate(0,13)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121710" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22690"/>
     <cge:Term_Ref ObjectID="31868"/>
    <cge:TPSR_Ref TObjectID="22690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121711" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4820.000000 -773.000000) translate(0,29)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121711" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22690"/>
     <cge:Term_Ref ObjectID="31868"/>
    <cge:TPSR_Ref TObjectID="22690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121702" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4820.000000 -773.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121702" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22690"/>
     <cge:Term_Ref ObjectID="31868"/>
    <cge:TPSR_Ref TObjectID="22690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-121712" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4820.000000 -773.000000) translate(0,61)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121712" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22690"/>
     <cge:Term_Ref ObjectID="31868"/>
    <cge:TPSR_Ref TObjectID="22690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-121961" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4299.000000 -624.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121961" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22732"/>
     <cge:Term_Ref ObjectID="31955"/>
    <cge:TPSR_Ref TObjectID="22732"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-121962" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4873.000000 -622.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121962" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22733"/>
     <cge:Term_Ref ObjectID="31959"/>
    <cge:TPSR_Ref TObjectID="22733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121958" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4581.000000 -485.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22694"/>
     <cge:Term_Ref ObjectID="31876"/>
    <cge:TPSR_Ref TObjectID="22694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121959" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4581.000000 -485.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121959" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22694"/>
     <cge:Term_Ref ObjectID="31876"/>
    <cge:TPSR_Ref TObjectID="22694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121950" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4581.000000 -485.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121950" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22694"/>
     <cge:Term_Ref ObjectID="31876"/>
    <cge:TPSR_Ref TObjectID="22694"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="51" qtmmishow="hidden" width="190" x="3235" y="-1160"/>
    </a>
   <metadata/><rect fill="white" height="51" opacity="0" stroke="white" transform="" width="190" x="3235" y="-1160"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="85" qtmmishow="hidden" width="100" x="3163" y="-1184"/>
    </a>
   <metadata/><rect fill="white" height="85" opacity="0" stroke="white" transform="" width="100" x="3163" y="-1184"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="33" x="3564" y="-346"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="33" x="3564" y="-346"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="3709" y="-356"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="3709" y="-356"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="34" x="3935" y="-361"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="34" x="3935" y="-361"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="34" x="4055" y="-362"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="34" x="4055" y="-362"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="33" x="4178" y="-362"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="33" x="4178" y="-362"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="33" x="4304" y="-358"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="33" x="4304" y="-358"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="34" x="4610" y="-362"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="34" x="4610" y="-362"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="34" x="4732" y="-362"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="34" x="4732" y="-362"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="4854" y="-363"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="4854" y="-363"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="4977" y="-353"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="4977" y="-353"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="34" x="5102" y="-353"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="34" x="5102" y="-353"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="5226" y="-356"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="5226" y="-356"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="70" x="4181" y="-670"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="70" x="4181" y="-670"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="69" x="4761" y="-670"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="69" x="4761" y="-670"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="4063" y="-943"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="4063" y="-943"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="34" x="4387" y="-943"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="34" x="4387" y="-943"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="34" x="4629" y="-945"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="34" x="4629" y="-945"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="60" x="3151" y="-747"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="60" x="3151" y="-747"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3438" y="-1137"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3438" y="-1137"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3438" y="-1172"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3438" y="-1172"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="33" x="4459" y="-473"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="33" x="4459" y="-473"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="3422" y="-1007"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="3422" y="-1007"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="51" qtmmishow="hidden" width="190" x="3235" y="-1160"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="85" qtmmishow="hidden" width="100" x="3163" y="-1184"/></g>
   <g href="35kV插甸变WD_CD_057间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="33" x="3564" y="-346"/></g>
   <g href="35kV插甸变WD_CD_056间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="3709" y="-356"/></g>
   <g href="35kV插甸变WD_CD_054间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="34" x="3935" y="-361"/></g>
   <g href="35kV插甸变WD_CD_053间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="34" x="4055" y="-362"/></g>
   <g href="35kV插甸变WD_CD_052间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="33" x="4178" y="-362"/></g>
   <g href="35kV插甸变WD_CD_051间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="33" x="4304" y="-358"/></g>
   <g href="35kV插甸变WD_CD_061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="34" x="4610" y="-362"/></g>
   <g href="35kV插甸变WD_CD_062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="34" x="4732" y="-362"/></g>
   <g href="35kV插甸变WD_CD_063间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="4854" y="-363"/></g>
   <g href="35kV插甸变WD_CD_064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="4977" y="-353"/></g>
   <g href="35kV插甸变WD_CD_065间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="34" x="5102" y="-353"/></g>
   <g href="35kV插甸变WD_CD_066间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="5226" y="-356"/></g>
   <g href="35kV插甸变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="70" x="4181" y="-670"/></g>
   <g href="35kV插甸变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="69" x="4761" y="-670"/></g>
   <g href="35kV插甸变WD_CD_353间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="4063" y="-943"/></g>
   <g href="35kV插甸变WD_CD_352间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="34" x="4387" y="-943"/></g>
   <g href="35kV插甸变WD_CD_351间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="34" x="4629" y="-945"/></g>
   <g href="35kV插甸变GG间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="60" x="3151" y="-747"/></g>
   <g href="cx_配调_配网接线图35_武定.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3438" y="-1137"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3438" y="-1172"/></g>
   <g href="35kV插甸变WD_CD_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="33" x="4459" y="-473"/></g>
   <g href="AVC插甸站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="3422" y="-1007"/></g>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3731,-609 3724,-624 3739,-624 3731,-609 3731,-609 3731,-609 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3731,-559 3724,-544 3739,-544 3731,-559 3731,-559 3731,-559 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3918,-253 3911,-268 3926,-268 3918,-253 3918,-253 3918,-253 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3918,-199 3911,-184 3926,-184 3918,-199 3918,-199 3918,-199 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4040,-253 4033,-268 4048,-268 4040,-253 4040,-253 4040,-253 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4040,-199 4033,-184 4048,-184 4040,-199 4040,-199 4040,-199 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4163,-253 4156,-268 4171,-268 4163,-253 4163,-253 4163,-253 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4163,-199 4156,-184 4171,-184 4163,-199 4163,-199 4163,-199 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-253 4283,-268 4298,-268 4290,-253 4290,-253 4290,-253 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-235 4283,-220 4298,-220 4290,-235 4290,-235 4290,-235 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-255 4588,-270 4603,-270 4595,-255 4595,-255 4595,-255 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-201 4588,-186 4603,-186 4595,-201 4595,-201 4595,-201 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-255 4710,-270 4725,-270 4717,-255 4717,-255 4717,-255 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-201 4710,-186 4725,-186 4717,-201 4717,-201 4717,-201 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4840,-255 4833,-270 4848,-270 4840,-255 4840,-255 4840,-255 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4840,-201 4833,-186 4848,-186 4840,-201 4840,-201 4840,-201 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-252 4953,-267 4968,-267 4960,-252 4960,-252 4960,-252 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-234 4953,-219 4968,-219 4960,-234 4960,-234 4960,-234 " stroke="rgb(0,255,0)"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2ba7060">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4553.600000 -1011.000000)" xlink:href="#voltageTransformer:shape91"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f46680">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4313.600000 -1011.000000)" xlink:href="#voltageTransformer:shape91"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_329a390">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3988.600000 -1011.000000)" xlink:href="#voltageTransformer:shape91"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f3a550">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4346.000000 -681.000000)" xlink:href="#voltageTransformer:shape92"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b93020">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4469.000000 -234.000000)" xlink:href="#voltageTransformer:shape89"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_334e960">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3813.000000 -234.000000)" xlink:href="#voltageTransformer:shape89"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-WD_CD.054Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.747582 -28.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34099" ObjectName="EC-WD_CD.054Ld"/>
    <cge:TPSR_Ref TObjectID="34099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_CD.053Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4035.100775 -27.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34098" ObjectName="EC-WD_CD.053Ld"/>
    <cge:TPSR_Ref TObjectID="34098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_CD.052Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4158.211222 -28.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34097" ObjectName="EC-WD_CD.052Ld"/>
    <cge:TPSR_Ref TObjectID="34097"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_CD.061Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4590.090439 -30.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34094" ObjectName="EC-WD_CD.061Ld"/>
    <cge:TPSR_Ref TObjectID="34094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_CD.062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4712.100775 -29.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34095" ObjectName="EC-WD_CD.062Ld"/>
    <cge:TPSR_Ref TObjectID="34095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_CD.063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4834.839793 -30.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34096" ObjectName="EC-WD_CD.063Ld"/>
    <cge:TPSR_Ref TObjectID="34096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_CD.353Ld">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4040.000000 -1134.000000)" xlink:href="#load:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38125" ObjectName="EC-WD_CD.353Ld"/>
    <cge:TPSR_Ref TObjectID="38125"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2f385c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4611,-877 4611,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22657@0" ObjectIDZND0="22653@0" Pin0InfoVect0LinkObjId="g_2c01b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121997_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4611,-877 4611,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_335ada0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4611,-923 4611,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22656@0" ObjectIDZND0="22657@1" Pin0InfoVect0LinkObjId="SW-121997_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121996_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4611,-923 4611,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_335af90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4611,-950 4611,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22656@1" ObjectIDZND0="22658@0" Pin0InfoVect0LinkObjId="SW-121998_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121996_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4611,-950 4611,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c01b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-816 4689,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22691@1" ObjectIDZND0="22653@0" Pin0InfoVect0LinkObjId="g_2f385c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122351_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-816 4689,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c01df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-730 4689,-699 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="22690@0" ObjectIDZND0="22733@1" Pin0InfoVect0LinkObjId="g_3312a60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122347_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-730 4689,-699 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_333dde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-859 4370,-836 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22653@0" ObjectIDZND0="22699@1" Pin0InfoVect0LinkObjId="SW-122632_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f385c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-859 4370,-836 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_333e040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-765 4328,-777 4370,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_2c02050@0" ObjectIDZND0="22704@x" ObjectIDZND1="22699@x" ObjectIDZND2="g_2f3a550@0" Pin0InfoVect0LinkObjId="SW-122639_0" Pin0InfoVect1LinkObjId="SW-122632_0" Pin0InfoVect2LinkObjId="g_2f3a550_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c02050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-765 4328,-777 4370,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ba5e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4706,-975 4706,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22659@0" ObjectIDZND0="g_2c05ef0@0" Pin0InfoVect0LinkObjId="g_2c05ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121999_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4706,-975 4706,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ba60d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4576,-1091 4537,-1091 4537,-1075 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" ObjectIDND0="22658@x" ObjectIDND1="22659@x" ObjectIDND2="37811@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-121998_0" Pin1InfoVect1LinkObjId="SW-121999_0" Pin1InfoVect2LinkObjId="g_331d570_1" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4576,-1091 4537,-1091 4537,-1075 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bd3ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-877 4370,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22665@0" ObjectIDZND0="22653@0" Pin0InfoVect0LinkObjId="g_2f385c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122049_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-877 4370,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bd3d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-923 4370,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22664@0" ObjectIDZND0="22665@1" Pin0InfoVect0LinkObjId="SW-122049_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122048_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-923 4370,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bd3f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-950 4370,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22664@1" ObjectIDZND0="22666@0" Pin0InfoVect0LinkObjId="SW-122050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122048_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-950 4370,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f46420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4444,-973 4444,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22667@0" ObjectIDZND0="g_2f459d0@0" Pin0InfoVect0LinkObjId="g_2f459d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122051_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4444,-973 4444,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f42bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4045,-878 4045,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22661@0" ObjectIDZND0="22653@0" Pin0InfoVect0LinkObjId="g_2f385c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122023_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4045,-878 4045,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f42e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4045,-924 4045,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22660@0" ObjectIDZND0="22661@1" Pin0InfoVect0LinkObjId="SW-122023_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122022_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4045,-924 4045,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f43070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4045,-951 4045,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22660@1" ObjectIDZND0="22662@0" Pin0InfoVect0LinkObjId="SW-122024_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122022_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4045,-951 4045,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3299ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4125,-973 4125,-956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22663@0" ObjectIDZND0="g_3299480@0" Pin0InfoVect0LinkObjId="g_3299480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122025_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4125,-973 4125,-956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_329a130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-1091 3970,-1091 3970,-1082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="22662@x" ObjectIDND1="22663@x" ObjectIDND2="38125@x" ObjectIDZND0="g_2f07dc0@0" Pin0InfoVect0LinkObjId="g_2f07dc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122024_0" Pin1InfoVect1LinkObjId="SW-122025_0" Pin1InfoVect2LinkObjId="EC-WD_CD.353Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-1091 3970,-1091 3970,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f0b910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-757 4689,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22690@1" ObjectIDZND0="22691@x" ObjectIDZND1="22692@x" Pin0InfoVect0LinkObjId="SW-122351_0" Pin0InfoVect1LinkObjId="SW-122352_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122347_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-757 4689,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c12290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-772 4689,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22690@x" ObjectIDND1="22692@x" ObjectIDZND0="22691@0" Pin0InfoVect0LinkObjId="SW-122351_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-122347_0" Pin1InfoVect1LinkObjId="SW-122352_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-772 4689,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c14cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-772 4629,-772 4629,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22690@x" ObjectIDND1="22691@x" ObjectIDZND0="22692@0" Pin0InfoVect0LinkObjId="SW-122352_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-122347_0" Pin1InfoVect1LinkObjId="SW-122351_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-772 4629,-772 4629,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c14f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4629,-814 4629,-827 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22692@1" ObjectIDZND0="g_2c151b0@0" Pin0InfoVect0LinkObjId="g_2c151b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122352_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4629,-814 4629,-827 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2efe1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-819 4110,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22687@1" ObjectIDZND0="22653@0" Pin0InfoVect0LinkObjId="g_2f385c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122214_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-819 4110,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2efe450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-731 4110,-702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="22686@0" ObjectIDZND0="22732@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-731 4110,-702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2efe6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-758 4110,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22686@1" ObjectIDZND0="22688@x" ObjectIDZND1="22687@x" Pin0InfoVect0LinkObjId="SW-122215_0" Pin0InfoVect1LinkObjId="SW-122214_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122210_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-758 4110,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2efe910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-773 4110,-783 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22686@x" ObjectIDND1="22688@x" ObjectIDZND0="22687@0" Pin0InfoVect0LinkObjId="SW-122214_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-122210_0" Pin1InfoVect1LinkObjId="SW-122215_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-773 4110,-783 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f01300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-773 4051,-773 4051,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22686@x" ObjectIDND1="22687@x" ObjectIDZND0="22688@0" Pin0InfoVect0LinkObjId="SW-122215_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-122210_0" Pin1InfoVect1LinkObjId="SW-122214_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-773 4051,-773 4051,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f01560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4051,-816 4051,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22688@1" ObjectIDZND0="g_2f017c0@0" Pin0InfoVect0LinkObjId="g_2f017c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122215_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4051,-816 4051,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ede5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-777 4420,-777 4420,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_2c02050@0" ObjectIDND1="22699@x" ObjectIDND2="g_2f3a550@0" ObjectIDZND0="22704@1" Pin0InfoVect0LinkObjId="SW-122639_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c02050_0" Pin1InfoVect1LinkObjId="SW-122632_0" Pin1InfoVect2LinkObjId="g_2f3a550_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-777 4420,-777 4420,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ede810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4420,-733 4420,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22704@0" ObjectIDZND0="g_2edea70@0" Pin0InfoVect0LinkObjId="g_2edea70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122639_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4420,-733 4420,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2edff70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-800 4370,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="22699@0" ObjectIDZND0="g_2c02050@0" ObjectIDZND1="22704@x" ObjectIDZND2="g_2f3a550@0" Pin0InfoVect0LinkObjId="g_2c02050_0" Pin0InfoVect1LinkObjId="SW-122639_0" Pin0InfoVect2LinkObjId="g_2f3a550_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122632_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-800 4370,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f3a2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-721 4370,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2f3a550@0" ObjectIDZND0="g_2c02050@0" ObjectIDZND1="22704@x" ObjectIDZND2="22699@x" Pin0InfoVect0LinkObjId="g_2c02050_0" Pin0InfoVect1LinkObjId="SW-122639_0" Pin0InfoVect2LinkObjId="SW-122632_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f3a550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-721 4370,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2df7c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-859 3825,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22653@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f385c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-859 3825,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2df7e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-947 3825,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-947 3825,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bfc8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3572,-227 3572,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22717@0" ObjectIDZND0="g_2bfcb50@0" Pin0InfoVect0LinkObjId="g_2bfcb50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3572,-227 3572,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c00a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-233 3721,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22721@0" ObjectIDZND0="g_2c00ca0@0" Pin0InfoVect0LinkObjId="g_2c00ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122724_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-233 3721,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f2b4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4325,-234 4325,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22707@0" ObjectIDZND0="g_2f2b740@0" Pin0InfoVect0LinkObjId="g_2f2b740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122651_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4325,-234 4325,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b87ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-231 3949,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22684@0" ObjectIDZND0="g_2b88100@0" Pin0InfoVect0LinkObjId="g_2b88100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122192_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-231 3949,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2de71c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3918,-120 3881,-120 3881,-108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34099@x" ObjectIDND1="22685@x" ObjectIDZND0="g_2de81b0@0" Pin0InfoVect0LinkObjId="g_2de81b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_CD.054Ld_0" Pin1InfoVect1LinkObjId="SW-122193_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3918,-120 3881,-120 3881,-108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2de7cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3918,-49 3918,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="34099@0" ObjectIDZND0="22685@x" ObjectIDZND1="g_2de81b0@0" Pin0InfoVect0LinkObjId="SW-122193_0" Pin0InfoVect1LinkObjId="g_2de81b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_CD.054Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3918,-49 3918,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2de7f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3918,-120 3918,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34099@x" ObjectIDND1="g_2de81b0@0" ObjectIDZND0="22685@0" Pin0InfoVect0LinkObjId="SW-122193_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_CD.054Ld_0" Pin1InfoVect1LinkObjId="g_2de81b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3918,-120 3918,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2de9b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4072,-230 4072,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22681@0" ObjectIDZND0="g_2de9db0@0" Pin0InfoVect0LinkObjId="g_2de9db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122168_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4072,-230 4072,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f21e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4040,-115 4003,-115 4003,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34098@x" ObjectIDND1="22682@x" ObjectIDZND0="g_2f225b0@0" Pin0InfoVect0LinkObjId="g_2f225b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_CD.053Ld_0" Pin1InfoVect1LinkObjId="SW-122169_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4040,-115 4003,-115 4003,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f220f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4040,-48 4040,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="34098@0" ObjectIDZND0="22682@x" ObjectIDZND1="g_2f225b0@0" Pin0InfoVect0LinkObjId="SW-122169_0" Pin0InfoVect1LinkObjId="g_2f225b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_CD.053Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4040,-48 4040,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f22350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4040,-115 4040,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34098@x" ObjectIDND1="g_2f225b0@0" ObjectIDZND0="22682@0" Pin0InfoVect0LinkObjId="SW-122169_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_CD.053Ld_0" Pin1InfoVect1LinkObjId="g_2f225b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4040,-115 4040,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b8dc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4163,-116 4126,-116 4126,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34097@x" ObjectIDND1="22679@x" ObjectIDZND0="g_2b8e350@0" Pin0InfoVect0LinkObjId="g_2b8e350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_CD.052Ld_0" Pin1InfoVect1LinkObjId="SW-122145_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4163,-116 4126,-116 4126,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b8de90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4163,-49 4163,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34097@0" ObjectIDZND0="g_2b8e350@0" ObjectIDZND1="22679@x" Pin0InfoVect0LinkObjId="g_2b8e350_0" Pin0InfoVect1LinkObjId="SW-122145_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_CD.052Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4163,-49 4163,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b8e0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4163,-116 4163,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2b8e350@0" ObjectIDND1="34097@x" ObjectIDZND0="22679@0" Pin0InfoVect0LinkObjId="SW-122145_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b8e350_0" Pin1InfoVect1LinkObjId="EC-WD_CD.052Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4163,-116 4163,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b91880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4196,-231 4196,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22678@0" ObjectIDZND0="g_2f27020@0" Pin0InfoVect0LinkObjId="g_2f27020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122144_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4196,-231 4196,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0c6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5113,-228 5113,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22725@0" ObjectIDZND0="g_2c0c930@0" Pin0InfoVect0LinkObjId="g_2c0c930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122748_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5113,-228 5113,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c108b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5234,-231 5234,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22729@0" ObjectIDZND0="g_2c10b10@0" Pin0InfoVect0LinkObjId="g_2c10b10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122772_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5234,-231 5234,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c11560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3732,-531 3732,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_2efa340@0" ObjectIDND1="22731@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2efa340_0" Pin1InfoVect1LinkObjId="SW-122997_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3732,-531 3732,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bb68d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4576,-1084 4576,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_2ba7060@0" ObjectIDZND0="22658@x" ObjectIDZND1="22659@x" ObjectIDZND2="37811@1" Pin0InfoVect0LinkObjId="SW-121998_0" Pin0InfoVect1LinkObjId="SW-121999_0" Pin0InfoVect2LinkObjId="g_331d570_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ba7060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4576,-1084 4576,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bb6af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4576,-1091 4612,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_2ba7060@0" ObjectIDZND0="22658@x" ObjectIDZND1="22659@x" ObjectIDZND2="37811@1" Pin0InfoVect0LinkObjId="SW-121998_0" Pin0InfoVect1LinkObjId="SW-121999_0" Pin0InfoVect2LinkObjId="g_331d570_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ba7060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4576,-1091 4612,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bb6d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-1090 4336,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="voltageTransformer" ObjectIDND0="22666@x" ObjectIDND1="22667@x" ObjectIDND2="38120@1" ObjectIDZND0="g_2f46680@0" Pin0InfoVect0LinkObjId="g_2f46680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122050_0" Pin1InfoVect1LinkObjId="SW-122051_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-1090 4336,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bb77e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-1160 4370,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="38120@1" ObjectIDZND0="g_2f46680@0" ObjectIDZND1="22666@x" ObjectIDZND2="22667@x" Pin0InfoVect0LinkObjId="g_2f46680_0" Pin0InfoVect1LinkObjId="SW-122050_0" Pin0InfoVect2LinkObjId="SW-122051_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-1160 4370,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bb82d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-1090 4336,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="voltageTransformer" ObjectIDND0="22666@x" ObjectIDND1="22667@x" ObjectIDND2="38120@1" ObjectIDZND0="g_2f46680@0" Pin0InfoVect0LinkObjId="g_2f46680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122050_0" Pin1InfoVect1LinkObjId="SW-122051_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-1090 4336,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bb8530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4297,-1074 4297,-1090 4336,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDZND0="22666@x" ObjectIDZND1="22667@x" ObjectIDZND2="38120@1" Pin0InfoVect0LinkObjId="SW-122050_0" Pin0InfoVect1LinkObjId="SW-122051_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4297,-1074 4297,-1090 4336,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bb9020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4045,-1139 4045,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="38125@0" ObjectIDZND0="g_329a390@0" ObjectIDZND1="g_2f07dc0@0" ObjectIDZND2="22662@x" Pin0InfoVect0LinkObjId="g_329a390_0" Pin0InfoVect1LinkObjId="g_2f07dc0_0" Pin0InfoVect2LinkObjId="SW-122024_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_CD.353Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4045,-1139 4045,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bb9b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4045,-1091 4011,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="22662@x" ObjectIDND1="22663@x" ObjectIDND2="38125@x" ObjectIDZND0="g_329a390@0" ObjectIDZND1="g_2f07dc0@0" Pin0InfoVect0LinkObjId="g_329a390_0" Pin0InfoVect1LinkObjId="g_2f07dc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122024_0" Pin1InfoVect1LinkObjId="SW-122025_0" Pin1InfoVect2LinkObjId="EC-WD_CD.353Ld_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4045,-1091 4011,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bb9d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4011,-1091 4011,-1082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="voltageTransformer" ObjectIDND0="22662@x" ObjectIDND1="22663@x" ObjectIDND2="38125@x" ObjectIDZND0="g_329a390@0" Pin0InfoVect0LinkObjId="g_329a390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122024_0" Pin1InfoVect1LinkObjId="SW-122025_0" Pin1InfoVect2LinkObjId="EC-WD_CD.353Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4011,-1091 4011,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb9fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3918,-169 3918,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="22685@1" ObjectIDZND0="31743@0" Pin0InfoVect0LinkObjId="SW-122190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122193_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3918,-169 3918,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bba230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4040,-277 4008,-277 4008,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="22681@x" ObjectIDND1="22682@x" ObjectIDND2="31745@x" ObjectIDZND0="g_2dea800@0" Pin0InfoVect0LinkObjId="g_2dea800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122168_0" Pin1InfoVect1LinkObjId="SW-122169_0" Pin1InfoVect2LinkObjId="SW-122166_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4040,-277 4008,-277 4008,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bbaf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4040,-161 4040,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="22682@1" ObjectIDZND0="g_2dea800@0" ObjectIDZND1="22681@x" ObjectIDZND2="31745@x" Pin0InfoVect0LinkObjId="g_2dea800_0" Pin0InfoVect1LinkObjId="SW-122168_0" Pin0InfoVect2LinkObjId="SW-122166_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122169_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4040,-161 4040,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bbb1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4040,-277 4072,-277 4072,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2dea800@0" ObjectIDND1="22682@x" ObjectIDND2="31745@x" ObjectIDZND0="22681@1" Pin0InfoVect0LinkObjId="SW-122168_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2dea800_0" Pin1InfoVect1LinkObjId="SW-122169_0" Pin1InfoVect2LinkObjId="SW-122166_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4040,-277 4072,-277 4072,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bbb400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4163,-277 4132,-277 4132,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="22678@x" ObjectIDND1="22679@x" ObjectIDND2="31747@x" ObjectIDZND0="g_2f27a70@0" Pin0InfoVect0LinkObjId="g_2f27a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122144_0" Pin1InfoVect1LinkObjId="SW-122145_0" Pin1InfoVect2LinkObjId="SW-122142_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4163,-277 4132,-277 4132,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ef2090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4163,-163 4163,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="22679@1" ObjectIDZND0="g_2f27a70@0" ObjectIDZND1="22678@x" ObjectIDZND2="31747@x" Pin0InfoVect0LinkObjId="g_2f27a70_0" Pin0InfoVect1LinkObjId="SW-122144_0" Pin0InfoVect2LinkObjId="SW-122142_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122145_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4163,-163 4163,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ef22c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4163,-277 4196,-277 4196,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2f27a70@0" ObjectIDND1="22679@x" ObjectIDND2="31747@x" ObjectIDZND0="22678@1" Pin0InfoVect0LinkObjId="SW-122144_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f27a70_0" Pin1InfoVect1LinkObjId="SW-122145_0" Pin1InfoVect2LinkObjId="SW-122142_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4163,-277 4196,-277 4196,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ef2520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4333,-163 4290,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="22709@0" ObjectIDZND0="22825@x" ObjectIDZND1="22708@x" Pin0InfoVect0LinkObjId="CB-WD_CD.WD_CD_1C_0" Pin0InfoVect1LinkObjId="SW-122652_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122653_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4333,-163 4290,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ef3010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-163 4290,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="22708@x" ObjectIDND1="22709@x" ObjectIDZND0="22825@0" Pin0InfoVect0LinkObjId="CB-WD_CD.WD_CD_1C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-122652_0" Pin1InfoVect1LinkObjId="SW-122653_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-163 4290,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ef3270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-163 4290,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22825@x" ObjectIDND1="22709@x" ObjectIDZND0="22708@0" Pin0InfoVect0LinkObjId="SW-122652_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-WD_CD.WD_CD_1C_0" Pin1InfoVect1LinkObjId="SW-122653_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-163 4290,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ef7430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4045,-1023 4124,-1023 4124,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="22662@x" ObjectIDND1="g_329a390@0" ObjectIDND2="g_2f07dc0@0" ObjectIDZND0="22663@1" Pin0InfoVect0LinkObjId="SW-122025_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122024_0" Pin1InfoVect1LinkObjId="g_329a390_0" Pin1InfoVect2LinkObjId="g_2f07dc0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4045,-1023 4124,-1023 4124,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ef7f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4045,-997 4045,-1023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="22662@1" ObjectIDZND0="g_329a390@0" ObjectIDZND1="g_2f07dc0@0" ObjectIDZND2="38125@x" Pin0InfoVect0LinkObjId="g_329a390_0" Pin0InfoVect1LinkObjId="g_2f07dc0_0" Pin0InfoVect2LinkObjId="EC-WD_CD.353Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122024_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4045,-997 4045,-1023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ef8180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4045,-1023 4045,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="22662@x" ObjectIDND1="22663@x" ObjectIDZND0="g_329a390@0" ObjectIDZND1="g_2f07dc0@0" ObjectIDZND2="38125@x" Pin0InfoVect0LinkObjId="g_329a390_0" Pin0InfoVect1LinkObjId="g_2f07dc0_0" Pin0InfoVect2LinkObjId="EC-WD_CD.353Ld_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-122024_0" Pin1InfoVect1LinkObjId="SW-122025_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4045,-1023 4045,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ef83e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-1020 4444,-1020 4444,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="22666@x" ObjectIDND1="g_2f46680@0" ObjectIDND2="38120@1" ObjectIDZND0="22667@1" Pin0InfoVect0LinkObjId="SW-122051_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122050_0" Pin1InfoVect1LinkObjId="g_2f46680_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-1020 4444,-1020 4444,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ef8ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-996 4370,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="22666@1" ObjectIDZND0="g_2f46680@0" ObjectIDZND1="38120@1" ObjectIDZND2="22667@x" Pin0InfoVect0LinkObjId="g_2f46680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="SW-122051_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122050_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-996 4370,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ef9130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-1020 4370,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="powerLine" ObjectIDND0="22666@x" ObjectIDND1="22667@x" ObjectIDZND0="g_2f46680@0" ObjectIDZND1="38120@1" Pin0InfoVect0LinkObjId="g_2f46680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-122050_0" Pin1InfoVect1LinkObjId="SW-122051_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-1020 4370,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ef9390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4611,-1023 4704,-1023 4704,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="22658@x" ObjectIDND1="g_2ba7060@0" ObjectIDND2="37811@1" ObjectIDZND0="22659@1" Pin0InfoVect0LinkObjId="SW-121999_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-121998_0" Pin1InfoVect1LinkObjId="g_2ba7060_0" Pin1InfoVect2LinkObjId="g_331d570_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4611,-1023 4704,-1023 4704,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ef9e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4611,-996 4611,-1023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="22658@1" ObjectIDZND0="g_2ba7060@0" ObjectIDZND1="37811@1" ObjectIDZND2="22659@x" Pin0InfoVect0LinkObjId="g_2ba7060_0" Pin0InfoVect1LinkObjId="g_331d570_1" Pin0InfoVect2LinkObjId="SW-121999_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121998_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4611,-996 4611,-1023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2efa0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4611,-1023 4611,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="powerLine" ObjectIDND0="22658@x" ObjectIDND1="22659@x" ObjectIDZND0="g_2ba7060@0" ObjectIDZND1="37811@1" Pin0InfoVect0LinkObjId="g_2ba7060_0" Pin0InfoVect1LinkObjId="g_331d570_1" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-121998_0" Pin1InfoVect1LinkObjId="SW-121999_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4611,-1023 4611,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2efbcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3732,-533 3788,-533 3788,-554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="22731@x" ObjectIDZND0="g_2efa340@0" Pin0InfoVect0LinkObjId="g_2efa340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-122997_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3732,-533 3788,-533 3788,-554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ee89f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3545,-308 3545,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22818@1" ObjectIDZND0="22715@0" Pin0InfoVect0LinkObjId="SW-122697_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122698_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3545,-308 3545,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ee8c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-414 3694,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22654@0" ObjectIDZND0="22720@0" Pin0InfoVect0LinkObjId="SW-122722_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_332f160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-414 3694,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ee8eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3545,-414 3545,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22654@0" ObjectIDZND0="22716@0" Pin0InfoVect0LinkObjId="SW-122698_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_332f160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3545,-414 3545,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ee9110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3545,-371 3545,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22716@1" ObjectIDZND0="22715@1" Pin0InfoVect0LinkObjId="SW-122697_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122698_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3545,-371 3545,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ee9370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-314 3694,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22817@1" ObjectIDZND0="22719@0" Pin0InfoVect0LinkObjId="SW-122721_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122722_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-314 3694,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ee95d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-360 3694,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22719@1" ObjectIDZND0="22720@1" Pin0InfoVect0LinkObjId="SW-122722_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122721_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-360 3694,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eec030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-233 4626,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22675@0" ObjectIDZND0="g_2eec290@0" Pin0InfoVect0LinkObjId="g_2eec290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-233 4626,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ef0210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-122 4558,-122 4558,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34094@x" ObjectIDND1="22676@x" ObjectIDZND0="g_2ef0930@0" Pin0InfoVect0LinkObjId="g_2ef0930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_CD.061Ld_0" Pin1InfoVect1LinkObjId="SW-122121_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-122 4558,-122 4558,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ef0470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-51 4595,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34094@0" ObjectIDZND0="g_2ef0930@0" ObjectIDZND1="22676@x" Pin0InfoVect0LinkObjId="g_2ef0930_0" Pin0InfoVect1LinkObjId="SW-122121_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_CD.061Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-51 4595,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ef06d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-122 4595,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34094@x" ObjectIDND1="g_2ef0930@0" ObjectIDZND0="22676@0" Pin0InfoVect0LinkObjId="SW-122121_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_CD.061Ld_0" Pin1InfoVect1LinkObjId="g_2ef0930_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-122 4595,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f56d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4749,-232 4749,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22672@0" ObjectIDZND0="g_2f56f50@0" Pin0InfoVect0LinkObjId="g_2f56f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4749,-232 4749,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f5aed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-117 4680,-117 4680,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34095@x" ObjectIDND1="22673@x" ObjectIDZND0="g_2f5b5f0@0" Pin0InfoVect0LinkObjId="g_2f5b5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_CD.062Ld_0" Pin1InfoVect1LinkObjId="SW-122101_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-117 4680,-117 4680,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f5b130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-50 4717,-117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34095@0" ObjectIDZND0="g_2f5b5f0@0" ObjectIDZND1="22673@x" Pin0InfoVect0LinkObjId="g_2f5b5f0_0" Pin0InfoVect1LinkObjId="SW-122101_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_CD.062Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-50 4717,-117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f5b390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-117 4717,-130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34095@x" ObjectIDND1="g_2f5b5f0@0" ObjectIDZND0="22673@0" Pin0InfoVect0LinkObjId="SW-122101_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_CD.062Ld_0" Pin1InfoVect1LinkObjId="g_2f5b5f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-117 4717,-130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f63710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4840,-118 4803,-118 4803,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34096@x" ObjectIDND1="22670@x" ObjectIDZND0="g_2f63e30@0" Pin0InfoVect0LinkObjId="g_2f63e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_CD.063Ld_0" Pin1InfoVect1LinkObjId="SW-122076_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4840,-118 4803,-118 4803,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f63970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4840,-51 4840,-118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34096@0" ObjectIDZND0="g_2f63e30@0" ObjectIDZND1="22670@x" Pin0InfoVect0LinkObjId="g_2f63e30_0" Pin0InfoVect1LinkObjId="SW-122076_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_CD.063Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4840,-51 4840,-118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f63bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4840,-118 4840,-131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34096@x" ObjectIDND1="g_2f63e30@0" ObjectIDZND0="22670@0" Pin0InfoVect0LinkObjId="SW-122076_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_CD.063Ld_0" Pin1InfoVect1LinkObjId="g_2f63e30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4840,-118 4840,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f48ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4873,-233 4873,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22669@0" ObjectIDZND0="g_2f5f790@0" Pin0InfoVect0LinkObjId="g_2f5f790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122075_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4873,-233 4873,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f49ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-280 4562,-280 4562,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="22676@x" ObjectIDND1="22675@x" ObjectIDND2="31749@x" ObjectIDZND0="g_2eecce0@0" Pin0InfoVect0LinkObjId="g_2eecce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122121_0" Pin1InfoVect1LinkObjId="SW-122120_0" Pin1InfoVect2LinkObjId="SW-122118_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-280 4562,-280 4562,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f4a120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-174 4595,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="22676@1" ObjectIDZND0="g_2eecce0@0" ObjectIDZND1="22675@x" ObjectIDZND2="31749@x" Pin0InfoVect0LinkObjId="g_2eecce0_0" Pin0InfoVect1LinkObjId="SW-122120_0" Pin0InfoVect2LinkObjId="SW-122118_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122121_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-174 4595,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f4a380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-280 4626,-280 4626,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="22676@x" ObjectIDND1="g_2eecce0@0" ObjectIDND2="31749@x" ObjectIDZND0="22675@1" Pin0InfoVect0LinkObjId="SW-122120_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122121_0" Pin1InfoVect1LinkObjId="g_2eecce0_0" Pin1InfoVect2LinkObjId="SW-122118_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-280 4626,-280 4626,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f4a5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-279 4685,-279 4685,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="22673@x" ObjectIDND1="22672@x" ObjectIDND2="31751@x" ObjectIDZND0="g_2f579a0@0" Pin0InfoVect0LinkObjId="g_2f579a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122101_0" Pin1InfoVect1LinkObjId="SW-122100_0" Pin1InfoVect2LinkObjId="SW-122098_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-279 4685,-279 4685,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f4a840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-163 4717,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="22673@1" ObjectIDZND0="g_2f579a0@0" ObjectIDZND1="22672@x" ObjectIDZND2="31751@x" Pin0InfoVect0LinkObjId="g_2f579a0_0" Pin0InfoVect1LinkObjId="SW-122100_0" Pin0InfoVect2LinkObjId="SW-122098_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122101_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-163 4717,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f4aaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-279 4749,-279 4749,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="22673@x" ObjectIDND1="g_2f579a0@0" ObjectIDND2="31751@x" ObjectIDZND0="22672@1" Pin0InfoVect0LinkObjId="SW-122100_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122101_0" Pin1InfoVect1LinkObjId="g_2f579a0_0" Pin1InfoVect2LinkObjId="SW-122098_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-279 4749,-279 4749,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f4ad00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4840,-279 4809,-279 4809,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="22670@x" ObjectIDND1="22669@x" ObjectIDND2="31753@x" ObjectIDZND0="g_2f601e0@0" Pin0InfoVect0LinkObjId="g_2f601e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122076_0" Pin1InfoVect1LinkObjId="SW-122075_0" Pin1InfoVect2LinkObjId="SW-122073_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4840,-279 4809,-279 4809,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f4af60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4840,-165 4840,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="22670@1" ObjectIDZND0="g_2f601e0@0" ObjectIDZND1="22669@x" ObjectIDZND2="31753@x" Pin0InfoVect0LinkObjId="g_2f601e0_0" Pin0InfoVect1LinkObjId="SW-122075_0" Pin0InfoVect2LinkObjId="SW-122073_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122076_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4840,-165 4840,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f4b1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4840,-279 4873,-279 4873,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="22670@x" ObjectIDND1="g_2f601e0@0" ObjectIDND2="31753@x" ObjectIDZND0="22669@1" Pin0InfoVect0LinkObjId="SW-122075_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122076_0" Pin1InfoVect1LinkObjId="g_2f601e0_0" Pin1InfoVect2LinkObjId="SW-122073_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4840,-279 4873,-279 4873,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f4fd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-278 4325,-278 4325,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="22708@x" ObjectIDND1="g_2c016f0@0" ObjectIDND2="22816@x" ObjectIDZND0="22707@1" Pin0InfoVect0LinkObjId="SW-122651_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122652_0" Pin1InfoVect1LinkObjId="g_2c016f0_0" Pin1InfoVect2LinkObjId="SW-122648_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-278 4325,-278 4325,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f50a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-205 4290,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="22708@1" ObjectIDZND0="22707@x" ObjectIDZND1="g_2c016f0@0" ObjectIDZND2="22816@x" Pin0InfoVect0LinkObjId="SW-122651_0" Pin0InfoVect1LinkObjId="g_2c016f0_0" Pin0InfoVect2LinkObjId="SW-122648_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122652_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-205 4290,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f50ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-278 4261,-278 4261,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="22707@x" ObjectIDND1="22708@x" ObjectIDND2="22816@x" ObjectIDZND0="g_2c016f0@0" Pin0InfoVect0LinkObjId="g_2c016f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122651_0" Pin1InfoVect1LinkObjId="SW-122652_0" Pin1InfoVect2LinkObjId="SW-122648_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-278 4261,-278 4261,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_332f160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-394 4290,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22706@0" ObjectIDZND0="22654@0" Pin0InfoVect0LinkObjId="g_3300cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122648_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-394 4290,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_332f3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-278 4290,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="22707@x" ObjectIDND1="22708@x" ObjectIDND2="g_2c016f0@0" ObjectIDZND0="22816@0" Pin0InfoVect0LinkObjId="SW-122648_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122651_0" Pin1InfoVect1LinkObjId="SW-122652_0" Pin1InfoVect2LinkObjId="g_2c016f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-278 4290,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_332f620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-317 4290,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22816@1" ObjectIDZND0="22705@0" Pin0InfoVect0LinkObjId="SW-122647_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122648_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-317 4290,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_332f880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-360 4290,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22705@1" ObjectIDZND0="22706@1" Pin0InfoVect0LinkObjId="SW-122648_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122647_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-360 4290,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3333010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-233 4995,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22712@0" ObjectIDZND0="g_3333270@0" Pin0InfoVect0LinkObjId="g_3333270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122677_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4995,-233 4995,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3336af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5003,-162 4960,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="22714@0" ObjectIDZND0="22713@x" ObjectIDZND1="22826@x" Pin0InfoVect0LinkObjId="SW-122678_0" Pin0InfoVect1LinkObjId="CB-WD_CD.WD_CD_2C_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122679_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5003,-162 4960,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3336ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-162 4960,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="22713@x" ObjectIDND1="22714@x" ObjectIDZND0="22826@0" Pin0InfoVect0LinkObjId="CB-WD_CD.WD_CD_2C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-122678_0" Pin1InfoVect1LinkObjId="SW-122679_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-162 4960,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3336ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-162 4960,-168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="22714@x" ObjectIDND1="22826@x" ObjectIDZND0="22713@0" Pin0InfoVect0LinkObjId="SW-122678_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-122679_0" Pin1InfoVect1LinkObjId="CB-WD_CD.WD_CD_2C_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-162 4960,-168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3337360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-277 4995,-277 4995,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="22713@x" ObjectIDND1="22819@x" ObjectIDND2="g_332fae0@0" ObjectIDZND0="22712@1" Pin0InfoVect0LinkObjId="SW-122677_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122678_0" Pin1InfoVect1LinkObjId="SW-122675_0" Pin1InfoVect2LinkObjId="g_332fae0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-277 4995,-277 4995,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3337590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-204 4960,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="22713@1" ObjectIDZND0="22712@x" ObjectIDZND1="22819@x" ObjectIDZND2="g_332fae0@0" Pin0InfoVect0LinkObjId="SW-122677_0" Pin0InfoVect1LinkObjId="SW-122675_0" Pin0InfoVect2LinkObjId="g_332fae0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122678_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-204 4960,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33377c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-277 4931,-277 4931,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="22713@x" ObjectIDND1="22712@x" ObjectIDND2="22819@x" ObjectIDZND0="g_332fae0@0" Pin0InfoVect0LinkObjId="g_332fae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122678_0" Pin1InfoVect1LinkObjId="SW-122677_0" Pin1InfoVect2LinkObjId="SW-122675_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-277 4931,-277 4931,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f0e880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-393 4960,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22711@0" ObjectIDZND0="22655@0" Pin0InfoVect0LinkObjId="g_2f20740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122675_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-393 4960,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f0eae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-277 4960,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="22713@x" ObjectIDND1="22712@x" ObjectIDND2="g_332fae0@0" ObjectIDZND0="22819@0" Pin0InfoVect0LinkObjId="SW-122675_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122678_0" Pin1InfoVect1LinkObjId="SW-122677_0" Pin1InfoVect2LinkObjId="g_332fae0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-277 4960,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f0ed40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-316 4960,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22819@1" ObjectIDZND0="22710@0" Pin0InfoVect0LinkObjId="SW-122674_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122675_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-316 4960,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f0efa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-359 4960,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22710@1" ObjectIDZND0="22711@1" Pin0InfoVect0LinkObjId="SW-122675_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122674_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-359 4960,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f204e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5086,-360 5086,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22723@1" ObjectIDZND0="22724@1" Pin0InfoVect0LinkObjId="SW-122746_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122745_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5086,-360 5086,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f20740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5086,-394 5086,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22724@0" ObjectIDZND0="22655@0" Pin0InfoVect0LinkObjId="g_2f0e880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122746_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5086,-394 5086,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f209a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5207,-359 5207,-378 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22727@1" ObjectIDZND0="22728@1" Pin0InfoVect0LinkObjId="SW-122770_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122769_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5207,-359 5207,-378 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f20c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5207,-395 5207,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22728@0" ObjectIDZND0="22655@0" Pin0InfoVect0LinkObjId="g_2f0e880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5207,-395 5207,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bbc210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5086,-317 5086,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22820@1" ObjectIDZND0="22723@0" Pin0InfoVect0LinkObjId="SW-122745_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122746_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5086,-317 5086,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bbc450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5207,-317 5207,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22823@1" ObjectIDZND0="22727@0" Pin0InfoVect0LinkObjId="SW-122769_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122770_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5207,-317 5207,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bc25b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3732,-414 3732,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22654@0" ObjectIDZND0="22824@0" Pin0InfoVect0LinkObjId="SW-122997_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_332f160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3732,-414 3732,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bc2810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3732,-507 3732,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="22731@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2efa340@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2efa340_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122997_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3732,-507 3732,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd0f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4441,-452 4441,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22694@0" ObjectIDZND0="22814@1" Pin0InfoVect0LinkObjId="SW-122480_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122479_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4441,-452 4441,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd11c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4441,-422 4441,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22814@0" ObjectIDZND0="22655@0" Pin0InfoVect0LinkObjId="g_2f0e880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4441,-422 4441,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd1420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4441,-494 4441,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22695@1" ObjectIDZND0="22694@1" Pin0InfoVect0LinkObjId="SW-122479_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122480_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4441,-494 4441,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd1680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4349,-511 4349,-538 4441,-538 4441,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="22697@0" ObjectIDZND0="22695@0" Pin0InfoVect0LinkObjId="SW-122480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122482_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4349,-511 4349,-538 4441,-538 4441,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd18f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4349,-414 4349,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22654@0" ObjectIDZND0="22815@0" Pin0InfoVect0LinkObjId="SW-122482_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_332f160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4349,-414 4349,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_333fa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4349,-441 4349,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="22815@1" ObjectIDZND0="22697@1" Pin0InfoVect0LinkObjId="SW-122482_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122482_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4349,-441 4349,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3340640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3732,-437 3732,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="22824@1" ObjectIDZND0="g_333fc70@1" Pin0InfoVect0LinkObjId="g_333fc70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122997_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3732,-437 3732,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33408a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3732,-481 3732,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_333fc70@0" ObjectIDZND0="22731@1" Pin0InfoVect0LinkObjId="SW-122997_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_333fc70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3732,-481 3732,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_334c560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4478,-413 4478,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22655@0" ObjectIDZND0="22702@0" Pin0InfoVect0LinkObjId="SW-122637_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f0e880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4478,-413 4478,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_334c7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4478,-324 4478,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3345fe0@1" ObjectIDZND0="22822@1" Pin0InfoVect0LinkObjId="SW-122637_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3345fe0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4478,-324 4478,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_334ca20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4478,-283 4478,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="22822@0" ObjectIDZND0="g_2b93020@0" Pin0InfoVect0LinkObjId="g_2b93020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122637_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4478,-283 4478,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_334cc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4478,-368 4439,-368 4439,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="22702@x" ObjectIDND1="g_3345fe0@0" ObjectIDZND0="g_334dc30@0" Pin0InfoVect0LinkObjId="g_334dc30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-122637_0" Pin1InfoVect1LinkObjId="g_3345fe0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4478,-368 4439,-368 4439,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_334d770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4478,-381 4478,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="22702@1" ObjectIDZND0="g_3345fe0@0" ObjectIDZND1="g_334dc30@0" Pin0InfoVect0LinkObjId="g_3345fe0_0" Pin0InfoVect1LinkObjId="g_334dc30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122637_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4478,-381 4478,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_334d9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4478,-368 4478,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="22702@x" ObjectIDND1="g_334dc30@0" ObjectIDZND0="g_3345fe0@0" Pin0InfoVect0LinkObjId="g_3345fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-122637_0" Pin1InfoVect1LinkObjId="g_334dc30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4478,-368 4478,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3357590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3822,-414 3822,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22654@0" ObjectIDZND0="22700@0" Pin0InfoVect0LinkObjId="SW-122635_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_332f160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3822,-414 3822,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33577f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3822,-324 3822,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3351020@1" ObjectIDZND0="22821@1" Pin0InfoVect0LinkObjId="SW-122635_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3351020_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3822,-324 3822,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3357a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3822,-283 3822,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="22821@0" ObjectIDZND0="g_334e960@0" Pin0InfoVect0LinkObjId="g_334e960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122635_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3822,-283 3822,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3357cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3822,-368 3783,-368 3783,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="22700@x" ObjectIDND1="g_3351020@0" ObjectIDZND0="g_32f9ef0@0" Pin0InfoVect0LinkObjId="g_32f9ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-122635_0" Pin1InfoVect1LinkObjId="g_3351020_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3822,-368 3783,-368 3783,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3357f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3822,-381 3822,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="22700@1" ObjectIDZND0="g_3351020@0" ObjectIDZND1="g_32f9ef0@0" Pin0InfoVect0LinkObjId="g_3351020_0" Pin0InfoVect1LinkObjId="g_32f9ef0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122635_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3822,-381 3822,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3358170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3822,-368 3822,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="22700@x" ObjectIDND1="g_32f9ef0@0" ObjectIDZND0="g_3351020@0" Pin0InfoVect0LinkObjId="g_3351020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-122635_0" Pin1InfoVect1LinkObjId="g_32f9ef0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3822,-368 3822,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32ff980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4813,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4813,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32ffb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4040,-304 4040,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31745@0" ObjectIDZND0="g_2dea800@0" ObjectIDZND1="22681@x" ObjectIDZND2="22682@x" Pin0InfoVect0LinkObjId="g_2dea800_0" Pin0InfoVect1LinkObjId="SW-122168_0" Pin0InfoVect2LinkObjId="SW-122169_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122166_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4040,-304 4040,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32ffd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4163,-279 4163,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2f27a70@0" ObjectIDND1="22678@x" ObjectIDND2="22679@x" ObjectIDZND0="31747@0" Pin0InfoVect0LinkObjId="SW-122142_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f27a70_0" Pin1InfoVect1LinkObjId="SW-122144_0" Pin1InfoVect2LinkObjId="SW-122145_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4163,-279 4163,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32ffff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-283 4595,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="22676@x" ObjectIDND1="g_2eecce0@0" ObjectIDND2="22675@x" ObjectIDZND0="31749@0" Pin0InfoVect0LinkObjId="SW-122118_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122121_0" Pin1InfoVect1LinkObjId="g_2eecce0_0" Pin1InfoVect2LinkObjId="SW-122120_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-283 4595,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3300250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-305 4717,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="31751@0" ObjectIDZND0="22673@x" ObjectIDZND1="g_2f579a0@0" ObjectIDZND2="22672@x" Pin0InfoVect0LinkObjId="SW-122101_0" Pin0InfoVect1LinkObjId="g_2f579a0_0" Pin0InfoVect2LinkObjId="SW-122100_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122098_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-305 4717,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33004b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4840,-281 4840,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="22670@x" ObjectIDND1="g_2f601e0@0" ObjectIDND2="22669@x" ObjectIDZND0="31753@0" Pin0InfoVect0LinkObjId="SW-122073_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-122076_0" Pin1InfoVect1LinkObjId="g_2f601e0_0" Pin1InfoVect2LinkObjId="SW-122075_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4840,-281 4840,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3300cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-447 4110,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31755@0" ObjectIDZND0="22654@0" Pin0InfoVect0LinkObjId="g_332f160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-447 4110,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3300f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-450 4689,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31757@0" ObjectIDZND0="22655@0" Pin0InfoVect0LinkObjId="g_2f0e880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122387_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-450 4689,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_330cc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5086,-278 5059,-278 5059,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="22820@x" ObjectIDND1="22725@x" ObjectIDZND0="g_2c091a0@0" Pin0InfoVect0LinkObjId="g_2c091a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-122746_0" Pin1InfoVect1LinkObjId="SW-122748_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5086,-278 5059,-278 5059,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_330d960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5086,-222 5086,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="g_2c091a0@0" ObjectIDZND1="22820@x" ObjectIDZND2="22725@x" Pin0InfoVect0LinkObjId="g_2c091a0_0" Pin0InfoVect1LinkObjId="SW-122746_0" Pin0InfoVect2LinkObjId="SW-122748_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5086,-222 5086,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_330dbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5086,-278 5086,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2c091a0@0" ObjectIDND1="22725@x" ObjectIDZND0="22820@0" Pin0InfoVect0LinkObjId="SW-122746_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c091a0_0" Pin1InfoVect1LinkObjId="SW-122748_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5086,-278 5086,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_330de20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5113,-264 5113,-278 5086,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22725@1" ObjectIDZND0="g_2c091a0@0" ObjectIDZND1="22820@x" Pin0InfoVect0LinkObjId="g_2c091a0_0" Pin0InfoVect1LinkObjId="SW-122746_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122748_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5113,-264 5113,-278 5086,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_330e080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5182,-267 5182,-277 5207,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2c0d380@0" ObjectIDZND0="22823@x" ObjectIDZND1="22729@x" Pin0InfoVect0LinkObjId="SW-122770_0" Pin0InfoVect1LinkObjId="SW-122772_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c0d380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5182,-267 5182,-277 5207,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_330ed90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5207,-223 5207,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="g_2c0d380@0" ObjectIDZND1="22823@x" ObjectIDZND2="22729@x" Pin0InfoVect0LinkObjId="g_2c0d380_0" Pin0InfoVect1LinkObjId="SW-122770_0" Pin0InfoVect2LinkObjId="SW-122772_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5207,-223 5207,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_330eff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5207,-277 5207,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2c0d380@0" ObjectIDND1="22729@x" ObjectIDZND0="22823@0" Pin0InfoVect0LinkObjId="SW-122770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c0d380_0" Pin1InfoVect1LinkObjId="SW-122772_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5207,-277 5207,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_330f250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5234,-267 5234,-277 5207,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22729@1" ObjectIDZND0="g_2c0d380@0" ObjectIDZND1="22823@x" Pin0InfoVect0LinkObjId="g_2c0d380_0" Pin0InfoVect1LinkObjId="SW-122770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122772_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5234,-267 5234,-277 5207,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_330f4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3508,-263 3508,-271 3545,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2dfa8e0@0" ObjectIDZND0="22818@x" ObjectIDZND1="22717@x" Pin0InfoVect0LinkObjId="SW-122698_0" Pin0InfoVect1LinkObjId="SW-122700_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dfa8e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3508,-263 3508,-271 3545,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_330f710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-277 3657,-277 3657,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="22817@x" ObjectIDND1="22721@x" ObjectIDZND0="g_2bfd510@0" Pin0InfoVect0LinkObjId="g_2bfd510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-122722_0" Pin1InfoVect1LinkObjId="SW-122724_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-277 3657,-277 3657,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3310420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3545,-203 3545,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="g_2dfa8e0@0" ObjectIDZND1="22818@x" ObjectIDZND2="22717@x" Pin0InfoVect0LinkObjId="g_2dfa8e0_0" Pin0InfoVect1LinkObjId="SW-122698_0" Pin0InfoVect2LinkObjId="SW-122700_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3545,-203 3545,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3310680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3545,-271 3545,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2dfa8e0@0" ObjectIDND1="22717@x" ObjectIDZND0="22818@0" Pin0InfoVect0LinkObjId="SW-122698_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2dfa8e0_0" Pin1InfoVect1LinkObjId="SW-122700_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3545,-271 3545,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3311390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-209 3694,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="g_2bfd510@0" ObjectIDZND1="22817@x" ObjectIDZND2="22721@x" Pin0InfoVect0LinkObjId="g_2bfd510_0" Pin0InfoVect1LinkObjId="SW-122722_0" Pin0InfoVect2LinkObjId="SW-122724_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-209 3694,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33115f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-277 3694,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2bfd510@0" ObjectIDND1="22721@x" ObjectIDZND0="22817@0" Pin0InfoVect0LinkObjId="SW-122722_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bfd510_0" Pin1InfoVect1LinkObjId="SW-122724_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-277 3694,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3311850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3572,-263 3572,-271 3545,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22717@1" ObjectIDZND0="g_2dfa8e0@0" ObjectIDZND1="22818@x" Pin0InfoVect0LinkObjId="g_2dfa8e0_0" Pin0InfoVect1LinkObjId="SW-122698_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122700_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3572,-263 3572,-271 3545,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3311ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-269 3721,-277 3694,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22721@1" ObjectIDZND0="g_2bfd510@0" ObjectIDZND1="22817@x" Pin0InfoVect0LinkObjId="g_2bfd510_0" Pin0InfoVect1LinkObjId="SW-122722_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122724_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-269 3721,-277 3694,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3311d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-563 4748,-563 4748,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="31756@x" ObjectIDND1="22733@x" ObjectIDZND0="g_2c15c40@0" Pin0InfoVect0LinkObjId="g_2c15c40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-122387_0" Pin1InfoVect1LinkObjId="g_2c01df0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-563 4748,-563 4748,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3312800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-542 4689,-563 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="31756@0" ObjectIDZND0="g_2c15c40@0" ObjectIDZND1="22733@x" Pin0InfoVect0LinkObjId="g_2c15c40_0" Pin0InfoVect1LinkObjId="g_2c01df0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122387_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-542 4689,-563 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3312a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-563 4689,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_2c15c40@0" ObjectIDND1="31756@x" ObjectIDZND0="22733@0" Pin0InfoVect0LinkObjId="g_2c01df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c15c40_0" Pin1InfoVect1LinkObjId="SW-122387_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-563 4689,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_331acc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3885,-266 3885,-278 3949,-278 3949,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b88b50@0" ObjectIDZND0="22684@1" Pin0InfoVect0LinkObjId="SW-122192_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b88b50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3885,-266 3885,-278 3949,-278 3949,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_331d570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4611,-1091 4611,-1157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2ba7060@0" ObjectIDND1="22658@x" ObjectIDND2="22659@x" ObjectIDZND0="37811@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ba7060_0" Pin1InfoVect1LinkObjId="SW-121998_0" Pin1InfoVect2LinkObjId="SW-121999_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4611,-1091 4611,-1157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3323480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3918,-398 3918,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31742@0" ObjectIDZND0="22654@0" Pin0InfoVect0LinkObjId="g_332f160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3918,-398 3918,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33236e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3918,-321 3918,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31743@1" ObjectIDZND0="22683@0" Pin0InfoVect0LinkObjId="SW-122189_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122190_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3918,-321 3918,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3323940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3918,-364 3918,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22683@1" ObjectIDZND0="31742@1" Pin0InfoVect0LinkObjId="SW-122190_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122189_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3918,-364 3918,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33295e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4040,-399 4040,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31744@0" ObjectIDZND0="22654@0" Pin0InfoVect0LinkObjId="g_332f160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122166_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4040,-399 4040,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3329840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4040,-322 4040,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31745@1" ObjectIDZND0="22680@0" Pin0InfoVect0LinkObjId="SW-122165_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122166_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4040,-322 4040,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3329aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4040,-365 4040,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22680@1" ObjectIDZND0="31744@1" Pin0InfoVect0LinkObjId="SW-122166_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122165_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4040,-365 4040,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_329e6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4163,-399 4163,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31746@0" ObjectIDZND0="22654@0" Pin0InfoVect0LinkObjId="g_332f160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122142_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4163,-399 4163,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_329e8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4163,-322 4163,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31747@1" ObjectIDZND0="22677@0" Pin0InfoVect0LinkObjId="SW-122141_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122142_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4163,-322 4163,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_329eab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4163,-365 4163,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22677@1" ObjectIDZND0="31746@1" Pin0InfoVect0LinkObjId="SW-122142_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122141_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4163,-365 4163,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32aa2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-399 4595,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31748@0" ObjectIDZND0="22655@0" Pin0InfoVect0LinkObjId="g_2f0e880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122118_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-399 4595,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32aa530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-322 4595,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31749@1" ObjectIDZND0="22674@0" Pin0InfoVect0LinkObjId="SW-122117_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122118_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-322 4595,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32aa790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-365 4595,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22674@1" ObjectIDZND0="31748@1" Pin0InfoVect0LinkObjId="SW-122118_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122117_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-365 4595,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32b0500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-399 4717,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31750@0" ObjectIDZND0="22655@0" Pin0InfoVect0LinkObjId="g_2f0e880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122098_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-399 4717,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32b06f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-322 4717,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31751@1" ObjectIDZND0="22671@0" Pin0InfoVect0LinkObjId="SW-122097_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122098_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-322 4717,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32b08e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-365 4717,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22671@1" ObjectIDZND0="31750@1" Pin0InfoVect0LinkObjId="SW-122098_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122097_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-365 4717,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32b9420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4840,-323 4840,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31753@1" ObjectIDZND0="22668@0" Pin0InfoVect0LinkObjId="SW-122072_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122073_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4840,-323 4840,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32b9610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4840,-366 4840,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22668@1" ObjectIDZND0="31752@1" Pin0InfoVect0LinkObjId="SW-122073_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122072_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4840,-366 4840,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32c14e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4840,-400 4840,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31752@0" ObjectIDZND0="22655@0" Pin0InfoVect0LinkObjId="g_2f0e880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122073_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4840,-400 4840,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32c2340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-465 4110,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31755@1" ObjectIDZND0="22689@0" Pin0InfoVect0LinkObjId="SW-122245_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-465 4110,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32c2530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-508 4110,-525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22689@1" ObjectIDZND0="31754@1" Pin0InfoVect0LinkObjId="SW-122250_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122245_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-508 4110,-525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32ca9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-567 4110,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_2f02250@0" ObjectIDND1="22732@x" ObjectIDZND0="31754@0" Pin0InfoVect0LinkObjId="SW-122250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f02250_0" Pin1InfoVect1LinkObjId="g_2efe450_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-567 4110,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32cb4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4146,-549 4146,-567 4110,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_2f02250@0" ObjectIDZND0="31754@x" ObjectIDZND1="22732@x" Pin0InfoVect0LinkObjId="SW-122250_0" Pin0InfoVect1LinkObjId="g_2efe450_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f02250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4146,-549 4146,-567 4110,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32cb720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-610 4110,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="22732@0" ObjectIDZND0="31754@x" ObjectIDZND1="g_2f02250@0" Pin0InfoVect0LinkObjId="SW-122250_0" Pin0InfoVect1LinkObjId="g_2f02250_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2efe450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-610 4110,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32cb980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-508 4689,-525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22693@1" ObjectIDZND0="31756@1" Pin0InfoVect0LinkObjId="SW-122387_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122382_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-508 4689,-525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32d3bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-465 4689,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31757@1" ObjectIDZND0="22693@0" Pin0InfoVect0LinkObjId="SW-122382_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-122387_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-465 4689,-481 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-119709" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3435.500000 -1048.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22306" ObjectName="DYN-WD_CD"/>
     <cge:Meas_Ref ObjectId="119709"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1.022222 -0.000000 0.000000 -1.212121 210.288889 95.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ef3600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3902.000000 579.000000) translate(0,15)">油温(℃)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ef4ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3902.000000 594.000000) translate(0,15)">档位(档)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.966667 -0.000000 0.000000 -1.303030 151.533333 150.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ef5430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4786.000000 579.000000) translate(0,15)">油温(℃)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ef56d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4786.000000 594.000000) translate(0,15)">档位(档)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3312de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4167.000000 758.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3314aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4192.000000 743.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3315310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4197.000000 727.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3315f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4178.000000 773.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33168b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4750.000000 758.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3316b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4775.000000 743.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3316da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4780.000000 727.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3316fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4761.000000 773.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32d6020" transform="matrix(1.438596 -0.000000 0.000000 -1.277751 4965.508772 942.945221) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32d6ca0" transform="matrix(1.438596 -0.000000 0.000000 -1.277751 4965.508772 962.111486) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32d7260" transform="matrix(1.438596 -0.000000 0.000000 -1.277751 4965.508772 980.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32d74b0" transform="matrix(1.438596 -0.000000 0.000000 -1.277751 4954.000000 902.778956) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32d76f0" transform="matrix(1.438596 -0.000000 0.000000 -1.277751 5002.000000 880.778956) translate(0,12)">HZ:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32d7ec0" transform="matrix(1.438596 -0.000000 0.000000 -1.277751 4974.017544 923.334940) translate(0,12)">U0(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f1a90" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 3906.344262 958.260870) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f2eb0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 3893.000000 936.086957) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f3ed0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 3923.327869 913.913043) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f4840" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4214.344262 963.260870) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f4ae0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4201.000000 941.086957) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f4d20" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4231.327869 918.913043) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f5050" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4483.344262 961.260870) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f52b0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4470.000000 939.086957) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f54f0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4500.327869 916.913043) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f5820" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 3478.344262 111.260870) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f5a80" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 3465.000000 89.086957) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f5cc0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 3495.327869 66.913043) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f5ff0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 3634.344262 108.260870) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f6250" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 3621.000000 86.086957) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f6490" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 3651.327869 63.913043) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f67c0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 3855.344262 19.260870) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f6a20" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 3842.000000 -2.913043) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f6c60" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 3872.327869 -25.086957) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f6f90" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 3995.344262 16.260870) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f71f0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 3982.000000 -5.913043) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f7430" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4012.327869 -28.086957) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f7760" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4125.344262 17.260870) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f79c0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4112.000000 -4.913043) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f7c00" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4142.327869 -27.086957) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f7f30" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4274.344262 20.260870) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f8190" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4261.000000 -1.913043) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f83d0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4291.327869 -24.086957) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f8700" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4532.344262 18.260870) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f8960" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4519.000000 -3.913043) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f8ba0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4549.327869 -26.086957) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f8ed0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4655.344262 20.260870) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f9130" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4642.000000 -1.913043) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_32f9370" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4672.327869 -24.086957) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_34245c0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4795.344262 21.260870) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3424820" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4782.000000 -0.913043) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3424a60" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4812.327869 -23.086957) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3424d90" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4933.344262 21.260870) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3424ff0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4920.000000 -0.913043) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3425230" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4950.327869 -23.086957) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3425560" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 5062.344262 40.260870) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_34257c0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 5049.000000 18.086957) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3425a00" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 5079.327869 -4.086957) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3425d30" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 5181.344262 120.260870) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3425f90" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 5168.000000 98.086957) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_34261d0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 5198.327869 75.913043) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3426500" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 3953.344262 512.260870) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3426760" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 3940.000000 490.086957) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_34269a0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 3970.327869 467.913043) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3426cd0" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4789.344262 510.260870) translate(0,11)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3426f30" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4776.000000 488.086957) translate(0,11)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3427170" transform="matrix(1.213115 -0.000000 0.000000 -1.478261 4806.327869 465.913043) translate(0,11)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342b880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4537.000000 456.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342bae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4523.000000 486.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342bd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4512.000000 471.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="WD_DXS" endPointId="0" endStationName="WD_CD" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_dacha" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4370,-1158 4370,-1189 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38120" ObjectName="AC-35kV.LN_dacha"/>
    <cge:TPSR_Ref TObjectID="38120_SS-173"/></metadata>
   <polyline fill="none" opacity="0" points="4370,-1158 4370,-1189 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="WD_CD" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_JinYiChaTcd" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4611,-1154 4611,-1185 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37811" ObjectName="AC-35kV.LN_JinYiChaTcd"/>
    <cge:TPSR_Ref TObjectID="37811_SS-173"/></metadata>
   <polyline fill="none" opacity="0" points="4611,-1154 4611,-1185 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="22653" cx="4611" cy="-859" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22653" cx="4689" cy="-859" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22653" cx="4370" cy="-859" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22653" cx="4370" cy="-859" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22653" cx="4045" cy="-859" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22653" cx="3825" cy="-859" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22655" cx="5086" cy="-413" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22655" cx="4441" cy="-413" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22655" cx="5207" cy="-413" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22654" cx="3694" cy="-414" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22654" cx="3732" cy="-414" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22654" cx="4349" cy="-414" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22655" cx="4478" cy="-413" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22654" cx="3822" cy="-414" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22653" cx="4110" cy="-859" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22654" cx="4290" cy="-414" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22655" cx="4960" cy="-413" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22655" cx="4689" cy="-413" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22654" cx="3545" cy="-414" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22654" cx="3918" cy="-414" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22654" cx="4040" cy="-414" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22654" cx="4163" cy="-414" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22655" cx="4595" cy="-413" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22655" cx="4717" cy="-413" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22655" cx="4840" cy="-413" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22654" cx="4110" cy="-414" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-121996">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4602.238655 -914.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22656" ObjectName="SW-WD_CD.WD_CD_351BK"/>
     <cge:Meas_Ref ObjectId="121996"/>
    <cge:TPSR_Ref TObjectID="22656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122347">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4680.000000 -722.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22690" ObjectName="SW-WD_CD.WD_CD_302BK"/>
     <cge:Meas_Ref ObjectId="122347"/>
    <cge:TPSR_Ref TObjectID="22690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122048">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4361.238655 -914.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22664" ObjectName="SW-WD_CD.WD_CD_352BK"/>
     <cge:Meas_Ref ObjectId="122048"/>
    <cge:TPSR_Ref TObjectID="22664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122022">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4036.238655 -915.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22660" ObjectName="SW-WD_CD.WD_CD_353BK"/>
     <cge:Meas_Ref ObjectId="122022"/>
    <cge:TPSR_Ref TObjectID="22660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122210">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4100.714286 -723.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22686" ObjectName="SW-WD_CD.WD_CD_301BK"/>
     <cge:Meas_Ref ObjectId="122210"/>
    <cge:TPSR_Ref TObjectID="22686"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122697">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3535.885714 -319.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22715" ObjectName="SW-WD_CD.WD_CD_057BK"/>
     <cge:Meas_Ref ObjectId="122697"/>
    <cge:TPSR_Ref TObjectID="22715"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122721">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3685.057143 -325.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22719" ObjectName="SW-WD_CD.WD_CD_056BK"/>
     <cge:Meas_Ref ObjectId="122721"/>
    <cge:TPSR_Ref TObjectID="22719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122647">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4281.000000 -325.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22705" ObjectName="SW-WD_CD.WD_CD_051BK"/>
     <cge:Meas_Ref ObjectId="122647"/>
    <cge:TPSR_Ref TObjectID="22705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122674">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4951.000000 -324.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22710" ObjectName="SW-WD_CD.WD_CD_064BK"/>
     <cge:Meas_Ref ObjectId="122674"/>
    <cge:TPSR_Ref TObjectID="22710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122745">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5077.000000 -325.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22723" ObjectName="SW-WD_CD.WD_CD_065BK"/>
     <cge:Meas_Ref ObjectId="122745"/>
    <cge:TPSR_Ref TObjectID="22723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122769">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5198.000000 -324.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22727" ObjectName="SW-WD_CD.WD_CD_066BK"/>
     <cge:Meas_Ref ObjectId="122769"/>
    <cge:TPSR_Ref TObjectID="22727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122479">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4431.914286 -444.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22694" ObjectName="SW-WD_CD.WD_CD_012BK"/>
     <cge:Meas_Ref ObjectId="122479"/>
    <cge:TPSR_Ref TObjectID="22694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122189">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3909.000000 -329.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22683" ObjectName="SW-WD_CD.WD_CD_054BK"/>
     <cge:Meas_Ref ObjectId="122189"/>
    <cge:TPSR_Ref TObjectID="22683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122165">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4031.000000 -330.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22680" ObjectName="SW-WD_CD.WD_CD_053BK"/>
     <cge:Meas_Ref ObjectId="122165"/>
    <cge:TPSR_Ref TObjectID="22680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122141">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4154.000000 -330.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22677" ObjectName="SW-WD_CD.WD_CD_052BK"/>
     <cge:Meas_Ref ObjectId="122141"/>
    <cge:TPSR_Ref TObjectID="22677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122117">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4586.000000 -330.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22674" ObjectName="SW-WD_CD.WD_CD_061BK"/>
     <cge:Meas_Ref ObjectId="122117"/>
    <cge:TPSR_Ref TObjectID="22674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122097">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4708.000000 -330.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22671" ObjectName="SW-WD_CD.WD_CD_062BK"/>
     <cge:Meas_Ref ObjectId="122097"/>
    <cge:TPSR_Ref TObjectID="22671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122072">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4831.000000 -331.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22668" ObjectName="SW-WD_CD.WD_CD_063BK"/>
     <cge:Meas_Ref ObjectId="122072"/>
    <cge:TPSR_Ref TObjectID="22668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122245">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4101.000000 -473.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22689" ObjectName="SW-WD_CD.WD_CD_001BK"/>
     <cge:Meas_Ref ObjectId="122245"/>
    <cge:TPSR_Ref TObjectID="22689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-122382">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4680.000000 -473.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22693" ObjectName="SW-WD_CD.WD_CD_002BK"/>
     <cge:Meas_Ref ObjectId="122382"/>
    <cge:TPSR_Ref TObjectID="22693"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2df0900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3156.000000 -1006.000000) translate(0,16)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2df0900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3156.000000 -1006.000000) translate(0,36)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2df0900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3156.000000 -1006.000000) translate(0,56)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2df0900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3156.000000 -1006.000000) translate(0,76)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2df0900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3156.000000 -1006.000000) translate(0,96)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2df0900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3156.000000 -1006.000000) translate(0,116)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2df0900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3156.000000 -1006.000000) translate(0,136)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2df0900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3156.000000 -1006.000000) translate(0,156)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2df0900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3156.000000 -1006.000000) translate(0,176)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_335d360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -577.000000) translate(0,16)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_335d360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -577.000000) translate(0,36)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_335d360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -577.000000) translate(0,56)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_335d360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -577.000000) translate(0,76)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_335d360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -577.000000) translate(0,96)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_335d360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -577.000000) translate(0,116)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_335d360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -577.000000) translate(0,136)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_335d360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -577.000000) translate(0,156)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_335d360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -577.000000) translate(0,176)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_335d360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -577.000000) translate(0,196)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_335d360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -577.000000) translate(0,216)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_335d360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -577.000000) translate(0,236)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_335d360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -577.000000) translate(0,256)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_335d360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -577.000000) translate(0,276)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_335d360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -577.000000) translate(0,296)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_335d360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -577.000000) translate(0,316)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_335d360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -577.000000) translate(0,336)">联系方式：8833466</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimSun" font-size="40" graphid="g_2defb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3282.000000 -1154.500000) translate(0,32)">插甸变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dedec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4469.000000 -701.000000) translate(0,15)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dedec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4469.000000 -701.000000) translate(0,33)">SZ11-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dedec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4469.000000 -701.000000) translate(0,51)">10000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dedec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4469.000000 -701.000000) translate(0,69)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dedec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4469.000000 -701.000000) translate(0,87)">YN-d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dedec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4469.000000 -701.000000) translate(0,105)">Ud=7.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_333e2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3894.000000 -713.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_333e2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3894.000000 -713.000000) translate(0,33)">SZ11-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_333e2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3894.000000 -713.000000) translate(0,51)">10000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_333e2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3894.000000 -713.000000) translate(0,69)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_333e2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3894.000000 -713.000000) translate(0,87)">YN-d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_333e2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3894.000000 -713.000000) translate(0,105)">Ud=7.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ba82c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4625.000000 -1183.000000) translate(0,15)">近</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ba82c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4625.000000 -1183.000000) translate(0,33)">邑</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ba82c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4625.000000 -1183.000000) translate(0,51)">插</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ba82c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4625.000000 -1183.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f32530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4381.000000 -1183.000000) translate(0,15)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f32530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4381.000000 -1183.000000) translate(0,33)">插</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f32530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4381.000000 -1183.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_329b5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4054.000000 -1183.000000) translate(0,15)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_329b5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4054.000000 -1183.000000) translate(0,33)">插</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_329b5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4054.000000 -1183.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_2df9de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3769.000000 -1103.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_2c117c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3675.000000 -764.000000) translate(0,15)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_2c11cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5202.000000 -202.000000) translate(0,15)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_2c11cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5202.000000 -202.000000) translate(0,33)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_2c11cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5202.000000 -202.000000) translate(0,51)">四</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_2c11cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5202.000000 -202.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_2bb1740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5079.000000 -202.000000) translate(0,15)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_2bb1740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5079.000000 -202.000000) translate(0,33)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_2bb1740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5079.000000 -202.000000) translate(0,51)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_2bb1740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5079.000000 -202.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_2bb1ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3685.000000 -202.000000) translate(0,15)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_2bb1ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3685.000000 -202.000000) translate(0,33)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_2bb1ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3685.000000 -202.000000) translate(0,51)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_2bb1ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3685.000000 -202.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_2bb2200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3535.000000 -196.000000) translate(0,15)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_2bb2200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3535.000000 -196.000000) translate(0,33)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_2bb2200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3535.000000 -196.000000) translate(0,51)">一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_2bb2200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3535.000000 -196.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_2bb2760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.000000 -67.000000) translate(0,15)">1号电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_2bb2760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.000000 -67.000000) translate(0,33)">容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="20" graphid="g_2bb3510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4439.000000 -232.000000) translate(0,16)">10kVⅡ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="20" graphid="g_2bb3510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4439.000000 -232.000000) translate(0,36)">母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b94720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5090.000000 -454.000000) translate(0,15)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b95530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3477.000000 -441.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b95a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4812.000000 -888.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2b95d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4332.000000 -258.000000) translate(0,16)">05117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2b96410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4346.000000 -192.000000) translate(0,16)">05167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2b96870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4231.000000 -192.000000) translate(0,16)">0516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_33364c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5010.000000 -85.000000) translate(0,15)">2号电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_33364c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5010.000000 -85.000000) translate(0,33)">容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3340b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5002.000000 -256.000000) translate(0,12)">06427</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33415f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5000.000000 -194.000000) translate(0,16)">06467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3341a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4901.000000 -192.000000) translate(0,16)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3341cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4079.000000 -255.000000) translate(0,12)">05317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3341f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4056.000000 -157.000000) translate(0,16)">0536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3342140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3734.057143 -261.000000) translate(0,16)">05617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3342380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3709.057143 -356.000000) translate(0,16)">056</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33425c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4304.000000 -358.000000) translate(0,16)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3342800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4977.000000 -353.000000) translate(0,16)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3342a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3754.000000 -472.000000) translate(0,16)">0551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3342c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5120.000000 -254.000000) translate(0,12)">06527</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33433b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5102.000000 -353.000000) translate(0,16)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33436d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5241.000000 -257.000000) translate(0,12)">06627</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3343910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5226.000000 -356.000000) translate(0,16)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3343b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3583.885714 -256.000000) translate(0,16)">05717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3344e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3564.000000 -346.000000) translate(0,16)">057</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3345490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4756.000000 -257.000000) translate(0,12)">06227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33456d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4732.000000 -154.000000) translate(0,16)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_33506b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3783.000000 -218.000000) translate(0,15)">10kVⅠ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="18" graphid="g_33506b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3783.000000 -218.000000) translate(0,33)">母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32fab60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4203.000000 -258.000000) translate(0,12)">05217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32fb190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4177.000000 -152.000000) translate(0,16)">0526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32fb3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3956.000000 -257.000000) translate(0,12)">05417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32fb610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3933.000000 -155.000000) translate(0,16)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32fb850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4631.000000 -255.000000) translate(0,12)">06127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32fba90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4607.000000 -162.000000) translate(0,16)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32fbcd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4295.000000 -476.000000) translate(0,16)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32fbf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4459.000000 -473.000000) translate(0,16)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32fc150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4880.000000 -257.000000) translate(0,12)">06327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32fc390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4851.000000 -154.000000) translate(0,16)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32fc5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3836.000000 -346.000000) translate(0,16)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32fc810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4494.000000 -348.000000) translate(0,16)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32fcf40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4382.000000 -903.000000) translate(0,16)">3521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32fd260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4454.000000 -996.000000) translate(0,16)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32fd4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4381.000000 -988.000000) translate(0,16)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32fd6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4387.000000 -943.000000) translate(0,16)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32fd920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4628.000000 -902.000000) translate(0,16)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32fdb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4717.000000 -994.000000) translate(0,16)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32fdda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4625.000000 -989.000000) translate(0,16)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32fdfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4629.000000 -945.000000) translate(0,16)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32fe220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4377.000000 -825.000000) translate(0,16)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32fe460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4427.000000 -758.000000) translate(0,16)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32fe6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4058.000000 -897.000000) translate(0,16)">3531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32fe8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4135.000000 -999.000000) translate(0,16)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32feb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4057.000000 -989.000000) translate(0,16)">3536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32fed60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4063.000000 -943.000000) translate(0,16)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3307e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4710.000000 -754.000000) translate(0,16)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3308090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4561.000000 -811.000000) translate(0,16)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33082d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4702.000000 -811.000000) translate(0,16)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3308510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4132.000000 -756.000000) translate(0,16)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3308750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.000000 -811.000000) translate(0,16)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3308990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3978.000000 -815.000000) translate(0,16)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3319860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4184.000000 -667.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3319e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4765.000000 -667.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_331c5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3154.000000 -745.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3326240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -361.000000) translate(0,16)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_329e0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4056.000000 -361.000000) translate(0,16)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32a6fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4179.000000 -361.000000) translate(0,16)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32afed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4612.000000 -362.000000) translate(0,16)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32b8df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4733.000000 -362.000000) translate(0,16)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32c1d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4855.000000 -363.000000) translate(0,16)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32d3e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4065.000000 -504.000000) translate(0,16)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32d4440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4644.000000 -506.000000) translate(0,16)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_32d4840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3449.000000 -1129.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_32d5630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3449.000000 -1164.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(253,245,230)" font-family="SimSun" font-size="20" graphid="g_32d5ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4325.000000 -676.000000) translate(0,16)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32edc30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -114.000000) translate(0,15)">花</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32edc30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -114.000000) translate(0,33)">桥</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32edc30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -114.000000) translate(0,51)">矿</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32edc30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -114.000000) translate(0,69)">山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32edc30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -114.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32eef30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4056.000000 -84.000000) translate(0,15)">安</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32eef30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4056.000000 -84.000000) translate(0,33)">德</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32eef30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4056.000000 -84.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32ef7a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4181.000000 -83.000000) translate(0,15)">古</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32ef7a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4181.000000 -83.000000) translate(0,33)">普</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32ef7a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4181.000000 -83.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32f0020" transform="matrix(1.000000 0.000000 0.000000 1.000000 4609.000000 -100.000000) translate(0,15)">机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32f0020" transform="matrix(1.000000 0.000000 0.000000 1.000000 4609.000000 -100.000000) translate(0,33)">关</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32f0020" transform="matrix(1.000000 0.000000 0.000000 1.000000 4609.000000 -100.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32f08a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4735.000000 -95.000000) translate(0,15)">长</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32f08a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4735.000000 -95.000000) translate(0,33)">冲</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32f08a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4735.000000 -95.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32f1120" transform="matrix(1.000000 0.000000 0.000000 1.000000 4856.000000 -93.000000) translate(0,15)">哪</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32f1120" transform="matrix(1.000000 0.000000 0.000000 1.000000 4856.000000 -93.000000) translate(0,33)">吐</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32f1120" transform="matrix(1.000000 0.000000 0.000000 1.000000 4856.000000 -93.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_34273b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -209.500000) translate(0,16)">武定巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_34286f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3247.000000 -229.500000) translate(0,16)">18787878990</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_34286f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3247.000000 -229.500000) translate(0,36)">18787842893</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_34286f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3247.000000 -229.500000) translate(0,56)">13987880311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3428c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3438.500000 -995.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3429570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3514.000000 -555.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3429a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3514.000000 -570.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3429c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3514.000000 -586.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3429ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3520.000000 -538.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342a110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3506.000000 -523.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342a350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3506.000000 -507.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342a590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3506.000000 -492.000000) translate(0,12)">Uca(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342a7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5136.000000 -534.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342aa10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5136.000000 -549.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342ac50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5136.000000 -565.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342ae90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5142.000000 -517.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342b0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5128.000000 -502.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342b310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5128.000000 -486.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342b550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5128.000000 -471.000000) translate(0,12)">Uca(kV):</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2c05ef0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.043478 4699.600000 -938.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f459d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4437.600000 -938.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3299480" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4118.600000 -938.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c151b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4623.000000 -822.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f017c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4044.714286 -824.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2edea70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4414.000000 -702.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bfcb50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3566.195792 -199.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c00ca0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3714.987375 -205.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f2b740" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4319.000000 -206.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b88100" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3942.657143 -203.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2de9db0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4066.000000 -202.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f27020" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4190.371429 -203.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c0c930" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5107.000000 -200.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c10b10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5228.000000 -203.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eec290" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4620.000000 -205.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f56f50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4743.000000 -204.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f5f790" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4867.000000 -205.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3333270" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4989.000000 -205.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="WD_CD"/>
</svg>