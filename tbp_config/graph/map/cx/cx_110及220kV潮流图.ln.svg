<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="sys" aopId="786686" id="thSvg" viewBox="8059 -4124 6074 3267">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dba9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1db8e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1eeb3a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d97920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dd3b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dd2820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d83cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d81f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e8af70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c40970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c3f670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d629d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d7e1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1db4a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dfd0f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e53390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c3e6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f090e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_150fd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d70d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ed32c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d75620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c3b890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e55900" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1da2d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dcfc20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e10090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    
   </symbol>
   <symbol id="Tag:shape33">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1d77a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline fill="none" points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape34">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline fill="none" points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1e07670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape36">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
   </symbol>
   <symbol id="Tag:shape37">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="3277" width="6084" x="8054" y="-4129"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="14131" x2="14131" y1="-1264" y2="-1074"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="9298" x2="9298" y1="-985" y2="-938"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="9319" x2="9319" y1="-985" y2="-938"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="10114" x2="10114" y1="-1340" y2="-1340"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="10460" x2="10460" y1="-1696" y2="-1696"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="10329" x2="10329" y1="-2145" y2="-2145"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="12427" x2="12541" y1="-2162" y2="-2095"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="12427" x2="12323" y1="-1549" y2="-1445"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="9886" x2="9801" y1="-2625" y2="-2675"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="10152" x2="10039" y1="-1931" y2="-1931"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="10153" x2="10153" y1="-1916" y2="-1916"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="10329" x2="10223" y1="-2145" y2="-2039"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="10572" x2="10572" y1="-1727" y2="-1602"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="10138" x2="10032" y1="-1332" y2="-1280"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="10114" x2="10114" y1="-1342" y2="-1342"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="10522" x2="10522" y1="-2666" y2="-2666"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="12412" x2="12412" y1="-2034" y2="-2034"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="10211" x2="10211" y1="-906" y2="-906"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="10010" x2="10010" y1="-989" y2="-989"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="11369" x2="11369" y1="-1241" y2="-1241"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="10120" x2="10120" y1="-1338" y2="-1338"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="10372" x2="10537" y1="-2656" y2="-2656"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="8061" x2="8061" y1="-1058" y2="-958"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="8393" x2="8409" y1="-859" y2="-859"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="12641" x2="12641" y1="-1972" y2="-1972"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="12638" x2="12752" y1="-1956" y2="-1889"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-19270">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.818275 -1.818275 -1.507254 -1.507254 9654.441562 -3656.829496)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2934" ObjectName="SW-CX_YR.CX_YR_163BK"/>
     <cge:Meas_Ref ObjectId="19270"/>
    <cge:TPSR_Ref TObjectID="2934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-28191">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.285714 -2.226922 -1.846002 -1.065789 9242.607592 -3683.760329)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4557" ObjectName="SW-CX_WM.CX_WM_192BK"/>
     <cge:Meas_Ref ObjectId="28191"/>
    <cge:TPSR_Ref TObjectID="4557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(2.571429 -0.000000 0.000000 -2.131579 9543.857143 -915.105263)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-6840">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(2.135486 -0.000000 0.000000 -2.620204 9853.451562 -2440.608066)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1178" ObjectName="SW-CX_ZX.CX_ZX_283BK"/>
     <cge:Meas_Ref ObjectId="6840"/>
    <cge:TPSR_Ref TObjectID="1178"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.928571 -0.000000 0.000000 -1.858300 10471.142857 -1366.553306)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-27740">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(2.135486 -0.000000 0.000000 -2.620204 11068.451562 -1616.608066)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4485" ObjectName="SW-CX_CY.CX_CY_171BK"/>
     <cge:Meas_Ref ObjectId="27740"/>
    <cge:TPSR_Ref TObjectID="4485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29030">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(2.135486 -0.000000 0.000000 -2.620204 12233.451562 -3886.608066)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4661" ObjectName="SW-CX_YX.CX_YX_161BK"/>
     <cge:Meas_Ref ObjectId="29030"/>
    <cge:TPSR_Ref TObjectID="4661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19983">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.285714 2.226922 1.846002 -1.065789 10261.508417 -1772.940567)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3160" ObjectName="SW-CX_BLXC.CX_BLX_141BK"/>
     <cge:Meas_Ref ObjectId="19983"/>
    <cge:TPSR_Ref TObjectID="3160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-22264">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.063028 -1.892884 -1.526274 -0.905921 10038.640782 -1953.102479)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3624" ObjectName="SW-CX_XJ.CX_XJ_146BK"/>
     <cge:Meas_Ref ObjectId="22264"/>
    <cge:TPSR_Ref TObjectID="3624"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26202">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(2.571429 -0.000000 0.000000 -2.131579 10087.857143 -3121.605263)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4222" ObjectName="SW-CX_DY.CX_DY_152BK"/>
     <cge:Meas_Ref ObjectId="26202"/>
    <cge:TPSR_Ref TObjectID="4222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26867">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.285714 -2.226922 -1.846002 -1.065789 12306.607592 -2230.760329)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4330" ObjectName="SW-CX_DEG.CX_DEG_162BK"/>
     <cge:Meas_Ref ObjectId="26867"/>
    <cge:TPSR_Ref TObjectID="4330"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-2409">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(-1.818275 -1.818275 -1.507254 1.507254 12208.463397 -2037.734455)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="409" ObjectName="SW-CX_LF.CX_LF_144BK"/>
     <cge:Meas_Ref ObjectId="2409"/>
    <cge:TPSR_Ref TObjectID="409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-90956">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 12427.000000 -2041.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19569" ObjectName="SW-CX_LQS.CX_LQS_181BK"/>
     <cge:Meas_Ref ObjectId="90956"/>
    <cge:TPSR_Ref TObjectID="19569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77350">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11612.000000 -3258.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16799" ObjectName="SW-CX_TZS.CX_TZS_354BK"/>
     <cge:Meas_Ref ObjectId="77350"/>
    <cge:TPSR_Ref TObjectID="16799"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-28131" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8974.000000 -3799.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28131" ObjectName="CX_WM:CX_WM_191BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-28132" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8973.000000 -3785.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28132" ObjectName="CX_WM:CX_WM_191BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-28140" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9009.000000 -3745.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28140" ObjectName="CX_WM:CX_WM_194BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-28141" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9008.000000 -3730.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28141" ObjectName="CX_WM:CX_WM_194BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-27968" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9143.000000 -3700.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27968" ObjectName="CX_WM:CX_WM_192BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-27967" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9144.000000 -3715.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27967" ObjectName="CX_WM:CX_WM_192BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-70432" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9382.000000 -3615.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70432" ObjectName="CX_GBL:CX_GBL_151BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-70431" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9383.000000 -3630.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="70431" ObjectName="CX_GBL:CX_GBL_151BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-18994" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9441.000000 -3844.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="18994" ObjectName="CX_YR:CX_YR_161BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-18993" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9442.000000 -3859.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="18993" ObjectName="CX_YR:CX_YR_161BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-19002" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9623.000000 -3811.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19002" ObjectName="CX_YR:CX_YR_162BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-19003" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9622.000000 -3796.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19003" ObjectName="CX_YR:CX_YR_162BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-57176" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9319.000000 -3985.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57176" ObjectName="CX_DL:CX_DL_181BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-57177" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9318.000000 -3970.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57177" ObjectName="CX_DL:CX_DL_181BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-19010" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9584.000000 -3681.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19010" ObjectName="CX_YR:CX_YR_163BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-19011" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9583.000000 -3666.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="19011" ObjectName="CX_YR:CX_YR_163BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-3770" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10657.000000 -3507.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3770" ObjectName="CX_YM:CX_YM_138BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-3771" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10656.000000 -3492.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3771" ObjectName="CX_YM:CX_YM_138BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-3778" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10863.000000 -3462.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3778" ObjectName="CX_YM:CX_YM_139BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-3779" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10862.000000 -3447.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3779" ObjectName="CX_YM:CX_YM_139BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-3730" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10930.000000 -3313.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3730" ObjectName="CX_YM:CX_YM_128BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-3731" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10929.000000 -3298.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3731" ObjectName="CX_YM:CX_YM_128BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-3722" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11033.000000 -3238.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3722" ObjectName="CX_YM:CX_YM_129BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-3723" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11032.000000 -3223.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3723" ObjectName="CX_YM:CX_YM_129BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-3754" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10702.000000 -3280.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3754" ObjectName="CX_YM:CX_YM_135BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-3755" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10701.000000 -3265.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3755" ObjectName="CX_YM:CX_YM_135BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-3746" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10723.000000 -3200.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3746" ObjectName="CX_YM:CX_YM_133BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-3747" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10722.000000 -3185.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3747" ObjectName="CX_YM:CX_YM_133BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-3738" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10863.000000 -3112.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3738" ObjectName="CX_YM:CX_YM_132BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-3739" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10862.000000 -3097.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="3739" ObjectName="CX_YM:CX_YM_132BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-26087" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9959.000000 -3304.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26087" ObjectName="CX_DY:CX_DY_154BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-26088" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9958.000000 -3289.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26088" ObjectName="CX_DY:CX_DY_154BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-26079" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9954.000000 -3211.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26079" ObjectName="CX_DY:CX_DY_153BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-26080" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9953.000000 -3196.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26080" ObjectName="CX_DY:CX_DY_153BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-26072" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10131.000000 -3213.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26072" ObjectName="CX_DY:CX_DY_152BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-26073" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10130.000000 -3198.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26073" ObjectName="CX_DY:CX_DY_152BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-46658" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9100.000000 -3084.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46658" ObjectName="CX_YPJ:CX_YPJ_181BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-46659" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9099.000000 -3069.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46659" ObjectName="CX_YPJ:CX_YPJ_181BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-4343" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10148.000000 -3038.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="4343" ObjectName="CX_YA:CX_YA_161BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-4344" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10147.000000 -3023.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="4344" ObjectName="CX_YA:CX_YA_161BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-25066" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9837.000000 -3032.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25066" ObjectName="CX_DLK:CX_DLK_122BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-25067" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9836.000000 -3017.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25067" ObjectName="CX_DLK:CX_DLK_122BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-25060" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9842.000000 -2919.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25060" ObjectName="CX_DLK:CX_DLK_121BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-25061" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9841.000000 -2904.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25061" ObjectName="CX_DLK:CX_DLK_121BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-6576" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10032.000000 -2604.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6576" ObjectName="CX_ZX:CX_ZX_184BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-6577" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10031.000000 -2590.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6577" ObjectName="CX_ZX:CX_ZX_184BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-6573" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9944.000000 -2535.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6573" ObjectName="CX_ZX:CX_ZX_182BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-6574" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9943.000000 -2522.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6574" ObjectName="CX_ZX:CX_ZX_182BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-6561" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9866.000000 -2446.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6561" ObjectName="CX_ZX:CX_ZX_283BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-6562" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9865.000000 -2431.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6562" ObjectName="CX_ZX:CX_ZX_283BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-6567" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9948.000000 -2275.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6567" ObjectName="CX_ZX:CX_ZX_188BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-6568" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9947.000000 -2260.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6568" ObjectName="CX_ZX:CX_ZX_188BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-6579" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10071.000000 -2233.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6579" ObjectName="CX_ZX:CX_ZX_186BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-6580" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10070.000000 -2218.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6580" ObjectName="CX_ZX:CX_ZX_186BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-6570" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10218.000000 -2243.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6570" ObjectName="CX_ZX:CX_ZX_189BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-6571" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10217.000000 -2228.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6571" ObjectName="CX_ZX:CX_ZX_189BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-43449" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9679.000000 -2039.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43449" ObjectName="CX_FT:CX_FT_161BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-43450" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9678.000000 -2024.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43450" ObjectName="CX_FT:CX_FT_161BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-21886" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10071.000000 -2023.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21886" ObjectName="CX_XJ:CX_XJ_146BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-21887" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10070.000000 -2008.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21887" ObjectName="CX_XJ:CX_XJ_146BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-21872" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10014.000000 -1844.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21872" ObjectName="CX_XJ:CX_XJ_148BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-21873" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10013.000000 -1829.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21873" ObjectName="CX_XJ:CX_XJ_148BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-43159" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9779.000000 -1545.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43159" ObjectName="CX_FJ:CX_FJ_101BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-43160" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9778.000000 -1530.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43160" ObjectName="CX_FJ:CX_FJ_101BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-47079" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10098.000000 -1404.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47079" ObjectName="CX_LHSE:CX_LHSE_183BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-47080" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10097.000000 -1389.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47080" ObjectName="CX_LHSE:CX_LHSE_183BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-47092" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10177.000000 -1275.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47092" ObjectName="CX_LHSE:CX_LHSE_186BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-47093" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10176.000000 -1260.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47093" ObjectName="CX_LHSE:CX_LHSE_186BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-47074" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9945.000000 -1336.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47074" ObjectName="CX_LHSE:CX_LHSE_182BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-47075" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9944.000000 -1321.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47075" ObjectName="CX_LHSE:CX_LHSE_182BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-47102" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9855.000000 -1224.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47102" ObjectName="CX_LHSE:CX_LHSE_188BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-47103" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9854.000000 -1209.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47103" ObjectName="CX_LHSE:CX_LHSE_188BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-47097" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10102.000000 -1191.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47097" ObjectName="CX_LHSE:CX_LHSE_187BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-47098" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10101.000000 -1176.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47098" ObjectName="CX_LHSE:CX_LHSE_187BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-47216" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9383.000000 -1290.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47216" ObjectName="CX_LHSY:CX_LHSY_181BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-47217" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9382.000000 -1275.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47217" ObjectName="CX_LHSY:CX_LHSY_181BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-47814" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10145.000000 -1103.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47814" ObjectName="CX_BGSJ:CX_BGSJ_152BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-47815" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10144.000000 -1088.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47815" ObjectName="CX_BGSJ:CX_BGSJ_152BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-12905" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10568.000000 -2034.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12905" ObjectName="CX_XJH:CX_XJH_128BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-12906" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10567.000000 -2019.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12906" ObjectName="CX_XJH:CX_XJH_128BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-12941" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10574.000000 -2090.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12941" ObjectName="CX_XJH:CX_XJH_221BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-12942" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10573.000000 -2075.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12942" ObjectName="CX_XJH:CX_XJH_221BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-12923" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10614.000000 -2206.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12923" ObjectName="CX_XJH:CX_XJH_124BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-12924" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10619.000000 -2191.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12924" ObjectName="CX_XJH:CX_XJH_124BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-12821" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10843.000000 -2064.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12821" ObjectName="CX_XJH:CX_XJH_223BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-12822" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10842.000000 -2049.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12822" ObjectName="CX_XJH:CX_XJH_223BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-13057" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10719.000000 -1898.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="13057" ObjectName="CX_XJH:CX_XJH_132BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-13058" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10718.000000 -1883.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="13058" ObjectName="CX_XJH:CX_XJH_132BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-30798" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10401.000000 -1706.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30798" ObjectName="CX_DJ:CX_DJ_162BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-30799" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10400.000000 -1691.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="30799" ObjectName="CX_DJ:CX_DJ_162BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-12932" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10748.000000 -1836.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12932" ObjectName="CX_XJH:CX_XJH_127BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-12933" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10747.000000 -1821.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12933" ObjectName="CX_XJH:CX_XJH_127BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-12989" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10891.000000 -1851.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12989" ObjectName="CX_XJH:CX_XJH_126BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-12990" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10890.000000 -1836.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12990" ObjectName="CX_XJH:CX_XJH_126BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-13006" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10957.000000 -1900.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="13006" ObjectName="CX_XJH:CX_XJH_133BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-13007" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10956.000000 -1885.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="13007" ObjectName="CX_XJH:CX_XJH_133BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-12950" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11123.000000 -1990.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12950" ObjectName="CX_XJH:CX_XJH_123BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-12951" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11122.000000 -1975.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12951" ObjectName="CX_XJH:CX_XJH_123BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-16674" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11090.000000 -1876.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="16674" ObjectName="CX_LT:CX_LT_182BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-16675" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11089.000000 -1861.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="16675" ObjectName="CX_LT:CX_LT_182BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-16682" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11139.000000 -1834.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="16682" ObjectName="CX_LT:CX_LT_183BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-16683" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11138.000000 -1819.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="16683" ObjectName="CX_LT:CX_LT_183BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-27661" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11093.000000 -1615.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27661" ObjectName="CX_CY:CX_CY_171BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-27662" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11092.000000 -1600.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27662" ObjectName="CX_CY:CX_CY_171BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-27676" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11240.000000 -1714.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27676" ObjectName="CX_CY:CX_CY_172BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-27677" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11239.000000 -1699.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="27677" ObjectName="CX_CY:CX_CY_172BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-20460" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11518.000000 -1997.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="20460" ObjectName="CX_JS:CX_JS_191BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-20461" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11517.000000 -1982.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="20461" ObjectName="CX_JS:CX_JS_191BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-20476" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11533.000000 -1898.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="20476" ObjectName="CX_JS:CX_JS_193BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-20477" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11532.000000 -1883.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="20477" ObjectName="CX_JS:CX_JS_193BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-1858" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11886.000000 -1998.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1858" ObjectName="CX_LF:CX_LF_145BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-1859" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11885.000000 -1983.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1859" ObjectName="CX_LF:CX_LF_145BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-1853" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11915.000000 -1918.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1853" ObjectName="CX_LF:CX_LF_148BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-1854" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11914.000000 -1904.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1854" ObjectName="CX_LF:CX_LF_148BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-1838" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11950.000000 -1837.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1838" ObjectName="CX_LF:CX_LF_147BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-1839" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11949.000000 -1822.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1839" ObjectName="CX_LF:CX_LF_147BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-1843" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11952.000000 -1689.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1843" ObjectName="CX_LF:CX_LF_141BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-1844" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11951.000000 -1674.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1844" ObjectName="CX_LF:CX_LF_141BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-1999" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12087.000000 -1773.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1999" ObjectName="CX_LF:CX_LF_165BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-2000" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12086.000000 -1758.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="2000" ObjectName="CX_LF:CX_LF_165BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-2007" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12184.000000 -1800.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="2007" ObjectName="CX_LF:CX_LF_149BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-2008" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12183.000000 -1785.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="2008" ObjectName="CX_LF:CX_LF_149BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-1937" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12131.000000 -1937.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1937" ObjectName="CX_LF:CX_LF_161BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-1938" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12130.000000 -1922.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1938" ObjectName="CX_LF:CX_LF_161BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-1943" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12183.000000 -1989.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1943" ObjectName="CX_LF:CX_LF_143BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-1944" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12182.000000 -1974.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1944" ObjectName="CX_LF:CX_LF_143BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-1863" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12193.000000 -2079.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1863" ObjectName="CX_LF:CX_LF_144BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-1864" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12192.000000 -2064.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1864" ObjectName="CX_LF:CX_LF_144BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-1833" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12101.000000 -2121.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1833" ObjectName="CX_LF:CX_LF_146BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-1834" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12100.000000 -2106.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1834" ObjectName="CX_LF:CX_LF_146BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-26731" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12209.000000 -2257.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26731" ObjectName="CX_DEG:CX_DEG_162BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-26732" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12208.000000 -2242.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26732" ObjectName="CX_DEG:CX_DEG_162BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-5474" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12764.000000 -2475.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5474" ObjectName="CX_YZ:CX_YZ_158BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-5475" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12763.000000 -2460.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5475" ObjectName="CX_YZ:CX_YZ_158BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-5465" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12713.000000 -2412.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5465" ObjectName="CX_YZ:CX_YZ_157BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-5466" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12712.000000 -2397.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5466" ObjectName="CX_YZ:CX_YZ_157BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-5447" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12621.000000 -2340.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5447" ObjectName="CX_YZ:CX_YZ_151BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-5448" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12620.000000 -2325.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5448" ObjectName="CX_YZ:CX_YZ_151BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-5456" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12528.000000 -2435.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5456" ObjectName="CX_YZ:CX_YZ_156BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-5457" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12527.000000 -2420.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5457" ObjectName="CX_YZ:CX_YZ_156BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-5597" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12607.000000 -2615.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5597" ObjectName="CX_YZ:CX_YZ_255BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-5598" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12606.000000 -2600.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5598" ObjectName="CX_YZ:CX_YZ_255BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-5606" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12714.000000 -2625.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5606" ObjectName="CX_YZ:CX_YZ_256BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-5607" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12713.000000 -2610.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="5607" ObjectName="CX_YZ:CX_YZ_256BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-48784" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12143.000000 -2542.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="48784" ObjectName="CX_HP:CX_HP_283BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-48785" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12142.000000 -2527.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="48785" ObjectName="CX_HP:CX_HP_283BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-52016" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12133.000000 -2465.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52016" ObjectName="CX_HP:CX_HP_282BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-52017" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12132.000000 -2450.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52017" ObjectName="CX_HP:CX_HP_282BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-48693" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11949.000000 -2364.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="48693" ObjectName="CX_HP:CX_HP_271BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-48694" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11948.000000 -2349.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="48694" ObjectName="CX_HP:CX_HP_271BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-48676" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12062.000000 -2382.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="48676" ObjectName="CX_HP:CX_HP_272BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-48677" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12061.000000 -2367.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="48677" ObjectName="CX_HP:CX_HP_272BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-48710" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11809.000000 -2639.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="48710" ObjectName="CX_HP:CX_HP_273BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-48711" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11808.000000 -2624.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="48711" ObjectName="CX_HP:CX_HP_273BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-48932" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11846.000000 -2535.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="48932" ObjectName="CX_HP:CX_HP_276BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-48933" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11845.000000 -2520.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="48933" ObjectName="CX_HP:CX_HP_276BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-48767" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11840.000000 -2430.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="48767" ObjectName="CX_HP:CX_HP_278BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-48768" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11839.000000 -2415.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="48768" ObjectName="CX_HP:CX_HP_278BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-62126" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10168.000000 -2761.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62126" ObjectName="CX_XQ:CX_XQ_122BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-62127" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10167.000000 -2746.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62127" ObjectName="CX_XQ:CX_XQ_122BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-25352" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10350.000000 -2885.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25352" ObjectName="CX_XQ:CX_XQ_101BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-25353" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10349.000000 -2870.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25353" ObjectName="CX_XQ:CX_XQ_101BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-1646" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12395.000000 -4005.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1646" ObjectName="CX_SS:CX_SS_177BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-1647" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12394.000000 -3990.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1647" ObjectName="CX_SS:CX_SS_177BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-1642" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12383.000000 -3944.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1642" ObjectName="CX_SS:CX_SS_178BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-1643" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12382.000000 -3929.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1643" ObjectName="CX_SS:CX_SS_178BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-1610" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12603.000000 -3983.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1610" ObjectName="CX_SS:CX_SS_171BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-1611" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12602.000000 -3968.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1611" ObjectName="CX_SS:CX_SS_171BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-62683" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12635.000000 -3860.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62683" ObjectName="CX_SS:CX_SS_176BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-62684" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12634.000000 -3845.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62684" ObjectName="CX_SS:CX_SS_176BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-1602" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12553.000000 -3813.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1602" ObjectName="CX_SS:CX_SS_182BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-1603" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12552.000000 -3798.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1603" ObjectName="CX_SS:CX_SS_182BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-1598" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12475.000000 -3764.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1598" ObjectName="CX_SS:CX_SS_183BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-1599" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12474.000000 -3749.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1599" ObjectName="CX_SS:CX_SS_183BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-57935" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12318.000000 -3851.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57935" ObjectName="CX_SS:CX_SS_174BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-57936" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12317.000000 -3836.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57936" ObjectName="CX_SS:CX_SS_174BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-57923" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12415.000000 -3847.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57923" ObjectName="CX_SS:CX_SS_173BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-57925" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12414.000000 -3832.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57925" ObjectName="CX_SS:CX_SS_173BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-74709" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12018.000000 -3714.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74709" ObjectName="CX_GY:CX_GY_183BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-74710" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12017.000000 -3699.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74710" ObjectName="CX_GY:CX_GY_183BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-15349" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12558.000000 -3544.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="15349" ObjectName="CX_WD:CX_WD_151BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-15350" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12557.000000 -3529.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="15350" ObjectName="CX_WD:CX_WD_151BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-72518" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12796.000000 -3547.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72518" ObjectName="CX_TX:CX_TX_161BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-72519" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12796.000000 -3532.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72519" ObjectName="CX_TX:CX_TX_161BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-1606" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12768.000000 -3900.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1606" ObjectName="CX_SS:CX_SS_172BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-1607" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12767.000000 -3885.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1607" ObjectName="CX_SS:CX_SS_172BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-37835" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12362.000000 -3476.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37835" ObjectName="CX_XL:CX_XL_132BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-37836" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12361.000000 -3461.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37836" ObjectName="CX_XL:CX_XL_132BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-1868" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12052.000000 -1577.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1868" ObjectName="CX_LF:CX_LF_142BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-1869" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12051.000000 -1562.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1869" ObjectName="CX_LF:CX_LF_142BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-85134" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12863.000000 -2159.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="85134" ObjectName="CX_ZHY:CX_ZHY_163BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-85135" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12863.000000 -2144.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="85135" ObjectName="CX_ZHY:CX_ZHY_163BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-12914" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10400.000000 -1964.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12914" ObjectName="CX_XJH:CX_XJH_129BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-12915" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10399.000000 -1949.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="12915" ObjectName="CX_XJH:CX_XJH_129BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-47914" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11055.000000 -3479.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47914" ObjectName="CX_LYS:CX_LYS_181BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-47915" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11054.000000 -3464.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47915" ObjectName="CX_LYS:CX_LYS_181BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-87974" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11488.000000 -2065.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87974" ObjectName="CX_SZ:CX_SZ_121BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-87975" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11487.000000 -2050.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87975" ObjectName="CX_SZ:CX_SZ_121BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-88029" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11339.000000 -2055.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88029" ObjectName="CX_SZ:CX_SZ_122BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-88030" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11338.000000 -2040.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88030" ObjectName="CX_SZ:CX_SZ_122BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-83764" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9874.000000 -2339.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83764" ObjectName="CX_ZX:CX_ZX_183BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-83765" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9873.000000 -2324.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="83765" ObjectName="CX_ZX:CX_ZX_183BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-88808" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 13071.000000 -3636.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88808" ObjectName="CX_MGH:CX_MGH_181BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-88809" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 13070.000000 -3622.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88809" ObjectName="CX_MGH:CX_MGH_181BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-88671" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9495.000000 -2084.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88671" ObjectName="CX_DW:CX_DW_1516SW_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-88672" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9494.000000 -2069.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="88672" ObjectName="CX_DW:CX_DW_1516SW_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-92387" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11952.000000 -3274.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92387" ObjectName="CX_MBT:CX_MBT_101BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-92388" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11951.000000 -3259.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="92388" ObjectName="CX_MBT:CX_MBT_101BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-96546" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12383.000000 -3802.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96546" ObjectName="CX_SS:CX_SS_179BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-96547" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12383.000000 -3788.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96547" ObjectName="CX_SS:CX_SS_179BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-96553" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12459.000000 -3812.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96553" ObjectName="CX_SS:CX_SS_181BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-96554" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12459.000000 -3798.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="96554" ObjectName="CX_SS:CX_SS_181BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-102692" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9288.000000 -2296.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102692" ObjectName="CX_HTP:CX_HTP_231BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-102693" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9287.000000 -2281.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102693" ObjectName="CX_HTP:CX_HTP_231BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-104523" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9729.000000 -2415.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="104523" ObjectName="CX_ZX:CX_ZX_281BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-104524" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9728.000000 -2400.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="104524" ObjectName="CX_ZX:CX_ZX_281BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-105377" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8969.000000 -2295.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105377" ObjectName="CX_WJ:CX_WJ_251BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-105378" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8968.000000 -2280.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="105378" ObjectName="CX_WJ:CX_WJ_251BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-90627" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12390.000000 -2030.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="90627" ObjectName="CX_LQS:CX_LQS_181BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-90626" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12391.000000 -2045.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="90626" ObjectName="CX_LQS:CX_LQS_181BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-48751" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12162.000000 -2608.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="48751" ObjectName="CX_HP:CX_HP_279BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-48750" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12163.000000 -2623.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="48750" ObjectName="CX_HP:CX_HP_279BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-52033" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12070.000000 -2671.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52033" ObjectName="CX_HP:CX_HP_281BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-52032" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12071.000000 -2686.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52032" ObjectName="CX_HP:CX_HP_281BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-1772" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12130.000000 -1899.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1772" ObjectName="CX_LF:CX_LF_243BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-1773" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12129.000000 -1884.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="1773" ObjectName="CX_LF:CX_LF_243BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-77291" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11594.000000 -3250.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="77291" ObjectName="CX_TZS:CX_TZS_354BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-77292" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11593.000000 -3235.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="77292" ObjectName="CX_TZS:CX_TZS_354BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-114501" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10127.000000 -1214.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114501" ObjectName="CX_LHSE:CX_LHSE_189BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-114502" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10050.000000 -1199.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114502" ObjectName="CX_LHSE:CX_LHSE_189BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-118400" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11636.000000 -3417.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118400" ObjectName="CX_HW:CX_HW_161BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-118401" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11635.000000 -3402.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="118401" ObjectName="CX_HW:CX_HW_161BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-116525" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12752.000000 -1805.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116525" ObjectName="CX_STP:CX_STP_271BK_P_1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-116526" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12689.000000 -1790.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116526" ObjectName="CX_STP:CX_STP_271BK_Q_2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-72302" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10166.000000 -3705.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72302" ObjectName="CX_XT:CX_XT_151BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-72303" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10165.000000 -3690.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="72303" ObjectName="CX_XT:CX_XT_151BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-124162" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12040.000000 -3188.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124162" ObjectName="CX_DZS:CX_DZS_151BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-124163" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12039.000000 -3174.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124163" ObjectName="CX_DZS:CX_DZS_151BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-124751" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12053.000000 -3102.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124751" ObjectName="CX_LJS:CX_LJS_101BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-124752" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12052.000000 -3087.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="124752" ObjectName="CX_LJS:CX_LJS_101BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-125657" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9939.000000 -3515.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125657" ObjectName="CX_BX:CX_BX_341BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-125658" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9939.000000 -3502.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125658" ObjectName="CX_BX:CX_BX_341BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-115187" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9989.000000 -3471.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="115187" ObjectName="CX_FS:CX_FS_169BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-115188" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9989.000000 -3458.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="115188" ObjectName="CX_FS:CX_FS_169BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-115195" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9989.000000 -3436.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="115195" ObjectName="CX_FS:CX_FS_171BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-115196" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9989.000000 -3423.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="115196" ObjectName="CX_FS:CX_FS_171BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-146218" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9754.000000 -3475.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="146218" ObjectName="CX_LC:CX_LC_152BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-146219" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9754.000000 -3462.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="146219" ObjectName="CX_LC:CX_LC_152BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-146225" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9754.000000 -3435.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="146225" ObjectName="CX_LC:CX_LC_153BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-146226" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9754.000000 -3422.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="146226" ObjectName="CX_LC:CX_LC_153BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-156753" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12041.000000 -3836.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156753" ObjectName="CX_SYS:CX_SYS_161BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-156754" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 12041.000000 -3821.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156754" ObjectName="CX_SYS:CX_SYS_161BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-178737" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11960.000000 -2827.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178737" ObjectName="CX_YS:CX_YS_261BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-178738" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11959.000000 -2812.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178738" ObjectName="CX_YS:CX_YS_261BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-165617" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11149.000000 -3210.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165617" ObjectName="CX_SF:CX_SF_162BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-165618" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11149.000000 -3197.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165618" ObjectName="CX_SF:CX_SF_162BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-165610" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11270.000000 -3210.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165610" ObjectName="CX_SF:CX_SF_163BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-165611" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11269.000000 -3197.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="165611" ObjectName="CX_SF:CX_SF_163BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-176740" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9506.000000 -1505.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176740" ObjectName="CX_DuJ:CX_DuJ_131BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-176741" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 9505.000000 -1490.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="176741" ObjectName="CX_DuJ:CX_DuJ_131BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-89497" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11571.000000 -2664.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89497" ObjectName="CX_BDS:CX_BDS_242BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-89498" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11570.000000 -2649.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89498" ObjectName="CX_BDS:CX_BDS_242BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-89129" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10203.000000 -2568.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89129" ObjectName="CX_ZX:CX_ZX_285BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-89130" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10202.000000 -2553.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="89130" ObjectName="CX_ZX:CX_ZX_285BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-187562" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11576.000000 -2956.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187562" ObjectName="CX_LuC:CX_LuC_261BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-187561" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11577.000000 -2971.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187561" ObjectName="CX_LuC:CX_LuC_261BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-187527" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11849.000000 -2777.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187527" ObjectName="CX_LuC:CX_LuC_LHYX_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-187526" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 11850.000000 -2792.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="187526" ObjectName="CX_LuC:CX_LuC_LHYX_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-194070" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10301.000000 -2519.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194070" ObjectName="CX_ZX:CX_ZX_282BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-194069" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10302.000000 -2534.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194069" ObjectName="CX_ZX:CX_ZX_282BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="9" appendix="MW" dataTimeFlag="0" decimal="2" id="ME-6624" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10237.000000 -2428.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6624" ObjectName="CX_ZX:CX_ZX_286BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="10" appendix="MVar" dataTimeFlag="0" decimal="2" id="ME-6625" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 10236.000000 -2413.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="6625" ObjectName="CX_ZX:CX_ZX_286BK_Q"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="10243" y="-2125"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="10243" y="-2125"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="10061" y="-1970"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="10061" y="-1970"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="9845" y="-1966"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="9845" y="-1966"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="10533" y="-1701"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="10533" y="-1701"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="11048" y="-1834"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="11048" y="-1834"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="11187" y="-1677"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="11187" y="-1677"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="11213" y="-2289"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="11213" y="-2289"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="11406" y="-2142"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="11406" y="-2142"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="11602" y="-1977"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="11602" y="-1977"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="11815" y="-1740"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="11815" y="-1740"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="73" x="10853" y="-1337"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="73" x="10853" y="-1337"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="10452" y="-1520"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="10452" y="-1520"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="10416" y="-2696"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="10416" y="-2696"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="10949" y="-2974"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="10949" y="-2974"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="11204" y="-2881"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="11204" y="-2881"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="10072" y="-3008"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="10072" y="-3008"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="9522" y="-3310"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="9522" y="-3310"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="9526" y="-3843"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="9526" y="-3843"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="9056" y="-3842"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="9056" y="-3842"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="10398" y="-3734"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="10398" y="-3734"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="10638" y="-3766"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="10638" y="-3766"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="10815" y="-3852"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="10815" y="-3852"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="11404" y="-3313"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="11404" y="-3313"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="12309" y="-3426"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="12309" y="-3426"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="12505" y="-3637"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="12505" y="-3637"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="12082" y="-3707"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="12082" y="-3707"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="12163" y="-3966"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="12163" y="-3966"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="12847" y="-4093"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="12847" y="-4093"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="12162" y="-2371"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="12162" y="-2371"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="12448" y="-2166"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="12448" y="-2166"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="13069" y="-2536"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="13069" y="-2536"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="12334" y="-1533"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="12334" y="-1533"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="12000" y="-1518"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="12000" y="-1518"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="96" qtmmishow="hidden" width="96" x="10063" y="-2527"/>
    </a>
   <metadata/><rect fill="white" height="96" opacity="0" stroke="white" transform="" width="96" x="10063" y="-2527"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="96" qtmmishow="hidden" width="96" x="11991" y="-1980"/>
    </a>
   <metadata/><rect fill="white" height="96" opacity="0" stroke="white" transform="" width="96" x="11991" y="-1980"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="96" qtmmishow="hidden" width="96" x="12653" y="-2539"/>
    </a>
   <metadata/><rect fill="white" height="96" opacity="0" stroke="white" transform="" width="96" x="12653" y="-2539"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="96" qtmmishow="hidden" width="96" x="12653" y="-2947"/>
    </a>
   <metadata/><rect fill="white" height="96" opacity="0" stroke="white" transform="" width="96" x="12653" y="-2947"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="96" qtmmishow="hidden" width="96" x="12488" y="-3964"/>
    </a>
   <metadata/><rect fill="white" height="96" opacity="0" stroke="white" transform="" width="96" x="12488" y="-3964"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="96" qtmmishow="hidden" width="96" x="10799" y="-3318"/>
    </a>
   <metadata/><rect fill="white" height="96" opacity="0" stroke="white" transform="" width="96" x="10799" y="-3318"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="9806" y="-2686"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="9806" y="-2686"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="9680" y="-2008"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="9680" y="-2008"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="9780" y="-2989"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="9780" y="-2989"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="9079" y="-3047"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="9079" y="-3047"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="8724" y="-3830"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="8724" y="-3830"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="9265" y="-4045"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="9265" y="-4045"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="9685" y="-1530"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="9685" y="-1530"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="9291" y="-1325"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="9291" y="-1325"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="9489" y="-1156"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="9489" y="-1156"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="9662" y="-1167"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="9662" y="-1167"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="10044" y="-1327"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="10044" y="-1327"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="10046" y="-1151"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="10046" y="-1151"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="10292" y="-1151"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="10292" y="-1151"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="12499" y="-3208"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="12499" y="-3208"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="11812" y="-3694"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="11812" y="-3694"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="12656" y="-1959"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="12656" y="-1959"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="10246" y="-2831"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="10246" y="-2831"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="71" qtmmishow="hidden" width="73" x="10073" y="-3304"/>
    </a>
   <metadata/><rect fill="white" height="71" opacity="0" stroke="white" transform="" width="73" x="10073" y="-3304"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="10199" y="-1777"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="10199" y="-1777"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="1251" qtmmishow="hidden" width="241" x="8554" y="-3266"/>
    </a>
   <metadata/><rect fill="white" height="1251" opacity="0" stroke="white" transform="" width="241" x="8554" y="-3266"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="96" qtmmishow="hidden" width="96" x="10829" y="-1983"/>
    </a>
   <metadata/><rect fill="white" height="96" opacity="0" stroke="white" transform="" width="96" x="10829" y="-1983"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="9328" y="-3590"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="9328" y="-3590"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="9050" y="-3593"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="9050" y="-3593"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="149" qtmmishow="hidden" width="153" x="11967" y="-2571"/>
    </a>
   <metadata/><rect fill="white" height="149" opacity="0" stroke="white" transform="" width="153" x="11967" y="-2571"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="9710" y="-2584"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="9710" y="-2584"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="12835" y="-3643"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="12835" y="-3643"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="12953" y="-2207"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="12953" y="-2207"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="50" qtmmishow="hidden" width="86" x="11169" y="-3502"/>
    </a>
   <metadata/><rect fill="white" height="50" opacity="0" stroke="white" transform="" width="86" x="11169" y="-3502"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="78" qtmmishow="hidden" width="78" x="13096" y="-4090"/>
    </a>
   <metadata/><rect fill="white" height="78" opacity="0" stroke="white" transform="" width="78" x="13096" y="-4090"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="13144" y="-3629"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="13144" y="-3629"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="9491" y="-2054"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="9491" y="-2054"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="11818" y="-3104"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="11818" y="-3104"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="82" x="12037" y="-3287"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="82" x="12037" y="-3287"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="12244" y="-3623"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="12244" y="-3623"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="9284" y="-2266"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="9284" y="-2266"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="82" x="8970" y="-2264"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="82" x="8970" y="-2264"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="45" qtmmishow="hidden" width="76" x="12473" y="-2074"/>
    </a>
   <metadata/><rect fill="white" height="45" opacity="0" stroke="white" transform="" width="76" x="12473" y="-2074"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="12306" y="-2852"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="12306" y="-2852"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="45" qtmmishow="hidden" width="76" x="12414" y="-1785"/>
    </a>
   <metadata/><rect fill="white" height="45" opacity="0" stroke="white" transform="" width="76" x="12414" y="-1785"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="45" qtmmishow="hidden" width="76" x="11684" y="-3290"/>
    </a>
   <metadata/><rect fill="white" height="45" opacity="0" stroke="white" transform="" width="76" x="11684" y="-3290"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="9888" y="-1146"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="9888" y="-1146"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="96" qtmmishow="hidden" width="96" x="10116" y="-3495"/>
    </a>
   <metadata/><rect fill="white" height="96" opacity="0" stroke="white" transform="" width="96" x="10116" y="-3495"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="45" qtmmishow="hidden" width="76" x="11746" y="-3444"/>
    </a>
   <metadata/><rect fill="white" height="45" opacity="0" stroke="white" transform="" width="76" x="11746" y="-3444"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="82" x="12684" y="-1786"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="82" x="12684" y="-1786"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="45" qtmmishow="hidden" width="76" x="10122" y="-3753"/>
    </a>
   <metadata/><rect fill="white" height="45" opacity="0" stroke="white" transform="" width="76" x="10122" y="-3753"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="82" x="12130" y="-3209"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="82" x="12130" y="-3209"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="82" x="12134" y="-3108"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="82" x="12134" y="-3108"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="45" qtmmishow="hidden" width="76" x="9855" y="-3527"/>
    </a>
   <metadata/><rect fill="white" height="45" opacity="0" stroke="white" transform="" width="76" x="9855" y="-3527"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="9653" y="-3478"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="9653" y="-3478"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="49" qtmmishow="hidden" width="83" x="12024" y="-3804"/>
    </a>
   <metadata/><rect fill="white" height="49" opacity="0" stroke="white" transform="" width="83" x="12024" y="-3804"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="45" qtmmishow="hidden" width="76" x="12001" y="-2901"/>
    </a>
   <metadata/><rect fill="white" height="45" opacity="0" stroke="white" transform="" width="76" x="12001" y="-2901"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="11209" y="-3167"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="11209" y="-3167"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="77" qtmmishow="hidden" width="76" x="9551" y="-1458"/>
    </a>
   <metadata/><rect fill="white" height="77" opacity="0" stroke="white" transform="" width="76" x="9551" y="-1458"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="149" qtmmishow="hidden" width="153" x="11714" y="-2957"/>
    </a>
   <metadata/><rect fill="white" height="149" opacity="0" stroke="white" transform="" width="153" x="11714" y="-2957"/></g>
  </g><g id="MotifButton_Layer">
   <g href="楚雄地区_110kV_沙沟变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="10243" y="-2125"/></g>
   <g href="楚雄地区_110kV_西郊变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="10061" y="-1970"/></g>
   <g href="楚雄地区_110kV_东瓜变电站.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="9845" y="-1966"/></g>
   <g href="楚雄地区_110kV_东郊变电站.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="10533" y="-1701"/></g>
   <g href="楚雄地区_110kV_龙潭变电站.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="11048" y="-1834"/></g>
   <g href="楚雄地区_110kV_楚烟变电站.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="11187" y="-1677"/></g>
   <g href="楚雄地区_110kV_广通牵引变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="11213" y="-2289"/></g>
   <g href="楚雄地区_110kV_舍资变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="11406" y="-2142"/></g>
   <g href="楚雄地区_110kV_金山变电站.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="11602" y="-1977"/></g>
   <g href="楚雄地区_110kV_禄丰牵引变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="11815" y="-1740"/></g>
   <g href="楚雄地区_110kV_双柏变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="73" x="10853" y="-1337"/></g>
   <g href="楚雄地区_110kV_东瓜变电站.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="10452" y="-1520"/></g>
   <g href="楚雄地区_110kV_牟定变电站.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="10416" y="-2696"/></g>
   <g href="楚雄地区_110kV_羊臼河牵引变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="10949" y="-2974"/></g>
   <g href="楚雄地区_110kV_甸心牵引变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="11204" y="-2881"/></g>
   <g href="楚雄地区_110kV_姚安变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="10072" y="-3008"/></g>
   <g href="楚雄地区_35kV_六苴变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="9522" y="-3310"/></g>
   <g href="楚雄地区_110kV_永仁变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="9526" y="-3843"/></g>
   <g href="楚雄地区_110kV_万马变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="9056" y="-3842"/></g>
   <g href="楚雄地区_110kV_大湾子牵引变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="10398" y="-3734"/></g>
   <g href="楚雄地区_110kV_元谋牵引变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="10638" y="-3766"/></g>
   <g href="楚雄地区_110kV_沙地变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="10815" y="-3852"/></g>
   <g href="楚雄地区_110kV_黄瓜园变电站.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="11404" y="-3313"/></g>
   <g href="楚雄地区_110kV_兴棱变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="12309" y="-3426"/></g>
   <g href="楚雄地区_110kV_武定变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="12505" y="-3637"/></g>
   <g href="楚雄地区_110kV_果园变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="12082" y="-3707"/></g>
   <g href="楚雄地区_110kV_云新变电站.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="12163" y="-3966"/></g>
   <g href="楚雄地区_110kV_崇德变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="12847" y="-4093"/></g>
   <g href="楚雄地区_110kV_德钢变电站.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="12162" y="-2371"/></g>
   <g href="楚雄地区_110kV_勤丰变电站.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="12448" y="-2166"/></g>
   <g href="楚雄地区_110kV_双楣变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="13069" y="-2536"/></g>
   <g href="楚雄地区_110kV_上营变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="12334" y="-1533"/></g>
   <g href="楚雄地区_110kV_勤丰牵引变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="12000" y="-1518"/></g>
   <g href="楚雄地区_220kV_紫溪变.svg" style="fill-opacity:0"><rect height="96" qtmmishow="hidden" width="96" x="10063" y="-2527"/></g>
   <g href="楚雄地区_220kV_禄丰变电站.svg" style="fill-opacity:0"><rect height="96" qtmmishow="hidden" width="96" x="11991" y="-1980"/></g>
   <g href="楚雄地区_220kV_腰站变电站.svg" style="fill-opacity:0"><rect height="96" qtmmishow="hidden" width="96" x="12653" y="-2539"/></g>
   <g href="楚雄地区_220kV_新立钛业变.svg" style="fill-opacity:0"><rect height="96" qtmmishow="hidden" width="96" x="12653" y="-2947"/></g>
   <g href="楚雄地区_220kV_狮山变.svg" style="fill-opacity:0"><rect height="96" qtmmishow="hidden" width="96" x="12488" y="-3964"/></g>
   <g href="楚雄地区_220kV_元谋变.svg" style="fill-opacity:0"><rect height="96" qtmmishow="hidden" width="96" x="10799" y="-3318"/></g>
   <g href="楚雄地区_110kV_南华变电站.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="9806" y="-2686"/></g>
   <g href="楚雄地区_110kV_凤屯升压站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="9680" y="-2008"/></g>
   <g href="楚雄地区_110kV_大龙口变电站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="9780" y="-2989"/></g>
   <g href="楚雄地区_110kV_渔泡江三级电站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="9079" y="-3047"/></g>
   <g href="楚雄地区_110kV_迤资一级电站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="8724" y="-3830"/></g>
   <g href="楚雄地区_110kV_的鲁升压站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="9265" y="-4045"/></g>
   <g href="楚雄地区_110kV_福嘉升压站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="9685" y="-1530"/></g>
   <g href="楚雄地区_110kV_老虎山变电站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="9291" y="-1325"/></g>
   <g href="楚雄地区_110kV_三丘田变电站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="9489" y="-1156"/></g>
   <g href="楚雄地区_110kV_东瓜变电站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="9662" y="-1167"/></g>
   <g href="楚雄地区_110kV_老虎山变电站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="10044" y="-1327"/></g>
   <g href="楚雄地区_110kV_泥堵河三级电站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="10046" y="-1151"/></g>
   <g href="楚雄地区_35kV_不管河四级电站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="10292" y="-1151"/></g>
   <g href="楚雄地区_110kV_伊尔格变.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="12499" y="-3208"/></g>
   <g href="楚雄地区_110kV_仙人洞升压站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="11812" y="-3694"/></g>
   <g href="楚雄地区_110kV_洪山变电站.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="12656" y="-1959"/></g>
   <g href="楚雄地区_110kV_新桥变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="10246" y="-2831"/></g>
   <g href="楚雄地区_110kV_大姚变电站.svg" style="fill-opacity:0"><rect height="71" qtmmishow="hidden" width="73" x="10073" y="-3304"/></g>
   <g href="楚雄地区_110kV_白龙新村变电站.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="10199" y="-1777"/></g>
   <g href="cx_地调_监视中心.sys.svg" style="fill-opacity:0"><rect height="1251" qtmmishow="hidden" width="241" x="8554" y="-3266"/></g>
   <g href="楚雄地区_220kV_谢家河变电站.svg" style="fill-opacity:0"><rect height="96" qtmmishow="hidden" width="96" x="10829" y="-1983"/></g>
   <g href="楚雄地区_110kV_干巴拉升压站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="9328" y="-3590"/></g>
   <g href="楚雄地区_110kV_多底河电站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="9050" y="-3593"/></g>
   <g href="楚雄地区_500kV_和平变电站.svg" style="fill-opacity:0"><rect height="149" qtmmishow="hidden" width="153" x="11967" y="-2571"/></g>
   <g href="楚雄地区_110kV_云台山升压站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="9710" y="-2584"/></g>
   <g href="楚雄地区_110kV_田心变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="12835" y="-3643"/></g>
   <g href="楚雄地区_110kV_指挥营变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="12953" y="-2207"/></g>
   <g href="楚雄地区_110kV_雷应山升压站.svg" style="fill-opacity:0"><rect height="50" qtmmishow="hidden" width="86" x="11169" y="-3502"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="78" qtmmishow="hidden" width="78" x="13096" y="-4090"/></g>
   <g href="楚雄地区_110kV_勐果河六级电站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="13144" y="-3629"/></g>
   <g href="楚雄地区_110kV_大湾电站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="9491" y="-2054"/></g>
   <g href="楚雄地区_220kV_保顶山风电场.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="11818" y="-3104"/></g>
   <g href="楚雄地区_110kV_茅稗田升压站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="82" x="12037" y="-3287"/></g>
   <g href="楚雄地区_110kV_西和变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="12244" y="-3623"/></g>
   <g href="楚雄地区_220kV_红土坡风电场.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="9284" y="-2266"/></g>
   <g href="楚雄地区_220kV_五街风电场.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="82" x="8970" y="-2264"/></g>
   <g href="楚雄地区_110kV_老青山升压站.svg" style="fill-opacity:0"><rect height="45" qtmmishow="hidden" width="76" x="12473" y="-2074"/></g>
   <g href="楚雄地区_220kV_禄丰南牵引变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="12306" y="-2852"/></g>
   <g href="楚雄地区_220kV_阿普风电场.svg" style="fill-opacity:0"><rect height="45" qtmmishow="hidden" width="76" x="12414" y="-1785"/></g>
   <g href="楚雄地区_35kV_天子山开关站.svg" style="fill-opacity:0"><rect height="45" qtmmishow="hidden" width="76" x="11684" y="-3290"/></g>
   <g href="楚雄地区_110kV_小江河一级电站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="9888" y="-1146"/></g>
   <g href="楚雄地区_220kV_方山变.svg" style="fill-opacity:0"><rect height="96" qtmmishow="hidden" width="96" x="10116" y="-3495"/></g>
   <g href="楚雄地区_110kV_河外升压站.svg" style="fill-opacity:0"><rect height="45" qtmmishow="hidden" width="76" x="11746" y="-3444"/></g>
   <g href="楚雄地区_220kV_三台坡风电场.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="82" x="12684" y="-1786"/></g>
   <g href="楚雄地区_110kV_秀田升压站.svg" style="fill-opacity:0"><rect height="45" qtmmishow="hidden" width="76" x="10122" y="-3753"/></g>
   <g href="楚雄地区_110kV_大中山升压站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="82" x="12130" y="-3209"/></g>
   <g href="楚雄地区_110kV_老尖山升压站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="82" x="12134" y="-3108"/></g>
   <g href="楚雄地区_35kV_天子山开关站.svg" style="fill-opacity:0"><rect height="45" qtmmishow="hidden" width="76" x="9855" y="-3527"/></g>
   <g href="楚雄地区_110kV_莲池变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="9653" y="-3478"/></g>
   <g href="楚雄地区_110kV_三月山风电场.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" width="83" x="12024" y="-3804"/></g>
   <g href="楚雄地区_220kV_彝山风电场.svg" style="fill-opacity:0"><rect height="45" qtmmishow="hidden" width="76" x="12001" y="-2901"/></g>
   <g href="楚雄地区_110kV_哨房变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="11209" y="-3167"/></g>
   <g href="楚雄地区_110kV_杜鹃变.svg" style="fill-opacity:0"><rect height="77" qtmmishow="hidden" width="76" x="9551" y="-1458"/></g>
   <g href="楚雄地区_500kV_鹿城变电站.svg" style="fill-opacity:0"><rect height="149" qtmmishow="hidden" width="153" x="11714" y="-2957"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="9052" y="-3590"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="8797" y="-983"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" fillStyle="1" height="43" stroke="rgb(65,105,225)" stroke-width="1" width="78" x="8500" y="-983"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="8727" y="-3826"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="9081" y="-3044"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="9682" y="-2005"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="10046" y="-1325"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="9293" y="-1322"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="9491" y="-1153"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="9664" y="-1165"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="9687" y="-1526"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="9268" y="-4041"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="12502" y="-3204"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="11814" y="-3691"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="10294" y="-1148"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="10048" y="-1148"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="9782" y="-2986"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="9330" y="-3587"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="9712" y="-2581"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="13147" y="-3625"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="9493" y="-2051"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="11820" y="-3101"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="12039" y="-3284"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="9196" y="-2492"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="9286" y="-2263"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="8972" y="-2261"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="74" x="12474" y="-2073"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="12413" y="-1784"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="41" stroke="rgb(119,136,153)" stroke-width="1" width="73" x="11686" y="-3288"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="9890" y="-1143"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="41" stroke="rgb(119,136,153)" stroke-width="1" width="72" x="11748" y="-3442"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="12686" y="-1783"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="44" stroke="rgb(119,136,153)" stroke-width="1" width="80" x="11172" y="-3499"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="41" stroke="rgb(119,136,153)" stroke-width="1" width="73" x="10124" y="-3751"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="12132" y="-3206"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="12136" y="-3105"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="41" stroke="rgb(119,136,153)" stroke-width="1" width="73" x="9857" y="-3525"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="12026" y="-3801"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(65,105,225)" fillStyle="1" height="43" stroke="rgb(119,136,153)" stroke-width="1" width="78" x="12000" y="-2900"/>
  </g><g id="ConnectNode_Layer"/><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="10877" cy="-1934" fill="rgb(255,255,255)" fillStyle="1" r="46.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="12696" cy="-1919" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="9095" cy="-3804" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="9565" cy="-3804" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="9561" cy="-3272" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="9845" cy="-2646" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="8861" cy="-2469" fill="rgb(255,255,255)" fillStyle="1" r="46.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="10100" cy="-1932" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="12374" cy="-1494" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="12701" cy="-2490" fill="rgb(255,255,255)" fillStyle="1" r="46.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="12201" cy="-2332" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="12486" cy="-2126" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="12536" cy="-3915" fill="rgb(255,255,255)" fillStyle="1" r="46.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="12201" cy="-3927" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="10239" cy="-1737" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="10571" cy="-1662" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="12045" cy="-2495" fill="rgb(213,0,0)" fillStyle="1" r="71.5" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="10282" cy="-2088" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="12701" cy="-2898" fill="rgb(255,255,255)" fillStyle="1" r="46.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="10847" cy="-3269" fill="rgb(255,255,255)" fillStyle="1" r="46.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="12348" cy="-3389" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="12544" cy="-3599" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="12120" cy="-3668" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="13135" cy="-4051" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="12885" cy="-4055" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="13109" cy="-2495" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="11445" cy="-2103" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="11643" cy="-1936" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="12039" cy="-1931" fill="rgb(255,255,255)" fillStyle="1" r="46.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="10491" cy="-1480" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="10892" cy="-1300" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="11251" cy="-2249" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="9884" cy="-1927" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="11244" cy="-2843" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="10988" cy="-2935" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="10285" cy="-2794" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="10111" cy="-2477" fill="rgb(255,255,255)" fillStyle="1" r="46.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="10110" cy="-2969" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="10111" cy="-3268" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="11442" cy="-3274" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="10853" cy="-3813" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="10677" cy="-3728" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="10438" cy="-3695" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="10456" cy="-2658" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="11088" cy="-1794" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="12039" cy="-1478" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="11853" cy="-1701" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="11226" cy="-1640" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="9085" cy="-962" fill="rgb(0,255,0)" fillStyle="1" r="37" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="12873" cy="-3603" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="12988" cy="-2167" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="12283" cy="-3585" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="12344" cy="-2812" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="10164" cy="-3446" fill="rgb(255,255,255)" fillStyle="1" r="46.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="9692" cy="-3440" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="11248" cy="-3128" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="9590" cy="-1418" fill="rgb(170,85,127)" fillStyle="1" r="35" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="11793" cy="-2883" fill="rgb(213,0,0)" fillStyle="1" r="71.5" stroke="rgb(213,0,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" transform="matrix(1.000000 0.000000 0.000000 1.000000 8606.000000 -979.000000) translate(0,28)">火电厂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" transform="matrix(1.000000 0.000000 0.000000 1.000000 8889.000000 -979.000000) translate(0,28)">水电厂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" transform="matrix(1.000000 0.000000 0.000000 1.000000 9339.000000 -979.000000) translate(0,28)">500kV串补站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="35" transform="matrix(1.000000 0.000000 0.000000 1.000000 9136.000000 -979.000000) translate(0,28)">变电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 12745.000000 -2562.000000) translate(0,24)">腰站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 10908.000000 -2020.000000) translate(0,24)">谢家河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 10166.000000 -2482.000000) translate(0,24)">紫溪</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 10743.000000 -3245.000000) translate(0,24)">元谋</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 12188.000000 -2401.000000) translate(0,24)">德钢</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 12530.000000 -2144.000000) translate(0,24)">勤丰</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 11611.000000 -2010.000000) translate(0,24)">金山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 12390.000000 -1450.000000) translate(0,24)">上营</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 11209.000000 -2319.000000) translate(0,24)">广通牵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 11455.000000 -2176.000000) translate(0,24)">舍资</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 10255.000000 -2047.000000) translate(0,24)">沙沟</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 9695.000000 -1564.000000) translate(0,24)">福嘉</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 9263.000000 -1382.000000) translate(0,24)">老虎山一级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 9743.000000 -2663.000000) translate(0,24)">南华</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 9547.000000 -3873.000000) translate(0,24)">永仁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 9060.000000 -3873.000000) translate(0,24)">万马</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 9446.000000 -3253.000000) translate(0,24)">六苴</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 10192.000000 -2809.000000) translate(0,24)">新桥</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 10949.000000 -2893.000000) translate(0,24)">羊臼河牵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 11204.000000 -2801.000000) translate(0,24)">甸心牵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 8734.000000 -3869.000000) translate(0,24)">迤资</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 12581.000000 -3591.000000) translate(0,24)">武定</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 8836.000000 -2415.000000) translate(0,24)">祥云</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 10366.000000 -2825.000000) translate(0,12)">LGJ-240，0.07km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 10920.000000 -2760.000000) translate(0,12)">LGJ-150，13.3km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 9776.000000 -3288.000000) translate(0,12)">LGJ-185, 23.702km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 10958.000000 -1959.000000) translate(0,12)">LGJ-240</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 10960.000000 -1942.000000) translate(0,12)">9.6km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 10934.000000 -2263.000000) translate(0,12)">LGJ-240，3.247km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 11719.000000 -1970.000000) translate(0,12)">LGJ-185，11.9km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 11860.000000 -1902.000000) translate(0,12)">LGJ-185</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 11875.000000 -1886.000000) translate(0,12)">12.3km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 9852.000000 -1516.000000) translate(0,12)">LGJ-240/40</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 9411.000000 -1317.000000) translate(0,12)">LGJ-240,0.217km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 10460.000000 -1300.000000) translate(0,12)">LGJ-240,13.281km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 10150.000000 -1143.000000) translate(0,12)">LGJ-240/40，0.3km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 8865.000000 -3827.000000) translate(0,12)">LGJ-150/25, 8.122km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 9322.000000 -2461.000000) translate(0,12)">LGJ-400/505，59.137km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 12253.000000 -2515.000000) translate(0,12)">2*LGJ-400/50，19.084km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 12252.000000 -2465.000000) translate(0,12)">2*LGJ-400/50，19.051km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 9050.000000 -3536.000000) translate(0,24)">多底河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 9079.000000 -2991.000000) translate(0,24)">渔泡江</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 9649.000000 -1954.000000) translate(0,24)">凤屯风电场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1805500" transform="matrix(1.000000 0.000000 0.000000 1.000000 8976.000000 -3652.000000) translate(0,12)">LGJ-240/40，42km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1805970" transform="matrix(1.000000 0.000000 0.000000 1.000000 9229.000000 -3664.000000) translate(0,12)">LGJ-185/25，47km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1805bb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10478.000000 -3289.000000) translate(0,12)">LGJ-240，58.459km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1805df0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9486.000000 -3097.000000) translate(0,12)">LGJ-150，58km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1806030" transform="matrix(1.000000 0.000000 0.000000 1.000000 10280.000000 -3645.000000) translate(0,12)">LGJ-185，0.576km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1806270" transform="matrix(1.000000 0.000000 0.000000 1.000000 10489.000000 -3645.000000) translate(0,12)">LGJ-150，32.158km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_18d65a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10675.000000 -3472.000000) translate(0,12)">LGJ-150，9.399km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_18d67e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10710.000000 -3668.000000) translate(0,12)">LGJ-150，8.774km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_18d6a20" transform="matrix(1.000000 0.000000 0.000000 1.000000 10998.000000 -3252.000000) translate(0,12)">LGJ-240，20.77km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_18d6c60" transform="matrix(1.000000 0.000000 0.000000 1.000000 9940.000000 -3059.000000) translate(0,12)">LGJ-185，10.653km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_18d6ea0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9897.000000 -2867.000000) translate(0,12)">LGJ-185，6.648km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_18d70e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10575.000000 -2867.000000) translate(0,12)">LGJ-185，10.895km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_18d7320" transform="matrix(1.000000 0.000000 0.000000 1.000000 10753.000000 -2922.000000) translate(0,12)">LGJ-150，13.937km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_18d7560" transform="matrix(1.000000 0.000000 0.000000 1.000000 10833.000000 -3148.000000) translate(0,12)">LGJ-185，20km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_18412f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10867.000000 -3011.000000) translate(0,12)">LGJ-185，0.217kmm</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1841530" transform="matrix(1.000000 0.000000 0.000000 1.000000 11107.000000 -2970.000000) translate(0,12)">LGJ-400/50，82.2km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1841790" transform="matrix(1.000000 0.000000 0.000000 1.000000 10385.000000 -3098.000000) translate(0,12)">LGJ-185，41.186km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1841a00" transform="matrix(1.000000 0.000000 0.000000 1.000000 10454.000000 -2435.000000) translate(0,12)">LGJ-185，43.32km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1841c40" transform="matrix(1.000000 0.000000 0.000000 1.000000 10581.000000 -2224.000000) translate(0,12)">LGJ-185，5.22km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1841e80" transform="matrix(1.000000 0.000000 0.000000 1.000000 10720.000000 -2349.000000) translate(0,12)">LGJ-400/50，84.471km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1842090" transform="matrix(1.000000 0.000000 0.000000 1.000000 10245.000000 -2172.000000) translate(0,12)">LGJ-240/30，10.8km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_18422d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10323.000000 -2239.000000) translate(0,12)">LGJ-400/50，14.453km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1842510" transform="matrix(1.000000 0.000000 0.000000 1.000000 10418.000000 -2041.000000) translate(0,12)">LGJ-240/30，5.9km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1b805e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9536.000000 -1247.000000) translate(0,12)">LGJ-185,3.25km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1b80820" transform="matrix(1.000000 0.000000 0.000000 1.000000 9487.000000 -1099.000000) translate(0,24)">三丘田</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1b80fd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12039.000000 -1547.000000) translate(0,12)">LGJ-240  9km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1b81440" transform="matrix(1.000000 0.000000 0.000000 1.000000 12139.000000 -2142.000000) translate(0,12)">LGJ-240，10.04km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1b81680" transform="matrix(1.000000 0.000000 0.000000 1.000000 12512.000000 -2256.000000) translate(0,12)">LGJ-185/30，11.103km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_19dc310" transform="matrix(1.000000 0.000000 0.000000 1.000000 12256.000000 -2101.000000) translate(0,12)">LGJ-185，13.404km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_19dc580" transform="matrix(1.000000 0.000000 0.000000 1.000000 12334.000000 -2299.000000) translate(0,12)">LGJ-185，21.992km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_19dc7c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12460.000000 -2408.000000) translate(0,12)">LGJ-240，17.543km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_19dca00" transform="matrix(1.000000 0.000000 0.000000 1.000000 12204.000000 -2218.000000) translate(0,12)">LGJ-185，6.843km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_19dcc40" transform="matrix(1.000000 0.000000 0.000000 1.000000 12323.000000 -3913.000000) translate(0,12)">LGJ-185/25，2.812km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_19dce50" transform="matrix(1.000000 0.000000 0.000000 1.000000 12610.000000 -3898.000000) translate(0,12)">LGJ-240/30，3.789km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_19dd090" transform="matrix(1.000000 0.000000 0.000000 1.000000 12557.000000 -3744.000000) translate(0,12)">LGJ-185，2.482km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_19dd300" transform="matrix(1.000000 0.000000 0.000000 1.000000 9580.000000 -3711.000000) translate(0,16)">163</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1843f90" transform="matrix(1.000000 0.000000 0.000000 1.000000 9205.000000 -3769.000000) translate(0,16)">192</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1857520" transform="matrix(1.000000 0.000000 0.000000 1.000000 9978.000000 -2036.000000) translate(0,12)">LGJ-240，0.237km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 10001.000000 -1894.000000) translate(0,24)">西郊</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 10394.000000 -2616.000000) translate(0,24)">牟定</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1855bc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11970.000000 -2024.000000) translate(0,24)">禄丰</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 9850.000000 -1322.000000) translate(0,12)">LGJ-240,3km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 11060.000000 -1754.000000) translate(0,24)">龙潭</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 10544.000000 -1625.000000) translate(0,24)">东郊</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 10170.000000 -1684.000000) translate(0,24)">白龙新村</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_18b7960" transform="matrix(1.000000 0.000000 0.000000 1.000000 11891.000000 -2573.000000) translate(0,24)">和平</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_19da3e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12268.000000 -2314.000000) translate(0,16)">162</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_19da840" transform="matrix(1.000000 0.000000 0.000000 1.000000 12117.000000 -2052.000000) translate(0,16)">144</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_19dac50" transform="matrix(1.000000 0.000000 0.000000 1.000000 11102.000000 -1681.000000) translate(0,16)">171</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_19db1b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10263.000000 -1805.000000) translate(0,16)">141</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_19db410" transform="matrix(1.000000 0.000000 0.000000 1.000000 10173.000000 -1818.000000) translate(0,12)">3.81km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1863c80" transform="matrix(1.000000 0.000000 0.000000 1.000000 10031.000000 -2012.000000) translate(0,16)">146</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1863ec0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9883.000000 -2518.000000) translate(0,24)">283</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1864880" transform="matrix(1.000000 0.000000 0.000000 1.000000 10129.000000 -3175.000000) translate(0,16)">152</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1864ef0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10381.000000 -3454.000000) translate(0,12)">LGJ-185, 39.87km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1865140" transform="matrix(1.000000 0.000000 0.000000 1.000000 11078.000000 -2081.000000) translate(0,12)">LGJ-185，1.265km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_183ed70" transform="matrix(1.000000 0.000000 0.000000 1.000000 10044.000000 -2269.000000) translate(0,12)">LGJ-240,  7.2km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_183efb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10619.000000 -1884.000000) translate(0,12)">LGJ-240，5.2km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_183f1f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10378.000000 -1727.000000) translate(0,12)">LGJ-240，0.394km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_183f430" transform="matrix(1.000000 0.000000 0.000000 1.000000 10255.000000 -1828.000000) translate(0,12)">LGJ-240，4.312km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_183f670" transform="matrix(1.000000 0.000000 0.000000 1.000000 11204.000000 -1601.000000) translate(0,24)">楚烟</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_183fb90" transform="matrix(1.000000 0.000000 0.000000 1.000000 10424.000000 -1813.000000) translate(0,12)">LGJ-240，6.62km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1840100" transform="matrix(1.000000 0.000000 0.000000 1.000000 10109.000000 -1867.000000) translate(0,12)">LGJ-240，3km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_186be50" transform="matrix(1.000000 0.000000 0.000000 1.000000 12423.000000 -3274.000000) translate(0,12)">LGJ-120，38.305km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_186c090" transform="matrix(1.000000 0.000000 0.000000 1.000000 12548.000000 -3505.000000) translate(0,12)">LGJ-185，0.56km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_186c2d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12386.000000 -3415.000000) translate(0,12)">LGJ-120，2.1267km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_186c510" transform="matrix(1.000000 0.000000 0.000000 1.000000 12433.000000 -3703.000000) translate(0,12)">LGJ-185，2.482km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_186c750" transform="matrix(1.000000 0.000000 0.000000 1.000000 12713.000000 -3953.000000) translate(0,12)">LGJ-240/30，3.018km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_186c9b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12297.000000 -3960.000000) translate(0,12)">LGJ-185/25，2.812km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_186cbf0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11431.000000 -2907.000000) translate(0,12)">LGJ-150，25.47km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_186ce60" transform="matrix(1.000000 0.000000 0.000000 1.000000 12781.000000 -2524.000000) translate(0,12)">LGJ-240，14.5km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_186d0a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12855.000000 -2485.000000) translate(0,12)">LGJ-185，2.14km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_186d2e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12569.000000 -2700.000000) translate(0,12)">LGJ-300/40，4.3km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_184ab00" transform="matrix(1.000000 0.000000 0.000000 1.000000 12248.000000 -2029.000000) translate(0,12)">LGJ-240，13.696km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_184ad40" transform="matrix(1.000000 0.000000 0.000000 1.000000 11870.000000 -2245.000000) translate(0,12)">NRLH58GJ-400/50，13.5km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_184b740" transform="matrix(1.000000 0.000000 0.000000 1.000000 12716.000000 -2287.000000) translate(0,12)">LGJ-240/30，21.113km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_184b9c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12343.000000 -1929.000000) translate(0,12)">LGJ-240，27.868km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_184bc30" transform="matrix(1.000000 0.000000 0.000000 1.000000 12225.000000 -1733.000000) translate(0,12)">LGJ-240  9km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_184be70" transform="matrix(1.000000 0.000000 0.000000 1.000000 11914.000000 -1603.000000) translate(0,12)">LGJ-185，16.127km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_184c0b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11899.000000 -1749.000000) translate(0,12)">LGJ-185，19.5538km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_19ced00" transform="matrix(1.000000 0.000000 0.000000 1.000000 11736.000000 -1836.000000) translate(0,12)">LGJ-185，5.781km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_19cef70" transform="matrix(1.000000 0.000000 0.000000 1.000000 11233.000000 -1858.000000) translate(0,12)">LGJ-240,5.85km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_19cf1b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11127.000000 -1780.000000) translate(0,12)">LGJ-240,0.36km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_19cf3f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10971.000000 -1864.000000) translate(0,12)">LGJ-240,3.768km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_19cf630" transform="matrix(1.000000 0.000000 0.000000 1.000000 11122.000000 -1961.000000) translate(0,12)">LGJ-240,12.032km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_19cf870" transform="matrix(1.000000 0.000000 0.000000 1.000000 11266.000000 -1962.000000) translate(0,12)">LGJ-240,14.558km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_19cfab0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11516.000000 -2299.000000) translate(0,12)">LGJ-400/50，56.073km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_19cfcc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10895.000000 -1529.000000) translate(0,12)">LGJ-240,64.701km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_19cff30" transform="matrix(1.000000 0.000000 0.000000 1.000000 10450.000000 -1550.000000) translate(0,24)">大红山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_19d5080" transform="matrix(1.000000 0.000000 0.000000 1.000000 10040.000000 -1094.000000) translate(0,24)">不管河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_19d5980" transform="matrix(1.000000 0.000000 0.000000 1.000000 10132.000000 -1351.000000) translate(0,24)">老虎山二级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_19d5ed0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10290.000000 -1099.000000) translate(0,24)">泥堵河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_19d6750" transform="matrix(1.000000 0.000000 0.000000 1.000000 10091.000000 -1206.000000) translate(0,12)">LGJ-185,16.653km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_192de30" transform="matrix(1.000000 0.000000 0.000000 1.000000 9642.000000 -1112.000000) translate(0,24)">老虎山零级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_192e350" transform="matrix(1.000000 0.000000 0.000000 1.000000 9958.000000 -1718.000000) translate(0,12)">LGJ-240/40，82.7km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_192e5f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9924.000000 -1956.000000) translate(0,12)">LGJ-240，0.637km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_192e860" transform="matrix(1.000000 0.000000 0.000000 1.000000 10677.000000 -3063.000000) translate(0,12)">LGJ-185，7.031km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_192eaa0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9984.000000 -2127.000000) translate(0,12)">LGJ-240, 30.9km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_192ece0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9853.000000 -2171.000000) translate(0,12)">LGJ-240/30，8.42km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_192eef0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9988.000000 -2619.000000) translate(0,12)">LGJ-240/30，24.10km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_192f130" transform="matrix(1.000000 0.000000 0.000000 1.000000 10625.000000 -3624.000000) translate(0,12)">LGJ-150，2.713km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_192f3a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9949.000000 -2651.000000) translate(0,12)">LGJ-240，7km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_192f5e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10116.000000 -2857.000000) translate(0,12)">LGJ-185，34.29km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_192f820" transform="matrix(1.000000 0.000000 0.000000 1.000000 10126.000000 -3098.000000) translate(0,12)">LGJ-185，31.376km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1834010" transform="matrix(1.000000 0.000000 0.000000 1.000000 11002.000000 -3310.000000) translate(0,12)">LGJ-240，20.77km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1834230" transform="matrix(1.000000 0.000000 0.000000 1.000000 10855.000000 -3504.000000) translate(0,12)">LGJ-185, 2.3km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1834470" transform="matrix(1.000000 0.000000 0.000000 1.000000 10861.000000 -3700.000000) translate(0,12)">LGJ-185, 0.099km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 11713.000000 -3687.000000) translate(0,24)">仙人洞</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 12848.000000 -3676.000000) translate(0,24)">田心</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 12088.000000 -3631.000000) translate(0,24)">果园</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 9658.000000 -2935.000000) translate(0,24)">大龙口风电场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 11158.000000 -2509.000000) translate(0,12)">2*LGJ-300/40    LGJ-400/50,69.12km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 10179.000000 -1292.000000) translate(0,12)">LGJ-240/GJ-185，47.344km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 11713.000000 -1916.000000) translate(0,12)">LGJ-185，4.68km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 11509.000000 -1912.000000) translate(0,12)">26.21km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 11268.000000 -1909.000000) translate(0,12)">LGJ-185,49.061km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 10957.000000 -1914.000000) translate(0,12)">LGJ-240,3.768km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 10920.000000 -1670.000000) translate(0,12)">LGJ-185/25，3.08km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 10299.000000 -1931.000000) translate(0,12)">LGJ-240，7.766km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 12856.000000 -4124.000000) translate(0,24)">崇德</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 13105.000000 -4122.000000) translate(0,24)">禄劝</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 12596.000000 -3199.000000) translate(0,24)">伊尔格（勐果河）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 12322.000000 -3342.000000) translate(0,24)">兴棱</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 12158.000000 -4001.000000) translate(0,24)">云新</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 12640.000000 -2983.000000) translate(0,24)">新立钛业</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 11398.000000 -3347.000000) translate(0,24)">黄瓜园</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 10823.000000 -3887.000000) translate(0,24)">沙地</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 10148.000000 -2961.000000) translate(0,24)">姚安</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 9846.000000 -1883.000000) translate(0,24)">东瓜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 10933.000000 -1277.000000) translate(0,24)">双柏</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 11994.000000 -1440.000000) translate(0,24)">勤丰牵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 13152.000000 -2508.000000) translate(0,24)">双楣</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 12740.000000 -1928.000000) translate(0,24)">洪山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 12514.000000 -4000.000000) translate(0,24)">狮山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 11718.000000 -1712.000000) translate(0,24)">禄丰牵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1938680" transform="matrix(1.000000 0.000000 0.000000 1.000000 11867.000000 -1868.000000) translate(0,12)">110kV禄牵I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_186e8f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11844.000000 -1938.000000) translate(0,12)">110kV禄金II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_186ef70" transform="matrix(1.000000 0.000000 0.000000 1.000000 11840.000000 -1964.000000) translate(0,12)">110kV禄金I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_186f1f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12129.000000 -1659.000000) translate(0,12)">110kV禄上I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_186f710" transform="matrix(1.000000 0.000000 0.000000 1.000000 12237.000000 -1712.000000) translate(0,12)">110kV禄上II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_186f990" transform="matrix(1.000000 0.000000 0.000000 1.000000 12334.000000 -1950.000000) translate(0,12)">110kV禄洪线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1877dc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12058.000000 -2273.000000) translate(0,12)">220kV和禄I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1878650" transform="matrix(1.000000 0.000000 0.000000 1.000000 11914.000000 -2275.000000) translate(0,12)">220kV和禄II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18788d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11399.000000 -2236.000000) translate(0,12)">220kV和谢I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1878df0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10075.000000 -3338.000000) translate(0,24)">大姚</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1879060" transform="matrix(1.000000 0.000000 0.000000 1.000000 12322.000000 -2263.000000) translate(0,12)">110kV禄腰德线_禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18a3b90" transform="matrix(1.000000 0.000000 0.000000 1.000000 12893.000000 -2506.000000) translate(0,12)">110kV腰双线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18a4200" transform="matrix(1.000000 0.000000 0.000000 1.000000 12716.000000 -2311.000000) translate(0,12)">110kV腰洪指线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18a4770" transform="matrix(1.000000 0.000000 0.000000 1.000000 12610.000000 -2301.000000) translate(0,12)">110kV腰勤线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18a4cc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12219.000000 -2448.000000) translate(0,12)">220kV和腰I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18a4f50" transform="matrix(1.000000 0.000000 0.000000 1.000000 12423.000000 -2531.000000) translate(0,12)">220kV和腰II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18a5190" transform="matrix(1.000000 0.000000 0.000000 1.000000 12049.000000 -1701.000000) translate(0,12)">110kV勤牵Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18a56b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11926.000000 -1712.000000) translate(0,12)">110kV勤牵I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18a5930" transform="matrix(1.000000 0.000000 0.000000 1.000000 12231.000000 -2015.000000) translate(0,12)">110kV禄勤老线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19ded70" transform="matrix(1.000000 0.000000 0.000000 1.000000 10916.000000 -2511.000000) translate(0,12)">220kV和紫线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19df180" transform="matrix(1.000000 0.000000 0.000000 1.000000 11315.000000 -2958.000000) translate(0,12)">220kV和元Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19dfa10" transform="matrix(1.000000 0.000000 0.000000 1.000000 12321.000000 -3899.000000) translate(0,12)">110kV狮云I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e0290" transform="matrix(1.000000 0.000000 0.000000 1.000000 12362.000000 -3974.000000) translate(0,12)">110kV狮云II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e0510" transform="matrix(1.000000 0.000000 0.000000 1.000000 12441.000000 -3723.000000) translate(0,12)">110kV狮武I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e0a30" transform="matrix(1.000000 0.000000 0.000000 1.000000 12554.000000 -3762.000000) translate(0,12)">110kV狮武II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e0cb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12588.000000 -3953.000000) translate(0,12)">110kV狮禄崇I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e11d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12750.000000 -3996.000000) translate(0,12)">110kV狮禄崇I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e52c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12903.000000 -3992.000000) translate(0,12)">110kV狮禄崇II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e5500" transform="matrix(1.000000 0.000000 0.000000 1.000000 12876.000000 -3882.000000) translate(0,12)">110kV狮禄崇II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e5740" transform="matrix(1.000000 0.000000 0.000000 1.000000 12149.000000 -2177.000000) translate(0,12)">110kV德钢I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e5b80" transform="matrix(1.000000 0.000000 0.000000 1.000000 10364.000000 -2222.000000) translate(0,12)">220kV谢紫线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e5df0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10703.000000 -1915.000000) translate(0,12)">110kV谢白线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e6310" transform="matrix(1.000000 0.000000 0.000000 1.000000 10686.000000 -1782.000000) translate(0,12)">110kV谢东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e6870" transform="matrix(1.000000 0.000000 0.000000 1.000000 10373.000000 -1740.000000) translate(0,12)">110kV东郊T接线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e7420" transform="matrix(1.000000 0.000000 0.000000 1.000000 10386.000000 -2066.000000) translate(0,12)">110kV谢沙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b99c80" transform="matrix(1.000000 0.000000 0.000000 1.000000 10297.000000 -1956.000000) translate(0,12)">110kV谢西线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b9a070" transform="matrix(1.000000 0.000000 0.000000 1.000000 9959.000000 -2051.000000) translate(0,12)">110kV紫西东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b9a320" transform="matrix(1.000000 0.000000 0.000000 1.000000 10058.000000 -2250.000000) translate(0,12)">110kV紫白西线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b9a560" transform="matrix(1.000000 0.000000 0.000000 1.000000 9933.000000 -2243.000000) translate(0,12)">110kV紫东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b9a790" transform="matrix(1.000000 0.000000 0.000000 1.000000 9897.000000 -2291.000000) translate(0,12)">110kV紫凤线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b9acb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10210.000000 -2259.000000) translate(0,12)">110kV紫沙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b9af30" transform="matrix(1.000000 0.000000 0.000000 1.000000 10116.000000 -2877.000000) translate(0,12)">110kV紫姚南线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b9b780" transform="matrix(1.000000 0.000000 0.000000 1.000000 10517.000000 -3245.000000) translate(0,12)">110kV元大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b9bcd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11028.000000 -3397.000000) translate(0,12)">110kV元雷线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b9c230" transform="matrix(1.000000 0.000000 0.000000 1.000000 11187.000000 -3304.000000) translate(0,12)">110kV元黄哨II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d0af0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10601.000000 -3049.000000) translate(0,12)">110kV元牟线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d1040" transform="matrix(1.000000 0.000000 0.000000 1.000000 10412.000000 -3434.000000) translate(0,12)">110kV方元大线永仁侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d1bf0" transform="matrix(1.000000 0.000000 0.000000 1.000000 8865.000000 -3794.000000) translate(0,12)">110kV迤万线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d2460" transform="matrix(1.000000 0.000000 0.000000 1.000000 9663.000000 -3289.000000) translate(0,12)">110kV大六线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d29c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10117.000000 -3231.000000) translate(0,12)">110kV姚大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d2c40" transform="matrix(1.000000 0.000000 0.000000 1.000000 10117.000000 -3057.000000) translate(0,12)">110kV姚大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d2e80" transform="matrix(1.000000 0.000000 0.000000 1.000000 10730.000000 -2381.000000) translate(0,12)">220kV谢方线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d30c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10888.000000 -1495.000000) translate(0,12)">110kV谢烟双线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1870530" transform="matrix(1.000000 0.000000 0.000000 1.000000 10888.000000 -1780.000000) translate(0,12)">110kV谢烟双线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18707b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9103.000000 -3665.000000) translate(0,12)">110kV多万线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(238,0,0)" font-family="SimSun" font-size="255" graphid="g_1870ca0" transform="matrix(1.000000 0.000000 0.000000 1.000000 8555.000000 -3276.000000) translate(0,204)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(238,0,0)" font-family="SimSun" font-size="255" graphid="g_1870ca0" transform="matrix(1.000000 0.000000 0.000000 1.000000 8555.000000 -3276.000000) translate(0,459)">网</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(238,0,0)" font-family="SimSun" font-size="255" graphid="g_1870ca0" transform="matrix(1.000000 0.000000 0.000000 1.000000 8555.000000 -3276.000000) translate(0,714)">潮</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(238,0,0)" font-family="SimSun" font-size="255" graphid="g_1870ca0" transform="matrix(1.000000 0.000000 0.000000 1.000000 8555.000000 -3276.000000) translate(0,969)">流</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(238,0,0)" font-family="SimSun" font-size="255" graphid="g_1870ca0" transform="matrix(1.000000 0.000000 0.000000 1.000000 8555.000000 -3276.000000) translate(0,1224)">图</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1868450" transform="matrix(1.000000 0.000000 0.000000 1.000000 9219.000000 -3683.000000) translate(0,12)">110kV永干万线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e3940" transform="matrix(1.000000 0.000000 0.000000 1.000000 10499.000000 -3524.000000) translate(0,12)">110kV大湾子线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18bb9b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10856.000000 -3484.000000) translate(0,12)">110kV月沙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18655d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9186.000000 -3823.000000) translate(0,12)">110kV永万的线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 9339.000000 -3822.000000) translate(0,12)">LGJ-240/40, 42.101km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1865de0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9316.000000 -3912.000000) translate(0,12)">110kV永万的线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1866050" transform="matrix(1.000000 0.000000 0.000000 1.000000 10100.000000 -1759.000000) translate(0,12)">110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1866050" transform="matrix(1.000000 0.000000 0.000000 1.000000 10100.000000 -1759.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1866050" transform="matrix(1.000000 0.000000 0.000000 1.000000 10100.000000 -1759.000000) translate(0,42)">西</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1866050" transform="matrix(1.000000 0.000000 0.000000 1.000000 10100.000000 -1759.000000) translate(0,57)">虎</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1866050" transform="matrix(1.000000 0.000000 0.000000 1.000000 10100.000000 -1759.000000) translate(0,72)">杜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1866050" transform="matrix(1.000000 0.000000 0.000000 1.000000 10100.000000 -1759.000000) translate(0,87)">嘉</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1866050" transform="matrix(1.000000 0.000000 0.000000 1.000000 10100.000000 -1759.000000) translate(0,102)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1b9e6f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10098.000000 -1422.000000) translate(0,12)">LGJ-150，6.6km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b9e900" transform="matrix(1.000000 0.000000 0.000000 1.000000 9949.000000 -1523.000000) translate(0,12)">110kV西虎杜嘉线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b9eb40" transform="matrix(1.000000 0.000000 0.000000 1.000000 9733.000000 -1319.000000) translate(0,12)">110kV一二级联线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b9f9c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9844.000000 -1255.000000) translate(0,12)">110kV零二线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1870f20" transform="matrix(1.000000 0.000000 0.000000 1.000000 10173.000000 -1118.000000) translate(0,12)">110kV泥不线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1871780" transform="matrix(1.000000 0.000000 0.000000 1.000000 10281.000000 -1322.000000) translate(0,12)">110kV双鄂大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1871cf0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9952.000000 -2490.000000) translate(0,12)">220kV天紫线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1872240" transform="matrix(1.000000 0.000000 0.000000 1.000000 9355.000000 -2486.000000) translate(0,12)">220kV天紫线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18724c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10367.000000 -2848.000000) translate(0,12)">110kV新桥T接线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1872d10" transform="matrix(1.000000 0.000000 0.000000 1.000000 10208.000000 -2638.000000) translate(0,12)">110kV紫新甸线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e7af0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10915.000000 -2781.000000) translate(0,12)">110kV紫新甸线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e7d70" transform="matrix(1.000000 0.000000 0.000000 1.000000 10109.000000 -2695.000000) translate(0,12)">110kV紫姚南线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e7fb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9943.000000 -2674.000000) translate(0,12)">110kV紫姚南线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e81f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9959.000000 -2915.000000) translate(0,12)">110kV大龙口I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e8990" transform="matrix(1.000000 0.000000 0.000000 1.000000 9936.000000 -3077.000000) translate(0,12)">110kV大龙口Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e8c10" transform="matrix(1.000000 0.000000 0.000000 1.000000 10477.000000 -2413.000000) translate(0,12)">110kV楚牟线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e9120" transform="matrix(1.000000 0.000000 0.000000 1.000000 11064.000000 -2265.000000) translate(0,12)">110kV楚牟T广线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e9690" transform="matrix(1.000000 0.000000 0.000000 1.000000 11255.000000 -2087.000000) translate(0,12)">110kV谢金T广线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e9910" transform="matrix(1.000000 0.000000 0.000000 1.000000 10848.000000 -3130.000000) translate(0,12)">110kV羊臼河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19ea470" transform="matrix(1.000000 0.000000 0.000000 1.000000 11081.000000 -2995.000000) translate(0,12)">110kV羊臼T甸线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19ea6f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10755.000000 -2945.000000) translate(0,12)">110kV元牟T羊线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19307f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11233.000000 -1803.000000) translate(0,12)">110kV谢烟龙金线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1930a30" transform="matrix(1.000000 0.000000 0.000000 1.000000 10934.000000 -1637.000000) translate(0,12)">110kV谢烟双线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1930c70" transform="matrix(1.000000 0.000000 0.000000 1.000000 11011.000000 -1967.000000) translate(0,12)">110kV谢金I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 0.000000 0.000000 1.000000 11484.000000 -1969.000000) translate(0,12)">LGJ-240,26.206</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19310f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11099.000000 -1916.000000) translate(0,12)">110kV谢烟龙金线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1931330" transform="matrix(1.000000 0.000000 0.000000 1.000000 11086.000000 -1893.000000) translate(0,12)">110kV龙潭I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1931850" transform="matrix(1.000000 0.000000 0.000000 1.000000 11466.000000 -2026.000000) translate(0,12)">110kV舍资I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19320d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11312.000000 -2023.000000) translate(0,12)">110kV舍资II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1932350" transform="matrix(1.000000 0.000000 0.000000 1.000000 10632.000000 -3640.000000) translate(0,12)">110kV大湾T元线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1932590" transform="matrix(1.000000 0.000000 0.000000 1.000000 10242.000000 -3629.000000) translate(0,12)">110kV方元大线大湾子侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19327d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10733.000000 -3716.000000) translate(0,12)">110kV月沙T元线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1932a10" transform="matrix(1.000000 0.000000 0.000000 1.000000 12568.000000 -2717.000000) translate(0,12)">220kV腰新I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1932c50" transform="matrix(1.000000 0.000000 0.000000 1.000000 12714.000000 -2720.000000) translate(0,12)">220kV腰新II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1932e90" transform="matrix(1.000000 0.000000 0.000000 1.000000 12128.000000 -3777.000000) translate(0,12)">110kV狮果I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19333b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12204.000000 -3721.000000) translate(0,12)">110kV狮果II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bba910" transform="matrix(1.000000 0.000000 0.000000 1.000000 9492.000000 -3146.000000) translate(0,12)">110kV渔大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bbadd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10093.000000 -1222.000000) translate(0,12)">110kV不鄂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bbb050" transform="matrix(1.000000 0.000000 0.000000 1.000000 12282.000000 -2126.000000) translate(0,12)">110kV禄腰德线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bbb2a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12456.000000 -2388.000000) translate(0,12)">110kV禄腰德线_禄丰侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bbb4e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11737.000000 -1857.000000) translate(0,12)">110kV禄牵II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1b848c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9328.000000 -3533.000000) translate(0,24)">干巴拉</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1b85fc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9808.000000 -2577.000000) translate(0,12)">JL/G1A-240，18.3km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1b86e80" transform="matrix(1.000000 0.000000 0.000000 1.000000 9665.000000 -2529.000000) translate(0,24)">云台山风电场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1ba0240" transform="matrix(1.000000 0.000000 0.000000 1.000000 9860.000000 -2602.000000) translate(0,12)">JL/G1A-240，12km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1ba04e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9945.000000 -2552.000000) translate(0,12)">JL/G1A-240，17km</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1ba0720" transform="matrix(1.000000 0.000000 0.000000 1.000000 10633.000000 -3804.000000) translate(0,24)">元谋牵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1ba0950" transform="matrix(1.000000 0.000000 0.000000 1.000000 10395.000000 -3770.000000) translate(0,24)">大湾牵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bb1660" transform="matrix(1.000000 0.000000 0.000000 1.000000 11927.000000 -3690.000000) translate(0,12)">110kV仙果线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bb2d10" transform="matrix(1.000000 0.000000 0.000000 1.000000 12720.000000 -3475.000000) translate(0,12)">110kV田心T接线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bb3550" transform="matrix(1.000000 0.000000 0.000000 1.000000 12677.000000 -3817.000000) translate(0,12)">110kV狮田线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1bb5830" transform="matrix(1.000000 0.000000 0.000000 1.000000 13031.000000 -2180.000000) translate(0,24)">指挥营变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bb71f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12764.000000 -2181.000000) translate(0,12)">110kV腰洪指线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bb80c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11269.000000 -3254.000000) translate(0,12)">110kV元黄哨I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1bb8430" transform="matrix(1.000000 0.000000 0.000000 1.000000 11117.000000 -3551.000000) translate(0,24)">雷应山风电场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bb96f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12851.000000 -4015.000000) translate(0,11)">191</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bba000" transform="matrix(1.000000 0.000000 0.000000 1.000000 12597.000000 -3936.000000) translate(0,11)">171</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bba560" transform="matrix(1.000000 0.000000 0.000000 1.000000 12598.000000 -3913.000000) translate(0,11)">172</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bbe400" transform="matrix(1.000000 0.000000 0.000000 1.000000 12901.000000 -4014.000000) translate(0,11)">192</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bbe810" transform="matrix(1.000000 0.000000 0.000000 1.000000 13096.000000 -4009.000000) translate(0,11)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bbecc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 13151.000000 -4009.000000) translate(0,11)">182</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bbef40" transform="matrix(1.000000 0.000000 0.000000 1.000000 12463.000000 -3955.000000) translate(0,11)">177</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bbf180" transform="matrix(1.000000 0.000000 0.000000 1.000000 12464.000000 -3923.000000) translate(0,11)">178</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bbf3c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12460.000000 -3889.000000) translate(0,11)">174</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bbf690" transform="matrix(1.000000 0.000000 0.000000 1.000000 12461.000000 -3866.000000) translate(0,11)">173</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bbfb90" transform="matrix(1.000000 0.000000 0.000000 1.000000 12527.000000 -3861.000000) translate(0,11)">183</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc0080" transform="matrix(1.000000 0.000000 0.000000 1.000000 12560.000000 -3862.000000) translate(0,11)">182</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc02c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12600.000000 -3869.000000) translate(0,11)">176</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1bc0570" transform="matrix(1.000000 0.000000 0.000000 1.000000 12262.000000 -3892.000000) translate(0,16)">161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc0a60" transform="matrix(1.000000 0.000000 0.000000 1.000000 12131.000000 -3729.000000) translate(0,11)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc0ca0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12163.000000 -3705.000000) translate(0,11)">182</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc0ee0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12062.000000 -3682.000000) translate(0,11)">183</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc1120" transform="matrix(1.000000 0.000000 0.000000 1.000000 11893.000000 -3683.000000) translate(0,11)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc1360" transform="matrix(1.000000 0.000000 0.000000 1.000000 12508.000000 -3652.000000) translate(0,11)">153</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc1880" transform="matrix(1.000000 0.000000 0.000000 1.000000 12559.000000 -3652.000000) translate(0,11)">152</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc1b00" transform="matrix(1.000000 0.000000 0.000000 1.000000 12523.000000 -3558.000000) translate(0,11)">151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc1d40" transform="matrix(1.000000 0.000000 0.000000 1.000000 12514.000000 -3224.000000) translate(0,11)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc2250" transform="matrix(1.000000 0.000000 0.000000 1.000000 12386.000000 -3386.000000) translate(0,11)">131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc2490" transform="matrix(1.000000 0.000000 0.000000 1.000000 12810.000000 -3648.000000) translate(0,11)">162</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc26d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12818.000000 -3564.000000) translate(0,11)">161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc2910" transform="matrix(1.000000 0.000000 0.000000 1.000000 8810.000000 -3820.000000) translate(0,11)">121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc2b50" transform="matrix(1.000000 0.000000 0.000000 1.000000 9033.000000 -3820.000000) translate(0,11)">191</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc2d90" transform="matrix(1.000000 0.000000 0.000000 1.000000 9134.000000 -3818.000000) translate(0,11)">193</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc2fd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9096.000000 -3763.000000) translate(0,11)">194</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc3210" transform="matrix(1.000000 0.000000 0.000000 1.000000 9096.000000 -3607.000000) translate(0,11)">171</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc3450" transform="matrix(1.000000 0.000000 0.000000 1.000000 9285.000000 -3992.000000) translate(0,11)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc3690" transform="matrix(1.000000 0.000000 0.000000 1.000000 9505.000000 -3819.000000) translate(0,11)">161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc38d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9504.000000 -3787.000000) translate(0,11)">164</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc3b10" transform="matrix(1.000000 0.000000 0.000000 1.000000 9607.000000 -3771.000000) translate(0,11)">162</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc3d50" transform="matrix(1.000000 0.000000 0.000000 1.000000 9589.000000 -3310.000000) translate(0,11)">142</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc3f90" transform="matrix(1.000000 0.000000 0.000000 1.000000 9608.000000 -3287.000000) translate(0,11)">141</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc41d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10049.000000 -3283.000000) translate(0,11)">154</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc4410" transform="matrix(1.000000 0.000000 0.000000 1.000000 10044.000000 -3238.000000) translate(0,11)">153</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc4650" transform="matrix(1.000000 0.000000 0.000000 1.000000 10149.000000 -3281.000000) translate(0,11)">151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc4890" transform="matrix(1.000000 0.000000 0.000000 1.000000 9176.000000 -3032.000000) translate(0,11)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc4ad0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10115.000000 -3023.000000) translate(0,11)">161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc4d10" transform="matrix(1.000000 0.000000 0.000000 1.000000 10115.000000 -2925.000000) translate(0,11)">162</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc4f50" transform="matrix(1.000000 0.000000 0.000000 1.000000 9876.000000 -2985.000000) translate(0,11)">122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc5190" transform="matrix(1.000000 0.000000 0.000000 1.000000 9882.000000 -2952.000000) translate(0,11)">121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc53d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9794.000000 -2557.000000) translate(0,11)">161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc5610" transform="matrix(1.000000 0.000000 0.000000 1.000000 9888.000000 -2670.000000) translate(0,11)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc5850" transform="matrix(1.000000 0.000000 0.000000 1.000000 9890.000000 -2620.000000) translate(0,11)">186</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc5a90" transform="matrix(1.000000 0.000000 0.000000 1.000000 10087.000000 -2544.000000) translate(0,11)">184</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc5cd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10038.000000 -2504.000000) translate(0,11)">182</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc5f10" transform="matrix(1.000000 0.000000 0.000000 1.000000 10029.000000 -2399.000000) translate(0,11)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc6150" transform="matrix(1.000000 0.000000 0.000000 1.000000 10063.000000 -2417.000000) translate(0,11)">188</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bc6390" transform="matrix(1.000000 0.000000 0.000000 1.000000 9986.000000 -2144.000000) translate(0,12)">110kV紫西东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc65e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10072.000000 -2401.000000) translate(0,11)">187</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc6810" transform="matrix(1.000000 0.000000 0.000000 1.000000 10102.000000 -2421.000000) translate(0,11)">186</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc6a50" transform="matrix(1.000000 0.000000 0.000000 1.000000 10127.000000 -2409.000000) translate(0,11)">189</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc6c90" transform="matrix(1.000000 0.000000 0.000000 1.000000 9772.000000 -1998.000000) translate(0,11)">161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc6ed0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10157.000000 -2543.000000) translate(0,11)">192</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc7110" transform="matrix(1.000000 0.000000 0.000000 1.000000 9921.000000 -1982.000000) translate(0,11)">171</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc7350" transform="matrix(1.000000 0.000000 0.000000 1.000000 9869.000000 -1983.000000) translate(0,11)">172</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc7590" transform="matrix(1.000000 0.000000 0.000000 1.000000 10095.000000 -1889.000000) translate(0,11)">148</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc77d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10140.000000 -1904.000000) translate(0,11)">144</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc7a10" transform="matrix(1.000000 0.000000 0.000000 1.000000 10139.000000 -1953.000000) translate(0,11)">143</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc7c50" transform="matrix(1.000000 0.000000 0.000000 1.000000 10206.000000 -1790.000000) translate(0,11)">142</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc7e90" transform="matrix(1.000000 0.000000 0.000000 1.000000 10520.000000 -1716.000000) translate(0,11)">162</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc80d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10587.000000 -1715.000000) translate(0,11)">161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc8310" transform="matrix(1.000000 0.000000 0.000000 1.000000 10809.000000 -1919.000000) translate(0,11)">132</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc8550" transform="matrix(1.000000 0.000000 0.000000 1.000000 10822.000000 -1893.000000) translate(0,11)">127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc8790" transform="matrix(1.000000 0.000000 0.000000 1.000000 10515.000000 -1950.000000) translate(0,11)">129</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc89d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10807.000000 -1955.000000) translate(0,11)">128</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc8c10" transform="matrix(1.000000 0.000000 0.000000 1.000000 10321.000000 -2053.000000) translate(0,11)">161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc8e50" transform="matrix(1.000000 0.000000 0.000000 1.000000 10255.000000 -2141.000000) translate(0,11)">163</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc9090" transform="matrix(1.000000 0.000000 0.000000 1.000000 10841.000000 -1999.000000) translate(0,11)">124</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc92d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10885.000000 -1881.000000) translate(0,11)">126</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc9510" transform="matrix(1.000000 0.000000 0.000000 1.000000 10926.000000 -1944.000000) translate(0,11)">123</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc9750" transform="matrix(1.000000 0.000000 0.000000 1.000000 10926.000000 -1920.000000) translate(0,11)">133</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc9990" transform="matrix(1.000000 0.000000 0.000000 1.000000 11063.000000 -1846.000000) translate(0,11)">182</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc9bd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11125.000000 -1796.000000) translate(0,11)">183</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bc9e10" transform="matrix(1.000000 0.000000 0.000000 1.000000 11203.000000 -1692.000000) translate(0,11)">172</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bca050" transform="matrix(1.000000 0.000000 0.000000 1.000000 11252.000000 -2209.000000) translate(0,11)">1381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bca290" transform="matrix(1.000000 0.000000 0.000000 1.000000 11183.000000 -2243.000000) translate(0,11)">1391</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bca4d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10440.000000 -2706.000000) translate(0,11)">186</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bca710" transform="matrix(1.000000 0.000000 0.000000 1.000000 10462.000000 -2617.000000) translate(0,11)">184</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bca950" transform="matrix(1.000000 0.000000 0.000000 1.000000 10321.000000 -2815.000000) translate(0,11)">121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcab90" transform="matrix(1.000000 0.000000 0.000000 1.000000 10261.000000 -2752.000000) translate(0,11)">122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcadd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11407.000000 -2062.000000) translate(0,11)">122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcb010" transform="matrix(1.000000 0.000000 0.000000 1.000000 11464.000000 -2062.000000) translate(0,11)">121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcb250" transform="matrix(1.000000 0.000000 0.000000 1.000000 11580.000000 -1957.000000) translate(0,11)">192</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcb490" transform="matrix(1.000000 0.000000 0.000000 1.000000 11580.000000 -1933.000000) translate(0,11)">193</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcb6d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11680.000000 -1960.000000) translate(0,11)">191</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcb910" transform="matrix(1.000000 0.000000 0.000000 1.000000 11679.500000 -1935.000000) translate(0,11)">194</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcbb50" transform="matrix(1.000000 0.000000 0.000000 1.000000 11169.000000 -2840.000000) translate(0,11)">1481</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcbd90" transform="matrix(1.000000 0.000000 0.000000 1.000000 11216.000000 -2905.000000) translate(0,11)">1491</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcbfd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10921.000000 -2938.000000) translate(0,11)">1581</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcc210" transform="matrix(1.000000 0.000000 0.000000 1.000000 10996.000000 -2985.000000) translate(0,11)">1591</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcc450" transform="matrix(1.000000 0.000000 0.000000 1.000000 10809.000000 -3218.000000) translate(0,11)">133</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcc690" transform="matrix(1.000000 0.000000 0.000000 1.000000 10870.000000 -3221.000000) translate(0,11)">132</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcc8d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10777.000000 -3263.000000) translate(0,11)">135</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bccb10" transform="matrix(1.000000 0.000000 0.000000 1.000000 10774.000000 -3322.000000) translate(0,11)">136</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bccd50" transform="matrix(1.000000 0.000000 0.000000 1.000000 10794.000000 -3340.000000) translate(0,11)">138</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bccf90" transform="matrix(1.000000 0.000000 0.000000 1.000000 10853.000000 -3334.000000) translate(0,11)">139</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcd1d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10895.000000 -3337.000000) translate(0,11)">137</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcd410" transform="matrix(1.000000 0.000000 0.000000 1.000000 10899.000000 -3296.000000) translate(0,11)">128</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcd650" transform="matrix(1.000000 0.000000 0.000000 1.000000 10899.000000 -3268.000000) translate(0,11)">129</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcd890" transform="matrix(1.000000 0.000000 0.000000 1.000000 11380.000000 -3297.000000) translate(0,11)">173</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcdad0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11380.000000 -3268.000000) translate(0,11)">172</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcdd10" transform="matrix(1.000000 0.000000 0.000000 1.000000 11144.000000 -3484.000000) translate(0,11)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcdf50" transform="matrix(1.000000 0.000000 0.000000 1.000000 10855.000000 -3772.000000) translate(0,11)">152</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bce190" transform="matrix(1.000000 0.000000 0.000000 1.000000 10715.000000 -3730.000000) translate(0,11)">1681</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bce3d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10652.000000 -3684.000000) translate(0,11)">1691</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bce610" transform="matrix(1.000000 0.000000 0.000000 1.000000 10468.000000 -3668.000000) translate(0,11)">1781</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bce850" transform="matrix(1.000000 0.000000 0.000000 1.000000 10404.000000 -3655.000000) translate(0,11)">1791</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcea90" transform="matrix(1.000000 0.000000 0.000000 1.000000 12071.000000 -2008.000000) translate(0,11)">146</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcecd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12113.000000 -1968.000000) translate(0,11)">143</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcef10" transform="matrix(1.000000 0.000000 0.000000 1.000000 12098.000000 -1943.000000) translate(0,11)">161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcf150" transform="matrix(1.000000 0.000000 0.000000 1.000000 11968.000000 -1960.000000) translate(0,11)">145</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcf390" transform="matrix(1.000000 0.000000 0.000000 1.000000 11968.000000 -1935.000000) translate(0,11)">148</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcf5d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11973.000000 -1882.000000) translate(0,11)">147</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcf810" transform="matrix(1.000000 0.000000 0.000000 1.000000 12016.000000 -1882.000000) translate(0,11)">141</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcfa50" transform="matrix(1.000000 0.000000 0.000000 1.000000 12039.000000 -1868.000000) translate(0,11)">142</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcfc90" transform="matrix(1.000000 0.000000 0.000000 1.000000 12089.000000 -1894.000000) translate(0,11)">149</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bcfed0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12075.000000 -1873.000000) translate(0,11)">165</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd0110" transform="matrix(1.000000 0.000000 0.000000 1.000000 12306.000000 -1540.000000) translate(0,11)">132</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd0350" transform="matrix(1.000000 0.000000 0.000000 1.000000 12353.000000 -1556.000000) translate(0,11)">133</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd0590" transform="matrix(1.000000 0.000000 0.000000 1.000000 12000.000000 -1531.000000) translate(0,11)">1191</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd07d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12048.000000 -1531.000000) translate(0,11)">1181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd0a10" transform="matrix(1.000000 0.000000 0.000000 1.000000 11876.000000 -1762.000000) translate(0,11)">1281</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd0c50" transform="matrix(1.000000 0.000000 0.000000 1.000000 11826.000000 -1754.000000) translate(0,11)">1291</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd0e90" transform="matrix(1.000000 0.000000 0.000000 1.000000 12633.000000 -1945.000000) translate(0,11)">171</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd10d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12706.000000 -1975.000000) translate(0,11)">172</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd1310" transform="matrix(1.000000 0.000000 0.000000 1.000000 12421.000000 -2108.000000) translate(0,11)">197</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd1550" transform="matrix(1.000000 0.000000 0.000000 1.000000 12504.000000 -2188.000000) translate(0,11)">196</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd1790" transform="matrix(1.000000 0.000000 0.000000 1.000000 12168.000000 -2290.000000) translate(0,11)">161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd19d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12666.000000 -2436.000000) translate(0,11)">151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd1c10" transform="matrix(1.000000 0.000000 0.000000 1.000000 12627.000000 -2452.000000) translate(0,11)">156</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd1e50" transform="matrix(1.000000 0.000000 0.000000 1.000000 12706.000000 -2439.000000) translate(0,11)">157</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd2090" transform="matrix(1.000000 0.000000 0.000000 1.000000 12751.000000 -2504.000000) translate(0,11)">158</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd22d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 13044.000000 -2504.000000) translate(0,11)">173</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd2510" transform="matrix(1.000000 0.000000 0.000000 1.000000 12925.000000 -2178.000000) translate(0,11)">163</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd2750" transform="matrix(1.000000 0.000000 0.000000 1.000000 10886.000000 -1351.000000) translate(0,11)">156</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd2990" transform="matrix(1.000000 0.000000 0.000000 1.000000 10831.000000 -1319.000000) translate(0,11)">154</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1bd2bd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10500.000000 -1417.000000) translate(0,16)">152</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd2e10" transform="matrix(1.000000 0.000000 0.000000 1.000000 10128.000000 -1299.000000) translate(0,11)">186</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd3050" transform="matrix(1.000000 0.000000 0.000000 1.000000 10091.000000 -1274.000000) translate(0,11)">187</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd3290" transform="matrix(1.000000 0.000000 0.000000 1.000000 10066.000000 -1163.000000) translate(0,11)">153</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd34d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10128.000000 -1123.000000) translate(0,11)">152</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd3710" transform="matrix(1.000000 0.000000 0.000000 1.000000 10270.000000 -1124.000000) translate(0,11)">131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd3950" transform="matrix(1.000000 0.000000 0.000000 1.000000 9733.000000 -1190.000000) translate(0,11)">151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd3b90" transform="matrix(1.000000 0.000000 0.000000 1.000000 10011.000000 -1274.000000) translate(0,11)">188</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd3dd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10018.000000 -1298.000000) translate(0,11)">182</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd4010" transform="matrix(1.000000 0.000000 0.000000 1.000000 9372.000000 -1312.000000) translate(0,11)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd4250" transform="matrix(1.000000 0.000000 0.000000 1.000000 9533.000000 -1170.000000) translate(0,11)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd4490" transform="matrix(1.000000 0.000000 0.000000 1.000000 9771.000000 -1500.000000) translate(0,11)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd46d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10069.000000 -1340.000000) translate(0,11)">183</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd4910" transform="matrix(1.000000 0.000000 0.000000 1.000000 9348.000000 -3604.000000) translate(0,11)">151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd4b50" transform="matrix(1.000000 0.000000 0.000000 1.000000 12243.000000 -3964.000000) translate(0,11)">162</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd6930" transform="matrix(1.000000 0.000000 0.000000 1.000000 9694.000000 -2230.000000) translate(0,12)">110kV紫湾线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd6c30" transform="matrix(1.000000 0.000000 0.000000 1.000000 10014.000000 -2423.000000) translate(0,11)">183</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1bd6f50" transform="matrix(1.000000 0.000000 0.000000 1.000000 13119.000000 -3578.000000) translate(0,24)">勐果河六级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd73d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 13118.000000 -3600.000000) translate(0,11)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd7690" transform="matrix(1.000000 0.000000 0.000000 1.000000 12912.000000 -3617.000000) translate(0,11)">163</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd8180" transform="matrix(1.000000 0.000000 0.000000 1.000000 12962.000000 -3599.000000) translate(0,12)">110kV勐果河六级线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1bd8790" transform="matrix(1.000000 0.000000 0.000000 1.000000 9473.000000 -2001.000000) translate(0,24)">大湾电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bd8c20" transform="matrix(1.000000 0.000000 0.000000 1.000000 9580.000000 -2053.000000) translate(0,11)">1516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1bd9870" transform="matrix(1.000000 0.000000 0.000000 1.000000 11687.000000 -3139.000000) translate(0,24)">保顶山风电场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_1bda3b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11750.000000 -2985.000000) translate(0,20)">262</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bdb7d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12066.500000 -3276.000000) translate(0,11)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1bdbae0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11987.500000 -3325.000000) translate(0,24)">茅稗田升压场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bdca10" transform="matrix(1.000000 0.000000 0.000000 1.000000 12005.000000 -2420.000000) translate(0,11)">271</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bdcc70" transform="matrix(1.000000 0.000000 0.000000 1.000000 12008.000000 -1994.000000) translate(0,11)">244</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bdceb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12057.000000 -2421.000000) translate(0,11)">272</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bdd0f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12047.000000 -1994.000000) translate(0,11)">241</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1bdd410" transform="matrix(1.000000 0.000000 0.000000 1.000000 9149.000000 -2437.000000) translate(0,24)">天峰山风电场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1bddff0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12252.000000 -3544.000000) translate(0,24)">西和</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bde520" transform="matrix(1.000000 0.000000 0.000000 1.000000 12266.000000 -3638.000000) translate(0,11)">192</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bde7e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12309.000000 -3638.000000) translate(0,11)">193</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bded80" transform="matrix(1.000000 0.000000 0.000000 1.000000 12316.000000 -3768.000000) translate(0,12)">110kV狮西Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bdf0f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12330.000000 -3668.000000) translate(0,12)">110kV狮西Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bdf330" transform="matrix(1.000000 0.000000 0.000000 1.000000 12482.000000 -3858.000000) translate(0,11)">179</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1bdf560" transform="matrix(1.000000 0.000000 0.000000 1.000000 12505.000000 -3852.000000) translate(0,11)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_1be0a70" transform="matrix(1.000000 0.000000 0.000000 1.000000 9964.000000 -2456.000000) translate(0,20)">281</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1be13a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9240.000000 -2216.000000) translate(0,24)">红土坡风电场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1be1f90" transform="matrix(1.000000 0.000000 0.000000 1.000000 9379.000000 -2290.000000) translate(0,20)">231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1be48f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9058.000000 -2268.000000) translate(0,20)">251</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1d20a30" transform="matrix(1.000000 0.000000 0.000000 1.000000 8922.000000 -2211.000000) translate(0,24)">五街风电场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d21450" transform="matrix(1.000000 0.000000 0.000000 1.000000 11820.000000 -3010.000000) translate(0,12)">220kV鹿保线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d21ce0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9525.000000 -2343.000000) translate(0,12)">220kV红紫线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d222f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9125.000000 -2234.000000) translate(0,12)">220kV红五线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d227b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11922.000000 -3188.000000) translate(0,12)">110kV保田线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1d243c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12467.000000 -2027.000000) translate(0,24)">老青山风电场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d25040" transform="matrix(1.000000 0.000000 0.000000 1.000000 12440.000000 -2071.000000) translate(0,11)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1d25d70" transform="matrix(1.000000 0.000000 0.000000 1.000000 12295.000000 -2885.000000) translate(0,24)">禄丰南牵引变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d27bd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12136.000000 -2763.000000) translate(0,12)">220kV和丰I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d27e20" transform="matrix(1.000000 0.000000 0.000000 1.000000 12233.000000 -2694.000000) translate(0,12)">220kV和丰Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1d281f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12373.000000 -1816.000000) translate(0,24)">阿普风电场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d28db0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12089.000000 -1919.000000) translate(0,11)">243</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1d299e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11638.000000 -3320.000000) translate(0,24)">天子山开关站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d2c8c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11621.000000 -3287.000000) translate(0,11)">354</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d2d700" transform="matrix(1.000000 0.000000 0.000000 1.000000 11500.000000 -3287.000000) translate(0,12)">35kV天子山线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1d2ea40" transform="matrix(1.000000 0.000000 0.000000 1.000000 9855.000000 -1091.000000) translate(0,24)">小江河一级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d2f470" transform="matrix(1.000000 0.000000 0.000000 1.000000 9908.000000 -1158.000000) translate(0,11)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d2f700" transform="matrix(1.000000 0.000000 0.000000 1.000000 9970.000000 -1118.000000) translate(0,11)">152</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d2f940" transform="matrix(1.000000 0.000000 0.000000 1.000000 10058.000000 -1274.000000) translate(0,11)">189</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1d30700" transform="matrix(1.000000 0.000000 0.000000 1.000000 10109.000000 -3400.000000) translate(0,24)">方山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d31520" transform="matrix(1.000000 0.000000 0.000000 1.000000 10175.000000 -3540.000000) translate(0,12)">110kV方元大线方山侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d31b00" transform="matrix(1.000000 0.000000 0.000000 1.000000 9833.000000 -3670.000000) translate(0,12)">110kV方永II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d31df0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10216.000000 -3505.000000) translate(0,11)">167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d32020" transform="matrix(1.000000 0.000000 0.000000 1.000000 10092.000000 -3507.000000) translate(0,11)">168</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d32a50" transform="matrix(1.000000 0.000000 0.000000 1.000000 10775.000000 -3288.000000) translate(0,11)">231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d32e50" transform="matrix(1.000000 0.000000 0.000000 1.000000 10231.000000 -3468.000000) translate(0,11)">262</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d33090" transform="matrix(1.000000 0.000000 0.000000 1.000000 11681.000000 -2926.000000) translate(0,11)">261</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d332d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10361.000000 -3121.000000) translate(0,12)">220kV谢方线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d33510" transform="matrix(1.000000 0.000000 0.000000 1.000000 10506.000000 -3353.000000) translate(0,12)">220kV元方线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1d33750" transform="matrix(1.000000 0.000000 0.000000 1.000000 11692.000000 -3479.000000) translate(0,24)">河外光伏电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d345a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11721.000000 -3419.000000) translate(0,11)">161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d351d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11587.000000 -3372.000000) translate(0,12)">110kV河黄线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d35590" transform="matrix(1.000000 0.000000 0.000000 1.000000 11482.000000 -3315.000000) translate(0,11)">171</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d36370" transform="matrix(1.000000 0.000000 0.000000 1.000000 12659.000000 -1776.000000) translate(0,12)">271</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1d369b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12630.000000 -1736.000000) translate(0,24)">三台坡风电场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d36e40" transform="matrix(1.000000 0.000000 0.000000 1.000000 12550.000000 -1758.000000) translate(0,12)">220kV台阿线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d376d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12493.000000 -1778.000000) translate(0,12)">281</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d37c80" transform="matrix(1.000000 0.000000 0.000000 1.000000 12385.000000 -1767.000000) translate(0,12)">282</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d381d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10097.000000 -3411.000000) translate(0,11)">173</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d38580" transform="matrix(1.000000 0.000000 0.000000 1.000000 10077.000000 -3487.000000) translate(0,11)">172</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d387c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9785.000000 -3586.000000) translate(0,12)">110kV方永Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d38a10" transform="matrix(1.000000 0.000000 0.000000 1.000000 9817.000000 -3370.000000) translate(0,12)">110kV方六线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1d38ee0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10084.000000 -3783.000000) translate(0,24)">秀田升压站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d397d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10140.000000 -3516.000000) translate(0,11)">161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d39a10" transform="matrix(1.000000 0.000000 0.000000 1.000000 10140.000000 -3706.000000) translate(0,11)">151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d39c50" transform="matrix(1.000000 0.000000 0.000000 1.000000 10110.000000 -3625.000000) translate(0,12)">110kV方秀线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d3b4b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12107.000000 -3196.000000) translate(0,11)">151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1d3b7c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12081.000000 -3156.000000) translate(0,24)">大中山升压站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d3bee0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11967.000000 -3138.000000) translate(0,12)">110kV保大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d3c9e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12105.000000 -3065.000000) translate(0,11)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1d3ccf0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12088.000000 -3052.000000) translate(0,24)">老尖山升压站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d3d360" transform="matrix(1.000000 0.000000 0.000000 1.000000 11986.000000 -3064.000000) translate(0,12)">110kV保尖线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d3d940" transform="matrix(1.000000 0.000000 0.000000 1.000000 11868.000000 -3132.000000) translate(0,11)">142</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d3dc10" transform="matrix(1.000000 0.000000 0.000000 1.000000 11908.000000 -3106.000000) translate(0,11)">143</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d3de50" transform="matrix(1.000000 0.000000 0.000000 1.000000 11909.000000 -3067.000000) translate(0,11)">144</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1d3e330" transform="matrix(1.000000 0.000000 0.000000 1.000000 9760.000000 -3558.000000) translate(0,24)">班幸开关站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d3edb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10301.000000 -3341.000000) translate(0,11)">341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d3f000" transform="matrix(1.000000 0.000000 0.000000 1.000000 10210.000000 -3437.000000) translate(0,11)">367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1d3fbc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9594.000000 -3456.000000) translate(0,24)">莲池</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d40550" transform="matrix(1.000000 0.000000 0.000000 1.000000 9730.000000 -3435.000000) translate(0,11)">153</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d409a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9730.000000 -3464.000000) translate(0,11)">152</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d40f40" transform="matrix(1.000000 0.000000 0.000000 1.000000 9844.000000 -3463.000000) translate(0,12)">110kV方莲Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d41500" transform="matrix(1.000000 0.000000 0.000000 1.000000 9858.000000 -3437.000000) translate(0,12)">110kV方莲Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d41780" transform="matrix(1.000000 0.000000 0.000000 1.000000 10091.000000 -3434.000000) translate(0,11)">171</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d419b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10091.000000 -3460.000000) translate(0,11)">169</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d44010" transform="matrix(1.000000 0.000000 0.000000 1.000000 12106.000000 -3801.000000) translate(0,11)">161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1d442f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11925.000000 -3797.000000) translate(0,24)">三月山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d448d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12456.000000 -3903.000000) translate(0,11)">175</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d44bb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 12192.000000 -3848.000000) translate(0,12)">110kV三狮线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d46000" transform="matrix(1.000000 0.000000 0.000000 1.000000 12010.000000 -2851.000000) translate(0,12)">261</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1d46810" transform="matrix(1.000000 0.000000 0.000000 1.000000 11973.000000 -2937.000000) translate(0,24)">彝山风电场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b99890" transform="matrix(1.000000 0.000000 0.000000 1.000000 12046.000000 -2597.000000) translate(0,12)">277</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e1560" transform="matrix(1.000000 0.000000 0.000000 1.000000 12042.000000 -2768.000000) translate(0,12)">220</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e1560" transform="matrix(1.000000 0.000000 0.000000 1.000000 12042.000000 -2768.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e1560" transform="matrix(1.000000 0.000000 0.000000 1.000000 12042.000000 -2768.000000) translate(0,42)">和</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e1560" transform="matrix(1.000000 0.000000 0.000000 1.000000 12042.000000 -2768.000000) translate(0,57)">彝</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e1560" transform="matrix(1.000000 0.000000 0.000000 1.000000 12042.000000 -2768.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_18b1530" transform="matrix(1.000000 0.000000 0.000000 1.000000 11207.000000 -3181.000000) translate(0,11)">162</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_18b18f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11263.000000 -3181.000000) translate(0,11)">163</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_18b2c80" transform="matrix(1.000000 0.000000 0.000000 1.000000 11287.000000 -3143.000000) translate(0,24)">哨房</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_18b3200" transform="matrix(1.000000 0.000000 0.000000 1.000000 9561.000000 -1378.000000) translate(0,24)">杜鹃</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d4bfe0" transform="matrix(1.000000 0.000000 0.000000 1.000000 9570.000000 -1472.000000) translate(0,11)">131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4c430" transform="matrix(1.000000 0.000000 0.000000 1.000000 9838.000000 -1619.000000) translate(0,12)">110kV西虎杜嘉线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_1d4db90" transform="matrix(1.000000 0.000000 0.000000 1.000000 11768.000000 -3055.000000) translate(0,20)">242</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1d4e110" transform="matrix(1.000000 0.000000 0.000000 1.000000 11648.000000 -2901.000000) translate(0,24)">鹿城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_1d4e600" transform="matrix(1.000000 0.000000 0.000000 1.000000 11652.000000 -2864.000000) translate(0,20)">271</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_1d4f7a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10175.000000 -2533.000000) translate(0,20)">285</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d4fba0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10662.000000 -2669.000000) translate(0,12)">220kV鹿紫Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d50cf0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10226.000000 -3415.000000) translate(0,11)">263</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d51a40" transform="matrix(1.000000 0.000000 0.000000 1.000000 11476.000000 -2993.000000) translate(0,12)">220kV鹿方线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d52520" transform="matrix(1.000000 0.000000 0.000000 1.000000 11850.000000 -2747.000000) translate(0,12)">500kV鹿和乙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d529c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11855.000000 -2825.000000) translate(0,11)">5651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d52bf0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11985.000000 -2598.000000) translate(0,11)">5741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d52e30" transform="matrix(1.000000 0.000000 0.000000 1.000000 12018.000000 -3231.000000) translate(0,11)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d53070" transform="matrix(1.000000 0.000000 0.000000 1.000000 10782.000000 -2624.000000) translate(0,12)">220kV鹿紫Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_1d532c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 11675.000000 -2816.000000) translate(0,20)">269</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_1d536c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10234.000000 -2522.000000) translate(0,20)">282</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_1d53b20" transform="matrix(1.000000 0.000000 0.000000 1.000000 10190.000000 -2451.000000) translate(0,20)">286</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d556a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10330.000000 -2347.000000) translate(0,12)">220kV谢紫Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d558e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 10813.000000 -1998.000000) translate(0,11)">223</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_1d55b10" transform="matrix(1.000000 0.000000 0.000000 1.000000 10800.000000 -1978.000000) translate(0,11)">221</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(1.000000 0.000000 0.000000 1.000000 9226.000000 -4077.000000) translate(0,24)">的鲁光伏电站</text>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11750,-2837 10137,-2490 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="11750,-2837 10137,-2490 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12024,-2564 11834,-2826 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="12024,-2564 11834,-2826 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11726,-2893 10176,-3417 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="11726,-2893 10176,-3417 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="187564" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11826,-3066 11806,-2953 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="11826,-3066 11806,-2953 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9917,-3504 10205,-3427 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="9917,-3504 10205,-3427 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11860,-3095 12052,-3261 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="11860,-3095 12052,-3261 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11886,-3090 12139,-3189 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="11886,-3090 12139,-3189 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11891,-3072 12157,-3071 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="11891,-3072 12157,-3071 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="187570" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11731,-2849 10136,-2506 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="11731,-2849 10136,-2506 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="88808" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="13147,-3603 12908,-3603 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="13147,-3603 12908,-3603 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1642" beginStationName="CX_SS" endPointId="0" endStationName="CX_YX" flowDrawDirect="1" flowShape="0" id="AC-110kV.shiyuIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12494,-3913 12310,-3913 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9187" ObjectName="AC-110kV.shiyuIhui_line"/>
    <cge:TPSR_Ref TObjectID="9187_SS-35"/></metadata>
   <polyline fill="none" opacity="0" points="12494,-3913 12310,-3913 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="57935" beginStationName="CX_SS" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-110kV.shiguoIIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12524,-3887 12140,-3689 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11547" ObjectName="AC-110kV.shiguoIIhui_line"/>
    <cge:TPSR_Ref TObjectID="11547_SS-3"/></metadata>
   <polyline fill="none" opacity="0" points="12524,-3887 12140,-3689 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="72518" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12845,-3592 12541,-3287 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="12845,-3592 12541,-3287 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="62683" beginStationName="CX_SS" endPointId="0" endStationName="PAS_XN" flowDrawDirect="1" flowShape="0" id="AC-110kV.shitian_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12565,-3892 12865,-3617 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="12699" ObjectName="AC-110kV.shitian_line"/>
    <cge:TPSR_Ref TObjectID="12699_SS-3"/></metadata>
   <polyline fill="none" opacity="0" points="12565,-3892 12865,-3617 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="74709" beginStationName="CX_XRD" endPointId="0" endStationName="CX_GY" flowDrawDirect="1" flowShape="0" id="AC-110kV.xianguo_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12086,-3669 11892,-3669 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17996" ObjectName="AC-110kV.xianguo_line"/>
    <cge:TPSR_Ref TObjectID="17996_SS-128"/></metadata>
   <polyline fill="none" opacity="0" points="12086,-3669 11892,-3669 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="27967" beginStationName="CX_YR" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-110kV.yongliuwan_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9120,-3783 9190,-3743 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9198" ObjectName="AC-110kV.yongliuwan_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="9120,-3783 9190,-3743 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="27967" beginStationName="CX_YR" endPointId="0" endStationName="CX_WM" flowDrawDirect="1" flowShape="0" id="AC-110kV.yongliuwan_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9239,-3714 9541,-3778 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9198" ObjectName="AC-110kV.yongliuwan_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="9239,-3714 9541,-3778 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="28131" beginStationName="CX_YIZ" endPointId="0" endStationName="CX_WM" flowDrawDirect="1" flowShape="0" id="AC-110kV.yiwan_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9060,-3804 8802,-3804 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11713" ObjectName="AC-110kV.yiwan_line"/>
    <cge:TPSR_Ref TObjectID="11713_SS-52"/></metadata>
   <polyline fill="none" opacity="0" points="9060,-3804 8802,-3804 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="28140" beginStationName="CX_DDH" endPointId="0" endStationName="CX_WM" flowDrawDirect="1" flowShape="0" id="AC-110kV.duowan_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9094,-3769 9094,-3590 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11712" ObjectName="AC-110kV.duowan_line"/>
    <cge:TPSR_Ref TObjectID="11712_SS-47"/></metadata>
   <polyline fill="none" opacity="0" points="9094,-3769 9094,-3590 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="19010" beginStationName="CX_YR" endPointId="0" endStationName="CX_WM" flowDrawDirect="1" flowShape="0" id="AC-110kV.yongliuwan_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9581,-3775 9618,-3727 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9198" ObjectName="AC-110kV.yongliuwan_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="9581,-3775 9618,-3727 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="19010" beginStationName="CX_YR" endPointId="0" endStationName="CX_WM" flowDrawDirect="1" flowShape="0" id="AC-110kV.yongliuwan_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9659,-3687 10121,-3463 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9198" ObjectName="AC-110kV.yongliuwan_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="9659,-3687 10121,-3463 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="19010" beginStationName="CX_YR" endPointId="0" endStationName="CX_WM" flowDrawDirect="1" flowShape="0" id="AC-110kV.yongliuwan_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10150,-3407 9590,-3291 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9198" ObjectName="AC-110kV.yongliuwan_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10150,-3407 9590,-3291 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="19002" beginStationName="CX_YM" endPointId="0" endStationName="CX_YR" flowDrawDirect="0" flowShape="0" id="AC-110kV.yuanyong_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10339,-3542 10808,-3290 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9197" ObjectName="AC-110kV.yuanyong_line"/>
    <cge:TPSR_Ref TObjectID="9197_SS-6"/></metadata>
   <polyline fill="none" opacity="0" points="10339,-3542 10808,-3290 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YM" endPointId="0" endStationName="CX_YR" flowDrawDirect="1" flowShape="0" id="AC-110kV.yuanyongTda_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10339,-3542 10415,-3673 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11861" ObjectName="AC-110kV.yuanyongTda_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10339,-3542 10415,-3673 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="3770" beginStationName="CX_YM" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-110kV.dawanzi_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10819,-3306 10457,-3667 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11757" ObjectName="AC-110kV.dawanzi_line"/>
    <cge:TPSR_Ref TObjectID="11757_SS-6"/></metadata>
   <polyline fill="none" opacity="0" points="10819,-3306 10457,-3667 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YM" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-110kV.dawanTyuan_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10574,-3550 10660,-3698 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11860" ObjectName="AC-110kV.dawanTyuan_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10574,-3550 10660,-3698 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YM" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-110kV.yueshaTyuan_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10852,-3638 10710,-3719 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11859" ObjectName="AC-110kV.yueshaTyuan_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10852,-3638 10710,-3719 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="3778" beginStationName="CX_YM" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-110kV.yuesha_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10852,-3316 10852,-3638 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11758" ObjectName="AC-110kV.yuesha_line"/>
    <cge:TPSR_Ref TObjectID="11758_SS-6"/></metadata>
   <polyline fill="none" opacity="0" points="10852,-3316 10852,-3638 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="3778" beginStationName="CX_YM" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-110kV.yuesha_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10852,-3638 10852,-3780 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11758" ObjectName="AC-110kV.yuesha_line"/>
    <cge:TPSR_Ref TObjectID="11758_SS-6"/></metadata>
   <polyline fill="none" opacity="0" points="10852,-3638 10852,-3780 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="3730" beginStationName="CX_YM" endPointId="0" endStationName="CX_HGY" flowDrawDirect="1" flowShape="0" id="AC-110kV.yuanhuang2hui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10890,-3284 11409,-3284 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11548" ObjectName="AC-110kV.yuanhuang2hui_line"/>
    <cge:TPSR_Ref TObjectID="11548_SS-6"/></metadata>
   <polyline fill="none" opacity="0" points="10890,-3284 11409,-3284 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="3722" beginStationName="CX_YM" endPointId="0" endStationName="CX_HGY" flowDrawDirect="1" flowShape="0" id="AC-110kV.yuanhuang1hui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10891,-3255 11414,-3255 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11549" ObjectName="AC-110kV.yuanhuang1hui_line"/>
    <cge:TPSR_Ref TObjectID="11549_SS-6"/></metadata>
   <polyline fill="none" opacity="0" points="10891,-3255 11414,-3255 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="3754" beginStationName="CX_YM" endPointId="0" endStationName="CX_DY" flowDrawDirect="1" flowShape="0" id="AC-110kV.yuanda_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10800,-3266 10145,-3266 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11550" ObjectName="AC-110kV.yuanda_line"/>
    <cge:TPSR_Ref TObjectID="11550_SS-6"/></metadata>
   <polyline fill="none" opacity="0" points="10800,-3266 10145,-3266 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="26087" beginStationName="CX_DY" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-110kV.daliu_line" maxValue="1" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="1">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10078,-3268 9592,-3268 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11715" ObjectName="AC-110kV.daliu_line"/>
    <cge:TPSR_Ref TObjectID="11715_SS-26"/></metadata>
   <polyline fill="none" opacity="0" points="10078,-3268 9592,-3268 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="26079" beginStationName="CX_YPJ" endPointId="0" endStationName="CX_DY" flowDrawDirect="1" flowShape="0" id="AC-110kV.yuda_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10081,-3249 9161,-3028 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11716" ObjectName="AC-110kV.yuda_line"/>
    <cge:TPSR_Ref TObjectID="11716_SS-54"/></metadata>
   <polyline fill="none" opacity="0" points="10081,-3249 9161,-3028 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="6576" beginStationName="CX_ZX" endPointId="0" endStationName="CX_YA" flowDrawDirect="1" flowShape="0" id="AC-110kV.ziyaonan_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10112,-2655 9872,-2655 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9193" ObjectName="AC-110kV.ziyaonan_line"/>
    <cge:TPSR_Ref TObjectID="9193_SS-7"/></metadata>
   <polyline fill="none" opacity="0" points="10112,-2655 9872,-2655 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="6576" beginStationName="CX_ZX" endPointId="0" endStationName="CX_YA" flowDrawDirect="1" flowShape="0" id="AC-110kV.ziyaonan_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10112,-2523 10112,-2655 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9193" ObjectName="AC-110kV.ziyaonan_line"/>
    <cge:TPSR_Ref TObjectID="9193_SS-7"/></metadata>
   <polyline fill="none" opacity="0" points="10112,-2523 10112,-2655 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="6573" beginStationName="CX_ZX" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-110kV.zinanyunTzx_line" maxValue="2" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="1">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10071,-2498 9965,-2560 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11564" ObjectName="AC-110kV.zinanyunTzx_line"/>
    <cge:TPSR_Ref TObjectID="11564_SS-146"/></metadata>
   <polyline fill="none" opacity="0" points="10071,-2498 9965,-2560 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="25060" beginStationName="CX_YM" endPointId="0" endStationName="CX_HGY" flowDrawDirect="1" flowShape="0" id="AC-110kV.dalongkouⅠhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9858,-2953 10112,-2807 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11880" ObjectName="AC-110kV.dalongkouⅠhui_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="9858,-2953 10112,-2807 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="6573" beginStationName="CX_ZX" endPointId="0" endStationName="CX_YA" flowDrawDirect="1" flowShape="0" id="AC-110kV.ziyaonan_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10112,-2655 10112,-2807 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9193" ObjectName="AC-110kV.ziyaonan_line"/>
    <cge:TPSR_Ref TObjectID="9193_SS-7"/></metadata>
   <polyline fill="none" opacity="0" points="10112,-2655 10112,-2807 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="6573" beginStationName="CX_ZX" endPointId="0" endStationName="CX_YA" flowDrawDirect="1" flowShape="0" id="AC-110kV.ziyao_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10112,-2807 10112,-2933 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9193" ObjectName="AC-110kV.ziyao_line"/>
    <cge:TPSR_Ref TObjectID="9193_SS-7"/></metadata>
   <polyline fill="none" opacity="0" points="10112,-2807 10112,-2933 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="62126" beginStationName="CX_ZX" endPointId="0" endStationName="CX_XQ" flowDrawDirect="1" flowShape="0" id="AC-110kV.zixindian_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10268,-2763 10142,-2511 " stroke-width="1.097"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11884" ObjectName="AC-110kV.zixindian_line"/>
    <cge:TPSR_Ref TObjectID="11884_SS-119"/></metadata>
   <polyline fill="none" opacity="0" points="10268,-2763 10142,-2511 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="6561" beginStationName="CX_ZX" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-220kV.xiangzi_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9873,-2467 9275,-2467 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6571" ObjectName="AC-220kV.xiangzi_line"/>
    <cge:TPSR_Ref TObjectID="6571_SS-247"/></metadata>
   <polyline fill="none" opacity="0" points="9873,-2467 9275,-2467 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_ZX" endPointId="0" endStationName="CX_XQ" flowDrawDirect="1" flowShape="0" id="AC-110kV.zixindian_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10235,-2701 11213,-2830 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11884" ObjectName="AC-110kV.zixindian_line"/>
    <cge:TPSR_Ref TObjectID="11884_SS-119"/></metadata>
   <polyline fill="none" opacity="0" points="10235,-2701 11213,-2830 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="3746" beginStationName="CX_YM" endPointId="0" endStationName="CX_MD" flowDrawDirect="1" flowShape="0" id="AC-110kV.yuanmou_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10817,-3235 10452,-2684 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11708" ObjectName="AC-110kV.yuanmou_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10817,-3235 10452,-2684 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YM" endPointId="0" endStationName="CX_YR" flowDrawDirect="1" flowShape="0" id="AC-110kV.yuanmouTyang_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10613,-2928 10953,-2928 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11863" ObjectName="AC-110kV.yuanmouTyang_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10613,-2928 10953,-2928 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="3738" beginStationName="CX_YM" endPointId="0" endStationName="CX_YR" flowDrawDirect="1" flowShape="0" id="AC-110kV.yangjiuhe_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10861,-3226 11006,-2959 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11865" ObjectName="AC-110kV.yangjiuhe_line"/>
    <cge:TPSR_Ref TObjectID="11865_SS-6"/></metadata>
   <polyline fill="none" opacity="0" points="10861,-3226 11006,-2959 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YM" endPointId="0" endStationName="CX_YR" flowDrawDirect="1" flowShape="0" id="AC-110kV.yangjiuTdian_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10950,-3062 11246,-2872 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11866" ObjectName="AC-110kV.yangjiuTdian_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10950,-3062 11246,-2872 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="6567" beginStationName="CX_ZX" endPointId="0" endStationName="CX_DJ" flowDrawDirect="1" flowShape="0" id="AC-110kV.zidongIIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10090,-2443 9886,-1962 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11562" ObjectName="AC-110kV.zidongIIhui_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10090,-2443 9886,-1962 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="6567" beginStationName="CX_ZX" endPointId="0" endStationName="CX_DJ" flowDrawDirect="1" flowShape="0" id="AC-110kV.zidongIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10098,-2433 9908,-1951 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11560" ObjectName="AC-110kV.zidongIhui_line"/>
    <cge:TPSR_Ref TObjectID="11560_SS-7"/></metadata>
   <polyline fill="none" opacity="0" points="10098,-2433 9908,-1951 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="12923" beginStationName="CX_LF" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-110kV.chumou_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10854,-1975 10670,-2243 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11851" ObjectName="AC-110kV.chumou_line"/>
    <cge:TPSR_Ref TObjectID="11851_SS-4"/></metadata>
   <polyline fill="none" opacity="0" points="10854,-1975 10670,-2243 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_LF" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-110kV.chumouTguan_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10670,-2243 11226,-2243 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11852" ObjectName="AC-110kV.chumouTguan_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10670,-2243 11226,-2243 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_XJH" endPointId="0" endStationName="CX_JS" flowDrawDirect="1" flowShape="0" id="AC-110kV.xiejinTguan_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11251,-1944 11251,-2215 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11853" ObjectName="AC-110kV.xiejinTguan_line"/>
    <cge:TPSR_Ref TObjectID="11853_SS-119"/></metadata>
   <polyline fill="none" opacity="0" points="11251,-1944 11251,-2215 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="6570" beginStationName="CX_ZX" endPointId="0" endStationName="CX_SG" flowDrawDirect="1" flowShape="0" id="AC-110kV.zisha_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10121,-2432 10262,-2117 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11565" ObjectName="AC-110kV.zisha_line"/>
    <cge:TPSR_Ref TObjectID="11565_SS-7"/></metadata>
   <polyline fill="none" opacity="0" points="10121,-2432 10262,-2117 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_XJH" endPointId="0" endStationName="CX_XJ" flowDrawDirect="1" flowShape="0" id="AC-110kV.xiexi_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10831,-1937 10135,-1937 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11557" ObjectName="AC-110kV.xiexi_line"/>
    <cge:TPSR_Ref TObjectID="11557_SS-4"/></metadata>
   <polyline fill="none" opacity="0" points="10831,-1937 10135,-1937 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="12905" beginStationName="CX_XJH" endPointId="0" endStationName="CX_SG" flowDrawDirect="1" flowShape="0" id="AC-110kV.xiesha_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10835,-1954 10311,-2068 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11556" ObjectName="AC-110kV.xiesha_line"/>
    <cge:TPSR_Ref TObjectID="11556_SS-255"/></metadata>
   <polyline fill="none" opacity="0" points="10835,-1954 10311,-2068 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="12941" beginStationName="CX_XJH" endPointId="0" endStationName="CX_ZX" flowDrawDirect="1" flowShape="0" id="AC-220kV.xiezi_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10835,-1954 10138,-2440 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9165" ObjectName="AC-220kV.xiezi_line"/>
    <cge:TPSR_Ref TObjectID="9165_SS-7"/></metadata>
   <polyline fill="none" opacity="0" points="10835,-1954 10138,-2440 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="21886" beginStationName="CX_ZX" endPointId="0" endStationName="CX_BLXC" flowDrawDirect="1" flowShape="0" id="AC-110kV.zibaixi_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10076,-1956 10036,-1978 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6570" ObjectName="AC-110kV.zibaixi_line"/>
    <cge:TPSR_Ref TObjectID="6570_SS-7"/></metadata>
   <polyline fill="none" opacity="0" points="10076,-1956 10036,-1978 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="43159" beginStationName="CX_YM" endPointId="0" endStationName="CX_HGY" flowDrawDirect="1" flowShape="0" id="AC-110kV.fujiaTjie_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9765,-1503 10091,-1503 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11858" ObjectName="AC-110kV.fujiaTjie_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="9765,-1503 10091,-1503 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="21872" beginStationName="CX_YM" endPointId="0" endStationName="CX_HGY" flowDrawDirect="0" flowShape="0" id="AC-110kV.exie_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10091,-1503 10091,-1897 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11857" ObjectName="AC-110kV.exie_line"/>
    <cge:TPSR_Ref TObjectID="11857_SS-11"/></metadata>
   <polyline fill="none" opacity="0" points="10091,-1503 10091,-1897 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="47079" beginStationName="CX_YM" endPointId="0" endStationName="CX_HGY" flowDrawDirect="1" flowShape="0" id="AC-110kV.exie_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10091,-1503 10091,-1323 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11857" ObjectName="AC-110kV.exie_line"/>
    <cge:TPSR_Ref TObjectID="11857_SS-11"/></metadata>
   <polyline fill="none" opacity="0" points="10091,-1503 10091,-1323 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9531,-1300 9531,-1151 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="9531,-1300 9531,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="47074" beginStationName="CX_YM" endPointId="0" endStationName="CX_YR" flowDrawDirect="1" flowShape="0" id="AC-110kV.yierjilian_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10048,-1300 9531,-1300 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11875" ObjectName="AC-110kV.yierjilian_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10048,-1300 9531,-1300 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="47216" beginStationName="CX_YM" endPointId="0" endStationName="CX_YR" flowDrawDirect="1" flowShape="0" id="AC-110kV.yierjilian_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9374,-1300 9531,-1300 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11875" ObjectName="AC-110kV.yierjilian_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="9374,-1300 9531,-1300 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="47102" beginStationName="CX_YM" endPointId="0" endStationName="CX_YR" flowDrawDirect="1" flowShape="0" id="AC-110kV.linger_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10057,-1281 9712,-1165 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11876" ObjectName="AC-110kV.linger_line"/>
    <cge:TPSR_Ref TObjectID="11876_SS-76"/></metadata>
   <polyline fill="none" opacity="0" points="10057,-1281 9712,-1165 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="47097" beginStationName="CX_YM" endPointId="0" endStationName="CX_HGY" flowDrawDirect="1" flowShape="0" id="AC-110kV.bue_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10089,-1282 10089,-1147 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11886" ObjectName="AC-110kV.bue_line"/>
    <cge:TPSR_Ref TObjectID="11886_SS-78"/></metadata>
   <polyline fill="none" opacity="0" points="10089,-1282 10089,-1147 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="47814" beginStationName="CX_YM" endPointId="0" endStationName="CX_YR" flowDrawDirect="1" flowShape="0" id="AC-110kV.nibu_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10126,-1125 10295,-1125 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11878" ObjectName="AC-110kV.nibu_line"/>
    <cge:TPSR_Ref TObjectID="11878_SS-78"/></metadata>
   <polyline fill="none" opacity="0" points="10126,-1125 10295,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="29327" beginStationName="CX_YM" endPointId="0" endStationName="CX_HGY" flowDrawDirect="1" flowShape="0" id="AC-110kV.shuangeda_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10861,-1304 10490,-1304 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11856" ObjectName="AC-110kV.shuangeda_line"/>
    <cge:TPSR_Ref TObjectID="11856_SS-115"/></metadata>
   <polyline fill="none" opacity="0" points="10861,-1304 10490,-1304 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="47092" beginStationName="CX_YM" endPointId="0" endStationName="CX_HGY" flowDrawDirect="1" flowShape="0" id="AC-110kV.shuangeda_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10490,-1304 10123,-1304 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11856" ObjectName="AC-110kV.shuangeda_line"/>
    <cge:TPSR_Ref TObjectID="11856_SS-115"/></metadata>
   <polyline fill="none" opacity="0" points="10490,-1304 10123,-1304 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="12989" beginStationName="CX_XJH" endPointId="0" endStationName="CX_SB" flowDrawDirect="1" flowShape="0" id="AC-110kV.xieyanshuang_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10884,-1889 10884,-1630 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11720" ObjectName="AC-110kV.xieyanshuang_line"/>
    <cge:TPSR_Ref TObjectID="11720_SS-115"/></metadata>
   <polyline fill="none" opacity="0" points="10884,-1889 10884,-1630 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="48932" beginStationName="CX_HP" endPointId="0" endStationName="CX_ZX" flowDrawDirect="1" flowShape="0" id="AC-220kV.hezi_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11974,-2492 10162,-2492 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9207" ObjectName="AC-220kV.hezi_line"/>
    <cge:TPSR_Ref TObjectID="9207_SS-245"/></metadata>
   <polyline fill="none" opacity="0" points="11974,-2492 10162,-2492 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="48767" beginStationName="CX_HP" endPointId="0" endStationName="CX_XJH" flowDrawDirect="1" flowShape="0" id="AC-220kV.hexieI_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12002,-2441 10921,-1951 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9208" ObjectName="AC-220kV.hexieI_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="12002,-2441 10921,-1951 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="48676" beginStationName="CX_HP" endPointId="0" endStationName="CX_LF" flowDrawDirect="1" flowShape="0" id="AC-220kV.heluIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12029,-2427 12031,-1974 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9171" ObjectName="AC-220kV.heluIhui_line"/>
    <cge:TPSR_Ref TObjectID="9171_SS-2"/></metadata>
   <polyline fill="none" opacity="0" points="12029,-2427 12031,-1974 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="48693" beginStationName="CX_HP" endPointId="0" endStationName="CX_LF" flowDrawDirect="1" flowShape="0" id="AC-220kV.heluIIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12054,-2426 12054,-1975 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9170" ObjectName="AC-220kV.heluIIhui_line"/>
    <cge:TPSR_Ref TObjectID="9170_SS-2"/></metadata>
   <polyline fill="none" opacity="0" points="12054,-2426 12054,-1975 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="20460" beginStationName="CX_XJH" endPointId="0" endStationName="CX_JS" flowDrawDirect="1" flowShape="0" id="AC-110kV.xiejinIhuui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11616,-1944 11462,-1944 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11554" ObjectName="AC-110kV.xiejinIhuui_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="11616,-1944 11462,-1944 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="20476" beginStationName="CX_XJH" endPointId="0" endStationName="CX_LT" flowDrawDirect="1" flowShape="0" id="AC-110kV.xieyanlongjin_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11615,-1920 11430,-1920 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11721" ObjectName="AC-110kV.xieyanlongjin_line"/>
    <cge:TPSR_Ref TObjectID="11721_SS-21"/></metadata>
   <polyline fill="none" opacity="0" points="11615,-1920 11430,-1920 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="13006" beginStationName="CX_XJH" endPointId="0" endStationName="CX_LT" flowDrawDirect="1" flowShape="0" id="AC-110kV.xieyanlongjin_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10924,-1920 11225,-1920 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11721" ObjectName="AC-110kV.xieyanlongjin_line"/>
    <cge:TPSR_Ref TObjectID="11721_SS-21"/></metadata>
   <polyline fill="none" opacity="0" points="10924,-1920 11225,-1920 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="16682" beginStationName="CX_XJH" endPointId="0" endStationName="CX_LT" flowDrawDirect="1" flowShape="0" id="AC-110kV.xieyanlongjin_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11118,-1798 11225,-1798 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11721" ObjectName="AC-110kV.xieyanlongjin_line"/>
    <cge:TPSR_Ref TObjectID="11721_SS-21"/></metadata>
   <polyline fill="none" opacity="0" points="11118,-1798 11225,-1798 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="13006" beginStationName="CX_XJH" endPointId="0" endStationName="CX_LT" flowDrawDirect="1" flowShape="0" id="AC-110kV.xieyanlongjin_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11225,-1920 11225,-1798 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11721" ObjectName="AC-110kV.xieyanlongjin_line"/>
    <cge:TPSR_Ref TObjectID="11721_SS-21"/></metadata>
   <polyline fill="none" opacity="0" points="11225,-1920 11225,-1798 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1853" beginStationName="CX_LF" endPointId="0" endStationName="CX_JS" flowDrawDirect="1" flowShape="0" id="AC-110kV.lujiIIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11995,-1922 11854,-1922 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9174" ObjectName="AC-110kV.lujiIIhui_line"/>
    <cge:TPSR_Ref TObjectID="9174_SS-2"/></metadata>
   <polyline fill="none" opacity="0" points="11995,-1922 11854,-1922 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1999" beginStationName="CX_LF" endPointId="0" endStationName="CX_SY" flowDrawDirect="1" flowShape="0" id="AC-110kV.lushangIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12063,-1892 12346,-1516 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9184" ObjectName="AC-110kV.lushangIhui_line"/>
    <cge:TPSR_Ref TObjectID="9184_SS-2"/></metadata>
   <polyline fill="none" opacity="0" points="12063,-1892 12346,-1516 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="48784" beginStationName="CX_HP" endPointId="0" endStationName="CX_YZ" flowDrawDirect="1" flowShape="0" id="AC-220kV.heyaoIIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12116,-2501 12656,-2501 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11490" ObjectName="AC-220kV.heyaoIIhui_line"/>
    <cge:TPSR_Ref TObjectID="11490_SS-5"/></metadata>
   <polyline fill="none" opacity="0" points="12116,-2501 12656,-2501 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="52016" beginStationName="CX_HP" endPointId="0" endStationName="CX_YZ" flowDrawDirect="1" flowShape="0" id="AC-220kV.heyaoIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12113,-2473 12659,-2473 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11491" ObjectName="AC-220kV.heyaoIhui_line"/>
    <cge:TPSR_Ref TObjectID="11491_SS-5"/></metadata>
   <polyline fill="none" opacity="0" points="12113,-2473 12659,-2473 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="5465" beginStationName="CX_YZ" endPointId="0" endStationName="CX_HS" flowDrawDirect="1" flowShape="0" id="AC-110kV.yaohong_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12705,-2445 12705,-1943 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11433" ObjectName="AC-110kV.yaohong_line"/>
    <cge:TPSR_Ref TObjectID="11433_SS-5"/></metadata>
   <polyline fill="none" opacity="0" points="12705,-2445 12705,-1943 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1833" beginStationName="CX_LF" endPointId="0" endStationName="CX_DEG" flowDrawDirect="1" flowShape="0" id="AC-110kV.degangIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12070,-1967 12174,-2313 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9176" ObjectName="AC-110kV.degangIhui_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="12070,-1967 12174,-2313 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="26731" beginStationName="CX_LF" endPointId="0" endStationName="CX_DEG" flowDrawDirect="1" flowShape="0" id="AC-110kV.luyaode_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12303,-2261 12381,-2211 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11432" ObjectName="AC-110kV.luyaode_line"/>
    <cge:TPSR_Ref TObjectID="11432_SS-2"/></metadata>
   <polyline fill="none" opacity="0" points="12303,-2261 12381,-2211 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="5606" beginStationName="CX_YZ" endPointId="0" endStationName="CX_XLTY" flowDrawDirect="1" flowShape="0" id="AC-220kV.yaoxinhui_2line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12709,-2535 12709,-2855 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11489" ObjectName="AC-220kV.yaoxinhui_2line"/>
    <cge:TPSR_Ref TObjectID="11489_SS-5"/></metadata>
   <polyline fill="none" opacity="0" points="12709,-2535 12709,-2855 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1943" beginStationName="CX_LF" endPointId="0" endStationName="CX_QF" flowDrawDirect="1" flowShape="0" id="AC-110kV.luqinIIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12083,-1945 12470,-2109 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9173" ObjectName="AC-110kV.luqinIIhui_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="12083,-1945 12470,-2109 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="5447" beginStationName="CX_YZ" endPointId="0" endStationName="CX_QF" flowDrawDirect="1" flowShape="0" id="AC-110kV.yaoqin_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12685,-2448 12508,-2142 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11431" ObjectName="AC-110kV.yaoqin_line"/>
    <cge:TPSR_Ref TObjectID="11431_SS-5"/></metadata>
   <polyline fill="none" opacity="0" points="12685,-2448 12508,-2142 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1863" beginStationName="CX_LF" endPointId="0" endStationName="CX_DEG" flowDrawDirect="1" flowShape="0" id="AC-110kV.luyaode_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12082,-1955 12138,-2002 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11432" ObjectName="AC-110kV.luyaode_line"/>
    <cge:TPSR_Ref TObjectID="11432_SS-2"/></metadata>
   <polyline fill="none" opacity="0" points="12082,-1955 12138,-2002 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="26731" beginStationName="CX_LF" endPointId="0" endStationName="CX_DEG" flowDrawDirect="1" flowShape="0" id="AC-110kV.luyaode_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12225,-2312 12254,-2290 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11432" ObjectName="AC-110kV.luyaode_line"/>
    <cge:TPSR_Ref TObjectID="11432_SS-2"/></metadata>
   <polyline fill="none" opacity="0" points="12225,-2312 12254,-2290 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="5474" beginStationName="CX_YZ" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-110kV.yaoshuang_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12747,-2490 13074,-2490 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11434" ObjectName="AC-110kV.yaoshuang_line"/>
    <cge:TPSR_Ref TObjectID="11434_SS-5"/></metadata>
   <polyline fill="none" opacity="0" points="12747,-2490 13074,-2490 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="48710" beginStationName="CX_HP" endPointId="0" endStationName="CX_YM" flowDrawDirect="1" flowShape="0" id="AC-220kV.heyuanIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12009,-2558 10854,-3282 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11500" ObjectName="AC-220kV.heyuanIhui_line"/>
    <cge:TPSR_Ref TObjectID="11500_SS-6"/></metadata>
   <polyline fill="none" opacity="0" points="12009,-2558 10854,-3282 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1646" beginStationName="CX_SS" endPointId="0" endStationName="CX_YX" flowDrawDirect="1" flowShape="0" id="AC-110kV.shiyunIIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12494,-3948 12224,-3948 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9188" ObjectName="AC-110kV.shiyunIIhui_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="12494,-3948 12224,-3948 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1610" beginStationName="CX_SS" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-110kV.shiluchongIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12873,-3930 12873,-4025 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9189" ObjectName="AC-110kV.shiluchongIhui_line"/>
    <cge:TPSR_Ref TObjectID="9189_SS-3"/></metadata>
   <polyline fill="none" opacity="0" points="12873,-3930 12873,-4025 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1610" beginStationName="CX_SS" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-110kV.shiluchongIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12578,-3930 12873,-3930 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9189" ObjectName="AC-110kV.shiluchongIhui_line"/>
    <cge:TPSR_Ref TObjectID="9189_SS-3"/></metadata>
   <polyline fill="none" opacity="0" points="12578,-3930 12873,-3930 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1610" beginStationName="CX_SS" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-110kV.shiluchongIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12873,-3930 13120,-3930 13120,-4019 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9189" ObjectName="AC-110kV.shiluchongIhui_line"/>
    <cge:TPSR_Ref TObjectID="9189_SS-3"/></metadata>
   <polyline fill="none" opacity="0" points="12873,-3930 13120,-3930 13120,-4019 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1606" beginStationName="CX_SS" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-110kV.shiluchongII_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12900,-3906 12900,-4021 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9191" ObjectName="AC-110kV.shiluchongII_line"/>
    <cge:TPSR_Ref TObjectID="9191_SS-3"/></metadata>
   <polyline fill="none" opacity="0" points="12900,-3906 12900,-4021 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1606" beginStationName="CX_SS" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-110kV.shiluchongII_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12580,-3906 12900,-3906 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9191" ObjectName="AC-110kV.shiluchongII_line"/>
    <cge:TPSR_Ref TObjectID="9191_SS-3"/></metadata>
   <polyline fill="none" opacity="0" points="12580,-3906 12900,-3906 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1606" beginStationName="CX_SS" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-110kV.shiluchongII_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12900,-3906 13149,-3906 13149,-4023 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9191" ObjectName="AC-110kV.shiluchongII_line"/>
    <cge:TPSR_Ref TObjectID="9191_SS-3"/></metadata>
   <polyline fill="none" opacity="0" points="12900,-3906 13149,-3906 13149,-4023 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="57923" beginStationName="CX_SS" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-110kV.shiguoIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12509,-3899 12121,-3701 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11546" ObjectName="AC-110kV.shiguoIhui_line"/>
    <cge:TPSR_Ref TObjectID="11546_SS-3"/></metadata>
   <polyline fill="none" opacity="0" points="12509,-3899 12121,-3701 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1598" beginStationName="CX_SS" endPointId="0" endStationName="CX_WD" flowDrawDirect="1" flowShape="0" id="AC-110kV.shiwuIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12535,-3882 12535,-3628 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9185" ObjectName="AC-110kV.shiwuIhui_line"/>
    <cge:TPSR_Ref TObjectID="9185_SS-3"/></metadata>
   <polyline fill="none" opacity="0" points="12535,-3882 12535,-3628 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1602" beginStationName="CX_SS" endPointId="0" endStationName="CX_WD" flowDrawDirect="1" flowShape="0" id="AC-110kV.shiwuIIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12558,-3882 12558,-3627 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9186" ObjectName="AC-110kV.shiwuIIhui_line"/>
    <cge:TPSR_Ref TObjectID="9186_SS-3"/></metadata>
   <polyline fill="none" opacity="0" points="12558,-3882 12558,-3627 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="15349" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12541,-3389 12382,-3389 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="12541,-3389 12382,-3389 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="15349" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12541,-3566 12541,-3389 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="12541,-3566 12541,-3389 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="15349" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12541,-3389 12541,-3287 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="12541,-3389 12541,-3287 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12541,-3287 12541,-3204 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="12541,-3287 12541,-3204 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="6579" beginStationName="CX_ZX" endPointId="0" endStationName="CX_BLXC" flowDrawDirect="1" flowShape="0" id="AC-110kV.zibai_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10110,-2432 10232,-1772 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11559" ObjectName="AC-110kV.zibai_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10110,-2432 10232,-1772 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="13057" beginStationName="CX_XJH" endPointId="0" endStationName="CX_BLXC" flowDrawDirect="1" flowShape="0" id="AC-110kV.xiebai_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10833,-1922 10339,-1789 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11552" ObjectName="AC-110kV.xiebai_line"/>
    <cge:TPSR_Ref TObjectID="11552_SS-4"/></metadata>
   <polyline fill="none" opacity="0" points="10833,-1922 10339,-1789 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="21442" beginStationName="CX_ZX" endPointId="0" endStationName="CX_BLXC" flowDrawDirect="1" flowShape="0" id="AC-110kV.zibaixi_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10121,-1906 10222,-1826 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6570" ObjectName="AC-110kV.zibaixi_line"/>
    <cge:TPSR_Ref TObjectID="6570_SS-7"/></metadata>
   <polyline fill="none" opacity="0" points="10121,-1906 10222,-1826 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="12932" beginStationName="CX_XJH" endPointId="0" endStationName="CX_DJ" flowDrawDirect="1" flowShape="0" id="AC-110kV.xiedong_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10852,-1896 10605,-1670 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11553" ObjectName="AC-110kV.xiedong_line"/>
    <cge:TPSR_Ref TObjectID="11553_SS-4"/></metadata>
   <polyline fill="none" opacity="0" points="10852,-1896 10605,-1670 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="12932" beginStationName="CX_XJH" endPointId="0" endStationName="CX_DJ" flowDrawDirect="1" flowShape="0" id="AC-110kV.xiedong_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10375,-1797 10535,-1670 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11553" ObjectName="AC-110kV.xiedong_line"/>
    <cge:TPSR_Ref TObjectID="11553_SS-4"/></metadata>
   <polyline fill="none" opacity="0" points="10375,-1797 10535,-1670 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="29424" beginStationName="CX_XJH" endPointId="0" endStationName="CX_SB" flowDrawDirect="1" flowShape="0" id="AC-110kV.xieyanshuang_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10884,-1333 10884,-1643 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11720" ObjectName="AC-110kV.xieyanshuang_line"/>
    <cge:TPSR_Ref TObjectID="11720_SS-115"/></metadata>
   <polyline fill="none" opacity="0" points="10884,-1333 10884,-1643 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="20484" beginStationName="CX_LF" endPointId="0" endStationName="CX_JS" flowDrawDirect="1" flowShape="0" id="AC-110kV.lujiIIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11660,-1922 11854,-1922 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9174" ObjectName="AC-110kV.lujiIIhui_line"/>
    <cge:TPSR_Ref TObjectID="9174_SS-2"/></metadata>
   <polyline fill="none" opacity="0" points="11660,-1922 11854,-1922 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1863" beginStationName="CX_LF" endPointId="0" endStationName="CX_DEG" flowDrawDirect="1" flowShape="0" id="AC-110kV.luyaode_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12178,-2042 12381,-2211 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11432" ObjectName="AC-110kV.luyaode_line"/>
    <cge:TPSR_Ref TObjectID="11432_SS-2"/></metadata>
   <polyline fill="none" opacity="0" points="12178,-2042 12381,-2211 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="27661" beginStationName="CX_XJH" endPointId="0" endStationName="CX_SB" flowDrawDirect="1" flowShape="0" id="AC-110kV.xieyanshuang_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11191,-1643 11145,-1643 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11720" ObjectName="AC-110kV.xieyanshuang_line"/>
    <cge:TPSR_Ref TObjectID="11720_SS-115"/></metadata>
   <polyline fill="none" opacity="0" points="11191,-1643 11145,-1643 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="27676" beginStationName="CX_XJH" endPointId="0" endStationName="CX_LT" flowDrawDirect="1" flowShape="0" id="AC-110kV.xieyanlongjin_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11225,-1671 11225,-1798 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11721" ObjectName="AC-110kV.xieyanlongjin_line"/>
    <cge:TPSR_Ref TObjectID="11721_SS-21"/></metadata>
   <polyline fill="none" opacity="0" points="11225,-1671 11225,-1798 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="12950" beginStationName="CX_XJH" endPointId="0" endStationName="CX_JS" flowDrawDirect="1" flowShape="0" id="AC-110kV.xiejinIhuui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10923,-1944 11085,-1944 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11554" ObjectName="AC-110kV.xiejinIhuui_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10923,-1944 11085,-1944 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="16674" beginStationName="CX_YM" endPointId="0" endStationName="CX_HGY" flowDrawDirect="1" flowShape="0" id="AC-110kV.longtanIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11085,-1829 11085,-1944 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11855" ObjectName="AC-110kV.longtanIhui_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="11085,-1829 11085,-1944 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="87974" beginStationName="CX_YM" endPointId="0" endStationName="CX_HGY" flowDrawDirect="1" flowShape="0" id="AC-110kV.sheziIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11461,-2087 11462,-1944 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11854" ObjectName="AC-110kV.sheziIhui_line"/>
    <cge:TPSR_Ref TObjectID="11854_SS-15"/></metadata>
   <polyline fill="none" opacity="0" points="11461,-2087 11462,-1944 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="88029" beginStationName="CX_YM" endPointId="0" endStationName="CX_HGY" flowDrawDirect="1" flowShape="0" id="AC-110kV.sheziIIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11430,-2091 11430,-1920 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11885" ObjectName="AC-110kV.sheziIIhui_line"/>
    <cge:TPSR_Ref TObjectID="11885_SS-15"/></metadata>
   <polyline fill="none" opacity="0" points="11430,-2091 11430,-1920 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="25066" beginStationName="CX_YM" endPointId="0" endStationName="CX_HGY" flowDrawDirect="1" flowShape="0" id="AC-110kV.dalongkouⅡhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9860,-2978 10114,-3099 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11881" ObjectName="AC-110kV.dalongkouⅡhui_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="9860,-2978 10114,-3099 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="25352" beginStationName="CX_YM" endPointId="0" endStationName="CX_YR" flowDrawDirect="1" flowShape="0" id="AC-110kV.xinqiaoTjie_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10312,-2802 10530,-2802 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11862" ObjectName="AC-110kV.xinqiaoTjie_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10312,-2802 10530,-2802 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="6561" beginStationName="CX_ZX" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-220kV.xiangzi_line" maxValue="2" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="1">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10067,-2467 9930,-2467 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6571" ObjectName="AC-220kV.xiangzi_line"/>
    <cge:TPSR_Ref TObjectID="6571_SS-247"/></metadata>
   <polyline fill="none" opacity="0" points="10067,-2467 9930,-2467 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="2007" beginStationName="CX_LF" endPointId="0" endStationName="CX_SY" flowDrawDirect="1" flowShape="0" id="AC-110kV.lushangIIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12079,-1906 12365,-1528 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9183" ObjectName="AC-110kV.lushangIIhui_line"/>
    <cge:TPSR_Ref TObjectID="9183_SS-2"/></metadata>
   <polyline fill="none" opacity="0" points="12079,-1906 12365,-1528 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1937" beginStationName="CX_LF" endPointId="0" endStationName="CX_HS" flowDrawDirect="1" flowShape="0" id="AC-110kV.luhong_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12088,-1931 12667,-1931 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9182" ObjectName="AC-110kV.luhong_line"/>
    <cge:TPSR_Ref TObjectID="9182_SS-24"/></metadata>
   <polyline fill="none" opacity="0" points="12088,-1931 12667,-1931 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1858" beginStationName="CX_LF" endPointId="0" endStationName="CX_JS" flowDrawDirect="1" flowShape="0" id="AC-110kV.lujinIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11996,-1946 11668,-1946 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9177" ObjectName="AC-110kV.lujinIhui_line"/>
    <cge:TPSR_Ref TObjectID="9177_SS-22"/></metadata>
   <polyline fill="none" opacity="0" points="11996,-1946 11668,-1946 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1838" beginStationName="CX_LF" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-110kV.luqianIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11999,-1905 11866,-1724 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9175" ObjectName="AC-110kV.luqianIhui_line"/>
    <cge:TPSR_Ref TObjectID="9175_SS-2"/></metadata>
   <polyline fill="none" opacity="0" points="11999,-1905 11866,-1724 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1868" beginStationName="CX_LF" endPointId="0" endStationName="PAS_XN" flowDrawDirect="1" flowShape="0" id="AC-110kV.qinqianIIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12046,-1884 12046,-1510 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9172" ObjectName="AC-110kV.qinqianIIhui_line"/>
    <cge:TPSR_Ref TObjectID="9172_SS-2"/></metadata>
   <polyline fill="none" opacity="0" points="12046,-1884 12046,-1510 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1843" beginStationName="CX_LF" endPointId="0" endStationName="PAS_XN" flowDrawDirect="1" flowShape="0" id="AC-110kV.qinqianIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12031,-1887 12031,-1511 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9180" ObjectName="AC-110kV.qinqianIhui_line"/>
    <cge:TPSR_Ref TObjectID="9180_SS-2"/></metadata>
   <polyline fill="none" opacity="0" points="12031,-1887 12031,-1511 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="12950" beginStationName="CX_XJH" endPointId="0" endStationName="CX_JS" flowDrawDirect="1" flowShape="0" id="AC-110kV.xiejinIhuui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11251,-1944 11462,-1944 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11554" ObjectName="AC-110kV.xiejinIhuui_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="11251,-1944 11462,-1944 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="27661" beginStationName="CX_XJH" endPointId="0" endStationName="CX_SB" flowDrawDirect="1" flowShape="0" id="AC-110kV.xieyanshuang_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11088,-1643 10884,-1643 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11720" ObjectName="AC-110kV.xieyanshuang_line"/>
    <cge:TPSR_Ref TObjectID="11720_SS-115"/></metadata>
   <polyline fill="none" opacity="0" points="11088,-1643 10884,-1643 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="21886" beginStationName="CX_ZX" endPointId="0" endStationName="CX_BLXC" flowDrawDirect="1" flowShape="0" id="AC-110kV.zibaixi_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9994,-2003 9940,-2034 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6570" ObjectName="AC-110kV.zibaixi_line"/>
    <cge:TPSR_Ref TObjectID="6570_SS-7"/></metadata>
   <polyline fill="none" opacity="0" points="9994,-2003 9940,-2034 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_XN" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-110kV.ShuangEDaTDHS" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10490,-1304 10490,-1384 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18056" ObjectName="AC-110kV.ShuangEDaTDHS"/>
    <cge:TPSR_Ref TObjectID="18056_SS-119"/></metadata>
   <polyline fill="none" opacity="0" points="10490,-1304 10490,-1384 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="18993" beginStationName="CX_YR" endPointId="0" endStationName="CX_WM" flowDrawDirect="1" flowShape="0" id="AC-110kV.yongwandi_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9533,-3804 9308,-3803 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11714" ObjectName="AC-110kV.yongwandi_line"/>
    <cge:TPSR_Ref TObjectID="11714_SS-8"/></metadata>
   <polyline fill="none" opacity="0" points="9533,-3804 9308,-3803 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="18993" beginStationName="CX_YR" endPointId="0" endStationName="CX_WM" flowDrawDirect="1" flowShape="0" id="AC-110kV.yongwandi_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9308,-3803 9131,-3803 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11714" ObjectName="AC-110kV.yongwandi_line"/>
    <cge:TPSR_Ref TObjectID="11714_SS-8"/></metadata>
   <polyline fill="none" opacity="0" points="9308,-3803 9131,-3803 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="18993" beginStationName="CX_YR" endPointId="0" endStationName="CX_WM" flowDrawDirect="1" flowShape="0" id="AC-110kV.yongwandi_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9308,-3803 9308,-3998 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11714" ObjectName="AC-110kV.yongwandi_line"/>
    <cge:TPSR_Ref TObjectID="11714_SS-8"/></metadata>
   <polyline fill="none" opacity="0" points="9308,-3803 9308,-3998 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="13006" beginStationName="CX_XJH" endPointId="0" endStationName="CX_LT" flowDrawDirect="1" flowShape="0" id="AC-110kV.xieyanlongjin_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11225,-1920 11431,-1920 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11721" ObjectName="AC-110kV.xieyanlongjin_line"/>
    <cge:TPSR_Ref TObjectID="11721_SS-21"/></metadata>
   <polyline fill="none" opacity="0" points="11225,-1920 11431,-1920 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="13057" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10289,-1761 10259,-1745 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10289,-1761 10259,-1745 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="26072" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10112,-3233 10112,-3197 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10112,-3233 10112,-3197 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="4343" beginStationName="CX_YA" endPointId="0" endStationName="CX_DY" flowDrawDirect="1" flowShape="0" id="AC-110kV.yaoda_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10112,-3008 10112,-3061 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9192" ObjectName="AC-110kV.yaoda_line"/>
    <cge:TPSR_Ref TObjectID="9192_SS-26"/></metadata>
   <polyline fill="none" opacity="0" points="10112,-3008 10112,-3061 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="4343" beginStationName="CX_YA" endPointId="0" endStationName="CX_DY" flowDrawDirect="1" flowShape="0" id="AC-110kV.yaoda_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10112,-3061 10112,-3139 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9192" ObjectName="AC-110kV.yaoda_line"/>
    <cge:TPSR_Ref TObjectID="9192_SS-26"/></metadata>
   <polyline fill="none" opacity="0" points="10112,-3061 10112,-3139 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="12950" beginStationName="CX_XJH" endPointId="0" endStationName="CX_JS" flowDrawDirect="1" flowShape="0" id="AC-110kV.xiejinIhuui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11085,-1944 11251,-1944 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11554" ObjectName="AC-110kV.xiejinIhuui_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="11085,-1944 11251,-1944 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="12923" beginStationName="CX_LF" endPointId="0" endStationName="SysStation" flowDrawDirect="1" flowShape="0" id="AC-110kV.chumou_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10670,-2243 10457,-2627 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11851" ObjectName="AC-110kV.chumou_line"/>
    <cge:TPSR_Ref TObjectID="11851_SS-4"/></metadata>
   <polyline fill="none" opacity="0" points="10670,-2243 10457,-2627 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="5456" beginStationName="CX_LF" endPointId="0" endStationName="CX_DEG" flowDrawDirect="1" flowShape="0" id="AC-110kV.luyaode_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12668,-2460 12381,-2211 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11432" ObjectName="AC-110kV.luyaode_line"/>
    <cge:TPSR_Ref TObjectID="11432_SS-2"/></metadata>
   <polyline fill="none" opacity="0" points="12668,-2460 12381,-2211 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_LF" endPointId="0" endStationName="CX_QF" flowDrawDirect="1" flowShape="0" id="AC-110kV.luqianIIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11854,-1736 11854,-1922 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11493" ObjectName="AC-110kV.luqianIIhui_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="11854,-1736 11854,-1922 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="5597" beginStationName="CX_YZ" endPointId="0" endStationName="CX_XLTY" flowDrawDirect="1" flowShape="0" id="AC-220kV.yaoxinhui_1line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12690,-2536 12690,-2856 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11488" ObjectName="AC-220kV.yaoxinhui_1line"/>
    <cge:TPSR_Ref TObjectID="11488_SS-5"/></metadata>
   <polyline fill="none" opacity="0" points="12690,-2536 12690,-2856 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1642" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-110kV.shiyunIhui_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12253,-3913 12228,-3913 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9187" ObjectName="AC-110kV.shiyunIhui_line"/>
    <cge:TPSR_Ref TObjectID="9187_SS-35"/></metadata>
   <polyline fill="none" opacity="0" points="12253,-3913 12228,-3913 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="70431" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9371,-3589 9371,-3743 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="9371,-3589 9371,-3743 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="43449" beginStationName="CX_ZX" endPointId="0" endStationName="CX_FT" flowDrawDirect="1" flowShape="0" id="AC-110kV.zifeng_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9752,-1999 10084,-2446 " stroke-width="0.65591"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11563" ObjectName="AC-110kV.zifeng_line"/>
    <cge:TPSR_Ref TObjectID="11563_SS-7"/></metadata>
   <polyline fill="none" opacity="0" points="9752,-1999 10084,-2446 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="9585" beginStationName="CX_NH" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-110kV.zinanyunTnh_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9867,-2624 9965,-2560 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18267" ObjectName="AC-110kV.zinanyunTnh_line"/>
    <cge:TPSR_Ref TObjectID="18267_SS-7"/></metadata>
   <polyline fill="none" opacity="0" points="9867,-2624 9965,-2560 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="82732" beginStationName="CX_NH" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-110kV.zinanyunTyts_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9793,-2560 9964,-2560 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18268" ObjectName="AC-110kV.zinanyunTyts_line"/>
    <cge:TPSR_Ref TObjectID="18268_SS-2"/></metadata>
   <polyline fill="none" opacity="0" points="9793,-2560 9964,-2560 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="85134" beginStationName="PAS_T1" endPointId="0" endStationName="CX_ZHY" flowDrawDirect="0" flowShape="0" id="AC-110kV.yaohongzhiTzhy_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12954,-2161 12705,-2161 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19328" ObjectName="AC-110kV.yaohongzhiTzhy_line"/>
    <cge:TPSR_Ref TObjectID="19328_SS-136"/></metadata>
   <polyline fill="none" opacity="0" points="12954,-2161 12705,-2161 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YM" endPointId="0" endStationName="CX_LYS" flowDrawDirect="1" flowShape="0" id="AC-110kV.yuanlei_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11172,-3476 10877,-3304 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11551" ObjectName="AC-110kV.yuanlei_line"/>
    <cge:TPSR_Ref TObjectID="11551_SS-6"/></metadata>
   <polyline fill="none" opacity="0" points="11172,-3476 10877,-3304 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="88671" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9567,-2044 10076,-2448 " stroke-width="0.65591"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="9567,-2044 10076,-2448 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9195,-2469 8909,-2469 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="9195,-2469 8909,-2469 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12514,-3876 12279,-3618 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="12514,-3876 12279,-3618 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12531,-3873 12296,-3615 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="12531,-3873 12296,-3615 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="104523" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10080,-2467 9362,-2247 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10080,-2467 9362,-2247 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="105377" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9050,-2239 9287,-2239 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="9050,-2239 9287,-2239 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="52032" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12055,-2564 12317,-2829 12318,-2827 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="12055,-2564 12317,-2829 12318,-2827 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="48750" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12088,-2543 12336,-2791 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="12088,-2543 12336,-2791 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="1772" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="0" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12083,-1917 12415,-1764 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="12083,-1917 12415,-1764 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="77291" beginStationName="CX_HGY" endPointId="0" endStationName="CX_TZS" flowDrawDirect="1" flowShape="0" id="AC-35kV.tianzishan_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11684,-3268 11648,-3268 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18021" ObjectName="AC-35kV.tianzishan_line"/>
    <cge:TPSR_Ref TObjectID="18021_SS-23"/></metadata>
   <polyline fill="none" opacity="0" points="11684,-3268 11648,-3268 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="77291" beginStationName="CX_HGY" endPointId="0" endStationName="CX_TZS" flowDrawDirect="1" flowShape="0" id="AC-35kV.tianzishan_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11621,-3268 11476,-3268 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18021" ObjectName="AC-35kV.tianzishan_line"/>
    <cge:TPSR_Ref TObjectID="18021_SS-23"/></metadata>
   <polyline fill="none" opacity="0" points="11621,-3268 11476,-3268 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_LF" endPointId="0" endStationName="CX_QF" flowDrawDirect="1" flowShape="0" id="AC-110kV.luqinlao_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12475,-2051 12463,-2051 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9173" ObjectName="AC-110kV.luqinlao_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="12475,-2051 12463,-2051 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="90626" beginStationName="CX_LF" endPointId="0" endStationName="CX_QF" flowDrawDirect="1" flowShape="0" id="AC-110kV.luqinlao_line" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12436,-2051 12333,-2051 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9173" ObjectName="AC-110kV.luqinlao_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="12436,-2051 12333,-2051 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10081,-1282 9944,-1146 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10081,-1282 9944,-1146 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10194,-3481 10339,-3542 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10194,-3481 10339,-3542 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="0" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9589,-3798 10130,-3477 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="9589,-3798 10130,-3477 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10205,-3461 10807,-3281 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10205,-3461 10807,-3281 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11745,-3411 11472,-3292 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="11745,-3411 11472,-3292 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12686,-1761 12490,-1761 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="12686,-1761 12490,-1761 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_XN" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-110kV.ShuangEDaTDHS" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10489,-1432 10489,-1447 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18056" ObjectName="AC-110kV.ShuangEDaTDHS"/>
    <cge:TPSR_Ref TObjectID="18056_SS-119"/></metadata>
   <polyline fill="none" opacity="0" points="10489,-1432 10489,-1447 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10161,-3711 10161,-3493 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10161,-3711 10161,-3493 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10119,-3445 9725,-3449 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10119,-3445 9725,-3449 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10125,-3422 9721,-3422 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10125,-3422 9721,-3422 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12494,-3906 12101,-3780 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="12494,-3906 12101,-3780 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="12037,-2856 12037,-2564 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="12037,-2856 12037,-2564 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11262,-3255 11262,-3157 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="11262,-3255 11262,-3157 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11231,-3284 11231,-3157 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="11231,-3284 11231,-3157 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10091,-1598 9592,-1598 9592,-1420 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10091,-1598 9592,-1598 9592,-1420 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="11846,-3090 11862,-3090 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="11846,-3090 11862,-3090 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="11862,-3090 11886,-3090 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="11862,-3090 11886,-3090 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10843,-1970 10146,-2456 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="10843,-1970 10146,-2456 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="0" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layer4="0:0.000000 0.000000" layer5="220kV绾胯矾:0.000000 0.000000" layer6="110kV绾胯矾:0.000000 0.000000" layer7="$AUDIT_BAD_LAYER1:0.000000 0.000000" layerN="8" moveAndZoomFlag="1"/>
</svg>