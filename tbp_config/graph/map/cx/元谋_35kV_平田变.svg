<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-239" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-749 -1362 1917 1278">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="hydroGenerator:shape3">
    <polyline arcFlag="1" points="25,25 25,26 25,27 25,28 24,29 24,30 23,31 23,31 22,32 21,32 20,33 19,33 18,33 17,34 16,33 15,33 14,33 13,32 13,32 12,31 11,31 11,30 10,29 10,28 10,27 9,26 10,25 " stroke-width="1.14"/>
    <circle cx="24" cy="24" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="40,25 41,24 40,24 40,23 40,22 39,21 39,20 38,19 37,19 37,18 36,18 35,17 34,17 33,17 32,17 31,17 30,18 29,18 28,19 27,19 27,20 26,21 26,22 25,23 25,24 25,24 25,25 " stroke-width="1.14"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape6">
    <polyline DF8003:Layer="PUBLIC" points="16,51 0,51 9,26 16,50 16,51 "/>
    <polyline DF8003:Layer="PUBLIC" points="1,3 17,3 9,27 1,4 1,3 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.03226" x1="9" x2="9" y1="54" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.03226" x1="8" x2="8" y1="52" y2="52"/>
   </symbol>
   <symbol id="lightningRod:shape94">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="50" y2="37"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape30_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape22_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1.06452"/>
    <polyline points="58,100 64,100 " stroke-width="1.06452"/>
    <polyline points="64,100 64,93 " stroke-width="1.06452"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="30" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="30" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="38" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="38" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="62" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer2:shape22_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="voltageTransformer:shape54">
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="32" y2="23"/>
   </symbol>
   <symbol id="voltageTransformer:shape15">
    <ellipse cx="23" cy="24" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="2" y1="20" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="2" y1="26" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="14" y2="6"/>
    <rect height="13" stroke-width="1" width="7" x="4" y="14"/>
    <ellipse cx="34" cy="24" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="23" cy="35" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="34" cy="35" rx="7.5" ry="7" stroke-width="0.66594"/>
    <polyline points="24,36 8,36 8,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="3" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="21" x2="23" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="24" x2="24" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="26" x2="23" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="35" x2="35" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="32" x2="34" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="37" x2="34" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="34" x2="32" y1="27" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="35" x2="37" y1="27" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="37" x2="32" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="23" x2="23" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="20" x2="23" y1="26" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="26" x2="23" y1="26" y2="24"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1bd5c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bd6dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bd78a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bd8450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bd96d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bda1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bdadb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1bdb8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1bdcf00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1bdcf00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bde690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bde690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1be03f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1be03f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1be1440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1be2ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1be3970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1be4750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1be4ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1be67d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1be7270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1be7b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1be82f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1be90b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1be9a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bea520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1beaee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1bec510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1becf60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1bee100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1beed20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bf5470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bf5f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1bf78a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1bf01f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1288" width="1927" x="-754" y="-1367"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_193e670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -146.000000 129.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_175a2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -157.000000 114.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16ac000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -132.000000 99.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1968140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 382.000000 828.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1968b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 382.000000 843.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19694e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -192.000000 622.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1969d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -176.000000 606.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196a920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -184.000000 637.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196ae80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -184.000000 652.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196b100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -184.000000 666.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196b430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 739.000000 831.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196b690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 739.000000 846.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196df70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 393.000000 689.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196e230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 382.000000 674.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196e470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 407.000000 659.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196e890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 733.000000 681.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196eb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 722.000000 666.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196ed90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 747.000000 651.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196f1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 769.000000 1362.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196f470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 758.000000 1347.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196f6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 783.000000 1332.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18bfbe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 53.000000 129.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18bfdd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 42.000000 114.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18bff80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 67.000000 99.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c0310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 251.000000 129.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c04c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 240.000000 114.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c0670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 265.000000 99.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c0a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 496.000000 129.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c0bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 485.000000 114.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c0d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 510.000000 99.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c1130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 672.000000 129.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c13c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 661.000000 114.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c15d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 686.000000 99.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c19c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 846.000000 129.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c1c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 835.000000 114.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c1e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 860.000000 99.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c5a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -50.000000 1076.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c5ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -34.000000 1060.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c6110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -42.000000 1091.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c6350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -42.000000 1106.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c6590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -42.000000 1120.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c7200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 386.000000 959.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c7490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 375.000000 944.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c76d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 400.000000 929.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c80c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 726.000000 940.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c8350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 715.000000 925.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18c8590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 740.000000 910.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-199262">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -63.356070 -495.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30434" ObjectName="SW-YM_PT.YM_PT_0321SW"/>
     <cge:Meas_Ref ObjectId="199262"/>
    <cge:TPSR_Ref TObjectID="30434"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199263">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -63.356070 -364.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30435" ObjectName="SW-YM_PT.YM_PT_0326SW"/>
     <cge:Meas_Ref ObjectId="199263"/>
    <cge:TPSR_Ref TObjectID="30435"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199071">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 326.561370 -963.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30422" ObjectName="SW-YM_PT.YM_PT_3011SW"/>
     <cge:Meas_Ref ObjectId="199071"/>
    <cge:TPSR_Ref TObjectID="30422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199072">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 325.561370 -594.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30423" ObjectName="SW-YM_PT.YM_PT_0011SW"/>
     <cge:Meas_Ref ObjectId="199072"/>
    <cge:TPSR_Ref TObjectID="30423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199153">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 664.000000 -974.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30427" ObjectName="SW-YM_PT.YM_PT_3021SW"/>
     <cge:Meas_Ref ObjectId="199153"/>
    <cge:TPSR_Ref TObjectID="30427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199162">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 664.561370 -694.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30431" ObjectName="SW-YM_PT.YM_PT_0026SW"/>
     <cge:Meas_Ref ObjectId="199162"/>
    <cge:TPSR_Ref TObjectID="30431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199161">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 664.561370 -585.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30430" ObjectName="SW-YM_PT.YM_PT_0021SW"/>
     <cge:Meas_Ref ObjectId="199161"/>
    <cge:TPSR_Ref TObjectID="30430"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199154">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 714.819337 -962.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30428" ObjectName="SW-YM_PT.YM_PT_30217SW"/>
     <cge:Meas_Ref ObjectId="199154"/>
    <cge:TPSR_Ref TObjectID="30428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199260">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1063.229964 -485.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30432" ObjectName="SW-YM_PT.YM_PT_0901SW"/>
     <cge:Meas_Ref ObjectId="199260"/>
    <cge:TPSR_Ref TObjectID="30432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199069">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 121.000000 -967.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30420" ObjectName="SW-YM_PT.YM_PT_3100SW"/>
     <cge:Meas_Ref ObjectId="199069"/>
    <cge:TPSR_Ref TObjectID="30420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199066">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 242.000000 -1108.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30417" ObjectName="SW-YM_PT.YM_PT_3436SW"/>
     <cge:Meas_Ref ObjectId="199066"/>
    <cge:TPSR_Ref TObjectID="30417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199068">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 296.819337 -1071.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30419" ObjectName="SW-YM_PT.YM_PT_34360SW"/>
     <cge:Meas_Ref ObjectId="199068"/>
    <cge:TPSR_Ref TObjectID="30419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 162.544703 -1137.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199067">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 292.819337 -1210.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30418" ObjectName="SW-YM_PT.YM_PT_34367SW"/>
     <cge:Meas_Ref ObjectId="199067"/>
    <cge:TPSR_Ref TObjectID="30418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199074">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 325.561370 -697.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30425" ObjectName="SW-YM_PT.YM_PT_0016SW"/>
     <cge:Meas_Ref ObjectId="199074"/>
    <cge:TPSR_Ref TObjectID="30425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199023">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 825.000000 -1052.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30412" ObjectName="SW-YM_PT.YM_PT_3321SW"/>
     <cge:Meas_Ref ObjectId="199023"/>
    <cge:TPSR_Ref TObjectID="30412"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199025">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 825.000000 -1178.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30414" ObjectName="SW-YM_PT.YM_PT_3326SW"/>
     <cge:Meas_Ref ObjectId="199025"/>
    <cge:TPSR_Ref TObjectID="30414"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199024">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 859.819337 -1099.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30413" ObjectName="SW-YM_PT.YM_PT_33217SW"/>
     <cge:Meas_Ref ObjectId="199024"/>
    <cge:TPSR_Ref TObjectID="30413"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199026">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 859.819337 -1162.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30415" ObjectName="SW-YM_PT.YM_PT_33260SW"/>
     <cge:Meas_Ref ObjectId="199026"/>
    <cge:TPSR_Ref TObjectID="30415"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199027">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 862.819337 -1230.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30416" ObjectName="SW-YM_PT.YM_PT_33267SW"/>
     <cge:Meas_Ref ObjectId="199027"/>
    <cge:TPSR_Ref TObjectID="30416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199308">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 114.643930 -493.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30437" ObjectName="SW-YM_PT.YM_PT_0331SW"/>
     <cge:Meas_Ref ObjectId="199308"/>
    <cge:TPSR_Ref TObjectID="30437"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199309">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 114.643930 -362.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30438" ObjectName="SW-YM_PT.YM_PT_0336SW"/>
     <cge:Meas_Ref ObjectId="199309"/>
    <cge:TPSR_Ref TObjectID="30438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199354">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 311.643930 -492.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30440" ObjectName="SW-YM_PT.YM_PT_0341SW"/>
     <cge:Meas_Ref ObjectId="199354"/>
    <cge:TPSR_Ref TObjectID="30440"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199355">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 311.643930 -361.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30441" ObjectName="SW-YM_PT.YM_PT_0346SW"/>
     <cge:Meas_Ref ObjectId="199355"/>
    <cge:TPSR_Ref TObjectID="30441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199400">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 551.643930 -489.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30443" ObjectName="SW-YM_PT.YM_PT_0351SW"/>
     <cge:Meas_Ref ObjectId="199400"/>
    <cge:TPSR_Ref TObjectID="30443"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199401">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 551.643930 -358.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30444" ObjectName="SW-YM_PT.YM_PT_0356SW"/>
     <cge:Meas_Ref ObjectId="199401"/>
    <cge:TPSR_Ref TObjectID="30444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199446">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 726.643930 -490.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30446" ObjectName="SW-YM_PT.YM_PT_0361SW"/>
     <cge:Meas_Ref ObjectId="199446"/>
    <cge:TPSR_Ref TObjectID="30446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199447">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 726.643930 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30447" ObjectName="SW-YM_PT.YM_PT_0366SW"/>
     <cge:Meas_Ref ObjectId="199447"/>
    <cge:TPSR_Ref TObjectID="30447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199492">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.643930 -487.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30449" ObjectName="SW-YM_PT.YM_PT_0371SW"/>
     <cge:Meas_Ref ObjectId="199492"/>
    <cge:TPSR_Ref TObjectID="30449"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199493">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.643930 -356.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30450" ObjectName="SW-YM_PT.YM_PT_0376SW"/>
     <cge:Meas_Ref ObjectId="199493"/>
    <cge:TPSR_Ref TObjectID="30450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.888889 -0.000000 0.000000 -0.672727 374.376122 -204.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YM_PT.YM_PT_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-174,-575 1167,-575 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="30409" ObjectName="BS-YM_PT.YM_PT_9IM"/>
    <cge:TPSR_Ref TObjectID="30409"/></metadata>
   <polyline fill="none" opacity="0" points="-174,-575 1167,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YM_PT.YM_PT_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-31,-1036 1051,-1036 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="30408" ObjectName="BS-YM_PT.YM_PT_3IM"/>
    <cge:TPSR_Ref TObjectID="30408"/></metadata>
   <polyline fill="none" opacity="0" points="-31,-1036 1051,-1036 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YM_PT.032Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -63.356070 -197.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34169" ObjectName="EC-YM_PT.032Ld"/>
    <cge:TPSR_Ref TObjectID="34169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_PT.033Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 114.643930 -195.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34170" ObjectName="EC-YM_PT.033Ld"/>
    <cge:TPSR_Ref TObjectID="34170"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_PT.034Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 311.643930 -174.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34171" ObjectName="EC-YM_PT.034Ld"/>
    <cge:TPSR_Ref TObjectID="34171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_PT.035Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 551.643930 -174.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34172" ObjectName="EC-YM_PT.035Ld"/>
    <cge:TPSR_Ref TObjectID="34172"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_PT.036Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 726.643930 -192.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34173" ObjectName="EC-YM_PT.036Ld"/>
    <cge:TPSR_Ref TObjectID="34173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_PT.037Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.643930 -189.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34174" ObjectName="EC-YM_PT.037Ld"/>
    <cge:TPSR_Ref TObjectID="34174"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1a188a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.819337 -961.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a021f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 347.819337 -1070.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19e0f00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 343.819337 -1209.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19c4470" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 910.819337 -1098.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19dbce0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 909.819337 -1160.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1948520" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 912.819337 -1228.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_16a6dd0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -20.440918 -201.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1936b60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.703704 326.000000 -767.000000)" xlink:href="#lightningRod:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1937010">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.703704 664.000000 -769.000000)" xlink:href="#lightningRod:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19ca910">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 119.000000 -808.000000)" xlink:href="#lightningRod:shape94"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19a3fa0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 123.000000 -893.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a22180">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -59.000000 -280.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19e1c30">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 203.398535 -1110.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19c8610">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 900.000000 -848.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1949070">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 779.398535 -1193.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1935480">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 157.559082 -199.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1901060">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 119.000000 -278.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19c57d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 316.000000 -274.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18b7c10">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 594.559082 -270.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18b9000">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 556.000000 -274.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f5570">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 769.559082 -196.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18ede30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 731.000000 -275.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1958b70">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 946.559082 -193.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_195a000">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 908.000000 -272.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19989d0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 426.559082 -185.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1955e10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 382.000000 -692.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19064b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 719.000000 -694.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1889440">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -20.440918 -301.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17feeb0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 157.559082 -301.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1800b90">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 351.559082 -288.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1802f70">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 770.559082 -293.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1803f00">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 946.559082 -287.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1807900">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1063.000000 -428.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18c87d0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 595.559082 -197.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -618.000000 -1193.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217878" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -590.000000 -1016.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217878" ObjectName="YM_PT:YM_PT_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-219734" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -590.000000 -973.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219734" ObjectName="YM_PT:YM_PT_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217878" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -592.000000 -1101.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217878" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217878" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -592.000000 -1061.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217878" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-198953" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -132.000000 -664.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198953" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30409"/>
     <cge:Term_Ref ObjectID="43248"/>
    <cge:TPSR_Ref TObjectID="30409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-198954" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -132.000000 -664.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198954" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30409"/>
     <cge:Term_Ref ObjectID="43248"/>
    <cge:TPSR_Ref TObjectID="30409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-198955" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -132.000000 -664.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198955" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30409"/>
     <cge:Term_Ref ObjectID="43248"/>
    <cge:TPSR_Ref TObjectID="30409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-198956" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -132.000000 -664.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198956" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30409"/>
     <cge:Term_Ref ObjectID="43248"/>
    <cge:TPSR_Ref TObjectID="30409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-198960" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -132.000000 -664.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198960" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30409"/>
     <cge:Term_Ref ObjectID="43248"/>
    <cge:TPSR_Ref TObjectID="30409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-198915" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 822.000000 -1358.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198915" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30411"/>
     <cge:Term_Ref ObjectID="43251"/>
    <cge:TPSR_Ref TObjectID="30411"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-198916" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 822.000000 -1358.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198916" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30411"/>
     <cge:Term_Ref ObjectID="43251"/>
    <cge:TPSR_Ref TObjectID="30411"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-198912" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 822.000000 -1358.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198912" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30411"/>
     <cge:Term_Ref ObjectID="43251"/>
    <cge:TPSR_Ref TObjectID="30411"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-198926" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 447.000000 -687.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198926" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30424"/>
     <cge:Term_Ref ObjectID="43277"/>
    <cge:TPSR_Ref TObjectID="30424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-198927" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 447.000000 -687.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198927" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30424"/>
     <cge:Term_Ref ObjectID="43277"/>
    <cge:TPSR_Ref TObjectID="30424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-198923" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 447.000000 -687.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198923" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30424"/>
     <cge:Term_Ref ObjectID="43277"/>
    <cge:TPSR_Ref TObjectID="30424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-198940" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 790.000000 -679.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198940" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30429"/>
     <cge:Term_Ref ObjectID="43287"/>
    <cge:TPSR_Ref TObjectID="30429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-198941" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 790.000000 -679.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198941" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30429"/>
     <cge:Term_Ref ObjectID="43287"/>
    <cge:TPSR_Ref TObjectID="30429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-198937" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 790.000000 -679.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198937" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30429"/>
     <cge:Term_Ref ObjectID="43287"/>
    <cge:TPSR_Ref TObjectID="30429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-198964" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -85.000000 -130.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198964" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30433"/>
     <cge:Term_Ref ObjectID="43295"/>
    <cge:TPSR_Ref TObjectID="30433"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-198965" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -85.000000 -130.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198965" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30433"/>
     <cge:Term_Ref ObjectID="43295"/>
    <cge:TPSR_Ref TObjectID="30433"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-198961" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -85.000000 -130.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198961" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30433"/>
     <cge:Term_Ref ObjectID="43295"/>
    <cge:TPSR_Ref TObjectID="30433"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-198970" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 112.000000 -129.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198970" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30436"/>
     <cge:Term_Ref ObjectID="43301"/>
    <cge:TPSR_Ref TObjectID="30436"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-198971" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 112.000000 -129.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198971" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30436"/>
     <cge:Term_Ref ObjectID="43301"/>
    <cge:TPSR_Ref TObjectID="30436"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-198967" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 112.000000 -129.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198967" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30436"/>
     <cge:Term_Ref ObjectID="43301"/>
    <cge:TPSR_Ref TObjectID="30436"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-198976" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 312.000000 -129.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198976" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30439"/>
     <cge:Term_Ref ObjectID="43307"/>
    <cge:TPSR_Ref TObjectID="30439"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-198977" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 312.000000 -129.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198977" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30439"/>
     <cge:Term_Ref ObjectID="43307"/>
    <cge:TPSR_Ref TObjectID="30439"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-198973" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 312.000000 -129.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198973" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30439"/>
     <cge:Term_Ref ObjectID="43307"/>
    <cge:TPSR_Ref TObjectID="30439"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-198982" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 553.000000 -129.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198982" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30442"/>
     <cge:Term_Ref ObjectID="43313"/>
    <cge:TPSR_Ref TObjectID="30442"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-198983" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 553.000000 -129.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198983" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30442"/>
     <cge:Term_Ref ObjectID="43313"/>
    <cge:TPSR_Ref TObjectID="30442"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-198979" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 553.000000 -129.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198979" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30442"/>
     <cge:Term_Ref ObjectID="43313"/>
    <cge:TPSR_Ref TObjectID="30442"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-198988" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 728.000000 -129.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198988" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30445"/>
     <cge:Term_Ref ObjectID="43319"/>
    <cge:TPSR_Ref TObjectID="30445"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-198989" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 728.000000 -129.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198989" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30445"/>
     <cge:Term_Ref ObjectID="43319"/>
    <cge:TPSR_Ref TObjectID="30445"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-198985" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 728.000000 -129.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198985" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30445"/>
     <cge:Term_Ref ObjectID="43319"/>
    <cge:TPSR_Ref TObjectID="30445"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-198994" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 901.000000 -129.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198994" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30448"/>
     <cge:Term_Ref ObjectID="43325"/>
    <cge:TPSR_Ref TObjectID="30448"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-198995" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 901.000000 -129.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198995" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30448"/>
     <cge:Term_Ref ObjectID="43325"/>
    <cge:TPSR_Ref TObjectID="30448"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-198991" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 901.000000 -129.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198991" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30448"/>
     <cge:Term_Ref ObjectID="43325"/>
    <cge:TPSR_Ref TObjectID="30448"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-198930" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 453.000000 -839.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198930" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30451"/>
     <cge:Term_Ref ObjectID="43331"/>
    <cge:TPSR_Ref TObjectID="30451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-198929" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 453.000000 -839.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198929" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30451"/>
     <cge:Term_Ref ObjectID="43331"/>
    <cge:TPSR_Ref TObjectID="30451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-198944" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 811.000000 -844.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198944" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30452"/>
     <cge:Term_Ref ObjectID="43335"/>
    <cge:TPSR_Ref TObjectID="30452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-198943" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 811.000000 -844.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198943" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30452"/>
     <cge:Term_Ref ObjectID="43335"/>
    <cge:TPSR_Ref TObjectID="30452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-198945" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 11.000000 -1120.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198945" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30408"/>
     <cge:Term_Ref ObjectID="43247"/>
    <cge:TPSR_Ref TObjectID="30408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-198946" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 11.000000 -1120.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198946" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30408"/>
     <cge:Term_Ref ObjectID="43247"/>
    <cge:TPSR_Ref TObjectID="30408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-198947" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 11.000000 -1120.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198947" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30408"/>
     <cge:Term_Ref ObjectID="43247"/>
    <cge:TPSR_Ref TObjectID="30408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-198948" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 11.000000 -1120.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198948" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30408"/>
     <cge:Term_Ref ObjectID="43247"/>
    <cge:TPSR_Ref TObjectID="30408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-198952" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 11.000000 -1120.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198952" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30408"/>
     <cge:Term_Ref ObjectID="43247"/>
    <cge:TPSR_Ref TObjectID="30408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-198920" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 445.000000 -958.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198920" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30421"/>
     <cge:Term_Ref ObjectID="43271"/>
    <cge:TPSR_Ref TObjectID="30421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-198921" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 445.000000 -958.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198921" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30421"/>
     <cge:Term_Ref ObjectID="43271"/>
    <cge:TPSR_Ref TObjectID="30421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-199587" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 445.000000 -958.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="199587" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30421"/>
     <cge:Term_Ref ObjectID="43271"/>
    <cge:TPSR_Ref TObjectID="30421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-198934" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 784.000000 -940.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198934" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30426"/>
     <cge:Term_Ref ObjectID="43281"/>
    <cge:TPSR_Ref TObjectID="30426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-198935" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 784.000000 -940.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198935" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30426"/>
     <cge:Term_Ref ObjectID="43281"/>
    <cge:TPSR_Ref TObjectID="30426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-198931" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 784.000000 -940.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198931" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30426"/>
     <cge:Term_Ref ObjectID="43281"/>
    <cge:TPSR_Ref TObjectID="30426"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-607" y="-1250"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-607" y="-1250"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-655" y="-1269"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-438" y="-1238"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-438" y="-1238"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-438" y="-1277"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-438" y="-1277"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="843" y="-1146"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="843" y="-1146"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="366" y="-867"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="366" y="-867"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="701" y="-871"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="701" y="-871"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="-46" y="-460"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="-46" y="-460"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="134" y="-458"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="134" y="-458"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="330" y="-452"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="330" y="-452"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="570" y="-450"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="570" y="-450"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="745" y="-451"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="745" y="-451"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="922" y="-448"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="922" y="-448"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="29" qtmmishow="hidden" width="92" x="-706" y="-882"/>
    </a>
   <metadata/><rect fill="white" height="29" opacity="0" stroke="white" transform="" width="92" x="-706" y="-882"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="-299,-1262 -302,-1265 -302,-1208 -299,-1211 -299,-1262" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="-299,-1262 -302,-1265 -239,-1265 -242,-1262 -299,-1262" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="-299,-1211 -302,-1208 -239,-1208 -242,-1211 -299,-1211" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="-242,-1262 -239,-1265 -239,-1208 -242,-1211 -242,-1262" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="51" stroke="rgb(255,255,255)" width="57" x="-299" y="-1262"/>
     <rect fill="none" height="51" qtmmishow="hidden" stroke="rgb(0,0,0)" width="57" x="-299" y="-1262"/>
    </a>
   <metadata/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-607" y="-1250"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/></g>
   <g href="cx_配调_配网接线图35_元谋.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-438" y="-1238"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-438" y="-1277"/></g>
   <g href="35kV平田变35kV平新线332断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="843" y="-1146"/></g>
   <g href="35kV平田变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="366" y="-867"/></g>
   <g href="35kV平田变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="701" y="-871"/></g>
   <g href="35kV平田变10kV平田线032断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="-46" y="-460"/></g>
   <g href="35kV平田变10kV班恺线033断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="134" y="-458"/></g>
   <g href="35kV平田变10kV新华线034断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="330" y="-452"/></g>
   <g href="35kV平田变10kV已保线035断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="570" y="-450"/></g>
   <g href="35kV平田变10kV华竹线036断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="745" y="-451"/></g>
   <g href="35kV平田变10kV普登线037断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="922" y="-448"/></g>
   <g href="35kV平田变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="29" qtmmishow="hidden" width="92" x="-706" y="-882"/></g>
   <g href="AVC平田站.svg" style="fill-opacity:0"><rect height="51" qtmmishow="hidden" stroke="rgb(0,0,0)" width="57" x="-299" y="-1262"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-199261">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -64.905089 -432.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30433" ObjectName="SW-YM_PT.YM_PT_032BK"/>
     <cge:Meas_Ref ObjectId="199261"/>
    <cge:TPSR_Ref TObjectID="30433"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199070">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 325.561370 -908.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30421" ObjectName="SW-YM_PT.YM_PT_301BK"/>
     <cge:Meas_Ref ObjectId="199070"/>
    <cge:TPSR_Ref TObjectID="30421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199073">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 325.561370 -648.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30424" ObjectName="SW-YM_PT.YM_PT_001BK"/>
     <cge:Meas_Ref ObjectId="199073"/>
    <cge:TPSR_Ref TObjectID="30424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199152">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 664.000000 -917.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30426" ObjectName="SW-YM_PT.YM_PT_302BK"/>
     <cge:Meas_Ref ObjectId="199152"/>
    <cge:TPSR_Ref TObjectID="30426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199160">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 664.561370 -638.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30429" ObjectName="SW-YM_PT.YM_PT_002BK"/>
     <cge:Meas_Ref ObjectId="199160"/>
    <cge:TPSR_Ref TObjectID="30429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199022">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 825.000000 -1117.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30411" ObjectName="SW-YM_PT.YM_PT_332BK"/>
     <cge:Meas_Ref ObjectId="199022"/>
    <cge:TPSR_Ref TObjectID="30411"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199307">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 115.094911 -429.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30436" ObjectName="SW-YM_PT.YM_PT_033BK"/>
     <cge:Meas_Ref ObjectId="199307"/>
    <cge:TPSR_Ref TObjectID="30436"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199353">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 312.094911 -426.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30439" ObjectName="SW-YM_PT.YM_PT_034BK"/>
     <cge:Meas_Ref ObjectId="199353"/>
    <cge:TPSR_Ref TObjectID="30439"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199399">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 552.094911 -421.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30442" ObjectName="SW-YM_PT.YM_PT_035BK"/>
     <cge:Meas_Ref ObjectId="199399"/>
    <cge:TPSR_Ref TObjectID="30442"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199445">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 727.094911 -422.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30445" ObjectName="SW-YM_PT.YM_PT_036BK"/>
     <cge:Meas_Ref ObjectId="199445"/>
    <cge:TPSR_Ref TObjectID="30445"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199491">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 904.094911 -419.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30448" ObjectName="SW-YM_PT.YM_PT_037BK"/>
     <cge:Meas_Ref ObjectId="199491"/>
    <cge:TPSR_Ref TObjectID="30448"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SF" endPointId="0" endStationName="YM_PT" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_ShaoPing" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="251,-1294 251,-1260 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34175" ObjectName="AC-35kV.LN_ShaoPing"/>
    <cge:TPSR_Ref TObjectID="34175_SS-239"/></metadata>
   <polyline fill="none" opacity="0" points="251,-1294 251,-1260 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YM_PT" endPointId="0" endStationName="YM_XH" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_PingXin" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="834,-1263 834,-1295 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34176" ObjectName="AC-35kV.LN_PingXin"/>
    <cge:TPSR_Ref TObjectID="34176_SS-239"/></metadata>
   <polyline fill="none" opacity="0" points="834,-1263 834,-1295 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-10KV" transform="matrix(0.571429 -0.000000 0.000000 -0.648148 251.000000 -165.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="30408" cx="336" cy="-1036" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30409" cx="674" cy="-575" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30408" cx="673" cy="-1036" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30408" cx="130" cy="-1036" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30409" cx="335" cy="-575" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30408" cx="907" cy="-1036" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30408" cx="251" cy="-1036" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30408" cx="834" cy="-1036" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30409" cx="-54" cy="-575" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30409" cx="124" cy="-575" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30409" cx="1072" cy="-575" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30409" cx="913" cy="-575" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30409" cx="736" cy="-575" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30409" cx="561" cy="-575" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30409" cx="321" cy="-575" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(64,64,64)" font-family="SimHei" font-size="20" graphid="g_1663ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -567.000000 -1239.500000) translate(0,16)">平田变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16185d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16185d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16185d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16185d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16185d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16185d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16185d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16185d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16185d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16185d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16185d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16185d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16185d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16185d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16185d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16185d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16185d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16185d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1678ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1037.000000 -371.000000) translate(0,12)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1963cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1071.000000 -607.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a22a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 85.000000 -797.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b9d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 168.000000 -1256.000000) translate(0,12)">35kV哨平线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a7440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 217.000000 -167.000000) translate(0,12)">湾腰树电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a8420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 363.000000 -169.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a8980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 704.000000 -1260.000000) translate(0,12)">35kV平新线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19aaa10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 131.000000 -523.000000) translate(0,12)">0331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19113c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 131.000000 -392.000000) translate(0,12)">0336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1911720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 133.000000 -458.000000) translate(0,12)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1911b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 568.000000 -519.000000) translate(0,12)">0351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1911d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 568.000000 -388.000000) translate(0,12)">0356</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1911fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 570.000000 -450.000000) translate(0,12)">035</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1912210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 743.000000 -520.000000) translate(0,12)">0361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1912450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 743.000000 -389.000000) translate(0,12)">0366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1912690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 745.000000 -451.000000) translate(0,12)">036</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19128d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1079.000000 -515.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1912b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -47.000000 -525.000000) translate(0,12)">0321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1912d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -47.000000 -394.000000) translate(0,12)">0326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1912f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -47.000000 -461.000000) translate(0,12)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19131d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 920.000000 -517.000000) translate(0,12)">0371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19136f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 920.000000 -386.000000) translate(0,12)">0376</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1913970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 922.000000 -448.000000) translate(0,12)">037</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1913bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 328.000000 -519.000000) translate(0,12)">0341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1913df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 328.000000 -388.000000) translate(0,12)">0346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1914030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 330.000000 -452.000000) translate(0,12)">034</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1914270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 342.000000 -624.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19144b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 342.000000 -727.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19146f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 344.000000 -677.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1914930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 343.000000 -993.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1914b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 344.000000 -937.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1914db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 702.000000 -871.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19152e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 681.000000 -615.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1915560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 681.000000 -724.000000) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19157a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 683.000000 -667.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19159e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 718.000000 -993.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1915c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 680.000000 -1004.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1915e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 682.000000 -946.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1966790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 990.000000 -1057.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19669d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 875.000000 -1130.000000) translate(0,12)">33217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1966c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 841.000000 -1082.000000) translate(0,12)">3321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1966e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 874.000000 -1193.000000) translate(0,12)">33260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1967090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 877.000000 -1261.000000) translate(0,12)">33267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19672d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 841.000000 -1208.000000) translate(0,12)">3326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1967510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 843.000000 -1146.000000) translate(0,12)">332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1967750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 300.000000 -1102.000000) translate(0,12)">34360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1967990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 296.000000 -1241.000000) translate(0,12)">34367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1967bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 258.000000 -1138.000000) translate(0,12)">3436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1967e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 137.000000 -997.000000) translate(0,12)">3100</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1973650" transform="matrix(1.697608 -0.000000 -0.000000 1.350309 -748.881548 -614.305556) translate(0,13)">1、本站主变调档机构异常，</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1973650" transform="matrix(1.697608 -0.000000 -0.000000 1.350309 -748.881548 -614.305556) translate(0,29)">不能进行调档操作（遥调）。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1908940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 366.000000 -867.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1908f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 169.000000 -904.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1908f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 169.000000 -904.000000) translate(0,27)">SZ11-3150/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1908f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 169.000000 -904.000000) translate(0,42)">35000/10500V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1908f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 169.000000 -904.000000) translate(0,57)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_190b8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 508.000000 -907.000000) translate(0,12)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_190b8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 508.000000 -907.000000) translate(0,27)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_190b8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 508.000000 -907.000000) translate(0,42)">35000/10500V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_190b8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 508.000000 -907.000000) translate(0,57)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_190bb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_190bb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_190bb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_190bb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_190bb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_190bb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_190bb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_190bb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_190bb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_19c14f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -427.000000 -1231.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_19ba940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -427.000000 -1268.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_1944160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -707.000000 -880.000000) translate(0,20)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_18d2900" transform="matrix(1.697608 -0.000000 -0.000000 1.350309 -747.881548 -544.305556) translate(0,13)">2、35kV母线无母线TV，无法采集电压，</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_18d2900" transform="matrix(1.697608 -0.000000 -0.000000 1.350309 -747.881548 -544.305556) translate(0,29)">高侧P、Q值均错误，需参看低压侧。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_18d2900" transform="matrix(1.697608 -0.000000 -0.000000 1.350309 -747.881548 -544.305556) translate(0,45)">（经与原元谋县调核实，因场地受限，</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_18d2900" transform="matrix(1.697608 -0.000000 -0.000000 1.350309 -747.881548 -544.305556) translate(0,61)">无法加装TV）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_18bb230" transform="matrix(1.697608 -0.000000 -0.000000 1.350309 -742.881548 -440.305556) translate(0,13)">3、1号主变温度现场上送为0。(已处理）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18bd9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -90.000000 -152.000000) translate(0,12)">10kV平田线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18bdea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 90.000000 -152.000000) translate(0,12)">10kV班恺线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18be720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 284.000000 -152.000000) translate(0,12)">10kV新华线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18bec80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 701.000000 -152.000000) translate(0,12)">10kV华竹线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18bf350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 881.000000 -152.000000) translate(0,12)">10kV普登线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_18c2070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -737.000000 -263.500000) translate(0,17)">元谋巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_19b6510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -580.000000 -273.000000) translate(0,16)">18787879021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_19b6510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -580.000000 -273.000000) translate(0,36)">13908784331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18c4ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -580.000000 -308.000000) translate(0,16)">8314047</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_18c4e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -293.000000 -1245.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18f11c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 527.000000 -159.000000) translate(0,12)">10kV己保线</text>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="673" cy="-787" fill="none" fillStyle="0" r="4.5" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="335" cy="-785" fill="none" fillStyle="0" r="4.5" stroke="rgb(0,255,0)" stroke-width="1"/>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YM_PT.YM_PT_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="43337"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.939394 -0.000000 0.000000 -0.980392 637.587954 -814.000000)" xlink:href="#transformer2:shape22_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.939394 -0.000000 0.000000 -0.980392 637.587954 -814.000000)" xlink:href="#transformer2:shape22_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="30452" ObjectName="TF-YM_PT.YM_PT_2T"/>
    <cge:TPSR_Ref TObjectID="30452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.740741 -0.000000 0.000000 -0.423077 368.351561 -177.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.740741 -0.000000 0.000000 -0.423077 368.351561 -177.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YM_PT.YM_PT_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="43333"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.939394 -0.000000 0.000000 -0.980392 299.587954 -808.000000)" xlink:href="#transformer2:shape22_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.939394 -0.000000 0.000000 -0.980392 299.587954 -808.000000)" xlink:href="#transformer2:shape22_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="30451" ObjectName="TF-YM_PT.YM_PT_1T"/>
    <cge:TPSR_Ref TObjectID="30451"/></metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-154236" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -426.000000 -1146.000000)" xlink:href="#dynamicPoint:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26170" ObjectName="DYN-YM_PT"/>
     <cge:Meas_Ref ObjectId="154236"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1a03180">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 158.544703 -1085.000000)" xlink:href="#voltageTransformer:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1804e90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1049.000000 -371.000000)" xlink:href="#voltageTransformer:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_1a4e8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-54,-500 -54,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="30434@0" ObjectIDZND0="30433@1" Pin0InfoVect0LinkObjId="SW-199261_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199262_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-54,-500 -54,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19481f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-54,-440 -54,-405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="30433@0" ObjectIDZND0="30435@1" Pin0InfoVect0LinkObjId="SW-199263_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199261_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-54,-440 -54,-405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19ffee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="335,-656 335,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="30424@0" ObjectIDZND0="30423@1" Pin0InfoVect0LinkObjId="SW-199072_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199073_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="335,-656 335,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a000d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-1004 336,-1036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30422@1" ObjectIDZND0="30408@0" Pin0InfoVect0LinkObjId="g_19c9180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199071_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="336,-1004 336,-1036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a002c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="335,-599 335,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30423@0" ObjectIDZND0="30409@0" Pin0InfoVect0LinkObjId="g_19b9b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199072_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="335,-599 335,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a01c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-1036 673,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="30408@0" ObjectIDZND0="30427@1" Pin0InfoVect0LinkObjId="SW-199153_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a000d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,-1036 673,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f0860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-968 336,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="30422@0" ObjectIDZND0="30421@1" Pin0InfoVect0LinkObjId="SW-199070_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199071_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="336,-968 336,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1936970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-925 673,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="30426@0" ObjectIDZND0="30452@0" Pin0InfoVect0LinkObjId="g_19559b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199152_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,-925 673,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19b7cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="674,-699 674,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="30431@0" ObjectIDZND0="30429@1" Pin0InfoVect0LinkObjId="SW-199160_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199162_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="674,-699 674,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19b9970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="335,-916 335,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="30421@0" ObjectIDZND0="30451@0" Pin0InfoVect0LinkObjId="g_1906250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="335,-916 335,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19b9b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="674,-590 674,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30430@0" ObjectIDZND0="30409@0" Pin0InfoVect0LinkObjId="g_1a002c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199161_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="674,-590 674,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a168c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="674,-626 674,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="30430@1" ObjectIDZND0="30429@0" Pin0InfoVect0LinkObjId="SW-199160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199161_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="674,-626 674,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19644b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="756,-967 770,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="30428@0" ObjectIDZND0="g_1a188a0@0" Pin0InfoVect0LinkObjId="g_1a188a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199154_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="756,-967 770,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1964d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="720,-967 673,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="30428@1" ObjectIDZND0="30427@1" ObjectIDZND1="30426@x" Pin0InfoVect0LinkObjId="SW-199153_1" Pin0InfoVect1LinkObjId="SW-199152_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199154_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="720,-967 673,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19654e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-979 673,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="30427@0" ObjectIDZND0="30428@x" ObjectIDZND1="30426@x" Pin0InfoVect0LinkObjId="SW-199154_0" Pin0InfoVect1LinkObjId="SW-199152_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199153_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="673,-979 673,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19656d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-967 673,-952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="30428@x" ObjectIDND1="30427@1" ObjectIDZND0="30426@1" Pin0InfoVect0LinkObjId="SW-199152_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-199154_0" Pin1InfoVect1LinkObjId="SW-199153_1" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,-967 673,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19ca720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1072,-490 1072,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="30432@0" ObjectIDZND0="g_1807900@0" Pin0InfoVect0LinkObjId="g_1807900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1072,-490 1072,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19a3db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="130,-1036 130,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="30408@0" ObjectIDZND0="30420@1" Pin0InfoVect0LinkObjId="SW-199069_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a000d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="130,-1036 130,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19a4610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="130,-898 130,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_19a3fa0@0" ObjectIDZND0="g_19ca910@0" Pin0InfoVect0LinkObjId="g_19ca910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19a3fa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="130,-898 130,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a22890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-54,-536 -54,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30434@1" ObjectIDZND0="30409@0" Pin0InfoVect0LinkObjId="g_1a002c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199262_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-54,-536 -54,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a02b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="338,-1076 352,-1076 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="30419@0" ObjectIDZND0="g_1a021f0@0" Pin0InfoVect0LinkObjId="g_1a021f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199068_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="338,-1076 352,-1076 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a02d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="175,-1153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="175,-1153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a02f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="168,-1142 168,-1116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_1a03180@0" Pin0InfoVect0LinkObjId="g_1a03180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="168,-1142 168,-1116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19e17f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="334,-1215 348,-1215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="30418@0" ObjectIDZND0="g_19e0f00@0" Pin0InfoVect0LinkObjId="g_19e0f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199067_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="334,-1215 348,-1215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19e1a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="298,-1215 251,-1215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="30418@1" ObjectIDZND0="0@x" ObjectIDZND1="g_19e1c30@0" ObjectIDZND2="30417@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_19e1c30_0" Pin0InfoVect2LinkObjId="SW-199066_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199067_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="298,-1215 251,-1215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19e2ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="251,-1215 251,-1261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="30418@x" ObjectIDND1="0@x" ObjectIDND2="g_19e1c30@0" ObjectIDZND0="34175@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-199067_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_19e1c30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="251,-1215 251,-1261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19e3110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="168,-1187 250,-1187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="30418@x" ObjectIDZND1="34175@1" ObjectIDZND2="g_19e1c30@0" Pin0InfoVect0LinkObjId="SW-199067_0" Pin0InfoVect1LinkObjId="g_19e2ef0_1" Pin0InfoVect2LinkObjId="g_19e1c30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="168,-1187 250,-1187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_198abc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="251,-1187 251,-1215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="0@x" ObjectIDND1="g_19e1c30@0" ObjectIDND2="30417@x" ObjectIDZND0="30418@x" ObjectIDZND1="34175@1" Pin0InfoVect0LinkObjId="SW-199067_0" Pin0InfoVect1LinkObjId="g_19e2ef0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_19e1c30_0" Pin1InfoVect2LinkObjId="SW-199066_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="251,-1187 251,-1215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_198ade0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="210,-1164 251,-1164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_19e1c30@0" ObjectIDZND0="30417@x" ObjectIDZND1="0@x" ObjectIDZND2="30418@x" Pin0InfoVect0LinkObjId="SW-199066_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-199067_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19e1c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="210,-1164 251,-1164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_198b750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="251,-1149 251,-1164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="30417@1" ObjectIDZND0="g_19e1c30@0" ObjectIDZND1="0@x" ObjectIDZND2="30418@x" Pin0InfoVect0LinkObjId="g_19e1c30_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-199067_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199066_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="251,-1149 251,-1164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_198b970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="251,-1164 251,-1187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_19e1c30@0" ObjectIDND1="30417@x" ObjectIDZND0="0@x" ObjectIDZND1="30418@x" ObjectIDZND2="34175@1" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-199067_0" Pin0InfoVect2LinkObjId="g_19e2ef0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="g_19e1c30_0" Pin1InfoVect1LinkObjId="SW-199066_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="251,-1164 251,-1187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19c81d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="335,-702 335,-683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="30425@0" ObjectIDZND0="30424@1" Pin0InfoVect0LinkObjId="SW-199073_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199074_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="335,-702 335,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19c83f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="130,-972 130,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="30420@0" ObjectIDZND0="g_19a3fa0@1" Pin0InfoVect0LinkObjId="g_19a3fa0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199069_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="130,-972 130,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19c9180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="907,-902 907,-1036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_19c8610@0" ObjectIDZND0="30408@0" Pin0InfoVect0LinkObjId="g_1a000d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19c8610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="907,-902 907,-1036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19c9950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="251,-1076 251,-1036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="30419@x" ObjectIDND1="30417@x" ObjectIDZND0="30408@0" Pin0InfoVect0LinkObjId="g_1a000d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-199068_0" Pin1InfoVect1LinkObjId="SW-199066_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="251,-1076 251,-1036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ca3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="302,-1076 251,-1076 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="30419@1" ObjectIDZND0="30417@x" ObjectIDZND1="30408@0" Pin0InfoVect0LinkObjId="SW-199066_0" Pin0InfoVect1LinkObjId="g_1a000d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199068_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="302,-1076 251,-1076 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18d5930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="251,-1076 251,-1113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="30419@x" ObjectIDND1="30408@0" ObjectIDZND0="30417@0" Pin0InfoVect0LinkObjId="SW-199066_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-199068_0" Pin1InfoVect1LinkObjId="g_1a000d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="251,-1076 251,-1113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19b19d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-1036 834,-1057 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="30408@0" ObjectIDZND0="30412@0" Pin0InfoVect0LinkObjId="SW-199023_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a000d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="834,-1036 834,-1057 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a09c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="901,-1104 915,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="30413@0" ObjectIDZND0="g_19c4470@0" Pin0InfoVect0LinkObjId="g_19c4470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199024_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="901,-1104 915,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a09e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="865,-1104 836,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="30413@1" ObjectIDZND0="30412@x" ObjectIDZND1="30411@x" Pin0InfoVect0LinkObjId="SW-199023_0" Pin0InfoVect1LinkObjId="SW-199022_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199024_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="865,-1104 836,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19dc610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="901,-1167 914,-1166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="30415@0" ObjectIDZND0="g_19dbce0@0" Pin0InfoVect0LinkObjId="g_19dbce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199026_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="901,-1167 914,-1166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19dd0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-1093 834,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="30412@1" ObjectIDZND0="30413@x" ObjectIDZND1="30411@x" Pin0InfoVect0LinkObjId="SW-199024_0" Pin0InfoVect1LinkObjId="SW-199022_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199023_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="834,-1093 834,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19dd2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-1104 834,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="30413@x" ObjectIDND1="30412@x" ObjectIDZND0="30411@0" Pin0InfoVect0LinkObjId="SW-199022_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-199024_0" Pin1InfoVect1LinkObjId="SW-199023_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="834,-1104 834,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19dd4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="865,-1167 835,-1167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="30415@1" ObjectIDZND0="30414@x" ObjectIDZND1="30411@x" Pin0InfoVect0LinkObjId="SW-199025_0" Pin0InfoVect1LinkObjId="SW-199022_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199026_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="865,-1167 835,-1167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ddf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-1183 834,-1167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="30414@0" ObjectIDZND0="30415@x" ObjectIDZND1="30411@x" Pin0InfoVect0LinkObjId="SW-199026_0" Pin0InfoVect1LinkObjId="SW-199022_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199025_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="834,-1183 834,-1167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19de190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-1167 834,-1150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="30415@x" ObjectIDND1="30414@x" ObjectIDZND0="30411@1" Pin0InfoVect0LinkObjId="SW-199022_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-199026_0" Pin1InfoVect1LinkObjId="SW-199025_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="834,-1167 834,-1150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1948e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-1235 917,-1234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="30416@0" ObjectIDZND0="g_1948520@0" Pin0InfoVect0LinkObjId="g_1948520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199027_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="904,-1235 917,-1234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1949be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="786,-1247 834,-1247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_1949070@0" ObjectIDZND0="30416@x" ObjectIDZND1="30414@x" ObjectIDZND2="34176@1" Pin0InfoVect0LinkObjId="SW-199027_0" Pin0InfoVect1LinkObjId="SW-199025_0" Pin0InfoVect2LinkObjId="g_194a800_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1949070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="786,-1247 834,-1247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1949e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-1247 834,-1232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1949070@0" ObjectIDND1="34176@1" ObjectIDZND0="30416@x" ObjectIDZND1="30414@x" Pin0InfoVect0LinkObjId="SW-199027_0" Pin0InfoVect1LinkObjId="SW-199025_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1949070_0" Pin1InfoVect1LinkObjId="g_194a800_1" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="834,-1247 834,-1232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_194a800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-1247 834,-1265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_1949070@0" ObjectIDND1="30416@x" ObjectIDND2="30414@x" ObjectIDZND0="34176@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1949070_0" Pin1InfoVect1LinkObjId="SW-199027_0" Pin1InfoVect2LinkObjId="SW-199025_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="834,-1247 834,-1265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_194b290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="868,-1235 834,-1235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="30416@1" ObjectIDZND0="g_1949070@0" ObjectIDZND1="34176@1" ObjectIDZND2="30414@x" Pin0InfoVect0LinkObjId="g_1949070_0" Pin0InfoVect1LinkObjId="g_194a800_1" Pin0InfoVect2LinkObjId="SW-199025_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199027_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="868,-1235 834,-1235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a0f9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-1235 834,-1219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1949070@0" ObjectIDND1="34176@1" ObjectIDND2="30416@x" ObjectIDZND0="30414@1" Pin0InfoVect0LinkObjId="SW-199025_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1949070_0" Pin1InfoVect1LinkObjId="g_194a800_1" Pin1InfoVect2LinkObjId="SW-199027_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="834,-1235 834,-1219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a10440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-13,-255 -54,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="g_16a6dd0@0" ObjectIDZND0="34169@x" ObjectIDZND1="g_1a22180@0" Pin0InfoVect0LinkObjId="EC-YM_PT.032Ld_0" Pin0InfoVect1LinkObjId="g_1a22180_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16a6dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-13,-255 -54,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a10660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-54,-255 -54,-224 -52,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="g_16a6dd0@0" ObjectIDND1="g_1a22180@0" ObjectIDZND0="34169@0" Pin0InfoVect0LinkObjId="EC-YM_PT.032Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_16a6dd0_0" Pin1InfoVect1LinkObjId="g_1a22180_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-54,-255 -54,-224 -52,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a10880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-54,-255 -54,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="g_16a6dd0@0" ObjectIDND1="34169@x" ObjectIDZND0="g_1a22180@0" Pin0InfoVect0LinkObjId="g_1a22180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_16a6dd0_0" Pin1InfoVect1LinkObjId="EC-YM_PT.032Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-54,-255 -54,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1932eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="124,-498 124,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="30437@0" ObjectIDZND0="30436@1" Pin0InfoVect0LinkObjId="SW-199307_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199308_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="124,-498 124,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1935260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="124,-437 124,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="30436@0" ObjectIDZND0="30438@1" Pin0InfoVect0LinkObjId="SW-199309_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199307_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="124,-437 124,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1901980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="124,-534 124,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30437@1" ObjectIDZND0="30409@0" Pin0InfoVect0LinkObjId="g_1a002c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199308_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="124,-534 124,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1901ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="165,-253 124,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="g_1935480@0" ObjectIDZND0="34170@x" ObjectIDZND1="g_1901060@0" Pin0InfoVect0LinkObjId="EC-YM_PT.033Ld_0" Pin0InfoVect1LinkObjId="g_1901060_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1935480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="165,-253 124,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1901dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="124,-253 124,-222 126,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="g_1935480@0" ObjectIDND1="g_1901060@0" ObjectIDZND0="34170@0" Pin0InfoVect0LinkObjId="EC-YM_PT.033Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1935480_0" Pin1InfoVect1LinkObjId="g_1901060_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="124,-253 124,-222 126,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1901fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="124,-253 124,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="34170@x" ObjectIDND1="g_1935480@0" ObjectIDZND0="g_1901060@0" Pin0InfoVect0LinkObjId="g_1901060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YM_PT.033Ld_0" Pin1InfoVect1LinkObjId="g_1935480_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="124,-253 124,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19037e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="321,-497 321,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="30440@0" ObjectIDZND0="30439@1" Pin0InfoVect0LinkObjId="SW-199353_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199354_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="321,-497 321,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19c4eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="321,-434 321,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="30439@0" ObjectIDZND0="30441@1" Pin0InfoVect0LinkObjId="SW-199355_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199353_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="321,-434 321,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1991d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="561,-494 561,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="30443@0" ObjectIDZND0="30442@1" Pin0InfoVect0LinkObjId="SW-199399_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="561,-494 561,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18b79b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="561,-429 561,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="30442@0" ObjectIDZND0="30444@1" Pin0InfoVect0LinkObjId="SW-199401_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199399_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="561,-429 561,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18b9a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="561,-363 561,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="30444@0" ObjectIDZND0="g_18b9000@1" Pin0InfoVect0LinkObjId="g_18b9000_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199401_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="561,-363 561,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18b9c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="602,-324 561,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_18b7c10@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18b7c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="602,-324 561,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19f28b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="736,-495 736,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="30446@0" ObjectIDZND0="30445@1" Pin0InfoVect0LinkObjId="SW-199445_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199446_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="736,-495 736,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19f5310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="736,-430 736,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="30445@0" ObjectIDZND0="30447@1" Pin0InfoVect0LinkObjId="SW-199447_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199445_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="736,-430 736,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18ee850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="777,-250 736,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="g_19f5570@0" ObjectIDZND0="34173@x" ObjectIDZND1="g_18ede30@0" Pin0InfoVect0LinkObjId="EC-YM_PT.036Ld_0" Pin0InfoVect1LinkObjId="g_18ede30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19f5570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="777,-250 736,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18eeab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="736,-250 736,-219 738,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="g_19f5570@0" ObjectIDND1="g_18ede30@0" ObjectIDZND0="34173@0" Pin0InfoVect0LinkObjId="EC-YM_PT.036Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_19f5570_0" Pin1InfoVect1LinkObjId="g_18ede30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="736,-250 736,-219 738,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18eed10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="736,-250 736,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="g_19f5570@0" ObjectIDND1="34173@x" ObjectIDZND0="g_18ede30@0" Pin0InfoVect0LinkObjId="g_18ede30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_19f5570_0" Pin1InfoVect1LinkObjId="EC-YM_PT.036Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="736,-250 736,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18cd190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="913,-492 913,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="30449@0" ObjectIDZND0="30448@1" Pin0InfoVect0LinkObjId="SW-199491_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199492_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="913,-492 913,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1958910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="913,-427 913,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="30448@0" ObjectIDZND0="30450@1" Pin0InfoVect0LinkObjId="SW-199493_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199491_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="913,-427 913,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_195aa20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="954,-247 913,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="g_1958b70@0" ObjectIDZND0="34174@x" ObjectIDZND1="g_195a000@0" Pin0InfoVect0LinkObjId="EC-YM_PT.037Ld_0" Pin0InfoVect1LinkObjId="g_195a000_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1958b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="954,-247 913,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_192e9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="913,-247 913,-216 915,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="g_1958b70@0" ObjectIDND1="g_195a000@0" ObjectIDZND0="34174@0" Pin0InfoVect0LinkObjId="EC-YM_PT.037Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1958b70_0" Pin1InfoVect1LinkObjId="g_195a000_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="913,-247 913,-216 915,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_192ec40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="913,-247 913,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="g_1958b70@0" ObjectIDND1="34174@x" ObjectIDZND0="g_195a000@0" Pin0InfoVect0LinkObjId="g_195a000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1958b70_0" Pin1InfoVect1LinkObjId="EC-YM_PT.037Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="913,-247 913,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_192f710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1072,-526 1072,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30432@1" ObjectIDZND0="30409@0" Pin0InfoVect0LinkObjId="g_1a002c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199260_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1072,-526 1072,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_192fdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="913,-528 913,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30449@1" ObjectIDZND0="30409@0" Pin0InfoVect0LinkObjId="g_1a002c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199492_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="913,-528 913,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1930600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="736,-531 736,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30446@1" ObjectIDZND0="30409@0" Pin0InfoVect0LinkObjId="g_1a002c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199446_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="736,-531 736,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1930e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="561,-530 561,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30443@1" ObjectIDZND0="30409@0" Pin0InfoVect0LinkObjId="g_1a002c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199400_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="561,-530 561,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19964f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="378,-207 378,-197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="378,-207 378,-197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1997ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="378,-237 321,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="0@1" ObjectIDZND0="g_19c57d0@0" ObjectIDZND1="g_19989d0@0" ObjectIDZND2="34171@x" Pin0InfoVect0LinkObjId="g_19c57d0_0" Pin0InfoVect1LinkObjId="g_19989d0_0" Pin0InfoVect2LinkObjId="EC-YM_PT.034Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="378,-237 321,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19a6770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="321,-279 321,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="hydroGenerator" EndDevType2="switch" ObjectIDND0="g_19c57d0@0" ObjectIDZND0="34171@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="EC-YM_PT.034Ld_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19c57d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="321,-279 321,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19a69d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="321,-256 321,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" EndDevType1="hydroGenerator" EndDevType2="switch" ObjectIDND0="g_19c57d0@0" ObjectIDND1="g_19989d0@0" ObjectIDZND0="34171@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="EC-YM_PT.034Ld_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_19c57d0_0" Pin1InfoVect1LinkObjId="g_19989d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="321,-256 321,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19a6c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="321,-533 321,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30440@1" ObjectIDZND0="30409@0" Pin0InfoVect0LinkObjId="g_1a002c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199354_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="321,-533 321,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1955010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-748 673,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_19064b0@0" ObjectIDZND0="30431@x" ObjectIDZND1="30452@x" Pin0InfoVect0LinkObjId="SW-199162_0" Pin0InfoVect1LinkObjId="g_1936970_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19064b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="726,-748 673,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19559b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="674,-735 674,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="30431@1" ObjectIDZND0="30452@x" ObjectIDZND1="g_19064b0@0" Pin0InfoVect0LinkObjId="g_1936970_0" Pin0InfoVect1LinkObjId="g_19064b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199162_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="674,-735 674,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1955be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="674,-748 673,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="30431@x" ObjectIDND1="g_19064b0@0" ObjectIDZND0="30452@1" Pin0InfoVect0LinkObjId="g_1936970_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-199162_0" Pin1InfoVect1LinkObjId="g_19064b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="674,-748 673,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1905550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="389,-746 335,-746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_1955e10@0" ObjectIDZND0="30425@x" ObjectIDZND1="30451@x" Pin0InfoVect0LinkObjId="SW-199074_0" Pin0InfoVect1LinkObjId="g_19b9970_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1955e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="389,-746 335,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1905ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="335,-738 335,-746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="30425@1" ObjectIDZND0="g_1955e10@0" ObjectIDZND1="30451@x" Pin0InfoVect0LinkObjId="g_1955e10_0" Pin0InfoVect1LinkObjId="g_19b9970_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199074_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="335,-738 335,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1906250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="335,-746 335,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_1955e10@0" ObjectIDND1="30425@x" ObjectIDZND0="30451@1" Pin0InfoVect0LinkObjId="g_19b9970_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1955e10_0" Pin1InfoVect1LinkObjId="SW-199074_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="335,-746 335,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1943690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="321,-201 321,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="34171@0" ObjectIDZND0="g_19c57d0@0" ObjectIDZND1="g_19989d0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_19c57d0_0" Pin0InfoVect1LinkObjId="g_19989d0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YM_PT.034Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="321,-201 321,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19438f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="321,-226 321,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="hydroGenerator" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="34171@x" ObjectIDND1="0@x" ObjectIDZND0="g_19c57d0@0" ObjectIDZND1="g_19989d0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_19c57d0_0" Pin0InfoVect1LinkObjId="g_19989d0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YM_PT.034Ld_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="321,-226 321,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1889fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-13,-355 -54,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1889440@0" ObjectIDZND0="30435@x" ObjectIDZND1="g_1a22180@0" Pin0InfoVect0LinkObjId="SW-199263_0" Pin0InfoVect1LinkObjId="g_1a22180_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1889440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-13,-355 -54,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_188aab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-54,-369 -54,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="30435@0" ObjectIDZND0="g_1889440@0" ObjectIDZND1="g_1a22180@0" Pin0InfoVect0LinkObjId="g_1889440_0" Pin0InfoVect1LinkObjId="g_1a22180_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199263_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-54,-369 -54,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_188ad10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-54,-355 -54,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1889440@0" ObjectIDND1="30435@x" ObjectIDZND0="g_1a22180@1" Pin0InfoVect0LinkObjId="g_1a22180_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1889440_0" Pin1InfoVect1LinkObjId="SW-199263_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-54,-355 -54,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17ffbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="165,-355 124,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_17feeb0@0" ObjectIDZND0="30438@x" ObjectIDZND1="g_1901060@0" Pin0InfoVect0LinkObjId="SW-199309_0" Pin0InfoVect1LinkObjId="g_1901060_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17feeb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="165,-355 124,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18006d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="124,-367 124,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="30438@0" ObjectIDZND0="g_17feeb0@0" ObjectIDZND1="g_1901060@0" Pin0InfoVect0LinkObjId="g_17feeb0_0" Pin0InfoVect1LinkObjId="g_1901060_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199309_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="124,-367 124,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1800930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="124,-355 124,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_17feeb0@0" ObjectIDND1="30438@x" ObjectIDZND0="g_1901060@1" Pin0InfoVect0LinkObjId="g_1901060_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_17feeb0_0" Pin1InfoVect1LinkObjId="SW-199309_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="124,-355 124,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18018c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="434,-239 434,-256 321,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="hydroGenerator" ObjectIDND0="g_19989d0@0" ObjectIDZND0="g_19c57d0@0" ObjectIDZND1="34171@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_19c57d0_0" Pin0InfoVect1LinkObjId="EC-YM_PT.034Ld_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19989d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="434,-239 434,-256 321,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1802d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="264,-196 264,-226 321,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="34171@x" ObjectIDZND1="g_19c57d0@0" ObjectIDZND2="g_19989d0@0" Pin0InfoVect0LinkObjId="EC-YM_PT.034Ld_0" Pin0InfoVect1LinkObjId="g_19c57d0_0" Pin0InfoVect2LinkObjId="g_19989d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="264,-196 264,-226 321,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1803ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="778,-347 737,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1802f70@0" ObjectIDZND0="30447@x" ObjectIDZND1="g_18ede30@0" Pin0InfoVect0LinkObjId="SW-199447_0" Pin0InfoVect1LinkObjId="g_18ede30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1802f70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="778,-347 737,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1804c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="954,-341 913,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1803f00@0" ObjectIDZND0="30450@x" ObjectIDZND1="g_195a000@0" Pin0InfoVect0LinkObjId="SW-199493_0" Pin0InfoVect1LinkObjId="g_195a000_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1803f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="954,-341 913,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18082d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1072,-433 1072,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1807900@1" ObjectIDZND0="g_1804e90@0" Pin0InfoVect0LinkObjId="g_1804e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1807900_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1072,-433 1072,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1808530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="359,-342 321,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1800b90@0" ObjectIDZND0="30441@x" ObjectIDZND1="g_19c57d0@0" Pin0InfoVect0LinkObjId="SW-199355_0" Pin0InfoVect1LinkObjId="g_19c57d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1800b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="359,-342 321,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1809020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="321,-366 321,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="30441@0" ObjectIDZND0="g_1800b90@0" ObjectIDZND1="g_19c57d0@0" Pin0InfoVect0LinkObjId="g_1800b90_0" Pin0InfoVect1LinkObjId="g_19c57d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199355_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="321,-366 321,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1809280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="321,-342 321,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1800b90@0" ObjectIDND1="30441@x" ObjectIDZND0="g_19c57d0@1" Pin0InfoVect0LinkObjId="g_19c57d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1800b90_0" Pin1InfoVect1LinkObjId="SW-199355_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="321,-342 321,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1809d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="736,-364 736,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="30447@0" ObjectIDZND0="g_1802f70@0" ObjectIDZND1="g_18ede30@0" Pin0InfoVect0LinkObjId="g_1802f70_0" Pin0InfoVect1LinkObjId="g_18ede30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199447_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="736,-364 736,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1809fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="736,-347 736,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1802f70@0" ObjectIDND1="30447@x" ObjectIDZND0="g_18ede30@1" Pin0InfoVect0LinkObjId="g_18ede30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1802f70_0" Pin1InfoVect1LinkObjId="SW-199447_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="736,-347 736,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_180aac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="913,-361 913,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="30450@0" ObjectIDZND0="g_1803f00@0" ObjectIDZND1="g_195a000@0" Pin0InfoVect0LinkObjId="g_1803f00_0" Pin0InfoVect1LinkObjId="g_195a000_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199493_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="913,-361 913,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_180ad20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="913,-341 913,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1803f00@0" ObjectIDND1="30450@x" ObjectIDZND0="g_195a000@1" Pin0InfoVect0LinkObjId="g_195a000_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1803f00_0" Pin1InfoVect1LinkObjId="SW-199493_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="913,-341 913,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18c9250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="603,-251 562,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_18c87d0@0" ObjectIDZND0="g_18b9000@0" ObjectIDZND1="34172@x" Pin0InfoVect0LinkObjId="g_18b9000_0" Pin0InfoVect1LinkObjId="EC-YM_PT.035Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18c87d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="603,-251 562,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18c9d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="561,-201 561,-218 561,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="34172@0" ObjectIDZND0="g_18c87d0@0" ObjectIDZND1="g_18b9000@0" Pin0InfoVect0LinkObjId="g_18c87d0_0" Pin0InfoVect1LinkObjId="g_18b9000_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YM_PT.035Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="561,-201 561,-218 561,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18f0f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="561,-251 561,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="g_18c87d0@0" ObjectIDND1="34172@x" ObjectIDZND0="g_18b9000@0" Pin0InfoVect0LinkObjId="g_18b9000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_18c87d0_0" Pin1InfoVect1LinkObjId="EC-YM_PT.035Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="561,-251 561,-279 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YM_PT"/>
</svg>