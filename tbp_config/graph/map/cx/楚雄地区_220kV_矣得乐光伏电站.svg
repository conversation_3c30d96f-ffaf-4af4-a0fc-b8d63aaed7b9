<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-327" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-243 -1228 3048 1865">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="19" y2="19"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape126">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="46" y2="29"/>
   </symbol>
   <symbol id="lightningRod:shape139">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27195" x1="7" x2="7" y1="6" y2="15"/>
    <rect height="25" stroke-width="1" width="12" x="1" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="48" y2="23"/>
   </symbol>
   <symbol id="lightningRod:shape174">
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="14" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="39" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="7" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape206">
    <circle cx="7" cy="21" fillStyle="0" r="6" stroke-width="0.431185"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="3,17 11,25 " stroke-width="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="2,24 11,17 " stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="27" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="reactance:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="39" y2="47"/>
    <polyline points="13,39 15,39 17,38 18,38 20,37 21,36 23,35 24,33 25,31 25,30 26,28 26,26 26,24 25,22 25,21 24,19 23,18 21,16 20,15 18,14 17,14 15,13 13,13 11,13 9,14 8,14 6,15 5,16 3,18 2,19 1,21 1,22 0,24 0,26 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="0" x2="12" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="5" y2="26"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape34_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape34_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="33" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape34-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape34-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="33" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer:shape23_0">
    <circle cx="77" cy="48" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="70" x2="70" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="70" x2="68" y1="86" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="65" x2="70" y1="86" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="70" y1="47" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="71" y1="58" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="89" y1="58" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="71" x2="89" y1="42" y2="42"/>
   </symbol>
   <symbol id="transformer:shape23_1">
    <circle cx="41" cy="64" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="33" y1="69" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="47" x2="40" y1="62" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="40" y1="78" y2="69"/>
   </symbol>
   <symbol id="transformer:shape23-2">
    <circle cx="41" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="33" y1="24" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="47" x2="40" y1="31" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="40" y1="15" y2="24"/>
   </symbol>
   <symbol id="voltageTransformer:shape144">
    <circle cx="17" cy="36" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="0" x2="10" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07362" x1="3" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="2" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="5" x2="10" y1="37" y2="37"/>
    <polyline points="5,7 5,37 " stroke-width="1"/>
    <circle cx="17" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="39" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="17" x2="15" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="30" x2="30" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="34" x2="30" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="34" x2="30" y1="24" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="43" x2="41" y1="27" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="43" x2="41" y1="33" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="41" x2="38" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="32" x2="30" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="30" x2="27" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="32" x2="30" y1="39" y2="36"/>
    <circle cx="30" cy="23" r="7.5" stroke-width="0.804311"/>
    <circle cx="29" cy="36" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="22" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="28" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="17" x2="14" y1="25" y2="25"/>
    <circle cx="40" cy="30" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_247d0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_192d030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_247f6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2480610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24818e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24824f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24830a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2483ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_21aa2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_21aa2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24871f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24871f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2489120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2489120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_248a140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_248bd40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_248c930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_248d6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_248e030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_248f6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24902d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2490b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2491350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2492430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2492db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24938a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2494260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2495720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24962e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2497310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2497f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24a68d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2499780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_249a920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_249bf00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1875" width="3058" x="-248" y="-1233"/>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="-192" cy="-48" fill="rgb(60,120,255)" fillStyle="1" r="3" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="-158" cy="-48" fill="rgb(60,120,255)" fillStyle="1" r="3" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="2754" cy="545" fill="rgb(60,120,255)" fillStyle="1" r="3" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="2720" cy="545" fill="rgb(60,120,255)" fillStyle="1" r="3" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1.16541" x1="1428" x2="1418" y1="-1115" y2="-1115"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1.07362" x1="1422" x2="1425" y1="-1119" y2="-1119"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1.16541" x1="1421" x2="1426" y1="-1118" y2="-1118"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.804274" x1="1402" x2="1400" y1="-1098" y2="-1101"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.804274" x1="1402" x2="1400" y1="-1104" y2="-1101"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.804306" x1="1400" x2="1397" y1="-1101" y2="-1101"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.726459" x1="1412" x2="1412" y1="-1085" y2="-1090"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.752494" x1="1417" x2="1413" y1="-1086" y2="-1085"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.752385" x1="1417" x2="1413" y1="-1089" y2="-1090"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.804274" x1="1425" x2="1423" y1="-1092" y2="-1095"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.804274" x1="1425" x2="1423" y1="-1097" y2="-1095"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.804306" x1="1423" x2="1421" y1="-1094" y2="-1094"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.804274" x1="1414" x2="1412" y1="-1098" y2="-1101"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.804306" x1="1412" x2="1410" y1="-1100" y2="-1100"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.804274" x1="1414" x2="1412" y1="-1103" y2="-1101"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.804274" x1="1401" x2="1399" y1="-1087" y2="-1090"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.804274" x1="1401" x2="1399" y1="-1092" y2="-1090"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.804306" x1="1399" x2="1397" y1="-1089" y2="-1089"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1.16541" x1="1452" x2="1452" y1="-1089" y2="-1099"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1.16541" x1="1446" x2="1446" y1="-1089" y2="-1099"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-147" x2="-200" y1="14" y2="14"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-147" x2="-200" y1="-84" y2="-84"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-200" x2="-200" y1="-84" y2="14"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-157" x2="-157" y1="-47" y2="-4"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-157" x2="-192" y1="-5" y2="-5"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-192" x2="-192" y1="-4" y2="-46"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-169" x2="-169" y1="-1" y2="-7"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-197" x2="-186" y1="-47" y2="-47"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-152" x2="-163" y1="-47" y2="-47"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-166" x2="-185" y1="-60" y2="-60"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-171" x2="-182" y1="-67" y2="-67"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-176" x2="-176" y1="-67" y2="-75"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-186" x2="-178" y1="-47" y2="-60"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-163" x2="-170" y1="-47" y2="-60"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-174" x2="-174" y1="-84" y2="-85"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-147" x2="-147" y1="-84" y2="14"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="-174" x2="-174" y1="-84" y2="-113"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="-174" x2="-220" y1="-113" y2="-113"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="-220" x2="-220" y1="-113" y2="-40"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="-220" x2="-220" y1="-27" y2="36"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="-219" x2="-169" y1="36" y2="36"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="-169" x2="-169" y1="36" y2="15"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="-241" x2="-203" y1="-27" y2="-27"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2710" x2="2763" y1="607" y2="607"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2710" x2="2763" y1="508" y2="508"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2763" x2="2763" y1="508" y2="607"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2719" x2="2719" y1="544" y2="587"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2719" x2="2754" y1="587" y2="587"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2754" x2="2754" y1="587" y2="545"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2731" x2="2731" y1="590" y2="584"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2759" x2="2748" y1="544" y2="544"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2714" x2="2725" y1="544" y2="544"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2728" x2="2747" y1="531" y2="531"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2733" x2="2744" y1="524" y2="524"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2738" x2="2738" y1="524" y2="516"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2748" x2="2740" y1="545" y2="532"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2725" x2="2732" y1="545" y2="532"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2737" x2="2737" y1="508" y2="507"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2710" x2="2710" y1="508" y2="607"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="2736" x2="2736" y1="508" y2="479"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="2736" x2="2782" y1="479" y2="479"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="2782" x2="2782" y1="479" y2="552"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="2782" x2="2782" y1="565" y2="628"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="2781" x2="2731" y1="628" y2="628"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="2731" x2="2731" y1="628" y2="607"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2803" x2="2765" y1="565" y2="565"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="6" stroke="rgb(60,120,255)" stroke-width="1" width="39" x="-243" y="-40"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="6" stroke="rgb(60,120,255)" stroke-width="1" width="39" x="2766" y="552"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="24" stroke="rgb(60,120,255)" stroke-width="0.379884" width="13" x="561" y="-137"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="0.416667" width="28" x="-31" y="-83"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="24" stroke="rgb(60,120,255)" stroke-width="0.379884" width="13" x="569" y="340"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="0.416667" width="28" x="2566" y="510"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-306128">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1465.742135 -941.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47433" ObjectName="SW-CX_YDLGF.CX_YDLGF_2716SW"/>
     <cge:Meas_Ref ObjectId="306128"/>
    <cge:TPSR_Ref TObjectID="47433"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306125">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1465.742135 -772.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47430" ObjectName="SW-CX_YDLGF.CX_YDLGF_2711SW"/>
     <cge:Meas_Ref ObjectId="306125"/>
    <cge:TPSR_Ref TObjectID="47430"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306130">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1416.742135 -910.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47435" ObjectName="SW-CX_YDLGF.CX_YDLGF_27160SW"/>
     <cge:Meas_Ref ObjectId="306130"/>
    <cge:TPSR_Ref TObjectID="47435"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306126">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1415.742135 -831.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47431" ObjectName="SW-CX_YDLGF.CX_YDLGF_27117SW"/>
     <cge:Meas_Ref ObjectId="306126"/>
    <cge:TPSR_Ref TObjectID="47431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306127">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1416.742135 -747.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47432" ObjectName="SW-CX_YDLGF.CX_YDLGF_27110SW"/>
     <cge:Meas_Ref ObjectId="306127"/>
    <cge:TPSR_Ref TObjectID="47432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306136">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 1.000000 1383.000000 -683.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47436" ObjectName="SW-CX_YDLGF.CX_YDLGF_2010SW"/>
     <cge:Meas_Ref ObjectId="306136"/>
    <cge:TPSR_Ref TObjectID="47436"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306137">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1353.000000 -568.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47437" ObjectName="SW-CX_YDLGF.CX_YDLGF_3010SW"/>
     <cge:Meas_Ref ObjectId="306137"/>
    <cge:TPSR_Ref TObjectID="47437"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306129">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1410.742135 -1007.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47434" ObjectName="SW-CX_YDLGF.CX_YDLGF_27167SW"/>
     <cge:Meas_Ref ObjectId="306129"/>
    <cge:TPSR_Ref TObjectID="47434"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306145">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 172.437855 -228.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47446" ObjectName="SW-CX_YDLGF.CX_YDLGF_371XC1"/>
     <cge:Meas_Ref ObjectId="306145"/>
    <cge:TPSR_Ref TObjectID="47446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306145">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 172.437855 -320.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47445" ObjectName="SW-CX_YDLGF.CX_YDLGF_371XC"/>
     <cge:Meas_Ref ObjectId="306145"/>
    <cge:TPSR_Ref TObjectID="47445"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306146">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 263.311273 -153.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47447" ObjectName="SW-CX_YDLGF.CX_YDLGF_37167SW"/>
     <cge:Meas_Ref ObjectId="306146"/>
    <cge:TPSR_Ref TObjectID="47447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306149">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -40.500000 -26.529091)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47450" ObjectName="SW-CX_YDLGF.CX_YDLGF_3718SW"/>
     <cge:Meas_Ref ObjectId="306149"/>
    <cge:TPSR_Ref TObjectID="47450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306153">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 364.437855 -230.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47453" ObjectName="SW-CX_YDLGF.CX_YDLGF_372XC1"/>
     <cge:Meas_Ref ObjectId="306153"/>
    <cge:TPSR_Ref TObjectID="47453"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306153">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 364.437855 -322.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47452" ObjectName="SW-CX_YDLGF.CX_YDLGF_372XC"/>
     <cge:Meas_Ref ObjectId="306153"/>
    <cge:TPSR_Ref TObjectID="47452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306154">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 455.311273 -155.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47454" ObjectName="SW-CX_YDLGF.CX_YDLGF_37267SW"/>
     <cge:Meas_Ref ObjectId="306154"/>
    <cge:TPSR_Ref TObjectID="47454"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306354">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 558.437855 -230.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47523" ObjectName="SW-CX_YDLGF.CX_YDLGF_3901XC1"/>
     <cge:Meas_Ref ObjectId="306354"/>
    <cge:TPSR_Ref TObjectID="47523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306354">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 558.437855 -322.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47522" ObjectName="SW-CX_YDLGF.CX_YDLGF_3901XC"/>
     <cge:Meas_Ref ObjectId="306354"/>
    <cge:TPSR_Ref TObjectID="47522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306158">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 750.437855 -232.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47457" ObjectName="SW-CX_YDLGF.CX_YDLGF_374XC1"/>
     <cge:Meas_Ref ObjectId="306158"/>
    <cge:TPSR_Ref TObjectID="47457"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306158">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 750.437855 -324.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47456" ObjectName="SW-CX_YDLGF.CX_YDLGF_374XC"/>
     <cge:Meas_Ref ObjectId="306158"/>
    <cge:TPSR_Ref TObjectID="47456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306159">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 841.311273 -157.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47458" ObjectName="SW-CX_YDLGF.CX_YDLGF_37467SW"/>
     <cge:Meas_Ref ObjectId="306159"/>
    <cge:TPSR_Ref TObjectID="47458"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306163">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 956.437855 -231.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47461" ObjectName="SW-CX_YDLGF.CX_YDLGF_373XC1"/>
     <cge:Meas_Ref ObjectId="306163"/>
    <cge:TPSR_Ref TObjectID="47461"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306163">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 956.437855 -323.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47460" ObjectName="SW-CX_YDLGF.CX_YDLGF_373XC"/>
     <cge:Meas_Ref ObjectId="306163"/>
    <cge:TPSR_Ref TObjectID="47460"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306164">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1047.311273 -156.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47462" ObjectName="SW-CX_YDLGF.CX_YDLGF_37367SW"/>
     <cge:Meas_Ref ObjectId="306164"/>
    <cge:TPSR_Ref TObjectID="47462"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306168">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1148.437855 -233.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47465" ObjectName="SW-CX_YDLGF.CX_YDLGF_375XC1"/>
     <cge:Meas_Ref ObjectId="306168"/>
    <cge:TPSR_Ref TObjectID="47465"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306168">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1148.437855 -325.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47464" ObjectName="SW-CX_YDLGF.CX_YDLGF_375XC"/>
     <cge:Meas_Ref ObjectId="306168"/>
    <cge:TPSR_Ref TObjectID="47464"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306169">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1239.311273 -158.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47466" ObjectName="SW-CX_YDLGF.CX_YDLGF_37567SW"/>
     <cge:Meas_Ref ObjectId="306169"/>
    <cge:TPSR_Ref TObjectID="47466"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306147">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 79.000000 -27.000000)" xlink:href="#switch2:shape34_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47448" ObjectName="SW-CX_YDLGF.CX_YDLGF_3717SW"/>
     <cge:Meas_Ref ObjectId="306147"/>
    <cge:TPSR_Ref TObjectID="47448"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306148">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 50.000000 -46.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47449" ObjectName="SW-CX_YDLGF.CX_YDLGF_37177SW"/>
     <cge:Meas_Ref ObjectId="306148"/>
    <cge:TPSR_Ref TObjectID="47449"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306351">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 2602.500000 565.470909)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47521" ObjectName="SW-CX_YDLGF.CX_YDLGF_3898SW"/>
     <cge:Meas_Ref ObjectId="306351"/>
    <cge:TPSR_Ref TObjectID="47521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306349">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2484.000000 565.000000)" xlink:href="#switch2:shape34_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47519" ObjectName="SW-CX_YDLGF.CX_YDLGF_3897SW"/>
     <cge:Meas_Ref ObjectId="306349"/>
    <cge:TPSR_Ref TObjectID="47519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306350">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2512.000000 546.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47520" ObjectName="SW-CX_YDLGF.CX_YDLGF_38977SW"/>
     <cge:Meas_Ref ObjectId="306350"/>
    <cge:TPSR_Ref TObjectID="47520"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306173">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1549.437855 -228.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47469" ObjectName="SW-CX_YDLGF.CX_YDLGF_376XC1"/>
     <cge:Meas_Ref ObjectId="306173"/>
    <cge:TPSR_Ref TObjectID="47469"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306173">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1549.437855 -320.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47468" ObjectName="SW-CX_YDLGF.CX_YDLGF_376XC"/>
     <cge:Meas_Ref ObjectId="306173"/>
    <cge:TPSR_Ref TObjectID="47468"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306174">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1640.311273 -153.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47470" ObjectName="SW-CX_YDLGF.CX_YDLGF_37667SW"/>
     <cge:Meas_Ref ObjectId="306174"/>
    <cge:TPSR_Ref TObjectID="47470"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306178">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1743.437855 -228.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47473" ObjectName="SW-CX_YDLGF.CX_YDLGF_377XC1"/>
     <cge:Meas_Ref ObjectId="306178"/>
    <cge:TPSR_Ref TObjectID="47473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306178">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1743.437855 -320.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47472" ObjectName="SW-CX_YDLGF.CX_YDLGF_377XC"/>
     <cge:Meas_Ref ObjectId="306178"/>
    <cge:TPSR_Ref TObjectID="47472"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306179">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1834.311273 -153.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47474" ObjectName="SW-CX_YDLGF.CX_YDLGF_37767SW"/>
     <cge:Meas_Ref ObjectId="306179"/>
    <cge:TPSR_Ref TObjectID="47474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306183">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1935.437855 -230.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47477" ObjectName="SW-CX_YDLGF.CX_YDLGF_378XC1"/>
     <cge:Meas_Ref ObjectId="306183"/>
    <cge:TPSR_Ref TObjectID="47477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306183">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1935.437855 -322.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47476" ObjectName="SW-CX_YDLGF.CX_YDLGF_378XC"/>
     <cge:Meas_Ref ObjectId="306183"/>
    <cge:TPSR_Ref TObjectID="47476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306184">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2026.311273 -155.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47478" ObjectName="SW-CX_YDLGF.CX_YDLGF_37867SW"/>
     <cge:Meas_Ref ObjectId="306184"/>
    <cge:TPSR_Ref TObjectID="47478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306188">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2141.437855 -229.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47481" ObjectName="SW-CX_YDLGF.CX_YDLGF_379XC1"/>
     <cge:Meas_Ref ObjectId="306188"/>
    <cge:TPSR_Ref TObjectID="47481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306188">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2141.437855 -321.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47480" ObjectName="SW-CX_YDLGF.CX_YDLGF_379XC"/>
     <cge:Meas_Ref ObjectId="306188"/>
    <cge:TPSR_Ref TObjectID="47480"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306189">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2232.311273 -154.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47482" ObjectName="SW-CX_YDLGF.CX_YDLGF_37967SW"/>
     <cge:Meas_Ref ObjectId="306189"/>
    <cge:TPSR_Ref TObjectID="47482"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2333.437855 -231.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2333.437855 -323.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2424.311273 -156.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306139">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.994444 1361.437855 -232.905556)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47440" ObjectName="SW-CX_YDLGF.CX_YDLGF_301XC1"/>
     <cge:Meas_Ref ObjectId="306139"/>
    <cge:TPSR_Ref TObjectID="47440"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306139">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.994444 1361.437855 -324.394444)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47439" ObjectName="SW-CX_YDLGF.CX_YDLGF_301XC"/>
     <cge:Meas_Ref ObjectId="306139"/>
    <cge:TPSR_Ref TObjectID="47439"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306193">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 173.437855 253.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47485" ObjectName="SW-CX_YDLGF.CX_YDLGF_381XC1"/>
     <cge:Meas_Ref ObjectId="306193"/>
    <cge:TPSR_Ref TObjectID="47485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306193">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 173.437855 161.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47484" ObjectName="SW-CX_YDLGF.CX_YDLGF_381XC"/>
     <cge:Meas_Ref ObjectId="306193"/>
    <cge:TPSR_Ref TObjectID="47484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306194">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 264.311273 328.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47486" ObjectName="SW-CX_YDLGF.CX_YDLGF_38167SW"/>
     <cge:Meas_Ref ObjectId="306194"/>
    <cge:TPSR_Ref TObjectID="47486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306198">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 365.437855 251.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47489" ObjectName="SW-CX_YDLGF.CX_YDLGF_382XC1"/>
     <cge:Meas_Ref ObjectId="306198"/>
    <cge:TPSR_Ref TObjectID="47489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306198">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 365.437855 159.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47488" ObjectName="SW-CX_YDLGF.CX_YDLGF_382XC"/>
     <cge:Meas_Ref ObjectId="306198"/>
    <cge:TPSR_Ref TObjectID="47488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306199">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 456.311273 326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47490" ObjectName="SW-CX_YDLGF.CX_YDLGF_38267SW"/>
     <cge:Meas_Ref ObjectId="306199"/>
    <cge:TPSR_Ref TObjectID="47490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306203">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 751.437855 249.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47493" ObjectName="SW-CX_YDLGF.CX_YDLGF_383XC1"/>
     <cge:Meas_Ref ObjectId="306203"/>
    <cge:TPSR_Ref TObjectID="47493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306203">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 751.437855 157.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47492" ObjectName="SW-CX_YDLGF.CX_YDLGF_383XC"/>
     <cge:Meas_Ref ObjectId="306203"/>
    <cge:TPSR_Ref TObjectID="47492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306204">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 842.311273 324.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47494" ObjectName="SW-CX_YDLGF.CX_YDLGF_38367SW"/>
     <cge:Meas_Ref ObjectId="306204"/>
    <cge:TPSR_Ref TObjectID="47494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306208">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 957.437855 250.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47497" ObjectName="SW-CX_YDLGF.CX_YDLGF_384XC1"/>
     <cge:Meas_Ref ObjectId="306208"/>
    <cge:TPSR_Ref TObjectID="47497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306208">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 957.437855 158.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47496" ObjectName="SW-CX_YDLGF.CX_YDLGF_384XC"/>
     <cge:Meas_Ref ObjectId="306208"/>
    <cge:TPSR_Ref TObjectID="47496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306209">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1048.311273 328.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47498" ObjectName="SW-CX_YDLGF.CX_YDLGF_38467SW"/>
     <cge:Meas_Ref ObjectId="306209"/>
    <cge:TPSR_Ref TObjectID="47498"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306213">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1149.437855 248.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47501" ObjectName="SW-CX_YDLGF.CX_YDLGF_385XC1"/>
     <cge:Meas_Ref ObjectId="306213"/>
    <cge:TPSR_Ref TObjectID="47501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306213">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1149.437855 156.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47500" ObjectName="SW-CX_YDLGF.CX_YDLGF_385XC"/>
     <cge:Meas_Ref ObjectId="306213"/>
    <cge:TPSR_Ref TObjectID="47500"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306214">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1240.311273 323.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47502" ObjectName="SW-CX_YDLGF.CX_YDLGF_38567SW"/>
     <cge:Meas_Ref ObjectId="306214"/>
    <cge:TPSR_Ref TObjectID="47502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306218">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1550.437855 253.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47505" ObjectName="SW-CX_YDLGF.CX_YDLGF_386XC1"/>
     <cge:Meas_Ref ObjectId="306218"/>
    <cge:TPSR_Ref TObjectID="47505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306218">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1550.437855 161.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47504" ObjectName="SW-CX_YDLGF.CX_YDLGF_386XC"/>
     <cge:Meas_Ref ObjectId="306218"/>
    <cge:TPSR_Ref TObjectID="47504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306219">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1641.311273 328.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47506" ObjectName="SW-CX_YDLGF.CX_YDLGF_38667SW"/>
     <cge:Meas_Ref ObjectId="306219"/>
    <cge:TPSR_Ref TObjectID="47506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306337">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1744.437855 253.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47509" ObjectName="SW-CX_YDLGF.CX_YDLGF_387XC1"/>
     <cge:Meas_Ref ObjectId="306337"/>
    <cge:TPSR_Ref TObjectID="47509"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306337">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1744.437855 161.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47508" ObjectName="SW-CX_YDLGF.CX_YDLGF_387XC"/>
     <cge:Meas_Ref ObjectId="306337"/>
    <cge:TPSR_Ref TObjectID="47508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306338">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1835.311273 328.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47510" ObjectName="SW-CX_YDLGF.CX_YDLGF_38767SW"/>
     <cge:Meas_Ref ObjectId="306338"/>
    <cge:TPSR_Ref TObjectID="47510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306342">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1936.437855 251.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47513" ObjectName="SW-CX_YDLGF.CX_YDLGF_388XC1"/>
     <cge:Meas_Ref ObjectId="306342"/>
    <cge:TPSR_Ref TObjectID="47513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306342">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1936.437855 159.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47512" ObjectName="SW-CX_YDLGF.CX_YDLGF_388XC"/>
     <cge:Meas_Ref ObjectId="306342"/>
    <cge:TPSR_Ref TObjectID="47512"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306343">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2027.311273 326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47514" ObjectName="SW-CX_YDLGF.CX_YDLGF_38867SW"/>
     <cge:Meas_Ref ObjectId="306343"/>
    <cge:TPSR_Ref TObjectID="47514"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306347">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2142.437855 252.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47517" ObjectName="SW-CX_YDLGF.CX_YDLGF_389XC1"/>
     <cge:Meas_Ref ObjectId="306347"/>
    <cge:TPSR_Ref TObjectID="47517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306347">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2142.437855 160.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47516" ObjectName="SW-CX_YDLGF.CX_YDLGF_389XC"/>
     <cge:Meas_Ref ObjectId="306347"/>
    <cge:TPSR_Ref TObjectID="47516"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306348">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2233.311273 327.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47518" ObjectName="SW-CX_YDLGF.CX_YDLGF_38967SW"/>
     <cge:Meas_Ref ObjectId="306348"/>
    <cge:TPSR_Ref TObjectID="47518"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2334.437855 250.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2334.437855 158.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2425.311273 325.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306141">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.994444 1362.437855 248.094444)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47443" ObjectName="SW-CX_YDLGF.CX_YDLGF_302XC1"/>
     <cge:Meas_Ref ObjectId="306141"/>
    <cge:TPSR_Ref TObjectID="47443"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306141">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.994444 1362.437855 156.605556)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47442" ObjectName="SW-CX_YDLGF.CX_YDLGF_302XC"/>
     <cge:Meas_Ref ObjectId="306141"/>
    <cge:TPSR_Ref TObjectID="47442"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306356">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 566.437855 247.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47525" ObjectName="SW-CX_YDLGF.CX_YDLGF_3902XC1"/>
     <cge:Meas_Ref ObjectId="306356"/>
    <cge:TPSR_Ref TObjectID="47525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306356">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 566.437855 155.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47524" ObjectName="SW-CX_YDLGF.CX_YDLGF_3902XC"/>
     <cge:Meas_Ref ObjectId="306356"/>
    <cge:TPSR_Ref TObjectID="47524"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1966200">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 551.000000 -38.000000)" xlink:href="#voltageTransformer:shape144"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26fe400">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 559.000000 439.000000)" xlink:href="#voltageTransformer:shape144"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YDLGF.CX_YDLGF_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="25,-384 2562,-384 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47426" ObjectName="BS-CX_YDLGF.CX_YDLGF_3IM"/>
    <cge:TPSR_Ref TObjectID="47426"/></metadata>
   <polyline fill="none" opacity="0" points="25,-384 2562,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YDLGF.CX_YDLGF_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="26,97 2563,97 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47427" ObjectName="BS-CX_YDLGF.CX_YDLGF_3IIM"/>
    <cge:TPSR_Ref TObjectID="47427"/></metadata>
   <polyline fill="none" opacity="0" points="26,97 2563,97 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Reactance_Layer">
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 -120.500000 -44.529091)" xlink:href="#reactance:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 2682.500000 547.470909)" xlink:href="#reactance:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_27ebde0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1402.742135 -921.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2784000" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1401.742135 -842.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aa48a0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1402.742135 -758.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28960b0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1380.500000 -707.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27f3470" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1269.000000 -579.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f90460" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1396.742135 -1018.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22cbe00" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 252.311273 -152.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_195aee0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 444.311273 -154.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18a0b90" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 830.311273 -156.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18b7610" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1036.311273 -155.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_287ca40" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1228.311273 -157.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ab37f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 53.000000 -97.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_278d4f0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2509.000000 495.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19d7750" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1629.311273 -152.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2980580" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1823.311273 -152.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_242d320" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2015.311273 -154.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30dce00" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2221.311273 -153.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a2e060" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2413.311273 -155.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25c8060" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 253.311273 330.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22b8c40" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 445.311273 328.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22dcc10" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 831.311273 326.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2765460" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1037.311273 327.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_276cb80" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1229.311273 325.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aa93f0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1630.311273 330.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a1ae30" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1824.311273 330.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19b6380" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2016.311273 328.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25ef440" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2222.311273 329.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_312ddb0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2414.311273 327.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-220KV" id="g_27e7bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1398,-915 1422,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_27ebde0@0" ObjectIDZND0="47435@0" Pin0InfoVect0LinkObjId="SW-306130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27ebde0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1398,-915 1422,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_27ecff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1458,-915 1475,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="47435@1" ObjectIDZND0="47429@x" ObjectIDZND1="47433@x" Pin0InfoVect0LinkObjId="SW-306124_0" Pin0InfoVect1LinkObjId="SW-306128_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306130_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1458,-915 1475,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_27ea250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-888 1475,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="47429@1" ObjectIDZND0="47435@x" ObjectIDZND1="47433@x" Pin0InfoVect0LinkObjId="SW-306130_0" Pin0InfoVect1LinkObjId="SW-306128_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306124_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-888 1475,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_260b360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-915 1475,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="47435@x" ObjectIDND1="47429@x" ObjectIDZND0="47433@0" Pin0InfoVect0LinkObjId="SW-306128_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-306130_0" Pin1InfoVect1LinkObjId="SW-306124_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-915 1475,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_26a5120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1397,-836 1421,-836 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2784000@0" ObjectIDZND0="47431@0" Pin0InfoVect0LinkObjId="SW-306126_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2784000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1397,-836 1421,-836 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_27ecb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1457,-836 1475,-836 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="47431@1" ObjectIDZND0="47430@x" ObjectIDZND1="47429@x" Pin0InfoVect0LinkObjId="SW-306125_0" Pin0InfoVect1LinkObjId="SW-306124_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306126_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1457,-836 1475,-836 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_240d570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-813 1475,-836 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="47430@1" ObjectIDZND0="47431@x" ObjectIDZND1="47429@x" Pin0InfoVect0LinkObjId="SW-306126_0" Pin0InfoVect1LinkObjId="SW-306124_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306125_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-813 1475,-836 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_260c230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-836 1475,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="47431@x" ObjectIDND1="47430@x" ObjectIDZND0="47429@0" Pin0InfoVect0LinkObjId="SW-306124_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-306126_0" Pin1InfoVect1LinkObjId="SW-306125_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-836 1475,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_27ed4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1398,-752 1422,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1aa48a0@0" ObjectIDZND0="47432@0" Pin0InfoVect0LinkObjId="SW-306127_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aa48a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1398,-752 1422,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1a53c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1458,-752 1475,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="47432@1" ObjectIDZND0="47430@x" ObjectIDZND1="47526@x" Pin0InfoVect0LinkObjId="SW-306125_0" Pin0InfoVect1LinkObjId="g_3137b30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306127_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1458,-752 1475,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_27ec270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-752 1475,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="47432@x" ObjectIDND1="47526@x" ObjectIDZND0="47430@0" Pin0InfoVect0LinkObjId="SW-306125_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-306127_0" Pin1InfoVect1LinkObjId="g_3137b30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-752 1475,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3137b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1394,-573 1475,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="47437@1" ObjectIDZND0="47526@x" Pin0InfoVect0LinkObjId="g_27992d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306137_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1394,-573 1475,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27f3210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1264,-573 1288,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_27f3470@0" ObjectIDZND0="g_30ed000@0" Pin0InfoVect0LinkObjId="g_30ed000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27f3470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1264,-573 1288,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27990e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1322,-573 1358,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30ed000@1" ObjectIDZND0="47437@0" Pin0InfoVect0LinkObjId="SW-306137_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30ed000_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1322,-573 1358,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27992d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1617,-574 1617,-599 1536,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" ObjectIDND0="g_27f6760@0" ObjectIDZND0="47526@0" Pin0InfoVect0LinkObjId="g_3137b30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27f6760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1617,-574 1617,-599 1536,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_27994c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1374,-712 1374,-678 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_28960b0@0" ObjectIDZND0="47436@0" Pin0InfoVect0LinkObjId="SW-306136_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28960b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1374,-712 1374,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2960df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1328,-649 1328,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer" EndDevType2="lightningRod" ObjectIDND0="g_1a53ec0@1" ObjectIDZND0="47436@x" ObjectIDZND1="47526@x" ObjectIDZND2="g_27a5cc0@0" Pin0InfoVect0LinkObjId="SW-306136_0" Pin0InfoVect1LinkObjId="g_3137b30_0" Pin0InfoVect2LinkObjId="g_27a5cc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a53ec0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1328,-649 1328,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_30fea40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1374,-642 1374,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="47436@1" ObjectIDZND0="47526@x" ObjectIDZND1="g_1a53ec0@0" ObjectIDZND2="g_27a5cc0@0" Pin0InfoVect0LinkObjId="g_3137b30_0" Pin0InfoVect1LinkObjId="g_1a53ec0_0" Pin0InfoVect2LinkObjId="g_27a5cc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306136_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1374,-642 1374,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1ff7470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1476,-620 1374,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="47526@x" ObjectIDZND0="47436@x" ObjectIDZND1="g_1a53ec0@0" ObjectIDZND2="g_27a5cc0@0" Pin0InfoVect0LinkObjId="SW-306136_0" Pin0InfoVect1LinkObjId="g_1a53ec0_0" Pin0InfoVect2LinkObjId="g_27a5cc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3137b30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1476,-620 1374,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1ff56b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1374,-620 1328,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="47436@x" ObjectIDND1="47526@x" ObjectIDZND0="g_1a53ec0@0" ObjectIDZND1="g_27a5cc0@0" Pin0InfoVect0LinkObjId="g_1a53ec0_0" Pin0InfoVect1LinkObjId="g_27a5cc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-306136_0" Pin1InfoVect1LinkObjId="g_3137b30_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1374,-620 1328,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1ff5910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1289,-646 1289,-620 1328,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="transformer" ObjectIDND0="g_27a5cc0@0" ObjectIDZND0="g_1a53ec0@0" ObjectIDZND1="47436@x" ObjectIDZND2="47526@x" Pin0InfoVect0LinkObjId="g_1a53ec0_0" Pin0InfoVect1LinkObjId="SW-306136_0" Pin0InfoVect2LinkObjId="g_3137b30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27a5cc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1289,-646 1289,-620 1328,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_189e3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-752 1475,-639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer" ObjectIDND0="47432@x" ObjectIDND1="47430@x" ObjectIDZND0="47526@1" Pin0InfoVect0LinkObjId="g_3137b30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-306127_0" Pin1InfoVect1LinkObjId="SW-306125_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-752 1475,-639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_27b8a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1392,-1012 1416,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1f90460@0" ObjectIDZND0="47434@0" Pin0InfoVect0LinkObjId="SW-306129_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f90460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1392,-1012 1416,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_19bb740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1452,-1012 1475,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="47434@1" ObjectIDZND0="47433@x" ObjectIDZND1="g_29aa4f0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-306128_0" Pin0InfoVect1LinkObjId="g_29aa4f0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306129_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-1012 1475,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2358010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-982 1475,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="47433@1" ObjectIDZND0="47434@x" ObjectIDZND1="g_29aa4f0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-306129_0" Pin0InfoVect1LinkObjId="g_29aa4f0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306128_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-982 1475,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_311f1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1452,-1094 1475,-1094 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDZND0="47434@x" ObjectIDZND1="47433@x" ObjectIDZND2="g_29aa4f0@0" Pin0InfoVect0LinkObjId="SW-306129_0" Pin0InfoVect1LinkObjId="SW-306128_0" Pin0InfoVect2LinkObjId="g_29aa4f0_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-1094 1475,-1094 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_30dc920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-1012 1475,-1094 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="47434@x" ObjectIDND1="47433@x" ObjectIDZND0="g_29aa4f0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_29aa4f0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-306129_0" Pin1InfoVect1LinkObjId="SW-306128_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-1012 1475,-1094 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29aa060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1423,-1094 1446,-1094 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1423,-1094 1446,-1094 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29aa290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1423,-1102 1423,-1115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1423,-1102 1423,-1115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2953e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1496,-1126 1475,-1126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_29aa4f0@0" ObjectIDZND0="47434@x" ObjectIDZND1="47433@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-306129_0" Pin0InfoVect1LinkObjId="SW-306128_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29aa4f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1496,-1126 1475,-1126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_27874b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-1094 1475,-1126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="47434@x" ObjectIDND1="47433@x" ObjectIDZND0="g_29aa4f0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_29aa4f0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-306129_0" Pin1InfoVect1LinkObjId="SW-306128_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-1094 1475,-1126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2787710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-1126 1475,-1184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_29aa4f0@0" ObjectIDND1="47434@x" ObjectIDND2="47433@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29aa4f0_0" Pin1InfoVect1LinkObjId="SW-306129_0" Pin1InfoVect2LinkObjId="SW-306128_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-1126 1475,-1184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2775410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1459,-487 1475,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_30d7b10@0" ObjectIDZND0="47526@x" ObjectIDZND1="47440@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3137b30_0" Pin0InfoVect1LinkObjId="SW-306139_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30d7b10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1459,-487 1475,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_261eaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-487 1475,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="transformer" ObjectIDND0="g_30d7b10@0" ObjectIDND1="47440@x" ObjectIDND2="0@x" ObjectIDZND0="47526@2" Pin0InfoVect0LinkObjId="g_3137b30_2" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_30d7b10_0" Pin1InfoVect1LinkObjId="SW-306139_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-487 1475,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_279c0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="182,-327 182,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47445@1" ObjectIDZND0="47444@1" Pin0InfoVect0LinkObjId="SW-306144_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306145_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="182,-327 182,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_279c350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="182,-277 182,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47444@0" ObjectIDZND0="47446@1" Pin0InfoVect0LinkObjId="SW-306145_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306144_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="182,-277 182,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2876b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="223,-191 223,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="switch" ObjectIDND0="g_27b34b0@0" ObjectIDZND0="47446@x" ObjectIDZND1="0@x" ObjectIDZND2="47448@x" Pin0InfoVect0LinkObjId="SW-306145_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-306147_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27b34b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="223,-191 223,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27ae400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="182,-211 223,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47446@x" ObjectIDND1="0@x" ObjectIDND2="47448@x" ObjectIDZND0="g_27b34b0@0" ObjectIDZND1="47447@x" Pin0InfoVect0LinkObjId="g_27b34b0_0" Pin0InfoVect1LinkObjId="SW-306146_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306145_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-306147_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="182,-211 223,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27ae660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="223,-211 258,-211 258,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="g_27b34b0@0" ObjectIDND1="47446@x" ObjectIDND2="0@x" ObjectIDZND0="47447@1" Pin0InfoVect0LinkObjId="SW-306146_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_27b34b0_0" Pin1InfoVect1LinkObjId="SW-306145_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="223,-211 258,-211 258,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ef990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="258,-147 258,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22cbe00@0" ObjectIDZND0="47447@0" Pin0InfoVect0LinkObjId="SW-306146_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22cbe00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="258,-147 258,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19efbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="182,-235 182,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="47446@0" ObjectIDZND0="g_27b34b0@0" ObjectIDZND1="47447@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_27b34b0_0" Pin0InfoVect1LinkObjId="SW-306146_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306145_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="182,-235 182,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2982be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="141,-173 141,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_27a66d0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27a66d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="141,-173 141,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27e4ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="141,-195 141,-211 182,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_27b34b0@0" ObjectIDZND1="47447@x" ObjectIDZND2="47446@x" Pin0InfoVect0LinkObjId="g_27b34b0_0" Pin0InfoVect1LinkObjId="SW-306146_0" Pin0InfoVect2LinkObjId="SW-306145_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="141,-195 141,-211 182,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27b23f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-148,-32 -115,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="reactance" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-148,-32 -115,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27b2650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-51,-32 -51,-75 -31,-75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="47450@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-306149_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="-51,-32 -51,-75 -31,-75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27b28b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-4,-75 16,-75 16,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDZND0="47450@x" ObjectIDZND1="g_2444ac0@0" ObjectIDZND2="47449@x" Pin0InfoVect0LinkObjId="SW-306149_0" Pin0InfoVect1LinkObjId="g_2444ac0_0" Pin0InfoVect2LinkObjId="SW-306148_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-4,-75 16,-75 16,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27b2b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1,-32 16,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="47450@1" ObjectIDZND0="g_2444ac0@0" ObjectIDZND1="47449@x" ObjectIDZND2="47448@x" Pin0InfoVect0LinkObjId="g_2444ac0_0" Pin0InfoVect1LinkObjId="SW-306148_0" Pin0InfoVect2LinkObjId="SW-306147_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306149_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1,-32 16,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27b0b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-73,-32 -51,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="47450@x" Pin0InfoVect0LinkObjId="SW-306149_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-73,-32 -51,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27b0dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-51,-32 -35,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="47450@0" Pin0InfoVect0LinkObjId="SW-306149_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-51,-32 -35,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_218dea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="374,-329 374,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47452@1" ObjectIDZND0="47451@1" Pin0InfoVect0LinkObjId="SW-306152_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306153_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="374,-329 374,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27612b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="374,-279 374,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47451@0" ObjectIDZND0="47453@1" Pin0InfoVect0LinkObjId="SW-306153_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306152_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="374,-279 374,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_189d320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="415,-193 415,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="generator" ObjectIDND0="g_27e66f0@0" ObjectIDZND0="47453@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-306153_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27e66f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="415,-193 415,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_189d580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="374,-213 415,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="generator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47453@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_27e66f0@0" ObjectIDZND1="47454@x" Pin0InfoVect0LinkObjId="g_27e66f0_0" Pin0InfoVect1LinkObjId="SW-306154_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306153_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="374,-213 415,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_189d7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="415,-213 450,-213 450,-197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="g_27e66f0@0" ObjectIDND1="47453@x" ObjectIDND2="0@x" ObjectIDZND0="47454@1" Pin0InfoVect0LinkObjId="SW-306154_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_27e66f0_0" Pin1InfoVect1LinkObjId="SW-306153_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="415,-213 450,-213 450,-197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27a80e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="450,-149 450,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_195aee0@0" ObjectIDZND0="47454@0" Pin0InfoVect0LinkObjId="SW-306154_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_195aee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="450,-149 450,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27a8340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="374,-237 374,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="47453@0" ObjectIDZND0="g_27e66f0@0" ObjectIDZND1="47454@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_27e66f0_0" Pin0InfoVect1LinkObjId="SW-306154_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306153_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="374,-237 374,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27af620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="333,-175 333,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_27a85a0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27a85a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="333,-175 333,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27af880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="333,-197 333,-213 374,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_27e66f0@0" ObjectIDZND1="47454@x" ObjectIDZND2="47453@x" Pin0InfoVect0LinkObjId="g_27e66f0_0" Pin0InfoVect1LinkObjId="SW-306154_0" Pin0InfoVect2LinkObjId="SW-306153_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="333,-197 333,-213 374,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27afae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="374,-38 374,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_27e66f0@0" ObjectIDZND1="47454@x" ObjectIDZND2="47453@x" Pin0InfoVect0LinkObjId="g_27e66f0_0" Pin0InfoVect1LinkObjId="SW-306154_0" Pin0InfoVect2LinkObjId="SW-306153_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="374,-38 374,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22c7150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="568,-237 568,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="47523@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1966200@0" ObjectIDZND2="g_22c63d0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1966200_0" Pin0InfoVect2LinkObjId="g_22c63d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306354_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="568,-237 568,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2789720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="527,-175 527,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_22c73b0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22c73b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="527,-175 527,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2789980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="527,-197 527,-213 568,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="47523@x" ObjectIDZND1="g_1966200@0" ObjectIDZND2="g_22c63d0@0" Pin0InfoVect0LinkObjId="SW-306354_0" Pin0InfoVect1LinkObjId="g_1966200_0" Pin0InfoVect2LinkObjId="g_22c63d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="527,-197 527,-213 568,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2951cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="568,-83 568,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="lightningRod" ObjectIDND0="g_1966200@0" ObjectIDZND0="47523@x" ObjectIDZND1="0@x" ObjectIDZND2="g_22c63d0@0" Pin0InfoVect0LinkObjId="SW-306354_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_22c63d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1966200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="568,-83 568,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f1a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="760,-331 760,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47456@1" ObjectIDZND0="47455@1" Pin0InfoVect0LinkObjId="SW-306157_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306158_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="760,-331 760,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1899ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="760,-281 760,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47455@0" ObjectIDZND0="47457@1" Pin0InfoVect0LinkObjId="SW-306158_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306157_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="760,-281 760,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ed680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-195 801,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="generator" ObjectIDND0="g_1959640@0" ObjectIDZND0="47457@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-306158_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1959640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="801,-195 801,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ed8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="760,-215 801,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="generator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47457@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1959640@0" ObjectIDZND1="47458@x" Pin0InfoVect0LinkObjId="g_1959640_0" Pin0InfoVect1LinkObjId="SW-306159_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306158_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="760,-215 801,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30edb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-215 836,-215 836,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="g_1959640@0" ObjectIDND1="47457@x" ObjectIDND2="0@x" ObjectIDZND0="47458@1" Pin0InfoVect0LinkObjId="SW-306159_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1959640_0" Pin1InfoVect1LinkObjId="SW-306158_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="801,-215 836,-215 836,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18a1620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="836,-151 836,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_18a0b90@0" ObjectIDZND0="47458@0" Pin0InfoVect0LinkObjId="SW-306159_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18a0b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="836,-151 836,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18a1880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="760,-239 760,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="47457@0" ObjectIDZND0="g_1959640@0" ObjectIDZND1="47458@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1959640_0" Pin0InfoVect1LinkObjId="SW-306159_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306158_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="760,-239 760,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26b49c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-177 719,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_26b2f10@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26b2f10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-177 719,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26b4c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-199 719,-215 760,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1959640@0" ObjectIDZND1="47458@x" ObjectIDZND2="47457@x" Pin0InfoVect0LinkObjId="g_1959640_0" Pin0InfoVect1LinkObjId="SW-306159_0" Pin0InfoVect2LinkObjId="SW-306158_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="719,-199 719,-215 760,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3134050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="760,-40 760,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1959640@0" ObjectIDZND1="47458@x" ObjectIDZND2="47457@x" Pin0InfoVect0LinkObjId="g_1959640_0" Pin0InfoVect1LinkObjId="SW-306159_0" Pin0InfoVect2LinkObjId="SW-306158_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="760,-40 760,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27ad830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="966,-330 966,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47460@1" ObjectIDZND0="47459@1" Pin0InfoVect0LinkObjId="SW-306162_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306163_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="966,-330 966,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31189a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="966,-280 966,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47459@0" ObjectIDZND0="47461@1" Pin0InfoVect0LinkObjId="SW-306163_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306162_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="966,-280 966,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_277cf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1007,-194 1007,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="generator" ObjectIDND0="g_277c200@0" ObjectIDZND0="47461@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-306163_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_277c200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1007,-194 1007,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_277d1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="966,-214 1007,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="generator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47461@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_277c200@0" ObjectIDZND1="47462@x" Pin0InfoVect0LinkObjId="g_277c200_0" Pin0InfoVect1LinkObjId="SW-306164_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306163_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="966,-214 1007,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_277d440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1007,-214 1042,-214 1042,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="g_277c200@0" ObjectIDND1="47461@x" ObjectIDND2="0@x" ObjectIDZND0="47462@1" Pin0InfoVect0LinkObjId="SW-306164_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_277c200_0" Pin1InfoVect1LinkObjId="SW-306163_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1007,-214 1042,-214 1042,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18b33a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1042,-150 1042,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_18b7610@0" ObjectIDZND0="47462@0" Pin0InfoVect0LinkObjId="SW-306164_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18b7610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1042,-150 1042,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18b3600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="966,-238 966,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="47461@0" ObjectIDZND0="g_277c200@0" ObjectIDZND1="47462@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_277c200_0" Pin0InfoVect1LinkObjId="SW-306164_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306163_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="966,-238 966,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18b52f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="925,-176 925,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_18b3860@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18b3860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="925,-176 925,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18b5550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="925,-198 925,-214 966,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_277c200@0" ObjectIDZND1="47462@x" ObjectIDZND2="47461@x" Pin0InfoVect0LinkObjId="g_277c200_0" Pin0InfoVect1LinkObjId="SW-306164_0" Pin0InfoVect2LinkObjId="SW-306163_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="925,-198 925,-214 966,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18b57b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="966,-39 966,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_277c200@0" ObjectIDZND1="47462@x" ObjectIDZND2="47461@x" Pin0InfoVect0LinkObjId="g_277c200_0" Pin0InfoVect1LinkObjId="SW-306164_0" Pin0InfoVect2LinkObjId="SW-306163_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="966,-39 966,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_279e940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1158,-332 1158,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47464@1" ObjectIDZND0="47463@1" Pin0InfoVect0LinkObjId="SW-306167_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306168_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1158,-332 1158,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_279eba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1158,-282 1158,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47463@0" ObjectIDZND0="47465@1" Pin0InfoVect0LinkObjId="SW-306168_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306167_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1158,-282 1158,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_218ad30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1199,-196 1199,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="generator" ObjectIDND0="g_22cb120@0" ObjectIDZND0="47465@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-306168_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22cb120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1199,-196 1199,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_218af90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1158,-216 1199,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="generator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47465@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_22cb120@0" ObjectIDZND1="47466@x" Pin0InfoVect0LinkObjId="g_22cb120_0" Pin0InfoVect1LinkObjId="SW-306169_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306168_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1158,-216 1199,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_218b1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1199,-216 1234,-216 1234,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="g_22cb120@0" ObjectIDND1="47465@x" ObjectIDND2="0@x" ObjectIDZND0="47466@1" Pin0InfoVect0LinkObjId="SW-306169_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22cb120_0" Pin1InfoVect1LinkObjId="SW-306168_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1199,-216 1234,-216 1234,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_287d4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1234,-152 1234,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_287ca40@0" ObjectIDZND0="47466@0" Pin0InfoVect0LinkObjId="SW-306169_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_287ca40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1234,-152 1234,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_287d730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1158,-240 1158,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="47465@0" ObjectIDZND0="g_22cb120@0" ObjectIDZND1="47466@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_22cb120_0" Pin0InfoVect1LinkObjId="SW-306169_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306168_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1158,-240 1158,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f8c980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1117,-178 1117,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_287d990@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_287d990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1117,-178 1117,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f8cbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1117,-200 1117,-216 1158,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_22cb120@0" ObjectIDZND1="47466@x" ObjectIDZND2="47465@x" Pin0InfoVect0LinkObjId="g_22cb120_0" Pin0InfoVect1LinkObjId="SW-306169_0" Pin0InfoVect2LinkObjId="SW-306168_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1117,-200 1117,-216 1158,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f8ce40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1158,-41 1158,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_22cb120@0" ObjectIDZND1="47466@x" ObjectIDZND2="47465@x" Pin0InfoVect0LinkObjId="g_22cb120_0" Pin0InfoVect1LinkObjId="SW-306169_0" Pin0InfoVect2LinkObjId="SW-306168_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1158,-41 1158,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25db800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="120,-32 182,-32 182,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="47448@0" ObjectIDZND0="g_27b34b0@0" ObjectIDZND1="47447@x" ObjectIDZND2="47446@x" Pin0InfoVect0LinkObjId="g_27b34b0_0" Pin0InfoVect1LinkObjId="SW-306146_0" Pin0InfoVect2LinkObjId="SW-306145_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306147_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="120,-32 182,-32 182,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab4280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="59,-102 59,-87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2ab37f0@0" ObjectIDZND0="47449@1" Pin0InfoVect0LinkObjId="SW-306148_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ab37f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="59,-102 59,-87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ab44e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="64,-69 104,-69 104,-40 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="64,-69 104,-69 104,-40 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2444600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="59,-51 59,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="47449@0" ObjectIDZND0="47448@x" ObjectIDZND1="g_2444ac0@0" ObjectIDZND2="47450@x" Pin0InfoVect0LinkObjId="SW-306147_0" Pin0InfoVect1LinkObjId="g_2444ac0_0" Pin0InfoVect2LinkObjId="SW-306149_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306148_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="59,-51 59,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2444860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="59,-32 84,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="47449@x" ObjectIDND1="g_2444ac0@0" ObjectIDND2="47450@x" ObjectIDZND0="47448@1" Pin0InfoVect0LinkObjId="SW-306147_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306148_0" Pin1InfoVect1LinkObjId="g_2444ac0_0" Pin1InfoVect2LinkObjId="SW-306149_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="59,-32 84,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24457b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="37,-13 37,-33 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2444ac0@0" ObjectIDZND0="47450@x" ObjectIDZND1="47449@x" ObjectIDZND2="47448@x" Pin0InfoVect0LinkObjId="SW-306149_0" Pin0InfoVect1LinkObjId="SW-306148_0" Pin0InfoVect2LinkObjId="SW-306147_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2444ac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="37,-13 37,-33 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2446250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="16,-32 37,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="47450@x" ObjectIDZND0="g_2444ac0@0" ObjectIDZND1="47449@x" ObjectIDZND2="47448@x" Pin0InfoVect0LinkObjId="g_2444ac0_0" Pin0InfoVect1LinkObjId="SW-306148_0" Pin0InfoVect2LinkObjId="SW-306147_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306149_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="16,-32 37,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24464b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="37,-32 60,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2444ac0@0" ObjectIDND1="47450@x" ObjectIDZND0="47449@x" ObjectIDZND1="47448@x" Pin0InfoVect0LinkObjId="SW-306148_0" Pin0InfoVect1LinkObjId="SW-306147_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2444ac0_0" Pin1InfoVect1LinkObjId="SW-306149_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="37,-32 60,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_190eba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2710,560 2677,560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="reactance" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2710,560 2677,560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_190ee00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2613,560 2613,517 2593,517 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="47521@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-306351_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2613,560 2613,517 2593,517 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_190f060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2566,517 2546,517 2546,560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDZND0="47521@x" ObjectIDZND1="g_19f2cf0@0" ObjectIDZND2="47520@x" Pin0InfoVect0LinkObjId="SW-306351_0" Pin0InfoVect1LinkObjId="g_19f2cf0_0" Pin0InfoVect2LinkObjId="SW-306350_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2566,517 2546,517 2546,560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_190f2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2561,560 2546,560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="47521@1" ObjectIDZND0="g_19f2cf0@0" ObjectIDZND1="47520@x" ObjectIDZND2="47519@x" Pin0InfoVect0LinkObjId="g_19f2cf0_0" Pin0InfoVect1LinkObjId="SW-306350_0" Pin0InfoVect2LinkObjId="SW-306349_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306351_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2561,560 2546,560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_190f520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2635,560 2613,560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="47521@x" Pin0InfoVect0LinkObjId="SW-306351_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2635,560 2613,560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21f2f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2613,560 2597,560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="47521@0" Pin0InfoVect0LinkObjId="SW-306351_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2613,560 2597,560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_278df80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2503,490 2503,505 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_278d4f0@0" ObjectIDZND0="47520@1" Pin0InfoVect0LinkObjId="SW-306350_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_278d4f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2503,490 2503,505 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19f25d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2498,523 2458,523 2458,552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2498,523 2458,523 2458,552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f2830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2503,541 2503,560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="47520@0" ObjectIDZND0="47519@x" ObjectIDZND1="g_19f2cf0@0" ObjectIDZND2="47521@x" Pin0InfoVect0LinkObjId="SW-306349_0" Pin0InfoVect1LinkObjId="g_19f2cf0_0" Pin0InfoVect2LinkObjId="SW-306351_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2503,541 2503,560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f2a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2503,560 2478,560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="47520@x" ObjectIDND1="g_19f2cf0@0" ObjectIDND2="47521@x" ObjectIDZND0="47519@1" Pin0InfoVect0LinkObjId="SW-306349_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306350_0" Pin1InfoVect1LinkObjId="g_19f2cf0_0" Pin1InfoVect2LinkObjId="SW-306351_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2503,560 2478,560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f38e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2525,579 2525,559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_19f2cf0@0" ObjectIDZND0="47521@x" ObjectIDZND1="47520@x" ObjectIDZND2="47519@x" Pin0InfoVect0LinkObjId="SW-306351_0" Pin0InfoVect1LinkObjId="SW-306350_0" Pin0InfoVect2LinkObjId="SW-306349_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19f2cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2525,579 2525,559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f3b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2546,560 2525,560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="47521@x" ObjectIDZND0="g_19f2cf0@0" ObjectIDZND1="47520@x" ObjectIDZND2="47519@x" Pin0InfoVect0LinkObjId="g_19f2cf0_0" Pin0InfoVect1LinkObjId="SW-306350_0" Pin0InfoVect2LinkObjId="SW-306349_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306351_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2546,560 2525,560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f3da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2525,560 2502,560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_19f2cf0@0" ObjectIDND1="47521@x" ObjectIDZND0="47520@x" ObjectIDZND1="47519@x" Pin0InfoVect0LinkObjId="SW-306350_0" Pin0InfoVect1LinkObjId="SW-306349_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_19f2cf0_0" Pin1InfoVect1LinkObjId="SW-306351_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2525,560 2502,560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27a2c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1559,-327 1559,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47468@1" ObjectIDZND0="47467@1" Pin0InfoVect0LinkObjId="SW-306172_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306173_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1559,-327 1559,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27a2e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1559,-277 1559,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47467@0" ObjectIDZND0="47469@1" Pin0InfoVect0LinkObjId="SW-306173_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306172_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1559,-277 1559,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19d4c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1600,-191 1600,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="generator" ObjectIDND0="g_22442f0@0" ObjectIDZND0="47469@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-306173_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22442f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1600,-191 1600,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19d4e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1559,-211 1600,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="generator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47469@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_22442f0@0" ObjectIDZND1="47470@x" Pin0InfoVect0LinkObjId="g_22442f0_0" Pin0InfoVect1LinkObjId="SW-306174_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306173_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1559,-211 1600,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19d50d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1600,-211 1635,-211 1635,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="g_22442f0@0" ObjectIDND1="47469@x" ObjectIDND2="0@x" ObjectIDZND0="47470@1" Pin0InfoVect0LinkObjId="SW-306174_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22442f0_0" Pin1InfoVect1LinkObjId="SW-306173_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1600,-211 1635,-211 1635,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2190750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1635,-147 1635,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_19d7750@0" ObjectIDZND0="47470@0" Pin0InfoVect0LinkObjId="SW-306174_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19d7750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1635,-147 1635,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21909b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1559,-235 1559,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="47469@0" ObjectIDZND0="g_22442f0@0" ObjectIDZND1="47470@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_22442f0_0" Pin0InfoVect1LinkObjId="SW-306174_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306173_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1559,-235 1559,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21926f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1518,-173 1518,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_2190c10@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2190c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1518,-173 1518,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2192950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1518,-195 1518,-211 1559,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_22442f0@0" ObjectIDZND1="47470@x" ObjectIDZND2="47469@x" Pin0InfoVect0LinkObjId="g_22442f0_0" Pin0InfoVect1LinkObjId="SW-306174_0" Pin0InfoVect2LinkObjId="SW-306173_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1518,-195 1518,-211 1559,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2192bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1559,-36 1559,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_22442f0@0" ObjectIDZND1="47470@x" ObjectIDZND2="47469@x" Pin0InfoVect0LinkObjId="g_22442f0_0" Pin0InfoVect1LinkObjId="SW-306174_0" Pin0InfoVect2LinkObjId="SW-306173_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1559,-36 1559,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23116b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1753,-327 1753,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47472@1" ObjectIDZND0="47471@1" Pin0InfoVect0LinkObjId="SW-306177_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306178_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1753,-327 1753,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23118f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1753,-277 1753,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47471@0" ObjectIDZND0="47473@1" Pin0InfoVect0LinkObjId="SW-306178_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306177_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1753,-277 1753,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a01490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1794,-191 1794,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="generator" ObjectIDND0="g_1a006e0@0" ObjectIDZND0="47473@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-306178_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a006e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1794,-191 1794,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_297db80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1753,-211 1794,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="generator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47473@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1a006e0@0" ObjectIDZND1="47474@x" Pin0InfoVect0LinkObjId="g_1a006e0_0" Pin0InfoVect1LinkObjId="SW-306179_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306178_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1753,-211 1794,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_297dde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1794,-211 1829,-211 1829,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="g_1a006e0@0" ObjectIDND1="47473@x" ObjectIDND2="0@x" ObjectIDZND0="47474@1" Pin0InfoVect0LinkObjId="SW-306179_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a006e0_0" Pin1InfoVect1LinkObjId="SW-306178_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1794,-211 1829,-211 1829,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1998d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1829,-147 1829,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2980580@0" ObjectIDZND0="47474@0" Pin0InfoVect0LinkObjId="SW-306179_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2980580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1829,-147 1829,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1998f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1753,-235 1753,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="47473@0" ObjectIDZND0="g_1a006e0@0" ObjectIDZND1="47474@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1a006e0_0" Pin0InfoVect1LinkObjId="SW-306179_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306178_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1753,-235 1753,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_199aba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1712,-173 1712,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_19991e0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19991e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1712,-173 1712,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_199ae00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1712,-195 1712,-211 1753,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1a006e0@0" ObjectIDZND1="47474@x" ObjectIDZND2="47473@x" Pin0InfoVect0LinkObjId="g_1a006e0_0" Pin0InfoVect1LinkObjId="SW-306179_0" Pin0InfoVect2LinkObjId="SW-306178_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1712,-195 1712,-211 1753,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_199b060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1753,-36 1753,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1a006e0@0" ObjectIDZND1="47474@x" ObjectIDZND2="47473@x" Pin0InfoVect0LinkObjId="g_1a006e0_0" Pin0InfoVect1LinkObjId="SW-306179_0" Pin0InfoVect2LinkObjId="SW-306178_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1753,-36 1753,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_193b5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1945,-329 1945,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47476@1" ObjectIDZND0="47475@1" Pin0InfoVect0LinkObjId="SW-306182_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306183_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1945,-329 1945,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_193b850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1945,-279 1945,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47475@0" ObjectIDZND0="47477@1" Pin0InfoVect0LinkObjId="SW-306183_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306182_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1945,-279 1945,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1961570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1986,-193 1986,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="generator" ObjectIDND0="g_19607c0@0" ObjectIDZND0="47477@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-306183_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19607c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1986,-193 1986,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19617d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1945,-213 1986,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="generator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47477@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_19607c0@0" ObjectIDZND1="47478@x" Pin0InfoVect0LinkObjId="g_19607c0_0" Pin0InfoVect1LinkObjId="SW-306184_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306183_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1945,-213 1986,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1961a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1986,-213 2021,-213 2021,-197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="g_19607c0@0" ObjectIDND1="47477@x" ObjectIDND2="0@x" ObjectIDZND0="47478@1" Pin0InfoVect0LinkObjId="SW-306184_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_19607c0_0" Pin1InfoVect1LinkObjId="SW-306183_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1986,-213 2021,-213 2021,-197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_242ddb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2021,-149 2021,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_242d320@0" ObjectIDZND0="47478@0" Pin0InfoVect0LinkObjId="SW-306184_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_242d320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2021,-149 2021,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_242e010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1945,-237 1945,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="47477@0" ObjectIDZND0="g_19607c0@0" ObjectIDZND1="47478@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_19607c0_0" Pin0InfoVect1LinkObjId="SW-306184_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306183_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1945,-237 1945,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_311bfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1904,-175 1904,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_242e270@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_242e270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1904,-175 1904,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_311c200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1904,-197 1904,-213 1945,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_19607c0@0" ObjectIDZND1="47478@x" ObjectIDZND2="47477@x" Pin0InfoVect0LinkObjId="g_19607c0_0" Pin0InfoVect1LinkObjId="SW-306184_0" Pin0InfoVect2LinkObjId="SW-306183_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1904,-197 1904,-213 1945,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_311c460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1945,-38 1945,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_19607c0@0" ObjectIDZND1="47478@x" ObjectIDZND2="47477@x" Pin0InfoVect0LinkObjId="g_19607c0_0" Pin0InfoVect1LinkObjId="SW-306184_0" Pin0InfoVect2LinkObjId="SW-306183_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1945,-38 1945,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_195d5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2151,-328 2151,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47480@1" ObjectIDZND0="47479@1" Pin0InfoVect0LinkObjId="SW-306187_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306188_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2151,-328 2151,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_195d850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2151,-278 2151,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47479@0" ObjectIDZND0="47481@1" Pin0InfoVect0LinkObjId="SW-306188_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306187_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2151,-278 2151,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2877e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2192,-192 2192,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="generator" ObjectIDND0="g_28770d0@0" ObjectIDZND0="47481@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-306188_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28770d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2192,-192 2192,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28780b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2151,-212 2192,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="generator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47481@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_28770d0@0" ObjectIDZND1="47482@x" Pin0InfoVect0LinkObjId="g_28770d0_0" Pin0InfoVect1LinkObjId="SW-306189_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306188_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2151,-212 2192,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2878310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2192,-212 2227,-212 2227,-196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="g_28770d0@0" ObjectIDND1="47481@x" ObjectIDND2="0@x" ObjectIDZND0="47482@1" Pin0InfoVect0LinkObjId="SW-306189_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28770d0_0" Pin1InfoVect1LinkObjId="SW-306188_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2192,-212 2227,-212 2227,-196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30dd890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2227,-148 2227,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_30dce00@0" ObjectIDZND0="47482@0" Pin0InfoVect0LinkObjId="SW-306189_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30dce00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2227,-148 2227,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ddaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2151,-236 2151,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="47481@0" ObjectIDZND0="g_28770d0@0" ObjectIDZND1="47482@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_28770d0_0" Pin0InfoVect1LinkObjId="SW-306189_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306188_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2151,-236 2151,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30df830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2110,-174 2110,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_30ddd50@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30ddd50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2110,-174 2110,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30dfa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2110,-196 2110,-212 2151,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_28770d0@0" ObjectIDZND1="47482@x" ObjectIDZND2="47481@x" Pin0InfoVect0LinkObjId="g_28770d0_0" Pin0InfoVect1LinkObjId="SW-306189_0" Pin0InfoVect2LinkObjId="SW-306188_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2110,-196 2110,-212 2151,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30dfcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2151,-37 2151,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_28770d0@0" ObjectIDZND1="47482@x" ObjectIDZND2="47481@x" Pin0InfoVect0LinkObjId="g_28770d0_0" Pin0InfoVect1LinkObjId="SW-306189_0" Pin0InfoVect2LinkObjId="SW-306188_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2151,-37 2151,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a3b7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2343,-330 2343,-307 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2343,-330 2343,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a3ba50">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2343,-280 2343,-255 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2343,-280 2343,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a2b3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2384,-194 2384,-214 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="generator" ObjectIDND0="g_1a2a640@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a2a640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2384,-194 2384,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a2b650">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2343,-214 2384,-214 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="generator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1a2a640@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1a2a640_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2343,-214 2384,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a2b8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2384,-214 2419,-214 2419,-198 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="g_1a2a640@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a2a640_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2384,-214 2419,-214 2419,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a2eaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2419,-150 2419,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a2e060@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a2e060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2419,-150 2419,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a2ed50">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2343,-238 2343,-214 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="0@0" ObjectIDZND0="g_1a2a640@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1a2a640_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2343,-238 2343,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19c2510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2302,-176 2302,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_1a2efb0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a2efb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2302,-176 2302,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19c2770">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2302,-198 2302,-214 2343,-214 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1a2a640@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1a2a640_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2302,-198 2302,-214 2343,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19c29d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2343,-39 2343,-217 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1a2a640@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1a2a640_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2343,-39 2343,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_312b690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1371,-331 1371,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47439@1" ObjectIDZND0="47438@1" Pin0InfoVect0LinkObjId="SW-306138_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306139_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1371,-331 1371,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_312b8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1371,-282 1371,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47438@0" ObjectIDZND0="47440@1" Pin0InfoVect0LinkObjId="SW-306139_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306138_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1371,-282 1371,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1965250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1330,-178 1330,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_1963770@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1963770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1330,-178 1330,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1965d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1371,-240 1371,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="47440@0" ObjectIDZND0="0@x" ObjectIDZND1="g_30d7b10@0" ObjectIDZND2="47526@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_30d7b10_0" Pin0InfoVect2LinkObjId="g_3137b30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306139_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1371,-240 1371,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1965fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1371,-216 1330,-216 1330,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="capacitor" ObjectIDND0="47440@x" ObjectIDND1="g_30d7b10@0" ObjectIDND2="47526@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306139_0" Pin1InfoVect1LinkObjId="g_30d7b10_0" Pin1InfoVect2LinkObjId="g_3137b30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1371,-216 1330,-216 1330,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25c32e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="183,154 183,177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47484@1" ObjectIDZND0="47483@1" Pin0InfoVect0LinkObjId="SW-306192_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306193_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="183,154 183,177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25c3540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="183,204 183,229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47483@0" ObjectIDZND0="47485@1" Pin0InfoVect0LinkObjId="SW-306193_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306192_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="183,204 183,229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18ad760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="224,290 224,270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="generator" ObjectIDND0="g_18ac9b0@0" ObjectIDZND0="47485@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-306193_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18ac9b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="224,290 224,270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25c5660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="183,270 224,270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="generator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47485@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_18ac9b0@0" ObjectIDZND1="47486@x" Pin0InfoVect0LinkObjId="g_18ac9b0_0" Pin0InfoVect1LinkObjId="SW-306194_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306193_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="183,270 224,270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25c58c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="224,270 259,270 259,286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="g_18ac9b0@0" ObjectIDND1="47485@x" ObjectIDND2="0@x" ObjectIDZND0="47486@1" Pin0InfoVect0LinkObjId="SW-306194_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_18ac9b0_0" Pin1InfoVect1LinkObjId="SW-306193_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="224,270 259,270 259,286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25c8af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="259,334 259,323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25c8060@0" ObjectIDZND0="47486@0" Pin0InfoVect0LinkObjId="SW-306194_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25c8060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="259,334 259,323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25c8d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="183,246 183,270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="47485@0" ObjectIDZND0="g_18ac9b0@0" ObjectIDZND1="47486@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_18ac9b0_0" Pin0InfoVect1LinkObjId="SW-306194_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306193_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="183,246 183,270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2317ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="142,308 142,296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_23166e0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23166e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="142,308 142,296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2318120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="142,286 142,270 183,270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_18ac9b0@0" ObjectIDZND1="47486@x" ObjectIDZND2="47485@x" Pin0InfoVect0LinkObjId="g_18ac9b0_0" Pin0InfoVect1LinkObjId="SW-306194_0" Pin0InfoVect2LinkObjId="SW-306193_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="142,286 142,270 183,270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18afd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="375,152 375,175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47488@1" ObjectIDZND0="47487@1" Pin0InfoVect0LinkObjId="SW-306197_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306198_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="375,152 375,175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18affb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="375,202 375,227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47487@0" ObjectIDZND0="47489@1" Pin0InfoVect0LinkObjId="SW-306198_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306197_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="375,202 375,227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30da9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,288 416,268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="generator" ObjectIDND0="g_30d9c30@0" ObjectIDZND0="47489@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-306198_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30d9c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="416,288 416,268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30dac40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="375,268 416,268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="generator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47489@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_30d9c30@0" ObjectIDZND1="47490@x" Pin0InfoVect0LinkObjId="g_30d9c30_0" Pin0InfoVect1LinkObjId="SW-306199_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306198_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="375,268 416,268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30daea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,268 451,268 451,284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="g_30d9c30@0" ObjectIDND1="47489@x" ObjectIDND2="0@x" ObjectIDZND0="47490@1" Pin0InfoVect0LinkObjId="SW-306199_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_30d9c30_0" Pin1InfoVect1LinkObjId="SW-306198_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="416,268 451,268 451,284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22b96d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="451,332 451,321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22b8c40@0" ObjectIDZND0="47490@0" Pin0InfoVect0LinkObjId="SW-306199_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22b8c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="451,332 451,321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22b9930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="375,244 375,268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="47489@0" ObjectIDZND0="g_30d9c30@0" ObjectIDZND1="47490@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_30d9c30_0" Pin0InfoVect1LinkObjId="SW-306199_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306198_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="375,244 375,268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22bb670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="334,306 334,294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_22b9b90@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22b9b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="334,306 334,294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22bb8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="334,284 334,268 375,268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_30d9c30@0" ObjectIDZND1="47490@x" ObjectIDZND2="47489@x" Pin0InfoVect0LinkObjId="g_30d9c30_0" Pin0InfoVect1LinkObjId="SW-306199_0" Pin0InfoVect2LinkObjId="SW-306198_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="334,284 334,268 375,268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_196bef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="761,150 761,173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47492@1" ObjectIDZND0="47491@1" Pin0InfoVect0LinkObjId="SW-306202_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306203_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="761,150 761,173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22be670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="761,200 761,225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47491@0" ObjectIDZND0="47493@1" Pin0InfoVect0LinkObjId="SW-306203_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306202_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="761,200 761,225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22d9fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="802,286 802,266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="generator" ObjectIDND0="g_22d9200@0" ObjectIDZND0="47493@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-306203_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22d9200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="802,286 802,266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22da210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="761,266 802,266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="generator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47493@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_22d9200@0" ObjectIDZND1="47494@x" Pin0InfoVect0LinkObjId="g_22d9200_0" Pin0InfoVect1LinkObjId="SW-306204_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306203_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="761,266 802,266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22da470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="802,266 837,266 837,282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="g_22d9200@0" ObjectIDND1="47493@x" ObjectIDND2="0@x" ObjectIDZND0="47494@1" Pin0InfoVect0LinkObjId="SW-306204_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22d9200_0" Pin1InfoVect1LinkObjId="SW-306203_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="802,266 837,266 837,282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_264b670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="837,330 837,319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22dcc10@0" ObjectIDZND0="47494@0" Pin0InfoVect0LinkObjId="SW-306204_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22dcc10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="837,330 837,319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_264b8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="761,242 761,266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="47493@0" ObjectIDZND0="g_22d9200@0" ObjectIDZND1="47494@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_22d9200_0" Pin0InfoVect1LinkObjId="SW-306204_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306203_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="761,242 761,266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_264d610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="720,304 720,292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_264bb30@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_264bb30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="720,304 720,292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_264d870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="720,282 720,266 761,266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_22d9200@0" ObjectIDZND1="47494@x" ObjectIDZND2="47493@x" Pin0InfoVect0LinkObjId="g_22d9200_0" Pin0InfoVect1LinkObjId="SW-306204_0" Pin0InfoVect2LinkObjId="SW-306203_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="720,282 720,266 761,266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c61330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="967,151 967,174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47496@1" ObjectIDZND0="47495@1" Pin0InfoVect0LinkObjId="SW-306207_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306208_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="967,151 967,174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c61590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="967,201 967,226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47495@0" ObjectIDZND0="47497@1" Pin0InfoVect0LinkObjId="SW-306208_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306207_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="967,201 967,226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30e56f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1008,267 1042,267 1042,288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="generator" EndDevType0="switch" ObjectIDND0="47497@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="47498@1" Pin0InfoVect0LinkObjId="SW-306209_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306208_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1008,267 1042,267 1042,288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2765ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1043,331 1043,323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2765460@0" ObjectIDZND0="47498@0" Pin0InfoVect0LinkObjId="SW-306209_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2765460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1043,331 1043,323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2766150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="967,243 967,267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="47497@0" ObjectIDZND0="0@x" ObjectIDZND1="g_30e4940@0" ObjectIDZND2="47498@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_30e4940_0" Pin0InfoVect2LinkObjId="SW-306209_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306208_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="967,243 967,267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2767e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="926,305 926,293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_27663b0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27663b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="926,305 926,293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27680f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="926,283 926,267 967,267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="47497@x" ObjectIDZND1="g_30e4940@0" ObjectIDZND2="47498@x" Pin0InfoVect0LinkObjId="SW-306208_0" Pin0InfoVect1LinkObjId="g_30e4940_0" Pin0InfoVect2LinkObjId="SW-306209_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="926,283 926,267 967,267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a96d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1159,149 1159,172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47500@1" ObjectIDZND0="47499@1" Pin0InfoVect0LinkObjId="SW-306212_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306213_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1159,149 1159,172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a9930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1159,199 1159,224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47499@0" ObjectIDZND0="47501@1" Pin0InfoVect0LinkObjId="SW-306213_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306212_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1159,199 1159,224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2769f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,285 1200,265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="generator" ObjectIDND0="g_2769190@0" ObjectIDZND0="47501@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-306213_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2769190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1200,285 1200,265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_276a180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1159,265 1200,265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="generator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47501@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_2769190@0" ObjectIDZND1="47502@x" Pin0InfoVect0LinkObjId="g_2769190_0" Pin0InfoVect1LinkObjId="SW-306214_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306213_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1159,265 1200,265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_276a3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,265 1235,265 1235,281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="g_2769190@0" ObjectIDND1="47501@x" ObjectIDND2="0@x" ObjectIDZND0="47502@1" Pin0InfoVect0LinkObjId="SW-306214_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2769190_0" Pin1InfoVect1LinkObjId="SW-306213_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1200,265 1235,265 1235,281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_276d610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1235,329 1235,318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_276cb80@0" ObjectIDZND0="47502@0" Pin0InfoVect0LinkObjId="SW-306214_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_276cb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1235,329 1235,318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_276d870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1159,241 1159,265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="47501@0" ObjectIDZND0="g_2769190@0" ObjectIDZND1="47502@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2769190_0" Pin0InfoVect1LinkObjId="SW-306214_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306213_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1159,241 1159,265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29a1ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1118,303 1118,291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_276dad0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_276dad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1118,303 1118,291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a1d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1118,281 1118,265 1159,265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2769190@0" ObjectIDZND1="47502@x" ObjectIDZND2="47501@x" Pin0InfoVect0LinkObjId="g_2769190_0" Pin0InfoVect1LinkObjId="SW-306214_0" Pin0InfoVect2LinkObjId="SW-306213_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1118,281 1118,265 1159,265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff1040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1560,154 1560,177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47504@1" ObjectIDZND0="47503@1" Pin0InfoVect0LinkObjId="SW-306217_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306218_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1560,154 1560,177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff12a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1560,204 1560,229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47503@0" ObjectIDZND0="47505@1" Pin0InfoVect0LinkObjId="SW-306218_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306217_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1560,204 1560,229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb5860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1601,290 1601,270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_1fb4ab0@0" ObjectIDZND0="47506@x" ObjectIDZND1="47505@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-306219_0" Pin0InfoVect1LinkObjId="SW-306218_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fb4ab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1601,290 1601,270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb5ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1601,270 1636,270 1636,286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="g_1fb4ab0@0" ObjectIDND1="47505@x" ObjectIDND2="0@x" ObjectIDZND0="47506@1" Pin0InfoVect0LinkObjId="SW-306219_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1fb4ab0_0" Pin1InfoVect1LinkObjId="SW-306218_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1601,270 1636,270 1636,286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aa9e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1636,334 1636,323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2aa93f0@0" ObjectIDZND0="47506@0" Pin0InfoVect0LinkObjId="SW-306219_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aa93f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1636,334 1636,323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aaa0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1560,246 1560,270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="47505@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1fb4ab0@0" ObjectIDZND2="47506@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1fb4ab0_0" Pin0InfoVect2LinkObjId="SW-306219_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306218_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1560,246 1560,270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2aabde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1519,308 1519,296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_2aaa300@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aaa300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1519,308 1519,296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aac040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1519,286 1519,270 1560,270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="47505@x" ObjectIDZND1="g_1fb4ab0@0" ObjectIDZND2="47506@x" Pin0InfoVect0LinkObjId="SW-306218_0" Pin0InfoVect1LinkObjId="g_1fb4ab0_0" Pin0InfoVect2LinkObjId="SW-306219_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1519,286 1519,270 1560,270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a230f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1754,154 1754,177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47508@1" ObjectIDZND0="47507@1" Pin0InfoVect0LinkObjId="SW-306222_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306337_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1754,154 1754,177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a23350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1754,204 1754,229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47507@0" ObjectIDZND0="47509@1" Pin0InfoVect0LinkObjId="SW-306337_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306222_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1754,204 1754,229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2990f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1795,290 1795,270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="generator" ObjectIDND0="g_2990180@0" ObjectIDZND0="47509@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-306337_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2990180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1795,290 1795,270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2991190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1754,270 1795,270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="generator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47509@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_2990180@0" ObjectIDZND1="47510@x" Pin0InfoVect0LinkObjId="g_2990180_0" Pin0InfoVect1LinkObjId="SW-306338_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306337_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1754,270 1795,270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29913f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1795,270 1830,270 1830,286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="g_2990180@0" ObjectIDND1="47509@x" ObjectIDND2="0@x" ObjectIDZND0="47510@1" Pin0InfoVect0LinkObjId="SW-306338_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2990180_0" Pin1InfoVect1LinkObjId="SW-306337_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1795,270 1830,270 1830,286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a1b8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1830,334 1830,323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a1ae30@0" ObjectIDZND0="47510@0" Pin0InfoVect0LinkObjId="SW-306338_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a1ae30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1830,334 1830,323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a1bb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1754,246 1754,270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="47509@0" ObjectIDZND0="g_2990180@0" ObjectIDZND1="47510@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2990180_0" Pin0InfoVect1LinkObjId="SW-306338_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306337_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1754,246 1754,270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a1d860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1713,308 1713,296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_1a1bd80@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a1bd80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1713,308 1713,296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a1dac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1713,286 1713,270 1754,270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2990180@0" ObjectIDZND1="47510@x" ObjectIDZND2="47509@x" Pin0InfoVect0LinkObjId="g_2990180_0" Pin0InfoVect1LinkObjId="SW-306338_0" Pin0InfoVect2LinkObjId="SW-306337_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1713,286 1713,270 1754,270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_250f4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1946,152 1946,175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47512@1" ObjectIDZND0="47511@1" Pin0InfoVect0LinkObjId="SW-306341_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306342_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1946,152 1946,175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_250f710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1946,202 1946,227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47511@0" ObjectIDZND0="47513@1" Pin0InfoVect0LinkObjId="SW-306342_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306341_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1946,202 1946,227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19b38c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1987,288 1987,268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="g_21730e0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="47513@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-306342_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21730e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1987,288 1987,268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19b3b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1946,268 1987,268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="generator" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="47513@x" ObjectIDZND0="g_21730e0@0" ObjectIDZND1="47514@x" Pin0InfoVect0LinkObjId="g_21730e0_0" Pin0InfoVect1LinkObjId="SW-306343_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-306342_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1946,268 1987,268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19b3d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1987,268 2022,268 2022,284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" BeginDevType2="generator" EndDevType0="switch" ObjectIDND0="g_21730e0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="47514@1" Pin0InfoVect0LinkObjId="SW-306343_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21730e0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1987,268 2022,268 2022,284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19b6e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2022,332 2022,321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_19b6380@0" ObjectIDZND0="47514@0" Pin0InfoVect0LinkObjId="SW-306343_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19b6380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2022,332 2022,321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30e65d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1905,306 1905,294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_19b7070@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19b7070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1905,306 1905,294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30e6830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1905,284 1905,268 1946,268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="0@0" ObjectIDZND0="g_21730e0@0" ObjectIDZND1="47514@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_21730e0_0" Pin0InfoVect1LinkObjId="SW-306343_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1905,284 1905,268 1946,268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30e6a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1946,443 1946,265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="0@0" ObjectIDZND0="g_21730e0@0" ObjectIDZND1="47514@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_21730e0_0" Pin0InfoVect1LinkObjId="SW-306343_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1946,443 1946,265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30eb350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2152,153 2152,176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47516@1" ObjectIDZND0="47515@1" Pin0InfoVect0LinkObjId="SW-306346_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306347_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2152,153 2152,176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30eb5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2152,203 2152,228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47515@0" ObjectIDZND0="47517@1" Pin0InfoVect0LinkObjId="SW-306347_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306346_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2152,203 2152,228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ec7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2193,289 2193,269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="switch" ObjectIDND0="g_22e1ec0@0" ObjectIDZND0="47517@x" ObjectIDZND1="0@x" ObjectIDZND2="47519@x" Pin0InfoVect0LinkObjId="SW-306347_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-306349_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22e1ec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2193,289 2193,269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25eca40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2152,269 2193,269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47517@x" ObjectIDND1="0@x" ObjectIDND2="47519@x" ObjectIDZND0="g_22e1ec0@0" ObjectIDZND1="47518@x" Pin0InfoVect0LinkObjId="g_22e1ec0_0" Pin0InfoVect1LinkObjId="SW-306348_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306347_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-306349_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2152,269 2193,269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ecca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2193,269 2228,269 2228,285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="g_22e1ec0@0" ObjectIDND1="47517@x" ObjectIDND2="0@x" ObjectIDZND0="47518@1" Pin0InfoVect0LinkObjId="SW-306348_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22e1ec0_0" Pin1InfoVect1LinkObjId="SW-306347_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2193,269 2228,269 2228,285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25efed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2228,333 2228,322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25ef440@0" ObjectIDZND0="47518@0" Pin0InfoVect0LinkObjId="SW-306348_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25ef440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2228,333 2228,322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25f0130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2152,245 2152,269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="47517@0" ObjectIDZND0="g_22e1ec0@0" ObjectIDZND1="47518@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_22e1ec0_0" Pin0InfoVect1LinkObjId="SW-306348_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306347_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2152,245 2152,269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18e4180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2111,307 2111,295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_25f0390@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25f0390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2111,307 2111,295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18e43e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2111,285 2111,269 2152,269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_22e1ec0@0" ObjectIDZND1="47518@x" ObjectIDZND2="47517@x" Pin0InfoVect0LinkObjId="g_22e1ec0_0" Pin0InfoVect1LinkObjId="SW-306348_0" Pin0InfoVect2LinkObjId="SW-306347_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2111,285 2111,269 2152,269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18e75e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2344,151 2344,174 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2344,151 2344,174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18e7840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2344,201 2344,226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2344,201 2344,226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27ca8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2385,287 2385,267 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="generator" ObjectIDND0="g_27c9af0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27c9af0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2385,287 2385,267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27cab00">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2344,267 2385,267 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="generator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_27c9af0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_27c9af0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2344,267 2385,267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27cad60">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2385,267 2420,267 2420,283 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="g_27c9af0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_27c9af0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2385,267 2420,267 2420,283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_312e840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2420,331 2420,320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_312ddb0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_312ddb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2420,331 2420,320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_312eaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2344,243 2344,267 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="0@0" ObjectIDZND0="g_27c9af0@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_27c9af0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2344,243 2344,267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3130b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2303,305 2303,293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_312f330@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_312f330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2303,305 2303,293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3130d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2303,283 2303,267 2344,267 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_27c9af0@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_27c9af0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2303,283 2303,267 2344,267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3130fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2344,442 2344,264 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_27c9af0@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_27c9af0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2344,442 2344,264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2533d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1372,150 1372,173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47442@1" ObjectIDZND0="47441@1" Pin0InfoVect0LinkObjId="SW-306140_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306141_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1372,150 1372,173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2533f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1372,199 1372,224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47441@0" ObjectIDZND0="47443@1" Pin0InfoVect0LinkObjId="SW-306141_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1372,199 1372,224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30f3bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1331,303 1331,291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_30f2110@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30f2110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1331,303 1331,291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30f3e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1372,382 1372,265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="47443@x" ObjectIDND1="0@x" ObjectIDZND0="47443@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-306141_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-306141_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1372,382 1372,265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30f40b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1372,241 1372,265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="47443@0" ObjectIDZND0="0@x" ObjectIDZND1="47443@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-306141_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306141_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1372,241 1372,265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30f4310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1372,265 1331,265 1331,281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="capacitor" ObjectIDND0="47443@x" ObjectIDND1="47443@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306141_0" Pin1InfoVect1LinkObjId="SW-306141_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1372,265 1331,265 1331,281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_190b580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1372,382 1373,382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="47443@x" ObjectIDND1="0@x" ObjectIDZND0="47443@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-306141_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-306141_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1372,382 1373,382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_190b7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1476,382 1372,382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="capacitor" EndDevType2="switch" ObjectIDZND0="47443@x" ObjectIDZND1="0@x" ObjectIDZND2="47443@x" Pin0InfoVect0LinkObjId="SW-306141_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-306141_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1476,382 1372,382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a13440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-487 1475,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="g_30d7b10@0" ObjectIDND1="47526@x" ObjectIDZND0="47440@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-306139_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_30d7b10_0" Pin1InfoVect1LinkObjId="g_3137b30_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-487 1475,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a136a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-98 1475,382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="switch" ObjectIDND0="g_30d7b10@0" ObjectIDND1="47526@x" ObjectIDND2="47440@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_30d7b10_0" Pin1InfoVect1LinkObjId="g_3137b30_0" Pin1InfoVect2LinkObjId="SW-306139_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-98 1475,382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a13900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-98 1371,-98 1371,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="g_30d7b10@0" ObjectIDND1="47526@x" ObjectIDZND0="47440@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-306139_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_30d7b10_0" Pin1InfoVect1LinkObjId="g_3137b30_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-98 1371,-98 1371,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a13b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="183,448 183,270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_18ac9b0@0" ObjectIDZND1="47486@x" ObjectIDZND2="47485@x" Pin0InfoVect0LinkObjId="g_18ac9b0_0" Pin0InfoVect1LinkObjId="SW-306194_0" Pin0InfoVect2LinkObjId="SW-306193_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="183,448 183,270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a15350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2443,560 2152,560 2152,269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="47519@0" ObjectIDZND0="g_22e1ec0@0" ObjectIDZND1="47518@x" ObjectIDZND2="47517@x" Pin0InfoVect0LinkObjId="g_22e1ec0_0" Pin0InfoVect1LinkObjId="SW-306348_0" Pin0InfoVect2LinkObjId="SW-306347_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306349_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2443,560 2152,560 2152,269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a15750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="568,-254 568,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="47523@1" ObjectIDZND0="47522@1" Pin0InfoVect0LinkObjId="SW-306354_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306354_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="568,-254 568,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a159b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="609,-193 609,-213 568,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="voltageTransformer" ObjectIDND0="g_22c63d0@0" ObjectIDZND0="47523@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1966200@0" Pin0InfoVect0LinkObjId="SW-306354_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1966200_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22c63d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="609,-193 609,-213 568,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a176f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="613,-327 613,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_1a15c10@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a15c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="613,-327 613,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a17950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="613,-349 613,-365 568,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="0@0" ObjectIDZND0="47522@x" ObjectIDZND1="47426@0" Pin0InfoVect0LinkObjId="SW-306354_0" Pin0InfoVect1LinkObjId="g_25ac8a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="613,-349 613,-365 568,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1956130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,240 576,264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="47525@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1955380@0" ObjectIDZND2="g_26fe400@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1955380_0" Pin0InfoVect2LinkObjId="g_26fe400_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306356_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="576,240 576,264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26fdce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="535,302 535,290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_1956390@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1956390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="535,302 535,290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26fdf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="535,280 535,264 576,264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="47525@x" ObjectIDZND1="g_1955380@0" ObjectIDZND2="g_26fe400@0" Pin0InfoVect0LinkObjId="SW-306356_0" Pin0InfoVect1LinkObjId="g_1955380_0" Pin0InfoVect2LinkObjId="g_26fe400_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="535,280 535,264 576,264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26fe1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,394 576,264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_26fe400@0" ObjectIDZND0="0@x" ObjectIDZND1="47525@x" ObjectIDZND2="g_1955380@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-306356_0" Pin0InfoVect2LinkObjId="g_1955380_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26fe400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="576,394 576,264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2701220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,223 576,148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="47525@1" ObjectIDZND0="47524@1" Pin0InfoVect0LinkObjId="SW-306356_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306356_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,223 576,148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2701480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="617,284 617,264 576,264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_1955380@0" ObjectIDZND0="0@x" ObjectIDZND1="47525@x" ObjectIDZND2="g_26fe400@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-306356_0" Pin0InfoVect2LinkObjId="g_26fe400_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1955380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="617,284 617,264 576,264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27d2140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="621,150 621,138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_27016e0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27016e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="621,150 621,138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27d23a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="621,128 621,112 576,112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="0@0" ObjectIDZND0="47524@x" ObjectIDZND1="47427@0" Pin0InfoVect0LinkObjId="SW-306356_0" Pin0InfoVect1LinkObjId="g_27d42a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="621,128 621,112 576,112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27d42a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="183,137 183,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47484@0" ObjectIDZND0="47427@0" Pin0InfoVect0LinkObjId="g_27d4980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306193_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="183,137 183,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27d4980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="375,135 375,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47488@0" ObjectIDZND0="47427@0" Pin0InfoVect0LinkObjId="g_27d42a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306198_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="375,135 375,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27d5920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,131 576,112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="busSection" ObjectIDND0="47524@0" ObjectIDZND0="0@x" ObjectIDZND1="47427@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_27d42a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306356_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="576,131 576,112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27d5b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,112 576,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="0@x" ObjectIDND1="47524@x" ObjectIDZND0="47427@0" Pin0InfoVect0LinkObjId="g_27d42a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-306356_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,112 576,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27d63b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="761,133 761,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47492@0" ObjectIDZND0="47427@0" Pin0InfoVect0LinkObjId="g_27d42a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306203_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="761,133 761,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27d6be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="967,134 967,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47496@0" ObjectIDZND0="47427@0" Pin0InfoVect0LinkObjId="g_27d42a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306208_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="967,134 967,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27d7410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1159,132 1159,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47500@0" ObjectIDZND0="47427@0" Pin0InfoVect0LinkObjId="g_27d42a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306213_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1159,132 1159,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27d7c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1372,133 1372,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47442@0" ObjectIDZND0="47427@0" Pin0InfoVect0LinkObjId="g_27d42a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306141_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1372,133 1372,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27d8470">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2344,134 2344,97 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="47427@0" Pin0InfoVect0LinkObjId="g_27d42a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2344,134 2344,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25aa7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2152,136 2152,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47516@0" ObjectIDZND0="47427@0" Pin0InfoVect0LinkObjId="g_27d42a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306347_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2152,136 2152,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ab010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1946,135 1946,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47512@0" ObjectIDZND0="47427@0" Pin0InfoVect0LinkObjId="g_27d42a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306342_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1946,135 1946,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ab840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1754,137 1754,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47508@0" ObjectIDZND0="47427@0" Pin0InfoVect0LinkObjId="g_27d42a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306337_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1754,137 1754,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ac070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1560,137 1560,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47504@0" ObjectIDZND0="47427@0" Pin0InfoVect0LinkObjId="g_27d42a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306218_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1560,137 1560,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ac8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="182,-344 182,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47445@0" ObjectIDZND0="47426@0" Pin0InfoVect0LinkObjId="g_25ad0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306145_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="182,-344 182,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ad0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="374,-346 374,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47452@0" ObjectIDZND0="47426@0" Pin0InfoVect0LinkObjId="g_25ac8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306153_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="374,-346 374,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ae190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="568,-346 568,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="busSection" ObjectIDND0="47522@0" ObjectIDZND0="0@x" ObjectIDZND1="47426@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_25ac8a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306354_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="568,-346 568,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ae3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="568,-365 568,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="0@x" ObjectIDND1="47522@x" ObjectIDZND0="47426@0" Pin0InfoVect0LinkObjId="g_25ac8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-306354_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="568,-365 568,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25aec20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="760,-348 760,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47456@0" ObjectIDZND0="47426@0" Pin0InfoVect0LinkObjId="g_25ac8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306158_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="760,-348 760,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25af450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="966,-347 966,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47460@0" ObjectIDZND0="47426@0" Pin0InfoVect0LinkObjId="g_25ac8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306163_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="966,-347 966,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25afc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1158,-349 1158,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47464@0" ObjectIDZND0="47426@0" Pin0InfoVect0LinkObjId="g_25ac8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306168_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1158,-349 1158,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b04b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1371,-348 1371,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47439@0" ObjectIDZND0="47426@0" Pin0InfoVect0LinkObjId="g_25ac8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306139_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1371,-348 1371,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24db550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1559,-344 1559,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47468@0" ObjectIDZND0="47426@0" Pin0InfoVect0LinkObjId="g_25ac8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306173_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1559,-344 1559,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24dbd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1753,-344 1753,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47472@0" ObjectIDZND0="47426@0" Pin0InfoVect0LinkObjId="g_25ac8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306178_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1753,-344 1753,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24dc570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1945,-346 1945,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47476@0" ObjectIDZND0="47426@0" Pin0InfoVect0LinkObjId="g_25ac8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306183_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1945,-346 1945,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24dcd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2151,-345 2151,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47480@0" ObjectIDZND0="47426@0" Pin0InfoVect0LinkObjId="g_25ac8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306188_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2151,-345 2151,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24dd590">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2343,-347 2343,-384 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="47426@0" Pin0InfoVect0LinkObjId="g_25ac8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2343,-347 2343,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_239b7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="967,267 1008,267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="generator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47497@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_30e4940@0" ObjectIDZND1="47498@x" Pin0InfoVect0LinkObjId="g_30e4940_0" Pin0InfoVect1LinkObjId="SW-306209_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306208_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="967,267 1008,267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_239b9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1008,267 1008,287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="generator" EndDevType0="lightningRod" ObjectIDND0="47497@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_30e4940@0" Pin0InfoVect0LinkObjId="g_30e4940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306208_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1008,267 1008,287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2176c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1946,244 1946,268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="47513@0" ObjectIDZND0="g_21730e0@0" ObjectIDZND1="47514@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_21730e0_0" Pin0InfoVect1LinkObjId="SW-306343_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306342_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1946,244 1946,268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2176df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1946,263 1946,262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1946,263 1946,262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2176fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="375,265 375,443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="generator" ObjectIDND0="g_30d9c30@0" ObjectIDND1="47490@x" ObjectIDND2="47489@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_30d9c30_0" Pin1InfoVect1LinkObjId="SW-306199_0" Pin1InfoVect2LinkObjId="SW-306198_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="375,265 375,443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21771d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="967,442 967,264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="47497@x" ObjectIDZND1="0@x" ObjectIDZND2="g_30e4940@0" Pin0InfoVect0LinkObjId="SW-306208_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_30e4940_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="967,442 967,264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2177400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1946,265 1946,267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_21730e0@0" ObjectIDND1="47514@x" ObjectIDND2="0@x" ObjectIDZND0="g_21730e0@0" ObjectIDZND1="47514@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_21730e0_0" Pin0InfoVect1LinkObjId="SW-306343_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21730e0_0" Pin1InfoVect1LinkObjId="SW-306343_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1946,265 1946,267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2177630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,268 762,446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="generator" ObjectIDND0="g_22d9200@0" ObjectIDND1="47494@x" ObjectIDND2="47493@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22d9200_0" Pin1InfoVect1LinkObjId="SW-306204_0" Pin1InfoVect2LinkObjId="SW-306203_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="762,268 762,446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2177860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1159,267 1159,445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="generator" ObjectIDND0="g_2769190@0" ObjectIDND1="47502@x" ObjectIDND2="47501@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2769190_0" Pin1InfoVect1LinkObjId="SW-306214_0" Pin1InfoVect2LinkObjId="SW-306213_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1159,267 1159,445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2177a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1560,270 1601,270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="generator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47505@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1fb4ab0@0" ObjectIDZND1="47506@x" Pin0InfoVect0LinkObjId="g_1fb4ab0_0" Pin0InfoVect1LinkObjId="SW-306219_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306218_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1560,270 1601,270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2177cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1560,270 1560,448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="lightningRod" EndDevType0="generator" ObjectIDND0="47505@x" ObjectIDND1="0@x" ObjectIDND2="g_1fb4ab0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-306218_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1fb4ab0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1560,270 1560,448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2177ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1754,270 1754,448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="generator" ObjectIDND0="g_2990180@0" ObjectIDND1="47510@x" ObjectIDND2="47509@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2990180_0" Pin1InfoVect1LinkObjId="SW-306338_0" Pin1InfoVect2LinkObjId="SW-306337_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1754,270 1754,448 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="47427" cx="183" cy="97" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47427" cx="375" cy="97" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47427" cx="576" cy="97" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47427" cx="761" cy="97" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47427" cx="967" cy="97" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47427" cx="1159" cy="97" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47427" cx="1372" cy="97" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47427" cx="2344" cy="97" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47427" cx="2152" cy="97" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47427" cx="1946" cy="97" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47427" cx="1754" cy="97" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47427" cx="1560" cy="97" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47426" cx="182" cy="-384" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47426" cx="374" cy="-384" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47426" cx="568" cy="-384" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47426" cx="760" cy="-384" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47426" cx="966" cy="-384" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47426" cx="1158" cy="-384" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47426" cx="1371" cy="-384" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47426" cx="1559" cy="-384" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47426" cx="1753" cy="-384" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47426" cx="1945" cy="-384" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47426" cx="2151" cy="-384" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47426" cx="2343" cy="-384" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-305278" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 198.000000 -1110.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47296" ObjectName="DYN-CX_YDLGF"/>
     <cge:Meas_Ref ObjectId="305278"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2201ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -3.000000 -1188.500000) translate(0,16)">矣得乐光伏电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3124b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -125.000000 -1047.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3124b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -125.000000 -1047.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3124b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -125.000000 -1047.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3124b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -125.000000 -1047.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3124b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -125.000000 -1047.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3124b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -125.000000 -1047.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3124b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -125.000000 -1047.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1ff8770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 20.000000 -371.000000) translate(0,16)">IM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_322d3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1500.000000 -543.000000) translate(0,12)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_29925b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.000000 -1228.000000) translate(0,17)">220kV安矣线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_312ed00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2427.311273 293.000000) translate(0,12)">39067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3132800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2354.311273 180.000000) translate(0,12)">390</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24ddda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -116.000000 15.000000) translate(0,15)">35kV1号SVG</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24de260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 338.000000 -16.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24de490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 519.000000 -23.000000) translate(0,15)">35kVⅠ母PT</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24de6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 533.000000 452.000000) translate(0,15)">35kVⅡ母PT</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24de910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 705.000000 -15.000000) translate(0,15)">#1-1储能装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24de910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 705.000000 -15.000000) translate(0,33)">(备用)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24dee00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 907.000000 -16.000000) translate(0,15)">35kV矣得乐</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24dee00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 907.000000 -16.000000) translate(0,33)">Ⅰ回集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24df940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1120.000000 -17.000000) translate(0,15)">35kV矣得乐</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24df940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1120.000000 -17.000000) translate(0,33)">Ⅳ回集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24dfbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1502.000000 -14.000000) translate(0,15)">35kV老延家</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24dfbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1502.000000 -14.000000) translate(0,33)">Ⅱ回集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24e0400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1693.000000 -9.000000) translate(0,15)">35kV喜鹊窝</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24e0400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1693.000000 -9.000000) translate(0,33)">Ⅱ回集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24e0fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1888.000000 -15.000000) translate(0,15)">35kV琅井哨</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24e0fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1888.000000 -15.000000) translate(0,33)">Ⅰ回集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19a08d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2092.000000 -17.000000) translate(0,15)">#1备用间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19a0e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2288.000000 -13.000000) translate(0,15)">1号滤波电容器组(预留)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19a1d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 117.000000 474.000000) translate(0,15)">#2-1储能装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19a1d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 117.000000 474.000000) translate(0,33)">(备用)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19a1f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 310.000000 470.000000) translate(0,15)">35kV矣得乐</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19a1f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 310.000000 470.000000) translate(0,33)">Ⅱ回集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19a21b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 703.000000 465.000000) translate(0,15)">35kV矣得乐</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19a21b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 703.000000 465.000000) translate(0,33)">Ⅲ回集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19a23f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 906.000000 459.000000) translate(0,15)">35kV老延家</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19a23f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 906.000000 459.000000) translate(0,33)">Ⅰ回集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19a2630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1106.000000 468.000000) translate(0,15)">35kV喜鹊窝</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19a2630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1106.000000 468.000000) translate(0,33)">Ⅰ回集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19a2870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1502.000000 472.000000) translate(0,15)">35kV琅井哨</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19a2870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1502.000000 472.000000) translate(0,33)">Ⅱ回集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19a2ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1903.000000 479.000000) translate(0,15)">#2备用间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19a2cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1690.000000 477.000000) translate(0,15)">#3备用间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19a2f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2352.000000 400.000000) translate(0,15)">2号滤波电容器组(预留)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19a3180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2586.000000 591.000000) translate(0,15)">35kV2号SVG</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a4ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1484.000000 -882.000000) translate(0,12)">271</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a5390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1482.000000 -971.000000) translate(0,12)">2716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a55d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1414.000000 -1038.000000) translate(0,12)">27167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a5810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1420.000000 -941.000000) translate(0,12)">27160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a5a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1482.000000 -802.000000) translate(0,12)">2711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a5c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1420.000000 -778.000000) translate(0,12)">27110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a5ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1419.000000 -862.000000) translate(0,12)">27117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a6110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1385.000000 -667.000000) translate(0,12)">2010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a6350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1360.000000 -599.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a6590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1516.000000 -664.000000) translate(0,12)">#1主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a67d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -303.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a6a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1382.000000 178.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19a6c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3.000000 73.000000) translate(0,12)">IIM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23961b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 192.000000 -298.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23963f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 265.000000 -183.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2396630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 86.000000 -58.000000) translate(0,12)">3717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2396870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 71.000000 -90.000000) translate(0,12)">37177</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2396ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -33.000000 -58.000000) translate(0,12)">3718</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2396cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 384.000000 -300.000000) translate(0,12)">372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2396f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 386.000000 -345.000000) translate(0,12)">372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2397170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 457.000000 -185.000000) translate(0,12)">37267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23973b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 517.000000 -311.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23975f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 770.000000 -302.000000) translate(0,12)">374</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2397830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 843.000000 -187.000000) translate(0,12)">37467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2397a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 976.000000 -301.000000) translate(0,12)">373</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2397cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1049.000000 -186.000000) translate(0,12)">37367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2397ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1168.000000 -303.000000) translate(0,12)">375</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2398130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1241.000000 -188.000000) translate(0,12)">37567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2398370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1569.000000 -298.000000) translate(0,12)">376</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23985b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1642.000000 -183.000000) translate(0,12)">37667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23987f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1763.000000 -298.000000) translate(0,12)">377</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2398a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1836.000000 -183.000000) translate(0,12)">37767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2398c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1955.000000 -300.000000) translate(0,12)">378</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2398eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2028.000000 -185.000000) translate(0,12)">37867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23990f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2161.000000 -299.000000) translate(0,12)">379</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2399330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2234.000000 -184.000000) translate(0,12)">37967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2399570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2426.311273 -188.000000) translate(0,12)">38067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23997b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2353.311273 -301.000000) translate(0,12)">380</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23999f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 193.000000 183.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2399c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 266.000000 297.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2399e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 385.000000 181.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239a0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 458.000000 295.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239a2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 516.000000 168.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239a530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 771.000000 179.000000) translate(0,12)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239a770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 844.000000 293.000000) translate(0,12)">38367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239a9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 977.000000 180.000000) translate(0,12)">384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239abf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 979.000000 135.000000) translate(0,12)">384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239ae30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1050.000000 297.000000) translate(0,12)">38467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239bbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1169.000000 178.000000) translate(0,12)">385</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239c000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1242.000000 292.000000) translate(0,12)">38567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239c240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1570.000000 183.000000) translate(0,12)">386</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239c480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1643.000000 297.000000) translate(0,12)">38667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239c6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1764.000000 183.000000) translate(0,12)">387</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239c900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1837.000000 297.000000) translate(0,12)">38767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239cb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1956.000000 181.000000) translate(0,12)">388</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1986f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2029.000000 295.000000) translate(0,12)">38867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19871d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2162.000000 182.000000) translate(0,12)">389</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1987410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2164.000000 137.000000) translate(0,12)">389</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1987650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2235.000000 296.000000) translate(0,12)">38967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1987890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2444.000000 534.000000) translate(0,12)">3897</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1987ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2522.000000 492.000000) translate(0,12)">38977</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1987d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2563.000000 534.000000) translate(0,12)">3898</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28948d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -55.000000 424.666667) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d2130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -60.000000 411.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d2300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -52.000000 466.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d2540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -52.000000 438.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297d4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -52.000000 452.333333) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297d7c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1561.000000 900.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_322cf30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1550.000000 885.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_322d170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1575.000000 870.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2799a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 270.000000 -30.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_279a080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 259.000000 -45.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_279a2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 284.000000 -60.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c90a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 111.000000 -518.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c96f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 100.000000 -533.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23164a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 125.000000 -548.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27d3370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -34.000000 -153.333333) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27d39a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -39.000000 -167.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27d3be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -31.000000 -112.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27d3e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -31.000000 -139.333333) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27d4060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -31.000000 -125.666667) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_198b4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 103.000000 -63.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_198b710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 78.000000 -48.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2178240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2181.000000 -494.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2178710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2156.000000 -479.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-306124">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1465.742135 -853.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47429" ObjectName="SW-CX_YDLGF.CX_YDLGF_271BK"/>
     <cge:Meas_Ref ObjectId="306124"/>
    <cge:TPSR_Ref TObjectID="47429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306144">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 173.437855 -269.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47444" ObjectName="SW-CX_YDLGF.CX_YDLGF_371BK"/>
     <cge:Meas_Ref ObjectId="306144"/>
    <cge:TPSR_Ref TObjectID="47444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306152">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 365.437855 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47451" ObjectName="SW-CX_YDLGF.CX_YDLGF_372BK"/>
     <cge:Meas_Ref ObjectId="306152"/>
    <cge:TPSR_Ref TObjectID="47451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306157">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 751.437855 -273.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47455" ObjectName="SW-CX_YDLGF.CX_YDLGF_374BK"/>
     <cge:Meas_Ref ObjectId="306157"/>
    <cge:TPSR_Ref TObjectID="47455"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306162">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 957.437855 -272.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47459" ObjectName="SW-CX_YDLGF.CX_YDLGF_373BK"/>
     <cge:Meas_Ref ObjectId="306162"/>
    <cge:TPSR_Ref TObjectID="47459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306167">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1149.437855 -274.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47463" ObjectName="SW-CX_YDLGF.CX_YDLGF_375BK"/>
     <cge:Meas_Ref ObjectId="306167"/>
    <cge:TPSR_Ref TObjectID="47463"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306172">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1550.437855 -269.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47467" ObjectName="SW-CX_YDLGF.CX_YDLGF_376BK"/>
     <cge:Meas_Ref ObjectId="306172"/>
    <cge:TPSR_Ref TObjectID="47467"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306177">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1744.437855 -269.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47471" ObjectName="SW-CX_YDLGF.CX_YDLGF_377BK"/>
     <cge:Meas_Ref ObjectId="306177"/>
    <cge:TPSR_Ref TObjectID="47471"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306182">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1936.437855 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47475" ObjectName="SW-CX_YDLGF.CX_YDLGF_378BK"/>
     <cge:Meas_Ref ObjectId="306182"/>
    <cge:TPSR_Ref TObjectID="47475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306187">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2142.437855 -270.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47479" ObjectName="SW-CX_YDLGF.CX_YDLGF_379BK"/>
     <cge:Meas_Ref ObjectId="306187"/>
    <cge:TPSR_Ref TObjectID="47479"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2334.437855 -272.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306138">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.994444 1362.437855 -273.677778)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47438" ObjectName="SW-CX_YDLGF.CX_YDLGF_301BK"/>
     <cge:Meas_Ref ObjectId="306138"/>
    <cge:TPSR_Ref TObjectID="47438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306192">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 174.437855 213.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47483" ObjectName="SW-CX_YDLGF.CX_YDLGF_381BK"/>
     <cge:Meas_Ref ObjectId="306192"/>
    <cge:TPSR_Ref TObjectID="47483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306197">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 366.437855 211.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47487" ObjectName="SW-CX_YDLGF.CX_YDLGF_382BK"/>
     <cge:Meas_Ref ObjectId="306197"/>
    <cge:TPSR_Ref TObjectID="47487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306202">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 752.437855 209.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47491" ObjectName="SW-CX_YDLGF.CX_YDLGF_383BK"/>
     <cge:Meas_Ref ObjectId="306202"/>
    <cge:TPSR_Ref TObjectID="47491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306207">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 958.437855 210.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47495" ObjectName="SW-CX_YDLGF.CX_YDLGF_384BK"/>
     <cge:Meas_Ref ObjectId="306207"/>
    <cge:TPSR_Ref TObjectID="47495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306212">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1150.437855 208.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47499" ObjectName="SW-CX_YDLGF.CX_YDLGF_385BK"/>
     <cge:Meas_Ref ObjectId="306212"/>
    <cge:TPSR_Ref TObjectID="47499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306217">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1551.437855 213.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47503" ObjectName="SW-CX_YDLGF.CX_YDLGF_386BK"/>
     <cge:Meas_Ref ObjectId="306217"/>
    <cge:TPSR_Ref TObjectID="47503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306222">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1745.437855 213.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47507" ObjectName="SW-CX_YDLGF.CX_YDLGF_387BK"/>
     <cge:Meas_Ref ObjectId="306222"/>
    <cge:TPSR_Ref TObjectID="47507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306341">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1937.437855 211.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47511" ObjectName="SW-CX_YDLGF.CX_YDLGF_388BK"/>
     <cge:Meas_Ref ObjectId="306341"/>
    <cge:TPSR_Ref TObjectID="47511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306346">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2143.437855 212.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47515" ObjectName="SW-CX_YDLGF.CX_YDLGF_389BK"/>
     <cge:Meas_Ref ObjectId="306346"/>
    <cge:TPSR_Ref TObjectID="47515"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2335.437855 210.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306140">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.994444 1363.437855 207.322222)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47441" ObjectName="SW-CX_YDLGF.CX_YDLGF_302BK"/>
     <cge:Meas_Ref ObjectId="306140"/>
    <cge:TPSR_Ref TObjectID="47441"/></metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 369.311273 -17.173913)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 755.311273 -19.173913)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 961.311273 -18.173913)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1153.311273 -20.173913)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1554.311273 -15.173913)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1748.311273 -15.173913)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1940.311273 -17.173913)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2146.311273 -16.173913)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2338.311273 -18.173913)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 370.311273 463.826087)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 756.311273 461.826087)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 962.311273 462.826087)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1154.311273 460.826087)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1555.311273 465.826087)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1749.311273 465.826087)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1941.311273 463.826087)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2339.311273 462.826087)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 178.311273 468.826087)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-220KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1479.584553 -1205.500000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -34.000000 -1140.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="173" x="-30" y="-1199"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="173" x="-30" y="-1199"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-79" y="-1216"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-79" y="-1216"/></g>
  </g><g id="Circle_Layer">
   <circle DF8003:Layer="PUBLIC" cx="1400" cy="-1100" fill="none" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.804311"/>
   <circle DF8003:Layer="PUBLIC" cx="1400" cy="-1089" fill="none" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.804311"/>
   <circle DF8003:Layer="PUBLIC" cx="1413" cy="-1087" fill="none" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.804311"/>
   <circle DF8003:Layer="PUBLIC" cx="1412" cy="-1100" fill="none" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.804311"/>
   <circle DF8003:Layer="PUBLIC" cx="1423" cy="-1094" fill="none" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.804311"/>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_YDLGF.CX_YDLGF_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="45134"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1433.742135 -550.000000)" xlink:href="#transformer:shape23_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="45136"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1433.742135 -550.000000)" xlink:href="#transformer:shape23_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="45138"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1433.742135 -550.000000)" xlink:href="#transformer:shape23-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="47526" ObjectName="TF-CX_YDLGF.CX_YDLGF_1T"/>
    <cge:TPSR_Ref TObjectID="47526"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1a53ec0">
    <use class="BV-220KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 1333.000000 -694.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27a5cc0">
    <use class="BV-220KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 1295.000000 -694.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30ed000">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1326.500000 -566.500000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27f6760">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1624.000000 -517.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29aa4f0">
    <use class="BV-220KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1554.000000 -1133.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30d7b10">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1402.000000 -480.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27b34b0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 230.311273 -134.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27a66d0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 148.000000 -135.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27e66f0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 422.311273 -136.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27a85a0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 340.000000 -137.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22c63d0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 616.311273 -136.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22c73b0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 534.000000 -137.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1959640">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 808.311273 -138.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26b2f10">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 726.000000 -139.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_277c200">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1014.311273 -137.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18b3860">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 932.000000 -138.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22cb120">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1206.311273 -139.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_287d990">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1124.000000 -140.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2444ac0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 44.311273 44.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f2cf0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 2517.688727 637.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22442f0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1607.311273 -134.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2190c10">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1525.000000 -135.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a006e0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1801.311273 -134.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19991e0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1719.000000 -135.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19607c0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1993.311273 -136.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_242e270">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1911.000000 -137.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28770d0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2199.311273 -135.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30ddd50">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2117.000000 -136.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a2a640">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2391.311273 -137.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a2efb0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2309.000000 -138.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1963770">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -0.994444 1337.000000 -140.422222)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18ac9b0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 231.311273 348.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23166e0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 149.000000 346.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30d9c30">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 423.311273 346.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22b9b90">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 341.000000 344.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22d9200">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 809.311273 344.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_264bb30">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 727.000000 342.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30e4940">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1015.311273 345.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27663b0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 933.000000 343.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2769190">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1207.311273 343.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_276dad0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1125.000000 341.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb4ab0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1608.311273 348.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aaa300">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1526.000000 346.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2990180">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1802.311273 348.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a1bd80">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1720.000000 346.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21730e0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1994.311273 346.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19b7070">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1912.000000 344.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22e1ec0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2200.311273 347.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25f0390">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2118.000000 345.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27c9af0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2392.311273 345.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_312f330">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2310.000000 343.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30f2110">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -0.994444 1338.000000 340.577778)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a15c10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 606.000000 -289.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1955380">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 624.311273 342.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1956390">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 542.000000 340.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27016e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 614.000000 188.000000)" xlink:href="#lightningRod:shape206"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_省调直调电厂_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="173" x="-30" y="-1199"/></g>
   <g href="cx_索引_接线图_省调直调电厂_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-79" y="-1216"/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="-171,-6 -176,-1 -176,-7 -168,-4 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="-179,-53 -185,-49 -185,-55 -179,-53 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2733,585 2738,590 2738,584 2730,587 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2741,538 2747,542 2747,536 2741,538 " stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306082" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1613.000000 -900.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306082" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47429"/>
     <cge:Term_Ref ObjectID="44939"/>
    <cge:TPSR_Ref TObjectID="47429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306083" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1613.000000 -900.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306083" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47429"/>
     <cge:Term_Ref ObjectID="44939"/>
    <cge:TPSR_Ref TObjectID="47429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306079" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1613.000000 -900.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306079" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47429"/>
     <cge:Term_Ref ObjectID="44939"/>
    <cge:TPSR_Ref TObjectID="47429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306237" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 338.000000 30.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306237" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47451"/>
     <cge:Term_Ref ObjectID="44983"/>
    <cge:TPSR_Ref TObjectID="47451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306238" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 338.000000 30.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306238" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47451"/>
     <cge:Term_Ref ObjectID="44983"/>
    <cge:TPSR_Ref TObjectID="47451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306234" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 338.000000 30.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306234" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47451"/>
     <cge:Term_Ref ObjectID="44983"/>
    <cge:TPSR_Ref TObjectID="47451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306243" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 729.000000 30.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306243" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47455"/>
     <cge:Term_Ref ObjectID="44991"/>
    <cge:TPSR_Ref TObjectID="47455"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306244" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 729.000000 30.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306244" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47455"/>
     <cge:Term_Ref ObjectID="44991"/>
    <cge:TPSR_Ref TObjectID="47455"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306240" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 729.000000 30.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306240" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47455"/>
     <cge:Term_Ref ObjectID="44991"/>
    <cge:TPSR_Ref TObjectID="47455"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306249" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 936.000000 30.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306249" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47459"/>
     <cge:Term_Ref ObjectID="44999"/>
    <cge:TPSR_Ref TObjectID="47459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306250" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 936.000000 30.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306250" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47459"/>
     <cge:Term_Ref ObjectID="44999"/>
    <cge:TPSR_Ref TObjectID="47459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306246" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 936.000000 30.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306246" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47459"/>
     <cge:Term_Ref ObjectID="44999"/>
    <cge:TPSR_Ref TObjectID="47459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306255" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1130.000000 30.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306255" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47463"/>
     <cge:Term_Ref ObjectID="45007"/>
    <cge:TPSR_Ref TObjectID="47463"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306256" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1130.000000 30.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306256" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47463"/>
     <cge:Term_Ref ObjectID="45007"/>
    <cge:TPSR_Ref TObjectID="47463"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306252" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1130.000000 30.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306252" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47463"/>
     <cge:Term_Ref ObjectID="45007"/>
    <cge:TPSR_Ref TObjectID="47463"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306095" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1287.000000 -314.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306095" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47438"/>
     <cge:Term_Ref ObjectID="44957"/>
    <cge:TPSR_Ref TObjectID="47438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306096" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1287.000000 -314.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306096" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47438"/>
     <cge:Term_Ref ObjectID="44957"/>
    <cge:TPSR_Ref TObjectID="47438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306092" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1287.000000 -314.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306092" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47438"/>
     <cge:Term_Ref ObjectID="44957"/>
    <cge:TPSR_Ref TObjectID="47438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306261" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1523.000000 30.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306261" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47467"/>
     <cge:Term_Ref ObjectID="45015"/>
    <cge:TPSR_Ref TObjectID="47467"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306262" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1523.000000 30.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306262" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47467"/>
     <cge:Term_Ref ObjectID="45015"/>
    <cge:TPSR_Ref TObjectID="47467"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306258" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1523.000000 30.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306258" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47467"/>
     <cge:Term_Ref ObjectID="45015"/>
    <cge:TPSR_Ref TObjectID="47467"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306267" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1719.000000 30.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306267" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47471"/>
     <cge:Term_Ref ObjectID="45023"/>
    <cge:TPSR_Ref TObjectID="47471"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306268" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1719.000000 30.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306268" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47471"/>
     <cge:Term_Ref ObjectID="45023"/>
    <cge:TPSR_Ref TObjectID="47471"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306264" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1719.000000 30.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306264" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47471"/>
     <cge:Term_Ref ObjectID="45023"/>
    <cge:TPSR_Ref TObjectID="47471"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306273" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1910.000000 30.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306273" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47475"/>
     <cge:Term_Ref ObjectID="45031"/>
    <cge:TPSR_Ref TObjectID="47475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306274" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1910.000000 30.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306274" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47475"/>
     <cge:Term_Ref ObjectID="45031"/>
    <cge:TPSR_Ref TObjectID="47475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306270" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1910.000000 30.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306270" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47475"/>
     <cge:Term_Ref ObjectID="45031"/>
    <cge:TPSR_Ref TObjectID="47475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306279" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2116.000000 30.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306279" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47479"/>
     <cge:Term_Ref ObjectID="45039"/>
    <cge:TPSR_Ref TObjectID="47479"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306280" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2116.000000 30.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306280" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47479"/>
     <cge:Term_Ref ObjectID="45039"/>
    <cge:TPSR_Ref TObjectID="47479"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306276" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2116.000000 30.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306276" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47479"/>
     <cge:Term_Ref ObjectID="45039"/>
    <cge:TPSR_Ref TObjectID="47479"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306285" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 169.000000 519.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306285" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47483"/>
     <cge:Term_Ref ObjectID="45047"/>
    <cge:TPSR_Ref TObjectID="47483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306286" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 169.000000 519.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306286" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47483"/>
     <cge:Term_Ref ObjectID="45047"/>
    <cge:TPSR_Ref TObjectID="47483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306282" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 169.000000 519.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306282" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47483"/>
     <cge:Term_Ref ObjectID="45047"/>
    <cge:TPSR_Ref TObjectID="47483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306291" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 361.000000 517.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306291" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47487"/>
     <cge:Term_Ref ObjectID="45055"/>
    <cge:TPSR_Ref TObjectID="47487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306292" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 361.000000 517.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306292" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47487"/>
     <cge:Term_Ref ObjectID="45055"/>
    <cge:TPSR_Ref TObjectID="47487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306288" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 361.000000 517.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306288" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47487"/>
     <cge:Term_Ref ObjectID="45055"/>
    <cge:TPSR_Ref TObjectID="47487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306297" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 747.000000 515.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306297" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47491"/>
     <cge:Term_Ref ObjectID="45063"/>
    <cge:TPSR_Ref TObjectID="47491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306298" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 747.000000 515.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306298" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47491"/>
     <cge:Term_Ref ObjectID="45063"/>
    <cge:TPSR_Ref TObjectID="47491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306294" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 747.000000 515.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306294" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47491"/>
     <cge:Term_Ref ObjectID="45063"/>
    <cge:TPSR_Ref TObjectID="47491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306303" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 953.000000 516.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306303" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47495"/>
     <cge:Term_Ref ObjectID="45071"/>
    <cge:TPSR_Ref TObjectID="47495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306304" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 953.000000 516.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306304" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47495"/>
     <cge:Term_Ref ObjectID="45071"/>
    <cge:TPSR_Ref TObjectID="47495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306300" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 953.000000 516.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306300" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47495"/>
     <cge:Term_Ref ObjectID="45071"/>
    <cge:TPSR_Ref TObjectID="47495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306309" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1145.000000 514.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306309" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47499"/>
     <cge:Term_Ref ObjectID="45079"/>
    <cge:TPSR_Ref TObjectID="47499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306310" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1145.000000 514.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306310" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47499"/>
     <cge:Term_Ref ObjectID="45079"/>
    <cge:TPSR_Ref TObjectID="47499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306306" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1145.000000 514.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306306" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47499"/>
     <cge:Term_Ref ObjectID="45079"/>
    <cge:TPSR_Ref TObjectID="47499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306107" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1284.000000 167.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306107" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47441"/>
     <cge:Term_Ref ObjectID="44963"/>
    <cge:TPSR_Ref TObjectID="47441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306108" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1284.000000 167.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306108" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47441"/>
     <cge:Term_Ref ObjectID="44963"/>
    <cge:TPSR_Ref TObjectID="47441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306104" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1284.000000 167.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306104" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47441"/>
     <cge:Term_Ref ObjectID="44963"/>
    <cge:TPSR_Ref TObjectID="47441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306315" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1546.000000 519.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306315" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47503"/>
     <cge:Term_Ref ObjectID="45087"/>
    <cge:TPSR_Ref TObjectID="47503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306316" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1546.000000 519.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306316" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47503"/>
     <cge:Term_Ref ObjectID="45087"/>
    <cge:TPSR_Ref TObjectID="47503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306312" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1546.000000 519.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306312" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47503"/>
     <cge:Term_Ref ObjectID="45087"/>
    <cge:TPSR_Ref TObjectID="47503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306321" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1740.000000 519.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306321" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47507"/>
     <cge:Term_Ref ObjectID="45095"/>
    <cge:TPSR_Ref TObjectID="47507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306322" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1740.000000 519.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306322" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47507"/>
     <cge:Term_Ref ObjectID="45095"/>
    <cge:TPSR_Ref TObjectID="47507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306318" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1740.000000 519.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306318" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47507"/>
     <cge:Term_Ref ObjectID="45095"/>
    <cge:TPSR_Ref TObjectID="47507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306327" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1932.000000 517.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306327" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47511"/>
     <cge:Term_Ref ObjectID="45103"/>
    <cge:TPSR_Ref TObjectID="47511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306328" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1932.000000 517.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306328" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47511"/>
     <cge:Term_Ref ObjectID="45103"/>
    <cge:TPSR_Ref TObjectID="47511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306324" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1932.000000 517.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306324" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47511"/>
     <cge:Term_Ref ObjectID="45103"/>
    <cge:TPSR_Ref TObjectID="47511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306333" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2221.000000 478.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306333" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47515"/>
     <cge:Term_Ref ObjectID="45111"/>
    <cge:TPSR_Ref TObjectID="47515"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306330" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2221.000000 478.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306330" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47515"/>
     <cge:Term_Ref ObjectID="45111"/>
    <cge:TPSR_Ref TObjectID="47515"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306233" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 146.000000 50.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306233" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47444"/>
     <cge:Term_Ref ObjectID="44969"/>
    <cge:TPSR_Ref TObjectID="47444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306230" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 146.000000 50.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306230" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47444"/>
     <cge:Term_Ref ObjectID="44969"/>
    <cge:TPSR_Ref TObjectID="47444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-306114" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5.000000 -465.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306114" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47426"/>
     <cge:Term_Ref ObjectID="44935"/>
    <cge:TPSR_Ref TObjectID="47426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-306115" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5.000000 -465.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306115" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47426"/>
     <cge:Term_Ref ObjectID="44935"/>
    <cge:TPSR_Ref TObjectID="47426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-306116" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5.000000 -465.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306116" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47426"/>
     <cge:Term_Ref ObjectID="44935"/>
    <cge:TPSR_Ref TObjectID="47426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-306120" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5.000000 -465.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306120" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47426"/>
     <cge:Term_Ref ObjectID="44935"/>
    <cge:TPSR_Ref TObjectID="47426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-306117" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5.000000 -465.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306117" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47426"/>
     <cge:Term_Ref ObjectID="44935"/>
    <cge:TPSR_Ref TObjectID="47426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-306122" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 25.000000 111.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306122" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47427"/>
     <cge:Term_Ref ObjectID="44936"/>
    <cge:TPSR_Ref TObjectID="47427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-306223" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 25.000000 111.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306223" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47427"/>
     <cge:Term_Ref ObjectID="44936"/>
    <cge:TPSR_Ref TObjectID="47427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-306224" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 25.000000 111.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306224" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47427"/>
     <cge:Term_Ref ObjectID="44936"/>
    <cge:TPSR_Ref TObjectID="47427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-306228" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 25.000000 111.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306228" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47427"/>
     <cge:Term_Ref ObjectID="44936"/>
    <cge:TPSR_Ref TObjectID="47427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-306225" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 25.000000 111.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306225" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47427"/>
     <cge:Term_Ref ObjectID="44936"/>
    <cge:TPSR_Ref TObjectID="47427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-306110" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1540.000000 -542.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306110" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47526"/>
     <cge:Term_Ref ObjectID="45137"/>
    <cge:TPSR_Ref TObjectID="47526"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 133.000000 -182.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 325.000000 -184.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 519.000000 -184.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 711.000000 -186.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 917.000000 -185.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 1109.000000 -187.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 1510.000000 -182.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 1704.000000 -182.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 1896.000000 -184.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 2102.000000 -183.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 2294.000000 -185.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.662963 1322.000000 -187.161111)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 134.000000 299.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 326.000000 297.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 712.000000 295.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 918.000000 296.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 1110.000000 294.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 1511.000000 299.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 1705.000000 299.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 1897.000000 297.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 2103.000000 298.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 2295.000000 296.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.662963 1323.000000 293.838889)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-0.580645 -0.000000 0.000000 -0.666667 622.000000 -336.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.580645 -0.000000 0.000000 -0.666667 527.000000 293.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-0.580645 -0.000000 0.000000 -0.666667 630.000000 141.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_YDLGF"/>
</svg>