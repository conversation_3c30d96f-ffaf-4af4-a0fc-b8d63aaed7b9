<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-186" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="1350 -1404 1927 1180">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape33">
    <polyline points="26,16 24,16 22,17 21,17 19,18 18,19 16,20 15,22 14,24 14,25 13,27 13,29 13,31 14,33 14,34 15,36 16,37 18,39 19,40 21,41 22,41 24,42 26,42 28,42 30,41 31,41 33,40 34,39 36,37 37,36 38,34 38,33 39,31 39,29 " stroke-width="0.0972"/>
    <polyline arcFlag="1" points="43,91 44,91 45,91 45,91 46,90 46,90 47,90 47,89 48,89 48,88 48,87 48,87 49,86 49,85 49,85 48,84 48,83 48,83 48,82 47,82 47,81 46,81 46,81 45,80 45,80 44,80 43,80 " stroke-width="1"/>
    <rect height="24" stroke-width="0.398039" width="12" x="1" y="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="7" x2="7" y1="61" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="100" y2="92"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="100" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="102" y2="102"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="105" y2="105"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="18" x2="33" y1="95" y2="95"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="17" x2="33" y1="102" y2="102"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="26" x2="26" y1="102" y2="114"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="42" x2="42" y1="114" y2="111"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="9" x2="9" y1="114" y2="111"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="7" x2="43" y1="61" y2="61"/>
    <rect height="23" stroke-width="0.369608" width="12" x="20" y="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="26" x2="43" y1="108" y2="108"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="114" y2="114"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="26" x2="26" y1="29" y2="95"/>
    <polyline arcFlag="1" points="43,80 44,80 45,80 45,79 46,79 46,79 47,78 47,78 48,77 48,77 48,76 48,76 49,75 49,74 49,74 48,73 48,72 48,72 48,71 47,71 47,70 46,70 46,69 45,69 45,69 44,69 43,69 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,102 44,102 45,102 45,102 46,102 46,101 47,101 47,100 48,100 48,99 48,99 48,98 49,97 49,97 49,96 48,95 48,95 48,94 48,94 47,93 47,93 46,92 46,92 45,92 45,91 44,91 43,91 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="61" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="102" y2="108"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="39" x2="26" y1="29" y2="29"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape8_0">
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape8_1">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="22" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape22_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,100 64,93 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="30" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="30" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="38" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="38" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="62" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer2:shape22_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="voltageTransformer:shape37">
    <circle cx="32" cy="16" fillStyle="0" r="8" stroke-width="0.570276"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="5" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="9" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="32" x2="35" y1="16" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="29" x2="32" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="32" x2="32" y1="16" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="20" x2="23" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="17" x2="20" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="20" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="17" x2="20" y1="26" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="20" x2="23" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="20" x2="20" y1="23" y2="20"/>
    <circle cx="8" cy="16" fillStyle="0" r="7.5" stroke-width="0.536731"/>
    <circle cx="20" cy="9" fillStyle="0" r="8" stroke-width="0.570276"/>
    <circle cx="20" cy="23" fillStyle="0" r="8" stroke-width="0.570276"/>
   </symbol>
   <symbol id="voltageTransformer:shape79">
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="34" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="13" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="15" y2="9"/>
    <polyline points="40,23 28,32 28,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="37" y2="46"/>
    <rect height="14" stroke-width="1" width="8" x="30" y="23"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="23" y2="13"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3475490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_344c5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_326bb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_325a2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e6fe50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_324da50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3212190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2e21c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_343dfa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_343dfa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3222420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3222420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_327b670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_327b670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2ea5610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b6f280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2e44fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_344d9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3202ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e32cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_327a7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e20530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e5e250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e4d5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e986e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e68970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2e64f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3231600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3245360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3465e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e4ec70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_325c480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b71e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2e2e600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_32115e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1190" width="1937" x="1345" y="-1409"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="1694" y="-1285"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-246244">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.847826 -0.000000 0.000000 -1.000000 2161.869565 -1082.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41323" ObjectName="SW-NH_YL.NH_YL_30117SW"/>
     <cge:Meas_Ref ObjectId="246244"/>
    <cge:TPSR_Ref TObjectID="41323"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130914">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2171.788423 -1017.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23994" ObjectName="SW-NH_YL.NH_YL_30110SW"/>
     <cge:Meas_Ref ObjectId="130914"/>
    <cge:TPSR_Ref TObjectID="23994"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130917">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2124.666667 -680.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23998" ObjectName="SW-NH_YL.NH_YL_0011SW"/>
     <cge:Meas_Ref ObjectId="130917"/>
    <cge:TPSR_Ref TObjectID="23998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130913">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2126.666667 -1056.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23995" ObjectName="SW-NH_YL.NH_YL_3011SW"/>
     <cge:Meas_Ref ObjectId="130913"/>
    <cge:TPSR_Ref TObjectID="23995"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130929">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2260.666667 -576.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24009" ObjectName="SW-NH_YL.NH_YL_0631SW"/>
     <cge:Meas_Ref ObjectId="130929"/>
    <cge:TPSR_Ref TObjectID="24009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130930">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2260.666667 -449.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24010" ObjectName="SW-NH_YL.NH_YL_0636SW"/>
     <cge:Meas_Ref ObjectId="130930"/>
    <cge:TPSR_Ref TObjectID="24010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130926">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2475.666667 -577.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24006" ObjectName="SW-NH_YL.NH_YL_0641SW"/>
     <cge:Meas_Ref ObjectId="130926"/>
    <cge:TPSR_Ref TObjectID="24006"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130927">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2475.666667 -450.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24007" ObjectName="SW-NH_YL.NH_YL_0646SW"/>
     <cge:Meas_Ref ObjectId="130927"/>
    <cge:TPSR_Ref TObjectID="24007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130923">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2676.666667 -575.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24003" ObjectName="SW-NH_YL.NH_YL_0651SW"/>
     <cge:Meas_Ref ObjectId="130923"/>
    <cge:TPSR_Ref TObjectID="24003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130924">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2676.666667 -448.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24004" ObjectName="SW-NH_YL.NH_YL_0656SW"/>
     <cge:Meas_Ref ObjectId="130924"/>
    <cge:TPSR_Ref TObjectID="24004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130920">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2877.666667 -576.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24000" ObjectName="SW-NH_YL.NH_YL_0661SW"/>
     <cge:Meas_Ref ObjectId="130920"/>
    <cge:TPSR_Ref TObjectID="24000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130921">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2877.666667 -449.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24001" ObjectName="SW-NH_YL.NH_YL_0666SW"/>
     <cge:Meas_Ref ObjectId="130921"/>
    <cge:TPSR_Ref TObjectID="24001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130915">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3131.000000 -680.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23996" ObjectName="SW-NH_YL.NH_YL_0901SW"/>
     <cge:Meas_Ref ObjectId="130915"/>
    <cge:TPSR_Ref TObjectID="23996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130935">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2052.666667 -584.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24015" ObjectName="SW-NH_YL.NH_YL_0611SW"/>
     <cge:Meas_Ref ObjectId="130935"/>
    <cge:TPSR_Ref TObjectID="24015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130936">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2052.666667 -452.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24016" ObjectName="SW-NH_YL.NH_YL_0616SW"/>
     <cge:Meas_Ref ObjectId="130936"/>
    <cge:TPSR_Ref TObjectID="24016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130933">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2924.666667 -819.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24013" ObjectName="SW-NH_YL.NH_YL_0676SW"/>
     <cge:Meas_Ref ObjectId="130933"/>
    <cge:TPSR_Ref TObjectID="24013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130932">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2924.666667 -692.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24012" ObjectName="SW-NH_YL.NH_YL_0671SW"/>
     <cge:Meas_Ref ObjectId="130932"/>
    <cge:TPSR_Ref TObjectID="24012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246378">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 2964.000000 -847.788423)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41324" ObjectName="SW-NH_YL.NH_YL_06767SW"/>
     <cge:Meas_Ref ObjectId="246378"/>
    <cge:TPSR_Ref TObjectID="41324"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NH_YL.NH_YL_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1892,-654 3276,-654 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23992" ObjectName="BS-NH_YL.NH_YL_9IM"/>
    <cge:TPSR_Ref TObjectID="23992"/></metadata>
   <polyline fill="none" opacity="0" points="1892,-654 3276,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NH_YL.XM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2119,-1019 2152,-1019 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48334" ObjectName="BS-NH_YL.XM"/>
    <cge:TPSR_Ref TObjectID="48334"/></metadata>
   <polyline fill="none" opacity="0" points="2119,-1019 2152,-1019 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-NH_YL.NH_YL_Cb1">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2960.000000 -923.000000)" xlink:href="#capacitor:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41361" ObjectName="CB-NH_YL.NH_YL_Cb1"/>
    <cge:TPSR_Ref TObjectID="41361"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2000.500000 -1101.500000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2000.500000 -1101.500000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 3073.000000 -506.000000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 3073.000000 -506.000000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-NH_YL.NH_YL_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="33871"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2096.000000 -848.000000)" xlink:href="#transformer2:shape22_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2096.000000 -848.000000)" xlink:href="#transformer2:shape22_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24018" ObjectName="TF-NH_YL.NH_YL_1T"/>
    <cge:TPSR_Ref TObjectID="24018"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_326ace0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 2240.000000 -1164.333333)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37ad0a0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 2051.000000 -1112.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c38f70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3135.000000 -756.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b61da0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2100.003992 -362.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e43070">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2308.003992 -361.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3af2060">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2523.003992 -361.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_322d250">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2724.003992 -362.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33b5b70">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2925.003992 -361.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b58b50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3087.000000 -745.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3886290">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3065.000000 -537.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c351a0">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 2232.000000 -828.333333)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 1425.500000 -1323.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-130863" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1476.000000 -1149.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130863" ObjectName="NH_YL:NH_YL_O01BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-130864" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1475.000000 -1111.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130864" ObjectName="NH_YL:NH_YL_O01BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-130849" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1473.000000 -1234.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130849" ObjectName="NH_YL:NH_YL_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-130849" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1473.000000 -1195.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130849" ObjectName="NH_YL:NH_YL_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-130855" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2067.000000 -868.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130855" ObjectName="NH_YL:NH_YL_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-131022" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2066.000000 -885.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131022" ObjectName="NH_YL:NH_YL_1T_TAP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-130865" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2272.000000 -737.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130865" ObjectName="NH_YL:NH_YL_O01BK_Cos"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-130856" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1964.000000 -742.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130856" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23992"/>
     <cge:Term_Ref ObjectID="33818"/>
    <cge:TPSR_Ref TObjectID="23992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-130857" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1964.000000 -742.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130857" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23992"/>
     <cge:Term_Ref ObjectID="33818"/>
    <cge:TPSR_Ref TObjectID="23992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-130858" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1964.000000 -742.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130858" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23992"/>
     <cge:Term_Ref ObjectID="33818"/>
    <cge:TPSR_Ref TObjectID="23992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-130859" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1964.000000 -742.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130859" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23992"/>
     <cge:Term_Ref ObjectID="33818"/>
    <cge:TPSR_Ref TObjectID="23992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-130867" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1964.000000 -742.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130867" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23992"/>
     <cge:Term_Ref ObjectID="33818"/>
    <cge:TPSR_Ref TObjectID="23992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-130863" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2270.000000 -791.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130863" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23997"/>
     <cge:Term_Ref ObjectID="33827"/>
    <cge:TPSR_Ref TObjectID="23997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130864" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2270.000000 -791.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130864" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23997"/>
     <cge:Term_Ref ObjectID="33827"/>
    <cge:TPSR_Ref TObjectID="23997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130860" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2270.000000 -791.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130860" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23997"/>
     <cge:Term_Ref ObjectID="33827"/>
    <cge:TPSR_Ref TObjectID="23997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-130908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2046.000000 -277.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24014"/>
     <cge:Term_Ref ObjectID="33861"/>
    <cge:TPSR_Ref TObjectID="24014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130909" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2046.000000 -277.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130909" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24014"/>
     <cge:Term_Ref ObjectID="33861"/>
    <cge:TPSR_Ref TObjectID="24014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130905" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2046.000000 -277.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130905" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24014"/>
     <cge:Term_Ref ObjectID="33861"/>
    <cge:TPSR_Ref TObjectID="24014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-130894" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2261.000000 -277.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130894" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24008"/>
     <cge:Term_Ref ObjectID="33849"/>
    <cge:TPSR_Ref TObjectID="24008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130895" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2261.000000 -277.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130895" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24008"/>
     <cge:Term_Ref ObjectID="33849"/>
    <cge:TPSR_Ref TObjectID="24008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130891" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2261.000000 -277.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130891" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24008"/>
     <cge:Term_Ref ObjectID="33849"/>
    <cge:TPSR_Ref TObjectID="24008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-130887" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2477.000000 -277.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130887" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24005"/>
     <cge:Term_Ref ObjectID="33843"/>
    <cge:TPSR_Ref TObjectID="24005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130888" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2477.000000 -277.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130888" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24005"/>
     <cge:Term_Ref ObjectID="33843"/>
    <cge:TPSR_Ref TObjectID="24005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130884" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2477.000000 -277.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130884" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24005"/>
     <cge:Term_Ref ObjectID="33843"/>
    <cge:TPSR_Ref TObjectID="24005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-130880" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2678.000000 -277.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130880" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24002"/>
     <cge:Term_Ref ObjectID="33837"/>
    <cge:TPSR_Ref TObjectID="24002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130881" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2678.000000 -277.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24002"/>
     <cge:Term_Ref ObjectID="33837"/>
    <cge:TPSR_Ref TObjectID="24002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130877" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2678.000000 -277.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130877" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24002"/>
     <cge:Term_Ref ObjectID="33837"/>
    <cge:TPSR_Ref TObjectID="24002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-130873" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2881.000000 -277.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23999"/>
     <cge:Term_Ref ObjectID="33831"/>
    <cge:TPSR_Ref TObjectID="23999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130874" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2881.000000 -277.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23999"/>
     <cge:Term_Ref ObjectID="33831"/>
    <cge:TPSR_Ref TObjectID="23999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130870" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2881.000000 -277.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130870" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23999"/>
     <cge:Term_Ref ObjectID="33831"/>
    <cge:TPSR_Ref TObjectID="23999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-246123" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3020.000000 -759.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246123" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24011"/>
     <cge:Term_Ref ObjectID="33855"/>
    <cge:TPSR_Ref TObjectID="24011"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-246120" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3020.000000 -759.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246120" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24011"/>
     <cge:Term_Ref ObjectID="33855"/>
    <cge:TPSR_Ref TObjectID="24011"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-130849" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2240.000000 -994.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130849" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23993"/>
     <cge:Term_Ref ObjectID="33819"/>
    <cge:TPSR_Ref TObjectID="23993"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130850" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2240.000000 -994.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130850" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23993"/>
     <cge:Term_Ref ObjectID="33819"/>
    <cge:TPSR_Ref TObjectID="23993"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130846" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2240.000000 -994.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130846" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23993"/>
     <cge:Term_Ref ObjectID="33819"/>
    <cge:TPSR_Ref TObjectID="23993"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-130851" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2240.000000 -994.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130851" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23993"/>
     <cge:Term_Ref ObjectID="33819"/>
    <cge:TPSR_Ref TObjectID="23993"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="1460" y="-1386"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="1460" y="-1386"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="1412" y="-1403"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="1412" y="-1403"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="1413" y="-1404"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="1413" y="-1404"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2072" y="-547"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2072" y="-547"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2280" y="-544"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2280" y="-544"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2495" y="-545"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2495" y="-545"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2696" y="-543"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2696" y="-543"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2897" y="-544"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2897" y="-544"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1683" y="-1347"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1683" y="-1347"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1683" y="-1386"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1683" y="-1386"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="63" x="2175" y="-906"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="63" x="2175" y="-906"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="80" x="1360" y="-1033"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="80" x="1360" y="-1033"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2944" y="-787"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2944" y="-787"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="1693" y="-1286"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="1693" y="-1286"/></g>
  </g><g id="MotifButton_Layer">
   <g href="nh_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="1460" y="-1386"/></g>
   <g href="cx_配调_配网接线图35.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="1412" y="-1403"/></g>
   <g href="nh_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="1413" y="-1404"/></g>
   <g href="35kV雨露变NH_YL_061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2072" y="-547"/></g>
   <g href="35kV雨露变NH_YL_063间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2280" y="-544"/></g>
   <g href="35kV雨露变NH_YL_064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2495" y="-545"/></g>
   <g href="35kV雨露变10kV石门线065间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2696" y="-543"/></g>
   <g href="35kV雨露变10kV古苴线066间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2897" y="-544"/></g>
   <g href="cx_配调_配网接线图35_南华.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1683" y="-1347"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1683" y="-1386"/></g>
   <g href="35kV雨露变主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="63" x="2175" y="-906"/></g>
   <g href="35kV雨露变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="80" x="1360" y="-1033"/></g>
   <g href="35kV雨露变NH_YL_067间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2944" y="-787"/></g>
   <g href="AVC雨露站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="1693" y="-1286"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-130912">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2126.455090 -952.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23993" ObjectName="SW-NH_YL.NH_YL_301BK"/>
     <cge:Meas_Ref ObjectId="130912"/>
    <cge:TPSR_Ref TObjectID="23993"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130916">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2125.455090 -752.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23997" ObjectName="SW-NH_YL.NH_YL_001BK"/>
     <cge:Meas_Ref ObjectId="130916"/>
    <cge:TPSR_Ref TObjectID="23997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130928">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2261.337325 -515.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24008" ObjectName="SW-NH_YL.NH_YL_063BK"/>
     <cge:Meas_Ref ObjectId="130928"/>
    <cge:TPSR_Ref TObjectID="24008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130925">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2476.337325 -516.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24005" ObjectName="SW-NH_YL.NH_YL_064BK"/>
     <cge:Meas_Ref ObjectId="130925"/>
    <cge:TPSR_Ref TObjectID="24005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130922">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2677.337325 -514.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24002" ObjectName="SW-NH_YL.NH_YL_065BK"/>
     <cge:Meas_Ref ObjectId="130922"/>
    <cge:TPSR_Ref TObjectID="24002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130919">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2878.337325 -515.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23999" ObjectName="SW-NH_YL.NH_YL_066BK"/>
     <cge:Meas_Ref ObjectId="130919"/>
    <cge:TPSR_Ref TObjectID="23999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130934">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2053.337325 -518.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24014" ObjectName="SW-NH_YL.NH_YL_061BK"/>
     <cge:Meas_Ref ObjectId="130934"/>
    <cge:TPSR_Ref TObjectID="24014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130931">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2925.337325 -758.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24011" ObjectName="SW-NH_YL.NH_YL_067BK"/>
     <cge:Meas_Ref ObjectId="130931"/>
    <cge:TPSR_Ref TObjectID="24011"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_NH" endPointId="0" endStationName="NH_YL" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_yulu" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2136,-1296 2136,-1327 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38078" ObjectName="AC-35kV.LN_yulu"/>
    <cge:TPSR_Ref TObjectID="38078_SS-186"/></metadata>
   <polyline fill="none" opacity="0" points="2136,-1296 2136,-1327 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_33d2e60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2194.000000 -1102.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d50420" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2212.788423 -1037.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3afeff0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2984.000000 -823.211577)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="23992" cx="2270" cy="-654" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23992" cx="2686" cy="-654" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23992" cx="2887" cy="-654" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23992" cx="2063" cy="-654" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23992" cx="3140" cy="-654" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23992" cx="3060" cy="-654" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23992" cx="2934" cy="-654" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48334" cx="2136" cy="-1019" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48334" cx="2136" cy="-1019" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48334" cx="2136" cy="-1019" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48334" cx="2135" cy="-1019" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23992" cx="2134" cy="-654" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e464c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1918.000000 -1172.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35653c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1885.000000 -647.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_379bf00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2105.000000 -1340.000000) translate(0,15)">雨</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_379bf00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2105.000000 -1340.000000) translate(0,33)">露</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_379bf00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2105.000000 -1340.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33cf470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33cf470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33cf470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33cf470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33cf470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33cf470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33cf470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33cf470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33cf470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32b4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32b4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32b4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32b4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32b4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32b4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32b4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32b4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32b4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32b4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32b4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32b4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32b4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32b4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32b4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32b4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32b4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32b4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_33ae620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1506.000000 -1375.000000) translate(0,16)">雨露变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e921a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2155.000000 -1126.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebfb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2151.000000 -774.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33a27c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2151.000000 -703.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c7c520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3102.000000 -903.000000) translate(0,15)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c87730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2072.000000 -547.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c94330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2069.000000 -614.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c96950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2069.000000 -482.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c944f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2280.000000 -544.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cab8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2277.000000 -606.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c0ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2277.000000 -479.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3471040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2495.000000 -545.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3298790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2492.000000 -607.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c4d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2492.000000 -480.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b17c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2696.000000 -543.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3290540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2693.000000 -605.000000) translate(0,12)">0651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bb560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2693.000000 -478.000000) translate(0,12)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_338eee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2897.000000 -544.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3838770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2894.000000 -606.000000) translate(0,12)">0661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37a0cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2894.000000 -479.000000) translate(0,12)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b595f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3147.000000 -710.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b1e160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2144.000000 -981.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2eca5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1694.000000 -1340.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2e2b830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1694.000000 -1377.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3207bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2183.000000 -864.000000) translate(0,15)">SZ11-8000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2e51970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1362.000000 -1033.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f98e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2177.000000 -906.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_325cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2033.000000 -332.000000) translate(0,15)">铅厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33d63c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2246.000000 -331.000000) translate(0,15)">力戈线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_379bc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2458.000000 -331.000000) translate(0,15)">机关线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e63ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2663.000000 -329.000000) translate(0,15)">石门线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_378de50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2863.000000 -329.000000) translate(0,15)">古苴线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b0c5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1350.000000 -411.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b0c5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1350.000000 -411.000000) translate(0,38)">心变运一班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3aff650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1479.000000 -397.500000) translate(0,16)">13908784302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3af26c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1479.000000 -441.000000) translate(0,16)">7301031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ae5740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2191.000000 -1290.000000) translate(0,15)">线路电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38868f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2177.000000 -1066.000000) translate(0,12)">30110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3881fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2142.000000 -1083.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3874dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3016.000000 -389.000000) translate(0,15)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38225f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2944.000000 -787.000000) translate(0,12)">067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3867be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2941.000000 -849.000000) translate(0,12)">0676</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3822020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2941.000000 -722.000000) translate(0,12)">0671</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3259c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2970.000000 -1025.000000) translate(0,15)">10kV1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3259c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2970.000000 -1025.000000) translate(0,33)">1200KVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea8760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3001.000000 -864.000000) translate(0,12)">06767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33a4910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1998.000000 -868.000000) translate(0,12)">油温(℃):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33c8320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1998.000000 -885.000000) translate(0,12)">档位(档):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2949570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1710.500000 -1274.000000) translate(0,16)">AVC</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3210e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1903.000000 742.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329ff90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1903.000000 727.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3465290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1903.000000 711.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cab670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1909.000000 695.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ca1b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1895.000000 679.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c54040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1981.000000 280.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b7efd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1970.000000 265.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c46370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1995.000000 250.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339f840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2200.000000 279.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e84ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2189.000000 264.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e402f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2214.000000 249.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3464ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2415.000000 278.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e92ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2404.000000 263.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32836e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2429.000000 248.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cdbe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2617.000000 278.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_340cf40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2606.000000 263.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37bfac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2631.000000 248.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b32560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2818.000000 278.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b3f710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2807.000000 263.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b4c670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2832.000000 248.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37903d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2956.000000 760.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e37630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2981.000000 745.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33aa150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2186.000000 996.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6d8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2175.000000 981.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3452dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2200.000000 966.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d2630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2199.000000 948.000000) translate(0,12)">COS:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c34030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2215.000000 784.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc1810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2204.000000 769.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c17b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2229.000000 754.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c54340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2228.000000 736.000000) translate(0,12)">COS:</text>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-NH_YL.063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2261.000000 -335.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33930" ObjectName="EC-NH_YL.063Ld"/>
    <cge:TPSR_Ref TObjectID="33930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_YL.064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2476.000000 -336.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33931" ObjectName="EC-NH_YL.064Ld"/>
    <cge:TPSR_Ref TObjectID="33931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_YL.065Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2677.000000 -334.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33932" ObjectName="EC-NH_YL.065Ld"/>
    <cge:TPSR_Ref TObjectID="33932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_YL.066Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2878.000000 -335.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33933" ObjectName="EC-NH_YL.066Ld"/>
    <cge:TPSR_Ref TObjectID="33933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_YL.061Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2053.000000 -338.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33928" ObjectName="EC-NH_YL.061Ld"/>
    <cge:TPSR_Ref TObjectID="33928"/></metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-129397" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1629.000000 -1278.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23707" ObjectName="DYN-NH_YL"/>
     <cge:Meas_Ref ObjectId="129397"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_32091b0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.475000 -1.270270 -0.000000 2237.364865 -1203.262500)" xlink:href="#voltageTransformer:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_325cb30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3121.000000 -821.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_323b120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2198,-1108 2185,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_33d2e60@0" ObjectIDZND0="41323@1" Pin0InfoVect0LinkObjId="SW-246244_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33d2e60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2198,-1108 2185,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3554d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2217,-1043 2199,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2d50420@0" ObjectIDZND0="23994@1" Pin0InfoVect0LinkObjId="SW-130914_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d50420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2217,-1043 2199,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_356ca50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2134,-760 2134,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23997@0" ObjectIDZND0="23998@1" Pin0InfoVect0LinkObjId="SW-130917_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130916_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2134,-760 2134,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3443ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2134,-685 2134,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23998@0" ObjectIDZND0="23992@0" Pin0InfoVect0LinkObjId="g_2c35380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130917_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2134,-685 2134,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b62180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2136,-1157 2186,-1157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="41323@x" ObjectIDND1="23995@x" ObjectIDND2="g_37ad0a0@0" ObjectIDZND0="g_326ace0@0" Pin0InfoVect0LinkObjId="g_326ace0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-246244_0" Pin1InfoVect1LinkObjId="SW-130913_0" Pin1InfoVect2LinkObjId="g_37ad0a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2136,-1157 2186,-1157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b74c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2270,-654 2270,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23992@0" ObjectIDZND0="24009@1" Pin0InfoVect0LinkObjId="SW-130929_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3443ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2270,-654 2270,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b625f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2270,-581 2270,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24009@0" ObjectIDZND0="24008@1" Pin0InfoVect0LinkObjId="SW-130928_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130929_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2270,-581 2270,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3471910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2270,-523 2270,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24008@0" ObjectIDZND0="24010@1" Pin0InfoVect0LinkObjId="SW-130930_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130928_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2270,-523 2270,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31fd470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2485,-654 2485,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23992@0" ObjectIDZND0="24006@1" Pin0InfoVect0LinkObjId="SW-130926_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3443ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2485,-654 2485,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e97300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2485,-582 2485,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24006@0" ObjectIDZND0="24005@1" Pin0InfoVect0LinkObjId="SW-130925_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130926_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2485,-582 2485,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ea9420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2485,-524 2485,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24005@0" ObjectIDZND0="24007@1" Pin0InfoVect0LinkObjId="SW-130927_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130925_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2485,-524 2485,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3793dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2686,-654 2686,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23992@0" ObjectIDZND0="24003@1" Pin0InfoVect0LinkObjId="SW-130923_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3443ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2686,-654 2686,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2adb800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2686,-580 2686,-549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24003@0" ObjectIDZND0="24002@1" Pin0InfoVect0LinkObjId="SW-130922_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130923_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2686,-580 2686,-549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c3b410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2686,-522 2686,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24002@0" ObjectIDZND0="24004@1" Pin0InfoVect0LinkObjId="SW-130924_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130922_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2686,-522 2686,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c3b1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2887,-654 2887,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23992@0" ObjectIDZND0="24000@1" Pin0InfoVect0LinkObjId="SW-130920_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3443ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2887,-654 2887,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c3a450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2887,-581 2887,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24000@0" ObjectIDZND0="23999@1" Pin0InfoVect0LinkObjId="SW-130919_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2887,-581 2887,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c3a230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2887,-523 2887,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23999@0" ObjectIDZND0="24001@1" Pin0InfoVect0LinkObjId="SW-130921_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130919_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2887,-523 2887,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c393d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3140,-654 3140,-685 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23992@0" ObjectIDZND0="23996@0" Pin0InfoVect0LinkObjId="SW-130915_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3443ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3140,-654 3140,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c389f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2063,-654 2063,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23992@0" ObjectIDZND0="24015@1" Pin0InfoVect0LinkObjId="SW-130935_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3443ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2063,-654 2063,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c37db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2062,-589 2062,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24015@0" ObjectIDZND0="24014@1" Pin0InfoVect0LinkObjId="SW-130934_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130935_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2062,-589 2062,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c37b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2062,-526 2062,-493 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24014@0" ObjectIDZND0="24016@1" Pin0InfoVect0LinkObjId="SW-130936_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130934_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2062,-526 2062,-493 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c37230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2062,-427 2062,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24016@x" ObjectIDND1="g_2b61da0@0" ObjectIDZND0="33928@0" Pin0InfoVect0LinkObjId="EC-NH_YL.061Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-130936_0" Pin1InfoVect1LinkObjId="g_2b61da0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2062,-427 2062,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c366b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2062,-457 2062,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="24016@0" ObjectIDZND0="33928@x" ObjectIDZND1="g_2b61da0@0" Pin0InfoVect0LinkObjId="EC-NH_YL.061Ld_0" Pin0InfoVect1LinkObjId="g_2b61da0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130936_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2062,-457 2062,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a0e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2093,-416 2093,-427 2062,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2b61da0@0" ObjectIDZND0="24016@x" ObjectIDZND1="33928@x" Pin0InfoVect0LinkObjId="SW-130936_0" Pin0InfoVect1LinkObjId="EC-NH_YL.061Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b61da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2093,-416 2093,-427 2062,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c3af90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2301,-415 2301,-426 2270,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2e43070@0" ObjectIDZND0="24010@x" ObjectIDZND1="33930@x" Pin0InfoVect0LinkObjId="SW-130930_0" Pin0InfoVect1LinkObjId="EC-NH_YL.063Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e43070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2301,-415 2301,-426 2270,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c8b8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2270,-454 2270,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24010@0" ObjectIDZND0="g_2e43070@0" ObjectIDZND1="33930@x" Pin0InfoVect0LinkObjId="g_2e43070_0" Pin0InfoVect1LinkObjId="EC-NH_YL.063Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2270,-454 2270,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3286120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2270,-426 2270,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2e43070@0" ObjectIDND1="24010@x" ObjectIDZND0="33930@0" Pin0InfoVect0LinkObjId="EC-NH_YL.063Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e43070_0" Pin1InfoVect1LinkObjId="SW-130930_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2270,-426 2270,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3795cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2516,-415 2516,-426 2485,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_3af2060@0" ObjectIDZND0="24007@x" ObjectIDZND1="33931@x" Pin0InfoVect0LinkObjId="SW-130927_0" Pin0InfoVect1LinkObjId="EC-NH_YL.064Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3af2060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2516,-415 2516,-426 2485,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c37fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2485,-455 2485,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24007@0" ObjectIDZND0="g_3af2060@0" ObjectIDZND1="33931@x" Pin0InfoVect0LinkObjId="g_3af2060_0" Pin0InfoVect1LinkObjId="EC-NH_YL.064Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130927_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2485,-455 2485,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c37910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2485,-426 2485,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3af2060@0" ObjectIDND1="24007@x" ObjectIDZND0="33931@0" Pin0InfoVect0LinkObjId="EC-NH_YL.064Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3af2060_0" Pin1InfoVect1LinkObjId="SW-130927_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2485,-426 2485,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3afff30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2717,-416 2717,-427 2686,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_322d250@0" ObjectIDZND0="24004@x" ObjectIDZND1="33932@x" Pin0InfoVect0LinkObjId="SW-130924_0" Pin0InfoVect1LinkObjId="EC-NH_YL.065Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_322d250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2717,-416 2717,-427 2686,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3299130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2686,-453 2686,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24004@0" ObjectIDZND0="g_322d250@0" ObjectIDZND1="33932@x" Pin0InfoVect0LinkObjId="g_322d250_0" Pin0InfoVect1LinkObjId="EC-NH_YL.065Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130924_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2686,-453 2686,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3290d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2686,-427 2686,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_322d250@0" ObjectIDND1="24004@x" ObjectIDZND0="33932@0" Pin0InfoVect0LinkObjId="EC-NH_YL.065Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_322d250_0" Pin1InfoVect1LinkObjId="SW-130924_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2686,-427 2686,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38086c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2918,-415 2918,-426 2887,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_33b5b70@0" ObjectIDZND0="24001@x" ObjectIDZND1="33933@x" Pin0InfoVect0LinkObjId="SW-130921_0" Pin0InfoVect1LinkObjId="EC-NH_YL.066Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33b5b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2918,-415 2918,-426 2887,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38685e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2887,-454 2887,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24001@0" ObjectIDZND0="g_33b5b70@0" ObjectIDZND1="33933@x" Pin0InfoVect0LinkObjId="g_33b5b70_0" Pin0InfoVect1LinkObjId="EC-NH_YL.066Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130921_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2887,-454 2887,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3875870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2887,-426 2887,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_33b5b70@0" ObjectIDND1="24001@x" ObjectIDZND0="33933@0" Pin0InfoVect0LinkObjId="EC-NH_YL.066Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33b5b70_0" Pin1InfoVect1LinkObjId="SW-130921_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2887,-426 2887,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38871b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3140,-806 3140,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2c38f70@1" ObjectIDZND0="g_325cb30@0" Pin0InfoVect0LinkObjId="g_325cb30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c38f70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3140,-806 3140,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b1e9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1995,-1116 2056,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_37ad0a0@0" Pin0InfoVect0LinkObjId="g_37ad0a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1995,-1116 2056,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3af2f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2101,-1117 2136,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_37ad0a0@1" ObjectIDZND0="41323@x" ObjectIDZND1="23995@x" ObjectIDZND2="g_32091b0@0" Pin0InfoVect0LinkObjId="SW-246244_0" Pin0InfoVect1LinkObjId="SW-130913_0" Pin0InfoVect2LinkObjId="g_32091b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37ad0a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2101,-1117 2136,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b40200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2197,-1232 2136,-1232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_32091b0@0" ObjectIDZND0="41323@x" ObjectIDZND1="23995@x" ObjectIDZND2="g_37ad0a0@0" Pin0InfoVect0LinkObjId="SW-246244_0" Pin0InfoVect1LinkObjId="SW-130913_0" Pin0InfoVect2LinkObjId="g_37ad0a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32091b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2197,-1232 2136,-1232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b32e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2136,-1296 2136,-1232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="38078@1" ObjectIDZND0="41323@x" ObjectIDZND1="23995@x" ObjectIDZND2="g_37ad0a0@0" Pin0InfoVect0LinkObjId="SW-246244_0" Pin0InfoVect1LinkObjId="SW-130913_0" Pin0InfoVect2LinkObjId="g_37ad0a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2136,-1296 2136,-1232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b4cf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2136,-1232 2136,-1157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_32091b0@0" ObjectIDND1="38078@1" ObjectIDZND0="41323@x" ObjectIDZND1="23995@x" ObjectIDZND2="g_37ad0a0@0" Pin0InfoVect0LinkObjId="SW-246244_0" Pin0InfoVect1LinkObjId="SW-130913_0" Pin0InfoVect2LinkObjId="g_37ad0a0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32091b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2136,-1232 2136,-1157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b59ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3094,-749 3094,-737 3140,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2b58b50@0" ObjectIDZND0="23996@x" ObjectIDZND1="g_2c38f70@0" Pin0InfoVect0LinkObjId="SW-130915_0" Pin0InfoVect1LinkObjId="g_2c38f70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b58b50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3094,-749 3094,-737 3140,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c6720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3140,-721 3140,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="23996@1" ObjectIDZND0="g_2b58b50@0" ObjectIDZND1="g_2c38f70@0" Pin0InfoVect0LinkObjId="g_2b58b50_0" Pin0InfoVect1LinkObjId="g_2c38f70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130915_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3140,-721 3140,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c5f2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3140,-737 3140,-761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b58b50@0" ObjectIDND1="23996@x" ObjectIDZND0="g_2c38f70@0" Pin0InfoVect0LinkObjId="g_2c38f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b58b50_0" Pin1InfoVect1LinkObjId="SW-130915_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3140,-737 3140,-761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c35b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3060,-503 3060,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3886290@0" Pin0InfoVect0LinkObjId="g_3886290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3060,-503 3060,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c35380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3060,-587 3060,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_3886290@1" ObjectIDZND0="23992@0" Pin0InfoVect0LinkObjId="g_3443ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3886290_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3060,-587 3060,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c8a5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2934,-766 2934,-733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24011@0" ObjectIDZND0="24012@1" Pin0InfoVect0LinkObjId="SW-130932_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130931_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2934,-766 2934,-733 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37bd420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2934,-697 2934,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24012@0" ObjectIDZND0="23992@0" Pin0InfoVect0LinkObjId="g_3443ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130932_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2934,-697 2934,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c96b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2934,-824 2934,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24013@0" ObjectIDZND0="24011@1" Pin0InfoVect0LinkObjId="SW-130931_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130933_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2934,-824 2934,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ad4990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2990,-839 2990,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="41324@0" ObjectIDZND0="g_3afeff0@0" Pin0InfoVect0LinkObjId="g_3afeff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246378_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2990,-839 2990,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ac460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2934,-928 2934,-909 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="41361@0" ObjectIDZND0="24013@x" ObjectIDZND1="41324@x" Pin0InfoVect0LinkObjId="SW-130933_0" Pin0InfoVect1LinkObjId="SW-246378_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-NH_YL.NH_YL_Cb1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2934,-928 2934,-909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c34f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2934,-909 2934,-860 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="41324@x" ObjectIDND1="41361@x" ObjectIDZND0="24013@1" Pin0InfoVect0LinkObjId="SW-130933_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-246378_0" Pin1InfoVect1LinkObjId="CB-NH_YL.NH_YL_Cb1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2934,-909 2934,-860 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c5d980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2934,-909 2934,-901 2990,-901 2990,-875 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="24013@x" ObjectIDND1="41361@x" ObjectIDZND0="41324@1" Pin0InfoVect0LinkObjId="SW-246378_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-130933_0" Pin1InfoVect1LinkObjId="CB-NH_YL.NH_YL_Cb1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2934,-909 2934,-901 2990,-901 2990,-875 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad3ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2136,-1157 2136,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="powerLine" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_32091b0@0" ObjectIDND1="38078@1" ObjectIDND2="g_326ace0@0" ObjectIDZND0="41323@x" ObjectIDZND1="23995@x" ObjectIDZND2="g_37ad0a0@0" Pin0InfoVect0LinkObjId="SW-246244_0" Pin0InfoVect1LinkObjId="SW-130913_0" Pin0InfoVect2LinkObjId="g_37ad0a0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_32091b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="g_326ace0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2136,-1157 2136,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b9d320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2136,-1108 2154,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="powerLine" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_32091b0@0" ObjectIDND1="38078@1" ObjectIDND2="g_326ace0@0" ObjectIDZND0="41323@0" Pin0InfoVect0LinkObjId="SW-246244_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_32091b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="g_326ace0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2136,-1108 2154,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b934d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2136,-1117 2136,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="powerLine" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_32091b0@0" ObjectIDND1="38078@1" ObjectIDND2="g_326ace0@0" ObjectIDZND0="41323@x" ObjectIDZND1="23995@x" Pin0InfoVect0LinkObjId="SW-246244_0" Pin0InfoVect1LinkObjId="SW-130913_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_32091b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="g_326ace0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2136,-1117 2136,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad2710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2136,-1108 2136,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="powerLine" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_32091b0@0" ObjectIDND1="38078@1" ObjectIDND2="g_326ace0@0" ObjectIDZND0="23995@1" Pin0InfoVect0LinkObjId="SW-130913_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_32091b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="g_326ace0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2136,-1108 2136,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad2970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2134,-940 2134,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="24018@0" ObjectIDZND0="23993@0" Pin0InfoVect0LinkObjId="SW-130912_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2134,-940 2134,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b60270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2136,-821 2178,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="24018@x" ObjectIDND1="23997@x" ObjectIDZND0="g_2c351a0@0" Pin0InfoVect0LinkObjId="g_2c351a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-130916_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2136,-821 2178,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b604d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2136,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="busSection" ObjectIDND0="48334@0" ObjectIDZND0="48334@0" Pin0InfoVect0LinkObjId="g_2c126f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c126f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2136,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c126f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2135,-987 2135,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="23993@1" ObjectIDZND0="48334@0" Pin0InfoVect0LinkObjId="g_2b604d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130912_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2135,-987 2135,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c12de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2136,-1044 2163,-1044 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="48334@0" ObjectIDND1="23995@x" ObjectIDZND0="23994@0" Pin0InfoVect0LinkObjId="SW-130914_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b604d0_0" Pin1InfoVect1LinkObjId="SW-130913_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2136,-1044 2163,-1044 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c5ecd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2136,-1019 2136,-1044 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="48334@0" ObjectIDZND0="23995@x" ObjectIDZND1="23994@x" Pin0InfoVect0LinkObjId="SW-130913_0" Pin0InfoVect1LinkObjId="SW-130914_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b604d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2136,-1019 2136,-1044 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c5ef30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2136,-1044 2136,-1061 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="48334@0" ObjectIDND1="23994@x" ObjectIDZND0="23995@0" Pin0InfoVect0LinkObjId="SW-130913_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b604d0_0" Pin1InfoVect1LinkObjId="SW-130914_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2136,-1044 2136,-1061 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c4b750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2134,-852 2134,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="24018@1" ObjectIDZND0="g_2c351a0@0" ObjectIDZND1="23997@x" Pin0InfoVect0LinkObjId="g_2c351a0_0" Pin0InfoVect1LinkObjId="SW-130916_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2134,-852 2134,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a8410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2134,-821 2134,-788 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="g_2c351a0@0" ObjectIDND1="24018@x" ObjectIDZND0="23997@1" Pin0InfoVect0LinkObjId="SW-130916_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c351a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2134,-821 2134,-788 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="NH_YL"/>
</svg>