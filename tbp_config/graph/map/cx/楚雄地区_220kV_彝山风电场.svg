<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-245" aopId="10" id="thSvg" product="E8000V2" version="1.0" viewBox="36 -1285 3510 1378">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape29">
    <polyline arcFlag="1" points="26,105 24,105 22,104 21,104 19,103 18,102 16,101 15,99 14,97 14,96 13,94 13,92 13,90 14,88 14,87 15,85 16,84 18,82 19,81 21,80 22,80 24,79 26,79 28,79 30,80 31,80 33,81 34,82 36,84 37,85 38,87 38,88 39,90 39,92 " stroke-width="0.0972"/>
    <polyline arcFlag="1" points="43,30 44,30 45,30 45,30 46,31 46,31 47,31 47,32 48,32 48,33 48,34 48,34 49,35 49,36 49,36 48,37 48,38 48,38 48,39 47,39 47,40 46,40 46,40 45,41 45,41 44,41 43,41 " stroke-width="1"/>
    <rect height="24" stroke-width="0.398039" width="12" x="1" y="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="7" x2="7" y1="60" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="21" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="18" x2="33" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="17" x2="33" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="19" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="42" x2="42" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="9" x2="9" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="7" x2="43" y1="60" y2="60"/>
    <rect height="23" stroke-width="0.369608" width="12" x="20" y="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="26" x2="43" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="26" x2="26" y1="92" y2="26"/>
    <polyline arcFlag="1" points="43,41 44,41 45,41 45,42 46,42 46,42 47,43 47,43 48,44 48,44 48,45 48,45 49,46 49,47 49,47 48,48 48,49 48,49 48,50 47,50 47,51 46,51 46,52 45,52 45,52 44,52 43,52 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,19 44,19 45,19 45,19 46,19 46,20 47,20 47,21 48,21 48,22 48,22 48,23 49,24 49,24 49,25 48,26 48,26 48,27 48,27 47,28 47,28 46,29 46,29 45,29 45,30 44,30 43,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="60" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="105" y2="116"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="39" x2="26" y1="92" y2="92"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_34ebfe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape189">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="13" y1="21" y2="11"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="13,64 38,64 38,35 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="13" y1="58" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="32" x2="44" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="41" x2="35" y1="31" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="39" x2="37" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="13" y1="21" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="53" y2="0"/>
    <circle cx="13" cy="66" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="80" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="84" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="88" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="60" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="64" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="68" y2="64"/>
    <ellipse cx="13" cy="82" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape46_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="61" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="62" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="16" y1="56" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="31" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,62 40,62 40,31 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="47" y2="5"/>
    <circle cx="16" cy="62" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="61" y2="56"/>
   </symbol>
   <symbol id="transformer2:shape46_1">
    <circle cx="16" cy="84" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="16,93 10,81 22,81 16,93 16,92 16,93 "/>
   </symbol>
   <symbol id="transformer2:shape78_0">
    <circle cx="31" cy="90" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="23" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="65" y2="60"/>
    <circle cx="31" cy="68" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="65" y2="70"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="31" y1="60" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="36" y2="36"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,65 6,65 6,36 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="31,22 25,35 37,35 31,22 31,23 31,22 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="53" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="65" y2="70"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="26" y1="94" y2="94"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="31" y1="94" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="31" y1="94" y2="87"/>
   </symbol>
   <symbol id="transformer2:shape78_1"/>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="23" y1="35" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.229325" x1="18" x2="29" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.22074" x1="18" x2="29" y1="62" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="41" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="34" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="21" x2="25" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="20" x2="27" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="18" x2="29" y1="71" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.229325" x1="41" x2="41" y1="40" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.239135" x1="34" x2="34" y1="40" y2="51"/>
    <circle cx="24" cy="26" fillStyle="0" r="9" stroke-width="1"/>
    <circle cx="24" cy="10" fillStyle="0" r="9" stroke-width="1"/>
    <circle cx="10" cy="18" fillStyle="0" r="9" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape75">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649573" x1="6" x2="28" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="22" x2="22" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="22" x2="18" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="26" x2="22" y1="23" y2="25"/>
    <circle cx="22" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="49" x2="49" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="49" x2="45" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="53" x2="49" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="10" y2="12"/>
    <circle cx="35" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="35" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="46" x2="51" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="47" x2="46" y1="24" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="50" x2="51" y1="24" y2="28"/>
    <circle cx="48" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="48" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.475524" x1="6" x2="6" y1="27" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="33" y2="33"/>
   </symbol>
   <symbol id="voltageTransformer:shape114">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="14" x2="0" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="28" x2="24" y1="3" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="30" x2="22" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="32" x2="20" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="23" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="24" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="14" x2="1" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="13" x2="1" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="6" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="14" x2="1" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="7" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="9" x2="5" y1="56" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="11" x2="3" y1="53" y2="53"/>
    <circle cx="31" cy="48" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7538" x1="38" x2="35" y1="30" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.755439" x1="38" x2="35" y1="27" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="35" x2="35" y1="26" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="13" x2="1" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="31" y1="46" y2="48"/>
    <circle cx="26" cy="29" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="31" x2="31" y1="48" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="34" x2="31" y1="46" y2="48"/>
    <circle cx="36" cy="29" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="35" y2="35"/>
    <circle cx="36" cy="39" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="35" y2="35"/>
    <circle cx="26" cy="39" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="26" x2="26" y1="28" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="29" x2="26" y1="26" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="23" x2="26" y1="26" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="33" x2="36" y1="37" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="36" x2="36" y1="39" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="39" x2="36" y1="37" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="26" x2="26" y1="39" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="29" x2="26" y1="37" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="23" x2="26" y1="37" y2="39"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3a5c700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3a5d8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3a5e250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3a5ea10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3a5fb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3a60760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3a60ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3a61880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3a632b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3a632b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3a64a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3a64a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3a668b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3a668b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_3a678c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3a69550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3a6a1a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3a6b080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3a6b960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3a6cd10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3a6d780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3a6e040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3a6e800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3a6f8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3a70260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3a70d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3a71710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3a72c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3a73720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3a74770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3a753c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3a836d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3a83e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3a771c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3a78610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1388" width="3520" x="31" y="-1290"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f39d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 364.000000 -10.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f3c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 353.000000 -25.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f3e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 378.000000 -40.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f4400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 998.000000 566.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f4650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 987.000000 551.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f4890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1012.000000 536.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f4bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 998.000000 820.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f4e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 987.000000 805.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f5060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1012.000000 790.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f5390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 954.000000 1004.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f55f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 943.000000 989.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f5830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 968.000000 974.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3686220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 544.000000 922.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3686480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 544.000000 937.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36866c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 544.000000 952.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3686900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 536.000000 907.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3687210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 318.000000 528.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3687440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 318.000000 543.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3687680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 318.000000 558.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36878c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 310.000000 513.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3688780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 540.000000 -23.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36889b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 529.000000 -38.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3688bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 554.000000 -53.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3688f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 718.000000 -10.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3689180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 707.000000 -25.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36893c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 732.000000 -40.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36896f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 867.000000 -10.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3689950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 856.000000 -25.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3689b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 881.000000 -40.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368b020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1010.000000 -10.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368b250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 999.000000 -25.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368b490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1024.000000 -40.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368b7c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1160.000000 -10.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368ba20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1149.000000 -25.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368bc60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1174.000000 -40.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368bf90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1303.000000 -10.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368c1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1292.000000 -25.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368c430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1317.000000 -40.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368c760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1435.000000 -10.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368c9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1424.000000 -25.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368cc00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1449.000000 -40.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368dfd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1587.000000 -10.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368e200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1576.000000 -25.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368e440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1601.000000 -40.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368e770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1735.000000 -10.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368e9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1724.000000 -25.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368ec10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1749.000000 -40.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37cbe70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1992.000000 -9.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37cc0e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1981.000000 -24.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37cc320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2006.000000 -39.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37cf300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2161.000000 -6.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37cf530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2150.000000 -21.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37cf770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2175.000000 -36.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37cfaa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2348.000000 -6.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37cfd00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2337.000000 -21.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37cff40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2362.000000 -36.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d0270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2556.000000 -46.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d04d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2545.000000 -61.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d0710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2570.000000 -76.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d0a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2744.000000 -9.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d0ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2733.000000 -24.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d0ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2758.000000 -39.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d1210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2892.000000 -11.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d1470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2881.000000 -26.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d16b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2906.000000 -41.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d19e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3055.000000 -6.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d1c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3044.000000 -21.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d1e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3069.000000 -36.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d21b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3221.000000 -8.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d2410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3210.000000 -23.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d2650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3235.000000 -38.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d2980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2267.000000 825.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d2be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2256.000000 810.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d2e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2281.000000 795.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d3150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2208.000000 551.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d33b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2197.000000 536.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d35f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2222.000000 521.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d54b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3452.000000 522.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d5960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3452.000000 537.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d5ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3452.000000 552.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d5de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3444.000000 507.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3288,-1220 3283,-1231 3293,-1231 3288,-1220 3288,-1221 3288,-1220 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3207,-1220 3202,-1231 3212,-1231 3207,-1220 3207,-1221 3207,-1220 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="926,-666 935,-650 935,-650 918,-650 927,-666 925,-662 926,-666 " stroke="rgb(255,255,0)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-179250">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 887.000000 -962.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27524" ObjectName="SW-CX_YS.CX_YS_261BK"/>
     <cge:Meas_Ref ObjectId="179250"/>
    <cge:TPSR_Ref TObjectID="27524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179261">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 886.565184 -777.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27535" ObjectName="SW-CX_YS.CX_YS_201BK"/>
     <cge:Meas_Ref ObjectId="179261"/>
    <cge:TPSR_Ref TObjectID="27535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179285">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 392.000000 -389.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27567" ObjectName="SW-CX_YS.CX_YS_311BK"/>
     <cge:Meas_Ref ObjectId="179285"/>
    <cge:TPSR_Ref TObjectID="27567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179291">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 567.000000 -388.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27575" ObjectName="SW-CX_YS.CX_YS_312BK"/>
     <cge:Meas_Ref ObjectId="179291"/>
    <cge:TPSR_Ref TObjectID="27575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179297">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 743.000000 -391.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27582" ObjectName="SW-CX_YS.CX_YS_313BK"/>
     <cge:Meas_Ref ObjectId="179297"/>
    <cge:TPSR_Ref TObjectID="27582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179264">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 887.000000 -389.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27539" ObjectName="SW-CX_YS.CX_YS_314BK"/>
     <cge:Meas_Ref ObjectId="179264"/>
    <cge:TPSR_Ref TObjectID="27539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179267">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1031.000000 -386.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27543" ObjectName="SW-CX_YS.CX_YS_315BK"/>
     <cge:Meas_Ref ObjectId="179267"/>
    <cge:TPSR_Ref TObjectID="27543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179270">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1175.000000 -390.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27547" ObjectName="SW-CX_YS.CX_YS_316BK"/>
     <cge:Meas_Ref ObjectId="179270"/>
    <cge:TPSR_Ref TObjectID="27547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179273">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1319.000000 -392.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27551" ObjectName="SW-CX_YS.CX_YS_317BK"/>
     <cge:Meas_Ref ObjectId="179273"/>
    <cge:TPSR_Ref TObjectID="27551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179276">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1463.000000 -387.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27555" ObjectName="SW-CX_YS.CX_YS_318BK"/>
     <cge:Meas_Ref ObjectId="179276"/>
    <cge:TPSR_Ref TObjectID="27555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179279">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1607.000000 -387.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27559" ObjectName="SW-CX_YS.CX_YS_319BK"/>
     <cge:Meas_Ref ObjectId="179279"/>
    <cge:TPSR_Ref TObjectID="27559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179282">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1751.000000 -388.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27563" ObjectName="SW-CX_YS.CX_YS_321BK"/>
     <cge:Meas_Ref ObjectId="179282"/>
    <cge:TPSR_Ref TObjectID="27563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179262">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 887.000000 -529.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27536" ObjectName="SW-CX_YS.CX_YS_301BK"/>
     <cge:Meas_Ref ObjectId="179262"/>
    <cge:TPSR_Ref TObjectID="27536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293375">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2120.565184 -772.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45313" ObjectName="SW-CX_YS.CX_YS_202BK"/>
     <cge:Meas_Ref ObjectId="293375"/>
    <cge:TPSR_Ref TObjectID="45313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293381">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2121.000000 -523.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45319" ObjectName="SW-CX_YS.CX_YS_302BK"/>
     <cge:Meas_Ref ObjectId="293381"/>
    <cge:TPSR_Ref TObjectID="45319"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293405">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2054.000000 -384.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45320" ObjectName="SW-CX_YS.CX_YS_351BK"/>
     <cge:Meas_Ref ObjectId="293405"/>
    <cge:TPSR_Ref TObjectID="45320"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293409">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2228.000000 -381.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45324" ObjectName="SW-CX_YS.CX_YS_352BK"/>
     <cge:Meas_Ref ObjectId="293409"/>
    <cge:TPSR_Ref TObjectID="45324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293413">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2799.000000 -381.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45328" ObjectName="SW-CX_YS.CX_YS_353BK"/>
     <cge:Meas_Ref ObjectId="293413"/>
    <cge:TPSR_Ref TObjectID="45328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293417">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2943.000000 -383.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45332" ObjectName="SW-CX_YS.CX_YS_354BK"/>
     <cge:Meas_Ref ObjectId="293417"/>
    <cge:TPSR_Ref TObjectID="45332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293433">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3087.000000 -378.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45348" ObjectName="SW-CX_YS.CX_YS_358BK"/>
     <cge:Meas_Ref ObjectId="293433"/>
    <cge:TPSR_Ref TObjectID="45348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3413.000000 -568.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3394.000000 -386.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293425">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2392.000000 -390.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45340" ObjectName="SW-CX_YS.CX_YS_355BK"/>
     <cge:Meas_Ref ObjectId="293425"/>
    <cge:TPSR_Ref TObjectID="45340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293421">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2605.000000 -400.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45336" ObjectName="SW-CX_YS.CX_YS_356BK"/>
     <cge:Meas_Ref ObjectId="293421"/>
    <cge:TPSR_Ref TObjectID="45336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293429">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3243.000000 -376.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45344" ObjectName="SW-CX_YS.CX_YS_357BK"/>
     <cge:Meas_Ref ObjectId="293429"/>
    <cge:TPSR_Ref TObjectID="45344"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3262.000000 -568.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3106.000000 -570.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2962.000000 -568.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_34cf170">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 835.000000 -1118.000000)" xlink:href="#voltageTransformer:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34eceb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1109.000000 -645.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34fdba0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1226.000000 -1081.000000)" xlink:href="#voltageTransformer:shape114"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36baaa0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2366.000000 -638.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YS.CX_YS_2IM">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="540,-880 2337,-880 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27521" ObjectName="BS-CX_YS.CX_YS_2IM"/>
    <cge:TPSR_Ref TObjectID="27521"/></metadata>
   <polyline fill="none" opacity="0" points="540,-880 2337,-880 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YS.CX_YS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-480 1857,-480 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27522" ObjectName="BS-CX_YS.CX_YS_3IM"/>
    <cge:TPSR_Ref TObjectID="27522"/></metadata>
   <polyline fill="none" opacity="0" points="304,-480 1857,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YS.CX_YS_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1993,-479 3546,-479 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="45312" ObjectName="BS-CX_YS.CX_YS_3IIM"/>
    <cge:TPSR_Ref TObjectID="45312"/></metadata>
   <polyline fill="none" opacity="0" points="1993,-479 3546,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3180,-1047 3311,-1047 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3180,-1047 3311,-1047 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 550.000000 -53.000000)" xlink:href="#capacitor:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2588.000000 -65.000000)" xlink:href="#capacitor:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_34ccaa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 826.000000 -1074.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34cd530" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 826.000000 -1006.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34cdfc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 825.000000 -944.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34d6d30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1097.000000 -912.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34d75f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1112.000000 -1002.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34e67d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 947.565184 -817.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34e7260" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 947.565184 -769.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34e7cf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 947.565184 -711.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3508990" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 323.000000 -333.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3514aa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 309.000000 -142.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3516ec0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 498.000000 -333.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3521660" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 484.000000 -170.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_352af90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 570.000000 19.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35f43f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 674.000000 -332.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35fd0a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 818.000000 -331.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3603020" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 962.000000 -330.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3608ed0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1106.000000 -331.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_360dc60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1250.000000 -331.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3614c30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1394.000000 -328.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36199c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1538.000000 -329.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_361e750" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1682.000000 -329.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36b6ab0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2182.565184 -812.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36b7540" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2182.565184 -764.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36b7fd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2182.565184 -705.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36d1f50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1985.000000 -326.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36d6a80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2159.000000 -325.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36db300" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2724.000000 -322.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36dfe30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2874.000000 -322.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36e4960" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3018.000000 -319.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36e9490" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3481.000000 -625.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36edfa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3325.000000 -327.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_373e0c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2323.000000 -334.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37482a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2309.000000 -143.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3754f10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2536.000000 -345.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3766360" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2608.000000 7.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37735f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3174.000000 -317.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3784210" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3330.000000 -625.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3792660" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3174.000000 -627.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37a1bd0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3030.000000 -625.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37ba730" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2522.000000 -182.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-220KV" id="g_34cc120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,-950 843,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27527@0" ObjectIDZND0="g_34cdfc0@0" Pin0InfoVect0LinkObjId="g_34cdfc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179253_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="851,-950 843,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34cc380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,-1012 844,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27528@0" ObjectIDZND0="g_34cd530@0" Pin0InfoVect0LinkObjId="g_34cd530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179254_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="851,-1012 844,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34cc5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-1080 887,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="27526@x" ObjectIDND1="g_34cf170@0" ObjectIDND2="g_34fbb80@0" ObjectIDZND0="27529@1" Pin0InfoVect0LinkObjId="SW-179255_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-179252_0" Pin1InfoVect1LinkObjId="g_34cf170_0" Pin1InfoVect2LinkObjId="g_34fbb80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-1080 887,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34cc840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,-1080 844,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27529@0" ObjectIDZND0="g_34ccaa0@0" Pin0InfoVect0LinkObjId="g_34ccaa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179255_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="851,-1080 844,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34cea50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-1080 896,-1066 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="27529@x" ObjectIDND1="g_34cf170@0" ObjectIDND2="g_34fbb80@0" ObjectIDZND0="27526@1" Pin0InfoVect0LinkObjId="SW-179252_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-179255_0" Pin1InfoVect1LinkObjId="g_34cf170_0" Pin1InfoVect2LinkObjId="g_34fbb80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-1080 896,-1066 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34cecb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-1012 887,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="27526@x" ObjectIDND1="27524@x" ObjectIDZND0="27528@1" Pin0InfoVect0LinkObjId="SW-179254_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-179252_0" Pin1InfoVect1LinkObjId="SW-179250_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-1012 887,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34cef10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="887,-1163 896,-1163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_34cf170@0" ObjectIDZND0="27529@x" ObjectIDZND1="27526@x" ObjectIDZND2="g_34fbb80@0" Pin0InfoVect0LinkObjId="SW-179255_0" Pin0InfoVect1LinkObjId="SW-179252_0" Pin0InfoVect2LinkObjId="g_34fbb80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34cf170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="887,-1163 896,-1163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34d0bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-1163 896,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_34cf170@0" ObjectIDND1="g_34fbb80@0" ObjectIDND2="9207@1" ObjectIDZND0="27529@x" ObjectIDZND1="27526@x" Pin0InfoVect0LinkObjId="SW-179255_0" Pin0InfoVect1LinkObjId="SW-179252_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34cf170_0" Pin1InfoVect1LinkObjId="g_34fbb80_0" Pin1InfoVect2LinkObjId="g_37df120_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="896,-1163 896,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34d6b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1138,-1008 1130,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27585@0" ObjectIDZND0="g_34d75f0@0" Pin0InfoVect0LinkObjId="g_34d75f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1138,-1008 1130,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34e8780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="952,-823 944,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_34e67d0@0" ObjectIDZND0="27531@1" Pin0InfoVect0LinkObjId="SW-179257_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34e67d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="952,-823 944,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34e89e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="952,-775 944,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_34e7260@0" ObjectIDZND0="27533@1" Pin0InfoVect0LinkObjId="SW-179259_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34e7260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="952,-775 944,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34e8c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="908,-775 896,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="27533@0" ObjectIDZND0="27535@x" ObjectIDZND1="27532@x" Pin0InfoVect0LinkObjId="SW-179261_0" Pin0InfoVect1LinkObjId="SW-179258_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179259_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="908,-775 896,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34e8ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="952,-717 944,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_34e7cf0@0" ObjectIDZND0="27534@1" Pin0InfoVect0LinkObjId="SW-179260_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34e7cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="952,-717 944,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34e9100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-832 896,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="27530@0" ObjectIDZND0="27535@x" ObjectIDZND1="27531@x" Pin0InfoVect0LinkObjId="SW-179261_0" Pin0InfoVect1LinkObjId="SW-179257_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179256_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="896,-832 896,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34e9360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-823 908,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="27535@x" ObjectIDND1="27530@x" ObjectIDZND0="27531@0" Pin0InfoVect0LinkObjId="SW-179257_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-179261_0" Pin1InfoVect1LinkObjId="SW-179256_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-823 908,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34e95c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-823 896,-812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="27531@x" ObjectIDND1="27530@x" ObjectIDZND0="27535@1" Pin0InfoVect0LinkObjId="SW-179261_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-179257_0" Pin1InfoVect1LinkObjId="SW-179256_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-823 896,-812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34e9820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-785 896,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27535@0" ObjectIDZND0="27533@x" ObjectIDZND1="27532@x" Pin0InfoVect0LinkObjId="SW-179259_0" Pin0InfoVect1LinkObjId="SW-179258_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179261_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="896,-785 896,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34e9a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-764 896,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="27532@1" ObjectIDZND0="27535@x" ObjectIDZND1="27533@x" Pin0InfoVect0LinkObjId="SW-179261_0" Pin0InfoVect1LinkObjId="SW-179259_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179258_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="896,-764 896,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34e9ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="887,-950 896,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="27527@1" ObjectIDZND0="27524@x" ObjectIDZND1="27525@x" Pin0InfoVect0LinkObjId="SW-179250_0" Pin0InfoVect1LinkObjId="SW-179251_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179253_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="887,-950 896,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34e9f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-970 896,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="27524@0" ObjectIDZND0="27527@x" ObjectIDZND1="27525@x" Pin0InfoVect0LinkObjId="SW-179253_0" Pin0InfoVect1LinkObjId="SW-179251_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="896,-970 896,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34ea1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-950 896,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="27527@x" ObjectIDND1="27524@x" ObjectIDZND0="27525@1" Pin0InfoVect0LinkObjId="SW-179251_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-179253_0" Pin1InfoVect1LinkObjId="SW-179250_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-950 896,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34ea400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-1030 896,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27526@0" ObjectIDZND0="27528@x" ObjectIDZND1="27524@x" Pin0InfoVect0LinkObjId="SW-179254_0" Pin0InfoVect1LinkObjId="SW-179250_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179252_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="896,-1030 896,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34ea660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-1012 896,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="27528@x" ObjectIDND1="27526@x" ObjectIDZND0="27524@1" Pin0InfoVect0LinkObjId="SW-179250_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-179254_0" Pin1InfoVect1LinkObjId="SW-179252_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-1012 896,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34ea8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1174,-1008 1186,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="27585@1" ObjectIDZND0="27584@x" ObjectIDZND1="g_34fdba0@0" ObjectIDZND2="g_34fc770@0" Pin0InfoVect0LinkObjId="SW-179299_0" Pin0InfoVect1LinkObjId="g_34fdba0_0" Pin0InfoVect2LinkObjId="g_34fc770_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179300_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1174,-1008 1186,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34eab20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1186,-982 1186,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="27584@1" ObjectIDZND0="27585@x" ObjectIDZND1="g_34fdba0@0" ObjectIDZND2="g_34fc770@0" Pin0InfoVect0LinkObjId="SW-179300_0" Pin0InfoVect1LinkObjId="g_34fdba0_0" Pin0InfoVect2LinkObjId="g_34fc770_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179299_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1186,-982 1186,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34ec440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="431,-316 431,-325 401,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_350bbb0@0" ObjectIDZND0="27571@x" ObjectIDZND1="27569@x" ObjectIDZND2="g_350dec0@0" Pin0InfoVect0LinkObjId="SW-179288_0" Pin0InfoVect1LinkObjId="SW-179286_0" Pin0InfoVect2LinkObjId="g_350dec0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_350bbb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="431,-316 431,-325 401,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34ecc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1144,-633 1144,-650 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_34ec630@0" ObjectIDZND0="g_34eceb0@0" Pin0InfoVect0LinkObjId="g_34eceb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34ec630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1144,-633 1144,-650 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34f06a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1214,-592 1145,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_34ef8f0@0" ObjectIDZND0="g_34ec630@0" ObjectIDZND1="27587@x" Pin0InfoVect0LinkObjId="g_34ec630_0" Pin0InfoVect1LinkObjId="SW-179302_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34ef8f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1214,-592 1145,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34f5a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1124,-918 1115,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27586@0" ObjectIDZND0="g_34d6d30@0" Pin0InfoVect0LinkObjId="g_34d6d30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179301_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1124,-918 1115,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34fa380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3288,-1245 3288,-1205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3288,-1245 3288,-1205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34fb4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3288,-1111 3288,-1047 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3288,-1111 3288,-1047 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34fd480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1186,-1045 1233,-1045 1233,-1086 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_34fc770@0" ObjectIDND1="27585@x" ObjectIDND2="27584@x" ObjectIDZND0="g_34fdba0@0" Pin0InfoVect0LinkObjId="g_34fdba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34fc770_0" Pin1InfoVect1LinkObjId="SW-179300_0" Pin1InfoVect2LinkObjId="SW-179299_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1186,-1045 1233,-1045 1233,-1086 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34fd6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1144,-1061 1144,-1045 1186,-1045 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_34fc770@0" ObjectIDZND0="g_34fdba0@0" ObjectIDZND1="27585@x" ObjectIDZND2="27584@x" Pin0InfoVect0LinkObjId="g_34fdba0_0" Pin0InfoVect1LinkObjId="SW-179300_0" Pin0InfoVect2LinkObjId="SW-179299_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34fc770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1144,-1061 1144,-1045 1186,-1045 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_34fd940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1186,-1045 1186,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_34fdba0@0" ObjectIDND1="g_34fc770@0" ObjectIDZND0="27585@x" ObjectIDZND1="27584@x" Pin0InfoVect0LinkObjId="SW-179300_0" Pin0InfoVect1LinkObjId="SW-179299_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_34fdba0_0" Pin1InfoVect1LinkObjId="g_34fc770_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1186,-1045 1186,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3508730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="355,-339 341,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27571@0" ObjectIDZND0="g_3508990@0" Pin0InfoVect0LinkObjId="g_3508990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179288_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="355,-339 341,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3509420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="391,-339 401,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27571@1" ObjectIDZND0="g_350bbb0@0" ObjectIDZND1="g_350dec0@0" ObjectIDZND2="27569@x" Pin0InfoVect0LinkObjId="g_350bbb0_0" Pin0InfoVect1LinkObjId="g_350dec0_0" Pin0InfoVect2LinkObjId="SW-179286_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179288_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="391,-339 401,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_350da00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="401,-325 401,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_350bbb0@0" ObjectIDND1="g_350dec0@0" ObjectIDZND0="27571@x" ObjectIDZND1="27569@x" Pin0InfoVect0LinkObjId="SW-179288_0" Pin0InfoVect1LinkObjId="SW-179286_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_350bbb0_0" Pin1InfoVect1LinkObjId="g_350dec0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="401,-325 401,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_350dc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="401,-363 401,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27569@0" ObjectIDZND0="g_350bbb0@0" ObjectIDZND1="g_350dec0@0" ObjectIDZND2="27571@x" Pin0InfoVect0LinkObjId="g_350bbb0_0" Pin0InfoVect1LinkObjId="g_350dec0_0" Pin0InfoVect2LinkObjId="SW-179288_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179286_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="401,-363 401,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_350e940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="401,-285 401,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_350dec0@1" ObjectIDZND0="g_350bbb0@0" ObjectIDZND1="27571@x" ObjectIDZND2="27569@x" Pin0InfoVect0LinkObjId="g_350bbb0_0" Pin0InfoVect1LinkObjId="SW-179288_0" Pin0InfoVect2LinkObjId="SW-179286_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_350dec0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="401,-285 401,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3511e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="401,-148 401,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="27572@x" ObjectIDND1="g_34eba40@0" ObjectIDND2="g_3515c90@0" ObjectIDZND0="27570@0" Pin0InfoVect0LinkObjId="SW-179287_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-179289_0" Pin1InfoVect1LinkObjId="g_34eba40_0" Pin1InfoVect2LinkObjId="g_3515c90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="401,-148 401,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35145e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="401,-148 381,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="27570@x" ObjectIDND1="g_34eba40@0" ObjectIDND2="g_3515c90@0" ObjectIDZND0="27572@1" Pin0InfoVect0LinkObjId="SW-179289_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-179287_0" Pin1InfoVect1LinkObjId="g_34eba40_0" Pin1InfoVect2LinkObjId="g_3515c90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="401,-148 381,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3514840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="345,-148 327,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27572@0" ObjectIDZND0="g_3514aa0@0" Pin0InfoVect0LinkObjId="g_3514aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179289_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="345,-148 327,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35157d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="415,-148 401,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3515c90@0" ObjectIDZND0="27570@x" ObjectIDZND1="27572@x" ObjectIDZND2="g_34eba40@0" Pin0InfoVect0LinkObjId="SW-179287_0" Pin0InfoVect1LinkObjId="SW-179289_0" Pin0InfoVect2LinkObjId="g_34eba40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3515c90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="415,-148 401,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3515a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="401,-109 401,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_34eba40@0" ObjectIDZND0="27570@x" ObjectIDZND1="27572@x" ObjectIDZND2="g_3515c90@0" Pin0InfoVect0LinkObjId="SW-179287_0" Pin0InfoVect1LinkObjId="SW-179289_0" Pin0InfoVect2LinkObjId="g_3515c90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34eba40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="401,-109 401,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3516a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="606,-316 606,-325 576,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_351a0e0@0" ObjectIDZND0="27577@x" ObjectIDZND1="27574@x" ObjectIDZND2="g_351b2d0@0" Pin0InfoVect0LinkObjId="SW-179293_0" Pin0InfoVect1LinkObjId="SW-179290_0" Pin0InfoVect2LinkObjId="g_351b2d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_351a0e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="606,-316 606,-325 576,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3516c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="530,-339 516,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27577@0" ObjectIDZND0="g_3516ec0@0" Pin0InfoVect0LinkObjId="g_3516ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179293_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="530,-339 516,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3517950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="566,-339 576,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27577@1" ObjectIDZND0="g_351b2d0@0" ObjectIDZND1="g_351a0e0@0" ObjectIDZND2="27574@x" Pin0InfoVect0LinkObjId="g_351b2d0_0" Pin0InfoVect1LinkObjId="g_351a0e0_0" Pin0InfoVect2LinkObjId="SW-179290_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179293_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="566,-339 576,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_351ae10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-325 576,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_351b2d0@0" ObjectIDND1="g_351a0e0@0" ObjectIDZND0="27577@x" ObjectIDZND1="27574@x" Pin0InfoVect0LinkObjId="SW-179293_0" Pin0InfoVect1LinkObjId="SW-179290_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_351b2d0_0" Pin1InfoVect1LinkObjId="g_351a0e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="576,-325 576,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_351b070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-363 576,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="27574@0" ObjectIDZND0="27577@x" ObjectIDZND1="g_351b2d0@0" ObjectIDZND2="g_351a0e0@0" Pin0InfoVect0LinkObjId="SW-179293_0" Pin0InfoVect1LinkObjId="g_351b2d0_0" Pin0InfoVect2LinkObjId="g_351a0e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="576,-363 576,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_351bd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-285 576,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_351b2d0@1" ObjectIDZND0="27577@x" ObjectIDZND1="27574@x" ObjectIDZND2="g_351a0e0@0" Pin0InfoVect0LinkObjId="SW-179293_0" Pin0InfoVect1LinkObjId="SW-179290_0" Pin0InfoVect2LinkObjId="g_351a0e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_351b2d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="576,-285 576,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_351e7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-225 576,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="27576@1" ObjectIDZND0="g_351b2d0@0" Pin0InfoVect0LinkObjId="g_351b2d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179292_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,-225 576,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_351ea10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-176 576,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="27578@x" ObjectIDND1="g_3524680@0" ObjectIDND2="0@x" ObjectIDZND0="27576@0" Pin0InfoVect0LinkObjId="SW-179292_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-179294_0" Pin1InfoVect1LinkObjId="g_3524680_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,-176 576,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35211a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-176 556,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="27576@x" ObjectIDND1="g_3524680@0" ObjectIDND2="0@x" ObjectIDZND0="27578@1" Pin0InfoVect0LinkObjId="SW-179294_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-179292_0" Pin1InfoVect1LinkObjId="g_3524680_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,-176 556,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3521400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="520,-176 502,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27578@0" ObjectIDZND0="g_3521660@0" Pin0InfoVect0LinkObjId="g_3521660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179294_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="520,-176 502,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3522390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="590,-176 576,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_3524680@0" ObjectIDZND0="27576@x" ObjectIDZND1="27578@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-179292_0" Pin0InfoVect1LinkObjId="SW-179294_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3524680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="590,-176 576,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35225f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-169 576,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="27576@x" ObjectIDZND1="27578@x" ObjectIDZND2="g_3524680@0" Pin0InfoVect0LinkObjId="SW-179292_0" Pin0InfoVect1LinkObjId="SW-179294_0" Pin0InfoVect2LinkObjId="g_3524680_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="576,-169 576,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3524420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="401,-197 401,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="27570@1" ObjectIDZND0="g_350dec0@0" Pin0InfoVect0LinkObjId="g_350dec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179287_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="401,-197 401,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352aad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-61 576,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="27579@1" Pin0InfoVect0LinkObjId="SW-179295_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,-61 576,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352ad30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-12 576,1 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27579@0" ObjectIDZND0="g_352af90@0" Pin0InfoVect0LinkObjId="g_352af90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179295_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,-12 576,1 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352c270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1144,-592 1144,-602 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_34ef8f0@0" ObjectIDND1="27587@x" ObjectIDZND0="g_34ec630@1" Pin0InfoVect0LinkObjId="g_34ec630_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_34ef8f0_0" Pin1InfoVect1LinkObjId="SW-179302_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1144,-592 1144,-602 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f3e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="782,-315 782,-324 752,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_35f7470@0" ObjectIDZND0="27583@x" ObjectIDZND1="27581@x" ObjectIDZND2="g_35f8660@0" Pin0InfoVect0LinkObjId="SW-179298_0" Pin0InfoVect1LinkObjId="SW-179296_0" Pin0InfoVect2LinkObjId="g_35f8660_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35f7470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="782,-315 782,-324 752,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f4010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,-459 752,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27580@0" ObjectIDZND0="27522@0" Pin0InfoVect0LinkObjId="g_35fcca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179296_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="752,-459 752,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f4200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="706,-338 692,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27583@0" ObjectIDZND0="g_35f43f0@0" Pin0InfoVect0LinkObjId="g_35f43f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179298_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="706,-338 692,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f4ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="742,-338 752,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27583@1" ObjectIDZND0="g_35f7470@0" ObjectIDZND1="g_35f8660@0" ObjectIDZND2="27581@x" Pin0InfoVect0LinkObjId="g_35f7470_0" Pin0InfoVect1LinkObjId="g_35f8660_0" Pin0InfoVect2LinkObjId="SW-179296_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179298_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="742,-338 752,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f81a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,-324 752,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_35f7470@0" ObjectIDND1="g_35f8660@0" ObjectIDZND0="27583@x" ObjectIDZND1="27581@x" Pin0InfoVect0LinkObjId="SW-179298_0" Pin0InfoVect1LinkObjId="SW-179296_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_35f7470_0" Pin1InfoVect1LinkObjId="g_35f8660_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="752,-324 752,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f8400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,-367 752,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27581@0" ObjectIDZND0="g_35f7470@0" ObjectIDZND1="g_35f8660@0" ObjectIDZND2="27583@x" Pin0InfoVect0LinkObjId="g_35f7470_0" Pin0InfoVect1LinkObjId="g_35f8660_0" Pin0InfoVect2LinkObjId="SW-179298_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179296_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="752,-367 752,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35f90e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,-284 752,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_35f8660@1" ObjectIDZND0="g_35f7470@0" ObjectIDZND1="27583@x" ObjectIDZND2="27581@x" Pin0InfoVect0LinkObjId="g_35f7470_0" Pin0InfoVect1LinkObjId="SW-179298_0" Pin0InfoVect2LinkObjId="SW-179296_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35f8660_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="752,-284 752,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35fc350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,-190 752,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_35f8660@0" Pin0InfoVect0LinkObjId="g_35f8660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="752,-190 752,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35fcab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="926,-314 926,-323 896,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_3600130@0" ObjectIDZND0="27542@x" ObjectIDZND1="27541@x" ObjectIDZND2="43384@x" Pin0InfoVect0LinkObjId="SW-179266_0" Pin0InfoVect1LinkObjId="SW-179265_0" Pin0InfoVect2LinkObjId="SM-CX_YS.P1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3600130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="926,-314 926,-323 896,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35fcca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-458 896,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27540@0" ObjectIDZND0="27522@0" Pin0InfoVect0LinkObjId="g_35f4010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179265_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-458 896,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35fce90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="850,-337 836,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27542@0" ObjectIDZND0="g_35fd0a0@0" Pin0InfoVect0LinkObjId="g_35fd0a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179266_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="850,-337 836,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35fd9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="886,-337 896,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="27542@1" ObjectIDZND0="g_3600130@0" ObjectIDZND1="43384@x" ObjectIDZND2="27541@x" Pin0InfoVect0LinkObjId="g_3600130_0" Pin0InfoVect1LinkObjId="SM-CX_YS.P1_0" Pin0InfoVect2LinkObjId="SW-179265_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179266_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="886,-337 896,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3600e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-323 896,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3600130@0" ObjectIDND1="43384@x" ObjectIDZND0="27542@x" ObjectIDZND1="27541@x" Pin0InfoVect0LinkObjId="SW-179266_0" Pin0InfoVect1LinkObjId="SW-179265_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3600130_0" Pin1InfoVect1LinkObjId="SM-CX_YS.P1_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="896,-323 896,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36010c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-366 896,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="27541@0" ObjectIDZND0="g_3600130@0" ObjectIDZND1="43384@x" ObjectIDZND2="27542@x" Pin0InfoVect0LinkObjId="g_3600130_0" Pin0InfoVect1LinkObjId="SM-CX_YS.P1_0" Pin0InfoVect2LinkObjId="SW-179266_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179265_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="896,-366 896,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3601320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-242 896,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="43384@0" ObjectIDZND0="g_3600130@0" ObjectIDZND1="27542@x" ObjectIDZND2="27541@x" Pin0InfoVect0LinkObjId="g_3600130_0" Pin0InfoVect1LinkObjId="SW-179266_0" Pin0InfoVect2LinkObjId="SW-179265_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_YS.P1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="896,-242 896,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3602900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1070,-313 1070,-322 1040,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_3606240@0" ObjectIDZND0="27546@x" ObjectIDZND1="27545@x" ObjectIDZND2="43385@x" Pin0InfoVect0LinkObjId="SW-179269_0" Pin0InfoVect1LinkObjId="SW-179268_0" Pin0InfoVect2LinkObjId="SM-CX_YS.P2_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3606240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1070,-313 1070,-322 1040,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3602b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-457 1040,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27544@0" ObjectIDZND0="27522@0" Pin0InfoVect0LinkObjId="g_35f4010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179268_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-457 1040,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3602dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="994,-336 980,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27546@0" ObjectIDZND0="g_3603020@0" Pin0InfoVect0LinkObjId="g_3603020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179269_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="994,-336 980,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3603ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1030,-336 1040,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="27546@1" ObjectIDZND0="g_3606240@0" ObjectIDZND1="43385@x" ObjectIDZND2="27545@x" Pin0InfoVect0LinkObjId="g_3606240_0" Pin0InfoVect1LinkObjId="SM-CX_YS.P2_0" Pin0InfoVect2LinkObjId="SW-179268_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179269_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1030,-336 1040,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3606f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-322 1040,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3606240@0" ObjectIDND1="43385@x" ObjectIDZND0="27546@x" ObjectIDZND1="27545@x" Pin0InfoVect0LinkObjId="SW-179269_0" Pin0InfoVect1LinkObjId="SW-179268_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3606240_0" Pin1InfoVect1LinkObjId="SM-CX_YS.P2_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-322 1040,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36071d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-365 1040,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="27545@0" ObjectIDZND0="g_3606240@0" ObjectIDZND1="43385@x" ObjectIDZND2="27546@x" Pin0InfoVect0LinkObjId="g_3606240_0" Pin0InfoVect1LinkObjId="SM-CX_YS.P2_0" Pin0InfoVect2LinkObjId="SW-179269_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179268_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-365 1040,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3607430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-241 1040,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="43385@0" ObjectIDZND0="g_3606240@0" ObjectIDZND1="27546@x" ObjectIDZND2="27545@x" Pin0InfoVect0LinkObjId="g_3606240_0" Pin0InfoVect1LinkObjId="SW-179269_0" Pin0InfoVect2LinkObjId="SW-179268_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_YS.P2_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-241 1040,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36087b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1214,-314 1214,-323 1184,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_360c0f0@0" ObjectIDZND0="27550@x" ObjectIDZND1="27549@x" ObjectIDZND2="43386@x" Pin0InfoVect0LinkObjId="SW-179272_0" Pin0InfoVect1LinkObjId="SW-179271_0" Pin0InfoVect2LinkObjId="SM-CX_YS.P3_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_360c0f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1214,-314 1214,-323 1184,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3608a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1184,-458 1184,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27548@0" ObjectIDZND0="27522@0" Pin0InfoVect0LinkObjId="g_35f4010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179271_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1184,-458 1184,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3608c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1138,-337 1124,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27550@0" ObjectIDZND0="g_3608ed0@0" Pin0InfoVect0LinkObjId="g_3608ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179272_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1138,-337 1124,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3609960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1174,-337 1184,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="27550@1" ObjectIDZND0="g_360c0f0@0" ObjectIDZND1="43386@x" ObjectIDZND2="27549@x" Pin0InfoVect0LinkObjId="g_360c0f0_0" Pin0InfoVect1LinkObjId="SM-CX_YS.P3_0" Pin0InfoVect2LinkObjId="SW-179271_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179272_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1174,-337 1184,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_360ce20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1184,-323 1184,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_360c0f0@0" ObjectIDND1="43386@x" ObjectIDZND0="27550@x" ObjectIDZND1="27549@x" Pin0InfoVect0LinkObjId="SW-179272_0" Pin0InfoVect1LinkObjId="SW-179271_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_360c0f0_0" Pin1InfoVect1LinkObjId="SM-CX_YS.P3_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1184,-323 1184,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_360d080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1184,-366 1184,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="27549@0" ObjectIDZND0="g_360c0f0@0" ObjectIDZND1="43386@x" ObjectIDZND2="27550@x" Pin0InfoVect0LinkObjId="g_360c0f0_0" Pin0InfoVect1LinkObjId="SM-CX_YS.P3_0" Pin0InfoVect2LinkObjId="SW-179272_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179271_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1184,-366 1184,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_360d2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1184,-242 1184,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="43386@0" ObjectIDZND0="g_360c0f0@0" ObjectIDZND1="27550@x" ObjectIDZND2="27549@x" Pin0InfoVect0LinkObjId="g_360c0f0_0" Pin0InfoVect1LinkObjId="SW-179272_0" Pin0InfoVect2LinkObjId="SW-179271_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_YS.P3_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1184,-242 1184,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_360d540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-314 1358,-323 1328,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_3610e80@0" ObjectIDZND0="27554@x" ObjectIDZND1="27553@x" ObjectIDZND2="43387@x" Pin0InfoVect0LinkObjId="SW-179275_0" Pin0InfoVect1LinkObjId="SW-179274_0" Pin0InfoVect2LinkObjId="SM-CX_YS.P4_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3610e80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-314 1358,-323 1328,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_360d7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1328,-458 1328,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27552@0" ObjectIDZND0="27522@0" Pin0InfoVect0LinkObjId="g_35f4010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179274_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1328,-458 1328,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_360da00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1282,-337 1268,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27554@0" ObjectIDZND0="g_360dc60@0" Pin0InfoVect0LinkObjId="g_360dc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179275_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1282,-337 1268,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_360e6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,-337 1328,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="27554@1" ObjectIDZND0="g_3610e80@0" ObjectIDZND1="43387@x" ObjectIDZND2="27553@x" Pin0InfoVect0LinkObjId="g_3610e80_0" Pin0InfoVect1LinkObjId="SM-CX_YS.P4_0" Pin0InfoVect2LinkObjId="SW-179274_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179275_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1318,-337 1328,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3611bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1328,-323 1328,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3610e80@0" ObjectIDND1="43387@x" ObjectIDZND0="27554@x" ObjectIDZND1="27553@x" Pin0InfoVect0LinkObjId="SW-179275_0" Pin0InfoVect1LinkObjId="SW-179274_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3610e80_0" Pin1InfoVect1LinkObjId="SM-CX_YS.P4_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1328,-323 1328,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3611e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1328,-366 1328,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="27553@0" ObjectIDZND0="g_3610e80@0" ObjectIDZND1="43387@x" ObjectIDZND2="27554@x" Pin0InfoVect0LinkObjId="g_3610e80_0" Pin0InfoVect1LinkObjId="SM-CX_YS.P4_0" Pin0InfoVect2LinkObjId="SW-179275_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179274_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1328,-366 1328,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3612070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1328,-242 1328,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43387@0" ObjectIDZND0="27554@x" ObjectIDZND1="27553@x" ObjectIDZND2="g_3610e80@0" Pin0InfoVect0LinkObjId="SW-179275_0" Pin0InfoVect1LinkObjId="SW-179274_0" Pin0InfoVect2LinkObjId="g_3610e80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_YS.P4_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1328,-242 1328,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3614510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1502,-311 1502,-320 1472,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_3617e50@0" ObjectIDZND0="27558@x" ObjectIDZND1="27557@x" ObjectIDZND2="43388@x" Pin0InfoVect0LinkObjId="SW-179278_0" Pin0InfoVect1LinkObjId="SW-179277_0" Pin0InfoVect2LinkObjId="SM-CX_YS.P5_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3617e50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1502,-311 1502,-320 1472,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3614770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1472,-455 1472,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27556@0" ObjectIDZND0="27522@0" Pin0InfoVect0LinkObjId="g_35f4010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179277_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1472,-455 1472,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36149d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1426,-334 1412,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27558@0" ObjectIDZND0="g_3614c30@0" Pin0InfoVect0LinkObjId="g_3614c30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179278_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1426,-334 1412,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36156c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1462,-334 1472,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="27558@1" ObjectIDZND0="g_3617e50@0" ObjectIDZND1="43388@x" ObjectIDZND2="27557@x" Pin0InfoVect0LinkObjId="g_3617e50_0" Pin0InfoVect1LinkObjId="SM-CX_YS.P5_0" Pin0InfoVect2LinkObjId="SW-179277_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179278_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1462,-334 1472,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3618b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1472,-320 1472,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3617e50@0" ObjectIDND1="43388@x" ObjectIDZND0="27558@x" ObjectIDZND1="27557@x" Pin0InfoVect0LinkObjId="SW-179278_0" Pin0InfoVect1LinkObjId="SW-179277_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3617e50_0" Pin1InfoVect1LinkObjId="SM-CX_YS.P5_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1472,-320 1472,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3618de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1472,-363 1472,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="27557@0" ObjectIDZND0="27558@x" ObjectIDZND1="g_3617e50@0" ObjectIDZND2="43388@x" Pin0InfoVect0LinkObjId="SW-179278_0" Pin0InfoVect1LinkObjId="g_3617e50_0" Pin0InfoVect2LinkObjId="SM-CX_YS.P5_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179277_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1472,-363 1472,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3619040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1472,-239 1472,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43388@0" ObjectIDZND0="27558@x" ObjectIDZND1="27557@x" ObjectIDZND2="g_3617e50@0" Pin0InfoVect0LinkObjId="SW-179278_0" Pin0InfoVect1LinkObjId="SW-179277_0" Pin0InfoVect2LinkObjId="g_3617e50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_YS.P5_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1472,-239 1472,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36192a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1646,-312 1646,-321 1616,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_361cbe0@0" ObjectIDZND0="27562@x" ObjectIDZND1="27561@x" ObjectIDZND2="43389@x" Pin0InfoVect0LinkObjId="SW-179281_0" Pin0InfoVect1LinkObjId="SW-179280_0" Pin0InfoVect2LinkObjId="SM-CX_YS.P6_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_361cbe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1646,-312 1646,-321 1616,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3619500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1616,-456 1616,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27560@0" ObjectIDZND0="27522@0" Pin0InfoVect0LinkObjId="g_35f4010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1616,-456 1616,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3619760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1570,-335 1556,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27562@0" ObjectIDZND0="g_36199c0@0" Pin0InfoVect0LinkObjId="g_36199c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179281_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1570,-335 1556,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_361a450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1606,-335 1616,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="27562@1" ObjectIDZND0="g_361cbe0@0" ObjectIDZND1="43389@x" ObjectIDZND2="27561@x" Pin0InfoVect0LinkObjId="g_361cbe0_0" Pin0InfoVect1LinkObjId="SM-CX_YS.P6_0" Pin0InfoVect2LinkObjId="SW-179280_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179281_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1606,-335 1616,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_361d910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1616,-321 1616,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_361cbe0@0" ObjectIDND1="43389@x" ObjectIDZND0="27562@x" ObjectIDZND1="27561@x" Pin0InfoVect0LinkObjId="SW-179281_0" Pin0InfoVect1LinkObjId="SW-179280_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_361cbe0_0" Pin1InfoVect1LinkObjId="SM-CX_YS.P6_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1616,-321 1616,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_361db70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1616,-364 1616,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="27561@0" ObjectIDZND0="27562@x" ObjectIDZND1="g_361cbe0@0" ObjectIDZND2="43389@x" Pin0InfoVect0LinkObjId="SW-179281_0" Pin0InfoVect1LinkObjId="g_361cbe0_0" Pin0InfoVect2LinkObjId="SM-CX_YS.P6_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1616,-364 1616,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_361ddd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1616,-240 1616,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="43389@0" ObjectIDZND0="g_361cbe0@0" ObjectIDZND1="27562@x" ObjectIDZND2="27561@x" Pin0InfoVect0LinkObjId="g_361cbe0_0" Pin0InfoVect1LinkObjId="SW-179281_0" Pin0InfoVect2LinkObjId="SW-179280_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_YS.P6_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1616,-240 1616,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_361e030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1790,-312 1790,-321 1760,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_3621970@0" ObjectIDZND0="27566@x" ObjectIDZND1="27565@x" ObjectIDZND2="43390@x" Pin0InfoVect0LinkObjId="SW-179284_0" Pin0InfoVect1LinkObjId="SW-179283_0" Pin0InfoVect2LinkObjId="SM-CX_YS.P7_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3621970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1790,-312 1790,-321 1760,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_361e290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,-456 1760,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27564@0" ObjectIDZND0="27522@0" Pin0InfoVect0LinkObjId="g_35f4010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179283_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1760,-456 1760,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_361e4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1714,-335 1700,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27566@0" ObjectIDZND0="g_361e750@0" Pin0InfoVect0LinkObjId="g_361e750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179284_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1714,-335 1700,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_361f1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1750,-335 1760,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="27566@1" ObjectIDZND0="g_3621970@0" ObjectIDZND1="43390@x" ObjectIDZND2="27565@x" Pin0InfoVect0LinkObjId="g_3621970_0" Pin0InfoVect1LinkObjId="SM-CX_YS.P7_0" Pin0InfoVect2LinkObjId="SW-179283_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179284_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1750,-335 1760,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36226a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,-321 1760,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3621970@0" ObjectIDND1="43390@x" ObjectIDZND0="27566@x" ObjectIDZND1="27565@x" Pin0InfoVect0LinkObjId="SW-179284_0" Pin0InfoVect1LinkObjId="SW-179283_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3621970_0" Pin1InfoVect1LinkObjId="SM-CX_YS.P7_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1760,-321 1760,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3622900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,-364 1760,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="27565@0" ObjectIDZND0="27566@x" ObjectIDZND1="g_3621970@0" ObjectIDZND2="43390@x" Pin0InfoVect0LinkObjId="SW-179284_0" Pin0InfoVect1LinkObjId="g_3621970_0" Pin0InfoVect2LinkObjId="SM-CX_YS.P7_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179283_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1760,-364 1760,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3622b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,-240 1760,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="43390@0" ObjectIDZND0="g_3621970@0" ObjectIDZND1="27566@x" ObjectIDZND2="27565@x" Pin0InfoVect0LinkObjId="g_3621970_0" Pin0InfoVect1LinkObjId="SW-179284_0" Pin0InfoVect2LinkObjId="SW-179283_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_YS.P7_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1760,-240 1760,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36368a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="401,-380 401,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27569@1" ObjectIDZND0="27567@0" Pin0InfoVect0LinkObjId="SW-179285_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179286_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="401,-380 401,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3636b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="401,-424 401,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27567@1" ObjectIDZND0="27568@1" Pin0InfoVect0LinkObjId="SW-179286_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179285_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="401,-424 401,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3659a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-380 576,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27574@1" ObjectIDZND0="27575@0" Pin0InfoVect0LinkObjId="SW-179291_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179290_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,-380 576,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3659c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-423 576,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27575@1" ObjectIDZND0="27573@1" Pin0InfoVect0LinkObjId="SW-179290_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179291_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,-423 576,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3659ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,-384 752,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27581@1" ObjectIDZND0="27582@0" Pin0InfoVect0LinkObjId="SW-179297_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179296_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="752,-384 752,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_365a120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,-426 752,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27582@1" ObjectIDZND0="27580@1" Pin0InfoVect0LinkObjId="SW-179296_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179297_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="752,-426 752,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_365a380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-383 896,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27541@1" ObjectIDZND0="27539@0" Pin0InfoVect0LinkObjId="SW-179264_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179265_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-383 896,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_365a5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-424 896,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27539@1" ObjectIDZND0="27540@1" Pin0InfoVect0LinkObjId="SW-179265_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179264_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-424 896,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_365a840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-382 1040,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27545@1" ObjectIDZND0="27543@0" Pin0InfoVect0LinkObjId="SW-179267_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179268_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-382 1040,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_365aaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-421 1040,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27543@1" ObjectIDZND0="27544@1" Pin0InfoVect0LinkObjId="SW-179268_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179267_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-421 1040,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3678b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1184,-383 1184,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27549@1" ObjectIDZND0="27547@0" Pin0InfoVect0LinkObjId="SW-179270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179271_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1184,-383 1184,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3678dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1184,-425 1184,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27547@1" ObjectIDZND0="27548@1" Pin0InfoVect0LinkObjId="SW-179271_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179270_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1184,-425 1184,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3679030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1328,-383 1328,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27553@1" ObjectIDZND0="27551@0" Pin0InfoVect0LinkObjId="SW-179273_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179274_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1328,-383 1328,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3679290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1328,-427 1328,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27551@1" ObjectIDZND0="27552@1" Pin0InfoVect0LinkObjId="SW-179274_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179273_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1328,-427 1328,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36794f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1472,-380 1472,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27557@1" ObjectIDZND0="27555@0" Pin0InfoVect0LinkObjId="SW-179276_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179277_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1472,-380 1472,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3679750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1472,-422 1472,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27555@1" ObjectIDZND0="27556@1" Pin0InfoVect0LinkObjId="SW-179277_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179276_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1472,-422 1472,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36799b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1616,-381 1616,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27561@1" ObjectIDZND0="27559@0" Pin0InfoVect0LinkObjId="SW-179279_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179280_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1616,-381 1616,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3679c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1616,-422 1616,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27559@1" ObjectIDZND0="27560@1" Pin0InfoVect0LinkObjId="SW-179280_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179279_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1616,-422 1616,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3681e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,-381 1760,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27565@1" ObjectIDZND0="27563@0" Pin0InfoVect0LinkObjId="SW-179282_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179283_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1760,-381 1760,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36820a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,-423 1760,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27563@1" ObjectIDZND0="27564@1" Pin0InfoVect0LinkObjId="SW-179283_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179282_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1760,-423 1760,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_369e670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1144,-481 1144,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27522@0" ObjectIDZND0="27588@0" Pin0InfoVect0LinkObjId="SW-179302_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35f4010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1144,-481 1144,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_369e8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1144,-528 1144,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="27588@1" ObjectIDZND0="27587@1" Pin0InfoVect0LinkObjId="SW-179302_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179302_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1144,-528 1144,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_369eb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1144,-563 1144,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="27587@0" ObjectIDZND0="g_34ec630@0" ObjectIDZND1="g_34ef8f0@0" Pin0InfoVect0LinkObjId="g_34ec630_0" Pin0InfoVect1LinkObjId="g_34ef8f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179302_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1144,-563 1144,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_36b8a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2187,-818 2179,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_36b6ab0@0" ObjectIDZND0="45316@1" Pin0InfoVect0LinkObjId="SW-293378_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36b6ab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2187,-818 2179,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_36b8cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2187,-770 2179,-770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_36b7540@0" ObjectIDZND0="45317@1" Pin0InfoVect0LinkObjId="SW-293379_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36b7540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2187,-770 2179,-770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_36b8f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2143,-770 2130,-770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="45317@0" ObjectIDZND0="45313@x" ObjectIDZND1="45315@x" Pin0InfoVect0LinkObjId="SW-293375_0" Pin0InfoVect1LinkObjId="SW-293377_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293379_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2143,-770 2130,-770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_36b9180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2187,-711 2178,-711 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_36b7fd0@0" ObjectIDZND0="45318@1" Pin0InfoVect0LinkObjId="SW-293380_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36b7fd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2187,-711 2178,-711 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_36b93e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-827 2130,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="45314@0" ObjectIDZND0="45316@x" ObjectIDZND1="45313@x" Pin0InfoVect0LinkObjId="SW-293378_0" Pin0InfoVect1LinkObjId="SW-293375_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293376_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-827 2130,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_36b9640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-818 2143,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="45314@x" ObjectIDND1="45313@x" ObjectIDZND0="45316@0" Pin0InfoVect0LinkObjId="SW-293378_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-293376_0" Pin1InfoVect1LinkObjId="SW-293375_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-818 2143,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_36b98a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-818 2130,-807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="45314@x" ObjectIDND1="45316@x" ObjectIDZND0="45313@1" Pin0InfoVect0LinkObjId="SW-293375_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-293376_0" Pin1InfoVect1LinkObjId="SW-293378_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-818 2130,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_36b9b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-780 2130,-770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="45313@0" ObjectIDZND0="45317@x" ObjectIDZND1="45315@x" Pin0InfoVect0LinkObjId="SW-293379_0" Pin0InfoVect1LinkObjId="SW-293377_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293375_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-780 2130,-770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_36b9d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-759 2130,-770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="45315@1" ObjectIDZND0="45317@x" ObjectIDZND1="45313@x" Pin0InfoVect0LinkObjId="SW-293379_0" Pin0InfoVect1LinkObjId="SW-293375_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293377_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-759 2130,-770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36ba840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2401,-626 2401,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_36b9fc0@0" ObjectIDZND0="g_36baaa0@0" Pin0InfoVect0LinkObjId="g_36baaa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36b9fc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2401,-626 2401,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36be400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2471,-585 2402,-585 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_36bd650@0" ObjectIDZND0="g_36b9fc0@0" ObjectIDZND1="45354@x" Pin0InfoVect0LinkObjId="g_36b9fc0_0" Pin0InfoVect1LinkObjId="SW-293447_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36bd650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2471,-585 2402,-585 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36bf640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2401,-585 2401,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_36bd650@0" ObjectIDND1="45354@x" ObjectIDZND0="g_36b9fc0@1" Pin0InfoVect0LinkObjId="g_36b9fc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36bd650_0" Pin1InfoVect1LinkObjId="SW-293447_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2401,-585 2401,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36cd640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2401,-521 2401,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="45353@1" ObjectIDZND0="45354@1" Pin0InfoVect0LinkObjId="SW-293447_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293447_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2401,-521 2401,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36cd8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2401,-556 2401,-585 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="45354@0" ObjectIDZND0="g_36bd650@0" ObjectIDZND1="g_36b9fc0@0" Pin0InfoVect0LinkObjId="g_36bd650_0" Pin0InfoVect1LinkObjId="g_36b9fc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293447_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2401,-556 2401,-585 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_36cf4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-899 896,-880 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27525@0" ObjectIDZND0="27521@0" Pin0InfoVect0LinkObjId="g_36cf710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179251_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-899 896,-880 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_36cf710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-868 896,-880 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27530@1" ObjectIDZND0="27521@0" Pin0InfoVect0LinkObjId="g_36cf4b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179256_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-868 896,-880 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_36d0ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1160,-918 1186,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="27586@1" ObjectIDZND0="27584@x" ObjectIDZND1="27521@0" Pin0InfoVect0LinkObjId="SW-179299_0" Pin0InfoVect1LinkObjId="g_36cf4b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179301_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1160,-918 1186,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_36d15d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1186,-946 1186,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="27584@0" ObjectIDZND0="27586@x" ObjectIDZND1="27521@0" Pin0InfoVect0LinkObjId="SW-179301_0" Pin0InfoVect1LinkObjId="g_36cf4b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179299_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1186,-946 1186,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_36d1830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1186,-918 1186,-880 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="27586@x" ObjectIDND1="27584@x" ObjectIDZND0="27521@0" Pin0InfoVect0LinkObjId="g_36cf4b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-179301_0" Pin1InfoVect1LinkObjId="SW-179299_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1186,-918 1186,-880 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d1a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2093,-309 2093,-318 2063,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_36d5170@0" ObjectIDZND0="45323@x" ObjectIDZND1="45322@x" ObjectIDZND2="46548@x" Pin0InfoVect0LinkObjId="SW-293407_0" Pin0InfoVect1LinkObjId="SW-293406_0" Pin0InfoVect2LinkObjId="SM-CX_YS.P8_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36d5170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2093,-309 2093,-318 2063,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d1cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2017,-332 2003,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="45323@0" ObjectIDZND0="g_36d1f50@0" Pin0InfoVect0LinkObjId="g_36d1f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293407_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2017,-332 2003,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d29e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2053,-332 2063,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="45323@1" ObjectIDZND0="g_36d5170@0" ObjectIDZND1="46548@x" ObjectIDZND2="45322@x" Pin0InfoVect0LinkObjId="g_36d5170_0" Pin0InfoVect1LinkObjId="SM-CX_YS.P8_0" Pin0InfoVect2LinkObjId="SW-293406_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293407_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2053,-332 2063,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d5ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2063,-318 2063,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_36d5170@0" ObjectIDND1="46548@x" ObjectIDZND0="45323@x" ObjectIDZND1="45322@x" Pin0InfoVect0LinkObjId="SW-293407_0" Pin0InfoVect1LinkObjId="SW-293406_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36d5170_0" Pin1InfoVect1LinkObjId="SM-CX_YS.P8_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2063,-318 2063,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d6100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2063,-361 2063,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="45322@0" ObjectIDZND0="45323@x" ObjectIDZND1="g_36d5170@0" ObjectIDZND2="46548@x" Pin0InfoVect0LinkObjId="SW-293407_0" Pin0InfoVect1LinkObjId="g_36d5170_0" Pin0InfoVect2LinkObjId="SM-CX_YS.P8_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293406_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2063,-361 2063,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d6360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2063,-237 2063,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="46548@0" ObjectIDZND0="45323@x" ObjectIDZND1="45322@x" ObjectIDZND2="g_36d5170@0" Pin0InfoVect0LinkObjId="SW-293407_0" Pin0InfoVect1LinkObjId="SW-293406_0" Pin0InfoVect2LinkObjId="g_36d5170_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_YS.P8_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2063,-237 2063,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d65c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2267,-308 2267,-317 2237,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_36d9ca0@0" ObjectIDZND0="45327@x" ObjectIDZND1="45326@x" ObjectIDZND2="46549@x" Pin0InfoVect0LinkObjId="SW-293411_0" Pin0InfoVect1LinkObjId="SW-293410_0" Pin0InfoVect2LinkObjId="SM-CX_YS.P9_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36d9ca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2267,-308 2267,-317 2237,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d6820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2191,-331 2177,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="45327@0" ObjectIDZND0="g_36d6a80@0" Pin0InfoVect0LinkObjId="g_36d6a80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293411_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2191,-331 2177,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d7510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2227,-331 2237,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="generator" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="45327@1" ObjectIDZND0="46549@x" ObjectIDZND1="g_36d9ca0@0" ObjectIDZND2="45326@x" Pin0InfoVect0LinkObjId="SM-CX_YS.P9_0" Pin0InfoVect1LinkObjId="g_36d9ca0_0" Pin0InfoVect2LinkObjId="SW-293410_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293411_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2227,-331 2237,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36da9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2237,-317 2237,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="46549@x" ObjectIDND1="g_36d9ca0@0" ObjectIDZND0="45327@x" ObjectIDZND1="45326@x" Pin0InfoVect0LinkObjId="SW-293411_0" Pin0InfoVect1LinkObjId="SW-293410_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SM-CX_YS.P9_0" Pin1InfoVect1LinkObjId="g_36d9ca0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2237,-317 2237,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36dac30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2237,-360 2237,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="generator" EndDevType2="lightningRod" ObjectIDND0="45326@0" ObjectIDZND0="45327@x" ObjectIDZND1="46549@x" ObjectIDZND2="g_36d9ca0@0" Pin0InfoVect0LinkObjId="SW-293411_0" Pin0InfoVect1LinkObjId="SM-CX_YS.P9_0" Pin0InfoVect2LinkObjId="g_36d9ca0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2237,-360 2237,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36dae90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2237,-236 2237,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="46549@0" ObjectIDZND0="45327@x" ObjectIDZND1="45326@x" ObjectIDZND2="g_36d9ca0@0" Pin0InfoVect0LinkObjId="SW-293411_0" Pin0InfoVect1LinkObjId="SW-293410_0" Pin0InfoVect2LinkObjId="g_36d9ca0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_YS.P9_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2237,-236 2237,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36db0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2838,-305 2838,-314 2808,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_36de520@0" ObjectIDZND0="45331@x" ObjectIDZND1="45330@x" ObjectIDZND2="46550@x" Pin0InfoVect0LinkObjId="SW-293415_0" Pin0InfoVect1LinkObjId="SW-293414_0" Pin0InfoVect2LinkObjId="SM-CX_YS.P10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36de520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2838,-305 2838,-314 2808,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36dbd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2798,-328 2808,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="45331@1" ObjectIDZND0="g_36de520@0" ObjectIDZND1="46550@x" ObjectIDZND2="45330@x" Pin0InfoVect0LinkObjId="g_36de520_0" Pin0InfoVect1LinkObjId="SM-CX_YS.P10_0" Pin0InfoVect2LinkObjId="SW-293414_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293415_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2798,-328 2808,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36df250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2808,-314 2808,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_36de520@0" ObjectIDND1="46550@x" ObjectIDZND0="45331@x" ObjectIDZND1="45330@x" Pin0InfoVect0LinkObjId="SW-293415_0" Pin0InfoVect1LinkObjId="SW-293414_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36de520_0" Pin1InfoVect1LinkObjId="SM-CX_YS.P10_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2808,-314 2808,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36df4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2808,-357 2808,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="45330@0" ObjectIDZND0="45331@x" ObjectIDZND1="g_36de520@0" ObjectIDZND2="46550@x" Pin0InfoVect0LinkObjId="SW-293415_0" Pin0InfoVect1LinkObjId="g_36de520_0" Pin0InfoVect2LinkObjId="SM-CX_YS.P10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293414_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2808,-357 2808,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36df710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2808,-233 2808,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="46550@0" ObjectIDZND0="g_36de520@0" ObjectIDZND1="45331@x" ObjectIDZND2="45330@x" Pin0InfoVect0LinkObjId="g_36de520_0" Pin0InfoVect1LinkObjId="SW-293415_0" Pin0InfoVect2LinkObjId="SW-293414_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_YS.P10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2808,-233 2808,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36df970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2982,-305 2982,-314 2952,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_36e3050@0" ObjectIDZND0="45335@x" ObjectIDZND1="45334@x" ObjectIDZND2="46551@x" Pin0InfoVect0LinkObjId="SW-293419_0" Pin0InfoVect1LinkObjId="SW-293418_0" Pin0InfoVect2LinkObjId="SM-CX_YS.P11_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36e3050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2982,-305 2982,-314 2952,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36dfbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2906,-328 2892,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="45335@0" ObjectIDZND0="g_36dfe30@0" Pin0InfoVect0LinkObjId="g_36dfe30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293419_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2906,-328 2892,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36e08c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2942,-328 2952,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="45335@1" ObjectIDZND0="g_36e3050@0" ObjectIDZND1="46551@x" ObjectIDZND2="45334@x" Pin0InfoVect0LinkObjId="g_36e3050_0" Pin0InfoVect1LinkObjId="SM-CX_YS.P11_0" Pin0InfoVect2LinkObjId="SW-293418_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293419_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2942,-328 2952,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36e3d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2952,-314 2952,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_36e3050@0" ObjectIDND1="46551@x" ObjectIDZND0="45335@x" ObjectIDZND1="45334@x" Pin0InfoVect0LinkObjId="SW-293419_0" Pin0InfoVect1LinkObjId="SW-293418_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36e3050_0" Pin1InfoVect1LinkObjId="SM-CX_YS.P11_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2952,-314 2952,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36e3fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2952,-357 2952,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="45334@0" ObjectIDZND0="45335@x" ObjectIDZND1="g_36e3050@0" ObjectIDZND2="46551@x" Pin0InfoVect0LinkObjId="SW-293419_0" Pin0InfoVect1LinkObjId="g_36e3050_0" Pin0InfoVect2LinkObjId="SM-CX_YS.P11_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293418_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2952,-357 2952,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36e4240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2952,-233 2952,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="46551@0" ObjectIDZND0="g_36e3050@0" ObjectIDZND1="45335@x" ObjectIDZND2="45334@x" Pin0InfoVect0LinkObjId="g_36e3050_0" Pin0InfoVect1LinkObjId="SW-293419_0" Pin0InfoVect2LinkObjId="SW-293418_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_YS.P11_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2952,-233 2952,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36e44a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3126,-302 3126,-311 3096,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_36e7b80@0" ObjectIDZND0="45351@x" ObjectIDZND1="45350@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-293435_0" Pin0InfoVect1LinkObjId="SW-293434_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36e7b80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3126,-302 3126,-311 3096,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36e4700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3050,-325 3036,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="45351@0" ObjectIDZND0="g_36e4960@0" Pin0InfoVect0LinkObjId="g_36e4960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293435_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3050,-325 3036,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36e53f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3086,-325 3096,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="45351@1" ObjectIDZND0="g_36e7b80@0" ObjectIDZND1="0@x" ObjectIDZND2="45350@x" Pin0InfoVect0LinkObjId="g_36e7b80_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-293434_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293435_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3086,-325 3096,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36e88b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3096,-311 3096,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_36e7b80@0" ObjectIDND1="0@x" ObjectIDZND0="45351@x" ObjectIDZND1="45350@x" Pin0InfoVect0LinkObjId="SW-293435_0" Pin0InfoVect1LinkObjId="SW-293434_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36e7b80_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3096,-311 3096,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36e8b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3096,-354 3096,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="45350@0" ObjectIDZND0="45351@x" ObjectIDZND1="g_36e7b80@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-293435_0" Pin0InfoVect1LinkObjId="g_36e7b80_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293434_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3096,-354 3096,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36e8d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3096,-230 3096,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_36e7b80@0" ObjectIDZND1="45351@x" ObjectIDZND2="45350@x" Pin0InfoVect0LinkObjId="g_36e7b80_0" Pin0InfoVect1LinkObjId="SW-293435_0" Pin0InfoVect2LinkObjId="SW-293434_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3096,-230 3096,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36e8fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3373,-642 3373,-633 3403,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_36ec690@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36ec690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3373,-642 3373,-633 3403,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36e9230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3449,-619 3463,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_36e9490@0" Pin0InfoVect0LinkObjId="g_36e9490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3449,-619 3463,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36e9f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3413,-619 3403,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="generator" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_36ec690@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_36ec690_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3413,-619 3403,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36ed3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3403,-633 3403,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_36ec690@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_36ec690_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3403,-633 3403,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36ed620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3403,-590 3403,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="generator" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_36ec690@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_36ec690_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3403,-590 3403,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36ed880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3403,-714 3403,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_36ec690@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_36ec690_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3403,-714 3403,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36edae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3433,-310 3433,-319 3403,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_36f11a0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36f11a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3433,-310 3433,-319 3403,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36edd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3357,-333 3343,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_36edfa0@0" Pin0InfoVect0LinkObjId="g_36edfa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3357,-333 3343,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36eea30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3393,-333 3403,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_36f11a0@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_36f11a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3393,-333 3403,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36f1ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3403,-319 3403,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_36f11a0@0" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36f11a0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3403,-319 3403,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36f2130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3403,-362 3403,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_36f11a0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_36f11a0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3403,-362 3403,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36f2390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3403,-238 3403,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_36f11a0@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_36f11a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3403,-238 3403,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37048c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2063,-378 2063,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="45322@1" ObjectIDZND0="45320@0" Pin0InfoVect0LinkObjId="SW-293405_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293406_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2063,-378 2063,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3704b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2063,-419 2063,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="45320@1" ObjectIDZND0="45321@1" Pin0InfoVect0LinkObjId="SW-293406_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293405_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2063,-419 2063,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3704d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2237,-377 2237,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="45326@1" ObjectIDZND0="45324@0" Pin0InfoVect0LinkObjId="SW-293409_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293410_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2237,-377 2237,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3704fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2237,-416 2237,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="45324@1" ObjectIDZND0="45325@1" Pin0InfoVect0LinkObjId="SW-293410_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293409_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2237,-416 2237,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3723010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2808,-374 2808,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="45330@1" ObjectIDZND0="45328@0" Pin0InfoVect0LinkObjId="SW-293413_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293414_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2808,-374 2808,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3723270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2808,-416 2808,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="45328@1" ObjectIDZND0="45329@1" Pin0InfoVect0LinkObjId="SW-293414_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293413_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2808,-416 2808,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37234d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2952,-374 2952,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="45334@1" ObjectIDZND0="45332@0" Pin0InfoVect0LinkObjId="SW-293417_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293418_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2952,-374 2952,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3723730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2952,-418 2952,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="45332@1" ObjectIDZND0="45333@1" Pin0InfoVect0LinkObjId="SW-293418_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293417_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2952,-418 2952,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3723990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3096,-371 3096,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="45350@1" ObjectIDZND0="45348@0" Pin0InfoVect0LinkObjId="SW-293433_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293434_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3096,-371 3096,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3723bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3096,-413 3096,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="45348@1" ObjectIDZND0="45349@1" Pin0InfoVect0LinkObjId="SW-293434_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293433_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3096,-413 3096,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3723e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3403,-573 3403,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3403,-573 3403,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37240b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3403,-532 3403,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3403,-532 3403,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_372c240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3403,-379 3403,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3403,-379 3403,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_372c4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3403,-421 3403,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3403,-421 3403,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_373b180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2063,-453 2063,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="45321@0" ObjectIDZND0="45312@0" Pin0InfoVect0LinkObjId="g_3770e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293406_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2063,-453 2063,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_373d8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2431,-317 2431,-326 2401,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3741040@0" ObjectIDZND0="45343@x" ObjectIDZND1="45342@x" ObjectIDZND2="g_3742230@0" Pin0InfoVect0LinkObjId="SW-293427_0" Pin0InfoVect1LinkObjId="SW-293426_0" Pin0InfoVect2LinkObjId="g_3742230_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3741040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2431,-317 2431,-326 2401,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_373ded0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2355,-340 2341,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="45343@0" ObjectIDZND0="g_373e0c0@0" Pin0InfoVect0LinkObjId="g_373e0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293427_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2355,-340 2341,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_373e8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2391,-340 2401,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="45343@1" ObjectIDZND0="g_3741040@0" ObjectIDZND1="g_3742230@0" ObjectIDZND2="45342@x" Pin0InfoVect0LinkObjId="g_3741040_0" Pin0InfoVect1LinkObjId="g_3742230_0" Pin0InfoVect2LinkObjId="SW-293426_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293427_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2391,-340 2401,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3741d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2401,-326 2401,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3741040@0" ObjectIDND1="g_3742230@0" ObjectIDZND0="45343@x" ObjectIDZND1="45342@x" Pin0InfoVect0LinkObjId="SW-293427_0" Pin0InfoVect1LinkObjId="SW-293426_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3741040_0" Pin1InfoVect1LinkObjId="g_3742230_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2401,-326 2401,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3741fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2401,-364 2401,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="45342@0" ObjectIDZND0="45343@x" ObjectIDZND1="g_3741040@0" ObjectIDZND2="g_3742230@0" Pin0InfoVect0LinkObjId="SW-293427_0" Pin0InfoVect1LinkObjId="g_3741040_0" Pin0InfoVect2LinkObjId="g_3742230_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293426_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2401,-364 2401,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3742cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2401,-286 2401,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3742230@1" ObjectIDZND0="g_3741040@0" ObjectIDZND1="45343@x" ObjectIDZND2="45342@x" Pin0InfoVect0LinkObjId="g_3741040_0" Pin0InfoVect1LinkObjId="SW-293427_0" Pin0InfoVect2LinkObjId="SW-293426_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3742230_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2401,-286 2401,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37456b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2401,-149 2401,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_37491f0@0" ObjectIDND2="g_373cad0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_37491f0_0" Pin1InfoVect2LinkObjId="g_373cad0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2401,-149 2401,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3747de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2401,-149 2381,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_37491f0@0" ObjectIDND2="g_373cad0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_37491f0_0" Pin1InfoVect2LinkObjId="g_373cad0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2401,-149 2381,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3748040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2345,-149 2327,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_37482a0@0" Pin0InfoVect0LinkObjId="g_37482a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2345,-149 2327,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3748d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2415,-149 2401,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_37491f0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_373cad0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_373cad0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37491f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2415,-149 2401,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3748f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2401,-110 2401,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_373cad0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_37491f0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_37491f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_373cad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2401,-110 2401,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3749f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2401,-198 2401,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3742230@0" Pin0InfoVect0LinkObjId="g_3742230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2401,-198 2401,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3752190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2401,-381 2401,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="45342@1" ObjectIDZND0="45340@0" Pin0InfoVect0LinkObjId="SW-293425_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293426_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2401,-381 2401,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37523f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2401,-425 2401,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="45340@1" ObjectIDZND0="45341@1" Pin0InfoVect0LinkObjId="SW-293426_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293425_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2401,-425 2401,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3754220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="401,-455 401,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27568@0" ObjectIDZND0="27522@0" Pin0InfoVect0LinkObjId="g_35f4010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179286_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="401,-455 401,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3754a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2644,-328 2644,-337 2614,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3758130@0" ObjectIDZND0="45339@x" ObjectIDZND1="45338@x" ObjectIDZND2="g_3759320@0" Pin0InfoVect0LinkObjId="SW-293423_0" Pin0InfoVect1LinkObjId="SW-293422_0" Pin0InfoVect2LinkObjId="g_3759320_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3758130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2644,-328 2644,-337 2614,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3754cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2568,-351 2554,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="45339@0" ObjectIDZND0="g_3754f10@0" Pin0InfoVect0LinkObjId="g_3754f10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293423_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2568,-351 2554,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37559a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2604,-351 2614,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="45339@1" ObjectIDZND0="g_3758130@0" ObjectIDZND1="g_3759320@0" ObjectIDZND2="45338@x" Pin0InfoVect0LinkObjId="g_3758130_0" Pin0InfoVect1LinkObjId="g_3759320_0" Pin0InfoVect2LinkObjId="SW-293422_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293423_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2604,-351 2614,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3758e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2614,-337 2614,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3758130@0" ObjectIDND1="g_3759320@0" ObjectIDZND0="45339@x" ObjectIDZND1="45338@x" Pin0InfoVect0LinkObjId="SW-293423_0" Pin0InfoVect1LinkObjId="SW-293422_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3758130_0" Pin1InfoVect1LinkObjId="g_3759320_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2614,-337 2614,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37590c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2614,-375 2614,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="45338@0" ObjectIDZND0="g_3758130@0" ObjectIDZND1="g_3759320@0" ObjectIDZND2="45339@x" Pin0InfoVect0LinkObjId="g_3758130_0" Pin0InfoVect1LinkObjId="g_3759320_0" Pin0InfoVect2LinkObjId="SW-293423_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293422_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2614,-375 2614,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3759da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2614,-297 2614,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3759320@1" ObjectIDZND0="45339@x" ObjectIDZND1="45338@x" ObjectIDZND2="g_3758130@0" Pin0InfoVect0LinkObjId="SW-293423_0" Pin0InfoVect1LinkObjId="SW-293422_0" Pin0InfoVect2LinkObjId="g_3758130_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3759320_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2614,-297 2614,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_375c7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2614,-237 2614,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3759320@0" Pin0InfoVect0LinkObjId="g_3759320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2614,-237 2614,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_375ca00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2614,-188 2614,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_375fab0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_375fab0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2614,-188 2614,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_375f130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2614,-188 2594,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_375fab0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_375fab0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2614,-188 2594,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_375f390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2558,-188 2540,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_37ba730@0" Pin0InfoVect0LinkObjId="g_37ba730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2558,-188 2540,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_375f5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2628,-188 2614,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_375fab0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_375fab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2628,-188 2614,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_375f850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2614,-181 2614,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_375fab0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_375fab0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2614,-181 2614,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3765ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2614,-73 2614,-60 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2614,-73 2614,-60 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3766100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2614,-24 2614,-11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_3766360@0" Pin0InfoVect0LinkObjId="g_3766360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2614,-24 2614,-11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_376ed80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2614,-392 2614,-408 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="45338@1" ObjectIDZND0="45336@0" Pin0InfoVect0LinkObjId="SW-293421_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293422_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2614,-392 2614,-408 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_376efe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2614,-435 2614,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="45336@1" ObjectIDZND0="45337@1" Pin0InfoVect0LinkObjId="SW-293422_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293421_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2614,-435 2614,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3770e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2237,-452 2237,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="45325@0" ObjectIDZND0="45312@0" Pin0InfoVect0LinkObjId="g_373b180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2237,-452 2237,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3771640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2401,-456 2401,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="45341@0" ObjectIDZND0="45312@0" Pin0InfoVect0LinkObjId="g_373b180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293426_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2401,-456 2401,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3771e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2401,-504 2401,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="45353@0" ObjectIDZND0="45312@0" Pin0InfoVect0LinkObjId="g_373b180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293447_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2401,-504 2401,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_37726a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-863 2130,-880 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="45314@1" ObjectIDZND0="27521@0" Pin0InfoVect0LinkObjId="g_36cf4b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293376_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-863 2130,-880 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3772ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2742,-328 2762,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_36db300@0" ObjectIDZND0="45331@0" Pin0InfoVect0LinkObjId="SW-293415_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36db300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2742,-328 2762,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3773130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3282,-300 3282,-309 3252,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3776810@0" ObjectIDZND0="45347@x" ObjectIDZND1="45346@x" ObjectIDZND2="g_3777a00@0" Pin0InfoVect0LinkObjId="SW-293431_0" Pin0InfoVect1LinkObjId="SW-293430_0" Pin0InfoVect2LinkObjId="g_3777a00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3776810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3282,-300 3282,-309 3252,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3773390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3206,-323 3192,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="45347@0" ObjectIDZND0="g_37735f0@0" Pin0InfoVect0LinkObjId="g_37735f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293431_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3206,-323 3192,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3774080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3242,-323 3252,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="45347@1" ObjectIDZND0="g_3776810@0" ObjectIDZND1="g_3777a00@0" ObjectIDZND2="45346@x" Pin0InfoVect0LinkObjId="g_3776810_0" Pin0InfoVect1LinkObjId="g_3777a00_0" Pin0InfoVect2LinkObjId="SW-293430_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293431_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3242,-323 3252,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3777540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3252,-309 3252,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3776810@0" ObjectIDND1="g_3777a00@0" ObjectIDZND0="45347@x" ObjectIDZND1="45346@x" Pin0InfoVect0LinkObjId="SW-293431_0" Pin0InfoVect1LinkObjId="SW-293430_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3776810_0" Pin1InfoVect1LinkObjId="g_3777a00_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3252,-309 3252,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37777a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3252,-352 3252,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="45346@0" ObjectIDZND0="g_3776810@0" ObjectIDZND1="g_3777a00@0" ObjectIDZND2="45347@x" Pin0InfoVect0LinkObjId="g_3776810_0" Pin0InfoVect1LinkObjId="g_3777a00_0" Pin0InfoVect2LinkObjId="SW-293431_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3252,-352 3252,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3778480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3252,-269 3252,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3777a00@1" ObjectIDZND0="g_3776810@0" ObjectIDZND1="45347@x" ObjectIDZND2="45346@x" Pin0InfoVect0LinkObjId="g_3776810_0" Pin0InfoVect1LinkObjId="SW-293431_0" Pin0InfoVect2LinkObjId="SW-293430_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3777a00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3252,-269 3252,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37806b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3252,-369 3252,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="45346@1" ObjectIDZND0="45344@0" Pin0InfoVect0LinkObjId="SW-293429_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293430_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3252,-369 3252,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3780910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3252,-418 3252,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="45345@1" Pin0InfoVect0LinkObjId="SW-293430_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3252,-418 3252,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3781c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2808,-449 2808,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="45329@0" ObjectIDZND0="45312@0" Pin0InfoVect0LinkObjId="g_373b180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293414_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2808,-449 2808,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37824c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2952,-449 2952,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="45333@0" ObjectIDZND0="45312@0" Pin0InfoVect0LinkObjId="g_373b180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293418_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2952,-449 2952,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3782cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3096,-446 3096,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="45349@0" ObjectIDZND0="45312@0" Pin0InfoVect0LinkObjId="g_373b180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293434_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3096,-446 3096,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3783520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3252,-444 3252,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="45345@0" ObjectIDZND0="45312@0" Pin0InfoVect0LinkObjId="g_373b180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3252,-444 3252,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3783d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3222,-642 3222,-633 3252,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_37873d0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37873d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3222,-642 3222,-633 3252,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3783fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3298,-619 3312,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_3784210@0" Pin0InfoVect0LinkObjId="g_3784210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3298,-619 3312,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3784ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3262,-619 3252,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_37873d0@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_37873d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3262,-619 3252,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3788100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3252,-633 3252,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_37873d0@0" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_37873d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3252,-633 3252,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3788360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3252,-590 3252,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_37873d0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_37873d0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3252,-590 3252,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37885c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3252,-714 3252,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_37873d0@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_37873d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3252,-714 3252,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3790710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3252,-573 3252,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3252,-573 3252,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3790970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3252,-532 3252,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3252,-532 3252,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37921a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3066,-644 3066,-635 3096,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_3795820@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3795820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3066,-644 3066,-635 3096,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3792400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3142,-621 3156,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_3792660@0" Pin0InfoVect0LinkObjId="g_3792660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3142,-621 3156,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37930f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3106,-621 3096,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_3795820@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3795820_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3106,-621 3096,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3796550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3096,-635 3096,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3795820@0" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3795820_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3096,-635 3096,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37967b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3096,-592 3096,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_3795820@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3795820_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3096,-592 3096,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3796a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3096,-716 3096,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_3795820@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3795820_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3096,-716 3096,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_379eb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3096,-575 3096,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3096,-575 3096,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_379edc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3096,-534 3096,-517 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3096,-534 3096,-517 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37a1710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2922,-642 2922,-633 2952,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_37a4d90@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37a4d90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2922,-642 2922,-633 2952,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37a1970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2998,-619 3012,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_37a1bd0@0" Pin0InfoVect0LinkObjId="g_37a1bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2998,-619 3012,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37a2660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2962,-619 2952,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_37a4d90@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_37a4d90_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2962,-619 2952,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37a5ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2952,-633 2952,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_37a4d90@0" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_37a4d90_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2952,-633 2952,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37a5d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2952,-590 2952,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="generator" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_37a4d90@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_37a4d90_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2952,-590 2952,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37a5f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2952,-714 2952,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_37a4d90@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_37a4d90_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2952,-714 2952,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37ae0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2952,-573 2952,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2952,-573 2952,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37ae330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2952,-532 2952,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2952,-532 2952,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37b0c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2952,-498 2952,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="45312@0" Pin0InfoVect0LinkObjId="g_373b180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2952,-498 2952,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37b25d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3096,-500 3096,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="45312@0" Pin0InfoVect0LinkObjId="g_373b180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3096,-500 3096,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37b2e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3252,-498 3252,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="45312@0" Pin0InfoVect0LinkObjId="g_373b180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3252,-498 3252,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37b3630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2614,-467 2614,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="45337@0" ObjectIDZND0="45312@0" Pin0InfoVect0LinkObjId="g_373b180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293422_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2614,-467 2614,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37b5d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3252,-235 3252,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3777a00@0" ObjectIDZND0="g_37b3e60@0" Pin0InfoVect0LinkObjId="g_37b3e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3777a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3252,-235 3252,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37b7a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3207,-1245 3207,-1205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3207,-1245 3207,-1205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37b7cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3207,-1111 3207,-1047 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3207,-1111 3207,-1047 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37c0080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3253,-182 3224,-182 3224,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3253,-182 3224,-182 3224,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_37c4c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2227,-625 2227,-637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="45977@0" Pin0InfoVect0LinkObjId="SW-296325_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2227,-625 2227,-637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37d45a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3403,-498 3403,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="45312@0" Pin0InfoVect0LinkObjId="g_373b180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3403,-498 3403,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37d4c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3403,-454 3403,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="45312@0" Pin0InfoVect0LinkObjId="g_373b180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3403,-454 3403,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_37d6640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2142,-711 2130,-711 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="45318@0" ObjectIDZND0="45315@x" Pin0InfoVect0LinkObjId="SW-293377_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2142,-711 2130,-711 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_37d6fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-723 2130,-711 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="45315@0" ObjectIDZND0="45318@x" Pin0InfoVect0LinkObjId="SW-293380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293377_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-723 2130,-711 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_37d71a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-711 2131,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="45315@x" ObjectIDND1="45318@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-293377_0" Pin1InfoVect1LinkObjId="SW-293380_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-711 2131,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37d8920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="576,-455 576,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27573@0" ObjectIDZND0="27522@0" Pin0InfoVect0LinkObjId="g_35f4010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="576,-455 576,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37d9720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-578 896,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27537@1" ObjectIDZND0="27536@1" Pin0InfoVect0LinkObjId="SW-179262_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179263_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-578 896,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37d9980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-537 896,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27536@0" ObjectIDZND0="27538@1" Pin0InfoVect0LinkObjId="SW-179263_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179262_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-537 896,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37d9be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-506 896,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27538@0" ObjectIDZND0="27522@0" Pin0InfoVect0LinkObjId="g_35f4010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179263_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-506 896,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37da410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-575 2130,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="45975@1" ObjectIDZND0="45319@1" Pin0InfoVect0LinkObjId="SW-293381_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296324_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-575 2130,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37da670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-531 2130,-517 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="45319@0" ObjectIDZND0="45976@1" Pin0InfoVect0LinkObjId="SW-296324_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-293381_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-531 2130,-517 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37da8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-500 2130,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="45976@0" ObjectIDZND0="45312@0" Pin0InfoVect0LinkObjId="g_373b180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296324_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-500 2130,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_37dab30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-701 896,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="46579@0" ObjectIDZND0="27534@x" ObjectIDZND1="27532@x" Pin0InfoVect0LinkObjId="SW-179260_0" Pin0InfoVect1LinkObjId="SW-179258_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37dc830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="896,-701 896,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_37db620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="908,-717 896,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="27534@0" ObjectIDZND0="27532@x" ObjectIDZND1="46579@x" Pin0InfoVect0LinkObjId="SW-179258_0" Pin0InfoVect1LinkObjId="g_37dc830_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="908,-717 896,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_37db880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-717 896,-728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="27534@x" ObjectIDND1="46579@x" ObjectIDZND0="27532@0" Pin0InfoVect0LinkObjId="SW-179258_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-179260_0" Pin1InfoVect1LinkObjId="g_37dc830_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-717 896,-728 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37dbae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="947,-589 947,-605 896,-605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_34f7d00@0" ObjectIDZND0="27537@x" ObjectIDZND1="46579@x" Pin0InfoVect0LinkObjId="SW-179263_0" Pin0InfoVect1LinkObjId="g_37dc830_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34f7d00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="947,-589 947,-605 896,-605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37dc5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-595 896,-605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="27537@0" ObjectIDZND0="g_34f7d00@0" ObjectIDZND1="46579@x" Pin0InfoVect0LinkObjId="g_34f7d00_0" Pin0InfoVect1LinkObjId="g_37dc830_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179263_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="896,-595 896,-605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37dc830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-605 896,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_34f7d00@0" ObjectIDND1="27537@x" ObjectIDZND0="46579@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_34f7d00_0" Pin1InfoVect1LinkObjId="SW-179263_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-605 896,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37dca90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2182,-581 2182,-599 2130,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_36beb60@0" ObjectIDZND0="45975@x" Pin0InfoVect0LinkObjId="SW-296324_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36beb60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2182,-581 2182,-599 2130,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37dd580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-592 2130,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="45975@0" ObjectIDZND0="g_36beb60@0" Pin0InfoVect0LinkObjId="g_36beb60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296324_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-592 2130,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37dd7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-599 2131,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_36beb60@0" ObjectIDND1="45975@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36beb60_0" Pin1InfoVect1LinkObjId="SW-296324_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-599 2131,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_37dda40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-681 2227,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="45977@x" Pin0InfoVect0LinkObjId="SW-296325_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-681 2227,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_37de530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2227,-673 2227,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="45977@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296325_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2227,-673 2227,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_37de790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2227,-681 2273,-681 2273,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="45977@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296325_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2227,-681 2273,-681 2273,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_37df120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-1181 896,-1201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_34fbb80@0" ObjectIDND1="g_34cf170@0" ObjectIDND2="27529@x" ObjectIDZND0="9207@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34fbb80_0" Pin1InfoVect1LinkObjId="g_34cf170_0" Pin1InfoVect2LinkObjId="SW-179255_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-1181 896,-1201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_37dfa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="919,-1181 896,-1181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_34fbb80@0" ObjectIDZND0="g_34cf170@0" ObjectIDZND1="27529@x" ObjectIDZND2="27526@x" Pin0InfoVect0LinkObjId="g_34cf170_0" Pin0InfoVect1LinkObjId="SW-179255_0" Pin0InfoVect2LinkObjId="SW-179252_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34fbb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="919,-1181 896,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_37dfc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-1181 896,-1163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_34fbb80@0" ObjectIDND1="9207@1" ObjectIDZND0="g_34cf170@0" ObjectIDZND1="27529@x" ObjectIDZND2="27526@x" Pin0InfoVect0LinkObjId="g_34cf170_0" Pin0InfoVect1LinkObjId="SW-179255_0" Pin0InfoVect2LinkObjId="SW-179252_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_34fbb80_0" Pin1InfoVect1LinkObjId="g_37df120_1" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="896,-1181 896,-1163 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectPoint_Layer"/><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-178737" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1009.000000 -1006.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178737" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27524"/>
     <cge:Term_Ref ObjectID="38688"/>
    <cge:TPSR_Ref TObjectID="27524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-178738" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1009.000000 -1006.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178738" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27524"/>
     <cge:Term_Ref ObjectID="38688"/>
    <cge:TPSR_Ref TObjectID="27524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-178736" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1009.000000 -1006.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178736" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27524"/>
     <cge:Term_Ref ObjectID="38688"/>
    <cge:TPSR_Ref TObjectID="27524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-178723" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 600.000000 -951.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178723" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27521"/>
     <cge:Term_Ref ObjectID="28396"/>
    <cge:TPSR_Ref TObjectID="27521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-178724" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 600.000000 -951.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178724" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27521"/>
     <cge:Term_Ref ObjectID="28396"/>
    <cge:TPSR_Ref TObjectID="27521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-178725" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 600.000000 -951.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178725" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27521"/>
     <cge:Term_Ref ObjectID="28396"/>
    <cge:TPSR_Ref TObjectID="27521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-178726" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 600.000000 -951.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178726" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27521"/>
     <cge:Term_Ref ObjectID="28396"/>
    <cge:TPSR_Ref TObjectID="27521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-178720" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1061.000000 -820.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178720" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27535"/>
     <cge:Term_Ref ObjectID="38964"/>
    <cge:TPSR_Ref TObjectID="27535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-178721" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1061.000000 -820.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178721" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27535"/>
     <cge:Term_Ref ObjectID="38964"/>
    <cge:TPSR_Ref TObjectID="27535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-178719" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1061.000000 -820.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178719" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27535"/>
     <cge:Term_Ref ObjectID="38964"/>
    <cge:TPSR_Ref TObjectID="27535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-178729" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 377.000000 -557.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178729" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27522"/>
     <cge:Term_Ref ObjectID="28397"/>
    <cge:TPSR_Ref TObjectID="27522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-178730" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 377.000000 -557.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178730" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27522"/>
     <cge:Term_Ref ObjectID="28397"/>
    <cge:TPSR_Ref TObjectID="27522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-178731" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 377.000000 -557.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178731" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27522"/>
     <cge:Term_Ref ObjectID="28397"/>
    <cge:TPSR_Ref TObjectID="27522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-178732" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 377.000000 -557.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178732" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27522"/>
     <cge:Term_Ref ObjectID="28397"/>
    <cge:TPSR_Ref TObjectID="27522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-178776" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 424.000000 10.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178776" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27567"/>
     <cge:Term_Ref ObjectID="39028"/>
    <cge:TPSR_Ref TObjectID="27567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-178777" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 424.000000 10.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178777" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27567"/>
     <cge:Term_Ref ObjectID="39028"/>
    <cge:TPSR_Ref TObjectID="27567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-178775" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 424.000000 10.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178775" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27567"/>
     <cge:Term_Ref ObjectID="39028"/>
    <cge:TPSR_Ref TObjectID="27567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-178772" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 593.000000 23.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178772" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27575"/>
     <cge:Term_Ref ObjectID="39044"/>
    <cge:TPSR_Ref TObjectID="27575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-178773" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 593.000000 23.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178773" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27575"/>
     <cge:Term_Ref ObjectID="39044"/>
    <cge:TPSR_Ref TObjectID="27575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-178771" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 593.000000 23.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178771" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27575"/>
     <cge:Term_Ref ObjectID="39044"/>
    <cge:TPSR_Ref TObjectID="27575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-178768" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 776.000000 10.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178768" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27582"/>
     <cge:Term_Ref ObjectID="39058"/>
    <cge:TPSR_Ref TObjectID="27582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-178769" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 776.000000 10.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178769" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27582"/>
     <cge:Term_Ref ObjectID="39058"/>
    <cge:TPSR_Ref TObjectID="27582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-178767" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 776.000000 10.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178767" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27582"/>
     <cge:Term_Ref ObjectID="39058"/>
    <cge:TPSR_Ref TObjectID="27582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-178741" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 925.000000 10.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178741" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27539"/>
     <cge:Term_Ref ObjectID="38972"/>
    <cge:TPSR_Ref TObjectID="27539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-179873" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 925.000000 10.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="179873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27539"/>
     <cge:Term_Ref ObjectID="38972"/>
    <cge:TPSR_Ref TObjectID="27539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-178740" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 925.000000 10.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178740" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27539"/>
     <cge:Term_Ref ObjectID="38972"/>
    <cge:TPSR_Ref TObjectID="27539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-178744" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1068.000000 10.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178744" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27543"/>
     <cge:Term_Ref ObjectID="38980"/>
    <cge:TPSR_Ref TObjectID="27543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-178745" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1068.000000 10.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178745" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27543"/>
     <cge:Term_Ref ObjectID="38980"/>
    <cge:TPSR_Ref TObjectID="27543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-178743" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1068.000000 10.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178743" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27543"/>
     <cge:Term_Ref ObjectID="38980"/>
    <cge:TPSR_Ref TObjectID="27543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-178748" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1214.000000 10.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178748" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27547"/>
     <cge:Term_Ref ObjectID="38988"/>
    <cge:TPSR_Ref TObjectID="27547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-178749" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1214.000000 10.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178749" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27547"/>
     <cge:Term_Ref ObjectID="38988"/>
    <cge:TPSR_Ref TObjectID="27547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-178747" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1214.000000 10.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178747" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27547"/>
     <cge:Term_Ref ObjectID="38988"/>
    <cge:TPSR_Ref TObjectID="27547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-178752" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1352.000000 10.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178752" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27551"/>
     <cge:Term_Ref ObjectID="38996"/>
    <cge:TPSR_Ref TObjectID="27551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-178753" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1352.000000 10.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178753" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27551"/>
     <cge:Term_Ref ObjectID="38996"/>
    <cge:TPSR_Ref TObjectID="27551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-178751" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1352.000000 10.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178751" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27551"/>
     <cge:Term_Ref ObjectID="38996"/>
    <cge:TPSR_Ref TObjectID="27551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-178756" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1490.000000 10.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178756" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27555"/>
     <cge:Term_Ref ObjectID="39004"/>
    <cge:TPSR_Ref TObjectID="27555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-178757" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1490.000000 10.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178757" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27555"/>
     <cge:Term_Ref ObjectID="39004"/>
    <cge:TPSR_Ref TObjectID="27555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-178755" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1490.000000 10.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178755" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27555"/>
     <cge:Term_Ref ObjectID="39004"/>
    <cge:TPSR_Ref TObjectID="27555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-178760" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1639.000000 10.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178760" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27559"/>
     <cge:Term_Ref ObjectID="39012"/>
    <cge:TPSR_Ref TObjectID="27559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-178761" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1639.000000 10.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178761" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27559"/>
     <cge:Term_Ref ObjectID="39012"/>
    <cge:TPSR_Ref TObjectID="27559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-178759" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1639.000000 10.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178759" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27559"/>
     <cge:Term_Ref ObjectID="39012"/>
    <cge:TPSR_Ref TObjectID="27559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-178764" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1791.000000 10.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178764" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27563"/>
     <cge:Term_Ref ObjectID="39020"/>
    <cge:TPSR_Ref TObjectID="27563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-178765" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1791.000000 10.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178765" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27563"/>
     <cge:Term_Ref ObjectID="39020"/>
    <cge:TPSR_Ref TObjectID="27563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-178763" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1791.000000 10.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="178763" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27563"/>
     <cge:Term_Ref ObjectID="39020"/>
    <cge:TPSR_Ref TObjectID="27563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-179870" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1054.000000 -566.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="179870" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27536"/>
     <cge:Term_Ref ObjectID="38966"/>
    <cge:TPSR_Ref TObjectID="27536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-179871" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1054.000000 -566.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="179871" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27536"/>
     <cge:Term_Ref ObjectID="38966"/>
    <cge:TPSR_Ref TObjectID="27536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-179869" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1054.000000 -566.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="179869" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27536"/>
     <cge:Term_Ref ObjectID="38966"/>
    <cge:TPSR_Ref TObjectID="27536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-293326" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2045.000000 8.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293326" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45320"/>
     <cge:Term_Ref ObjectID="24309"/>
    <cge:TPSR_Ref TObjectID="45320"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-293327" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2045.000000 8.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293327" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45320"/>
     <cge:Term_Ref ObjectID="24309"/>
    <cge:TPSR_Ref TObjectID="45320"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-293323" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2045.000000 8.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293323" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45320"/>
     <cge:Term_Ref ObjectID="24309"/>
    <cge:TPSR_Ref TObjectID="45320"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-293332" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2217.000000 9.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293332" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45324"/>
     <cge:Term_Ref ObjectID="24321"/>
    <cge:TPSR_Ref TObjectID="45324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-293333" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2217.000000 9.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293333" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45324"/>
     <cge:Term_Ref ObjectID="24321"/>
    <cge:TPSR_Ref TObjectID="45324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-293329" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2217.000000 9.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293329" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45324"/>
     <cge:Term_Ref ObjectID="24321"/>
    <cge:TPSR_Ref TObjectID="45324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-293355" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2405.000000 8.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293355" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45340"/>
     <cge:Term_Ref ObjectID="24369"/>
    <cge:TPSR_Ref TObjectID="45340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-293356" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2405.000000 8.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293356" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45340"/>
     <cge:Term_Ref ObjectID="24369"/>
    <cge:TPSR_Ref TObjectID="45340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-293352" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2405.000000 8.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293352" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45340"/>
     <cge:Term_Ref ObjectID="24369"/>
    <cge:TPSR_Ref TObjectID="45340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-293349" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2611.000000 48.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293349" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45336"/>
     <cge:Term_Ref ObjectID="24361"/>
    <cge:TPSR_Ref TObjectID="45336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-293350" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2611.000000 48.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293350" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45336"/>
     <cge:Term_Ref ObjectID="24361"/>
    <cge:TPSR_Ref TObjectID="45336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-293347" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2611.000000 48.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293347" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45336"/>
     <cge:Term_Ref ObjectID="24361"/>
    <cge:TPSR_Ref TObjectID="45336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-293338" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2800.000000 7.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293338" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45328"/>
     <cge:Term_Ref ObjectID="24345"/>
    <cge:TPSR_Ref TObjectID="45328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-293339" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2800.000000 7.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293339" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45328"/>
     <cge:Term_Ref ObjectID="24345"/>
    <cge:TPSR_Ref TObjectID="45328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-293335" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2800.000000 7.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293335" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45328"/>
     <cge:Term_Ref ObjectID="24345"/>
    <cge:TPSR_Ref TObjectID="45328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-293344" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2950.000000 9.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293344" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45332"/>
     <cge:Term_Ref ObjectID="24353"/>
    <cge:TPSR_Ref TObjectID="45332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-293345" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2950.000000 9.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293345" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45332"/>
     <cge:Term_Ref ObjectID="24353"/>
    <cge:TPSR_Ref TObjectID="45332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-293341" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2950.000000 9.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293341" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45332"/>
     <cge:Term_Ref ObjectID="24353"/>
    <cge:TPSR_Ref TObjectID="45332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-293367" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3110.000000 9.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293367" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45348"/>
     <cge:Term_Ref ObjectID="24385"/>
    <cge:TPSR_Ref TObjectID="45348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-293368" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3110.000000 9.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293368" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45348"/>
     <cge:Term_Ref ObjectID="24385"/>
    <cge:TPSR_Ref TObjectID="45348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-293364" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3110.000000 9.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293364" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45348"/>
     <cge:Term_Ref ObjectID="24385"/>
    <cge:TPSR_Ref TObjectID="45348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-293361" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3275.000000 9.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293361" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45344"/>
     <cge:Term_Ref ObjectID="24377"/>
    <cge:TPSR_Ref TObjectID="45344"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-293362" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3275.000000 9.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293362" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45344"/>
     <cge:Term_Ref ObjectID="24377"/>
    <cge:TPSR_Ref TObjectID="45344"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-293358" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3275.000000 9.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293358" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45344"/>
     <cge:Term_Ref ObjectID="24377"/>
    <cge:TPSR_Ref TObjectID="45344"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-293299" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2323.000000 -824.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293299" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45313"/>
     <cge:Term_Ref ObjectID="22363"/>
    <cge:TPSR_Ref TObjectID="45313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-293300" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2323.000000 -824.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293300" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45313"/>
     <cge:Term_Ref ObjectID="22363"/>
    <cge:TPSR_Ref TObjectID="45313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-293290" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2323.000000 -824.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293290" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45313"/>
     <cge:Term_Ref ObjectID="22363"/>
    <cge:TPSR_Ref TObjectID="45313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-293311" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2263.000000 -550.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293311" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45319"/>
     <cge:Term_Ref ObjectID="24307"/>
    <cge:TPSR_Ref TObjectID="45319"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-293312" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2263.000000 -550.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293312" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45319"/>
     <cge:Term_Ref ObjectID="24307"/>
    <cge:TPSR_Ref TObjectID="45319"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-293302" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2263.000000 -550.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293302" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45319"/>
     <cge:Term_Ref ObjectID="24307"/>
    <cge:TPSR_Ref TObjectID="45319"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-293315" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3510.000000 -551.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293315" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45312"/>
     <cge:Term_Ref ObjectID="22362"/>
    <cge:TPSR_Ref TObjectID="45312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-293316" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3510.000000 -551.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293316" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45312"/>
     <cge:Term_Ref ObjectID="22362"/>
    <cge:TPSR_Ref TObjectID="45312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-293317" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3510.000000 -551.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293317" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45312"/>
     <cge:Term_Ref ObjectID="22362"/>
    <cge:TPSR_Ref TObjectID="45312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-293318" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3510.000000 -551.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="293318" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="45312"/>
     <cge:Term_Ref ObjectID="22362"/>
    <cge:TPSR_Ref TObjectID="45312"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="151" x="90" y="-1209"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="151" x="90" y="-1209"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="41" y="-1226"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="41" y="-1226"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="270,-1084 267,-1087 267,-1033 270,-1036 270,-1084" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="270,-1084 267,-1087 416,-1087 413,-1084 270,-1084" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(112,119,119)" points="270,-1036 267,-1033 416,-1033 413,-1036 270,-1036" stroke="rgb(112,119,119)"/>
     <polygon fill="rgb(112,119,119)" points="413,-1084 416,-1087 416,-1033 413,-1036 413,-1084" stroke="rgb(112,119,119)"/>
     <rect fill="rgb(224,238,238)" height="48" stroke="rgb(224,238,238)" width="143" x="270" y="-1084"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="270" y="-1084"/>
    </a>
   <metadata/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_省调直调电厂_风电.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="151" x="90" y="-1209"/></g>
   <g href="cx_索引_接线图_省地共调_风电.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="41" y="-1226"/></g>
   <g href="AVC秧田箐.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="270" y="-1084"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="895" x2="832" y1="-684" y2="-684"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="832" x2="832" y1="-692" y2="-676"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="825" x2="825" y1="-689" y2="-679"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="819" x2="819" y1="-686" y2="-682"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="895" x2="849" y1="-639" y2="-639"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="849" x2="849" y1="-639" y2="-632"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="850" x2="850" y1="-617" y2="-610"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="846" x2="852" y1="-610" y2="-610"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="847" x2="851" y1="-608" y2="-608"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="848" x2="850" y1="-606" y2="-606"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="2" stroke="rgb(255,255,0)" stroke-dasharray="10 5 " stroke-width="1" x1="397" x2="363" y1="-178" y2="-178"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="2" stroke="rgb(255,255,0)" stroke-dasharray="10 5 " stroke-width="1" x1="363" x2="363" y1="-178" y2="-152"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="2" stroke="rgb(255,255,0)" stroke-dasharray="10 5 " stroke-width="1" x1="572" x2="538" y1="-206" y2="-206"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="2" stroke="rgb(255,255,0)" stroke-dasharray="10 5 " stroke-width="1" x1="538" x2="538" y1="-206" y2="-180"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="3223" x2="3223" y1="-88" y2="-134"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="0.423034" x1="2248" x2="2248" y1="-680" y2="-639"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="2247" x2="2247" y1="-640" y2="-607"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="2227" x2="2273" y1="-625" y2="-625"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="2159" x2="2164" y1="-694" y2="-694"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="2164" x2="2162" y1="-694" y2="-689"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2164" x2="2164" y1="-691" y2="-691"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="0.911765" x1="2094" x2="2162" y1="-655" y2="-693"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.510204" x1="2122" x2="2139" y1="-629" y2="-629"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.510204" x1="2131" x2="2139" y1="-645" y2="-629"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.510204" x1="2131" x2="2122" y1="-645" y2="-629"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="0.510204" x1="2123" x2="2131" y1="-685" y2="-677"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="0.510204" x1="2131" x2="2139" y1="-677" y2="-685"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="0.510204" x1="2131" x2="2131" y1="-669" y2="-677"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-179251">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 887.000000 -894.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27525" ObjectName="SW-CX_YS.CX_YS_2611SW"/>
     <cge:Meas_Ref ObjectId="179251"/>
    <cge:TPSR_Ref TObjectID="27525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179252">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 887.000000 -1025.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27526" ObjectName="SW-CX_YS.CX_YS_2616SW"/>
     <cge:Meas_Ref ObjectId="179252"/>
    <cge:TPSR_Ref TObjectID="27526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179254">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 846.000000 -1007.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27528" ObjectName="SW-CX_YS.CX_YS_26160SW"/>
     <cge:Meas_Ref ObjectId="179254"/>
    <cge:TPSR_Ref TObjectID="27528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179255">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 846.000000 -1075.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27529" ObjectName="SW-CX_YS.CX_YS_26167SW"/>
     <cge:Meas_Ref ObjectId="179255"/>
    <cge:TPSR_Ref TObjectID="27529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179299">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1177.000000 -941.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27584" ObjectName="SW-CX_YS.CX_YS_2901SW"/>
     <cge:Meas_Ref ObjectId="179299"/>
    <cge:TPSR_Ref TObjectID="27584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179300">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1133.000000 -1003.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27585" ObjectName="SW-CX_YS.CX_YS_29017SW"/>
     <cge:Meas_Ref ObjectId="179300"/>
    <cge:TPSR_Ref TObjectID="27585"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179256">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 886.565184 -827.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27530" ObjectName="SW-CX_YS.CX_YS_2011SW"/>
     <cge:Meas_Ref ObjectId="179256"/>
    <cge:TPSR_Ref TObjectID="27530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179258">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 886.565184 -723.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27532" ObjectName="SW-CX_YS.CX_YS_2016SW"/>
     <cge:Meas_Ref ObjectId="179258"/>
    <cge:TPSR_Ref TObjectID="27532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179257">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 902.565184 -818.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27531" ObjectName="SW-CX_YS.CX_YS_20117SW"/>
     <cge:Meas_Ref ObjectId="179257"/>
    <cge:TPSR_Ref TObjectID="27531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179259">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 902.565184 -770.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27533" ObjectName="SW-CX_YS.CX_YS_20160SW"/>
     <cge:Meas_Ref ObjectId="179259"/>
    <cge:TPSR_Ref TObjectID="27533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179260">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 902.565184 -712.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27534" ObjectName="SW-CX_YS.CX_YS_20167SW"/>
     <cge:Meas_Ref ObjectId="179260"/>
    <cge:TPSR_Ref TObjectID="27534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179253">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 846.000000 -945.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27527" ObjectName="SW-CX_YS.CX_YS_26117SW"/>
     <cge:Meas_Ref ObjectId="179253"/>
    <cge:TPSR_Ref TObjectID="27527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179301">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1119.000000 -913.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27586" ObjectName="SW-CX_YS.CX_YS_29010SW"/>
     <cge:Meas_Ref ObjectId="179301"/>
    <cge:TPSR_Ref TObjectID="27586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179288">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 350.000000 -334.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27571" ObjectName="SW-CX_YS.CX_YS_31160SW"/>
     <cge:Meas_Ref ObjectId="179288"/>
    <cge:TPSR_Ref TObjectID="27571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179287">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 392.000000 -156.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27570" ObjectName="SW-CX_YS.CX_YS_3116SW"/>
     <cge:Meas_Ref ObjectId="179287"/>
    <cge:TPSR_Ref TObjectID="27570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179289">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 340.000000 -143.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27572" ObjectName="SW-CX_YS.CX_YS_31167SW"/>
     <cge:Meas_Ref ObjectId="179289"/>
    <cge:TPSR_Ref TObjectID="27572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179293">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 525.000000 -334.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27577" ObjectName="SW-CX_YS.CX_YS_31237SW"/>
     <cge:Meas_Ref ObjectId="179293"/>
    <cge:TPSR_Ref TObjectID="27577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179292">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 567.000000 -184.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27576" ObjectName="SW-CX_YS.CX_YS_3126SW"/>
     <cge:Meas_Ref ObjectId="179292"/>
    <cge:TPSR_Ref TObjectID="27576"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179294">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 515.000000 -171.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27578" ObjectName="SW-CX_YS.CX_YS_31260SW"/>
     <cge:Meas_Ref ObjectId="179294"/>
    <cge:TPSR_Ref TObjectID="27578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179295">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 567.000000 -7.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27579" ObjectName="SW-CX_YS.CX_YS_31267SW"/>
     <cge:Meas_Ref ObjectId="179295"/>
    <cge:TPSR_Ref TObjectID="27579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179298">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 701.000000 -333.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27583" ObjectName="SW-CX_YS.CX_YS_31360SW"/>
     <cge:Meas_Ref ObjectId="179298"/>
    <cge:TPSR_Ref TObjectID="27583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179266">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 845.000000 -332.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27542" ObjectName="SW-CX_YS.CX_YS_31467SW"/>
     <cge:Meas_Ref ObjectId="179266"/>
    <cge:TPSR_Ref TObjectID="27542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179269">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 989.000000 -331.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27546" ObjectName="SW-CX_YS.CX_YS_31567SW"/>
     <cge:Meas_Ref ObjectId="179269"/>
    <cge:TPSR_Ref TObjectID="27546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179272">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1133.000000 -332.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27550" ObjectName="SW-CX_YS.CX_YS_31667SW"/>
     <cge:Meas_Ref ObjectId="179272"/>
    <cge:TPSR_Ref TObjectID="27550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179275">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1277.000000 -332.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27554" ObjectName="SW-CX_YS.CX_YS_31767SW"/>
     <cge:Meas_Ref ObjectId="179275"/>
    <cge:TPSR_Ref TObjectID="27554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179278">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1421.000000 -329.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27558" ObjectName="SW-CX_YS.CX_YS_31867SW"/>
     <cge:Meas_Ref ObjectId="179278"/>
    <cge:TPSR_Ref TObjectID="27558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179281">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1565.000000 -330.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27562" ObjectName="SW-CX_YS.CX_YS_31967SW"/>
     <cge:Meas_Ref ObjectId="179281"/>
    <cge:TPSR_Ref TObjectID="27562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179284">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1709.000000 -330.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27566" ObjectName="SW-CX_YS.CX_YS_32167SW"/>
     <cge:Meas_Ref ObjectId="179284"/>
    <cge:TPSR_Ref TObjectID="27566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179286">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 391.000000 -431.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27568" ObjectName="SW-CX_YS.CX_YS_311XC"/>
     <cge:Meas_Ref ObjectId="179286"/>
    <cge:TPSR_Ref TObjectID="27568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179286">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 391.000000 -356.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27569" ObjectName="SW-CX_YS.CX_YS_311XC1"/>
     <cge:Meas_Ref ObjectId="179286"/>
    <cge:TPSR_Ref TObjectID="27569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179290">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 566.000000 -431.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27573" ObjectName="SW-CX_YS.CX_YS_312XC"/>
     <cge:Meas_Ref ObjectId="179290"/>
    <cge:TPSR_Ref TObjectID="27573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179296">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 742.000000 -435.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27580" ObjectName="SW-CX_YS.CX_YS_313XC"/>
     <cge:Meas_Ref ObjectId="179296"/>
    <cge:TPSR_Ref TObjectID="27580"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179265">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 886.000000 -434.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27540" ObjectName="SW-CX_YS.CX_YS_314XC"/>
     <cge:Meas_Ref ObjectId="179265"/>
    <cge:TPSR_Ref TObjectID="27540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179268">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1030.000000 -433.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27544" ObjectName="SW-CX_YS.CX_YS_315XC"/>
     <cge:Meas_Ref ObjectId="179268"/>
    <cge:TPSR_Ref TObjectID="27544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179290">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 566.000000 -356.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27574" ObjectName="SW-CX_YS.CX_YS_312XC1"/>
     <cge:Meas_Ref ObjectId="179290"/>
    <cge:TPSR_Ref TObjectID="27574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179296">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 742.000000 -360.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27581" ObjectName="SW-CX_YS.CX_YS_313XC1"/>
     <cge:Meas_Ref ObjectId="179296"/>
    <cge:TPSR_Ref TObjectID="27581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179265">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 886.000000 -359.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27541" ObjectName="SW-CX_YS.CX_YS_314XC1"/>
     <cge:Meas_Ref ObjectId="179265"/>
    <cge:TPSR_Ref TObjectID="27541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179268">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1030.000000 -358.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27545" ObjectName="SW-CX_YS.CX_YS_315XC1"/>
     <cge:Meas_Ref ObjectId="179268"/>
    <cge:TPSR_Ref TObjectID="27545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179271">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1174.000000 -434.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27548" ObjectName="SW-CX_YS.CX_YS_316XC"/>
     <cge:Meas_Ref ObjectId="179271"/>
    <cge:TPSR_Ref TObjectID="27548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179274">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1318.000000 -434.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27552" ObjectName="SW-CX_YS.CX_YS_317XC"/>
     <cge:Meas_Ref ObjectId="179274"/>
    <cge:TPSR_Ref TObjectID="27552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179277">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1462.000000 -431.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27556" ObjectName="SW-CX_YS.CX_YS_318XC"/>
     <cge:Meas_Ref ObjectId="179277"/>
    <cge:TPSR_Ref TObjectID="27556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179280">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1606.000000 -432.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27560" ObjectName="SW-CX_YS.CX_YS_319XC"/>
     <cge:Meas_Ref ObjectId="179280"/>
    <cge:TPSR_Ref TObjectID="27560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179271">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1174.000000 -359.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27549" ObjectName="SW-CX_YS.CX_YS_316XC1"/>
     <cge:Meas_Ref ObjectId="179271"/>
    <cge:TPSR_Ref TObjectID="27549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179274">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1318.000000 -359.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27553" ObjectName="SW-CX_YS.CX_YS_317XC1"/>
     <cge:Meas_Ref ObjectId="179274"/>
    <cge:TPSR_Ref TObjectID="27553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179277">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1462.000000 -356.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27557" ObjectName="SW-CX_YS.CX_YS_318XC1"/>
     <cge:Meas_Ref ObjectId="179277"/>
    <cge:TPSR_Ref TObjectID="27557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179280">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1606.000000 -357.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27561" ObjectName="SW-CX_YS.CX_YS_319XC1"/>
     <cge:Meas_Ref ObjectId="179280"/>
    <cge:TPSR_Ref TObjectID="27561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179283">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1750.000000 -432.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27564" ObjectName="SW-CX_YS.CX_YS_321XC"/>
     <cge:Meas_Ref ObjectId="179283"/>
    <cge:TPSR_Ref TObjectID="27564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179283">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1750.000000 -357.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27565" ObjectName="SW-CX_YS.CX_YS_321XC1"/>
     <cge:Meas_Ref ObjectId="179283"/>
    <cge:TPSR_Ref TObjectID="27565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179263">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 886.000000 -571.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27537" ObjectName="SW-CX_YS.CX_YS_301XC"/>
     <cge:Meas_Ref ObjectId="179263"/>
    <cge:TPSR_Ref TObjectID="27537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179263">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 886.000000 -499.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27538" ObjectName="SW-CX_YS.CX_YS_301XC1"/>
     <cge:Meas_Ref ObjectId="179263"/>
    <cge:TPSR_Ref TObjectID="27538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179302">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1134.000000 -539.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27587" ObjectName="SW-CX_YS.CX_YS_3901XC"/>
     <cge:Meas_Ref ObjectId="179302"/>
    <cge:TPSR_Ref TObjectID="27587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179302">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1134.000000 -504.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27588" ObjectName="SW-CX_YS.CX_YS_3901XC1"/>
     <cge:Meas_Ref ObjectId="179302"/>
    <cge:TPSR_Ref TObjectID="27588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293376">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2120.565184 -822.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45314" ObjectName="SW-CX_YS.CX_YS_2021SW"/>
     <cge:Meas_Ref ObjectId="293376"/>
    <cge:TPSR_Ref TObjectID="45314"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293377">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2120.565184 -718.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45315" ObjectName="SW-CX_YS.CX_YS_2026SW"/>
     <cge:Meas_Ref ObjectId="293377"/>
    <cge:TPSR_Ref TObjectID="45315"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293378">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2137.565184 -813.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45316" ObjectName="SW-CX_YS.CX_YS_20217SW"/>
     <cge:Meas_Ref ObjectId="293378"/>
    <cge:TPSR_Ref TObjectID="45316"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293379">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2137.565184 -765.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45317" ObjectName="SW-CX_YS.CX_YS_20260SW"/>
     <cge:Meas_Ref ObjectId="293379"/>
    <cge:TPSR_Ref TObjectID="45317"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293380">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2136.565184 -706.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45318" ObjectName="SW-CX_YS.CX_YS_20267SW"/>
     <cge:Meas_Ref ObjectId="293380"/>
    <cge:TPSR_Ref TObjectID="45318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-296324">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2120.000000 -568.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45975" ObjectName="SW-CX_YS.CX_YS_302XC"/>
     <cge:Meas_Ref ObjectId="296324"/>
    <cge:TPSR_Ref TObjectID="45975"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-296324">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2120.000000 -493.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45976" ObjectName="SW-CX_YS.CX_YS_302XC1"/>
     <cge:Meas_Ref ObjectId="296324"/>
    <cge:TPSR_Ref TObjectID="45976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293447">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2391.000000 -532.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45354" ObjectName="SW-CX_YS.CX_YS_3902XC1"/>
     <cge:Meas_Ref ObjectId="293447"/>
    <cge:TPSR_Ref TObjectID="45354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293447">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2391.000000 -497.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45353" ObjectName="SW-CX_YS.CX_YS_3902XC"/>
     <cge:Meas_Ref ObjectId="293447"/>
    <cge:TPSR_Ref TObjectID="45353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293407">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2012.000000 -327.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45323" ObjectName="SW-CX_YS.CX_YS_35167SW"/>
     <cge:Meas_Ref ObjectId="293407"/>
    <cge:TPSR_Ref TObjectID="45323"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293411">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2186.000000 -326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45327" ObjectName="SW-CX_YS.CX_YS_35267SW"/>
     <cge:Meas_Ref ObjectId="293411"/>
    <cge:TPSR_Ref TObjectID="45327"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293415">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2757.000000 -323.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45331" ObjectName="SW-CX_YS.CX_YS_35367SW"/>
     <cge:Meas_Ref ObjectId="293415"/>
    <cge:TPSR_Ref TObjectID="45331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293419">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2901.000000 -323.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45335" ObjectName="SW-CX_YS.CX_YS_35467SW"/>
     <cge:Meas_Ref ObjectId="293419"/>
    <cge:TPSR_Ref TObjectID="45335"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293435">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3045.000000 -320.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45351" ObjectName="SW-CX_YS.CX_YS_35867SW"/>
     <cge:Meas_Ref ObjectId="293435"/>
    <cge:TPSR_Ref TObjectID="45351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3454.000000 -624.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3352.000000 -328.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293406">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2053.000000 -429.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45321" ObjectName="SW-CX_YS.CX_YS_351XC"/>
     <cge:Meas_Ref ObjectId="293406"/>
    <cge:TPSR_Ref TObjectID="45321"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293410">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2227.000000 -428.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45325" ObjectName="SW-CX_YS.CX_YS_352XC"/>
     <cge:Meas_Ref ObjectId="293410"/>
    <cge:TPSR_Ref TObjectID="45325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293406">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2053.000000 -354.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45322" ObjectName="SW-CX_YS.CX_YS_351XC1"/>
     <cge:Meas_Ref ObjectId="293406"/>
    <cge:TPSR_Ref TObjectID="45322"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293410">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2227.000000 -353.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45326" ObjectName="SW-CX_YS.CX_YS_352XC1"/>
     <cge:Meas_Ref ObjectId="293410"/>
    <cge:TPSR_Ref TObjectID="45326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293414">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2798.000000 -425.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45329" ObjectName="SW-CX_YS.CX_YS_353XC"/>
     <cge:Meas_Ref ObjectId="293414"/>
    <cge:TPSR_Ref TObjectID="45329"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293418">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2942.000000 -425.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45333" ObjectName="SW-CX_YS.CX_YS_354XC"/>
     <cge:Meas_Ref ObjectId="293418"/>
    <cge:TPSR_Ref TObjectID="45333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293434">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3086.000000 -422.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45349" ObjectName="SW-CX_YS.CX_YS_358XC"/>
     <cge:Meas_Ref ObjectId="293434"/>
    <cge:TPSR_Ref TObjectID="45349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3413.000000 -522.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293414">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2798.000000 -350.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45330" ObjectName="SW-CX_YS.CX_YS_353XC1"/>
     <cge:Meas_Ref ObjectId="293414"/>
    <cge:TPSR_Ref TObjectID="45330"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293418">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2942.000000 -350.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45334" ObjectName="SW-CX_YS.CX_YS_354XC1"/>
     <cge:Meas_Ref ObjectId="293418"/>
    <cge:TPSR_Ref TObjectID="45334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293434">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3086.000000 -347.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45350" ObjectName="SW-CX_YS.CX_YS_358XC1"/>
     <cge:Meas_Ref ObjectId="293434"/>
    <cge:TPSR_Ref TObjectID="45350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3413.000000 -597.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3393.000000 -430.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3393.000000 -355.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293427">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2350.000000 -335.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45343" ObjectName="SW-CX_YS.CX_YS_35567SW"/>
     <cge:Meas_Ref ObjectId="293427"/>
    <cge:TPSR_Ref TObjectID="45343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2392.000000 -157.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2340.000000 -144.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293426">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2391.000000 -432.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45341" ObjectName="SW-CX_YS.CX_YS_355XC"/>
     <cge:Meas_Ref ObjectId="293426"/>
    <cge:TPSR_Ref TObjectID="45341"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293426">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2391.000000 -357.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45342" ObjectName="SW-CX_YS.CX_YS_355XC1"/>
     <cge:Meas_Ref ObjectId="293426"/>
    <cge:TPSR_Ref TObjectID="45342"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293423">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2563.000000 -346.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45339" ObjectName="SW-CX_YS.CX_YS_35667SW"/>
     <cge:Meas_Ref ObjectId="293423"/>
    <cge:TPSR_Ref TObjectID="45339"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2605.000000 -196.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2553.000000 -183.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2605.000000 -19.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293422">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2604.000000 -443.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45337" ObjectName="SW-CX_YS.CX_YS_356XC"/>
     <cge:Meas_Ref ObjectId="293422"/>
    <cge:TPSR_Ref TObjectID="45337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293422">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2604.000000 -368.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45338" ObjectName="SW-CX_YS.CX_YS_356XC1"/>
     <cge:Meas_Ref ObjectId="293422"/>
    <cge:TPSR_Ref TObjectID="45338"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293431">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3201.000000 -318.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45347" ObjectName="SW-CX_YS.CX_YS_35767SW"/>
     <cge:Meas_Ref ObjectId="293431"/>
    <cge:TPSR_Ref TObjectID="45347"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293430">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3242.000000 -420.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45345" ObjectName="SW-CX_YS.CX_YS_357XC"/>
     <cge:Meas_Ref ObjectId="293430"/>
    <cge:TPSR_Ref TObjectID="45345"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-293430">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3242.000000 -345.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45346" ObjectName="SW-CX_YS.CX_YS_357XC1"/>
     <cge:Meas_Ref ObjectId="293430"/>
    <cge:TPSR_Ref TObjectID="45346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3303.000000 -624.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3262.000000 -522.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3262.000000 -597.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3147.000000 -626.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3106.000000 -524.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3106.000000 -599.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3003.000000 -624.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2962.000000 -522.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2962.000000 -597.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3214.565184 -128.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-296325">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2218.000000 -632.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45977" ObjectName="SW-CX_YS.CX_YS_2020SW"/>
     <cge:Meas_Ref ObjectId="296325"/>
    <cge:TPSR_Ref TObjectID="45977"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_HP" endPointId="0" endStationName="CX_YS" flowDrawDirect="1" flowShape="0" id="AC-220kV.heyi_line" runFlow="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="896,-1196 896,-1257 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9207" ObjectName="AC-220kV.heyi_line"/>
    <cge:TPSR_Ref TObjectID="9207_SS-245"/></metadata>
   <polyline fill="none" opacity="0" points="896,-1196 896,-1257 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_34eba40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 384.477768 -79.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34ec630">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1135.000000 -597.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34ef8f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1207.000000 -588.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34f7d00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 940.000000 -531.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34fbb80">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 915.000000 -1173.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34fc770">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1137.000000 -1057.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_350bbb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 424.000000 -262.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_350dec0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 391.000000 -246.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3515c90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 411.000000 -140.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_351a0e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 599.000000 -262.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_351b2d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 566.000000 -246.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3524680">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 586.000000 -168.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35f7470">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 775.000000 -261.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35f8660">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 742.000000 -245.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3600130">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 919.000000 -260.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3606240">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1063.000000 -259.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_360c0f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1207.000000 -260.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3610e80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1351.000000 -260.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3617e50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1495.000000 -257.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_361cbe0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1639.000000 -258.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3621970">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1783.000000 -258.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36b9fc0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2392.000000 -590.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36bd650">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2464.000000 -581.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36beb60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2175.000000 -523.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36d5170">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2086.000000 -255.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36d9ca0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2260.000000 -254.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36de520">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2831.000000 -251.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36e3050">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2975.000000 -251.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36e7b80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3119.000000 -248.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36ec690">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3380.000000 -696.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36f11a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3426.000000 -256.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_373cad0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2384.477768 -80.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3741040">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2424.000000 -263.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3742230">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2391.000000 -247.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37491f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2411.000000 -141.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3758130">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2637.000000 -274.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3759320">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2604.000000 -258.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_375fab0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2624.000000 -180.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3776810">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3275.000000 -246.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3777a00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3242.000000 -230.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37873d0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3229.000000 -696.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3795820">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3073.000000 -698.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37a4d90">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2929.000000 -696.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37b3e60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3239.000000 -119.000000)" xlink:href="#lightningRod:shape189"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="27522" cx="896" cy="-480" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27522" cx="1040" cy="-480" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27522" cx="1184" cy="-480" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27522" cx="1328" cy="-480" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27522" cx="1472" cy="-480" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27522" cx="1616" cy="-480" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27522" cx="1760" cy="-480" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27522" cx="752" cy="-480" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27521" cx="896" cy="-880" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27521" cx="896" cy="-880" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27521" cx="1186" cy="-880" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="45312" cx="2063" cy="-479" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27522" cx="401" cy="-480" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="45312" cx="2237" cy="-479" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="45312" cx="2401" cy="-479" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="45312" cx="2401" cy="-479" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27521" cx="2130" cy="-880" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="45312" cx="2808" cy="-479" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="45312" cx="2952" cy="-479" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="45312" cx="3096" cy="-479" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="45312" cx="3252" cy="-479" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="45312" cx="2952" cy="-479" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="45312" cx="3096" cy="-479" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="45312" cx="3252" cy="-479" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="45312" cx="2614" cy="-479" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3288" cy="-1047" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3207" cy="-1047" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="45312" cx="3403" cy="-479" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="45312" cx="3403" cy="-479" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27522" cx="576" cy="-480" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27522" cx="896" cy="-480" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="45312" cx="2130" cy="-479" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_34b8c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 544.000000 -874.000000) translate(0,16)">220kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,17)">危险点说明</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,374)">联系方式</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,17)">频率</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,59)">全站有功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,101)">风机出力</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,143)">全站无功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ba910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,185)">并网联络点的电压和交换功率</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_34bbd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 126.000000 -1198.500000) translate(0,16)">彝山风电场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34d5b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1115.000000 -1162.500000) translate(0,15)">220kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34d8020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 842.000000 -1284.500000) translate(0,15)">220kV和彝线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_34ead80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 312.000000 -469.000000) translate(0,16)">35kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34f0900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 290.000000 -75.000000) translate(0,15)">1号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34f0f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 368.000000 -61.000000) translate(0,15)">±32MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34f1170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3251.000000 -1264.000000) translate(0,15)">10kV猫街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34f1a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3317.500000 -1191.000000) translate(0,15)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f2d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 686.000000 -682.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f2d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 686.000000 -682.000000) translate(0,27)">SZF11-180000/220</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f2d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 686.000000 -682.000000) translate(0,42)">230±8×1.25%/36.75kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f2d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 686.000000 -682.000000) translate(0,57)">180000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f2d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 686.000000 -682.000000) translate(0,72)">YN,yn,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f2d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 686.000000 -682.000000) translate(0,87)">Ud%=14</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34f40c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1089.000000 -701.500000) translate(0,15)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34fa5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3329.000000 -1173.000000) translate(0,15)">（施工变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34fb6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3339.500000 -1154.000000) translate(0,15)">500kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_352c4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 -99.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_352cb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 620.000000 -85.000000) translate(0,15)">＋6MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35fc5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 724.000000 -81.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3628bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1142.000000 -195.500000) translate(0,15)">彝山Ⅲ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36290e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 976.000000 -172.500000) translate(0,15)">（8、11-14、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36290e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 976.000000 -172.500000) translate(0,33)">16-19号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_362a020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -195.500000) translate(0,15)">彝山Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_362a2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 844.000000 -172.500000) translate(0,15)">（1-7、9、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_362a2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 844.000000 -172.500000) translate(0,33)">10号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_362a4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 999.000000 -195.500000) translate(0,15)">彝山Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_362a700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1131.500000 -172.500000) translate(0,15)">（15、20-27、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_362a700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1131.500000 -172.500000) translate(0,33)">59号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_362a990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1284.000000 -195.500000) translate(0,15)">彝山Ⅳ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_362aec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1425.000000 -195.500000) translate(0,15)">彝山Ⅴ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_362b490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1570.000000 -195.500000) translate(0,15)">彝山Ⅵ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_362b9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1715.000000 -195.500000) translate(0,15)">彝山Ⅶ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_362bfa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1251.500000 -172.500000) translate(0,15)">（28-36号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_362c1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1417.000000 -172.500000) translate(0,15)">（39-44、60、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_362c1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1417.000000 -172.500000) translate(0,33)">61号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_362c470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1563.000000 -172.500000) translate(0,15)">（47、53-58</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_362c470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1563.000000 -172.500000) translate(0,33)">号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_362c6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1695.500000 -174.500000) translate(0,15)">（37、38、45、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_362c6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1695.500000 -174.500000) translate(0,33)">46、48-52号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_362c8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 831.500000 -1104.000000) translate(0,12)">26167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_362cb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 831.500000 -1041.000000) translate(0,12)">26160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_362cd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 832.000000 -973.000000) translate(0,12)">26117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_362cf80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.500000 -924.000000) translate(0,12)">2611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_362d1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 907.500000 -991.000000) translate(0,12)">261</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_362d400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.000000 -1055.000000) translate(0,12)">2616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_362d640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1122.000000 -944.000000) translate(0,12)">29010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_362d880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1136.000000 -1034.000000) translate(0,12)">29017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_362dac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1193.000000 -971.000000) translate(0,12)">2901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_362dd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 840.500000 -803.000000) translate(0,12)">201</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_362df40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 836.500000 -855.000000) translate(0,12)">2011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_362e180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 915.500000 -849.000000) translate(0,12)">20117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_362e3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 915.000000 -803.000000) translate(0,12)">20160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_362e600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 915.000000 -744.000000) translate(0,12)">20167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_362e840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 836.000000 -751.000000) translate(0,12)">2016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_362ea80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 340.000000 -360.000000) translate(0,12)">31160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3636d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 412.500000 -417.000000) translate(0,12)">311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3637390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 408.000000 -186.000000) translate(0,12)">3116</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36375d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 340.000000 -138.000000) translate(0,12)">31167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3682300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 585.000000 -417.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3682930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 510.000000 -360.000000) translate(0,12)">31237</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3682b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 510.000000 -166.000000) translate(0,12)">31260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3682db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 576.000000 -33.000000) translate(0,12)">31267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3682ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 580.500000 -216.000000) translate(0,12)">3126</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3683230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 -417.000000) translate(0,12)">313</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3683470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 699.000000 -360.000000) translate(0,12)">31360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36836b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 905.000000 -417.000000) translate(0,12)">314</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36838f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 849.000000 -360.000000) translate(0,12)">31467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3683b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1049.000000 -417.000000) translate(0,12)">315</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3683d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 985.000000 -360.000000) translate(0,12)">31567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3683fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1193.000000 -417.000000) translate(0,12)">316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36841f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1124.000000 -360.000000) translate(0,12)">31667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3684430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1337.000000 -417.000000) translate(0,12)">317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3684670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1271.000000 -360.000000) translate(0,12)">31767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36848b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1481.000000 -417.000000) translate(0,12)">318</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3684af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1422.000000 -360.000000) translate(0,12)">31867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3684d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1625.000000 -417.000000) translate(0,12)">319</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3684f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1562.000000 -360.000000) translate(0,12)">31967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36851b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1769.000000 -417.000000) translate(0,12)">321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36853f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1693.000000 -360.000000) translate(0,12)">32167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3697b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 906.000000 -558.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36986d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 132.000000 -278.000000) translate(0,12)">   6017600</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36986d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 132.000000 -278.000000) translate(0,27)">17808784226</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36986d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 132.000000 -278.000000) translate(0,42)">   93205000</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36986d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 132.000000 -278.000000) translate(0,57)">   93205001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36986d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 132.000000 -278.000000) translate(0,72)">18288956402</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_369ed90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1157.000000 -545.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36be660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2340.000000 -699.500000) translate(0,15)">35kVⅡ段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_373daa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2290.000000 -76.000000) translate(0,15)">2号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_373dd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2368.000000 -62.000000) translate(0,15)">±32MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37b8120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3060.500000 -1184.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37b8760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3214.500000 -1072.000000) translate(0,15)">400V母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b9370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2150.500000 -796.000000) translate(0,12)">20260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b9770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2150.500000 -742.000000) translate(0,12)">20267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b99b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2342.500000 -324.000000) translate(0,12)">35560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b9bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2331.500000 -181.000000) translate(0,12)">35567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b9e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2415.500000 -191.000000) translate(0,12)">3556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37ba070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2819.500000 -409.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37ba2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2557.500000 -338.000000) translate(0,12)">35660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37ba4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2619.500000 -223.000000) translate(0,12)">3566</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37baf70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2554.500000 -213.000000) translate(0,12)">35667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37bb400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2627.500000 -426.000000) translate(0,12)">356</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37bb640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2966.500000 -409.000000) translate(0,12)">354</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37bb880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2900.500000 -356.000000) translate(0,12)">35467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37bbac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3193.500000 -304.000000) translate(0,12)">35767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37bbd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3382.500000 -202.000000) translate(0,12)">集电线路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37bbd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3382.500000 -202.000000) translate(0,27)">（预留）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37bd130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2927.500000 -772.000000) translate(0,12)">集电线路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37bd130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2927.500000 -772.000000) translate(0,27)">（预留）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37bd4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3067.500000 -775.000000) translate(0,12)">集电线路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37bd4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3067.500000 -775.000000) translate(0,27)">（预留）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37bd710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3220.500000 -774.000000) translate(0,12)">集电线路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37bd710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3220.500000 -774.000000) translate(0,27)">（预留）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37bd950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3374.500000 -772.000000) translate(0,12)">集电线路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37bd950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3374.500000 -772.000000) translate(0,27)">（预留）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c1be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.500000 -156.000000) translate(0,12)">3020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c2090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1916.000000 -607.000000) translate(0,12)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c2090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1916.000000 -607.000000) translate(0,27)">SZF18-180000/220</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c2090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1916.000000 -607.000000) translate(0,42)">230±8×1.25%/37kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c2090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1916.000000 -607.000000) translate(0,57)">YN,d11 Ud=14%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37c5190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2581.000000 13.000000) translate(0,15)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37c5680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3156.000000 -54.000000) translate(0,15)">35kV2号接地兼站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37c6220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2019.000000 -194.500000) translate(0,15)">秧田箐Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37c6b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2010.000000 -171.500000) translate(0,15)">（1、11、12、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37c6b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2010.000000 -171.500000) translate(0,33)">1323、28号方</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37c6b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2010.000000 -171.500000) translate(0,51)">阵19.3MWp）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c7dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2096.000000 -799.000000) translate(0,12)">202</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c8080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2090.000000 -850.000000) translate(0,12)">2021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c8290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2180.000000 -841.000000) translate(0,12)">20217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c84a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2086.000000 -748.000000) translate(0,12)">2026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c86e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2141.000000 -554.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c8920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2418.000000 -531.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c8b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1993.000000 -499.000000) translate(0,12)">35kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c8da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2072.000000 -413.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c8fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2015.000000 -358.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c9220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2246.000000 -410.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c9460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2189.000000 -357.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c96a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2410.000000 -419.000000) translate(0,12)">355</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c98e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2760.000000 -354.000000) translate(0,12)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c9b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3105.000000 -407.000000) translate(0,12)">358</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c9d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3048.000000 -351.000000) translate(0,12)">35867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c9fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3261.000000 -405.000000) translate(0,12)">357</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37ca1e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3204.000000 -349.000000) translate(0,12)">35767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37ca420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2189.000000 -198.500000) translate(0,15)">新村Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37ca420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2189.000000 -198.500000) translate(0,33)">（2~10、14、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37ca420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2189.000000 -198.500000) translate(0,51)">17、22方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37ca420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2189.000000 -198.500000) translate(0,69)">22.1MWp）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37cad00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2764.000000 -193.500000) translate(0,15)">新村Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37cad00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2764.000000 -193.500000) translate(0,33)">（15、16、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37cad00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2764.000000 -193.500000) translate(0,51)">18~21、24~27、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37cad00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2764.000000 -193.500000) translate(0,69)">34~37方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37cad00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2764.000000 -193.500000) translate(0,87)">25.1MWp）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37caf80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2910.000000 -194.500000) translate(0,15)">新村Ⅲ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37caf80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2910.000000 -194.500000) translate(0,33)">（29~33、38~42、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37caf80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2910.000000 -194.500000) translate(0,51)">34~37方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_37caf80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2910.000000 -194.500000) translate(0,69)">23.5MWp）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37cb1e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3069.500000 -187.000000) translate(0,12)">储能</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37cb1e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3069.500000 -187.000000) translate(0,27)">（备用）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37d43a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2189.000000 -650.000000) translate(0,12)">2020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37de9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 977.000000 -704.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37deee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2028.000000 -662.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_38a5fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 297.000000 -1069.000000) translate(0,16)">AGC/AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_38335b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 269.000000 -1018.500000) translate(0,16)">110kV秧田箐光伏</text>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="925" cy="-658" fill="none" fillStyle="0" r="23" stroke="rgb(255,255,0)" stroke-width="1"/>
   <ellipse DF8003:Layer="PUBLIC" cx="2131" cy="-671" fill="none" fillStyle="0" rx="24" ry="24.5" stroke="rgb(255,255,255)" stroke-width="0.510204"/>
   <circle DF8003:Layer="PUBLIC" cx="2131" cy="-640" fill="none" fillStyle="0" r="24" stroke="rgb(255,255,0)" stroke-width="0.510204"/>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_YS.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 891.000000 -221.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43384" ObjectName="SM-CX_YS.P1"/>
    <cge:TPSR_Ref TObjectID="43384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YS.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1035.000000 -220.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43385" ObjectName="SM-CX_YS.P2"/>
    <cge:TPSR_Ref TObjectID="43385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YS.P3">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1179.000000 -221.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43386" ObjectName="SM-CX_YS.P3"/>
    <cge:TPSR_Ref TObjectID="43386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YS.P4">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1323.000000 -221.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43387" ObjectName="SM-CX_YS.P4"/>
    <cge:TPSR_Ref TObjectID="43387"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YS.P5">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1467.000000 -218.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43388" ObjectName="SM-CX_YS.P5"/>
    <cge:TPSR_Ref TObjectID="43388"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YS.P6">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1611.000000 -219.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43389" ObjectName="SM-CX_YS.P6"/>
    <cge:TPSR_Ref TObjectID="43389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YS.P7">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1755.000000 -219.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43390" ObjectName="SM-CX_YS.P7"/>
    <cge:TPSR_Ref TObjectID="43390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YS.P9">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2232.000000 -215.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46549" ObjectName="SM-CX_YS.P9"/>
    <cge:TPSR_Ref TObjectID="46549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YS.P10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2803.000000 -212.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46550" ObjectName="SM-CX_YS.P10"/>
    <cge:TPSR_Ref TObjectID="46550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YS.P11">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2947.000000 -212.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46551" ObjectName="SM-CX_YS.P11"/>
    <cge:TPSR_Ref TObjectID="46551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3091.000000 -209.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3408.000000 -735.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3398.000000 -217.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3257.000000 -735.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3101.000000 -737.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2957.000000 -735.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3218.000000 -68.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YS.P8">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2058.000000 -216.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46548" ObjectName="SM-CX_YS.P8"/>
    <cge:TPSR_Ref TObjectID="46548"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 78.000000 -1150.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="221" y="-1230"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="15" stroke="rgb(255,255,0)" stroke-width="1" width="7" x="845" y="-632"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="24" stroke="rgb(60,120,255)" stroke-width="0.379884" width="14" x="3217" y="-127"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="25" stroke="rgb(255,255,255)" stroke-width="1" width="12" x="2241" y="-666"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-177594" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 262.000000 -1118.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27501" ObjectName="DYN-CX_YS"/>
     <cge:Meas_Ref ObjectId="177594"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3272.000000 -1106.000000)" xlink:href="#transformer2:shape46_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3272.000000 -1106.000000)" xlink:href="#transformer2:shape46_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 721.000000 -86.000000)" xlink:href="#transformer2:shape78_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 721.000000 -86.000000)" xlink:href="#transformer2:shape78_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3191.000000 -1106.000000)" xlink:href="#transformer2:shape46_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3191.000000 -1106.000000)" xlink:href="#transformer2:shape46_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_YS.CX_YS_1T1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="24395"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 858.000000 -612.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 858.000000 -612.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="46579" ObjectName="TF-CX_YS.CX_YS_1T1"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_YS"/>
</svg>