<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-60" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1224 2197 1228">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="37" x2="37" y1="122" y2="130"/>
    <polyline arcFlag="1" points="37,122 35,122 33,121 32,121 30,120 29,119 27,118 26,116 25,114 25,113 24,111 24,109 24,107 25,105 25,104 26,102 27,101 29,99 30,98 32,97 33,97 35,96 37,96 39,96 41,97 42,97 44,98 45,99 47,101 48,102 49,104 49,105 50,107 50,109 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="50" x2="38" y1="109" y2="109"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.915724" x1="37" x2="37" y1="109" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="23" y2="14"/>
    <polyline arcFlag="1" points="13,23 12,23 12,23 11,23 10,23 10,24 9,24 8,25 8,25 8,26 7,27 7,27 7,28 7,29 7,30 7,30 8,31 8,32 8,32 9,33 10,33 10,34 11,34 12,34 12,34 13,34 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,34 12,34 12,34 11,34 10,34 10,35 9,35 8,36 8,36 8,37 7,38 7,38 7,39 7,40 7,41 7,41 8,42 8,43 8,43 9,44 10,44 10,45 11,45 12,45 12,45 13,45 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,45 12,45 12,45 11,45 10,45 10,46 9,46 8,47 8,47 8,48 7,49 7,49 7,50 7,51 7,52 7,52 8,53 8,54 8,54 9,55 10,55 10,56 11,56 12,56 12,56 13,56 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="65" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="65" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.393258" x1="21" x2="56" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="13" y2="13"/>
    <rect height="26" stroke-width="0.398039" width="12" x="31" y="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="20" x2="20" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="2" x2="2" y1="56" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="57" x2="57" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="37" x2="37" y1="16" y2="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape125">
    <ellipse cx="14" cy="17" fillStyle="0" rx="9" ry="7" stroke-width="0.153636"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.153636"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="25" x2="20" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="20" x2="20" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="8" x2="8" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="14" x2="11" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="16" x2="14" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="14" x2="14" y1="16" y2="18"/>
    <ellipse cx="19" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.153636"/>
   </symbol>
   <symbol id="lightningRod:shape59">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="72" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="23" y2="23"/>
    <circle cx="9" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="63" y2="63"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,61 9,39 9,30 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="48"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape39">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="49" x2="49" y1="6" y2="9"/>
    <rect height="8" stroke-width="0.75" width="18" x="11" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="24" x2="22" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="22" x2="24" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="24" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="29" x2="43" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="43" x2="43" y1="0" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="46" x2="46" y1="4" y2="10"/>
   </symbol>
   <symbol id="lightningRod:shape12">
    <polyline points="9,14 3,17 1,18 1,19 1,19 3,21 6,22 10,24 11,25 11,25 11,26 10,27 6,28 3,30 2,30 2,31 2,32 3,33 6,34 10,36 11,36 11,37 11,38 10,38 6,40 3,41 2,42 2,43 2,44 3,44 6,46 10,47 11,48 11,49 11,49 10,50 6,52 3,53 1,55 1,55 1,56 3,57 9,60 " stroke-width="2.00006"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="15" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="59" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="6" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="0" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="4" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape27_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape27_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape27-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape27-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="transformer2:shape54_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <rect height="28" stroke-width="1" width="14" x="90" y="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="95" x2="98" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="93" x2="101" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="97" x2="97" y1="28" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="103" x2="91" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="97" x2="97" y1="75" y2="40"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="39" y1="75" y2="75"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape54_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape21_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1.13333"/>
    <polyline points="58,100 64,100 " stroke-width="1.13333"/>
    <polyline points="64,100 64,93 " stroke-width="1.13333"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape21_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="transformer2:shape95_0">
    <ellipse cx="14" cy="43" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="13" y1="38" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="13" y1="7" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="9" y1="0" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="31" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="42" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="37" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="39" x2="35" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="46" x2="28" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="40" x2="33" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="46" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="42" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="38" y2="42"/>
   </symbol>
   <symbol id="transformer2:shape95_1">
    <circle cx="13" cy="62" fillStyle="0" r="13" stroke-width="0.265306"/>
    <polyline DF8003:Layer="PUBLIC" points="12,70 8,61 18,61 12,70 "/>
   </symbol>
   <symbol id="voltageTransformer:shape57">
    <circle cx="18" cy="16" fillStyle="0" r="16.5" stroke-width="0.340267"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.374294" x1="45" x2="39" y1="30" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="45" x2="39" y1="23" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="39" x2="39" y1="33" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="16" x2="11" y1="14" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="21" x2="16" y1="19" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="16" x2="16" y1="9" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="17" x2="12" y1="40" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="22" x2="17" y1="45" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="17" x2="17" y1="34" y2="40"/>
    <ellipse cx="38" cy="28" fillStyle="0" rx="16.5" ry="16" stroke-width="0.340267"/>
    <circle cx="17" cy="37" fillStyle="0" r="16.5" stroke-width="0.340267"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="14"/>
   </symbol>
   <symbol id="voltageTransformer:shape5">
    <circle cx="7" cy="9" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="6" cy="18" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="13" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2beb470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d31360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d31be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d32890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d33ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d34760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d34f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2d35960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2d373b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2d373b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d38b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d38b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d3a4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d3a4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2d3b170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d3cb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d3d750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d3e4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d3edd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d40790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d41090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d417a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d41f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d43040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d439c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d444b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d44e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d46350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d46e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d47ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d48b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d56e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d578f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2d4af10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2d4bef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1238" width="2207" x="3112" y="-1229"/>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="5223,-198 5222,-198 5222,-198 5221,-198 5221,-198 5220,-198 5220,-199 5220,-199 5220,-199 5219,-200 5219,-200 5219,-201 5219,-201 5219,-202 5219,-202 5219,-203 5219,-203 5220,-204 5220,-204 5220,-205 5220,-205 5221,-205 5221,-205 5222,-206 5222,-206 5223,-206 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="5223,-190 5222,-190 5222,-190 5221,-190 5221,-190 5220,-191 5220,-191 5220,-191 5220,-192 5219,-192 5219,-193 5219,-193 5219,-194 5219,-194 5219,-195 5219,-195 5219,-196 5220,-196 5220,-196 5220,-197 5220,-197 5221,-197 5221,-198 5222,-198 5222,-198 5223,-198 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="5223,-174 5222,-174 5222,-175 5221,-175 5221,-175 5220,-175 5220,-176 5220,-176 5220,-176 5219,-177 5219,-177 5219,-178 5219,-178 5219,-179 5219,-179 5219,-180 5219,-180 5220,-181 5220,-181 5220,-181 5220,-182 5221,-182 5221,-182 5222,-182 5222,-183 5223,-183 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="5223,-167 5222,-167 5222,-167 5221,-167 5221,-167 5220,-168 5220,-168 5220,-168 5220,-169 5219,-169 5219,-170 5219,-170 5219,-171 5219,-171 5219,-172 5219,-172 5219,-173 5220,-173 5220,-173 5220,-174 5220,-174 5221,-174 5221,-175 5222,-175 5222,-175 5223,-175 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="5284,-170 5283,-171 5283,-171 5282,-171 5282,-171 5282,-171 5281,-172 5281,-172 5281,-172 5280,-173 5280,-173 5280,-174 5280,-174 5280,-175 5280,-175 5280,-176 5280,-176 5281,-177 5281,-177 5281,-177 5282,-178 5282,-178 5282,-178 5283,-178 5283,-179 5284,-179 " stroke="rgb(50,205,50)" stroke-width="0.0277671"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4628" x2="4628" y1="-1059" y2="-1098"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4624" x2="4633" y1="-1104" y2="-1104"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4627" x2="4630" y1="-1108" y2="-1108"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4626" x2="4631" y1="-1106" y2="-1106"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4628" x2="4628" y1="-1096" y2="-1104"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4623" x2="4634" y1="-1087" y2="-1093"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4617" x2="4623" y1="-1087" y2="-1087"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.162533" x1="4655" x2="4653" y1="-1067" y2="-1068"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.145829" x1="4655" x2="4653" y1="-1064" y2="-1063"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.145829" x1="4653" x2="4653" y1="-1068" y2="-1063"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.145829" x1="4645" x2="4643" y1="-1059" y2="-1061"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.145829" x1="4647" x2="4645" y1="-1061" y2="-1059"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.145829" x1="4645" x2="4645" y1="-1057" y2="-1059"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.145829" x1="4645" x2="4643" y1="-1069" y2="-1071"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.145829" x1="4647" x2="4645" y1="-1071" y2="-1069"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.145829" x1="4645" x2="4645" y1="-1067" y2="-1069"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4628" x2="4645" y1="-1059" y2="-1059"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4628" x2="4645" y1="-1069" y2="-1069"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="1" x1="5253" x2="5253" y1="-253" y2="-253"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="1" x1="5125" x2="5125" y1="-237" y2="-237"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.637931" x1="5214" x2="5214" y1="-206" y2="-190"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.637931" x1="5214" x2="5214" y1="-183" y2="-167"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5223" x2="5247" y1="-167" y2="-167"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5223" x2="5242" y1="-182" y2="-182"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5223" x2="5228" y1="-190" y2="-190"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5223" x2="5228" y1="-205" y2="-205"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5247" x2="5247" y1="-162" y2="-167"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5247" x2="5264" y1="-161" y2="-161"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5252" x2="5264" y1="-182" y2="-182"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5284" x2="5284" y1="-239" y2="-174"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.637931" x1="5279" x2="5284" y1="-170" y2="-158"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5284" x2="5284" y1="-158" y2="-151"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.394631" x1="5288" x2="5280" y1="-137" y2="-137"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.394631" x1="5286" x2="5282" y1="-135" y2="-135"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.339777" x1="5285" x2="5283" y1="-133" y2="-133"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.197315" x1="5284" x2="5284" y1="-140" y2="-137"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5264" x2="5264" y1="-174" y2="-182"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5268" x2="5264" y1="-171" y2="-171"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5268" x2="5264" y1="-171" y2="-174"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5264" x2="5264" y1="-170" y2="-161"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5284" x2="5206" y1="-239" y2="-239"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="5190" x2="5190" y1="-239" y2="-221"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="5173" x2="5173" y1="-239" y2="-221"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="5206" x2="5198" y1="-231" y2="-231"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="5191" x2="5215" y1="-188" y2="-213"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3119" y="-1196"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1076"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-596"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="21" stroke="rgb(255,255,0)" stroke-width="0.330414" width="12" x="4640" y="-1049"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(255,255,0)" stroke-width="1" width="7" x="4625" y="-1096"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="58" stroke="rgb(0,255,0)" stroke-width="1" width="37" x="5194" y="-218"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="31" stroke="rgb(0,255,0)" stroke-width="1" width="34" x="5237" y="-189"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="6" stroke="rgb(0,255,0)" stroke-width="1" width="10" x="5242" y="-186"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="11" stroke="rgb(0,255,0)" stroke-width="1" width="6" x="5281" y="-152"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-40335">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3584.333333 -426.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6528" ObjectName="SW-CX_DH.CX_DH_0751SW"/>
     <cge:Meas_Ref ObjectId="40335"/>
    <cge:TPSR_Ref TObjectID="6528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40336">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3586.333333 -303.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6529" ObjectName="SW-CX_DH.CX_DH_0756SW"/>
     <cge:Meas_Ref ObjectId="40336"/>
    <cge:TPSR_Ref TObjectID="6529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40337">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3638.000000 -274.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6530" ObjectName="SW-CX_DH.CX_DH_07567SW"/>
     <cge:Meas_Ref ObjectId="40337"/>
    <cge:TPSR_Ref TObjectID="6530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3586.000000 -96.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40161">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4121.666667 -424.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6511" ObjectName="SW-CX_DH.CX_DH_0711SW"/>
     <cge:Meas_Ref ObjectId="40161"/>
    <cge:TPSR_Ref TObjectID="6511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40162">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4122.666667 -302.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6512" ObjectName="SW-CX_DH.CX_DH_0716SW"/>
     <cge:Meas_Ref ObjectId="40162"/>
    <cge:TPSR_Ref TObjectID="6512"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58384">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4457.666667 -424.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10979" ObjectName="SW-CX_DH.CX_DH_0922SW"/>
     <cge:Meas_Ref ObjectId="58384"/>
    <cge:TPSR_Ref TObjectID="10979"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58385">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4457.666667 -303.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10980" ObjectName="SW-CX_DH.CX_DH_0926SW"/>
     <cge:Meas_Ref ObjectId="58385"/>
    <cge:TPSR_Ref TObjectID="10980"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40249">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4632.000000 -422.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6518" ObjectName="SW-CX_DH.CX_DH_0932SW"/>
     <cge:Meas_Ref ObjectId="40249"/>
    <cge:TPSR_Ref TObjectID="6518"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40250">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4632.000000 -301.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6519" ObjectName="SW-CX_DH.CX_DH_0936SW"/>
     <cge:Meas_Ref ObjectId="40250"/>
    <cge:TPSR_Ref TObjectID="6519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58388">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4801.333333 -423.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10983" ObjectName="SW-CX_DH.CX_DH_0942SW"/>
     <cge:Meas_Ref ObjectId="58388"/>
    <cge:TPSR_Ref TObjectID="10983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58389">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4801.333333 -302.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10984" ObjectName="SW-CX_DH.CX_DH_0946SW"/>
     <cge:Meas_Ref ObjectId="58389"/>
    <cge:TPSR_Ref TObjectID="10984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40191">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4293.333333 -423.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6514" ObjectName="SW-CX_DH.CX_DH_0912SW"/>
     <cge:Meas_Ref ObjectId="40191"/>
    <cge:TPSR_Ref TObjectID="6514"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40192">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4293.333333 -302.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6515" ObjectName="SW-CX_DH.CX_DH_0916SW"/>
     <cge:Meas_Ref ObjectId="40192"/>
    <cge:TPSR_Ref TObjectID="6515"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40131">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4027.333333 -424.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6508" ObjectName="SW-CX_DH.CX_DH_0721SW"/>
     <cge:Meas_Ref ObjectId="40131"/>
    <cge:TPSR_Ref TObjectID="6508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40132">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4028.333333 -302.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6509" ObjectName="SW-CX_DH.CX_DH_0726SW"/>
     <cge:Meas_Ref ObjectId="40132"/>
    <cge:TPSR_Ref TObjectID="6509"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40102">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3931.500000 -424.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6505" ObjectName="SW-CX_DH.CX_DH_0731SW"/>
     <cge:Meas_Ref ObjectId="40102"/>
    <cge:TPSR_Ref TObjectID="6505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40103">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3932.500000 -302.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6506" ObjectName="SW-CX_DH.CX_DH_0736SW"/>
     <cge:Meas_Ref ObjectId="40103"/>
    <cge:TPSR_Ref TObjectID="6506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39976">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3730.333333 -532.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6489" ObjectName="SW-CX_DH.CX_DH_0901SW"/>
     <cge:Meas_Ref ObjectId="39976"/>
    <cge:TPSR_Ref TObjectID="6489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-189768">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4816.333333 -529.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28833" ObjectName="SW-CX_DH.CX_DH_0902SW"/>
     <cge:Meas_Ref ObjectId="189768"/>
    <cge:TPSR_Ref TObjectID="28833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40068">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3967.400000 -783.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6497" ObjectName="SW-CX_DH.CX_DH_3011SW"/>
     <cge:Meas_Ref ObjectId="40068"/>
    <cge:TPSR_Ref TObjectID="6497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40071">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4631.021622 -917.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6500" ObjectName="SW-CX_DH.CX_DH_3901SW"/>
     <cge:Meas_Ref ObjectId="40071"/>
    <cge:TPSR_Ref TObjectID="6500"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40065">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3968.333333 -1014.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6494" ObjectName="SW-CX_DH.CX_DH_3716SW"/>
     <cge:Meas_Ref ObjectId="40065"/>
    <cge:TPSR_Ref TObjectID="6494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40066">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3968.333333 -891.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6495" ObjectName="SW-CX_DH.CX_DH_3711SW"/>
     <cge:Meas_Ref ObjectId="40066"/>
    <cge:TPSR_Ref TObjectID="6495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40072">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 -1068.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6501" ObjectName="SW-CX_DH.CX_DH_37167SW"/>
     <cge:Meas_Ref ObjectId="40072"/>
    <cge:TPSR_Ref TObjectID="6501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40073">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4013.000000 -996.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6502" ObjectName="SW-CX_DH.CX_DH_37160SW"/>
     <cge:Meas_Ref ObjectId="40073"/>
    <cge:TPSR_Ref TObjectID="6502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40074">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 -940.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6503" ObjectName="SW-CX_DH.CX_DH_37117SW"/>
     <cge:Meas_Ref ObjectId="40074"/>
    <cge:TPSR_Ref TObjectID="6503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40070">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4677.000000 -889.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6499" ObjectName="SW-CX_DH.CX_DH_39010SW"/>
     <cge:Meas_Ref ObjectId="40070"/>
    <cge:TPSR_Ref TObjectID="6499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40069">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4676.000000 -966.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6498" ObjectName="SW-CX_DH.CX_DH_39017SW"/>
     <cge:Meas_Ref ObjectId="40069"/>
    <cge:TPSR_Ref TObjectID="6498"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40067">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4016.000000 -755.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6496" ObjectName="SW-CX_DH.CX_DH_30117SW"/>
     <cge:Meas_Ref ObjectId="40067"/>
    <cge:TPSR_Ref TObjectID="6496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39975">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3966.333333 -516.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6492" ObjectName="SW-CX_DH.CX_DH_0011SW"/>
     <cge:Meas_Ref ObjectId="39975"/>
    <cge:TPSR_Ref TObjectID="6492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40280">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4165.000000 -522.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6522" ObjectName="SW-CX_DH.CX_DH_0121SW"/>
     <cge:Meas_Ref ObjectId="40280"/>
    <cge:TPSR_Ref TObjectID="6522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40279">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4244.000000 -524.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6521" ObjectName="SW-CX_DH.CX_DH_0122SW"/>
     <cge:Meas_Ref ObjectId="40279"/>
    <cge:TPSR_Ref TObjectID="6521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-189719">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4393.400000 -796.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28830" ObjectName="SW-CX_DH.CX_DH_3021SW"/>
     <cge:Meas_Ref ObjectId="189719"/>
    <cge:TPSR_Ref TObjectID="28830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-189720">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4442.000000 -768.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28831" ObjectName="SW-CX_DH.CX_DH_30217SW"/>
     <cge:Meas_Ref ObjectId="189720"/>
    <cge:TPSR_Ref TObjectID="28831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-189775">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4393.333333 -517.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28835" ObjectName="SW-CX_DH.CX_DH_0022SW"/>
     <cge:Meas_Ref ObjectId="189775"/>
    <cge:TPSR_Ref TObjectID="28835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-189617">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4271.333333 -1018.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28825" ObjectName="SW-CX_DH.CX_DH_3726SW"/>
     <cge:Meas_Ref ObjectId="189617"/>
    <cge:TPSR_Ref TObjectID="28825"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-189616">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4271.333333 -895.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28824" ObjectName="SW-CX_DH.CX_DH_3721SW"/>
     <cge:Meas_Ref ObjectId="189616"/>
    <cge:TPSR_Ref TObjectID="28824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-189620">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4315.000000 -1072.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28828" ObjectName="SW-CX_DH.CX_DH_37267SW"/>
     <cge:Meas_Ref ObjectId="189620"/>
    <cge:TPSR_Ref TObjectID="28828"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-189619">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4316.000000 -1000.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28827" ObjectName="SW-CX_DH.CX_DH_37260SW"/>
     <cge:Meas_Ref ObjectId="189619"/>
    <cge:TPSR_Ref TObjectID="28827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-189618">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4318.000000 -944.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28826" ObjectName="SW-CX_DH.CX_DH_37217SW"/>
     <cge:Meas_Ref ObjectId="189618"/>
    <cge:TPSR_Ref TObjectID="28826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40309">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4962.333333 -426.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6524" ObjectName="SW-CX_DH.CX_DH_0952SW"/>
     <cge:Meas_Ref ObjectId="40309"/>
    <cge:TPSR_Ref TObjectID="6524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40310">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4964.333333 -303.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6525" ObjectName="SW-CX_DH.CX_DH_0956SW"/>
     <cge:Meas_Ref ObjectId="40310"/>
    <cge:TPSR_Ref TObjectID="6525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40311">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5016.000000 -274.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6526" ObjectName="SW-CX_DH.CX_DH_09567SW"/>
     <cge:Meas_Ref ObjectId="40311"/>
    <cge:TPSR_Ref TObjectID="6526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4964.000000 -96.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3503.000000 -423.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260539">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3783.500000 -423.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42705" ObjectName="SW-CX_DH.CX_DH_0741SW"/>
     <cge:Meas_Ref ObjectId="260539"/>
    <cge:TPSR_Ref TObjectID="42705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260540">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3784.500000 -301.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42706" ObjectName="SW-CX_DH.CX_DH_0746SW"/>
     <cge:Meas_Ref ObjectId="260540"/>
    <cge:TPSR_Ref TObjectID="42706"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285209">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5224.333333 -419.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44742" ObjectName="SW-CX_DH.CX_DH_0962SW"/>
     <cge:Meas_Ref ObjectId="285209"/>
    <cge:TPSR_Ref TObjectID="44742"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285211">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5197.000000 -245.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44743" ObjectName="SW-CX_DH.CX_DH_0030SW"/>
     <cge:Meas_Ref ObjectId="285211"/>
    <cge:TPSR_Ref TObjectID="44743"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_DH.CX_DH_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3504,-521 4202,-521 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6486" ObjectName="BS-CX_DH.CX_DH_9IM"/>
    <cge:TPSR_Ref TObjectID="6486"/></metadata>
   <polyline fill="none" opacity="0" points="3504,-521 4202,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DH.CX_DH_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4236,-521 5284,-521 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6487" ObjectName="BS-CX_DH.CX_DH_9IIM"/>
    <cge:TPSR_Ref TObjectID="6487"/></metadata>
   <polyline fill="none" opacity="0" points="4236,-521 5284,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DH.CX_DH_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,-876 4729,-876 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6485" ObjectName="BS-CX_DH.CX_DH_3IM"/>
    <cge:TPSR_Ref TObjectID="6485"/></metadata>
   <polyline fill="none" opacity="0" points="3799,-876 4729,-876 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_DH.CX_DH_Cb2">
    <use class="BV-10KV" transform="matrix(0.864407 -0.000000 0.000000 -0.867647 4947.000000 -161.000000)" xlink:href="#capacitor:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10568" ObjectName="CB-CX_DH.CX_DH_Cb2"/>
    <cge:TPSR_Ref TObjectID="10568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_DH.CX_DH_Cb1">
    <use class="BV-10KV" transform="matrix(0.966102 -0.000000 0.000000 -0.897059 3565.000000 -158.000000)" xlink:href="#capacitor:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41437" ObjectName="CB-CX_DH.CX_DH_Cb1"/>
    <cge:TPSR_Ref TObjectID="41437"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_DH.CX_DH_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="41083"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4369.000000 -628.000000)" xlink:href="#transformer2:shape54_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4369.000000 -628.000000)" xlink:href="#transformer2:shape54_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="28834" ObjectName="TF-CX_DH.CX_DH_2T"/>
    <cge:TPSR_Ref TObjectID="28834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_DH.CX_DH_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="9264"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.939394 -0.000000 0.000000 -0.882353 3945.000000 -628.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.939394 -0.000000 0.000000 -0.882353 3945.000000 -628.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="6531" ObjectName="TF-CX_DH.CX_DH_1T"/>
    <cge:TPSR_Ref TObjectID="6531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4166.000000 -122.000000)" xlink:href="#transformer2:shape95_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4166.000000 -122.000000)" xlink:href="#transformer2:shape95_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4243.000000 -700.000000)" xlink:href="#transformer2:shape95_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4243.000000 -700.000000)" xlink:href="#transformer2:shape95_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_21e52c0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4185.000000 -268.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2147ca0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4430.000000 -184.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_208f480">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4604.333333 -182.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2153b80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4771.666667 -183.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_218d700">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4264.666667 -183.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_206cfa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3998.666667 -183.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20dcf80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3903.166667 -182.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_218f430">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3739.000000 -680.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_218fe80">
    <use class="BV-10KV" transform="matrix(0.857143 -0.000000 0.000000 1.000000 3732.000000 -714.000000)" xlink:href="#lightningRod:shape125"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2191040">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3688.666667 -686.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e7e850">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4824.000000 -677.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2219cb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4773.666667 -683.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2171860">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4262.000000 -846.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_206f6e0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4083.000000 -1141.666667)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2070c70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3910.000000 -1050.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20c2df0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4386.000000 -1145.666667)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20c4020">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4213.000000 -1054.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2009fb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3530.666667 -224.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_200b7d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4915.666667 -246.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2235720">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4665.000000 -1008.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22394a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4183.000000 -266.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20628d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3755.166667 -181.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20813c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5234.000000 -315.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29be8c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5210.000000 -225.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aa89f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5197.000000 -146.000000)" xlink:href="#lightningRod:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-39895" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4121.000000 -661.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39895" ObjectName="CX_DH:CX_DH_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3228.500000 -1116.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-200703" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3243.000000 -980.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200703" ObjectName="CX_DH:CX_DH_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-200704" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3241.000000 -942.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200704" ObjectName="CX_DH:CX_DH_sumQ"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-39900" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3912.000000 -1016.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39900" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6493"/>
     <cge:Term_Ref ObjectID="9186"/>
    <cge:TPSR_Ref TObjectID="6493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39901" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3912.000000 -1016.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39901" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6493"/>
     <cge:Term_Ref ObjectID="9186"/>
    <cge:TPSR_Ref TObjectID="6493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3912.000000 -1016.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6493"/>
     <cge:Term_Ref ObjectID="9186"/>
    <cge:TPSR_Ref TObjectID="6493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-39974" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4121.000000 -677.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39974" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6531"/>
     <cge:Term_Ref ObjectID="9262"/>
    <cge:TPSR_Ref TObjectID="6531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39971" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3589.000000 -63.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39971" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6527"/>
     <cge:Term_Ref ObjectID="9254"/>
    <cge:TPSR_Ref TObjectID="6527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39966" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3589.000000 -63.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39966" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6527"/>
     <cge:Term_Ref ObjectID="9254"/>
    <cge:TPSR_Ref TObjectID="6527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-39908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3935.000000 -74.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6504"/>
     <cge:Term_Ref ObjectID="9208"/>
    <cge:TPSR_Ref TObjectID="6504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39909" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3935.000000 -74.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39909" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6504"/>
     <cge:Term_Ref ObjectID="9208"/>
    <cge:TPSR_Ref TObjectID="6504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39904" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3935.000000 -74.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39904" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6504"/>
     <cge:Term_Ref ObjectID="9208"/>
    <cge:TPSR_Ref TObjectID="6504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-39916" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4048.000000 -73.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39916" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6507"/>
     <cge:Term_Ref ObjectID="9214"/>
    <cge:TPSR_Ref TObjectID="6507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39917" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4048.000000 -73.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39917" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6507"/>
     <cge:Term_Ref ObjectID="9214"/>
    <cge:TPSR_Ref TObjectID="6507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39912" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4048.000000 -73.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39912" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6507"/>
     <cge:Term_Ref ObjectID="9214"/>
    <cge:TPSR_Ref TObjectID="6507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-39924" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4154.000000 -73.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39924" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6510"/>
     <cge:Term_Ref ObjectID="9220"/>
    <cge:TPSR_Ref TObjectID="6510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39925" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4154.000000 -73.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39925" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6510"/>
     <cge:Term_Ref ObjectID="9220"/>
    <cge:TPSR_Ref TObjectID="6510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39920" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4154.000000 -73.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39920" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6510"/>
     <cge:Term_Ref ObjectID="9220"/>
    <cge:TPSR_Ref TObjectID="6510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-39932" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4314.000000 -76.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6513"/>
     <cge:Term_Ref ObjectID="9226"/>
    <cge:TPSR_Ref TObjectID="6513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39933" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4314.000000 -76.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39933" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6513"/>
     <cge:Term_Ref ObjectID="9226"/>
    <cge:TPSR_Ref TObjectID="6513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39928" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4314.000000 -76.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39928" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6513"/>
     <cge:Term_Ref ObjectID="9226"/>
    <cge:TPSR_Ref TObjectID="6513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-39940" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4479.000000 -77.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39940" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6516"/>
     <cge:Term_Ref ObjectID="9232"/>
    <cge:TPSR_Ref TObjectID="6516"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39941" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4479.000000 -77.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39941" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6516"/>
     <cge:Term_Ref ObjectID="9232"/>
    <cge:TPSR_Ref TObjectID="6516"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39936" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4479.000000 -77.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39936" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6516"/>
     <cge:Term_Ref ObjectID="9232"/>
    <cge:TPSR_Ref TObjectID="6516"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-39946" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4657.000000 -76.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39946" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6517"/>
     <cge:Term_Ref ObjectID="9234"/>
    <cge:TPSR_Ref TObjectID="6517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39947" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4657.000000 -76.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39947" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6517"/>
     <cge:Term_Ref ObjectID="9234"/>
    <cge:TPSR_Ref TObjectID="6517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39944" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4657.000000 -76.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39944" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6517"/>
     <cge:Term_Ref ObjectID="9234"/>
    <cge:TPSR_Ref TObjectID="6517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-39858" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3789.000000 -971.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39858" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6485"/>
     <cge:Term_Ref ObjectID="9173"/>
    <cge:TPSR_Ref TObjectID="6485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-39859" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3789.000000 -971.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39859" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6485"/>
     <cge:Term_Ref ObjectID="9173"/>
    <cge:TPSR_Ref TObjectID="6485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-39860" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3789.000000 -971.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39860" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6485"/>
     <cge:Term_Ref ObjectID="9173"/>
    <cge:TPSR_Ref TObjectID="6485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-39879" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3789.000000 -971.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39879" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6485"/>
     <cge:Term_Ref ObjectID="9173"/>
    <cge:TPSR_Ref TObjectID="6485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-39862" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3789.000000 -971.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39862" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6485"/>
     <cge:Term_Ref ObjectID="9173"/>
    <cge:TPSR_Ref TObjectID="6485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-39861" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3789.000000 -971.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39861" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6485"/>
     <cge:Term_Ref ObjectID="9173"/>
    <cge:TPSR_Ref TObjectID="6485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-39865" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3572.000000 -660.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39865" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6486"/>
     <cge:Term_Ref ObjectID="9174"/>
    <cge:TPSR_Ref TObjectID="6486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-39866" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3572.000000 -660.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39866" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6486"/>
     <cge:Term_Ref ObjectID="9174"/>
    <cge:TPSR_Ref TObjectID="6486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-39867" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3572.000000 -660.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39867" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6486"/>
     <cge:Term_Ref ObjectID="9174"/>
    <cge:TPSR_Ref TObjectID="6486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-39880" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3572.000000 -660.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39880" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6486"/>
     <cge:Term_Ref ObjectID="9174"/>
    <cge:TPSR_Ref TObjectID="6486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-39869" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3572.000000 -660.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39869" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6486"/>
     <cge:Term_Ref ObjectID="9174"/>
    <cge:TPSR_Ref TObjectID="6486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-39870" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3572.000000 -660.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39870" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6486"/>
     <cge:Term_Ref ObjectID="9174"/>
    <cge:TPSR_Ref TObjectID="6486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-39871" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3572.000000 -660.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39871" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6486"/>
     <cge:Term_Ref ObjectID="9174"/>
    <cge:TPSR_Ref TObjectID="6486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-39868" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3572.000000 -660.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39868" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6486"/>
     <cge:Term_Ref ObjectID="9174"/>
    <cge:TPSR_Ref TObjectID="6486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-58402" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4821.000000 -75.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58402" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10982"/>
     <cge:Term_Ref ObjectID="15122"/>
    <cge:TPSR_Ref TObjectID="10982"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-58403" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4821.000000 -75.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58403" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10982"/>
     <cge:Term_Ref ObjectID="15122"/>
    <cge:TPSR_Ref TObjectID="10982"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-58405" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4821.000000 -75.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58405" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10982"/>
     <cge:Term_Ref ObjectID="15122"/>
    <cge:TPSR_Ref TObjectID="10982"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-39886" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4111.000000 -765.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39886" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6490"/>
     <cge:Term_Ref ObjectID="9180"/>
    <cge:TPSR_Ref TObjectID="6490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39887" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4111.000000 -765.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39887" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6490"/>
     <cge:Term_Ref ObjectID="9180"/>
    <cge:TPSR_Ref TObjectID="6490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39881" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4111.000000 -765.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6490"/>
     <cge:Term_Ref ObjectID="9180"/>
    <cge:TPSR_Ref TObjectID="6490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-39884" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4111.000000 -765.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39884" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6490"/>
     <cge:Term_Ref ObjectID="9180"/>
    <cge:TPSR_Ref TObjectID="6490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-189597" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4243.000000 -1019.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="189597" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28823"/>
     <cge:Term_Ref ObjectID="41059"/>
    <cge:TPSR_Ref TObjectID="28823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-189598" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4243.000000 -1019.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="189598" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28823"/>
     <cge:Term_Ref ObjectID="41059"/>
    <cge:TPSR_Ref TObjectID="28823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-189593" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4243.000000 -1019.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="189593" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28823"/>
     <cge:Term_Ref ObjectID="41059"/>
    <cge:TPSR_Ref TObjectID="28823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-189612" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.000000 -669.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="189612" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28834"/>
     <cge:Term_Ref ObjectID="41081"/>
    <cge:TPSR_Ref TObjectID="28834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-189611" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.000000 -669.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="189611" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28834"/>
     <cge:Term_Ref ObjectID="41081"/>
    <cge:TPSR_Ref TObjectID="28834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39963" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4966.000000 -63.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39963" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6523"/>
     <cge:Term_Ref ObjectID="9246"/>
    <cge:TPSR_Ref TObjectID="6523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39958" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4966.000000 -63.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6523"/>
     <cge:Term_Ref ObjectID="9246"/>
    <cge:TPSR_Ref TObjectID="6523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-39872" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -660.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39872" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6487"/>
     <cge:Term_Ref ObjectID="9175"/>
    <cge:TPSR_Ref TObjectID="6487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-39873" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -660.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6487"/>
     <cge:Term_Ref ObjectID="9175"/>
    <cge:TPSR_Ref TObjectID="6487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-39874" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -660.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6487"/>
     <cge:Term_Ref ObjectID="9175"/>
    <cge:TPSR_Ref TObjectID="6487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-40366" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -660.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40366" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6487"/>
     <cge:Term_Ref ObjectID="9175"/>
    <cge:TPSR_Ref TObjectID="6487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-39876" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -660.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39876" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6487"/>
     <cge:Term_Ref ObjectID="9175"/>
    <cge:TPSR_Ref TObjectID="6487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-39877" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -660.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39877" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6487"/>
     <cge:Term_Ref ObjectID="9175"/>
    <cge:TPSR_Ref TObjectID="6487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-39878" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -660.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39878" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6487"/>
     <cge:Term_Ref ObjectID="9175"/>
    <cge:TPSR_Ref TObjectID="6487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-39875" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -660.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39875" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6487"/>
     <cge:Term_Ref ObjectID="9175"/>
    <cge:TPSR_Ref TObjectID="6487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-39893" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -620.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39893" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6491"/>
     <cge:Term_Ref ObjectID="9182"/>
    <cge:TPSR_Ref TObjectID="6491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-39894" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -620.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39894" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6491"/>
     <cge:Term_Ref ObjectID="9182"/>
    <cge:TPSR_Ref TObjectID="6491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-39888" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -620.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39888" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6491"/>
     <cge:Term_Ref ObjectID="9182"/>
    <cge:TPSR_Ref TObjectID="6491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-39891" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -620.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="39891" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6491"/>
     <cge:Term_Ref ObjectID="9182"/>
    <cge:TPSR_Ref TObjectID="6491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-189603" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4574.000000 -817.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="189603" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28829"/>
     <cge:Term_Ref ObjectID="41071"/>
    <cge:TPSR_Ref TObjectID="28829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-189604" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4574.000000 -817.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="189604" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28829"/>
     <cge:Term_Ref ObjectID="41071"/>
    <cge:TPSR_Ref TObjectID="28829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-189599" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4574.000000 -817.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="189599" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28829"/>
     <cge:Term_Ref ObjectID="41071"/>
    <cge:TPSR_Ref TObjectID="28829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-189602" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4574.000000 -817.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="189602" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28829"/>
     <cge:Term_Ref ObjectID="41071"/>
    <cge:TPSR_Ref TObjectID="28829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-189609" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4558.000000 -613.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="189609" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28832"/>
     <cge:Term_Ref ObjectID="41077"/>
    <cge:TPSR_Ref TObjectID="28832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-189610" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4558.000000 -613.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="189610" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28832"/>
     <cge:Term_Ref ObjectID="41077"/>
    <cge:TPSR_Ref TObjectID="28832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-189605" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4558.000000 -613.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="189605" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28832"/>
     <cge:Term_Ref ObjectID="41077"/>
    <cge:TPSR_Ref TObjectID="28832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-189608" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4558.000000 -613.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="189608" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28832"/>
     <cge:Term_Ref ObjectID="41077"/>
    <cge:TPSR_Ref TObjectID="28832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-260535" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3781.000000 -74.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="260535" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42704"/>
     <cge:Term_Ref ObjectID="18504"/>
    <cge:TPSR_Ref TObjectID="42704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-260536" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3781.000000 -74.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="260536" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42704"/>
     <cge:Term_Ref ObjectID="18504"/>
    <cge:TPSR_Ref TObjectID="42704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-260532" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3781.000000 -74.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="260532" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42704"/>
     <cge:Term_Ref ObjectID="18504"/>
    <cge:TPSR_Ref TObjectID="42704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-285246" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5199.000000 -79.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285246" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44741"/>
     <cge:Term_Ref ObjectID="22289"/>
    <cge:TPSR_Ref TObjectID="44741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-285247" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5199.000000 -79.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285247" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44741"/>
     <cge:Term_Ref ObjectID="22289"/>
    <cge:TPSR_Ref TObjectID="44741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-285243" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5199.000000 -79.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285243" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44741"/>
     <cge:Term_Ref ObjectID="22289"/>
    <cge:TPSR_Ref TObjectID="44741"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3240" y="-1175"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3240" y="-1175"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3192" y="-1192"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3192" y="-1192"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3947" y="-1002"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3947" y="-1002"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3610" y="-414"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3610" y="-414"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3956" y="-412"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3956" y="-412"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4053" y="-412"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4053" y="-412"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4319" y="-412"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4319" y="-412"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4483" y="-413"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4483" y="-413"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4657" y="-411"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4657" y="-411"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4147" y="-412"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4147" y="-412"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4826" y="-412"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4826" y="-412"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4208" y="-630"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4208" y="-630"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4303" y="-1006"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4303" y="-1006"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="16" qtmmishow="hidden" width="54" x="4441" y="-725"/>
    </a>
   <metadata/><rect fill="white" height="16" opacity="0" stroke="white" transform="" width="54" x="4441" y="-725"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="23" qtmmishow="hidden" width="92" x="3161" y="-837"/>
    </a>
   <metadata/><rect fill="white" height="23" opacity="0" stroke="white" transform="" width="92" x="3161" y="-837"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4988" y="-414"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4988" y="-414"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3479" y="-1152"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3479" y="-1152"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3479" y="-1187"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3479" y="-1187"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="3876" y="-674"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="3876" y="-674"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3408,-1177 3405,-1180 3405,-1127 3408,-1130 3408,-1177" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3408,-1177 3405,-1180 3456,-1180 3453,-1177 3408,-1177" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="3408,-1130 3405,-1127 3456,-1127 3453,-1130 3408,-1130" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="3453,-1177 3456,-1180 3456,-1127 3453,-1130 3453,-1177" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="47" stroke="rgb(255,255,255)" width="45" x="3408" y="-1177"/>
     <rect fill="none" height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="3408" y="-1177"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3808" y="-411"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3808" y="-411"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="24" qtmmishow="hidden" width="96" x="3157" y="-782"/>
    </a>
   <metadata/><rect fill="white" height="24" opacity="0" stroke="white" transform="" width="96" x="3157" y="-782"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5248" y="-418"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5248" y="-418"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3240" y="-1175"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3192" y="-1192"/></g>
   <g href="35kV东华变东华变35kV白子东线371断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3947" y="-1002"/></g>
   <g href="35kV东华变10kV1号电容器组075断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3610" y="-414"/></g>
   <g href="35kV东华变东华变10kV东华线073断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3956" y="-412"/></g>
   <g href="35kV东华变10kV新街集镇线072断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4053" y="-412"/></g>
   <g href="35kV东华变东华变10kV本东线091断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4319" y="-412"/></g>
   <g href="35kV东华变东华变10kV朵基线092断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4483" y="-413"/></g>
   <g href="35kV东华变东华变10kV莲华线093断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4657" y="-411"/></g>
   <g href="35kV东华变东华变10kV东宜线071断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4147" y="-412"/></g>
   <g href="35kV东华变东华变10kV明东线094断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4826" y="-412"/></g>
   <g href="35kV东华变10kV母联012断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4208" y="-630"/></g>
   <g href="35kV东华变CX_DH_372间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4303" y="-1006"/></g>
   <g href="35kV东华变35kV2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="16" qtmmishow="hidden" width="54" x="4441" y="-725"/></g>
   <g href="35kV东华变GG虚设备间隔接线图_0.svg" style="fill-opacity:0"><rect height="23" qtmmishow="hidden" width="92" x="3161" y="-837"/></g>
   <g href="35kV东华变10kV2号电容器组095断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4988" y="-414"/></g>
   <g href="cx_配调_配网接线图35_楚雄.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3479" y="-1152"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3479" y="-1187"/></g>
   <g href="35kV东华变1号主变间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="3876" y="-674"/></g>
   <g href="AVC东华站.svg" style="fill-opacity:0"><rect height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="3408" y="-1177"/></g>
   <g href="35kV东华变10kV朵基Ⅱ回线074断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3808" y="-411"/></g>
   <g href="35kV东华变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="24" qtmmishow="hidden" width="96" x="3157" y="-782"/></g>
   <g href="35kV东华变CX_DH_96间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5248" y="-418"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a3b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3843.000000 1001.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a5200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3868.000000 986.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a5750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3854.000000 1016.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a60c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3867.000000 59.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a6360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3892.000000 44.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a65a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3878.000000 74.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a68d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3521.000000 63.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a6b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3546.000000 48.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_213f4f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4047.000000 661.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2140c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4047.000000 676.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2142060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4042.000000 750.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21422a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4067.000000 735.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2142450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4072.000000 719.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2142ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4053.000000 765.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2106b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4170.000000 1006.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2107060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4195.000000 991.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21072a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4181.000000 1021.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e2d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4534.000000 653.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e2f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4534.000000 668.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2048470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4899.000000 63.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2048980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4924.000000 48.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_200d780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3703.000000 937.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_200e950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3703.000000 950.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_200f000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3695.000000 906.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_200f240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3703.000000 967.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_200f480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3709.000000 890.000000) translate(0,12)">F（Hz）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_200ffe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3710.000000 921.000000) translate(0,12)">U0（V）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20121a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4031.000000 606.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2012730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4056.000000 591.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2012970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4062.000000 576.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2012bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4042.000000 621.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20133c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4507.000000 803.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20135e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4532.000000 788.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20137e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4537.000000 772.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2013a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4518.000000 818.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2014380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4488.000000 602.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20145e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4513.000000 587.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2014820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4518.000000 571.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2014a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4499.000000 617.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223cc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3985.000000 59.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223d290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4010.000000 44.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223d4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3996.000000 74.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223d800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4090.000000 58.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223da60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4115.000000 43.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223dca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4101.000000 73.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223dfd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4249.000000 62.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223e230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4274.000000 47.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223e470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4260.000000 77.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223e7a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4415.000000 62.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223ea00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4440.000000 47.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223ec40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4426.000000 77.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223ef70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4594.000000 61.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223f1d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4619.000000 46.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223f410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4605.000000 76.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223f740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4756.000000 61.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223f9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4781.000000 46.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223fbe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4767.000000 76.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fe3990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3716.000000 58.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fe3b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3741.000000 43.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fe3d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3727.000000 73.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2079960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5137.000000 63.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2079bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5162.000000 48.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2079e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5148.000000 78.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-40334">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3591.333333 -385.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6527" ObjectName="SW-CX_DH.CX_DH_075BK"/>
     <cge:Meas_Ref ObjectId="40334"/>
    <cge:TPSR_Ref TObjectID="6527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40160">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4128.666667 -383.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6510" ObjectName="SW-CX_DH.CX_DH_071BK"/>
     <cge:Meas_Ref ObjectId="40160"/>
    <cge:TPSR_Ref TObjectID="6510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40220">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4463.666667 -384.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6516" ObjectName="SW-CX_DH.CX_DH_092BK"/>
     <cge:Meas_Ref ObjectId="40220"/>
    <cge:TPSR_Ref TObjectID="6516"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40248">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4638.000000 -382.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6517" ObjectName="SW-CX_DH.CX_DH_093BK"/>
     <cge:Meas_Ref ObjectId="40248"/>
    <cge:TPSR_Ref TObjectID="6517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58387">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4807.333333 -383.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10982" ObjectName="SW-CX_DH.CX_DH_094BK"/>
     <cge:Meas_Ref ObjectId="58387"/>
    <cge:TPSR_Ref TObjectID="10982"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40190">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4299.333333 -383.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6513" ObjectName="SW-CX_DH.CX_DH_091BK"/>
     <cge:Meas_Ref ObjectId="40190"/>
    <cge:TPSR_Ref TObjectID="6513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40130">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4034.333333 -383.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6507" ObjectName="SW-CX_DH.CX_DH_072BK"/>
     <cge:Meas_Ref ObjectId="40130"/>
    <cge:TPSR_Ref TObjectID="6507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40101">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3937.500000 -383.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6504" ObjectName="SW-CX_DH.CX_DH_073BK"/>
     <cge:Meas_Ref ObjectId="40101"/>
    <cge:TPSR_Ref TObjectID="6504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39991">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.200000 -582.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6491" ObjectName="SW-CX_DH.CX_DH_001BK"/>
     <cge:Meas_Ref ObjectId="39991"/>
    <cge:TPSR_Ref TObjectID="6491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-39990">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3973.200000 -716.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6490" ObjectName="SW-CX_DH.CX_DH_301BK"/>
     <cge:Meas_Ref ObjectId="39990"/>
    <cge:TPSR_Ref TObjectID="6490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40064">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3974.333333 -973.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6493" ObjectName="SW-CX_DH.CX_DH_371BK"/>
     <cge:Meas_Ref ObjectId="40064"/>
    <cge:TPSR_Ref TObjectID="6493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40278">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 -596.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6520" ObjectName="SW-CX_DH.CX_DH_012BK"/>
     <cge:Meas_Ref ObjectId="40278"/>
    <cge:TPSR_Ref TObjectID="6520"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-189718">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4399.200000 -730.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28829" ObjectName="SW-CX_DH.CX_DH_302BK"/>
     <cge:Meas_Ref ObjectId="189718"/>
    <cge:TPSR_Ref TObjectID="28829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-189730">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4399.200000 -583.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28832" ObjectName="SW-CX_DH.CX_DH_002BK"/>
     <cge:Meas_Ref ObjectId="189730"/>
    <cge:TPSR_Ref TObjectID="28832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40308">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4969.333333 -385.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6523" ObjectName="SW-CX_DH.CX_DH_095BK"/>
     <cge:Meas_Ref ObjectId="40308"/>
    <cge:TPSR_Ref TObjectID="6523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3510.000000 -382.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-189615">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4277.333333 -977.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28823" ObjectName="SW-CX_DH.CX_DH_372BK"/>
     <cge:Meas_Ref ObjectId="189615"/>
    <cge:TPSR_Ref TObjectID="28823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-260537">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3788.500000 -383.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42704" ObjectName="SW-CX_DH.CX_DH_074BK"/>
     <cge:Meas_Ref ObjectId="260537"/>
    <cge:TPSR_Ref TObjectID="42704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285208">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5230.000000 -388.673469)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44741" ObjectName="SW-CX_DH.CX_DH_096BK"/>
     <cge:Meas_Ref ObjectId="285208"/>
    <cge:TPSR_Ref TObjectID="44741"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_BLXC" endPointId="0" endStationName="CX_DH" flowDrawDirect="1" flowShape="0" id="AC-35kV.baidong2_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4286,-1172 4286,-1206 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37781" ObjectName="AC-35kV.baidong2_line"/>
    <cge:TPSR_Ref TObjectID="37781_SS-60"/></metadata>
   <polyline fill="none" opacity="0" points="4286,-1172 4286,-1206 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="CX_DH" flowDrawDirect="1" flowShape="0" id="AC-35kV.baizidongTdh_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3983,-1187 3983,-1217 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37792" ObjectName="AC-35kV.baizidongTdh_line"/>
    <cge:TPSR_Ref TObjectID="37792_SS-60"/></metadata>
   <polyline fill="none" opacity="0" points="3983,-1187 3983,-1217 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_20a6d70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3595.000000 -90.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20a7540" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3682.000000 -294.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20a7fa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4057.000000 -1088.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20a8a30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4059.000000 -1016.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_213c9c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4060.000000 -960.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_213d450" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4057.000000 -775.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_213dee0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4721.000000 -909.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_213e970" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4720.000000 -986.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_209a330" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4483.000000 -788.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21074e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4360.000000 -1092.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2107d20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4362.000000 -1020.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2108750" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4363.000000 -964.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2048bc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4973.000000 -90.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2049390" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5060.000000 -294.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="6486" cx="4137" cy="-521" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6486" cx="4042" cy="-521" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6486" cx="3946" cy="-521" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6485" cx="4257" cy="-876" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6485" cx="4646" cy="-876" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6485" cx="3983" cy="-876" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6485" cx="3983" cy="-876" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6485" cx="4286" cy="-876" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6486" cx="3981" cy="-521" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6486" cx="3518" cy="-521" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6486" cx="3599" cy="-521" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6486" cx="3798" cy="-521" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6486" cx="4180" cy="-521" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6485" cx="4408" cy="-876" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6487" cx="4259" cy="-521" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6487" cx="4408" cy="-521" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6487" cx="4308" cy="-521" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6487" cx="4473" cy="-521" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6487" cx="4647" cy="-521" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6487" cx="4816" cy="-521" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6487" cx="4977" cy="-521" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6487" cx="4831" cy="-521" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6487" cx="5239" cy="-521" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20f4c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4602.000000 -1134.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2071640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4202.000000 -679.000000) translate(0,15)">35kV 1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fd2b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3709.000000 -734.000000) translate(0,15)">10kVI母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f3d390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4784.000000 -734.000000) translate(0,15)">10kVII母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2131330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3493.000000 -356.000000) translate(0,15)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c36d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3927.000000 -101.000000) translate(0,15)">东华线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2131900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4630.000000 -96.000000) translate(0,15)">莲华线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2131b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4104.000000 -101.000000) translate(0,15)">东宜线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2131da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4184.000000 -117.000000) translate(0,12)">10kV2号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2131da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4184.000000 -117.000000) translate(0,27)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2131ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4284.000000 -101.000000) translate(0,15)">本东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2132550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4446.000000 -102.000000) translate(0,15)">朵基线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21327d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4000.000000 -106.000000) translate(0,15)">新街集镇线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2132a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4789.000000 -101.000000) translate(0,15)">明东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2132c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3851.000000 -1080.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2132eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 -1224.000000) translate(0,15)">白</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2132eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 -1224.000000) translate(0,33)">子</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2132eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 -1224.000000) translate(0,51)">东</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2132eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 -1224.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21330e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4701.000000 -870.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2133310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 -1002.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20ee1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4001.000000 -1120.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20ee920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4002.000000 -1048.000000) translate(0,12)">37160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20eeba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4004.000000 -992.000000) translate(0,12)">37117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20eede0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3938.000000 -1061.000000) translate(0,12)">3716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20ef020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3939.000000 -938.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21203e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -964.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2120bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4683.000000 -1018.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2120e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4666.000000 -941.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2121070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3944.000000 -744.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2195470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3941.000000 -830.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2195960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4005.000000 -807.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2195ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3752.000000 -579.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2195de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3610.000000 -414.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2196020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3606.000000 -473.000000) translate(0,12)">0751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2196480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3608.000000 -350.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21966c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -326.000000) translate(0,12)">07567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21971d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3956.000000 -412.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21975d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3953.000000 -471.000000) translate(0,12)">0731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2197810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3954.000000 -349.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2197a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4053.000000 -412.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2197c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4049.000000 -471.000000) translate(0,12)">0721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2197ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4050.000000 -349.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2198110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4147.000000 -412.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2198350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4145.000000 -349.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2198590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4144.000000 -471.000000) translate(0,12)">0711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21987d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4319.000000 -412.000000) translate(0,12)">091</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2198a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4316.000000 -349.000000) translate(0,12)">0916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2198c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4315.000000 -471.000000) translate(0,12)">0912</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2198e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4483.000000 -413.000000) translate(0,12)">092</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21990d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4657.000000 -411.000000) translate(0,12)">093</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2199310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4655.000000 -348.000000) translate(0,12)">0936</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2199550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4654.000000 -470.000000) translate(0,12)">0932</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2199790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3992.000000 -612.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20abbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3988.000000 -563.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20ac0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3420.000000 -513.000000) translate(0,12)">10kVI母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20acb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4986.000000 -519.000000) translate(0,12)">10kVII母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2115470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1024.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2115470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1024.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2115470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1024.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2115470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1024.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2115470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1024.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2115470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1024.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2115470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1024.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_217c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -586.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_217c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -586.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_217c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -586.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_217c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -586.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_217c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -586.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_217c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -586.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_217c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -586.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_217c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -586.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_217c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -586.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_217c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -586.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_217c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -586.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_217c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -586.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_217c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -586.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_217c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -586.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_217c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -586.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_217c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -586.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_217c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -586.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_217c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -586.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2091cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3280.500000 -1164.500000) translate(0,16)">东华变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20dc3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4208.000000 -630.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20dc8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4138.000000 -569.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2183690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4266.000000 -571.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2185600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4824.000000 -472.000000) translate(0,12)">0942</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2185af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4825.000000 -350.000000) translate(0,12)">0946</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2187650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4480.000000 -472.000000) translate(0,12)">0922</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2187b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4481.000000 -350.000000) translate(0,12)">0926</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a34e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4826.000000 -412.000000) translate(0,12)">094</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20c5310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4173.000000 -1052.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20c5850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4255.000000 -1222.000000) translate(0,15)">白</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20c5850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4255.000000 -1222.000000) translate(0,33)">东</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20c5850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4255.000000 -1222.000000) translate(0,51)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20c5850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4255.000000 -1222.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20c5850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4255.000000 -1222.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e0b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4303.000000 -1006.000000) translate(0,12)">372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e1060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4242.000000 -1067.000000) translate(0,12)">3726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e12a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4243.000000 -943.000000) translate(0,12)">3721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e14e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4312.000000 -961.000000) translate(0,12)">37217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e1720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4304.000000 -1052.000000) translate(0,12)">37260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e1960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4303.000000 -1124.000000) translate(0,12)">37267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e1ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4418.000000 -759.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e1de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4419.000000 -853.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e2020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4431.000000 -820.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e2260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4422.000000 -562.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e24a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4424.000000 -614.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e26e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4444.000000 -724.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e3700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4783.000000 -583.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_20e40c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3247.000000 -227.000000) translate(0,16)">3806082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e5640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3814.000000 -733.000000) translate(0,12)">SZ11-4000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e5640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3814.000000 -733.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204cc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4984.000000 -473.000000) translate(0,12)">0952</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204d170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4986.000000 -350.000000) translate(0,12)">0956</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204d3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5007.000000 -324.000000) translate(0,12)">09567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_204d5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4988.000000 -414.000000) translate(0,12)">095</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20079d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3528.000000 -411.000000) translate(0,12)">076</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2008000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3525.000000 -470.000000) translate(0,12)">0761</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_200c7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3608.000000 -139.000000) translate(0,12)">07500</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_200cdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4987.000000 -141.000000) translate(0,12)">09500</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2010260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4487.000000 -698.000000) translate(0,12)">SZ11-8000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2010260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4487.000000 -698.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" graphid="g_2010930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3161.000000 -837.000000) translate(0,19)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2015c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3490.000000 -1144.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_20177e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3490.000000 -1179.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_223fe20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3128.000000 -196.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_223fe20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3128.000000 -196.000000) translate(0,38)">心变运一班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2242410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3247.000000 -175.500000) translate(0,16)">13908784302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2244a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3876.000000 -674.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_204fbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4930.000000 -86.000000) translate(0,15)">10kV2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2056ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3534.000000 -84.000000) translate(0,15)">10kV1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_20578f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3412.000000 -1163.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20583f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3517.000000 -630.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2058850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3517.000000 -645.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2058a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3517.000000 -661.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2058cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3523.000000 -613.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2058f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3529.000000 -553.000000) translate(0,12)">F(Hz)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2059150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3509.000000 -598.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2059390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3509.000000 -582.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20595d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3509.000000 -567.000000) translate(0,12)">Uca(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2059810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.000000 -629.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2059a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.000000 -644.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2059c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.000000 -660.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2059ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4912.000000 -612.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205a110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4918.000000 -552.000000) translate(0,12)">F(Hz)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205a350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4898.000000 -597.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205a590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4898.000000 -581.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205a7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4898.000000 -566.000000) translate(0,12)">Uca(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2063b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3779.000000 -100.000000) translate(0,15)">朵基II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20643d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3808.000000 -411.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20646f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3805.000000 -470.000000) translate(0,12)">0741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2064930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3806.000000 -348.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_1fe4100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3159.000000 -781.000000) translate(0,20)">隔刀远控</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ff7ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5179.000000 -124.000000) translate(0,15)">10kV1号消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ff7ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5179.000000 -124.000000) translate(0,33)">及接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20750b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5246.000000 -466.000000) translate(0,12)">0962</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20760f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5248.000000 -418.000000) translate(0,12)">096</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a5ee70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5213.000000 -275.000000) translate(0,12)">0030</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29a4a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5213.000000 -275.000000) translate(0,12)">0030</text>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="5240" cy="-296" fill="none" fillStyle="0" r="13" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="5174" cy="-230" fill="none" fillStyle="0" r="10" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="5189" cy="-230" fill="none" fillStyle="0" r="10" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_20a1dd0">
    <use class="BV-10KV" transform="matrix(0.589286 -0.000000 0.000000 0.517241 4819.000000 -715.000000)" xlink:href="#voltageTransformer:shape57"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2232320">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4639.000000 -1051.000000)" xlink:href="#voltageTransformer:shape5"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_21a6180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3599,-521 3599,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6486@0" ObjectIDZND0="6528@1" Pin0InfoVect0LinkObjId="SW-40335_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20dbc90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3599,-521 3599,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21a6370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3599,-448 3599,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6528@0" ObjectIDZND0="6527@1" Pin0InfoVect0LinkObjId="SW-40334_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40335_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3599,-448 3599,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21e50d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3600,-393 3600,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6527@0" ObjectIDZND0="6529@1" Pin0InfoVect0LinkObjId="SW-40336_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40334_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3600,-393 3600,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20cf730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-300 3686,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6530@1" ObjectIDZND0="g_20a7540@0" Pin0InfoVect0LinkObjId="g_20a7540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40337_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-300 3686,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20cf920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3629,-300 3601,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="lightningRod" ObjectIDND0="6530@0" ObjectIDZND0="6529@x" ObjectIDZND1="41437@x" ObjectIDZND2="g_2009fb0@0" Pin0InfoVect0LinkObjId="SW-40336_0" Pin0InfoVect1LinkObjId="CB-CX_DH.CX_DH_Cb1_0" Pin0InfoVect2LinkObjId="g_2009fb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40337_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3629,-300 3601,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20cfb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3601,-325 3601,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="lightningRod" ObjectIDND0="6529@0" ObjectIDZND0="6530@x" ObjectIDZND1="41437@x" ObjectIDZND2="g_2009fb0@0" Pin0InfoVect0LinkObjId="SW-40337_0" Pin0InfoVect1LinkObjId="CB-CX_DH.CX_DH_Cb1_0" Pin0InfoVect2LinkObjId="g_2009fb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40336_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3601,-325 3601,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20cfd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3601,-300 3601,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="capacitor" ObjectIDND0="6530@x" ObjectIDND1="6529@x" ObjectIDND2="g_2009fb0@0" ObjectIDZND0="41437@0" Pin0InfoVect0LinkObjId="CB-CX_DH.CX_DH_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40337_0" Pin1InfoVect1LinkObjId="SW-40336_0" Pin1InfoVect2LinkObjId="g_2009fb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3601,-300 3601,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20f1530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3601,-172 3601,-154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3601,-172 3601,-154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20f1720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3601,-118 3601,-108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_20a6d70@0" Pin0InfoVect0LinkObjId="g_20a6d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3601,-118 3601,-108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21ac890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4137,-521 4137,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6486@0" ObjectIDZND0="6511@1" Pin0InfoVect0LinkObjId="SW-40161_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20dbc90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4137,-521 4137,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21aca80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4137,-446 4137,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6511@0" ObjectIDZND0="6510@1" Pin0InfoVect0LinkObjId="SW-40160_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40161_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4137,-446 4137,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20f7100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4138,-391 4138,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6510@0" ObjectIDZND0="6512@1" Pin0InfoVect0LinkObjId="SW-40162_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4138,-391 4138,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20f72f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4179,-197 4179,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_21e52c0@0" Pin0InfoVect0LinkObjId="g_21e52c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4179,-197 4179,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2145cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4473,-446 4473,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10979@0" ObjectIDZND0="6516@1" Pin0InfoVect0LinkObjId="SW-40220_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58384_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4473,-446 4473,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2147ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4473,-392 4473,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6516@0" ObjectIDZND0="10980@1" Pin0InfoVect0LinkObjId="SW-58385_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4473,-392 4473,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20953e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4473,-260 4473,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="10980@x" ObjectIDND1="g_2147ca0@0" ObjectIDZND0="34371@0" Pin0InfoVect0LinkObjId="EC-CX_DH.092Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58385_0" Pin1InfoVect1LinkObjId="g_2147ca0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4473,-260 4473,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_208d4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4647,-444 4647,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6518@0" ObjectIDZND0="6517@1" Pin0InfoVect0LinkObjId="SW-40248_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40249_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4647,-444 4647,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_208f290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4647,-390 4647,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6517@0" ObjectIDZND0="6519@1" Pin0InfoVect0LinkObjId="SW-40250_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40248_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4647,-390 4647,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21678c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4647,-258 4647,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="6519@x" ObjectIDND1="g_208f480@0" ObjectIDZND0="34372@0" Pin0InfoVect0LinkObjId="EC-CX_DH.093Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40250_0" Pin1InfoVect1LinkObjId="g_208f480_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4647,-258 4647,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2151bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-445 4816,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10983@0" ObjectIDZND0="10982@1" Pin0InfoVect0LinkObjId="SW-58387_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58388_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-445 4816,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2153990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-391 4816,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10982@0" ObjectIDZND0="10984@1" Pin0InfoVect0LinkObjId="SW-58389_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58387_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-391 4816,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21549d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4779,-237 4779,-259 4816,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2153b80@0" ObjectIDZND0="10984@x" ObjectIDZND1="34373@x" Pin0InfoVect0LinkObjId="SW-58389_0" Pin0InfoVect1LinkObjId="EC-CX_DH.094Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2153b80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4779,-237 4779,-259 4816,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2154bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-324 4816,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="10984@0" ObjectIDZND0="g_2153b80@0" ObjectIDZND1="34373@x" Pin0InfoVect0LinkObjId="g_2153b80_0" Pin0InfoVect1LinkObjId="EC-CX_DH.094Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58389_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-324 4816,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2154db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-259 4816,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2153b80@0" ObjectIDND1="10984@x" ObjectIDZND0="34373@0" Pin0InfoVect0LinkObjId="EC-CX_DH.094Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2153b80_0" Pin1InfoVect1LinkObjId="SW-58389_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-259 4816,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_218b2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-445 4308,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6514@0" ObjectIDZND0="6513@1" Pin0InfoVect0LinkObjId="SW-40190_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40191_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4308,-445 4308,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_218d4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-391 4308,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6513@0" ObjectIDZND0="6515@1" Pin0InfoVect0LinkObjId="SW-40192_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4308,-391 4308,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_210fed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-259 4308,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="6515@x" ObjectIDND1="g_218d700@0" ObjectIDZND0="34370@0" Pin0InfoVect0LinkObjId="EC-CX_DH.091Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40192_0" Pin1InfoVect1LinkObjId="g_218d700_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4308,-259 4308,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_206a6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4042,-521 4042,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6486@0" ObjectIDZND0="6508@1" Pin0InfoVect0LinkObjId="SW-40131_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20dbc90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4042,-521 4042,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_206a8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4042,-446 4042,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6508@0" ObjectIDZND0="6507@1" Pin0InfoVect0LinkObjId="SW-40130_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40131_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4042,-446 4042,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_206cd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4043,-391 4043,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6507@0" ObjectIDZND0="6509@1" Pin0InfoVect0LinkObjId="SW-40132_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4043,-391 4043,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2174d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4043,-259 4043,-139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_206cfa0@0" ObjectIDND1="6509@x" ObjectIDZND0="34374@0" Pin0InfoVect0LinkObjId="EC-CX_DH.072Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_206cfa0_0" Pin1InfoVect1LinkObjId="SW-40132_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4043,-259 4043,-139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20dcb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3947,-391 3947,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6504@0" ObjectIDZND0="6506@1" Pin0InfoVect0LinkObjId="SW-40103_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40101_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3947,-391 3947,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20dcd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-236 3910,-258 3947,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_20dcf80@0" ObjectIDZND0="6506@x" ObjectIDZND1="34369@x" Pin0InfoVect0LinkObjId="SW-40103_0" Pin0InfoVect1LinkObjId="EC-CX_DH.073Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20dcf80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-236 3910,-258 3947,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20ddaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3947,-324 3947,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="6506@0" ObjectIDZND0="g_20dcf80@0" ObjectIDZND1="34369@x" Pin0InfoVect0LinkObjId="g_20dcf80_0" Pin0InfoVect1LinkObjId="EC-CX_DH.073Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40103_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3947,-324 3947,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20ddd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3947,-258 3947,-134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_20dcf80@0" ObjectIDND1="6506@x" ObjectIDZND0="34369@0" Pin0InfoVect0LinkObjId="EC-CX_DH.073Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20dcf80_0" Pin1InfoVect1LinkObjId="SW-40103_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3947,-258 3947,-134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_218f210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3745,-521 3745,-554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6486@0" ObjectIDZND0="6489@0" Pin0InfoVect0LinkObjId="SW-39976_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20dbc90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3745,-521 3745,-554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2191bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-632 3696,-610 3745,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2191040@0" ObjectIDZND0="g_218f430@0" ObjectIDZND1="6489@x" Pin0InfoVect0LinkObjId="g_218f430_0" Pin0InfoVect1LinkObjId="SW-39976_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2191040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-632 3696,-610 3745,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2191e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3744,-675 3744,-690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_218f430@1" ObjectIDZND0="g_218fe80@0" Pin0InfoVect0LinkObjId="g_218fe80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_218f430_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3744,-675 3744,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2117860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4781,-629 4781,-607 4829,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2219cb0@0" ObjectIDZND0="28833@x" ObjectIDZND1="g_1e7e850@0" Pin0InfoVect0LinkObjId="SW-189768_0" Pin0InfoVect1LinkObjId="g_1e7e850_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2219cb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4781,-629 4781,-607 4829,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2117a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4830,-591 4830,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="28833@1" ObjectIDZND0="g_2219cb0@0" ObjectIDZND1="g_1e7e850@0" Pin0InfoVect0LinkObjId="g_2219cb0_0" Pin0InfoVect1LinkObjId="g_1e7e850_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-189768_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4830,-591 4830,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2117ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4830,-607 4830,-627 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2219cb0@0" ObjectIDND1="28833@x" ObjectIDZND0="g_1e7e850@0" Pin0InfoVect0LinkObjId="g_1e7e850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2219cb0_0" Pin1InfoVect1LinkObjId="SW-189768_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4830,-607 4830,-627 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2117ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4829,-672 4829,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1e7e850@1" ObjectIDZND0="g_20a1dd0@0" Pin0InfoVect0LinkObjId="g_20a1dd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e7e850_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4829,-672 4829,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21720b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4256,-775 4256,-792 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2171860@0" Pin0InfoVect0LinkObjId="g_2171860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4256,-775 4256,-792 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21722d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-841 4257,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_2171860@1" ObjectIDZND0="6485@0" Pin0InfoVect0LinkObjId="g_20ae060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2171860_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-841 4257,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ade40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4646,-915 4646,-939 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="6499@x" ObjectIDND1="6485@0" ObjectIDZND0="6500@0" Pin0InfoVect0LinkObjId="SW-40071_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40070_0" Pin1InfoVect1LinkObjId="g_21722d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4646,-915 4646,-939 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ae060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4646,-915 4646,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="6500@x" ObjectIDND1="6499@x" ObjectIDZND0="6485@0" Pin0InfoVect0LinkObjId="g_21722d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40071_0" Pin1InfoVect1LinkObjId="SW-40070_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4646,-915 4646,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ae280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3983,-842 3983,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6497@1" ObjectIDZND0="6485@0" Pin0InfoVect0LinkObjId="g_21722d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40068_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3983,-842 3983,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ae4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3982,-781 3982,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6490@x" ObjectIDND1="6496@x" ObjectIDZND0="6497@0" Pin0InfoVect0LinkObjId="SW-40068_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-39990_0" Pin1InfoVect1LinkObjId="SW-40067_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3982,-781 3982,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a89d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3983,-876 3983,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6485@0" ObjectIDZND0="6495@0" Pin0InfoVect0LinkObjId="SW-40066_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21722d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3983,-876 3983,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a94a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3983,-949 3983,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6495@1" ObjectIDZND0="6493@x" ObjectIDZND1="6503@x" Pin0InfoVect0LinkObjId="SW-40064_0" Pin0InfoVect1LinkObjId="SW-40074_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40066_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3983,-949 3983,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21a9700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3983,-966 3983,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6495@x" ObjectIDND1="6503@x" ObjectIDZND0="6493@0" Pin0InfoVect0LinkObjId="SW-40064_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40066_0" Pin1InfoVect1LinkObjId="SW-40074_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3983,-966 3983,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_206e770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3983,-1008 3983,-1022 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6493@1" ObjectIDZND0="6494@x" ObjectIDZND1="6502@x" Pin0InfoVect0LinkObjId="SW-40065_0" Pin0InfoVect1LinkObjId="SW-40073_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40064_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3983,-1008 3983,-1022 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_206e9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3983,-1022 3983,-1036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6493@x" ObjectIDND1="6502@x" ObjectIDZND0="6494@0" Pin0InfoVect0LinkObjId="SW-40065_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40064_0" Pin1InfoVect1LinkObjId="SW-40073_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3983,-1022 3983,-1036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_206f4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3983,-1072 3983,-1094 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="6494@1" ObjectIDZND0="g_206f6e0@0" ObjectIDZND1="g_2070c70@0" ObjectIDZND2="37792@1" Pin0InfoVect0LinkObjId="g_206f6e0_0" Pin0InfoVect1LinkObjId="g_2070c70_0" Pin0InfoVect2LinkObjId="g_2070a10_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40065_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3983,-1072 3983,-1094 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_206fac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4029,-1149 3983,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_206f6e0@0" ObjectIDZND0="6494@x" ObjectIDZND1="6501@x" ObjectIDZND2="g_2070c70@0" Pin0InfoVect0LinkObjId="SW-40065_0" Pin0InfoVect1LinkObjId="SW-40072_0" Pin0InfoVect2LinkObjId="g_2070c70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_206f6e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4029,-1149 3983,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20707b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3983,-1094 3983,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="6494@x" ObjectIDND1="6501@x" ObjectIDZND0="g_206f6e0@0" ObjectIDZND1="g_2070c70@0" ObjectIDZND2="37792@1" Pin0InfoVect0LinkObjId="g_206f6e0_0" Pin0InfoVect1LinkObjId="g_2070c70_0" Pin0InfoVect2LinkObjId="g_2070a10_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40065_0" Pin1InfoVect1LinkObjId="SW-40072_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3983,-1094 3983,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2070a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3983,-1149 3983,-1189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_206f6e0@0" ObjectIDND1="6494@x" ObjectIDND2="6501@x" ObjectIDZND0="37792@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_206f6e0_0" Pin1InfoVect1LinkObjId="SW-40065_0" Pin1InfoVect2LinkObjId="SW-40072_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3983,-1149 3983,-1189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20713e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3983,-1149 3920,-1149 3920,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_206f6e0@0" ObjectIDND1="6494@x" ObjectIDND2="6501@x" ObjectIDZND0="g_2070c70@0" Pin0InfoVect0LinkObjId="g_2070c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_206f6e0_0" Pin1InfoVect1LinkObjId="SW-40065_0" Pin1InfoVect2LinkObjId="SW-40072_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3983,-1149 3920,-1149 3920,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2071d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3982,-751 3982,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6490@1" ObjectIDZND0="6497@x" ObjectIDZND1="6496@x" Pin0InfoVect0LinkObjId="SW-40068_0" Pin0InfoVect1LinkObjId="SW-40067_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39990_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3982,-751 3982,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21338a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4061,-1094 4039,-1094 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_20a7fa0@0" ObjectIDZND0="6501@1" Pin0InfoVect0LinkObjId="SW-40072_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20a7fa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4061,-1094 4039,-1094 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2133b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4003,-1094 3982,-1094 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="6501@0" ObjectIDZND0="6494@x" ObjectIDZND1="g_206f6e0@0" ObjectIDZND2="g_2070c70@0" Pin0InfoVect0LinkObjId="SW-40065_0" Pin0InfoVect1LinkObjId="g_206f6e0_0" Pin0InfoVect2LinkObjId="g_2070c70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40072_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4003,-1094 3982,-1094 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2134230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4063,-1022 4040,-1022 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_20a8a30@0" ObjectIDZND0="6502@1" Pin0InfoVect0LinkObjId="SW-40073_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20a8a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4063,-1022 4040,-1022 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ec050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4004,-1022 3984,-1022 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6502@0" ObjectIDZND0="6493@x" ObjectIDZND1="6494@x" Pin0InfoVect0LinkObjId="SW-40064_0" Pin0InfoVect1LinkObjId="SW-40065_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40073_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4004,-1022 3984,-1022 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20edce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4064,-966 4042,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_213c9c0@0" ObjectIDZND0="6503@1" Pin0InfoVect0LinkObjId="SW-40074_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_213c9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4064,-966 4042,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20edf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4006,-966 3985,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="6503@0" ObjectIDZND0="6495@x" ObjectIDZND1="6493@x" Pin0InfoVect0LinkObjId="SW-40066_0" Pin0InfoVect1LinkObjId="SW-40064_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40074_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4006,-966 3985,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_211fa60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4724,-992 4703,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_213e970@0" ObjectIDZND0="6498@1" Pin0InfoVect0LinkObjId="SW-40069_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_213e970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4724,-992 4703,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_211fcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4667,-992 4646,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="6498@0" ObjectIDZND0="6500@x" ObjectIDZND1="g_2235720@0" ObjectIDZND2="g_2232320@0" Pin0InfoVect0LinkObjId="SW-40071_0" Pin0InfoVect1LinkObjId="g_2235720_0" Pin0InfoVect2LinkObjId="g_2232320_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40069_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4667,-992 4646,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_211ff20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4646,-915 4668,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="6500@x" ObjectIDND1="6485@0" ObjectIDZND0="6499@0" Pin0InfoVect0LinkObjId="SW-40070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40071_0" Pin1InfoVect1LinkObjId="g_21722d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4646,-915 4668,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2120180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4704,-915 4725,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6499@1" ObjectIDZND0="g_213dee0@0" Pin0InfoVect0LinkObjId="g_213dee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40070_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4704,-915 4725,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2194fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4061,-781 4043,-781 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_213d450@0" ObjectIDZND0="6496@1" Pin0InfoVect0LinkObjId="SW-40067_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_213d450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4061,-781 4043,-781 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2195210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-781 3982,-781 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="6496@0" ObjectIDZND0="6497@x" ObjectIDZND1="6490@x" Pin0InfoVect0LinkObjId="SW-40068_0" Pin0InfoVect1LinkObjId="SW-39990_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40067_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-781 3982,-781 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2196900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3946,-521 3946,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6486@0" ObjectIDZND0="6505@1" Pin0InfoVect0LinkObjId="SW-40102_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20dbc90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3946,-521 3946,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2196af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3946,-446 3946,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6505@0" ObjectIDZND0="6504@1" Pin0InfoVect0LinkObjId="SW-40101_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40102_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3946,-446 3946,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20ab700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3981,-521 3981,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6486@0" ObjectIDZND0="6492@0" Pin0InfoVect0LinkObjId="SW-39975_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20dbc90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3981,-521 3981,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20ab960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3981,-574 3981,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6492@1" ObjectIDZND0="6491@0" Pin0InfoVect0LinkObjId="SW-39991_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39975_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3981,-574 3981,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20dbc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-544 4180,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6522@0" ObjectIDZND0="6486@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-544 4180,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20dbef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-606 4180,-606 4180,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6520@1" ObjectIDZND0="6522@1" Pin0InfoVect0LinkObjId="SW-40280_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40278_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-606 4180,-606 4180,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20dc150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4259,-582 4259,-606 4234,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6521@1" ObjectIDZND0="6520@0" Pin0InfoVect0LinkObjId="SW-40278_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40279_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4259,-582 4259,-606 4234,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21838b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3982,-725 3982,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="6490@0" ObjectIDZND0="6531@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-39990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3982,-725 3982,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21864b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4006,-237 4006,-259 4043,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_206cfa0@0" ObjectIDZND0="6509@x" ObjectIDZND1="34374@x" Pin0InfoVect0LinkObjId="SW-40132_0" Pin0InfoVect1LinkObjId="EC-CX_DH.072Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_206cfa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4006,-237 4006,-259 4043,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21866a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4043,-259 4043,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_206cfa0@0" ObjectIDND1="34374@x" ObjectIDZND0="6509@0" Pin0InfoVect0LinkObjId="SW-40132_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_206cfa0_0" Pin1InfoVect1LinkObjId="EC-CX_DH.072Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4043,-259 4043,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2186890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4272,-237 4272,-259 4308,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_218d700@0" ObjectIDZND0="6515@x" ObjectIDZND1="34370@x" Pin0InfoVect0LinkObjId="SW-40192_0" Pin0InfoVect1LinkObjId="EC-CX_DH.091Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_218d700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4272,-237 4272,-259 4308,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2186aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-259 4308,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_218d700@0" ObjectIDND1="34370@x" ObjectIDZND0="6515@0" Pin0InfoVect0LinkObjId="SW-40192_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_218d700_0" Pin1InfoVect1LinkObjId="EC-CX_DH.091Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4308,-259 4308,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2186cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4437,-238 4437,-260 4473,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2147ca0@0" ObjectIDZND0="10980@x" ObjectIDZND1="34371@x" Pin0InfoVect0LinkObjId="SW-58385_0" Pin0InfoVect1LinkObjId="EC-CX_DH.092Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2147ca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4437,-238 4437,-260 4473,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2186f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4473,-260 4473,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2147ca0@0" ObjectIDND1="34371@x" ObjectIDZND0="10980@0" Pin0InfoVect0LinkObjId="SW-58385_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2147ca0_0" Pin1InfoVect1LinkObjId="EC-CX_DH.092Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4473,-260 4473,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2187190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4611,-236 4611,-258 4647,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_208f480@0" ObjectIDZND0="6519@x" ObjectIDZND1="34372@x" Pin0InfoVect0LinkObjId="SW-40250_0" Pin0InfoVect1LinkObjId="EC-CX_DH.093Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_208f480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4611,-236 4611,-258 4647,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21873f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4647,-258 4647,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_208f480@0" ObjectIDND1="34372@x" ObjectIDZND0="6519@0" Pin0InfoVect0LinkObjId="SW-40250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_208f480_0" Pin1InfoVect1LinkObjId="EC-CX_DH.093Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4647,-258 4647,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20a32f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4138,-138 4138,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="34368@0" ObjectIDZND0="6512@x" ObjectIDZND1="g_22394a0@0" ObjectIDZND2="g_21e52c0@0" Pin0InfoVect0LinkObjId="SW-40162_0" Pin0InfoVect1LinkObjId="g_22394a0_0" Pin0InfoVect2LinkObjId="g_21e52c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_DH.071Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4138,-138 4138,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2141a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3745,-630 3745,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_218f430@0" ObjectIDZND0="g_2191040@0" ObjectIDZND1="6489@x" Pin0InfoVect0LinkObjId="g_2191040_0" Pin0InfoVect1LinkObjId="SW-39976_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_218f430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3745,-630 3745,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2141c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3745,-610 3745,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2191040@0" ObjectIDND1="g_218f430@0" ObjectIDZND0="6489@1" Pin0InfoVect0LinkObjId="SW-39976_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2191040_0" Pin1InfoVect1LinkObjId="g_218f430_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3745,-610 3745,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_213b300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4408,-854 4408,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28830@1" ObjectIDZND0="6485@0" Pin0InfoVect0LinkObjId="g_21722d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-189719_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4408,-854 4408,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_213b560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4408,-794 4408,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="28829@x" ObjectIDND1="28831@x" ObjectIDZND0="28830@0" Pin0InfoVect0LinkObjId="SW-189719_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-189718_0" Pin1InfoVect1LinkObjId="SW-189720_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4408,-794 4408,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_213b7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4408,-764 4408,-795 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28829@1" ObjectIDZND0="28830@x" ObjectIDZND1="28831@x" Pin0InfoVect0LinkObjId="SW-189719_0" Pin0InfoVect1LinkObjId="SW-189720_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-189718_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4408,-764 4408,-795 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2099c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4487,-794 4469,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_209a330@0" ObjectIDZND0="28831@1" Pin0InfoVect0LinkObjId="SW-189720_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_209a330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4487,-794 4469,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2099e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-794 4408,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28831@0" ObjectIDZND0="28830@x" ObjectIDZND1="28829@x" Pin0InfoVect0LinkObjId="SW-189719_0" Pin0InfoVect1LinkObjId="SW-189718_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-189720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-794 4408,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_209a0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4408,-738 4408,-725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="28829@0" ObjectIDZND0="28834@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-189718_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4408,-738 4408,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20a1910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4408,-575 4408,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28835@1" ObjectIDZND0="28832@0" Pin0InfoVect0LinkObjId="SW-189730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-189775_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4408,-575 4408,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20a1b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4408,-633 4408,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="28834@0" ObjectIDZND0="28832@1" Pin0InfoVect0LinkObjId="SW-189730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_209a0d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4408,-633 4408,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c1fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4286,-876 4286,-917 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6485@0" ObjectIDZND0="28824@0" Pin0InfoVect0LinkObjId="SW-189616_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21722d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4286,-876 4286,-917 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c2210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4286,-953 4286,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="28824@1" ObjectIDZND0="28823@x" ObjectIDZND1="28826@x" Pin0InfoVect0LinkObjId="SW-189615_0" Pin0InfoVect1LinkObjId="SW-189618_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-189616_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4286,-953 4286,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c2470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4286,-970 4286,-985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="28824@x" ObjectIDND1="28826@x" ObjectIDZND0="28823@0" Pin0InfoVect0LinkObjId="SW-189615_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-189616_0" Pin1InfoVect1LinkObjId="SW-189618_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4286,-970 4286,-985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c26d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4286,-1012 4286,-1026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28823@1" ObjectIDZND0="28825@x" ObjectIDZND1="28827@x" Pin0InfoVect0LinkObjId="SW-189617_0" Pin0InfoVect1LinkObjId="SW-189619_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-189615_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4286,-1012 4286,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c2930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4286,-1026 4286,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="28823@x" ObjectIDND1="28827@x" ObjectIDZND0="28825@0" Pin0InfoVect0LinkObjId="SW-189617_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-189615_0" Pin1InfoVect1LinkObjId="SW-189619_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4286,-1026 4286,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c2b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4286,-1076 4286,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28825@1" ObjectIDZND0="28828@x" ObjectIDZND1="g_20c4020@0" ObjectIDZND2="g_20c2df0@0" Pin0InfoVect0LinkObjId="SW-189620_0" Pin0InfoVect1LinkObjId="g_20c4020_0" Pin0InfoVect2LinkObjId="g_20c2df0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-189617_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4286,-1076 4286,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c3b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1153 4286,-1153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_20c2df0@0" ObjectIDZND0="g_20c4020@0" ObjectIDZND1="28825@x" ObjectIDZND2="28828@x" Pin0InfoVect0LinkObjId="g_20c4020_0" Pin0InfoVect1LinkObjId="SW-189617_0" Pin0InfoVect2LinkObjId="SW-189620_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20c2df0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1153 4286,-1153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c3dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4286,-1153 4286,-1173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_20c2df0@0" ObjectIDND1="g_20c4020@0" ObjectIDND2="28825@x" ObjectIDZND0="37781@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20c2df0_0" Pin1InfoVect1LinkObjId="g_20c4020_0" Pin1InfoVect2LinkObjId="SW-189617_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4286,-1153 4286,-1173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c50b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4286,-1131 4223,-1131 4223,-1126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28825@x" ObjectIDND1="28828@x" ObjectIDND2="g_20c2df0@0" ObjectIDZND0="g_20c4020@0" Pin0InfoVect0LinkObjId="g_20c4020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-189617_0" Pin1InfoVect1LinkObjId="SW-189620_0" Pin1InfoVect2LinkObjId="g_20c2df0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4286,-1131 4223,-1131 4223,-1126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21006c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4364,-1098 4342,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_21074e0@0" ObjectIDZND0="28828@1" Pin0InfoVect0LinkObjId="SW-189620_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21074e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4364,-1098 4342,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2100920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4306,-1098 4285,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28828@0" ObjectIDZND0="28825@x" ObjectIDZND1="g_20c4020@0" ObjectIDZND2="g_20c2df0@0" Pin0InfoVect0LinkObjId="SW-189617_0" Pin0InfoVect1LinkObjId="g_20c4020_0" Pin0InfoVect2LinkObjId="g_20c2df0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-189620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4306,-1098 4285,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2103630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-1026 4343,-1026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2107d20@0" ObjectIDZND0="28827@1" Pin0InfoVect0LinkObjId="SW-189619_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2107d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-1026 4343,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2103890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4307,-1026 4287,-1026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28827@0" ObjectIDZND0="28825@x" ObjectIDZND1="28823@x" Pin0InfoVect0LinkObjId="SW-189617_0" Pin0InfoVect1LinkObjId="SW-189615_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-189619_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4307,-1026 4287,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21065a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4367,-970 4345,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2108750@0" ObjectIDZND0="28826@1" Pin0InfoVect0LinkObjId="SW-189618_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2108750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4367,-970 4345,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2106800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4309,-970 4288,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="28826@0" ObjectIDZND0="28823@x" ObjectIDZND1="28824@x" Pin0InfoVect0LinkObjId="SW-189615_0" Pin0InfoVect1LinkObjId="SW-189616_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-189618_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4309,-970 4288,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20e03b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4286,-1098 4286,-1131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="28825@x" ObjectIDND1="28828@x" ObjectIDZND0="g_20c4020@0" ObjectIDZND1="g_20c2df0@0" ObjectIDZND2="37781@1" Pin0InfoVect0LinkObjId="g_20c4020_0" Pin0InfoVect1LinkObjId="g_20c2df0_0" Pin0InfoVect2LinkObjId="g_20c3dc0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-189617_0" Pin1InfoVect1LinkObjId="SW-189620_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4286,-1098 4286,-1131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20e0610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4286,-1153 4286,-1131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_20c2df0@0" ObjectIDND1="37781@1" ObjectIDZND0="g_20c4020@0" ObjectIDZND1="28825@x" ObjectIDZND2="28828@x" Pin0InfoVect0LinkObjId="g_20c4020_0" Pin0InfoVect1LinkObjId="SW-189617_0" Pin0InfoVect2LinkObjId="SW-189620_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20c2df0_0" Pin1InfoVect1LinkObjId="g_20c3dc0_1" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4286,-1153 4286,-1131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2048120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4979,-118 4979,-108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2048bc0@0" Pin0InfoVect0LinkObjId="g_2048bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4979,-118 4979,-108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_204be40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4977,-448 4978,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6524@0" ObjectIDZND0="6523@1" Pin0InfoVect0LinkObjId="SW-40308_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40309_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4977,-448 4978,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_204c0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4978,-393 4979,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6523@0" ObjectIDZND0="6525@1" Pin0InfoVect0LinkObjId="SW-40310_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40308_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4978,-393 4979,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_204c300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5007,-300 4979,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="lightningRod" ObjectIDND0="6526@0" ObjectIDZND0="6525@x" ObjectIDZND1="10568@x" ObjectIDZND2="g_200b7d0@0" Pin0InfoVect0LinkObjId="SW-40310_0" Pin0InfoVect1LinkObjId="CB-CX_DH.CX_DH_Cb2_0" Pin0InfoVect2LinkObjId="g_200b7d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40311_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5007,-300 4979,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_204c560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4979,-325 4979,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="lightningRod" ObjectIDND0="6525@0" ObjectIDZND0="6526@x" ObjectIDZND1="10568@x" ObjectIDZND2="g_200b7d0@0" Pin0InfoVect0LinkObjId="SW-40311_0" Pin0InfoVect1LinkObjId="CB-CX_DH.CX_DH_Cb2_0" Pin0InfoVect2LinkObjId="g_200b7d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4979,-325 4979,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_204c7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4979,-300 4979,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="capacitor" ObjectIDND0="6526@x" ObjectIDND1="6525@x" ObjectIDND2="g_200b7d0@0" ObjectIDZND0="10568@0" Pin0InfoVect0LinkObjId="CB-CX_DH.CX_DH_Cb2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40311_0" Pin1InfoVect1LinkObjId="SW-40310_0" Pin1InfoVect2LinkObjId="g_200b7d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4979,-300 4979,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_204ca20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4979,-154 4979,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4979,-154 4979,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20072e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3518,-521 3518,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6486@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20dbc90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3518,-521 3518,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2007510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3518,-445 3518,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3518,-445 3518,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2007770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3519,-390 3519,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3519,-390 3519,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2009d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3538,-278 3538,-300 3605,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_2009fb0@0" ObjectIDZND0="6530@x" ObjectIDZND1="6529@x" ObjectIDZND2="41437@x" Pin0InfoVect0LinkObjId="SW-40337_0" Pin0InfoVect1LinkObjId="SW-40336_0" Pin0InfoVect2LinkObjId="CB-CX_DH.CX_DH_Cb1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2009fb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3538,-278 3538,-300 3605,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_200c540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4923,-300 4979,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_200b7d0@0" ObjectIDZND0="6526@x" ObjectIDZND1="6525@x" ObjectIDZND2="10568@x" Pin0InfoVect0LinkObjId="SW-40311_0" Pin0InfoVect1LinkObjId="SW-40310_0" Pin0InfoVect2LinkObjId="CB-CX_DH.CX_DH_Cb2_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_200b7d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4923,-300 4979,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22332d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4646,-992 4646,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="6498@x" ObjectIDND1="g_2235720@0" ObjectIDND2="g_2232320@0" ObjectIDZND0="6500@1" Pin0InfoVect0LinkObjId="SW-40071_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40069_0" Pin1InfoVect1LinkObjId="g_2235720_0" Pin1InfoVect2LinkObjId="g_2232320_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4646,-992 4646,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22364d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4672,-1012 4672,-1006 4646,-1006 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2235720@0" ObjectIDZND0="g_2232320@0" ObjectIDZND1="6498@x" ObjectIDZND2="6500@x" Pin0InfoVect0LinkObjId="g_2232320_0" Pin0InfoVect1LinkObjId="SW-40069_0" Pin0InfoVect2LinkObjId="SW-40071_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2235720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4672,-1012 4672,-1006 4646,-1006 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2236fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4646,-1054 4646,-1006 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2232320@0" ObjectIDZND0="g_2235720@0" ObjectIDZND1="6498@x" ObjectIDZND2="6500@x" Pin0InfoVect0LinkObjId="g_2235720_0" Pin0InfoVect1LinkObjId="SW-40069_0" Pin0InfoVect2LinkObjId="SW-40071_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2232320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4646,-1054 4646,-1006 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2237220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4646,-1006 4646,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2235720@0" ObjectIDND1="g_2232320@0" ObjectIDZND0="6498@x" ObjectIDZND1="6500@x" Pin0InfoVect0LinkObjId="SW-40069_0" Pin0InfoVect1LinkObjId="SW-40071_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2235720_0" Pin1InfoVect1LinkObjId="g_2232320_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4646,-1006 4646,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2239240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4138,-324 4138,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="6512@0" ObjectIDZND0="34368@x" ObjectIDZND1="g_22394a0@0" ObjectIDZND2="g_21e52c0@0" Pin0InfoVect0LinkObjId="EC-CX_DH.071Ld_0" Pin0InfoVect1LinkObjId="g_22394a0_0" Pin0InfoVect2LinkObjId="g_21e52c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40162_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4138,-324 4138,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_223a210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4187,-274 4180,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_22394a0@0" ObjectIDZND0="34368@x" ObjectIDZND1="6512@x" ObjectIDZND2="g_21e52c0@0" Pin0InfoVect0LinkObjId="EC-CX_DH.071Ld_0" Pin0InfoVect1LinkObjId="SW-40162_0" Pin0InfoVect2LinkObjId="g_21e52c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22394a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4187,-274 4180,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_223ad00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4138,-281 4180,-281 4180,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="34368@x" ObjectIDND1="6512@x" ObjectIDZND0="g_22394a0@0" ObjectIDZND1="g_21e52c0@0" Pin0InfoVect0LinkObjId="g_22394a0_0" Pin0InfoVect1LinkObjId="g_21e52c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_DH.071Ld_0" Pin1InfoVect1LinkObjId="SW-40162_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4138,-281 4180,-281 4180,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_223af60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-274 4180,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_22394a0@0" ObjectIDND1="34368@x" ObjectIDND2="6512@x" ObjectIDZND0="g_21e52c0@1" Pin0InfoVect0LinkObjId="g_21e52c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22394a0_0" Pin1InfoVect1LinkObjId="EC-CX_DH.071Ld_0" Pin1InfoVect2LinkObjId="SW-40162_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-274 4180,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_223c320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3981,-632 3981,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="6531@1" ObjectIDZND0="6491@1" Pin0InfoVect0LinkObjId="SW-39991_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21838b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3981,-632 3981,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20575c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5064,-300 5043,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2049390@0" ObjectIDZND0="6526@1" Pin0InfoVect0LinkObjId="SW-40311_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2049390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5064,-300 5043,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2062410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,-390 3799,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42704@0" ObjectIDZND0="42706@1" Pin0InfoVect0LinkObjId="SW-260540_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260537_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3799,-390 3799,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2062670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3762,-235 3762,-257 3799,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_20628d0@0" ObjectIDZND0="42706@x" ObjectIDZND1="42703@x" Pin0InfoVect0LinkObjId="SW-260540_0" Pin0InfoVect1LinkObjId="EC-CX_DH.074Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20628d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3762,-235 3762,-257 3799,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2063640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,-323 3799,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="42706@0" ObjectIDZND0="g_20628d0@0" ObjectIDZND1="42703@x" Pin0InfoVect0LinkObjId="g_20628d0_0" Pin0InfoVect1LinkObjId="EC-CX_DH.074Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3799,-323 3799,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20638a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,-257 3799,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="42706@x" ObjectIDND1="g_20628d0@0" ObjectIDZND0="42703@0" Pin0InfoVect0LinkObjId="EC-CX_DH.074Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-260540_0" Pin1InfoVect1LinkObjId="g_20628d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3799,-257 3799,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2063ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3798,-521 3798,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6486@0" ObjectIDZND0="42705@1" Pin0InfoVect0LinkObjId="SW-260539_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20dbc90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3798,-521 3798,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20641e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3798,-445 3798,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42705@0" ObjectIDZND0="42704@1" Pin0InfoVect0LinkObjId="SW-260537_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-260539_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3798,-445 3798,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fea6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4259,-546 4259,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6521@0" ObjectIDZND0="6487@0" Pin0InfoVect0LinkObjId="g_1feaef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40279_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4259,-546 4259,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1feaef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4408,-539 4408,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28835@0" ObjectIDZND0="6487@0" Pin0InfoVect0LinkObjId="g_1fea6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-189775_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4408,-539 4408,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1feb700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-481 4308,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6514@1" ObjectIDZND0="6487@0" Pin0InfoVect0LinkObjId="g_1fea6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40191_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4308,-481 4308,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1febef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4473,-482 4473,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10979@1" ObjectIDZND0="6487@0" Pin0InfoVect0LinkObjId="g_1fea6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58384_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4473,-482 4473,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fec700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4647,-480 4647,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6518@1" ObjectIDZND0="6487@0" Pin0InfoVect0LinkObjId="g_1fea6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40249_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4647,-480 4647,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fecef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-481 4816,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10983@1" ObjectIDZND0="6487@0" Pin0InfoVect0LinkObjId="g_1fea6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58388_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-481 4816,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fed700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4977,-484 4977,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6524@1" ObjectIDZND0="6487@0" Pin0InfoVect0LinkObjId="g_1fea6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40309_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4977,-484 4977,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fedf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4831,-551 4831,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28833@0" ObjectIDZND0="6487@0" Pin0InfoVect0LinkObjId="g_1fea6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-189768_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4831,-551 4831,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ff0810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5239,-445 5239,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44742@0" ObjectIDZND0="44741@1" Pin0InfoVect0LinkObjId="SW-285208_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285209_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5239,-445 5239,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffa010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5239,-477 5239,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44742@1" ObjectIDZND0="6487@0" Pin0InfoVect0LinkObjId="g_1fea6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285209_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5239,-477 5239,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31aac60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5239,-397 5239,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="ellipse" ObjectIDND0="44741@0" ObjectIDZND0="44741@0" Pin0InfoVect0LinkObjId="SW-285208_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285208_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5239,-397 5239,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2993890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5215,-231 5206,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_29be8c0@0" ObjectIDZND0="g_2aa89f0@0" ObjectIDZND1="44743@x" Pin0InfoVect0LinkObjId="g_2aa89f0_0" Pin0InfoVect1LinkObjId="SW-285211_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29be8c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5215,-231 5206,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a8f5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5206,-231 5206,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_29be8c0@0" ObjectIDND1="44743@x" ObjectIDZND0="g_2aa89f0@0" Pin0InfoVect0LinkObjId="g_2aa89f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29be8c0_0" Pin1InfoVect1LinkObjId="SW-285211_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5206,-231 5206,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a6cd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5206,-227 5206,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_29be8c0@0" ObjectIDND1="g_2aa89f0@0" ObjectIDZND0="44743@1" Pin0InfoVect0LinkObjId="SW-285211_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29be8c0_0" Pin1InfoVect1LinkObjId="g_2aa89f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5206,-227 5206,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29889c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5206,-286 5206,-294 5239,-294 5239,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="44743@0" ObjectIDZND0="g_20813c0@1" Pin0InfoVect0LinkObjId="g_20813c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285211_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5206,-286 5206,-294 5239,-294 5239,-320 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3975,-1175 3994,-1175 3983,-1166 3975,-1174 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3975,-1157 3994,-1157 3983,-1166 3975,-1158 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5250,-300 5245,-302 5234,-290 5228,-294 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5247,-288 5247,-290 5239,-295 5238,-294 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5209,-213 5215,-213 5215,-208 " stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37329" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3404.000000 -1084.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5911" ObjectName="DYN-CX_DH"/>
     <cge:Meas_Ref ObjectId="37329"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_DH.073Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3938.000000 -107.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34369" ObjectName="EC-CX_DH.073Ld"/>
    <cge:TPSR_Ref TObjectID="34369"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DH.072Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4034.000000 -112.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34374" ObjectName="EC-CX_DH.072Ld"/>
    <cge:TPSR_Ref TObjectID="34374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DH.071Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4129.000000 -111.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34368" ObjectName="EC-CX_DH.071Ld"/>
    <cge:TPSR_Ref TObjectID="34368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DH.091Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4299.000000 -109.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34370" ObjectName="EC-CX_DH.091Ld"/>
    <cge:TPSR_Ref TObjectID="34370"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DH.092Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4464.000000 -108.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34371" ObjectName="EC-CX_DH.092Ld"/>
    <cge:TPSR_Ref TObjectID="34371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DH.093Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4638.000000 -106.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34372" ObjectName="EC-CX_DH.093Ld"/>
    <cge:TPSR_Ref TObjectID="34372"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DH.094Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4807.000000 -108.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34373" ObjectName="EC-CX_DH.094Ld"/>
    <cge:TPSR_Ref TObjectID="34373"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DH.074Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3790.000000 -106.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42703" ObjectName="EC-CX_DH.074Ld"/>
    <cge:TPSR_Ref TObjectID="42703"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_DH"/>
</svg>