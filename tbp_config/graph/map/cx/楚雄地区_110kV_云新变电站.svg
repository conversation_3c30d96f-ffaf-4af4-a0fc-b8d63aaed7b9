<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-35" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-54 -1035 2335 1261">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.7" x1="19" x2="10" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="25" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="16" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="20" x2="11" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="11" x2="2" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="11" x2="11" y1="9" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="66"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="25" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="16" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="11" x2="11" y1="9" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="11" x2="2" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="20" x2="11" y1="17" y2="8"/>
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="40"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape112">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.671329" x1="7" x2="7" y1="9" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="21" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.645996" x1="4" x2="10" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.457764" x1="6" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.19236" x1="13" x2="1" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="1" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="1" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="53" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="28" y2="20"/>
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="27"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape35">
    <circle cx="15" cy="21" fillStyle="0" r="6.5" stroke-width="0.45993"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="36" x2="37" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="35" x2="36" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="36" x2="36" y1="34" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="15" x2="36" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="36" x2="36" y1="16" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="31" x2="41" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="34" x2="38" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.212121" x1="35" x2="37" y1="2" y2="2"/>
    <rect height="14" stroke-width="0.571429" width="6" x="33" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="15" x2="15" y1="28" y2="43"/>
    <circle cx="7" cy="17" fillStyle="0" r="6.5" stroke-width="0.45993"/>
    <ellipse cx="14" cy="13" fillStyle="0" rx="6.5" ry="6" stroke-width="0.45993"/>
    <ellipse cx="21" cy="18" fillStyle="0" rx="6.5" ry="6" stroke-width="0.45993"/>
   </symbol>
   <symbol id="lightningRod:shape113">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.472899" x1="11" x2="11" y1="146" y2="152"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="20" x2="20" y1="2" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="2" x2="2" y1="2" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.274649" x1="2" x2="20" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="11" x2="11" y1="2" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="11" x2="11" y1="30" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="1" x2="21" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="1" x2="21" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="11" x2="11" y1="12" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="11" x2="11" y1="58" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="1" x2="21" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="1" x2="21" y1="58" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="11" x2="11" y1="40" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="11" x2="11" y1="85" y2="95"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="1" x2="21" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="1" x2="21" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="11" x2="11" y1="67" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="11" x2="11" y1="112" y2="122"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="1" x2="21" y1="104" y2="104"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="1" x2="21" y1="112" y2="112"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="11" x2="11" y1="94" y2="104"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.626154" x1="11" x2="11" y1="122" y2="137"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.683077" x1="2" x2="11" y1="137" y2="137"/>
    <polyline points="11,146 12,146 14,145 15,145 16,144 17,143 18,143 19,141 19,140 20,139 20,138 20,136 20,135 20,134 19,133 19,131 18,130 17,130 16,129 15,128 14,128 12,127 11,127 10,127 8,128 7,128 6,129 5,130 4,130 3,131 3,133 2,134 2,135 2,137 " stroke-width="0.201877"/>
   </symbol>
   <symbol id="lightningRod:shape115">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="48" x2="48" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="39" y1="135" y2="135"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="30" y1="4" y2="4"/>
    <circle cx="25" cy="4" fillStyle="0" r="4.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="21" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="15" y2="4"/>
    <polyline points="25,159 26,159 28,158 29,158 30,157 31,156 32,156 33,154 33,153 34,152 34,151 34,149 34,148 34,147 33,146 33,144 32,143 31,143 30,142 29,141 28,141 26,140 25,140 24,140 22,141 21,141 20,142 19,143 18,143 17,144 17,146 16,147 16,148 16,150 " stroke-width="0.201877"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.683077" x1="16" x2="25" y1="150" y2="150"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.626154" x1="25" x2="25" y1="135" y2="150"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="11" x2="11" y1="107" y2="117"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="1" x2="21" y1="125" y2="125"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="1" x2="21" y1="117" y2="117"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="11" x2="11" y1="125" y2="135"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="11" x2="11" y1="80" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="1" x2="21" y1="98" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="1" x2="21" y1="90" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="11" x2="11" y1="98" y2="108"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="11" x2="11" y1="53" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="1" x2="21" y1="71" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="1" x2="21" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="11" x2="11" y1="71" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="11" x2="11" y1="25" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="1" x2="21" y1="43" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="1" x2="21" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="11" x2="11" y1="43" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="11" x2="11" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.274649" x1="2" x2="20" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="2" x2="2" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="20" x2="20" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.472899" x1="25" x2="25" y1="159" y2="165"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="39" x2="39" y1="107" y2="117"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="29" x2="49" y1="125" y2="125"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="29" x2="49" y1="117" y2="117"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="39" x2="39" y1="125" y2="135"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="39" x2="39" y1="80" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="29" x2="49" y1="98" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="29" x2="49" y1="90" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="39" x2="39" y1="98" y2="108"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="39" x2="39" y1="53" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="29" x2="49" y1="71" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="29" x2="49" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="39" x2="39" y1="71" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="39" x2="39" y1="25" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="29" x2="49" y1="43" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="29" x2="49" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="39" x2="39" y1="43" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="39" x2="39" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.274649" x1="30" x2="48" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="30" x2="30" y1="15" y2="25"/>
   </symbol>
   <symbol id="lightningRod:shape199">
    <polyline DF8003:Layer="PUBLIC" points="50,52 56,65 43,65 50,52 50,53 50,52 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="69" y1="118" y2="118"/>
    <polyline DF8003:Layer="PUBLIC" points="69,103 63,90 76,90 69,103 69,102 69,103 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="69" x2="69" y1="118" y2="102"/>
    <polyline DF8003:Layer="PUBLIC" points="7,17 13,30 0,30 7,17 7,18 7,17 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="2" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.491815" x1="38" x2="38" y1="2" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.472899" x1="38" x2="38" y1="26" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.529647" x1="32" x2="38" y1="17" y2="17"/>
    <polyline points="39,26 39,26 40,25 41,25 42,24 43,23 44,23 44,21 45,20 45,19 45,18 45,16 45,15 45,14 45,13 44,11 44,10 43,10 42,9 41,8 40,8 39,7 39,7 38,7 37,8 36,8 35,9 34,10 33,10 33,11 32,13 32,14 32,15 32,17 " stroke-width="0.149389"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.491815" x1="37" x2="37" y1="100" y2="143"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.491815" x1="37" x2="37" y1="76" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.529647" x1="31" x2="37" y1="91" y2="91"/>
    <polyline points="38,100 38,100 39,99 40,99 41,98 42,97 43,97 43,95 44,94 44,93 44,92 44,90 44,89 44,88 44,87 43,85 43,84 42,84 41,83 40,82 39,82 38,81 38,81 37,81 36,82 35,82 34,83 33,84 32,84 32,85 31,87 31,88 31,89 31,91 " stroke-width="0.149389"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="49" x2="53" y1="54" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="26" y1="57" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="51" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="53" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="20" y1="53" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="46" x2="46" y1="53" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="55" x2="44" y1="53" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="50" x2="50" y1="53" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="50" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="53" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="32" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="50" x2="25" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="50" x2="50" y1="65" y2="76"/>
    <polyline DF8003:Layer="PUBLIC" points="25,53 19,40 32,40 25,53 25,52 25,53 "/>
   </symbol>
   <symbol id="lightningRod:shape198">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="34" x2="29" y1="20" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="30" x2="30" y1="23" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="17" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="21" x2="19" y1="18" y2="21"/>
    <rect height="18" stroke-width="0.84832" width="6" x="23" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="62" x2="62" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="35" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="23" x2="25" y1="32" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="26" x2="26" y1="30" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="25" y1="32" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="30" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="6" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="21" x2="19" y1="23" y2="21"/>
    <circle cx="29" cy="21" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="21" cy="20" fillStyle="0" rx="6" ry="6.5" stroke-width="0.431185"/>
    <circle cx="26" cy="28" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="34" x2="30" y1="20" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape120">
    <circle cx="12" cy="12" r="12.5" stroke-width="0.120929"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0433242" x1="18" x2="11" y1="34" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0433242" x1="11" x2="11" y1="39" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0433242" x1="18" x2="11" y1="34" y2="39"/>
    <ellipse cx="12" cy="32" rx="12.5" ry="12" stroke-width="0.120929"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0326126" x1="18" x2="13" y1="9" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0326126" x1="8" x2="13" y1="9" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0182374" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="lightningRod:shape196">
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="23" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="48" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="1" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="1" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="16" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.671329" x1="7" x2="7" y1="7" y2="11"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="54" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="58" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="61" y2="61"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="load:shape16">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,48 5,1 " stroke-width="3"/>
   </symbol>
   <symbol id="load:shape17">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,5 52,5 " stroke-width="3"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape13_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="35" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="57" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="88" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="88" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="35" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="34" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="42" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape1_0">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
   </symbol>
   <symbol id="transformer2:shape1_1">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape34">
    <polyline points="41,18 8,18 8,43 " stroke-width="1.3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="47" x2="42" y1="39" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="37" x2="42" y1="39" y2="36"/>
    <ellipse cx="42" cy="19" fillStyle="0" rx="10" ry="11" stroke-width="0.695459"/>
    <ellipse cx="40" cy="37" fillStyle="0" rx="9.5" ry="11" stroke-width="0.695459"/>
    <ellipse cx="54" cy="29" fillStyle="0" rx="10" ry="11" stroke-width="0.695459"/>
    <ellipse cx="28" cy="30" fillStyle="0" rx="9.5" ry="11" stroke-width="0.695459"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="50" x2="54" y1="30" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.32675" x1="55" x2="55" y1="26" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="59" x2="55" y1="30" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="37" x2="42" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.32675" x1="42" x2="42" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="47" x2="42" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.21447" x1="30" x2="34" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.21568" x1="30" x2="25" y1="32" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.17171" x1="25" x2="34" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.32675" x1="42" x2="42" y1="36" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.618687" x1="6" x2="10" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.789474" x1="0" x2="15" y1="43" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.789474" x1="5" x2="11" y1="47" y2="47"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1cbfaf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cc0c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1cc1630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1cc2940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1cc3ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1cc47c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cc5220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1cc5ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_18edb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_18edb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cc8ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cc8ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cca940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cca940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1ccb960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ccd380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ccdec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1cceda0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ccf680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cd0e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cd1960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cd20e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1cd28a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cd3980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cd4300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cd4df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1cd57b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1cd6c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1cd77c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1cd87f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1cd9430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ce7c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cdaab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1cdb770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1cdcc90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1271" width="2345" x="-59" y="-1040"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_120f1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 638.000000 994.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_fb5910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 627.000000 979.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1103b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 652.000000 964.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11bf800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 490.000000 994.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11bfa60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 479.000000 979.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11bfbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 504.000000 964.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11bfd40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1019.000000 995.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11bfeb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1008.000000 980.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11c0020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1033.000000 965.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12ca2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1310.000000 995.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12ca7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1299.000000 980.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12caa10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1324.000000 965.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1276980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1471.000000 995.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1276e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1460.000000 980.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12770d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1485.000000 965.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13923f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1159.000000 996.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1392a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1148.000000 981.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1392c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1173.000000 966.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 3.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_118c130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 341.000000 176.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_118c6e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 330.000000 161.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_118c920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 355.000000 146.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_144e8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1761.000000 406.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_144ed90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1750.000000 391.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_144efd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1775.000000 376.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14513a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1692.000000 996.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1451710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1681.000000 981.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1451950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1706.000000 966.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1451c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1794.000000 996.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1451ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1783.000000 981.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1452120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1808.000000 966.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1452450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1892.000000 996.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14526b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1881.000000 981.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14528f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1906.000000 966.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1452c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1996.000000 996.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1452e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1985.000000 981.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14530c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2010.000000 966.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1837040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2192.000000 1030.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18372a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2181.000000 1015.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18374e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2206.000000 1000.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="134" lineStyle="2" stroke="rgb(145,145,145)" stroke-dasharray="10 5 " stroke-width="1" width="218" x="493" y="-125"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="134" lineStyle="2" stroke="rgb(145,145,145)" stroke-dasharray="10 5 " stroke-width="1" width="98" x="759" y="-127"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="134" lineStyle="2" stroke="rgb(145,145,145)" stroke-dasharray="10 5 " stroke-width="1" width="90" x="943" y="-127"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="154" lineStyle="2" stroke="rgb(145,145,145)" stroke-dasharray="10 5 " stroke-width="1" width="215" x="665" y="64"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="154" lineStyle="2" stroke="rgb(145,145,145)" stroke-dasharray="10 5 " stroke-width="1" width="215" x="1387" y="66"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="135" lineStyle="2" stroke="rgb(145,145,145)" stroke-dasharray="10 5 " stroke-width="1" width="187" x="1237" y="-128"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="135" lineStyle="2" stroke="rgb(145,145,145)" stroke-dasharray="10 5 " stroke-width="1" width="204" x="1504" y="-154"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="-53" y="-383"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="-53" y="-863"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="-53" y="-983"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-29056">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 417.000000 -795.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4687" ObjectName="SW-CX_YX.CX_YX_10267SW"/>
     <cge:Meas_Ref ObjectId="29056"/>
    <cge:TPSR_Ref TObjectID="4687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29055">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 417.000000 -741.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4686" ObjectName="SW-CX_YX.CX_YX_10217SW"/>
     <cge:Meas_Ref ObjectId="29055"/>
    <cge:TPSR_Ref TObjectID="4686"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29054">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 464.000000 -814.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4685" ObjectName="SW-CX_YX.CX_YX_1026SW"/>
     <cge:Meas_Ref ObjectId="29054"/>
    <cge:TPSR_Ref TObjectID="4685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29053">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 489.000000 -680.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4684" ObjectName="SW-CX_YX.CX_YX_1022SW"/>
     <cge:Meas_Ref ObjectId="29053"/>
    <cge:TPSR_Ref TObjectID="4684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29052">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 438.000000 -684.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4683" ObjectName="SW-CX_YX.CX_YX_1021SW"/>
     <cge:Meas_Ref ObjectId="29052"/>
    <cge:TPSR_Ref TObjectID="4683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29043">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 607.000000 -856.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4674" ObjectName="SW-CX_YX.CX_YX_16267SW"/>
     <cge:Meas_Ref ObjectId="29043"/>
    <cge:TPSR_Ref TObjectID="4674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29042">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 605.000000 -797.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4673" ObjectName="SW-CX_YX.CX_YX_16260SW"/>
     <cge:Meas_Ref ObjectId="29042"/>
    <cge:TPSR_Ref TObjectID="4673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29041">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 604.000000 -743.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4672" ObjectName="SW-CX_YX.CX_YX_16217SW"/>
     <cge:Meas_Ref ObjectId="29041"/>
    <cge:TPSR_Ref TObjectID="4672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29040">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 651.000000 -808.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4671" ObjectName="SW-CX_YX.CX_YX_1626SW"/>
     <cge:Meas_Ref ObjectId="29040"/>
    <cge:TPSR_Ref TObjectID="4671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29038">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 625.000000 -684.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4669" ObjectName="SW-CX_YX.CX_YX_1621SW"/>
     <cge:Meas_Ref ObjectId="29038"/>
    <cge:TPSR_Ref TObjectID="4669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29039">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 676.000000 -683.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4670" ObjectName="SW-CX_YX.CX_YX_1622SW"/>
     <cge:Meas_Ref ObjectId="29039"/>
    <cge:TPSR_Ref TObjectID="4670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29072">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 749.000000 -761.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4703" ObjectName="SW-CX_YX.CX_YX_19017SW"/>
     <cge:Meas_Ref ObjectId="29072"/>
    <cge:TPSR_Ref TObjectID="4703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29071">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 750.000000 -693.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4702" ObjectName="SW-CX_YX.CX_YX_19010SW"/>
     <cge:Meas_Ref ObjectId="29071"/>
    <cge:TPSR_Ref TObjectID="4702"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29070">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 792.000000 -711.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4701" ObjectName="SW-CX_YX.CX_YX_1901SW"/>
     <cge:Meas_Ref ObjectId="29070"/>
    <cge:TPSR_Ref TObjectID="4701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29075">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 876.000000 -755.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4706" ObjectName="SW-CX_YX.CX_YX_19027SW"/>
     <cge:Meas_Ref ObjectId="29075"/>
    <cge:TPSR_Ref TObjectID="4706"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29074">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 877.000000 -687.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4705" ObjectName="SW-CX_YX.CX_YX_19020SW"/>
     <cge:Meas_Ref ObjectId="29074"/>
    <cge:TPSR_Ref TObjectID="4705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29073">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 919.000000 -705.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4704" ObjectName="SW-CX_YX.CX_YX_1902SW"/>
     <cge:Meas_Ref ObjectId="29073"/>
    <cge:TPSR_Ref TObjectID="4704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29036">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1002.000000 -858.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4667" ObjectName="SW-CX_YX.CX_YX_16167SW"/>
     <cge:Meas_Ref ObjectId="29036"/>
    <cge:TPSR_Ref TObjectID="4667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29035">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1000.000000 -795.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4666" ObjectName="SW-CX_YX.CX_YX_16160SW"/>
     <cge:Meas_Ref ObjectId="29035"/>
    <cge:TPSR_Ref TObjectID="4666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29034">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 999.000000 -745.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4665" ObjectName="SW-CX_YX.CX_YX_16117SW"/>
     <cge:Meas_Ref ObjectId="29034"/>
    <cge:TPSR_Ref TObjectID="4665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29033">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1046.000000 -808.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4664" ObjectName="SW-CX_YX.CX_YX_1616SW"/>
     <cge:Meas_Ref ObjectId="29033"/>
    <cge:TPSR_Ref TObjectID="4664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29031">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1020.000000 -686.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4662" ObjectName="SW-CX_YX.CX_YX_1611SW"/>
     <cge:Meas_Ref ObjectId="29031"/>
    <cge:TPSR_Ref TObjectID="4662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29032">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1071.000000 -685.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4663" ObjectName="SW-CX_YX.CX_YX_1612SW"/>
     <cge:Meas_Ref ObjectId="29032"/>
    <cge:TPSR_Ref TObjectID="4663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29063">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1311.000000 -794.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4694" ObjectName="SW-CX_YX.CX_YX_16360SW"/>
     <cge:Meas_Ref ObjectId="29063"/>
    <cge:TPSR_Ref TObjectID="4694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29062">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1310.000000 -744.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4693" ObjectName="SW-CX_YX.CX_YX_16317SW"/>
     <cge:Meas_Ref ObjectId="29062"/>
    <cge:TPSR_Ref TObjectID="4693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29061">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1357.000000 -808.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4692" ObjectName="SW-CX_YX.CX_YX_1636SW"/>
     <cge:Meas_Ref ObjectId="29061"/>
    <cge:TPSR_Ref TObjectID="4692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29059">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1331.000000 -685.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4690" ObjectName="SW-CX_YX.CX_YX_1631SW"/>
     <cge:Meas_Ref ObjectId="29059"/>
    <cge:TPSR_Ref TObjectID="4690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29060">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1382.000000 -684.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4691" ObjectName="SW-CX_YX.CX_YX_1632SW"/>
     <cge:Meas_Ref ObjectId="29060"/>
    <cge:TPSR_Ref TObjectID="4691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29049">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1137.000000 -801.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4680" ObjectName="SW-CX_YX.CX_YX_10167SW"/>
     <cge:Meas_Ref ObjectId="29049"/>
    <cge:TPSR_Ref TObjectID="4680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29048">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1137.000000 -743.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4679" ObjectName="SW-CX_YX.CX_YX_10117SW"/>
     <cge:Meas_Ref ObjectId="29048"/>
    <cge:TPSR_Ref TObjectID="4679"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29047">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1184.000000 -816.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4678" ObjectName="SW-CX_YX.CX_YX_1016SW"/>
     <cge:Meas_Ref ObjectId="29047"/>
    <cge:TPSR_Ref TObjectID="4678"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29046">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1209.000000 -682.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4677" ObjectName="SW-CX_YX.CX_YX_1012SW"/>
     <cge:Meas_Ref ObjectId="29046"/>
    <cge:TPSR_Ref TObjectID="4677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29045">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1158.000000 -686.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4676" ObjectName="SW-CX_YX.CX_YX_1011SW"/>
     <cge:Meas_Ref ObjectId="29045"/>
    <cge:TPSR_Ref TObjectID="4676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29069">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1448.000000 -796.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4700" ObjectName="SW-CX_YX.CX_YX_10467SW"/>
     <cge:Meas_Ref ObjectId="29069"/>
    <cge:TPSR_Ref TObjectID="4700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29068">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1447.000000 -746.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4699" ObjectName="SW-CX_YX.CX_YX_10417SW"/>
     <cge:Meas_Ref ObjectId="29068"/>
    <cge:TPSR_Ref TObjectID="4699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29067">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1494.000000 -810.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4698" ObjectName="SW-CX_YX.CX_YX_1046SW"/>
     <cge:Meas_Ref ObjectId="29067"/>
    <cge:TPSR_Ref TObjectID="4698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29065">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1468.000000 -687.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4696" ObjectName="SW-CX_YX.CX_YX_1041SW"/>
     <cge:Meas_Ref ObjectId="29065"/>
    <cge:TPSR_Ref TObjectID="4696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29066">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1519.000000 -686.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4697" ObjectName="SW-CX_YX.CX_YX_1042SW"/>
     <cge:Meas_Ref ObjectId="29066"/>
    <cge:TPSR_Ref TObjectID="4697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29127">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 395.000000 -208.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4756" ObjectName="SW-CX_YX.CX_YX_08767BK"/>
     <cge:Meas_Ref ObjectId="29127"/>
    <cge:TPSR_Ref TObjectID="4756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29125">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 486.000000 -209.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4754" ObjectName="SW-CX_YX.CX_YX_08667SW"/>
     <cge:Meas_Ref ObjectId="29125"/>
    <cge:TPSR_Ref TObjectID="4754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29123">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 570.000000 -207.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4752" ObjectName="SW-CX_YX.CX_YX_08567SW"/>
     <cge:Meas_Ref ObjectId="29123"/>
    <cge:TPSR_Ref TObjectID="4752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29121">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 662.000000 -207.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4750" ObjectName="SW-CX_YX.CX_YX_08467SW"/>
     <cge:Meas_Ref ObjectId="29121"/>
    <cge:TPSR_Ref TObjectID="4750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29119">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 752.000000 -208.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4748" ObjectName="SW-CX_YX.CX_YX_08367SW"/>
     <cge:Meas_Ref ObjectId="29119"/>
    <cge:TPSR_Ref TObjectID="4748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29117">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 842.000000 -206.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4746" ObjectName="SW-CX_YX.CX_YX_08267SW"/>
     <cge:Meas_Ref ObjectId="29117"/>
    <cge:TPSR_Ref TObjectID="4746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29115">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 933.000000 -205.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4744" ObjectName="SW-CX_YX.CX_YX_08167SW"/>
     <cge:Meas_Ref ObjectId="29115"/>
    <cge:TPSR_Ref TObjectID="4744"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 485.000000 -399.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5258" ObjectName="SW-CX_YX.CX_YX_00267SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="5258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1045.000000 -118.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5254" ObjectName="SW-CX_YX.CX_YX_F401SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="5254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1210.000000 -401.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5257" ObjectName="SW-CX_YX.CX_YX_00167SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="5257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29101">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1231.000000 -205.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4730" ObjectName="SW-CX_YX.CX_YX_06167SW"/>
     <cge:Meas_Ref ObjectId="29101"/>
    <cge:TPSR_Ref TObjectID="4730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29103">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1320.000000 -203.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4732" ObjectName="SW-CX_YX.CX_YX_06267SW"/>
     <cge:Meas_Ref ObjectId="29103"/>
    <cge:TPSR_Ref TObjectID="4732"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29105">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1408.000000 -203.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4734" ObjectName="SW-CX_YX.CX_YX_06367SW"/>
     <cge:Meas_Ref ObjectId="29105"/>
    <cge:TPSR_Ref TObjectID="4734"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29107">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1497.000000 -204.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4736" ObjectName="SW-CX_YX.CX_YX_06467SW"/>
     <cge:Meas_Ref ObjectId="29107"/>
    <cge:TPSR_Ref TObjectID="4736"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29109">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1585.000000 -202.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4738" ObjectName="SW-CX_YX.CX_YX_06567SW"/>
     <cge:Meas_Ref ObjectId="29109"/>
    <cge:TPSR_Ref TObjectID="4738"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29111">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1674.000000 -202.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4740" ObjectName="SW-CX_YX.CX_YX_06667SW"/>
     <cge:Meas_Ref ObjectId="29111"/>
    <cge:TPSR_Ref TObjectID="4740"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29113">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1762.000000 -202.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4742" ObjectName="SW-CX_YX.CX_YX_06767SW"/>
     <cge:Meas_Ref ObjectId="29113"/>
    <cge:TPSR_Ref TObjectID="4742"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29079">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1738.000000 -689.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4710" ObjectName="SW-CX_YX.CX_YX_1061SW"/>
     <cge:Meas_Ref ObjectId="29079"/>
    <cge:TPSR_Ref TObjectID="4710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29080">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1690.000000 -739.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4711" ObjectName="SW-CX_YX.CX_YX_10617SW"/>
     <cge:Meas_Ref ObjectId="29080"/>
    <cge:TPSR_Ref TObjectID="4711"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29085">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2033.000000 -686.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4716" ObjectName="SW-CX_YX.CX_YX_1091SW"/>
     <cge:Meas_Ref ObjectId="29085"/>
    <cge:TPSR_Ref TObjectID="4716"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29086">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1985.000000 -740.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4717" ObjectName="SW-CX_YX.CX_YX_10917SW"/>
     <cge:Meas_Ref ObjectId="29086"/>
    <cge:TPSR_Ref TObjectID="4717"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29083">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1934.000000 -688.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4714" ObjectName="SW-CX_YX.CX_YX_1081SW"/>
     <cge:Meas_Ref ObjectId="29083"/>
    <cge:TPSR_Ref TObjectID="4714"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29084">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1886.000000 -742.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4715" ObjectName="SW-CX_YX.CX_YX_10817SW"/>
     <cge:Meas_Ref ObjectId="29084"/>
    <cge:TPSR_Ref TObjectID="4715"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29081">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1831.000000 -690.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4712" ObjectName="SW-CX_YX.CX_YX_1071SW"/>
     <cge:Meas_Ref ObjectId="29081"/>
    <cge:TPSR_Ref TObjectID="4712"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29082">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1783.000000 -744.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4713" ObjectName="SW-CX_YX.CX_YX_10717SW"/>
     <cge:Meas_Ref ObjectId="29082"/>
    <cge:TPSR_Ref TObjectID="4713"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29077">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 1750.000000 -612.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4708" ObjectName="SW-CX_YX.CX_YX_1051SW"/>
     <cge:Meas_Ref ObjectId="29077"/>
    <cge:TPSR_Ref TObjectID="4708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29078">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1706.000000 -595.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4709" ObjectName="SW-CX_YX.CX_YX_10517SW"/>
     <cge:Meas_Ref ObjectId="29078"/>
    <cge:TPSR_Ref TObjectID="4709"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29076">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1786.000000 -509.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4707" ObjectName="SW-CX_YX.CX_YX_1050SW"/>
     <cge:Meas_Ref ObjectId="29076"/>
    <cge:TPSR_Ref TObjectID="4707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29091">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1939.000000 -260.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4720" ObjectName="SW-CX_YX.CX_YX_3621SW"/>
     <cge:Meas_Ref ObjectId="29091"/>
    <cge:TPSR_Ref TObjectID="4720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29093">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2000.000000 -259.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4722" ObjectName="SW-CX_YX.CX_YX_3622SW"/>
     <cge:Meas_Ref ObjectId="29093"/>
    <cge:TPSR_Ref TObjectID="4722"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29095">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2059.000000 -262.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4724" ObjectName="SW-CX_YX.CX_YX_3623SW"/>
     <cge:Meas_Ref ObjectId="29095"/>
    <cge:TPSR_Ref TObjectID="4724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29092">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1905.000000 -244.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4721" ObjectName="SW-CX_YX.CX_YX_36217SW"/>
     <cge:Meas_Ref ObjectId="29092"/>
    <cge:TPSR_Ref TObjectID="4721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29094">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 1967.000000 -240.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4723" ObjectName="SW-CX_YX.CX_YX_36227SW"/>
     <cge:Meas_Ref ObjectId="29094"/>
    <cge:TPSR_Ref TObjectID="4723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29096">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 2025.000000 -242.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4725" ObjectName="SW-CX_YX.CX_YX_36237SW"/>
     <cge:Meas_Ref ObjectId="29096"/>
    <cge:TPSR_Ref TObjectID="4725"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 616.000000 -69.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5238" ObjectName="SW-CX_YX.CX_YX_F085SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="5238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 797.000000 -76.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5253" ObjectName="SW-CX_YX.CX_YX_F0836SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="5253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 980.000000 -118.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5241" ObjectName="SW-CX_YX.CX_YX_F081SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="5241"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 708.000000 119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5242" ObjectName="SW-CX_YX.CX_YX_F084SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="5242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 816.000000 120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5243" ObjectName="SW-CX_YX.CX_YX_F066SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="5243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1402.000000 130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5255" ObjectName="SW-CX_YX.CX_YX_F082SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="5255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1276.000000 -72.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5245" ObjectName="SW-CX_YX.CX_YX_F061SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="5245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1365.000000 -74.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5244" ObjectName="SW-CX_YX.CX_YX_F062SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="5244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1631.000000 -93.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5252" ObjectName="SW-CX_YX.CX_YX_F0656SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="5252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.642857 -0.000000 0.000000 -0.586957 1457.000000 103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5250" ObjectName="SW-CX_YX.CX_YX_F063SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="5250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.642857 -0.000000 0.000000 -0.586957 1554.000000 103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5247" ObjectName="SW-CX_YX.CX_YX_F071SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="5247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.642857 -0.000000 0.000000 -0.586957 1512.000000 166.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5249" ObjectName="SW-CX_YX.CX_YX_F073SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="5249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.642857 -0.000000 0.000000 -0.586957 1512.000000 138.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5248" ObjectName="SW-CX_YX.CX_YX_F072SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="5248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29128">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.455696 2077.000000 -402.012658)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4757" ObjectName="SW-CX_YX.CX_YX_3011SW"/>
     <cge:Meas_Ref ObjectId="29128"/>
    <cge:TPSR_Ref TObjectID="4757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29050">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1117.000000 -494.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4681" ObjectName="SW-CX_YX.CX_YX_1010SW"/>
     <cge:Meas_Ref ObjectId="29050"/>
    <cge:TPSR_Ref TObjectID="4681"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29057">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 398.000000 -506.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4688" ObjectName="SW-CX_YX.CX_YX_1020SW"/>
     <cge:Meas_Ref ObjectId="29057"/>
    <cge:TPSR_Ref TObjectID="4688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1044.000000 -281.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221238">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 2187.000000 -852.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34574" ObjectName="SW-CX_YX.CX_YX_10367SW"/>
     <cge:Meas_Ref ObjectId="221238"/>
    <cge:TPSR_Ref TObjectID="34574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221235">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.891304 -0.000000 0.000000 -1.142857 2186.000000 -802.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34573" ObjectName="SW-CX_YX.CX_YX_10317SW"/>
     <cge:Meas_Ref ObjectId="221235"/>
    <cge:TPSR_Ref TObjectID="34573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221237">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2233.000000 -866.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34572" ObjectName="SW-CX_YX.CX_YX_1036SW"/>
     <cge:Meas_Ref ObjectId="221237"/>
    <cge:TPSR_Ref TObjectID="34572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221234">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2233.000000 -737.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34571" ObjectName="SW-CX_YX.CX_YX_1031SW"/>
     <cge:Meas_Ref ObjectId="221234"/>
    <cge:TPSR_Ref TObjectID="34571"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YX.CX_YX_1IIM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="391,-642 1589,-642 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4653" ObjectName="BS-CX_YX.CX_YX_1IIM"/>
    <cge:TPSR_Ref TObjectID="4653"/></metadata>
   <polyline fill="none" opacity="0" points="391,-642 1589,-642 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YX.CX_YX_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="391,-667 1589,-667 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4652" ObjectName="BS-CX_YX.CX_YX_1IM"/>
    <cge:TPSR_Ref TObjectID="4652"/></metadata>
   <polyline fill="none" opacity="0" points="391,-667 1589,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YX.CX_YX_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="423,-330 1139,-330 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4660" ObjectName="BS-CX_YX.CX_YX_9IIM"/>
    <cge:TPSR_Ref TObjectID="4660"/></metadata>
   <polyline fill="none" opacity="0" points="423,-330 1139,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YX.CX_YX_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1212,-331 1838,-331 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4659" ObjectName="BS-CX_YX.CX_YX_9IM"/>
    <cge:TPSR_Ref TObjectID="4659"/></metadata>
   <polyline fill="none" opacity="0" points="1212,-331 1838,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YX.CX_YX_1IIIM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1621,-668 2068,-668 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4658" ObjectName="BS-CX_YX.CX_YX_1IIIM"/>
    <cge:TPSR_Ref TObjectID="4658"/></metadata>
   <polyline fill="none" opacity="0" points="1621,-668 2068,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YX.CX_YX_GG">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1846,-429 2049,-429 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4654" ObjectName="BS-CX_YX.CX_YX_GG"/>
    <cge:TPSR_Ref TObjectID="4654"/></metadata>
   <polyline fill="none" opacity="0" points="1846,-429 2049,-429 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_YX.CX_YX_087LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 437.000000 -144.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15721" ObjectName="EC-CX_YX.CX_YX_087LD"/>
    <cge:TPSR_Ref TObjectID="15721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YX.067LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1807.000000 -125.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49440" ObjectName="EC-CX_YX.067LD"/>
    <cge:TPSR_Ref TObjectID="49440"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YX.CX_YX_065LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1634.000000 -23.000000)" xlink:href="#load:shape16"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22570" ObjectName="EC-CX_YX.CX_YX_065LD"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YX.CX_YX_F071LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1650.000000 53.000000)" xlink:href="#load:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22572" ObjectName="EC-CX_YX.CX_YX_F071LD"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2251.000000 -934.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YX.086LD">
    <use class="BKBV-10KV" transform="matrix(0.611111 -0.000000 0.000000 -0.750000 532.000000 -26.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49430" ObjectName="EC-CX_YX.086LD"/>
    <cge:TPSR_Ref TObjectID="49430"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YX.085LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 616.000000 -115.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49431" ObjectName="EC-CX_YX.085LD"/>
    <cge:TPSR_Ref TObjectID="49431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YX.084Ld">
    <use class="BKBV-10KV" transform="matrix(0.666667 -0.000000 0.000000 -0.875000 711.000000 72.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49432" ObjectName="EC-CX_YX.084Ld"/>
    <cge:TPSR_Ref TObjectID="49432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YX.083LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 798.000000 -123.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49433" ObjectName="EC-CX_YX.083LD"/>
    <cge:TPSR_Ref TObjectID="49433"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YX.081LD">
    <use class="BKBV-10KV" transform="matrix(0.666667 -0.000000 0.000000 -0.656250 983.000000 -64.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49434" ObjectName="EC-CX_YX.081LD"/>
    <cge:TPSR_Ref TObjectID="49434"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YX.062LD">
    <use class="BKBV-10KV" transform="matrix(0.722222 -0.000000 0.000000 -0.625000 1368.000000 -123.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49441" ObjectName="EC-CX_YX.062LD"/>
    <cge:TPSR_Ref TObjectID="49441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YX.061LD">
    <use class="BKBV-10KV" transform="matrix(0.722222 -0.000000 0.000000 -0.625000 1279.000000 -123.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49439" ObjectName="EC-CX_YX.061LD"/>
    <cge:TPSR_Ref TObjectID="49439"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YX.064Ld">
    <use class="BKBV-10KV" transform="matrix(0.722222 -0.000000 0.000000 -0.625000 1544.000000 -112.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49437" ObjectName="EC-CX_YX.064Ld"/>
    <cge:TPSR_Ref TObjectID="49437"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YX.065LD">
    <use class="BKBV-10KV" transform="matrix(0.722222 -0.000000 0.000000 -0.625000 1633.000000 -139.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49436" ObjectName="EC-CX_YX.065LD"/>
    <cge:TPSR_Ref TObjectID="49436"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YX.063LD">
    <use class="BKBV-10KV" transform="matrix(0.722222 -0.000000 0.000000 -0.625000 1456.000000 74.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49438" ObjectName="EC-CX_YX.063LD"/>
    <cge:TPSR_Ref TObjectID="49438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YX.066LD">
    <use class="BKBV-10KV" transform="matrix(0.722222 -0.000000 0.000000 -0.625000 819.000000 73.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49435" ObjectName="EC-CX_YX.066LD"/>
    <cge:TPSR_Ref TObjectID="49435"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YX.082LD">
    <use class="BKBV-10KV" transform="matrix(0.722222 -0.000000 0.000000 -0.625000 1405.000000 85.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49442" ObjectName="EC-CX_YX.082LD"/>
    <cge:TPSR_Ref TObjectID="49442"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_f8be20" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 393.000000 -806.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1245f20" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 393.000000 -752.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1246740" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 579.000000 -867.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1244130" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 581.000000 -808.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1244950" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 580.000000 -754.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1245170" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 725.000000 -772.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1261d60" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 726.000000 -704.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12f2a40" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 852.000000 -766.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12f32f0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 853.000000 -698.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_122ee10" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 974.000000 -869.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_122f850" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 976.000000 -806.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12b4070" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 975.000000 -756.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12dc860" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1287.000000 -805.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12dd340" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1286.000000 -755.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1314410" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1113.000000 -812.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13150a0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1113.000000 -754.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1277f70" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1424.000000 -807.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1278b50" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1423.000000 -757.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_127c900" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 389.000000 -180.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_129cc10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 480.000000 -182.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1152b20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 568.000000 -181.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1256890" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 660.000000 -181.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11966a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 750.000000 -183.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11d8390" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 840.000000 -180.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11dd9b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 931.000000 -179.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_113da80" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 456.000000 -410.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1247ed0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 1048.000000 -92.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_124b0b0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1182.000000 -412.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1284c00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 1229.000000 -179.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_128a360" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 1318.000000 -177.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1145920" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 1406.000000 -177.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_114b080" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 1495.000000 -178.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11fa6c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 1583.000000 -176.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11ffbc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 1672.000000 -175.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1205320" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 1760.000000 -176.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11b5cc0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1666.000000 -750.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12374c0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1961.000000 -751.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1240320" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1862.000000 -753.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11a0de0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1759.000000 -755.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11a6140" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1679.138299 -606.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11a9870" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 1789.000000 -486.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1385320" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 1897.000000 -218.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1388500" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 1961.000000 -214.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_138b6e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 2019.000000 -215.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11876a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 575.000000 -370.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_118a600" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 1300.000000 -375.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14e9470" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1120.000000 -468.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14f0170" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 402.000000 -482.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1502e40" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2163.000000 -863.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15039d0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2162.000000 -813.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_1292580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="473,-805 473,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4682@x" ObjectIDND1="4687@x" ObjectIDZND0="4685@0" Pin0InfoVect0LinkObjId="SW-29054_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29051_0" Pin1InfoVect1LinkObjId="SW-29056_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="473,-805 473,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1251100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="473,-790 473,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4682@1" ObjectIDZND0="4685@x" ObjectIDZND1="4687@x" Pin0InfoVect0LinkObjId="SW-29054_0" Pin0InfoVect1LinkObjId="SW-29056_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29051_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="473,-790 473,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12512f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="457,-800 473,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4687@1" ObjectIDZND0="4682@x" ObjectIDZND1="4685@x" Pin0InfoVect0LinkObjId="SW-29051_0" Pin0InfoVect1LinkObjId="SW-29054_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29056_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="457,-800 473,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12514e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="457,-746 473,-746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="4686@1" ObjectIDZND0="4684@x" ObjectIDZND1="4683@x" ObjectIDZND2="4682@x" Pin0InfoVect0LinkObjId="SW-29053_0" Pin0InfoVect1LinkObjId="SW-29052_0" Pin0InfoVect2LinkObjId="SW-29051_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29055_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="457,-746 473,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12516d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="643,-802 660,-802 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="4673@1" ObjectIDZND0="4671@x" ObjectIDZND1="4668@x" Pin0InfoVect0LinkObjId="SW-29040_0" Pin0InfoVect1LinkObjId="SW-29037_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29042_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="643,-802 660,-802 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1251e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="660,-802 660,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4673@x" ObjectIDND1="4671@x" ObjectIDZND0="4668@1" Pin0InfoVect0LinkObjId="SW-29037_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29042_0" Pin1InfoVect1LinkObjId="SW-29040_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="660,-802 660,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1252080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="643,-748 660,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="4672@1" ObjectIDZND0="4669@x" ObjectIDZND1="4670@x" ObjectIDZND2="4668@x" Pin0InfoVect0LinkObjId="SW-29038_0" Pin0InfoVect1LinkObjId="SW-29039_0" Pin0InfoVect2LinkObjId="SW-29037_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29041_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="643,-748 660,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1252840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="660,-735 660,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="4669@x" ObjectIDND1="4670@x" ObjectIDZND0="4672@x" ObjectIDZND1="4668@x" Pin0InfoVect0LinkObjId="SW-29041_0" Pin0InfoVect1LinkObjId="SW-29037_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29038_0" Pin1InfoVect1LinkObjId="SW-29039_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="660,-735 660,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1252a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="660,-748 660,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="4669@x" ObjectIDND1="4670@x" ObjectIDND2="4672@x" ObjectIDZND0="4668@0" Pin0InfoVect0LinkObjId="SW-29037_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29038_0" Pin1InfoVect1LinkObjId="SW-29039_0" Pin1InfoVect2LinkObjId="SW-29041_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="660,-748 660,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1252c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="790,-766 801,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="4703@1" ObjectIDZND0="g_148be70@0" ObjectIDZND1="g_14899b0@0" ObjectIDZND2="4701@x" Pin0InfoVect0LinkObjId="g_148be70_0" Pin0InfoVect1LinkObjId="g_14899b0_0" Pin0InfoVect2LinkObjId="SW-29070_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29072_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="790,-766 801,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1252e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,-698 801,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="4702@1" ObjectIDZND0="4652@0" ObjectIDZND1="4701@x" Pin0InfoVect0LinkObjId="g_12535d0_0" Pin0InfoVect1LinkObjId="SW-29070_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29071_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="791,-698 801,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12535d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-698 801,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="4701@x" ObjectIDND1="4702@x" ObjectIDZND0="4652@0" Pin0InfoVect0LinkObjId="g_1252e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29070_0" Pin1InfoVect1LinkObjId="SW-29071_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="801,-698 801,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12920c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="410,-800 421,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_f8be20@0" ObjectIDZND0="4687@0" Pin0InfoVect0LinkObjId="SW-29056_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_f8be20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="410,-800 421,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1245d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="410,-746 421,-746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1245f20@0" ObjectIDZND0="4686@0" Pin0InfoVect0LinkObjId="SW-29055_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1245f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="410,-746 421,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1246550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="596,-861 607,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1246740@0" ObjectIDZND0="4674@0" Pin0InfoVect0LinkObjId="SW-29043_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1246740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="596,-861 607,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1243f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="598,-802 609,-802 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1244130@0" ObjectIDZND0="4673@0" Pin0InfoVect0LinkObjId="SW-29042_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1244130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="598,-802 609,-802 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1244760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="597,-748 608,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1244950@0" ObjectIDZND0="4672@0" Pin0InfoVect0LinkObjId="SW-29041_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1244950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="597,-748 608,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1244f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="742,-766 753,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1245170@0" ObjectIDZND0="4703@0" Pin0InfoVect0LinkObjId="SW-29072_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1245170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="742,-766 753,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1262390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-698 754,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1261d60@0" ObjectIDZND0="4702@0" Pin0InfoVect0LinkObjId="SW-29071_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1261d60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="743,-698 754,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1262580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-667 447,-689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4652@0" ObjectIDZND0="4683@0" Pin0InfoVect0LinkObjId="SW-29052_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1252e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="447,-667 447,-689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1262770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-725 447,-735 473,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="4683@1" ObjectIDZND0="4682@x" ObjectIDZND1="4686@x" ObjectIDZND2="4684@x" Pin0InfoVect0LinkObjId="SW-29051_0" Pin0InfoVect1LinkObjId="SW-29055_0" Pin0InfoVect2LinkObjId="SW-29053_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29052_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="447,-725 447,-735 473,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1262960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="498,-642 498,-685 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4653@0" ObjectIDZND0="4684@0" Pin0InfoVect0LinkObjId="SW-29053_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12f23e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="498,-642 498,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1262b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="498,-721 498,-735 473,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="4684@1" ObjectIDZND0="4682@x" ObjectIDZND1="4686@x" ObjectIDZND2="4683@x" Pin0InfoVect0LinkObjId="SW-29051_0" Pin0InfoVect1LinkObjId="SW-29055_0" Pin0InfoVect2LinkObjId="SW-29052_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29053_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="498,-721 498,-735 473,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1262d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="634,-667 634,-689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4652@0" ObjectIDZND0="4669@0" Pin0InfoVect0LinkObjId="SW-29038_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1252e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="634,-667 634,-689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1262f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="634,-725 634,-735 660,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="4669@1" ObjectIDZND0="4672@x" ObjectIDZND1="4668@x" ObjectIDZND2="4670@x" Pin0InfoVect0LinkObjId="SW-29041_0" Pin0InfoVect1LinkObjId="SW-29037_0" Pin0InfoVect2LinkObjId="SW-29039_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29038_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="634,-725 634,-735 660,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1263120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="685,-642 685,-688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4653@0" ObjectIDZND0="4670@0" Pin0InfoVect0LinkObjId="SW-29039_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12f23e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="685,-642 685,-688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1263310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="685,-724 685,-735 660,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="4670@1" ObjectIDZND0="4672@x" ObjectIDZND1="4668@x" ObjectIDZND2="4669@x" Pin0InfoVect0LinkObjId="SW-29041_0" Pin0InfoVect1LinkObjId="SW-29037_0" Pin0InfoVect2LinkObjId="SW-29038_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29039_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="685,-724 685,-735 660,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1263500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="660,-802 660,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="4673@x" ObjectIDND1="4668@x" ObjectIDZND0="4671@0" Pin0InfoVect0LinkObjId="SW-29040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29042_0" Pin1InfoVect1LinkObjId="SW-29037_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="660,-802 660,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_11be760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-766 801,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="4701@x" ObjectIDND1="4703@x" ObjectIDZND0="g_148be70@0" ObjectIDZND1="g_14899b0@0" Pin0InfoVect0LinkObjId="g_148be70_0" Pin0InfoVect1LinkObjId="g_14899b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29070_0" Pin1InfoVect1LinkObjId="SW-29072_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="801,-766 801,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_11be950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-698 801,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4652@0" ObjectIDND1="4702@x" ObjectIDZND0="4701@0" Pin0InfoVect0LinkObjId="SW-29070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1252e10_0" Pin1InfoVect1LinkObjId="SW-29071_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="801,-698 801,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_11beb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-752 801,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="4701@1" ObjectIDZND0="g_148be70@0" ObjectIDZND1="g_14899b0@0" ObjectIDZND2="4703@x" Pin0InfoVect0LinkObjId="g_148be70_0" Pin0InfoVect1LinkObjId="g_14899b0_0" Pin0InfoVect2LinkObjId="SW-29072_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29070_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="801,-752 801,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_11bf420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="473,-746 473,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4682@x" ObjectIDND1="4686@x" ObjectIDZND0="4684@x" ObjectIDZND1="4683@x" Pin0InfoVect0LinkObjId="SW-29053_0" Pin0InfoVect1LinkObjId="SW-29052_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29051_0" Pin1InfoVect1LinkObjId="SW-29055_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="473,-746 473,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_11bf610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="473,-763 473,-746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="4682@0" ObjectIDZND0="4684@x" ObjectIDZND1="4683@x" ObjectIDZND2="4686@x" Pin0InfoVect0LinkObjId="SW-29053_0" Pin0InfoVect1LinkObjId="SW-29052_0" Pin0InfoVect2LinkObjId="SW-29055_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29051_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="473,-763 473,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12f21c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="917,-760 928,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="4706@1" ObjectIDZND0="g_148fe30@0" ObjectIDZND1="g_148d970@0" ObjectIDZND2="4704@x" Pin0InfoVect0LinkObjId="g_148fe30_0" Pin0InfoVect1LinkObjId="g_148d970_0" Pin0InfoVect2LinkObjId="SW-29073_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29075_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="917,-760 928,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12f23e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-692 928,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="4705@1" ObjectIDZND0="4653@0" ObjectIDZND1="4704@x" Pin0InfoVect0LinkObjId="g_12f2600_0" Pin0InfoVect1LinkObjId="SW-29073_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29074_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="918,-692 928,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12f2600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="928,-692 928,-642 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="4704@x" ObjectIDND1="4705@x" ObjectIDZND0="4653@0" Pin0InfoVect0LinkObjId="g_12f23e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29073_0" Pin1InfoVect1LinkObjId="SW-29074_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="928,-692 928,-642 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12f2820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="869,-760 880,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_12f2a40@0" ObjectIDZND0="4706@0" Pin0InfoVect0LinkObjId="SW-29075_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12f2a40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="869,-760 880,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12f3be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="870,-692 881,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_12f32f0@0" ObjectIDZND0="4705@0" Pin0InfoVect0LinkObjId="SW-29074_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12f32f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="870,-692 881,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12f3e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="928,-760 928,-797 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="4704@x" ObjectIDND1="4706@x" ObjectIDZND0="g_148fe30@0" ObjectIDZND1="g_148d970@0" Pin0InfoVect0LinkObjId="g_148fe30_0" Pin0InfoVect1LinkObjId="g_148d970_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29073_0" Pin1InfoVect1LinkObjId="SW-29075_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="928,-760 928,-797 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12f4020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="928,-692 928,-710 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4653@0" ObjectIDND1="4705@x" ObjectIDZND0="4704@0" Pin0InfoVect0LinkObjId="SW-29073_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_12f23e0_0" Pin1InfoVect1LinkObjId="SW-29074_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="928,-692 928,-710 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12f4240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="928,-746 928,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="4704@1" ObjectIDZND0="g_148fe30@0" ObjectIDZND1="g_148d970@0" ObjectIDZND2="4706@x" Pin0InfoVect0LinkObjId="g_148fe30_0" Pin0InfoVect1LinkObjId="g_148d970_0" Pin0InfoVect2LinkObjId="SW-29075_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29073_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="928,-746 928,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_122e270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1038,-800 1055,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="4666@1" ObjectIDZND0="4664@x" ObjectIDZND1="4661@x" Pin0InfoVect0LinkObjId="SW-29033_0" Pin0InfoVect1LinkObjId="SW-29030_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29035_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1038,-800 1055,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_122e460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1055,-800 1055,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4666@x" ObjectIDND1="4664@x" ObjectIDZND0="4661@1" Pin0InfoVect0LinkObjId="SW-29030_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29035_0" Pin1InfoVect1LinkObjId="SW-29033_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1055,-800 1055,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_122e650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1038,-750 1055,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="4665@1" ObjectIDZND0="4662@x" ObjectIDZND1="4663@x" ObjectIDZND2="4661@x" Pin0InfoVect0LinkObjId="SW-29031_0" Pin0InfoVect1LinkObjId="SW-29032_0" Pin0InfoVect2LinkObjId="SW-29030_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29034_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1038,-750 1055,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_122e840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1055,-737 1055,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="4662@x" ObjectIDND1="4663@x" ObjectIDZND0="4665@x" ObjectIDZND1="4661@x" Pin0InfoVect0LinkObjId="SW-29034_0" Pin0InfoVect1LinkObjId="SW-29030_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29031_0" Pin1InfoVect1LinkObjId="SW-29032_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1055,-737 1055,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_122ea30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1055,-750 1055,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="4662@x" ObjectIDND1="4663@x" ObjectIDND2="4665@x" ObjectIDZND0="4661@0" Pin0InfoVect0LinkObjId="SW-29030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29031_0" Pin1InfoVect1LinkObjId="SW-29032_0" Pin1InfoVect2LinkObjId="SW-29034_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1055,-750 1055,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_122ec20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="991,-863 1002,-863 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_122ee10@0" ObjectIDZND0="4667@0" Pin0InfoVect0LinkObjId="SW-29036_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_122ee10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="991,-863 1002,-863 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_122f630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="993,-800 1004,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_122f850@0" ObjectIDZND0="4666@0" Pin0InfoVect0LinkObjId="SW-29035_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_122f850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="993,-800 1004,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1230140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,-750 1003,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_12b4070@0" ObjectIDZND0="4665@0" Pin0InfoVect0LinkObjId="SW-29034_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12b4070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="992,-750 1003,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12b4960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1029,-667 1029,-691 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4652@0" ObjectIDZND0="4662@0" Pin0InfoVect0LinkObjId="SW-29031_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1252e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1029,-667 1029,-691 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12b4b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1029,-727 1029,-737 1055,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="4662@1" ObjectIDZND0="4665@x" ObjectIDZND1="4661@x" ObjectIDZND2="4663@x" Pin0InfoVect0LinkObjId="SW-29034_0" Pin0InfoVect1LinkObjId="SW-29030_0" Pin0InfoVect2LinkObjId="SW-29032_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29031_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1029,-727 1029,-737 1055,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12b4da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1080,-642 1080,-690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4653@0" ObjectIDZND0="4663@0" Pin0InfoVect0LinkObjId="SW-29032_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12f23e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1080,-642 1080,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12b4fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1080,-726 1080,-737 1055,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="4663@1" ObjectIDZND0="4665@x" ObjectIDZND1="4661@x" ObjectIDZND2="4662@x" Pin0InfoVect0LinkObjId="SW-29034_0" Pin0InfoVect1LinkObjId="SW-29030_0" Pin0InfoVect2LinkObjId="SW-29031_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29032_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1080,-726 1080,-737 1055,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12b51e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1055,-800 1055,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="4666@x" ObjectIDND1="4661@x" ObjectIDZND0="4664@0" Pin0InfoVect0LinkObjId="SW-29033_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29035_0" Pin1InfoVect1LinkObjId="SW-29030_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1055,-800 1055,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12cafb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1349,-799 1366,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4694@1" ObjectIDZND0="4689@x" ObjectIDZND1="4692@x" Pin0InfoVect0LinkObjId="SW-29058_0" Pin0InfoVect1LinkObjId="SW-29061_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29063_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1349,-799 1366,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12cb1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1366,-799 1366,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4692@x" ObjectIDND1="4694@x" ObjectIDZND0="4689@1" Pin0InfoVect0LinkObjId="SW-29058_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29061_0" Pin1InfoVect1LinkObjId="SW-29063_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1366,-799 1366,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12dc000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1349,-749 1366,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="4693@1" ObjectIDZND0="4690@x" ObjectIDZND1="4691@x" ObjectIDZND2="4689@x" Pin0InfoVect0LinkObjId="SW-29059_0" Pin0InfoVect1LinkObjId="SW-29060_0" Pin0InfoVect2LinkObjId="SW-29058_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29062_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1349,-749 1366,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12dc1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1366,-736 1366,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4690@x" ObjectIDND1="4691@x" ObjectIDZND0="4689@x" ObjectIDZND1="4693@x" Pin0InfoVect0LinkObjId="SW-29058_0" Pin0InfoVect1LinkObjId="SW-29062_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29059_0" Pin1InfoVect1LinkObjId="SW-29060_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1366,-736 1366,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12dc400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1366,-749 1366,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="4690@x" ObjectIDND1="4691@x" ObjectIDND2="4693@x" ObjectIDZND0="4689@0" Pin0InfoVect0LinkObjId="SW-29058_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29059_0" Pin1InfoVect1LinkObjId="SW-29060_0" Pin1InfoVect2LinkObjId="SW-29062_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1366,-749 1366,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12dc630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1304,-799 1315,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_12dc860@0" ObjectIDZND0="4694@0" Pin0InfoVect0LinkObjId="SW-29063_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12dc860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1304,-799 1315,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12dd0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1303,-749 1314,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_12dd340@0" ObjectIDZND0="4693@0" Pin0InfoVect0LinkObjId="SW-29062_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12dd340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1303,-749 1314,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12ddd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1340,-667 1340,-690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4652@0" ObjectIDZND0="4690@0" Pin0InfoVect0LinkObjId="SW-29059_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1252e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1340,-667 1340,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12ddfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1340,-726 1340,-736 1366,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="4690@1" ObjectIDZND0="4689@x" ObjectIDZND1="4693@x" ObjectIDZND2="4691@x" Pin0InfoVect0LinkObjId="SW-29058_0" Pin0InfoVect1LinkObjId="SW-29062_0" Pin0InfoVect2LinkObjId="SW-29060_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29059_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1340,-726 1340,-736 1366,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12de230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1391,-642 1391,-689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4653@0" ObjectIDZND0="4691@0" Pin0InfoVect0LinkObjId="SW-29060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12f23e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1391,-642 1391,-689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12de490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1391,-725 1391,-736 1366,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="4691@1" ObjectIDZND0="4689@x" ObjectIDZND1="4693@x" ObjectIDZND2="4690@x" Pin0InfoVect0LinkObjId="SW-29058_0" Pin0InfoVect1LinkObjId="SW-29062_0" Pin0InfoVect2LinkObjId="SW-29059_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29060_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1391,-725 1391,-736 1366,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12de6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1366,-799 1366,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4689@x" ObjectIDND1="4694@x" ObjectIDZND0="4692@0" Pin0InfoVect0LinkObjId="SW-29061_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29058_0" Pin1InfoVect1LinkObjId="SW-29063_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1366,-799 1366,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1313830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-806 1193,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4675@x" ObjectIDND1="4680@x" ObjectIDZND0="4678@0" Pin0InfoVect0LinkObjId="SW-29047_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29044_0" Pin1InfoVect1LinkObjId="SW-29049_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-806 1193,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1313a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-792 1193,-806 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4675@1" ObjectIDZND0="4678@x" ObjectIDZND1="4680@x" Pin0InfoVect0LinkObjId="SW-29047_0" Pin0InfoVect1LinkObjId="SW-29049_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29044_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-792 1193,-806 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1313cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1177,-806 1193,-806 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4680@1" ObjectIDZND0="4675@x" ObjectIDZND1="4678@x" Pin0InfoVect0LinkObjId="SW-29044_0" Pin0InfoVect1LinkObjId="SW-29047_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29049_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1177,-806 1193,-806 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1313f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1177,-748 1193,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="4679@1" ObjectIDZND0="4677@x" ObjectIDZND1="4676@x" ObjectIDZND2="4675@x" Pin0InfoVect0LinkObjId="SW-29046_0" Pin0InfoVect1LinkObjId="SW-29045_0" Pin0InfoVect2LinkObjId="SW-29044_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29048_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1177,-748 1193,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_13141b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1130,-806 1141,-806 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1314410@0" ObjectIDZND0="4680@0" Pin0InfoVect0LinkObjId="SW-29049_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1314410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1130,-806 1141,-806 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1314e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1130,-748 1141,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_13150a0@0" ObjectIDZND0="4679@0" Pin0InfoVect0LinkObjId="SW-29048_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13150a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1130,-748 1141,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1315ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1167,-667 1167,-691 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4652@0" ObjectIDZND0="4676@0" Pin0InfoVect0LinkObjId="SW-29045_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1252e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1167,-667 1167,-691 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12d7c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1167,-727 1167,-737 1193,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="4676@1" ObjectIDZND0="4675@x" ObjectIDZND1="4679@x" ObjectIDZND2="4677@x" Pin0InfoVect0LinkObjId="SW-29044_0" Pin0InfoVect1LinkObjId="SW-29048_0" Pin0InfoVect2LinkObjId="SW-29046_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29045_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1167,-727 1167,-737 1193,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12d7ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1218,-642 1218,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4653@0" ObjectIDZND0="4677@0" Pin0InfoVect0LinkObjId="SW-29046_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12f23e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1218,-642 1218,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12d8100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1218,-723 1218,-737 1193,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="4677@1" ObjectIDZND0="4675@x" ObjectIDZND1="4679@x" ObjectIDZND2="4676@x" Pin0InfoVect0LinkObjId="SW-29044_0" Pin0InfoVect1LinkObjId="SW-29048_0" Pin0InfoVect2LinkObjId="SW-29045_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29046_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1218,-723 1218,-737 1193,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12d8360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-748 1193,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4675@x" ObjectIDND1="4679@x" ObjectIDZND0="4677@x" ObjectIDZND1="4676@x" Pin0InfoVect0LinkObjId="SW-29046_0" Pin0InfoVect1LinkObjId="SW-29045_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29044_0" Pin1InfoVect1LinkObjId="SW-29048_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-748 1193,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12d85c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-765 1193,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="4675@0" ObjectIDZND0="4677@x" ObjectIDZND1="4676@x" ObjectIDZND2="4679@x" Pin0InfoVect0LinkObjId="SW-29046_0" Pin0InfoVect1LinkObjId="SW-29045_0" Pin0InfoVect2LinkObjId="SW-29048_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29044_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-765 1193,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1277310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1486,-801 1503,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="4700@1" ObjectIDZND0="4698@x" ObjectIDZND1="4695@x" Pin0InfoVect0LinkObjId="SW-29067_0" Pin0InfoVect1LinkObjId="SW-29064_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29069_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1486,-801 1503,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1277500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1503,-801 1503,-789 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4698@x" ObjectIDND1="4700@x" ObjectIDZND0="4695@1" Pin0InfoVect0LinkObjId="SW-29064_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29067_0" Pin1InfoVect1LinkObjId="SW-29069_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1503,-801 1503,-789 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12776f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1486,-751 1503,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="4699@1" ObjectIDZND0="4695@x" ObjectIDZND1="4696@x" ObjectIDZND2="4697@x" Pin0InfoVect0LinkObjId="SW-29064_0" Pin0InfoVect1LinkObjId="SW-29065_0" Pin0InfoVect2LinkObjId="SW-29066_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29068_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1486,-751 1503,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12778e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1503,-738 1503,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4696@x" ObjectIDND1="4697@x" ObjectIDZND0="4695@x" ObjectIDZND1="4699@x" Pin0InfoVect0LinkObjId="SW-29064_0" Pin0InfoVect1LinkObjId="SW-29068_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29065_0" Pin1InfoVect1LinkObjId="SW-29066_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1503,-738 1503,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1277b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1503,-751 1503,-762 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="4696@x" ObjectIDND1="4697@x" ObjectIDND2="4699@x" ObjectIDZND0="4695@0" Pin0InfoVect0LinkObjId="SW-29064_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29065_0" Pin1InfoVect1LinkObjId="SW-29066_0" Pin1InfoVect2LinkObjId="SW-29068_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1503,-751 1503,-762 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1277d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-801 1452,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1277f70@0" ObjectIDZND0="4700@0" Pin0InfoVect0LinkObjId="SW-29069_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1277f70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-801 1452,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12788f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1440,-751 1451,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1278b50@0" ObjectIDZND0="4699@0" Pin0InfoVect0LinkObjId="SW-29068_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1278b50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1440,-751 1451,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12795a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1477,-667 1477,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4652@0" ObjectIDZND0="4696@0" Pin0InfoVect0LinkObjId="SW-29065_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1252e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1477,-667 1477,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1279800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1477,-728 1477,-738 1503,-738 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="4696@1" ObjectIDZND0="4695@x" ObjectIDZND1="4699@x" ObjectIDZND2="4697@x" Pin0InfoVect0LinkObjId="SW-29064_0" Pin0InfoVect1LinkObjId="SW-29068_0" Pin0InfoVect2LinkObjId="SW-29066_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29065_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1477,-728 1477,-738 1503,-738 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_11e80f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,-642 1528,-691 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4653@0" ObjectIDZND0="4697@0" Pin0InfoVect0LinkObjId="SW-29066_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12f23e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1528,-642 1528,-691 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_11e8350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,-727 1528,-738 1503,-738 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="4697@1" ObjectIDZND0="4696@x" ObjectIDZND1="4695@x" ObjectIDZND2="4699@x" Pin0InfoVect0LinkObjId="SW-29065_0" Pin0InfoVect1LinkObjId="SW-29064_0" Pin0InfoVect2LinkObjId="SW-29068_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29066_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1528,-727 1528,-738 1503,-738 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_11e85b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1503,-801 1503,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4695@x" ObjectIDND1="4700@x" ObjectIDZND0="4698@0" Pin0InfoVect0LinkObjId="SW-29067_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29064_0" Pin1InfoVect1LinkObjId="SW-29069_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1503,-801 1503,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_129c9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="556,-214 537,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_129d660@0" ObjectIDZND0="4753@x" ObjectIDZND1="4754@x" ObjectIDZND2="49430@x" Pin0InfoVect0LinkObjId="SW-29124_0" Pin0InfoVect1LinkObjId="SW-29125_0" Pin0InfoVect2LinkObjId="EC-CX_YX.086LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_129d660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="556,-214 537,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1150340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="625,-213 606,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_11505a0@0" ObjectIDND1="4751@x" ObjectIDND2="49431@x" ObjectIDZND0="4752@1" Pin0InfoVect0LinkObjId="SW-29123_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_11505a0_0" Pin1InfoVect1LinkObjId="SW-29122_0" Pin1InfoVect2LinkObjId="EC-CX_YX.085LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="625,-213 606,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1151950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="624,-213 643,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="4751@x" ObjectIDND1="4752@x" ObjectIDND2="49431@x" ObjectIDZND0="g_11505a0@0" Pin0InfoVect0LinkObjId="g_11505a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29122_0" Pin1InfoVect1LinkObjId="SW-29123_0" Pin1InfoVect2LinkObjId="EC-CX_YX.085LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="624,-213 643,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1152660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="399,-214 395,-214 395,-197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4756@0" ObjectIDZND0="g_127c900@0" Pin0InfoVect0LinkObjId="g_127c900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29127_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="399,-214 395,-214 395,-197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11528c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="491,-215 486,-215 486,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4754@0" ObjectIDZND0="g_129cc10@0" Pin0InfoVect0LinkObjId="g_129cc10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29125_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="491,-215 486,-215 486,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1153570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,-212 574,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4752@0" ObjectIDZND0="g_1152b20@0" Pin0InfoVect0LinkObjId="g_1152b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29123_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="574,-212 574,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11537d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="717,-213 717,48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_1255280@0" ObjectIDND1="4749@x" ObjectIDND2="4750@x" ObjectIDZND0="49432@0" Pin0InfoVect0LinkObjId="EC-CX_YX.084Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1255280_0" Pin1InfoVect1LinkObjId="SW-29120_0" Pin1InfoVect2LinkObjId="SW-29121_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="717,-213 717,48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1255020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="717,-213 698,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_1255280@0" ObjectIDND1="4749@x" ObjectIDND2="49432@x" ObjectIDZND0="4750@1" Pin0InfoVect0LinkObjId="SW-29121_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1255280_0" Pin1InfoVect1LinkObjId="SW-29120_0" Pin1InfoVect2LinkObjId="EC-CX_YX.084Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="717,-213 698,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1256630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="716,-213 735,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="4749@x" ObjectIDND1="4750@x" ObjectIDND2="49432@x" ObjectIDZND0="g_1255280@0" Pin0InfoVect0LinkObjId="g_1255280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29120_0" Pin1InfoVect1LinkObjId="SW-29121_0" Pin1InfoVect2LinkObjId="EC-CX_YX.084Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="716,-213 735,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12572e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="666,-212 666,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4750@0" ObjectIDZND0="g_1256890@0" Pin0InfoVect0LinkObjId="g_1256890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29121_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="666,-212 666,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1257ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="807,-214 807,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_11950d0@0" ObjectIDND1="4747@x" ObjectIDND2="4748@x" ObjectIDZND0="49433@0" Pin0InfoVect0LinkObjId="EC-CX_YX.083LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_11950d0_0" Pin1InfoVect1LinkObjId="SW-29118_0" Pin1InfoVect2LinkObjId="SW-29119_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="807,-214 807,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1194e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="807,-214 788,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_11950d0@0" ObjectIDND1="4747@x" ObjectIDND2="49433@x" ObjectIDZND0="4748@1" Pin0InfoVect0LinkObjId="SW-29119_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_11950d0_0" Pin1InfoVect1LinkObjId="SW-29118_0" Pin1InfoVect2LinkObjId="EC-CX_YX.083LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="807,-214 788,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1196440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="806,-214 825,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="4747@x" ObjectIDND1="4748@x" ObjectIDND2="49433@x" ObjectIDZND0="g_11950d0@0" Pin0InfoVect0LinkObjId="g_11950d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29118_0" Pin1InfoVect1LinkObjId="SW-29119_0" Pin1InfoVect2LinkObjId="EC-CX_YX.083LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="806,-214 825,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11970f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="756,-213 756,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4748@0" ObjectIDZND0="g_11966a0@0" Pin0InfoVect0LinkObjId="g_11966a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29119_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="756,-213 756,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_119a330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,-212 878,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_119a590@0" ObjectIDND1="4745@x" ObjectIDND2="49442@x" ObjectIDZND0="4746@1" Pin0InfoVect0LinkObjId="SW-29117_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_119a590_0" Pin1InfoVect1LinkObjId="SW-29116_0" Pin1InfoVect2LinkObjId="EC-CX_YX.082LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="897,-212 878,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_119b940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="896,-212 915,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="4745@x" ObjectIDND1="4746@x" ObjectIDND2="49442@x" ObjectIDZND0="g_119a590@0" Pin0InfoVect0LinkObjId="g_119a590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29116_0" Pin1InfoVect1LinkObjId="SW-29117_0" Pin1InfoVect2LinkObjId="EC-CX_YX.082LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="896,-212 915,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11d8dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="846,-211 846,-197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4746@0" ObjectIDZND0="g_11d8390@0" Pin0InfoVect0LinkObjId="g_11d8390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29117_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="846,-211 846,-197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11de400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="937,-209 937,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4744@0" ObjectIDZND0="g_11dd9b0@0" Pin0InfoVect0LinkObjId="g_11dd9b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29115_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="937,-209 937,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_113e4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="485,-404 473,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5258@0" ObjectIDZND0="g_113da80@0" Pin0InfoVect0LinkObjId="g_113da80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="485,-404 473,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_113e990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="537,-46 537,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="49430@0" ObjectIDZND0="g_129d660@0" ObjectIDZND1="4753@x" ObjectIDZND2="4754@x" Pin0InfoVect0LinkObjId="g_129d660_0" Pin0InfoVect1LinkObjId="SW-29124_0" Pin0InfoVect2LinkObjId="SW-29125_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_YX.086LD_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="537,-46 537,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_113f6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="537,-214 522,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_129d660@0" ObjectIDND1="4753@x" ObjectIDND2="49430@x" ObjectIDZND0="4754@1" Pin0InfoVect0LinkObjId="SW-29125_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_129d660_0" Pin1InfoVect1LinkObjId="SW-29124_0" Pin1InfoVect2LinkObjId="EC-CX_YX.086LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="537,-214 522,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_124bb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1210,-406 1199,-406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5257@0" ObjectIDZND0="g_124b0b0@0" Pin0InfoVect0LinkObjId="g_124b0b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1210,-406 1199,-406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_124d790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1285,-211 1285,-139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_12835f0@0" ObjectIDND1="4729@x" ObjectIDND2="4730@x" ObjectIDZND0="49439@0" Pin0InfoVect0LinkObjId="EC-CX_YX.061LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_12835f0_0" Pin1InfoVect1LinkObjId="SW-29100_0" Pin1InfoVect2LinkObjId="SW-29101_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1285,-211 1285,-139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1283390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1285,-211 1268,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_12835f0@0" ObjectIDND1="4729@x" ObjectIDND2="49439@x" ObjectIDZND0="4730@1" Pin0InfoVect0LinkObjId="SW-29101_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_12835f0_0" Pin1InfoVect1LinkObjId="SW-29100_0" Pin1InfoVect2LinkObjId="EC-CX_YX.061LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1285,-211 1268,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12849a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1285,-211 1304,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="4729@x" ObjectIDND1="4730@x" ObjectIDND2="49439@x" ObjectIDZND0="g_12835f0@0" Pin0InfoVect0LinkObjId="g_12835f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29100_0" Pin1InfoVect1LinkObjId="SW-29101_0" Pin1InfoVect2LinkObjId="EC-CX_YX.061LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1285,-211 1304,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1285650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1235,-210 1235,-196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4730@0" ObjectIDZND0="g_1284c00@0" Pin0InfoVect0LinkObjId="g_1284c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29101_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1235,-210 1235,-196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1286360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1374,-209 1374,-139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_1288d50@0" ObjectIDND1="4731@x" ObjectIDND2="4732@x" ObjectIDZND0="49441@0" Pin0InfoVect0LinkObjId="EC-CX_YX.062LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1288d50_0" Pin1InfoVect1LinkObjId="SW-29102_0" Pin1InfoVect2LinkObjId="SW-29103_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1374,-209 1374,-139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1288af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1374,-209 1356,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_1288d50@0" ObjectIDND1="4731@x" ObjectIDND2="49441@x" ObjectIDZND0="4732@1" Pin0InfoVect0LinkObjId="SW-29103_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1288d50_0" Pin1InfoVect1LinkObjId="SW-29102_0" Pin1InfoVect2LinkObjId="EC-CX_YX.062LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1374,-209 1356,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_128a100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1374,-209 1393,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="4731@x" ObjectIDND1="4732@x" ObjectIDND2="49441@x" ObjectIDZND0="g_1288d50@0" Pin0InfoVect0LinkObjId="g_1288d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29102_0" Pin1InfoVect1LinkObjId="SW-29103_0" Pin1InfoVect2LinkObjId="EC-CX_YX.062LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1374,-209 1393,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_128adb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1324,-208 1324,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4732@0" ObjectIDZND0="g_128a360@0" Pin0InfoVect0LinkObjId="g_128a360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29103_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1324,-208 1324,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11440b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1462,-209 1445,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_1144310@0" ObjectIDND1="4733@x" ObjectIDND2="49438@x" ObjectIDZND0="4734@1" Pin0InfoVect0LinkObjId="SW-29105_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1144310_0" Pin1InfoVect1LinkObjId="SW-29104_0" Pin1InfoVect2LinkObjId="EC-CX_YX.063LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1462,-209 1445,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11456c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1462,-209 1481,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="4733@x" ObjectIDND1="4734@x" ObjectIDND2="49438@x" ObjectIDZND0="g_1144310@0" Pin0InfoVect0LinkObjId="g_1144310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29104_0" Pin1InfoVect1LinkObjId="SW-29105_0" Pin1InfoVect2LinkObjId="EC-CX_YX.063LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1462,-209 1481,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1146370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1412,-208 1412,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4734@0" ObjectIDZND0="g_1145920@0" Pin0InfoVect0LinkObjId="g_1145920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29105_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1412,-208 1412,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1147080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1551,-210 1550,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_1149a70@0" ObjectIDND1="4735@x" ObjectIDND2="4736@x" ObjectIDZND0="49437@0" Pin0InfoVect0LinkObjId="EC-CX_YX.064Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1149a70_0" Pin1InfoVect1LinkObjId="SW-29106_0" Pin1InfoVect2LinkObjId="SW-29107_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1551,-210 1550,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1149810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1551,-210 1533,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_1149a70@0" ObjectIDND1="4735@x" ObjectIDND2="49437@x" ObjectIDZND0="4736@1" Pin0InfoVect0LinkObjId="SW-29107_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1149a70_0" Pin1InfoVect1LinkObjId="SW-29106_0" Pin1InfoVect2LinkObjId="EC-CX_YX.064Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1551,-210 1533,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_114ae20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1551,-210 1570,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="4735@x" ObjectIDND1="4736@x" ObjectIDND2="49437@x" ObjectIDZND0="g_1149a70@0" Pin0InfoVect0LinkObjId="g_1149a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29106_0" Pin1InfoVect1LinkObjId="SW-29107_0" Pin1InfoVect2LinkObjId="EC-CX_YX.064Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1551,-210 1570,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_114bad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1501,-209 1501,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4736@0" ObjectIDZND0="g_114b080@0" Pin0InfoVect0LinkObjId="g_114b080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29107_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1501,-209 1501,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_114c7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1639,-208 1639,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_11f90b0@0" ObjectIDND1="4737@x" ObjectIDND2="4738@x" ObjectIDZND0="49436@0" Pin0InfoVect0LinkObjId="EC-CX_YX.065LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_11f90b0_0" Pin1InfoVect1LinkObjId="SW-29108_0" Pin1InfoVect2LinkObjId="SW-29109_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1639,-208 1639,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f8e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1639,-208 1622,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_11f90b0@0" ObjectIDND1="4737@x" ObjectIDND2="49436@x" ObjectIDZND0="4738@1" Pin0InfoVect0LinkObjId="SW-29109_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_11f90b0_0" Pin1InfoVect1LinkObjId="SW-29108_0" Pin1InfoVect2LinkObjId="EC-CX_YX.065LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1639,-208 1622,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11fa460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1639,-208 1658,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="4737@x" ObjectIDND1="4738@x" ObjectIDND2="49436@x" ObjectIDZND0="g_11f90b0@0" Pin0InfoVect0LinkObjId="g_11f90b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29108_0" Pin1InfoVect1LinkObjId="SW-29109_0" Pin1InfoVect2LinkObjId="EC-CX_YX.065LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1639,-208 1658,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11fb110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1589,-207 1589,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4738@0" ObjectIDZND0="g_11fa6c0@0" Pin0InfoVect0LinkObjId="g_11fa6c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29109_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1589,-207 1589,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11fe350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1728,-208 1711,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_11fe5b0@0" ObjectIDND1="4739@x" ObjectIDND2="49435@x" ObjectIDZND0="4740@1" Pin0InfoVect0LinkObjId="SW-29111_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_11fe5b0_0" Pin1InfoVect1LinkObjId="SW-29110_0" Pin1InfoVect2LinkObjId="EC-CX_YX.066LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1728,-208 1711,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11ff960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1728,-208 1747,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="4739@x" ObjectIDND1="4740@x" ObjectIDND2="49435@x" ObjectIDZND0="g_11fe5b0@0" Pin0InfoVect0LinkObjId="g_11fe5b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29110_0" Pin1InfoVect1LinkObjId="SW-29111_0" Pin1InfoVect2LinkObjId="EC-CX_YX.066LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1728,-208 1747,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1200610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1678,-207 1678,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4740@0" ObjectIDZND0="g_11ffbc0@0" Pin0InfoVect0LinkObjId="g_11ffbc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29111_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1678,-207 1678,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1205d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1766,-207 1766,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4742@0" ObjectIDZND0="g_1205320@0" Pin0InfoVect0LinkObjId="g_1205320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29113_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1766,-207 1766,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11b0040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1172,-216 1172,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="g_124bd60@0" ObjectIDZND0="4659@0" ObjectIDZND1="g_1487110@0" ObjectIDZND2="4728@x" Pin0InfoVect0LinkObjId="g_14e6590_0" Pin0InfoVect1LinkObjId="g_1487110_0" Pin0InfoVect2LinkObjId="SW-29099_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_124bd60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1172,-216 1172,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_11b5800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1729,-744 1746,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="4711@1" ObjectIDZND0="4710@x" ObjectIDZND1="g_11b0500@0" Pin0InfoVect0LinkObjId="SW-29079_0" Pin0InfoVect1LinkObjId="g_11b0500_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29080_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1729,-744 1746,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_11b5a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1683,-744 1694,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11b5cc0@0" ObjectIDZND0="4711@0" Pin0InfoVect0LinkObjId="SW-29080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11b5cc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1683,-744 1694,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_11b8c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1747,-726 1747,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4710@1" ObjectIDZND0="g_11b0500@0" ObjectIDZND1="4711@x" Pin0InfoVect0LinkObjId="g_11b0500_0" Pin0InfoVect1LinkObjId="SW-29080_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29079_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1747,-726 1747,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_11b8ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1747,-744 1747,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="4710@x" ObjectIDND1="4711@x" ObjectIDZND0="g_11b0500@0" Pin0InfoVect0LinkObjId="g_11b0500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29079_0" Pin1InfoVect1LinkObjId="SW-29080_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1747,-744 1747,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12343b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2042,-668 2042,-691 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4658@0" ObjectIDZND0="4716@0" Pin0InfoVect0LinkObjId="SW-29085_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1393770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2042,-668 2042,-691 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1234610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2024,-745 2041,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="4717@1" ObjectIDZND0="4716@x" ObjectIDZND1="g_11b9100@0" Pin0InfoVect0LinkObjId="SW-29085_0" Pin0InfoVect1LinkObjId="g_11b9100_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29086_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2024,-745 2041,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1234870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1978,-745 1989,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_12374c0@0" ObjectIDZND0="4717@0" Pin0InfoVect0LinkObjId="SW-29086_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12374c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1978,-745 1989,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1237000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2042,-727 2042,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4716@1" ObjectIDZND0="g_11b9100@0" ObjectIDZND1="4717@x" Pin0InfoVect0LinkObjId="g_11b9100_0" Pin0InfoVect1LinkObjId="SW-29086_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29085_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2042,-727 2042,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1237260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2042,-745 2042,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="4716@x" ObjectIDND1="4717@x" ObjectIDZND0="g_11b9100@0" Pin0InfoVect0LinkObjId="g_11b9100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29085_0" Pin1InfoVect1LinkObjId="SW-29086_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2042,-745 2042,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_123d210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1943,-668 1943,-693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4658@0" ObjectIDZND0="4714@0" Pin0InfoVect0LinkObjId="SW-29083_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1393770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1943,-668 1943,-693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_123d470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1925,-747 1942,-747 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="4715@1" ObjectIDZND0="4714@x" ObjectIDZND1="g_1237f10@0" Pin0InfoVect0LinkObjId="SW-29083_0" Pin0InfoVect1LinkObjId="g_1237f10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29084_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1925,-747 1942,-747 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_123d6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1879,-747 1890,-747 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1240320@0" ObjectIDZND0="4715@0" Pin0InfoVect0LinkObjId="SW-29084_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1240320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1879,-747 1890,-747 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_123fe60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1943,-729 1943,-747 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4714@1" ObjectIDZND0="g_1237f10@0" ObjectIDZND1="4715@x" Pin0InfoVect0LinkObjId="g_1237f10_0" Pin0InfoVect1LinkObjId="SW-29084_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29083_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1943,-729 1943,-747 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_12400c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1943,-747 1943,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="4714@x" ObjectIDND1="4715@x" ObjectIDZND0="g_1237f10@0" Pin0InfoVect0LinkObjId="g_1237f10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29083_0" Pin1InfoVect1LinkObjId="SW-29084_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1943,-747 1943,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_119deb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1840,-668 1840,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4658@0" ObjectIDZND0="4712@0" Pin0InfoVect0LinkObjId="SW-29081_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1393770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1840,-668 1840,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_119e110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1822,-749 1839,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="4713@1" ObjectIDZND0="4712@x" ObjectIDZND1="g_1240d70@0" Pin0InfoVect0LinkObjId="SW-29081_0" Pin0InfoVect1LinkObjId="g_1240d70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29082_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1822,-749 1839,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_119e370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1776,-749 1787,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11a0de0@0" ObjectIDZND0="4713@0" Pin0InfoVect0LinkObjId="SW-29082_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11a0de0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1776,-749 1787,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_11a0920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1840,-731 1840,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4712@1" ObjectIDZND0="g_1240d70@0" ObjectIDZND1="4713@x" Pin0InfoVect0LinkObjId="g_1240d70_0" Pin0InfoVect1LinkObjId="SW-29082_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29081_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1840,-731 1840,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_11a0b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1840,-749 1840,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="4712@x" ObjectIDND1="4713@x" ObjectIDZND0="g_1240d70@0" Pin0InfoVect0LinkObjId="g_1240d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29081_0" Pin1InfoVect1LinkObjId="SW-29082_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1840,-749 1840,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_11a9150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1757,-558 1795,-558 1795,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="5237@x" ObjectIDZND0="4707@1" Pin0InfoVect0LinkObjId="SW-29076_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1458a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1757,-558 1795,-558 1795,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_11a93b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1795,-514 1795,-503 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4707@0" ObjectIDZND0="g_11a9870@0" Pin0InfoVect0LinkObjId="g_11a9870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29076_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1795,-514 1795,-503 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_11a9610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1696,-600 1706,-600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11a6140@0" ObjectIDZND0="4709@0" Pin0InfoVect0LinkObjId="SW-29078_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11a6140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1696,-600 1706,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1223680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1864,-429 1864,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="4654@0" ObjectIDZND0="4718@1" Pin0InfoVect0LinkObjId="SW-29087_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1391e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1864,-429 1864,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1225930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1948,-429 1948,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="4654@0" ObjectIDZND0="4719@1" Pin0InfoVect0LinkObjId="SW-29089_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1391e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1948,-429 1948,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1226460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1948,-319 2009,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4719@x" ObjectIDND1="4720@x" ObjectIDZND0="4722@x" ObjectIDZND1="4724@x" Pin0InfoVect0LinkObjId="SW-29093_0" Pin0InfoVect1LinkObjId="SW-29095_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29089_0" Pin1InfoVect1LinkObjId="SW-29091_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1948,-319 2009,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1226f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1948,-368 1948,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="4719@0" ObjectIDZND0="4722@x" ObjectIDZND1="4724@x" ObjectIDZND2="4720@x" Pin0InfoVect0LinkObjId="SW-29093_0" Pin0InfoVect1LinkObjId="SW-29095_0" Pin0InfoVect2LinkObjId="SW-29091_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29089_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1948,-368 1948,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12299f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1948,-319 1948,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="4722@x" ObjectIDND1="4724@x" ObjectIDND2="4719@x" ObjectIDZND0="4720@1" Pin0InfoVect0LinkObjId="SW-29091_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29093_0" Pin1InfoVect1LinkObjId="SW-29095_0" Pin1InfoVect2LinkObjId="SW-29089_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1948,-319 1948,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_122c450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2009,-300 2009,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="4722@1" ObjectIDZND0="4719@x" ObjectIDZND1="4720@x" ObjectIDZND2="4724@x" Pin0InfoVect0LinkObjId="SW-29089_0" Pin0InfoVect1LinkObjId="SW-29091_0" Pin0InfoVect2LinkObjId="SW-29095_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29093_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2009,-300 2009,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1382b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2068,-303 2068,-319 2009,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="4724@1" ObjectIDZND0="4719@x" ObjectIDZND1="4720@x" ObjectIDZND2="4722@x" Pin0InfoVect0LinkObjId="SW-29089_0" Pin0InfoVect1LinkObjId="SW-29091_0" Pin0InfoVect2LinkObjId="SW-29093_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29095_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2068,-303 2068,-319 2009,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1385d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1909,-250 1903,-250 1903,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4721@0" ObjectIDZND0="g_1385320@0" Pin0InfoVect0LinkObjId="g_1385320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29092_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1909,-250 1903,-250 1903,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1388f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1971,-246 1967,-246 1967,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4723@0" ObjectIDZND0="g_1388500@0" Pin0InfoVect0LinkObjId="g_1388500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29094_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1971,-246 1967,-246 1967,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_138c130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2029,-248 2025,-248 2025,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4725@0" ObjectIDZND0="g_138b6e0@0" Pin0InfoVect0LinkObjId="g_138b6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29096_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2029,-248 2025,-248 2025,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_138f740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1865,-203 1865,-371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_138c390@0" ObjectIDZND0="4718@0" Pin0InfoVect0LinkObjId="SW-29087_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_138c390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1865,-203 1865,-371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1391e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2012,-397 2034,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="g_138f9a0@0" ObjectIDZND0="4654@0" ObjectIDZND1="4757@x" Pin0InfoVect0LinkObjId="g_13920a0_0" Pin0InfoVect1LinkObjId="SW-29128_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_138f9a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2012,-397 2034,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13920a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2034,-397 2034,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="g_138f9a0@0" ObjectIDND1="4757@x" ObjectIDZND0="4654@0" Pin0InfoVect0LinkObjId="g_1391e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_138f9a0_0" Pin1InfoVect1LinkObjId="SW-29128_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2034,-397 2034,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1393770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1758,-653 1758,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4708@1" ObjectIDZND0="4658@0" Pin0InfoVect0LinkObjId="g_1393960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29077_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1758,-653 1758,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1393960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1747,-694 1747,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4710@0" ObjectIDZND0="4658@0" Pin0InfoVect0LinkObjId="g_1393770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29079_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1747,-694 1747,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11c18a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="625,-213 625,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_11505a0@0" ObjectIDND1="4751@x" ObjectIDND2="4752@x" ObjectIDZND0="49431@0" Pin0InfoVect0LinkObjId="EC-CX_YX.085LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_11505a0_0" Pin1InfoVect1LinkObjId="SW-29122_0" Pin1InfoVect2LinkObjId="SW-29123_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="625,-213 625,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11c1b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="625,-74 625,-52 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="5238@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="625,-74 625,-52 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11c55d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="806,-81 807,-60 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5253@0" ObjectIDZND0="g_11c1ea0@0" Pin0InfoVect0LinkObjId="g_11c1ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="806,-81 807,-60 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11c92a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="989,-123 989,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" ObjectIDND0="5241@0" ObjectIDZND0="49434@0" Pin0InfoVect0LinkObjId="EC-CX_YX.081LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="989,-123 989,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11ccf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="717,114 717,128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5242@0" ObjectIDZND0="g_11c96a0@0" Pin0InfoVect0LinkObjId="g_11c96a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="717,114 717,128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11ce2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="825,115 825,129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5243@0" ObjectIDZND0="g_11cd1d0@0" Pin0InfoVect0LinkObjId="g_11cd1d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="825,115 825,129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11d4730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1411,125 1411,139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5255@0" ObjectIDZND0="g_11d0ea0@0" Pin0InfoVect0LinkObjId="g_11d0ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1411,125 1411,139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1155ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1374,-79 1374,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5244@0" ObjectIDZND0="g_1154f20@0" Pin0InfoVect0LinkObjId="g_1154f20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1374,-79 1374,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1158a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1285,-77 1285,-63 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5245@0" ObjectIDZND0="g_11d4f20@0" Pin0InfoVect0LinkObjId="g_11d4f20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1285,-77 1285,-63 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11590f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1640,-98 1639,-69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" ObjectIDND0="5252@0" ObjectIDZND0="22570@0" Pin0InfoVect0LinkObjId="EC-CX_YX.CX_YX_065LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1640,-98 1639,-69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1166e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1517,114 1517,107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5248@1" ObjectIDZND0="5250@x" ObjectIDZND1="5247@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1517,114 1517,107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11678f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1462,100 1462,107 1517,107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5250@0" ObjectIDZND0="5247@x" ObjectIDZND1="5248@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1462,100 1462,107 1517,107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1167b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1517,142 1517,135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="5249@1" ObjectIDZND0="5248@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1517,142 1517,135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1167db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1517,163 1517,170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5249@0" ObjectIDZND0="g_1160db0@0" Pin0InfoVect0LinkObjId="g_1160db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1517,163 1517,170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1168010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1462,57 1462,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="49438@0" ObjectIDZND0="g_1144310@0" ObjectIDZND1="4733@x" ObjectIDZND2="4734@x" Pin0InfoVect0LinkObjId="g_1144310_0" Pin0InfoVect1LinkObjId="SW-29104_0" Pin0InfoVect2LinkObjId="SW-29105_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_YX.063LD_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1462,57 1462,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1168270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1411,68 1411,35 1410,10 897,10 897,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="49442@0" ObjectIDZND0="g_119a590@0" ObjectIDZND1="4745@x" ObjectIDZND2="4746@x" Pin0InfoVect0LinkObjId="g_119a590_0" Pin0InfoVect1LinkObjId="SW-29116_0" Pin0InfoVect2LinkObjId="SW-29117_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_YX.082LD_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1411,68 1411,35 1410,10 897,10 897,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11684e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1054,-229 1054,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_113f900@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_113f900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1054,-229 1054,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1168740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1054,-159 1054,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="5254@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1054,-159 1054,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_116d3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1226,-331 1226,-227 1172,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="4659@0" ObjectIDZND0="g_124bd60@0" ObjectIDZND1="g_1487110@0" ObjectIDZND2="4728@x" Pin0InfoVect0LinkObjId="g_124bd60_0" Pin0InfoVect1LinkObjId="g_1487110_0" Pin0InfoVect2LinkObjId="SW-29099_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11b0040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1226,-331 1226,-227 1172,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1174b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="473,-855 473,-891 538,-891 538,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="4685@1" ObjectIDZND0="4759@1" Pin0InfoVect0LinkObjId="g_1188350_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29054_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="473,-855 473,-891 538,-891 538,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1178410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-857 1193,-892 1262,-892 1262,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="4678@1" ObjectIDZND0="4758@1" Pin0InfoVect0LinkObjId="g_118b2b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29047_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-857 1193,-892 1262,-892 1262,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_11791c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1648,-719 1648,-913 1503,-913 1503,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" ObjectIDND0="49427@1" ObjectIDZND0="4698@1" Pin0InfoVect0LinkObjId="SW-29067_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1648,-719 1648,-913 1503,-913 1503,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_117a770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="521,-404 537,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" EndDevType2="breaker" ObjectIDND0="5258@1" ObjectIDZND0="g_11868b0@0" ObjectIDZND1="4759@x" ObjectIDZND2="4727@x" Pin0InfoVect0LinkObjId="g_11868b0_0" Pin0InfoVect1LinkObjId="g_1174b90_0" Pin0InfoVect2LinkObjId="SW-29098_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="521,-404 537,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1183790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1054,-123 1054,-109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5254@0" ObjectIDZND0="g_1247ed0@0" Pin0InfoVect0LinkObjId="g_1247ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1054,-123 1054,-109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11880f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="581,-397 581,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_11868b0@1" ObjectIDZND0="g_11876a0@0" Pin0InfoVect0LinkObjId="g_11876a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11868b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="581,-397 581,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1188350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="581,-440 537,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_11868b0@0" ObjectIDZND0="4759@x" ObjectIDZND1="4727@x" ObjectIDZND2="5258@x" Pin0InfoVect0LinkObjId="g_1174b90_0" Pin0InfoVect1LinkObjId="SW-29098_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11868b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="581,-440 537,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1188e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="537,-485 537,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="4759@0" ObjectIDZND0="g_11868b0@0" ObjectIDZND1="4727@x" ObjectIDZND2="5258@x" Pin0InfoVect0LinkObjId="g_11868b0_0" Pin0InfoVect1LinkObjId="SW-29098_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1174b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="537,-485 537,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11890a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="537,-440 537,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_11868b0@0" ObjectIDND1="4759@x" ObjectIDZND0="4727@x" ObjectIDZND1="5258@x" Pin0InfoVect0LinkObjId="SW-29098_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_11868b0_0" Pin1InfoVect1LinkObjId="g_1174b90_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="537,-440 537,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1189300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1246,-406 1261,-406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" EndDevType2="breaker" ObjectIDND0="5257@1" ObjectIDZND0="g_1189560@0" ObjectIDZND1="4758@x" ObjectIDZND2="4726@x" Pin0InfoVect0LinkObjId="g_1189560_0" Pin0InfoVect1LinkObjId="g_1178410_0" Pin0InfoVect2LinkObjId="SW-29097_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1246,-406 1261,-406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_118b050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1306,-402 1306,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_1189560@1" ObjectIDZND0="g_118a600@0" Pin0InfoVect0LinkObjId="g_118a600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1189560_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1306,-402 1306,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_118b2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1306,-445 1261,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_1189560@0" ObjectIDZND0="4758@x" ObjectIDZND1="4726@x" ObjectIDZND2="5257@x" Pin0InfoVect0LinkObjId="g_1178410_0" Pin0InfoVect1LinkObjId="SW-29097_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1189560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1306,-445 1261,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_118bda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1261,-487 1261,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="4758@0" ObjectIDZND0="g_1189560@0" ObjectIDZND1="4726@x" ObjectIDZND2="5257@x" Pin0InfoVect0LinkObjId="g_1189560_0" Pin0InfoVect1LinkObjId="SW-29097_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1178410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1261,-487 1261,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_118ccb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1941,-250 1948,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4721@1" ObjectIDZND0="g_11aa2a0@0" ObjectIDZND1="4720@x" Pin0InfoVect0LinkObjId="g_11aa2a0_0" Pin0InfoVect1LinkObjId="SW-29091_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29092_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1941,-250 1948,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_118d690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1948,-265 1948,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4720@0" ObjectIDZND0="g_11aa2a0@0" ObjectIDZND1="4721@x" Pin0InfoVect0LinkObjId="g_11aa2a0_0" Pin0InfoVect1LinkObjId="SW-29092_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29091_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1948,-265 1948,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_118d8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1948,-250 1948,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="4720@x" ObjectIDND1="4721@x" ObjectIDZND0="g_11aa2a0@0" Pin0InfoVect0LinkObjId="g_11aa2a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29091_0" Pin1InfoVect1LinkObjId="SW-29092_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1948,-250 1948,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_118daf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2009,-245 2009,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_11af430@0" ObjectIDND1="4723@x" ObjectIDZND0="4722@0" Pin0InfoVect0LinkObjId="SW-29093_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_11af430_0" Pin1InfoVect1LinkObjId="SW-29094_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2009,-245 2009,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_118dd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2061,-248 2068,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4725@1" ObjectIDZND0="g_121c700@0" ObjectIDZND1="4724@x" Pin0InfoVect0LinkObjId="g_121c700_0" Pin0InfoVect1LinkObjId="SW-29095_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29096_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2061,-248 2068,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_118e810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2068,-207 2068,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_121c700@0" ObjectIDZND0="4724@x" ObjectIDZND1="4725@x" Pin0InfoVect0LinkObjId="SW-29095_0" Pin0InfoVect1LinkObjId="SW-29096_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_121c700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2068,-207 2068,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_118ea70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2068,-247 2068,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_121c700@0" ObjectIDND1="4725@x" ObjectIDZND0="4724@0" Pin0InfoVect0LinkObjId="SW-29095_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_121c700_0" Pin1InfoVect1LinkObjId="SW-29096_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2068,-247 2068,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_144e310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2003,-246 2009,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4723@1" ObjectIDZND0="g_11af430@0" ObjectIDZND1="4722@x" Pin0InfoVect0LinkObjId="g_11af430_0" Pin0InfoVect1LinkObjId="SW-29093_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29094_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2003,-246 2009,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_144e570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2009,-245 2009,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="4722@x" ObjectIDND1="4723@x" ObjectIDZND0="g_11af430@0" Pin0InfoVect0LinkObjId="g_11af430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29093_0" Pin1InfoVect1LinkObjId="SW-29094_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2009,-245 2009,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1458a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1758,-600 1758,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="4709@x" ObjectIDND1="4708@x" ObjectIDZND0="5237@1" Pin0InfoVect0LinkObjId="g_1458cc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29078_0" Pin1InfoVect1LinkObjId="SW-29077_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1758,-600 1758,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1458cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1742,-600 1758,-600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="4709@1" ObjectIDZND0="5237@x" ObjectIDZND1="4708@x" Pin0InfoVect0LinkObjId="g_1458a60_0" Pin0InfoVect1LinkObjId="SW-29077_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29078_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1742,-600 1758,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1458f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1758,-600 1758,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="4709@x" ObjectIDND1="5237@x" ObjectIDZND0="4708@0" Pin0InfoVect0LinkObjId="SW-29077_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29078_0" Pin1InfoVect1LinkObjId="g_1458a60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1758,-600 1758,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_145ab40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1261,-445 1261,-406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_1189560@0" ObjectIDND1="4758@x" ObjectIDZND0="4726@x" ObjectIDZND1="5257@x" Pin0InfoVect0LinkObjId="SW-29097_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1189560_0" Pin1InfoVect1LinkObjId="g_1178410_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1261,-445 1261,-406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_145ad30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1517,107 1559,107 1559,100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5250@x" ObjectIDND1="5248@x" ObjectIDZND0="5247@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1517,107 1559,107 1559,100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_145af40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1559,79 1559,48 1654,48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" ObjectIDND0="5247@1" ObjectIDZND0="22572@0" Pin0InfoVect0LinkObjId="EC-CX_YX.CX_YX_F071LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1559,79 1559,48 1654,48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1485030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2084,-431 2084,-470 1757,-470 1757,-505 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="4757@0" ObjectIDZND0="5237@0" Pin0InfoVect0LinkObjId="g_1458a60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29128_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2084,-431 2084,-470 1757,-470 1757,-505 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14852a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2084,-399 2084,-347 2034,-347 2034,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="lightningRod" ObjectIDND0="4757@1" ObjectIDZND0="4654@0" ObjectIDZND1="g_138f9a0@0" Pin0InfoVect0LinkObjId="g_1391e40_0" Pin0InfoVect1LinkObjId="g_138f9a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29128_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2084,-399 2084,-347 2034,-347 2034,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1488190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1093,-210 1093,-233 1124,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="busSection" EndDevType2="breaker" ObjectIDND0="g_1487110@0" ObjectIDZND0="g_124bd60@0" ObjectIDZND1="4659@0" ObjectIDZND2="4728@x" Pin0InfoVect0LinkObjId="g_124bd60_0" Pin0InfoVect1LinkObjId="g_11b0040_0" Pin0InfoVect2LinkObjId="SW-29099_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1487110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1093,-210 1093,-233 1124,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1488c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1172,-227 1124,-227 1124,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="busSection" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_124bd60@0" ObjectIDND1="4659@0" ObjectIDZND0="g_1487110@0" ObjectIDZND1="4728@x" Pin0InfoVect0LinkObjId="g_1487110_0" Pin0InfoVect1LinkObjId="SW-29099_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_124bd60_0" Pin1InfoVect1LinkObjId="g_11b0040_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1172,-227 1124,-227 1124,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_148d4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="770,-822 770,-800 801,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_14899b0@0" ObjectIDZND0="4701@x" ObjectIDZND1="4703@x" ObjectIDZND2="g_148be70@0" Pin0InfoVect0LinkObjId="SW-29070_0" Pin0InfoVect1LinkObjId="SW-29072_0" Pin0InfoVect2LinkObjId="g_148be70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14899b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="770,-822 770,-800 801,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_148d710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-800 820,-800 820,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="4701@x" ObjectIDND1="4703@x" ObjectIDND2="g_14899b0@0" ObjectIDZND0="g_148be70@0" Pin0InfoVect0LinkObjId="g_148be70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29070_0" Pin1InfoVect1LinkObjId="SW-29072_0" Pin1InfoVect2LinkObjId="g_14899b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="801,-800 820,-800 820,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1490be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="899,-819 899,-797 930,-797 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_148d970@0" ObjectIDZND0="4704@x" ObjectIDZND1="4706@x" ObjectIDZND2="g_148fe30@0" Pin0InfoVect0LinkObjId="SW-29073_0" Pin0InfoVect1LinkObjId="SW-29075_0" Pin0InfoVect2LinkObjId="g_148fe30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_148d970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="899,-819 899,-797 930,-797 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1490e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-797 949,-797 949,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="4704@x" ObjectIDND1="4706@x" ObjectIDND2="g_148d970@0" ObjectIDZND0="g_148fe30@0" Pin0InfoVect0LinkObjId="g_148fe30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29073_0" Pin1InfoVect1LinkObjId="SW-29075_0" Pin1InfoVect2LinkObjId="g_148d970_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="930,-797 949,-797 949,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1496a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-302 446,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="4755@1" ObjectIDZND0="4660@0" Pin0InfoVect0LinkObjId="g_14b9c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29126_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="447,-302 446,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_149b620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="537,-330 537,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="4660@0" ObjectIDZND0="4753@1" Pin0InfoVect0LinkObjId="SW-29124_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1496a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="537,-330 537,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_149b880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="537,-232 537,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="4753@0" ObjectIDZND0="g_129d660@0" ObjectIDZND1="4754@x" ObjectIDZND2="49430@x" Pin0InfoVect0LinkObjId="g_129d660_0" Pin0InfoVect1LinkObjId="SW-29125_0" Pin0InfoVect2LinkObjId="EC-CX_YX.086LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29124_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="537,-232 537,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14a0410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="625,-330 625,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="4660@0" ObjectIDZND0="4751@1" Pin0InfoVect0LinkObjId="SW-29122_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1496a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="625,-330 625,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14a0670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="625,-232 625,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="4751@0" ObjectIDZND0="g_11505a0@0" ObjectIDZND1="4752@x" ObjectIDZND2="49431@x" Pin0InfoVect0LinkObjId="g_11505a0_0" Pin0InfoVect1LinkObjId="SW-29123_0" Pin0InfoVect2LinkObjId="EC-CX_YX.085LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29122_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="625,-232 625,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14a5200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="717,-330 717,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="4660@0" ObjectIDZND0="4749@1" Pin0InfoVect0LinkObjId="SW-29120_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1496a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="717,-330 717,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14a5460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="717,-232 717,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="4749@0" ObjectIDZND0="g_1255280@0" ObjectIDZND1="4750@x" ObjectIDZND2="49432@x" Pin0InfoVect0LinkObjId="g_1255280_0" Pin0InfoVect1LinkObjId="SW-29121_0" Pin0InfoVect2LinkObjId="EC-CX_YX.084Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="717,-232 717,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14aa5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="807,-330 807,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="4660@0" ObjectIDZND0="4747@1" Pin0InfoVect0LinkObjId="SW-29118_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1496a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="807,-330 807,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14aa820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="807,-233 807,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="4747@0" ObjectIDZND0="g_11950d0@0" ObjectIDZND1="4748@x" ObjectIDZND2="49433@x" Pin0InfoVect0LinkObjId="g_11950d0_0" Pin0InfoVect1LinkObjId="SW-29119_0" Pin0InfoVect2LinkObjId="EC-CX_YX.083LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29118_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="807,-233 807,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14af3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,-330 897,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="4660@0" ObjectIDZND0="4745@1" Pin0InfoVect0LinkObjId="SW-29116_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1496a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="897,-330 897,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14af610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,-230 897,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="4745@0" ObjectIDZND0="g_119a590@0" ObjectIDZND1="4746@x" ObjectIDZND2="49442@x" Pin0InfoVect0LinkObjId="g_119a590_0" Pin0InfoVect1LinkObjId="SW-29117_0" Pin0InfoVect2LinkObjId="EC-CX_YX.082LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29116_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="897,-230 897,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14b41a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="988,-330 988,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="4660@0" ObjectIDZND0="4743@1" Pin0InfoVect0LinkObjId="SW-29114_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1496a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="988,-330 988,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14b4660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1054,-330 1054,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4660@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1496a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1054,-330 1054,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14b48c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1054,-288 1054,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_113f900@1" Pin0InfoVect0LinkObjId="g_113f900_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1054,-288 1054,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14b9a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1124,-233 1124,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="busSection" EndDevType0="breaker" ObjectIDND0="g_1487110@0" ObjectIDND1="g_124bd60@0" ObjectIDND2="4659@0" ObjectIDZND0="4728@0" Pin0InfoVect0LinkObjId="SW-29099_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1487110_0" Pin1InfoVect1LinkObjId="g_124bd60_0" Pin1InfoVect2LinkObjId="g_11b0040_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1124,-233 1124,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14b9c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1124,-314 1124,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="4728@1" ObjectIDZND0="4660@0" Pin0InfoVect0LinkObjId="g_1496a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29099_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1124,-314 1124,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14be810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1285,-331 1285,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="4659@0" ObjectIDZND0="4729@1" Pin0InfoVect0LinkObjId="SW-29100_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11b0040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1285,-331 1285,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14bea70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1285,-232 1285,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="4729@0" ObjectIDZND0="g_12835f0@0" ObjectIDZND1="4730@x" ObjectIDZND2="49439@x" Pin0InfoVect0LinkObjId="g_12835f0_0" Pin0InfoVect1LinkObjId="SW-29101_0" Pin0InfoVect2LinkObjId="EC-CX_YX.061LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1285,-232 1285,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14c3600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1374,-331 1374,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="4659@0" ObjectIDZND0="4731@1" Pin0InfoVect0LinkObjId="SW-29102_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11b0040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1374,-331 1374,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14c3860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1374,-232 1374,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="4731@0" ObjectIDZND0="g_1288d50@0" ObjectIDZND1="4732@x" ObjectIDZND2="49441@x" Pin0InfoVect0LinkObjId="g_1288d50_0" Pin0InfoVect1LinkObjId="SW-29103_0" Pin0InfoVect2LinkObjId="EC-CX_YX.062LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29102_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1374,-232 1374,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14c83f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1462,-331 1462,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="4659@0" ObjectIDZND0="4733@1" Pin0InfoVect0LinkObjId="SW-29104_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11b0040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1462,-331 1462,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14c8650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1462,-235 1462,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="4733@0" ObjectIDZND0="g_1144310@0" ObjectIDZND1="4734@x" ObjectIDZND2="49438@x" Pin0InfoVect0LinkObjId="g_1144310_0" Pin0InfoVect1LinkObjId="SW-29105_0" Pin0InfoVect2LinkObjId="EC-CX_YX.063LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29104_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1462,-235 1462,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14cd1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1551,-331 1551,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="4659@0" ObjectIDZND0="4735@1" Pin0InfoVect0LinkObjId="SW-29106_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11b0040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1551,-331 1551,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14cd440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1551,-233 1551,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="4735@0" ObjectIDZND0="g_1149a70@0" ObjectIDZND1="4736@x" ObjectIDZND2="49437@x" Pin0InfoVect0LinkObjId="g_1149a70_0" Pin0InfoVect1LinkObjId="SW-29107_0" Pin0InfoVect2LinkObjId="EC-CX_YX.064Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29106_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1551,-233 1551,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14d1fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1639,-331 1639,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="4659@0" ObjectIDZND0="4737@1" Pin0InfoVect0LinkObjId="SW-29108_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11b0040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1639,-331 1639,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14d2230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1639,-234 1639,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="4737@0" ObjectIDZND0="g_11f90b0@0" ObjectIDZND1="4738@x" ObjectIDZND2="49436@x" Pin0InfoVect0LinkObjId="g_11f90b0_0" Pin0InfoVect1LinkObjId="SW-29109_0" Pin0InfoVect2LinkObjId="EC-CX_YX.065LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29108_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1639,-234 1639,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14d6dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1728,-331 1728,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="4659@0" ObjectIDZND0="4739@1" Pin0InfoVect0LinkObjId="SW-29110_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11b0040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1728,-331 1728,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14d7020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1728,-230 1728,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="4739@0" ObjectIDZND0="g_11fe5b0@0" ObjectIDZND1="4740@x" ObjectIDZND2="49435@x" Pin0InfoVect0LinkObjId="g_11fe5b0_0" Pin0InfoVect1LinkObjId="SW-29111_0" Pin0InfoVect2LinkObjId="EC-CX_YX.066LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1728,-230 1728,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14dbbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1816,-331 1816,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="4659@0" ObjectIDZND0="4741@1" Pin0InfoVect0LinkObjId="SW-29112_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11b0040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1816,-331 1816,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14e1540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="537,-404 537,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_11868b0@0" ObjectIDND1="4759@x" ObjectIDND2="5258@x" ObjectIDZND0="4727@1" Pin0InfoVect0LinkObjId="SW-29098_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_11868b0_0" Pin1InfoVect1LinkObjId="g_1174b90_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="537,-404 537,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14e17a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="537,-337 537,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="4727@0" ObjectIDZND0="4660@0" Pin0InfoVect0LinkObjId="g_1496a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29098_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="537,-337 537,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14e6330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1261,-406 1261,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_1189560@0" ObjectIDND1="4758@x" ObjectIDND2="5257@x" ObjectIDZND0="4726@1" Pin0InfoVect0LinkObjId="SW-29097_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1189560_0" Pin1InfoVect1LinkObjId="g_1178410_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1261,-406 1261,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14e6590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1261,-340 1261,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="4726@0" ObjectIDZND0="4659@0" Pin0InfoVect0LinkObjId="g_11b0040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29097_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1261,-340 1261,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14e67f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-548 1176,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="transformer2" EndDevType0="lightningRod" ObjectIDND0="g_14e9ec0@0" ObjectIDND1="4681@x" ObjectIDND2="4758@x" ObjectIDZND0="g_14eaa90@0" Pin0InfoVect0LinkObjId="g_14eaa90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_14e9ec0_0" Pin1InfoVect1LinkObjId="SW-29050_0" Pin1InfoVect2LinkObjId="g_1178410_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-548 1176,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14e9210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1126,-499 1126,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4681@0" ObjectIDZND0="g_14e9470@0" Pin0InfoVect0LinkObjId="g_14e9470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1126,-499 1126,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14ec5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1126,-535 1126,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" EndDevType2="lightningRod" ObjectIDND0="4681@1" ObjectIDZND0="g_14eaa90@0" ObjectIDZND1="4758@x" ObjectIDZND2="g_14e9ec0@0" Pin0InfoVect0LinkObjId="g_14eaa90_0" Pin0InfoVect1LinkObjId="g_1178410_0" Pin0InfoVect2LinkObjId="g_14e9ec0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29050_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1126,-535 1126,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14ec7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1126,-548 1150,-548 1150,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_14eaa90@0" ObjectIDND1="4758@x" ObjectIDND2="4681@x" ObjectIDZND0="g_14e9ec0@0" Pin0InfoVect0LinkObjId="g_14e9ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_14eaa90_0" Pin1InfoVect1LinkObjId="g_1178410_0" Pin1InfoVect2LinkObjId="SW-29050_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1126,-548 1150,-548 1150,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14ed1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1126,-548 1176,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_14e9ec0@0" ObjectIDND1="4681@x" ObjectIDZND0="g_14eaa90@0" ObjectIDZND1="4758@x" Pin0InfoVect0LinkObjId="g_14eaa90_0" Pin0InfoVect1LinkObjId="g_1178410_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14e9ec0_0" Pin1InfoVect1LinkObjId="SW-29050_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1126,-548 1176,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14ed400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-548 1261,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="transformer2" ObjectIDND0="g_14eaa90@0" ObjectIDND1="g_14e9ec0@0" ObjectIDND2="4681@x" ObjectIDZND0="4758@x" Pin0InfoVect0LinkObjId="g_1178410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_14eaa90_0" Pin1InfoVect1LinkObjId="g_14e9ec0_0" Pin1InfoVect2LinkObjId="SW-29050_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-548 1261,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14ed660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="407,-546 537,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="4688@1" ObjectIDZND0="4759@x" Pin0InfoVect0LinkObjId="g_1174b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29057_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="407,-546 537,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14eff10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="407,-511 408,-499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4688@0" ObjectIDZND0="g_14f0170@0" Pin0InfoVect0LinkObjId="g_14f0170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29057_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="407,-511 408,-499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14f2500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="643,-861 660,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="4674@1" ObjectIDZND0="4671@x" ObjectIDZND1="48792@1" Pin0InfoVect0LinkObjId="SW-29040_0" Pin0InfoVect1LinkObjId="g_14f3250_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29043_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="643,-861 660,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14f2ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="660,-849 660,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="4671@1" ObjectIDZND0="4674@x" ObjectIDZND1="48792@1" Pin0InfoVect0LinkObjId="SW-29043_0" Pin0InfoVect1LinkObjId="g_14f3250_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29040_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="660,-849 660,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14f3250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="660,-861 660,-882 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" ObjectIDND0="4674@x" ObjectIDND1="4671@x" ObjectIDZND0="48792@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29043_0" Pin1InfoVect1LinkObjId="SW-29040_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="660,-861 660,-882 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14f34b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1038,-863 1055,-863 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="switch" ObjectIDND0="4667@1" ObjectIDZND0="9187@1" ObjectIDZND1="4664@x" Pin0InfoVect0LinkObjId="g_14f4200_1" Pin0InfoVect1LinkObjId="SW-29033_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29036_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1038,-863 1055,-863 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14f3fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1055,-849 1055,-863 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="4664@1" ObjectIDZND0="4667@x" ObjectIDZND1="9187@1" Pin0InfoVect0LinkObjId="SW-29036_0" Pin0InfoVect1LinkObjId="g_14f34b0_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29033_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1055,-849 1055,-863 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_14f4200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1055,-863 1055,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" ObjectIDND0="4667@x" ObjectIDND1="4664@x" ObjectIDZND0="9187@1" Pin0InfoVect0LinkObjId="g_14f34b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-29036_0" Pin1InfoVect1LinkObjId="SW-29033_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1055,-863 1055,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15007f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1728,-208 1728,26 825,26 825,56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_11fe5b0@0" ObjectIDND1="4739@x" ObjectIDND2="4740@x" ObjectIDZND0="49435@0" Pin0InfoVect0LinkObjId="EC-CX_YX.066LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_11fe5b0_0" Pin1InfoVect1LinkObjId="SW-29110_0" Pin1InfoVect2LinkObjId="SW-29111_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1728,-208 1728,26 825,26 825,56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1502870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2225,-857 2242,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="34574@1" ObjectIDZND0="34572@x" ObjectIDZND1="34570@x" Pin0InfoVect0LinkObjId="SW-221237_0" Pin0InfoVect1LinkObjId="SW-221236_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-221238_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2225,-857 2242,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1502a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2242,-857 2242,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="34574@x" ObjectIDND1="34572@x" ObjectIDZND0="34570@1" Pin0InfoVect0LinkObjId="SW-221236_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-221238_0" Pin1InfoVect1LinkObjId="SW-221237_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2242,-857 2242,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1502c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2180,-857 2191,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1502e40@0" ObjectIDZND0="34574@0" Pin0InfoVect0LinkObjId="SW-221238_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1502e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2180,-857 2191,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1503770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2179,-807 2190,-807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_15039d0@0" ObjectIDZND0="34573@0" Pin0InfoVect0LinkObjId="SW-221235_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15039d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2179,-807 2190,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1504420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2242,-857 2242,-871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="34574@x" ObjectIDND1="34570@x" ObjectIDZND0="34572@0" Pin0InfoVect0LinkObjId="SW-221237_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-221238_0" Pin1InfoVect1LinkObjId="SW-221236_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2242,-857 2242,-871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1837720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2242,-779 2242,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="34571@1" ObjectIDZND0="34573@x" ObjectIDZND1="34570@x" Pin0InfoVect0LinkObjId="SW-221235_0" Pin0InfoVect1LinkObjId="SW-221236_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-221234_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2242,-779 2242,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1838090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2225,-807 2242,-807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="34573@1" ObjectIDZND0="34570@x" ObjectIDZND1="34571@x" Pin0InfoVect0LinkObjId="SW-221236_0" Pin0InfoVect1LinkObjId="SW-221234_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-221235_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2225,-807 2242,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1838280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2242,-807 2242,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="34573@x" ObjectIDND1="34571@x" ObjectIDZND0="34570@0" Pin0InfoVect0LinkObjId="SW-221236_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-221235_0" Pin1InfoVect1LinkObjId="SW-221234_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2242,-807 2242,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1838490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2242,-742 2242,-686 2118,-686 2118,-1034 1366,-1033 1366,-863 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" ObjectIDND0="34571@0" ObjectIDZND0="49424@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-221234_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2242,-742 2242,-686 2118,-686 2118,-1034 1366,-1033 1366,-863 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_184b980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="989,-211 969,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="4743@x" ObjectIDND1="5241@x" ObjectIDND2="g_11dc3a0@0" ObjectIDZND0="4744@1" Pin0InfoVect0LinkObjId="SW-29115_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29114_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_11dc3a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="989,-211 969,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a98780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="988,-232 988,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="4743@0" ObjectIDZND0="4744@x" ObjectIDZND1="5241@x" ObjectIDZND2="g_11dc3a0@0" Pin0InfoVect0LinkObjId="SW-29115_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_11dc3a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29114_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="988,-232 988,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1290070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="988,-211 989,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="4744@x" ObjectIDND1="4743@x" ObjectIDND2="g_11dc3a0@0" ObjectIDZND0="5241@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29115_0" Pin1InfoVect1LinkObjId="SW-29114_0" Pin1InfoVect2LinkObjId="g_11dc3a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="988,-211 989,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a8b590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="988,-211 1006,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="4744@x" ObjectIDND1="4743@x" ObjectIDND2="5241@x" ObjectIDZND0="g_11dc3a0@0" Pin0InfoVect0LinkObjId="g_11dc3a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29115_0" Pin1InfoVect1LinkObjId="SW-29114_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="988,-211 1006,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a8b780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1816,-208 1798,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="4741@x" ObjectIDND1="49440@x" ObjectIDND2="g_1203d10@0" ObjectIDZND0="4742@1" Pin0InfoVect0LinkObjId="SW-29113_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29112_0" Pin1InfoVect1LinkObjId="EC-CX_YX.067LD_0" Pin1InfoVect2LinkObjId="g_1203d10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1816,-208 1798,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bb8d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1816,-237 1816,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="4741@0" ObjectIDZND0="4742@x" ObjectIDZND1="49440@x" ObjectIDZND2="g_1203d10@0" Pin0InfoVect0LinkObjId="SW-29113_0" Pin0InfoVect1LinkObjId="EC-CX_YX.067LD_0" Pin0InfoVect2LinkObjId="g_1203d10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29112_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1816,-237 1816,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bd9240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1816,-208 1816,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="4742@x" ObjectIDND1="4741@x" ObjectIDND2="g_1203d10@0" ObjectIDZND0="49440@0" Pin0InfoVect0LinkObjId="EC-CX_YX.067LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29113_0" Pin1InfoVect1LinkObjId="SW-29112_0" Pin1InfoVect2LinkObjId="g_1203d10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1816,-208 1816,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1230e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1816,-208 1835,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="4742@x" ObjectIDND1="4741@x" ObjectIDND2="49440@x" ObjectIDZND0="g_1203d10@0" Pin0InfoVect0LinkObjId="g_1203d10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29113_0" Pin1InfoVect1LinkObjId="SW-29112_0" Pin1InfoVect2LinkObjId="EC-CX_YX.067LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1816,-208 1835,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c71eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="431,-213 447,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="4756@1" ObjectIDZND0="4755@x" ObjectIDZND1="15721@x" ObjectIDZND2="g_127d350@0" Pin0InfoVect0LinkObjId="SW-29126_0" Pin0InfoVect1LinkObjId="EC-CX_YX.CX_YX_087LD_0" Pin0InfoVect2LinkObjId="g_127d350_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29127_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="431,-213 447,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bd8f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-231 447,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="4755@0" ObjectIDZND0="4756@x" ObjectIDZND1="15721@x" ObjectIDZND2="g_127d350@0" Pin0InfoVect0LinkObjId="SW-29127_0" Pin0InfoVect1LinkObjId="EC-CX_YX.CX_YX_087LD_0" Pin0InfoVect2LinkObjId="g_127d350_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-29126_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="447,-231 447,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c52390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-213 446,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="4756@x" ObjectIDND1="4755@x" ObjectIDND2="g_127d350@0" ObjectIDZND0="15721@0" Pin0InfoVect0LinkObjId="EC-CX_YX.CX_YX_087LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29127_0" Pin1InfoVect1LinkObjId="SW-29126_0" Pin1InfoVect2LinkObjId="g_127d350_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="447,-213 446,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c82be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-213 464,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="4756@x" ObjectIDND1="4755@x" ObjectIDND2="15721@x" ObjectIDZND0="g_127d350@0" Pin0InfoVect0LinkObjId="g_127d350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-29127_0" Pin1InfoVect1LinkObjId="SW-29126_0" Pin1InfoVect2LinkObjId="EC-CX_YX.CX_YX_087LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="447,-213 464,-213 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="4654" cx="1864" cy="-429" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4654" cx="1948" cy="-429" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4659" cx="1226" cy="-331" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4653" cx="928" cy="-642" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4652" cx="801" cy="-667" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4660" cx="717" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4660" cx="1124" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4659" cx="1261" cy="-331" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4660" cx="537" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4653" cx="498" cy="-642" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4652" cx="447" cy="-667" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4652" cx="634" cy="-667" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4653" cx="685" cy="-642" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4652" cx="1029" cy="-667" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4653" cx="1080" cy="-642" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4652" cx="1340" cy="-667" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4653" cx="1391" cy="-642" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4653" cx="1218" cy="-642" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4652" cx="1167" cy="-667" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4660" cx="1054" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4654" cx="2034" cy="-429" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4652" cx="1477" cy="-667" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4653" cx="1528" cy="-642" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4658" cx="2042" cy="-668" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4658" cx="1943" cy="-668" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4658" cx="1840" cy="-668" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4658" cx="1758" cy="-668" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4658" cx="1747" cy="-668" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4659" cx="1816" cy="-331" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4660" cx="446" cy="-330" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-28838" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 257.000000 -869.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4618" ObjectName="DYN-CX_YX"/>
     <cge:Meas_Ref ObjectId="28838"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12ff6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 624.000000 -944.000000) translate(0,15)">狮云II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12537c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 320.000000 -634.000000) translate(0,15)">110kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_132d980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 320.000000 -691.000000) translate(0,15)">110kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_fbf460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1057.000000 -352.000000) translate(0,15)">10kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_122dea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1001.000000 -947.000000) translate(0,15)">狮云I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12cac50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1318.000000 -941.000000) translate(0,15)">电炉变出线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1392ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1954.000000 -655.000000) translate(0,15)">110kV专用线母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1393b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1850.000000 -452.000000) translate(0,15)">35kV专用线母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1394540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1760.000000 -353.000000) translate(0,15)">10kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11689a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 513.000000 -113.500000) translate(0,12)">精</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11689a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 513.000000 -113.500000) translate(0,27)">选</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11689a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 513.000000 -113.500000) translate(0,42)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11689a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 513.000000 -113.500000) translate(0,57)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11689a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 513.000000 -113.500000) translate(0,72)">所</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1169e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 598.000000 -114.000000) translate(0,12)">渣</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1169e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 598.000000 -114.000000) translate(0,27)">处</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1169e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 598.000000 -114.000000) translate(0,42)">理</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1169e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 598.000000 -114.000000) translate(0,57)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1169e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 598.000000 -114.000000) translate(0,72)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1169e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 598.000000 -114.000000) translate(0,87)">所</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116ac50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 777.000000 -113.500000) translate(0,12)">化</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116ac50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 777.000000 -113.500000) translate(0,27)">验</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116ac50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 777.000000 -113.500000) translate(0,42)">中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116ac50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 777.000000 -113.500000) translate(0,57)">心</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116ac50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 777.000000 -113.500000) translate(0,72)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116ac50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 777.000000 -113.500000) translate(0,87)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116ac50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 777.000000 -113.500000) translate(0,102)">所</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116bb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 954.000000 -113.500000) translate(0,12)">熔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116bb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 954.000000 -113.500000) translate(0,27)">炼</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116bb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 954.000000 -113.500000) translate(0,42)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116bb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 954.000000 -113.500000) translate(0,57)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116bb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 954.000000 -113.500000) translate(0,72)">所</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116c3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.000000 -113.500000) translate(0,12)">气</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116c3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.000000 -113.500000) translate(0,27)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116c3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.000000 -113.500000) translate(0,42)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116c3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.000000 -113.500000) translate(0,57)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116c3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.000000 -113.500000) translate(0,72)">所</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116d5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1519.000000 -114.000000) translate(0,12)">烟</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116d5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1519.000000 -114.000000) translate(0,27)">气</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116d5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1519.000000 -114.000000) translate(0,42)">涤</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116d5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1519.000000 -114.000000) translate(0,57)">机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116e0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1614.000000 -114.000000) translate(0,12)">脱</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116e0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1614.000000 -114.000000) translate(0,27)">硫</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116e0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1614.000000 -114.000000) translate(0,42)">尘</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116e0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1614.000000 -114.000000) translate(0,57)">除</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116e0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1614.000000 -114.000000) translate(0,72)">风</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116e0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1614.000000 -114.000000) translate(0,87)">机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116f4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 111.000000) translate(0,12)">生</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116f4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 111.000000) translate(0,27)">铁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116f4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 111.000000) translate(0,42)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116f4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 111.000000) translate(0,57)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116f4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 111.000000) translate(0,72)">所</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116fd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 -113.500000) translate(0,12)">原</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116fd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 -113.500000) translate(0,27)">料</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116fd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 -113.500000) translate(0,42)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116fd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 -113.500000) translate(0,57)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_116fd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 -113.500000) translate(0,72)">所</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1170610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1490.000000 112.000000) translate(0,12)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1170610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1490.000000 112.000000) translate(0,27)">炉</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1170610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1490.000000 112.000000) translate(0,42)">冷</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1170610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1490.000000 112.000000) translate(0,57)">却</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1170610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1490.000000 112.000000) translate(0,72)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1170610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1490.000000 112.000000) translate(0,87)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1170610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1490.000000 112.000000) translate(0,102)">所</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1171190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1610.000000 55.000000) translate(0,12)">10kV备用电源</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11724d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 669.000000 -781.000000) translate(0,12)">162</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1172d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.000000 -714.000000) translate(0,12)">1621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11731f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 692.000000 -713.000000) translate(0,12)">1622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1173430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 -774.000000) translate(0,12)">16217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1173770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 607.000000 -828.000000) translate(0,12)">16260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1173bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 605.000000 -887.000000) translate(0,12)">16267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1173e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 667.000000 -838.000000) translate(0,12)">1626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1174050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 454.000000 -714.000000) translate(0,12)">1021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1174290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 501.000000 -710.000000) translate(0,12)">1022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11744d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 480.000000 -845.000000) translate(0,12)">1026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1174710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 482.000000 -784.000000) translate(0,12)">102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1174950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 419.000000 -772.000000) translate(0,12)">10217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1174d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 419.000000 -826.000000) translate(0,12)">10267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1175040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 752.000000 -724.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1175620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 751.000000 -790.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11758a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 935.000000 -735.000000) translate(0,12)">1902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1175ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 808.000000 -741.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1175d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 879.000000 -718.000000) translate(0,12)">19020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1175f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 879.000000 -785.000000) translate(0,12)">19027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11761a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1064.000000 -781.000000) translate(0,12)">161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11763e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1036.000000 -716.000000) translate(0,12)">1611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1176620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1087.000000 -715.000000) translate(0,12)">1612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1176860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1062.000000 -838.000000) translate(0,12)">1616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1176aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1000.000000 -889.000000) translate(0,12)">16167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1176ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1002.000000 -826.000000) translate(0,12)">16160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1176f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1002.000000 -776.000000) translate(0,12)">16117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1177160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1375.000000 -781.000000) translate(0,12)">163</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1177380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1202.000000 -786.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11778d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1174.000000 -716.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1177b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1225.000000 -712.000000) translate(0,12)">1012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1177d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1200.000000 -847.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1177f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1140.000000 -774.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11781d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1139.000000 -828.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1178600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -715.000000) translate(0,12)">1631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11788c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1398.000000 -714.000000) translate(0,12)">1632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1178b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1373.000000 -838.000000) translate(0,12)">1636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1178d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1312.000000 -775.000000) translate(0,12)">16317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1178f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1313.000000 -825.000000) translate(0,12)">16360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11793b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1512.000000 -783.000000) translate(0,12)">104</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1179770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1510.000000 -840.000000) translate(0,12)">1046</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1179c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1484.000000 -717.000000) translate(0,12)">1041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1179e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1535.000000 -716.000000) translate(0,12)">1042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117a0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1450.000000 -777.000000) translate(0,12)">10417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117a2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1450.000000 -827.000000) translate(0,12)">10467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117a530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 363.000000 -532.000000) translate(0,12)">1020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117b0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 548.000000 -372.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117b4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1273.000000 -375.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117b6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 455.000000 -271.000000) translate(0,12)">087</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117bc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 385.000000 -239.000000) translate(0,12)">08767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117bee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 546.000000 -271.000000) translate(0,12)">086</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117c120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 483.000000 -241.000000) translate(0,12)">08667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117c360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 634.000000 -271.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117c630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 572.000000 -239.000000) translate(0,12)">08567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117cab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 726.000000 -271.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117ccf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 664.000000 -239.000000) translate(0,12)">08467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117cf30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 816.000000 -272.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117d170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 753.000000 -240.000000) translate(0,12)">08367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117d3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 906.000000 -270.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117d5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 843.000000 -238.000000) translate(0,12)">08267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117d830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 997.000000 -269.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117da70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 935.000000 -237.000000) translate(0,12)">08167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117dcb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1132.000000 -287.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117def0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1295.000000 -269.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117e130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1233.000000 -237.000000) translate(0,12)">06167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117e370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1384.000000 -267.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117e5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1321.000000 -235.000000) translate(0,12)">06267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117e7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1472.000000 -267.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117ea30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1409.000000 -235.000000) translate(0,12)">06367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117ec70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1499.000000 -236.000000) translate(0,12)">06467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117eeb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1561.000000 -268.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117f0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1649.000000 -266.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117f330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1587.000000 -234.000000) translate(0,12)">06567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117f570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1738.000000 -266.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117f7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1675.000000 -234.000000) translate(0,12)">06667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117f9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1826.000000 -266.000000) translate(0,12)">067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117fc30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1763.000000 -234.000000) translate(0,12)">06767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117fe70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1765.138299 -642.000000) translate(0,12)">1051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11800b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1705.138299 -626.000000) translate(0,12)">10517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11802f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1754.000000 -719.000000) translate(0,12)">1061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1180530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1692.000000 -770.000000) translate(0,12)">10617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1180770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1847.000000 -720.000000) translate(0,12)">1071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11809b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1785.000000 -775.000000) translate(0,12)">10717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1180bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1950.000000 -718.000000) translate(0,12)">1081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1180e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1888.000000 -773.000000) translate(0,12)">10817</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1181070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2049.000000 -716.000000) translate(0,12)">1091</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11812b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1987.000000 -771.000000) translate(0,12)">10917</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11814f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1802.000000 -539.000000) translate(0,12)">1050</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1181730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1874.022812 -392.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1181970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1956.933919 -389.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1181bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1951.933919 -293.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1181df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1899.000000 -270.000000) translate(0,12)">36217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1182030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2013.933919 -293.000000) translate(0,12)">3622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1182270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1959.000000 -270.000000) translate(0,12)">36227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11824b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2073.933919 -293.000000) translate(0,12)">3623</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11826f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2018.000000 -270.000000) translate(0,12)">36237</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1182930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 633.000000 -97.000000) translate(0,12)">F085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1182e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 817.000000 -96.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11830d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 997.000000 -104.000000) translate(0,12)">F081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1183310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 727.000000 89.000000) translate(0,12)">F084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1183550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -146.000000) translate(0,12)">401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1183980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1297.000000 -99.000000) translate(0,12)">F061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1183c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -101.000000) translate(0,12)">F062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1183e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1644.000000 -121.000000) translate(0,12)">F0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11840b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1414.000000 100.000000) translate(0,12)">F082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11842f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1471.000000 86.000000) translate(0,12)">F063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1184530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1525.000000 119.000000) translate(0,12)">F072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1184770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1524.000000 149.000000) translate(0,12)">F073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1456540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 573.000000 -535.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1456540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 573.000000 -535.000000) translate(0,27)">SZ10-12500/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1457a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1300.000000 -535.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1457a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1300.000000 -535.000000) translate(0,27)">SZ10-12500/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1459180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1688.000000 -557.000000) translate(0,12)">补偿变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1459e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 482.000000 -430.000000) translate(0,12)">00267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_145a0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1210.000000 -430.000000) translate(0,12)">00167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_145b170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1565.000000 82.000000) translate(0,12)">F071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_145b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -362.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_145b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -362.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_145b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -362.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_145b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -362.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_145b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -362.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_145b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -362.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_145b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -362.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_145b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -362.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_145b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -362.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_145b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -362.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_145b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -362.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_145b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -362.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_145b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -362.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_145b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -362.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_145b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -362.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_145b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -362.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_145b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -362.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_145b620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -362.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14676e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -40.000000 -841.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14676e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -40.000000 -841.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14676e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -40.000000 -841.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14676e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -40.000000 -841.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14676e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -40.000000 -841.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14676e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -40.000000 -841.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14676e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -40.000000 -841.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_146a700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 116.000000 -949.500000) translate(0,16)">云新变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1485510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2096.000000 -422.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14eb800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1084.000000 -522.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1500a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 76.000000 -2.000000) translate(0,12)">8720151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1502050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2136.000000 -680.000000) translate(0,15)">电炉变进线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_150e0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2251.000000 -767.000000) translate(0,12)">1031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1835ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2249.000000 -896.000000) translate(0,12)">1036</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1835d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2188.000000 -833.000000) translate(0,12)">10317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1835f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2189.000000 -883.000000) translate(0,12)">10367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18400f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2251.000000 -839.000000) translate(0,12)">103</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18423a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 832.000000 89.000000) translate(0,12)">066</text>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1379" x2="1379" y1="216" y2="216"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1377" x2="1377" y1="224" y2="224"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1392" x2="1392" y1="216" y2="216"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="2" stroke="rgb(145,145,145)" stroke-dasharray="10 5 " stroke-width="1" x1="588" x2="588" y1="-127" y2="9"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1223" x2="1223" y1="14" y2="14"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1227" x2="1227" y1="30" y2="30"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1242" x2="1242" y1="22" y2="22"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="2" stroke="rgb(145,145,145)" stroke-dasharray="10 5 " stroke-width="1" x1="1340" x2="1340" y1="-128" y2="8"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1490" x2="1490" y1="-9" y2="-9"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="2" stroke="rgb(145,145,145)" stroke-dasharray="10 5 " stroke-width="1" x1="1607" x2="1607" y1="-151" y2="-15"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1431" x2="1431" y1="-413" y2="-413"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_14899b0">
    <use class="BV-110KV" transform="matrix(0.769231 -0.000000 0.000000 -0.811321 738.000000 -816.000000)" xlink:href="#voltageTransformer:shape34"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_148d970">
    <use class="BV-110KV" transform="matrix(0.769231 -0.000000 0.000000 -0.811321 867.000000 -813.000000)" xlink:href="#voltageTransformer:shape34"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_YX.CX_YX_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="6905"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 503.000000 -480.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 503.000000 -480.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="4759" ObjectName="TF-CX_YX.CX_YX_2T"/>
    <cge:TPSR_Ref TObjectID="4759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_YX.CX_YX_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="6901"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1227.000000 -482.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1227.000000 -482.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="4758" ObjectName="TF-CX_YX.CX_YX_1T"/>
    <cge:TPSR_Ref TObjectID="4758"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.480000 -0.000000 0.000000 -0.566667 1042.000000 -170.000000)" xlink:href="#transformer2:shape1_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.480000 -0.000000 0.000000 -0.566667 1042.000000 -170.000000)" xlink:href="#transformer2:shape1_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_YX.CX_YX_3T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="7629"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.680000 -0.000000 0.000000 -0.811111 1741.000000 -501.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(0.680000 -0.000000 0.000000 -0.811111 1741.000000 -501.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="5237" ObjectName="TF-CX_YX.CX_YX_3T"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28887" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 545.000000 -964.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28887" ObjectName="CX_YX:CX_YX_102BK_I"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28879" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 692.000000 -964.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28879" ObjectName="CX_YX:CX_YX_162BK_I"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28875" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1078.000000 -964.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28875" ObjectName="CX_YX:CX_YX_161BK_I"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28883" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1218.000000 -966.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28883" ObjectName="CX_YX:CX_YX_101BK_I"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28891" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1368.000000 -964.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28891" ObjectName="CX_YX:CX_YX_103BK_I"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28895" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1526.000000 -965.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28895" ObjectName="CX_YX:CX_YX_104BK_I"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28923" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1820.000000 -409.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28923" ObjectName="CX_YX:CX_YX_361BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28924" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1820.000000 -393.500000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28924" ObjectName="CX_YX:CX_YX_361BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28925" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1820.000000 -378.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28925" ObjectName="CX_YX:CX_YX_361BK_I"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28927" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1901.000000 -407.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28927" ObjectName="CX_YX:CX_YX_362BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28928" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1901.000000 -392.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28928" ObjectName="CX_YX:CX_YX_362BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28929" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1901.000000 -377.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28929" ObjectName="CX_YX:CX_YX_362BK_I"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28907" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1747.000000 -998.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28907" ObjectName="CX_YX:CX_YX_1061SW_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28908" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1747.000000 -983.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28908" ObjectName="CX_YX:CX_YX_1061SW_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28909" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1747.000000 -968.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28909" ObjectName="CX_YX:CX_YX_1061SW_I"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28911" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1848.000000 -996.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28911" ObjectName="CX_YX:CX_YX_1062SW_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28912" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1848.000000 -981.500000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28912" ObjectName="CX_YX:CX_YX_1062SW_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28913" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1848.000000 -967.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28913" ObjectName="CX_YX:CX_YX_1062SW_I"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28915" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1943.000000 -998.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28915" ObjectName="CX_YX:CX_YX_1063SW_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28916" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1943.000000 -984.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28916" ObjectName="CX_YX:CX_YX_1063SW_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28917" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1943.000000 -970.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28917" ObjectName="CX_YX:CX_YX_1063SW_I"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28919" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2048.000000 -996.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28919" ObjectName="CX_YX:CX_YX_1064SW_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28920" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2048.000000 -981.500000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28920" ObjectName="CX_YX:CX_YX_1064SW_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-28921" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2048.000000 -967.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28921" ObjectName="CX_YX:CX_YX_1064SW_I"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 64.000000 -901.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62655" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 77.538462 -799.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62655" ObjectName="CX_YX:CX_YX_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79755" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 77.538462 -756.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79755" ObjectName="CX_YX:CX_YX_sumQ"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="76" y="-960"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="76" y="-960"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="27" y="-977"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="27" y="-977"/></g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="1552" cy="-98" fill="none" fillStyle="0" r="11.5" stroke="rgb(145,145,145)" stroke-width="1"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SS" endPointId="0" endStationName="CX_YX" flowDrawDirect="1" flowShape="0" id="AC-110kV.shiYunErHui" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="660,-920 660,-880 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48792" ObjectName="AC-110kV.shiYunErHui"/>
    <cge:TPSR_Ref TObjectID="48792_SS-35"/></metadata>
   <polyline fill="none" opacity="0" points="660,-920 660,-880 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SS" endPointId="0" endStationName="CX_YX" flowDrawDirect="1" flowShape="0" id="AC-110kV.shiyunIhui_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1056,-928 1056,-891 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9187" ObjectName="AC-110kV.shiyunIhui_line"/>
    <cge:TPSR_Ref TObjectID="9187_SS-35"/></metadata>
   <polyline fill="none" opacity="0" points="1056,-928 1056,-891 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YX" endPointId="0" endStationName="PAS_XN" flowDrawDirect="1" flowShape="0" id="AC-110kV.YunXinDianL_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1366,-880 1366,-847 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49424" ObjectName="AC-110kV.YunXinDianL_line"/>
    <cge:TPSR_Ref TObjectID="49424_SS-35"/></metadata>
   <polyline fill="none" opacity="0" points="1366,-880 1366,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YX" endPointId="0" endStationName="PAS_XN" flowDrawDirect="1" flowShape="0" id="AC-110kV.YunXin104_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1648,-659 1648,-717 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49427" ObjectName="AC-110kV.YunXin104_line"/>
    <cge:TPSR_Ref TObjectID="49427_SS-35"/></metadata>
   <polyline fill="none" opacity="0" points="1648,-659 1648,-717 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-28901" prefix="Ua  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 301.500000 -614.000000) translate(0,12)">Ua   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28901" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4653"/>
     <cge:Term_Ref ObjectID="6697"/>
    <cge:TPSR_Ref TObjectID="4653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-28900" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 301.500000 -614.000000) translate(0,27)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28900" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4653"/>
     <cge:Term_Ref ObjectID="6697"/>
    <cge:TPSR_Ref TObjectID="4653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-28902" prefix="Hz " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 301.500000 -614.000000) translate(0,42)">Hz  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28902" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4653"/>
     <cge:Term_Ref ObjectID="6697"/>
    <cge:TPSR_Ref TObjectID="4653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-28944" prefix="Ua  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1847.000000 -657.000000) translate(0,12)">Ua   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28944" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4658"/>
     <cge:Term_Ref ObjectID="6702"/>
    <cge:TPSR_Ref TObjectID="4658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-28943" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1847.000000 -657.000000) translate(0,27)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28943" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4658"/>
     <cge:Term_Ref ObjectID="6702"/>
    <cge:TPSR_Ref TObjectID="4658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-28945" prefix="Hz " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1847.000000 -657.000000) translate(0,42)">Hz  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28945" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4658"/>
     <cge:Term_Ref ObjectID="6702"/>
    <cge:TPSR_Ref TObjectID="4658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-29018" prefix="Ua  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 960.000000 -378.000000) translate(0,12)">Ua   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29018" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4660"/>
     <cge:Term_Ref ObjectID="6704"/>
    <cge:TPSR_Ref TObjectID="4660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-29017" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 960.000000 -378.000000) translate(0,27)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29017" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4660"/>
     <cge:Term_Ref ObjectID="6704"/>
    <cge:TPSR_Ref TObjectID="4660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-29019" prefix="Hz " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 960.000000 -378.000000) translate(0,42)">Hz  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29019" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4660"/>
     <cge:Term_Ref ObjectID="6704"/>
    <cge:TPSR_Ref TObjectID="4660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-29015" prefix="Ua  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1648.000000 -380.000000) translate(0,12)">Ua   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29015" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4659"/>
     <cge:Term_Ref ObjectID="6703"/>
    <cge:TPSR_Ref TObjectID="4659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-29014" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1648.000000 -380.000000) translate(0,27)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29014" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4659"/>
     <cge:Term_Ref ObjectID="6703"/>
    <cge:TPSR_Ref TObjectID="4659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-29016" prefix="Hz " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1648.000000 -380.000000) translate(0,42)">Hz  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29016" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4659"/>
     <cge:Term_Ref ObjectID="6703"/>
    <cge:TPSR_Ref TObjectID="4659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-28898" prefix="Ua  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 301.000000 -737.000000) translate(0,12)">Ua   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28898" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4652"/>
     <cge:Term_Ref ObjectID="6696"/>
    <cge:TPSR_Ref TObjectID="4652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-28897" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 301.000000 -737.000000) translate(0,27)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28897" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4652"/>
     <cge:Term_Ref ObjectID="6696"/>
    <cge:TPSR_Ref TObjectID="4652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-28899" prefix="Hz " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 301.000000 -737.000000) translate(0,42)">Hz  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28899" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4652"/>
     <cge:Term_Ref ObjectID="6696"/>
    <cge:TPSR_Ref TObjectID="4652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28950" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 593.000000 -398.000000) translate(0,12)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28950" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4727"/>
     <cge:Term_Ref ObjectID="6837"/>
    <cge:TPSR_Ref TObjectID="4727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28951" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 593.000000 -398.000000) translate(0,27)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28951" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4727"/>
     <cge:Term_Ref ObjectID="6837"/>
    <cge:TPSR_Ref TObjectID="4727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28952" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 593.000000 -398.000000) translate(0,42)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28952" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4727"/>
     <cge:Term_Ref ObjectID="6837"/>
    <cge:TPSR_Ref TObjectID="4727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-28953" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 593.000000 -398.000000) translate(0,57)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28953" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4727"/>
     <cge:Term_Ref ObjectID="6837"/>
    <cge:TPSR_Ref TObjectID="4727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28946" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1314.000000 -399.000000) translate(0,12)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28946" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4726"/>
     <cge:Term_Ref ObjectID="6835"/>
    <cge:TPSR_Ref TObjectID="4726"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28947" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1314.000000 -399.000000) translate(0,27)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28947" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4726"/>
     <cge:Term_Ref ObjectID="6835"/>
    <cge:TPSR_Ref TObjectID="4726"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28948" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1314.000000 -399.000000) translate(0,42)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28948" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4726"/>
     <cge:Term_Ref ObjectID="6835"/>
    <cge:TPSR_Ref TObjectID="4726"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-28949" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1314.000000 -399.000000) translate(0,57)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28949" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4726"/>
     <cge:Term_Ref ObjectID="6835"/>
    <cge:TPSR_Ref TObjectID="4726"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28877" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 692.000000 -999.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28877" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4668"/>
     <cge:Term_Ref ObjectID="6719"/>
    <cge:TPSR_Ref TObjectID="4668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28878" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 692.000000 -999.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28878" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4668"/>
     <cge:Term_Ref ObjectID="6719"/>
    <cge:TPSR_Ref TObjectID="4668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28885" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 545.000000 -998.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28885" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4682"/>
     <cge:Term_Ref ObjectID="6747"/>
    <cge:TPSR_Ref TObjectID="4682"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28886" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 545.000000 -998.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28886" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4682"/>
     <cge:Term_Ref ObjectID="6747"/>
    <cge:TPSR_Ref TObjectID="4682"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28893" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1526.000000 -999.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28893" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4695"/>
     <cge:Term_Ref ObjectID="6773"/>
    <cge:TPSR_Ref TObjectID="4695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28894" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1526.000000 -999.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28894" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4695"/>
     <cge:Term_Ref ObjectID="6773"/>
    <cge:TPSR_Ref TObjectID="4695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28889" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1368.000000 -998.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28889" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4689"/>
     <cge:Term_Ref ObjectID="6761"/>
    <cge:TPSR_Ref TObjectID="4689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28890" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1368.000000 -998.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28890" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4689"/>
     <cge:Term_Ref ObjectID="6761"/>
    <cge:TPSR_Ref TObjectID="4689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28881" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1217.000000 -999.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4675"/>
     <cge:Term_Ref ObjectID="6733"/>
    <cge:TPSR_Ref TObjectID="4675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28882" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1217.000000 -999.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28882" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4675"/>
     <cge:Term_Ref ObjectID="6733"/>
    <cge:TPSR_Ref TObjectID="4675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28873" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1078.000000 -997.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4661"/>
     <cge:Term_Ref ObjectID="6705"/>
    <cge:TPSR_Ref TObjectID="4661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28874" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1078.000000 -997.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4661"/>
     <cge:Term_Ref ObjectID="6705"/>
    <cge:TPSR_Ref TObjectID="4661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-221230" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2250.000000 -1029.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="221230" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34570"/>
     <cge:Term_Ref ObjectID="29438"/>
    <cge:TPSR_Ref TObjectID="34570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-221231" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2250.000000 -1029.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="221231" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34570"/>
     <cge:Term_Ref ObjectID="29438"/>
    <cge:TPSR_Ref TObjectID="34570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-221232" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2250.000000 -1029.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="221232" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34570"/>
     <cge:Term_Ref ObjectID="29438"/>
    <cge:TPSR_Ref TObjectID="34570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-29010" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 398.000000 -172.000000) translate(0,12)">29010.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29010" ObjectName="CX_YX.CX_YX_087BK:F"/>
     <cge:PSR_Ref ObjectID="4755"/>
     <cge:Term_Ref ObjectID="6893"/>
    <cge:TPSR_Ref TObjectID="4755"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-29011" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 398.000000 -172.000000) translate(0,27)">29011.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29011" ObjectName="CX_YX.CX_YX_087BK:F"/>
     <cge:PSR_Ref ObjectID="4755"/>
     <cge:Term_Ref ObjectID="6893"/>
    <cge:TPSR_Ref TObjectID="4755"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-29012" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 398.000000 -172.000000) translate(0,42)">29012.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29012" ObjectName="CX_YX.CX_YX_087BK:F"/>
     <cge:PSR_Ref ObjectID="4755"/>
     <cge:Term_Ref ObjectID="6893"/>
    <cge:TPSR_Ref TObjectID="4755"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-29006" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 480.000000 -172.000000) translate(0,12)">29006.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29006" ObjectName="CX_YX.CX_YX_086BK:F"/>
     <cge:PSR_Ref ObjectID="4753"/>
     <cge:Term_Ref ObjectID="6889"/>
    <cge:TPSR_Ref TObjectID="4753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-29007" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 480.000000 -172.000000) translate(0,27)">29007.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29007" ObjectName="CX_YX.CX_YX_086BK:F"/>
     <cge:PSR_Ref ObjectID="4753"/>
     <cge:Term_Ref ObjectID="6889"/>
    <cge:TPSR_Ref TObjectID="4753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-29008" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 480.000000 -172.000000) translate(0,42)">29008.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29008" ObjectName="CX_YX.CX_YX_086BK:F"/>
     <cge:PSR_Ref ObjectID="4753"/>
     <cge:Term_Ref ObjectID="6889"/>
    <cge:TPSR_Ref TObjectID="4753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-29002" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 567.000000 -173.000000) translate(0,12)">29002.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29002" ObjectName="CX_YX.CX_YX_085BK:F"/>
     <cge:PSR_Ref ObjectID="4751"/>
     <cge:Term_Ref ObjectID="6885"/>
    <cge:TPSR_Ref TObjectID="4751"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-29003" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 567.000000 -173.000000) translate(0,27)">29003.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29003" ObjectName="CX_YX.CX_YX_085BK:F"/>
     <cge:PSR_Ref ObjectID="4751"/>
     <cge:Term_Ref ObjectID="6885"/>
    <cge:TPSR_Ref TObjectID="4751"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-29004" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 567.000000 -173.000000) translate(0,42)">29004.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29004" ObjectName="CX_YX.CX_YX_085BK:F"/>
     <cge:PSR_Ref ObjectID="4751"/>
     <cge:Term_Ref ObjectID="6885"/>
    <cge:TPSR_Ref TObjectID="4751"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28998" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 660.000000 -174.000000) translate(0,12)">28998.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28998" ObjectName="CX_YX.CX_YX_084BK:F"/>
     <cge:PSR_Ref ObjectID="4749"/>
     <cge:Term_Ref ObjectID="6881"/>
    <cge:TPSR_Ref TObjectID="4749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28999" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 660.000000 -174.000000) translate(0,27)">28999.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28999" ObjectName="CX_YX.CX_YX_084BK:F"/>
     <cge:PSR_Ref ObjectID="4749"/>
     <cge:Term_Ref ObjectID="6881"/>
    <cge:TPSR_Ref TObjectID="4749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-29000" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 660.000000 -174.000000) translate(0,42)">29000.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="29000" ObjectName="CX_YX.CX_YX_084BK:F"/>
     <cge:PSR_Ref ObjectID="4749"/>
     <cge:Term_Ref ObjectID="6881"/>
    <cge:TPSR_Ref TObjectID="4749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28994" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 747.000000 -174.000000) translate(0,12)">28994.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28994" ObjectName="CX_YX.CX_YX_083BK:F"/>
     <cge:PSR_Ref ObjectID="4747"/>
     <cge:Term_Ref ObjectID="6877"/>
    <cge:TPSR_Ref TObjectID="4747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28995" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 747.000000 -174.000000) translate(0,27)">28995.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28995" ObjectName="CX_YX.CX_YX_083BK:F"/>
     <cge:PSR_Ref ObjectID="4747"/>
     <cge:Term_Ref ObjectID="6877"/>
    <cge:TPSR_Ref TObjectID="4747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28996" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 747.000000 -174.000000) translate(0,42)">28996.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28996" ObjectName="CX_YX.CX_YX_083BK:F"/>
     <cge:PSR_Ref ObjectID="4747"/>
     <cge:Term_Ref ObjectID="6877"/>
    <cge:TPSR_Ref TObjectID="4747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28990" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 841.000000 -175.000000) translate(0,12)">28990.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28990" ObjectName="CX_YX.CX_YX_082BK:F"/>
     <cge:PSR_Ref ObjectID="4745"/>
     <cge:Term_Ref ObjectID="6873"/>
    <cge:TPSR_Ref TObjectID="4745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28991" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 841.000000 -175.000000) translate(0,27)">28991.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28991" ObjectName="CX_YX.CX_YX_082BK:F"/>
     <cge:PSR_Ref ObjectID="4745"/>
     <cge:Term_Ref ObjectID="6873"/>
    <cge:TPSR_Ref TObjectID="4745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28992" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 841.000000 -175.000000) translate(0,42)">28992.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28992" ObjectName="CX_YX.CX_YX_082BK:F"/>
     <cge:PSR_Ref ObjectID="4745"/>
     <cge:Term_Ref ObjectID="6873"/>
    <cge:TPSR_Ref TObjectID="4745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28986" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 927.000000 -174.000000) translate(0,12)">28986.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28986" ObjectName="CX_YX.CX_YX_081BK:F"/>
     <cge:PSR_Ref ObjectID="4743"/>
     <cge:Term_Ref ObjectID="6869"/>
    <cge:TPSR_Ref TObjectID="4743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28987" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 927.000000 -174.000000) translate(0,27)">28987.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28987" ObjectName="CX_YX.CX_YX_081BK:F"/>
     <cge:PSR_Ref ObjectID="4743"/>
     <cge:Term_Ref ObjectID="6869"/>
    <cge:TPSR_Ref TObjectID="4743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28988" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 927.000000 -174.000000) translate(0,42)">28988.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28988" ObjectName="CX_YX.CX_YX_081BK:F"/>
     <cge:PSR_Ref ObjectID="4743"/>
     <cge:Term_Ref ObjectID="6869"/>
    <cge:TPSR_Ref TObjectID="4743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28954" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1105.000000 -224.000000) translate(0,12)">28954.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28954" ObjectName="CX_YX.CX_YX_012BK:F"/>
     <cge:PSR_Ref ObjectID="4728"/>
     <cge:Term_Ref ObjectID="6839"/>
    <cge:TPSR_Ref TObjectID="4728"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28955" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1105.000000 -224.000000) translate(0,27)">28955.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28955" ObjectName="CX_YX.CX_YX_012BK:F"/>
     <cge:PSR_Ref ObjectID="4728"/>
     <cge:Term_Ref ObjectID="6839"/>
    <cge:TPSR_Ref TObjectID="4728"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28956" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1105.000000 -224.000000) translate(0,42)">28956.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28956" ObjectName="CX_YX.CX_YX_012BK:F"/>
     <cge:PSR_Ref ObjectID="4728"/>
     <cge:Term_Ref ObjectID="6839"/>
    <cge:TPSR_Ref TObjectID="4728"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28958" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1222.000000 -174.000000) translate(0,12)">28958.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28958" ObjectName="CX_YX.CX_YX_061BK:F"/>
     <cge:PSR_Ref ObjectID="4729"/>
     <cge:Term_Ref ObjectID="6841"/>
    <cge:TPSR_Ref TObjectID="4729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28959" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1222.000000 -174.000000) translate(0,27)">28959.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28959" ObjectName="CX_YX.CX_YX_061BK:F"/>
     <cge:PSR_Ref ObjectID="4729"/>
     <cge:Term_Ref ObjectID="6841"/>
    <cge:TPSR_Ref TObjectID="4729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28960" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1222.000000 -174.000000) translate(0,42)">28960.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28960" ObjectName="CX_YX.CX_YX_061BK:F"/>
     <cge:PSR_Ref ObjectID="4729"/>
     <cge:Term_Ref ObjectID="6841"/>
    <cge:TPSR_Ref TObjectID="4729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28962" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1318.000000 -173.000000) translate(0,12)">28962.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28962" ObjectName="CX_YX.CX_YX_062BK:F"/>
     <cge:PSR_Ref ObjectID="4731"/>
     <cge:Term_Ref ObjectID="6845"/>
    <cge:TPSR_Ref TObjectID="4731"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28963" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1318.000000 -173.000000) translate(0,27)">28963.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28963" ObjectName="CX_YX.CX_YX_062BK:F"/>
     <cge:PSR_Ref ObjectID="4731"/>
     <cge:Term_Ref ObjectID="6845"/>
    <cge:TPSR_Ref TObjectID="4731"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28964" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1318.000000 -173.000000) translate(0,42)">28964.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28964" ObjectName="CX_YX.CX_YX_062BK:F"/>
     <cge:PSR_Ref ObjectID="4731"/>
     <cge:Term_Ref ObjectID="6845"/>
    <cge:TPSR_Ref TObjectID="4731"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28966" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1406.000000 -174.000000) translate(0,12)">28966.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28966" ObjectName="CX_YX.CX_YX_063BK:F"/>
     <cge:PSR_Ref ObjectID="4733"/>
     <cge:Term_Ref ObjectID="6849"/>
    <cge:TPSR_Ref TObjectID="4733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28967" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1406.000000 -174.000000) translate(0,27)">28967.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28967" ObjectName="CX_YX.CX_YX_063BK:F"/>
     <cge:PSR_Ref ObjectID="4733"/>
     <cge:Term_Ref ObjectID="6849"/>
    <cge:TPSR_Ref TObjectID="4733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28968" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1406.000000 -174.000000) translate(0,42)">28968.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28968" ObjectName="CX_YX.CX_YX_063BK:F"/>
     <cge:PSR_Ref ObjectID="4733"/>
     <cge:Term_Ref ObjectID="6849"/>
    <cge:TPSR_Ref TObjectID="4733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28970" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1496.000000 -175.000000) translate(0,12)">28970.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28970" ObjectName="CX_YX.CX_YX_064BK:F"/>
     <cge:PSR_Ref ObjectID="4735"/>
     <cge:Term_Ref ObjectID="6853"/>
    <cge:TPSR_Ref TObjectID="4735"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28971" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1496.000000 -175.000000) translate(0,27)">28971.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28971" ObjectName="CX_YX.CX_YX_064BK:F"/>
     <cge:PSR_Ref ObjectID="4735"/>
     <cge:Term_Ref ObjectID="6853"/>
    <cge:TPSR_Ref TObjectID="4735"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28972" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1496.000000 -175.000000) translate(0,42)">28972.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28972" ObjectName="CX_YX.CX_YX_064BK:F"/>
     <cge:PSR_Ref ObjectID="4735"/>
     <cge:Term_Ref ObjectID="6853"/>
    <cge:TPSR_Ref TObjectID="4735"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28974" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1580.000000 -174.000000) translate(0,12)">28974.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28974" ObjectName="CX_YX.CX_YX_065BK:F"/>
     <cge:PSR_Ref ObjectID="4737"/>
     <cge:Term_Ref ObjectID="6857"/>
    <cge:TPSR_Ref TObjectID="4737"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28975" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1580.000000 -174.000000) translate(0,27)">28975.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28975" ObjectName="CX_YX.CX_YX_065BK:F"/>
     <cge:PSR_Ref ObjectID="4737"/>
     <cge:Term_Ref ObjectID="6857"/>
    <cge:TPSR_Ref TObjectID="4737"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28976" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1580.000000 -174.000000) translate(0,42)">28976.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28976" ObjectName="CX_YX.CX_YX_065BK:F"/>
     <cge:PSR_Ref ObjectID="4737"/>
     <cge:Term_Ref ObjectID="6857"/>
    <cge:TPSR_Ref TObjectID="4737"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28978" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1671.000000 -176.000000) translate(0,12)">28978.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28978" ObjectName="CX_YX.CX_YX_066BK:F"/>
     <cge:PSR_Ref ObjectID="4739"/>
     <cge:Term_Ref ObjectID="6861"/>
    <cge:TPSR_Ref TObjectID="4739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28979" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1671.000000 -176.000000) translate(0,27)">28979.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28979" ObjectName="CX_YX.CX_YX_066BK:F"/>
     <cge:PSR_Ref ObjectID="4739"/>
     <cge:Term_Ref ObjectID="6861"/>
    <cge:TPSR_Ref TObjectID="4739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28980" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1671.000000 -176.000000) translate(0,42)">28980.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28980" ObjectName="CX_YX.CX_YX_066BK:F"/>
     <cge:PSR_Ref ObjectID="4739"/>
     <cge:Term_Ref ObjectID="6861"/>
    <cge:TPSR_Ref TObjectID="4739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-28982" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1758.000000 -175.000000) translate(0,12)">28982.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28982" ObjectName="CX_YX.CX_YX_067BK:F"/>
     <cge:PSR_Ref ObjectID="4741"/>
     <cge:Term_Ref ObjectID="6865"/>
    <cge:TPSR_Ref TObjectID="4741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-28983" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1758.000000 -175.000000) translate(0,27)">28983.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28983" ObjectName="CX_YX.CX_YX_067BK:F"/>
     <cge:PSR_Ref ObjectID="4741"/>
     <cge:Term_Ref ObjectID="6865"/>
    <cge:TPSR_Ref TObjectID="4741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-28984" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1758.000000 -175.000000) translate(0,42)">28984.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="28984" ObjectName="CX_YX.CX_YX_067BK:F"/>
     <cge:PSR_Ref ObjectID="4741"/>
     <cge:Term_Ref ObjectID="6865"/>
    <cge:TPSR_Ref TObjectID="4741"/></metadata>
   </g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-29051">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 464.000000 -755.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4682" ObjectName="SW-CX_YX.CX_YX_102BK"/>
     <cge:Meas_Ref ObjectId="29051"/>
    <cge:TPSR_Ref TObjectID="4682"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29037">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 651.000000 -752.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4668" ObjectName="SW-CX_YX.CX_YX_162BK"/>
     <cge:Meas_Ref ObjectId="29037"/>
    <cge:TPSR_Ref TObjectID="4668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29030">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1046.000000 -752.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4661" ObjectName="SW-CX_YX.CX_YX_161BK"/>
     <cge:Meas_Ref ObjectId="29030"/>
    <cge:TPSR_Ref TObjectID="4661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29058">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1357.000000 -752.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4689" ObjectName="SW-CX_YX.CX_YX_163BK"/>
     <cge:Meas_Ref ObjectId="29058"/>
    <cge:TPSR_Ref TObjectID="4689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29044">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1184.000000 -757.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4675" ObjectName="SW-CX_YX.CX_YX_101BK"/>
     <cge:Meas_Ref ObjectId="29044"/>
    <cge:TPSR_Ref TObjectID="4675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29064">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1494.000000 -754.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4695" ObjectName="SW-CX_YX.CX_YX_104BK"/>
     <cge:Meas_Ref ObjectId="29064"/>
    <cge:TPSR_Ref TObjectID="4695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29087">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1856.022812 -363.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4718" ObjectName="SW-CX_YX.CX_YX_361BK"/>
     <cge:Meas_Ref ObjectId="29087"/>
    <cge:TPSR_Ref TObjectID="4718"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29089">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1938.933919 -360.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4719" ObjectName="SW-CX_YX.CX_YX_362BK"/>
     <cge:Meas_Ref ObjectId="29089"/>
    <cge:TPSR_Ref TObjectID="4719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29126">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.761905 -0.000000 0.000000 -0.782178 439.000000 -224.653465)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4755" ObjectName="SW-CX_YX.CX_YX_087BK"/>
     <cge:Meas_Ref ObjectId="29126"/>
    <cge:TPSR_Ref TObjectID="4755"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29124">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.761905 -0.000000 0.000000 -0.782178 529.000000 -225.653465)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4753" ObjectName="SW-CX_YX.CX_YX_086BK"/>
     <cge:Meas_Ref ObjectId="29124"/>
    <cge:TPSR_Ref TObjectID="4753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29122">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.761905 -0.000000 0.000000 -0.782178 617.000000 -225.653465)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4751" ObjectName="SW-CX_YX.CX_YX_085BK"/>
     <cge:Meas_Ref ObjectId="29122"/>
    <cge:TPSR_Ref TObjectID="4751"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29120">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.761905 -0.000000 0.000000 -0.782178 709.000000 -225.653465)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4749" ObjectName="SW-CX_YX.CX_YX_084BK"/>
     <cge:Meas_Ref ObjectId="29120"/>
    <cge:TPSR_Ref TObjectID="4749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29118">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.761905 -0.000000 0.000000 -0.782178 799.000000 -226.653465)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4747" ObjectName="SW-CX_YX.CX_YX_083BK"/>
     <cge:Meas_Ref ObjectId="29118"/>
    <cge:TPSR_Ref TObjectID="4747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29116">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.761905 -0.000000 0.000000 -0.782178 889.000000 -223.653465)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4745" ObjectName="SW-CX_YX.CX_YX_082BK"/>
     <cge:Meas_Ref ObjectId="29116"/>
    <cge:TPSR_Ref TObjectID="4745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29114">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.761905 -0.000000 0.000000 -0.782178 980.000000 -225.653465)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4743" ObjectName="SW-CX_YX.CX_YX_081BK"/>
     <cge:Meas_Ref ObjectId="29114"/>
    <cge:TPSR_Ref TObjectID="4743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29099">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.761905 -0.000000 0.000000 -0.782178 1116.000000 -236.653465)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4728" ObjectName="SW-CX_YX.CX_YX_012BK"/>
     <cge:Meas_Ref ObjectId="29099"/>
    <cge:TPSR_Ref TObjectID="4728"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29100">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.761905 -0.000000 0.000000 -0.782178 1277.000000 -225.653465)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4729" ObjectName="SW-CX_YX.CX_YX_061BK"/>
     <cge:Meas_Ref ObjectId="29100"/>
    <cge:TPSR_Ref TObjectID="4729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29102">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.761905 -0.000000 0.000000 -0.782178 1366.000000 -225.653465)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4731" ObjectName="SW-CX_YX.CX_YX_062BK"/>
     <cge:Meas_Ref ObjectId="29102"/>
    <cge:TPSR_Ref TObjectID="4731"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29104">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.761905 -0.000000 0.000000 -0.782178 1454.000000 -228.653465)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4733" ObjectName="SW-CX_YX.CX_YX_063BK"/>
     <cge:Meas_Ref ObjectId="29104"/>
    <cge:TPSR_Ref TObjectID="4733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29106">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.761905 -0.000000 0.000000 -0.782178 1543.000000 -226.653465)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4735" ObjectName="SW-CX_YX.CX_YX_064BK"/>
     <cge:Meas_Ref ObjectId="29106"/>
    <cge:TPSR_Ref TObjectID="4735"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29108">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.761905 -0.000000 0.000000 -0.782178 1631.000000 -227.653465)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4737" ObjectName="SW-CX_YX.CX_YX_065BK"/>
     <cge:Meas_Ref ObjectId="29108"/>
    <cge:TPSR_Ref TObjectID="4737"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29110">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.761905 -0.000000 0.000000 -0.782178 1720.000000 -223.653465)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4739" ObjectName="SW-CX_YX.CX_YX_066BK"/>
     <cge:Meas_Ref ObjectId="29110"/>
    <cge:TPSR_Ref TObjectID="4739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29112">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.761905 -0.000000 0.000000 -0.782178 1808.000000 -230.653465)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4741" ObjectName="SW-CX_YX.CX_YX_067BK"/>
     <cge:Meas_Ref ObjectId="29112"/>
    <cge:TPSR_Ref TObjectID="4741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29098">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.904762 -0.000000 0.000000 -0.633663 528.000000 -332.099010)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4727" ObjectName="SW-CX_YX.CX_YX_002BK"/>
     <cge:Meas_Ref ObjectId="29098"/>
    <cge:TPSR_Ref TObjectID="4727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-29097">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.904762 -0.000000 0.000000 -0.633663 1252.000000 -335.099010)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4726" ObjectName="SW-CX_YX.CX_YX_001BK"/>
     <cge:Meas_Ref ObjectId="29097"/>
    <cge:TPSR_Ref TObjectID="4726"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-221236">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2233.000000 -810.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34570" ObjectName="SW-CX_YX.CX_YX_103BK"/>
     <cge:Meas_Ref ObjectId="221236"/>
    <cge:TPSR_Ref TObjectID="34570"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="76" y="-960"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="27" y="-977"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_127d350">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 457.000000 -160.000000)" xlink:href="#lightningRod:shape112"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_129d660">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 548.000000 -160.000000)" xlink:href="#lightningRod:shape112"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11505a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 636.000000 -160.000000)" xlink:href="#lightningRod:shape112"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1255280">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 728.000000 -160.000000)" xlink:href="#lightningRod:shape112"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11950d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 818.000000 -161.000000)" xlink:href="#lightningRod:shape112"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_119a590">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 908.000000 -159.000000)" xlink:href="#lightningRod:shape112"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11dc3a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 999.000000 -158.000000)" xlink:href="#lightningRod:shape112"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_113f900">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1047.000000 -224.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_124bd60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1157.000000 -174.000000)" xlink:href="#lightningRod:shape35"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12835f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1297.000000 -158.000000)" xlink:href="#lightningRod:shape112"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1288d50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1386.000000 -156.000000)" xlink:href="#lightningRod:shape112"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1144310">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1474.000000 -156.000000)" xlink:href="#lightningRod:shape112"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1149a70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1563.000000 -157.000000)" xlink:href="#lightningRod:shape112"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11f90b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1651.000000 -155.000000)" xlink:href="#lightningRod:shape112"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11fe5b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1740.000000 -155.000000)" xlink:href="#lightningRod:shape112"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1203d10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1828.000000 -155.000000)" xlink:href="#lightningRod:shape112"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11b0500">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1758.000000 -924.000000)" xlink:href="#lightningRod:shape113"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11b9100">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2053.000000 -925.000000)" xlink:href="#lightningRod:shape113"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1237f10">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1954.000000 -927.000000)" xlink:href="#lightningRod:shape113"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1240d70">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1851.000000 -929.000000)" xlink:href="#lightningRod:shape113"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11aa2a0">
    <use class="BV-35KV" transform="matrix(0.740000 -0.000000 0.000000 -1.000000 1928.933919 -41.000000)" xlink:href="#lightningRod:shape115"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11af430">
    <use class="BV-35KV" transform="matrix(0.740000 -0.000000 0.000000 -1.000000 1990.933919 -39.000000)" xlink:href="#lightningRod:shape115"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_121c700">
    <use class="BV-35KV" transform="matrix(0.740000 -0.000000 0.000000 -1.000000 2049.933919 -42.000000)" xlink:href="#lightningRod:shape115"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_138c390">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1827.022812 -62.000000)" xlink:href="#lightningRod:shape199"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_138f9a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1985.138299 -332.000000)" xlink:href="#lightningRod:shape198"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13949c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 613.000000 -13.000000)" xlink:href="#lightningRod:shape120"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11c1ea0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 795.000000 -17.000000)" xlink:href="#lightningRod:shape120"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11c59d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 976.000000 -17.000000)" xlink:href="#lightningRod:shape120"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11c96a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 705.000000 172.000000)" xlink:href="#lightningRod:shape120"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11cd1d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 813.000000 173.000000)" xlink:href="#lightningRod:shape120"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11d0ea0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1399.000000 183.000000)" xlink:href="#lightningRod:shape120"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11d4f20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1273.000000 -19.000000)" xlink:href="#lightningRod:shape120"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1154f20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1362.000000 -21.000000)" xlink:href="#lightningRod:shape120"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1160db0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1505.000000 214.000000)" xlink:href="#lightningRod:shape120"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11868b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 574.000000 -392.000000)" xlink:href="#lightningRod:shape196"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1189560">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1299.000000 -397.000000)" xlink:href="#lightningRod:shape196"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1487110">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1086.000000 -157.000000)" xlink:href="#lightningRod:shape112"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_148be70">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 813.000000 -812.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_148fe30">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 942.000000 -809.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14e9ec0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1144.000000 -469.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14eaa90">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1169.000000 -472.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14f0bc0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 423.000000 -489.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14f1790">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 447.000000 -491.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_YX"/>
</svg>