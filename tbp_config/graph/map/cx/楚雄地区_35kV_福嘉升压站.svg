<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-49" aopId="0" id="thSvg" viewBox="3025 -1240 2247 1243">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line stroke-width="0.5" x1="15" x2="1" y1="35" y2="10"/>
    <line stroke-width="0.5" x1="14" x2="14" y1="9" y2="9"/>
    <line stroke-width="0.5" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line stroke-width="0.5" x1="15" x2="1" y1="35" y2="10"/>
    <line stroke-width="0.5" x1="14" x2="14" y1="9" y2="9"/>
    <line stroke-width="0.5" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="earth:shape3">
    <line stroke-width="0.185606" x1="29" x2="29" y1="7" y2="11"/>
    <line stroke-width="0.226608" x1="4" x2="22" y1="9" y2="9"/>
    <line stroke-width="0.226608" x1="22" x2="22" y1="0" y2="18"/>
    <line stroke-width="0.226608" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="earth:shape1">
    <line stroke-width="0.185606" x1="7" x2="11" y1="30" y2="30"/>
    <line stroke-width="0.226608" x1="9" x2="9" y1="5" y2="23"/>
    <line stroke-width="0.226608" x1="0" x2="18" y1="23" y2="23"/>
    <line stroke-width="0.226608" x1="6" x2="13" y1="26" y2="26"/>
   </symbol>
   <symbol id="hydroGenerator:shape2">
    <polyline fill="none" points="12,27 11,28 12,28 12,29 12,30 13,31 13,32 14,33 15,33 15,34 16,34 17,35 18,35 19,35 20,35 21,35 22,34 23,34 24,33 25,33 25,32 26,31 26,30 27,29 27,28 27,28 27,27 "/>
    <circle cx="27" cy="27" r="26.5"/>
    <polyline fill="none" points="28,27 28,26 28,25 28,24 29,23 29,22 30,21 30,21 31,20 32,20 33,19 34,19 35,19 36,18 37,19 38,19 39,19 40,20 40,20 41,21 42,21 42,22 43,23 43,24 43,25 44,26 43,27 "/>
   </symbol>
   <symbol id="lightningRod:shape38">
    <rect height="8" stroke-width="0.375" width="18" x="21" y="3"/>
    <line stroke-width="0.139205" x1="1" x2="1" y1="8" y2="5"/>
    <line stroke-width="0.17576" x1="4" x2="4" y1="10" y2="4"/>
    <line stroke-width="0.17576" x1="7" x2="7" y1="14" y2="0"/>
    <line stroke-width="0.17576" x1="21" x2="7" y1="7" y2="7"/>
    <line stroke-width="0.375" x1="46" x2="26" y1="7" y2="7"/>
    <line stroke-width="0.375" x1="28" x2="26" y1="9" y2="7"/>
    <line stroke-width="0.375" x1="26" x2="28" y1="7" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line stroke-width="0.5" x1="6" x2="6" y1="50" y2="42"/>
    <line stroke-width="0.5" x1="3" x2="9" y1="41" y2="41"/>
    <polyline fill="none" points="27,39 5,17 5,5 "/>
    <rect height="4" stroke-width="0.5" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <line stroke-width="1" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="1"/>
    <line stroke-width="1" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape113">
    <ellipse cx="12" cy="15" rx="11" ry="12.5" stroke-width="0.610862"/>
    <line stroke-width="0.666549" x1="8" x2="12" y1="18" y2="14"/>
    <line stroke-width="0.666554" x1="12" x2="12" y1="14" y2="10"/>
    <line stroke-width="0.666549" x1="16" x2="12" y1="18" y2="14"/>
    <ellipse cx="12" cy="35" rx="11" ry="12" stroke-width="0.610862"/>
    <line stroke-width="0.666555" x1="12" x2="17" y1="41" y2="35"/>
    <line stroke-width="0.666555" x1="7" x2="17" y1="35" y2="35"/>
    <line stroke-width="0.666555" x1="12" x2="7" y1="41" y2="35"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line stroke-width="0.162432" x1="7" x2="15" y1="48" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line stroke-width="0.162432" x1="15" x2="15" y1="51" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line stroke-width="0.162432" x1="7" x2="15" y1="48" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line stroke-width="0.162432" x1="15" x2="15" y1="51" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line stroke-width="0.162432" x1="17" x2="0" y1="34" y2="26"/>
    <line stroke-width="0.234885" x1="-9" x2="0" y1="26" y2="26"/>
    <line stroke-width="0.1875" x1="18" x2="18" y1="27" y2="25"/>
    <line stroke-width="0.234885" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line stroke-width="0.234885" x1="-8" x2="0" y1="26" y2="26"/>
    <line stroke-width="0.1875" x1="18" x2="18" y1="24" y2="27"/>
    <line stroke-width="0.234885" x1="19" x2="27" y1="26" y2="26"/>
    <line stroke-width="0.162432" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line stroke-width="0.234885" x1="18" x2="27" y1="26" y2="26"/>
    <line stroke-width="0.1875" x1="18" x2="18" y1="27" y2="25"/>
    <line stroke-width="0.234885" x1="-9" x2="0" y1="26" y2="26"/>
    <line stroke-width="0.162432" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line stroke-width="0.234885" x1="-8" x2="0" y1="26" y2="26"/>
    <line stroke-width="0.1875" x1="18" x2="18" y1="24" y2="27"/>
    <line stroke-width="0.234885" x1="19" x2="27" y1="26" y2="26"/>
    <line stroke-width="0.162432" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape17_0">
    <line stroke-width="0.162432" x1="37" x2="15" y1="13" y2="5"/>
    <line stroke-width="0.125" x1="38" x2="38" y1="7" y2="4"/>
    <line stroke-width="0.215311" x1="38" x2="47" y1="5" y2="5"/>
    <line stroke-width="0.215311" x1="6" x2="15" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape17_1">
    <line stroke-width="0.162432" x1="37" x2="15" y1="5" y2="5"/>
    <line stroke-width="0.125" x1="38" x2="38" y1="7" y2="4"/>
    <line stroke-width="0.215311" x1="38" x2="47" y1="5" y2="5"/>
    <line stroke-width="0.215311" x1="6" x2="15" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape17-UnNor1">
    <line stroke-width="0.162432" x1="15" x2="37" y1="13" y2="5"/>
    <line stroke-width="0.125" x1="14" x2="14" y1="7" y2="4"/>
    <line stroke-width="0.215311" x1="14" x2="5" y1="5" y2="5"/>
    <line stroke-width="0.215311" x1="46" x2="37" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape17-UnNor2">
    <line stroke-width="0.162432" x1="37" x2="15" y1="5" y2="5"/>
    <line stroke-width="0.125" x1="38" x2="38" y1="7" y2="4"/>
    <line stroke-width="0.215311" x1="38" x2="47" y1="5" y2="5"/>
    <line stroke-width="0.215311" x1="6" x2="15" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer:shape1_0">
    <circle cx="26" cy="29" r="24.5"/>
    <line stroke-width="0.260204" x1="25" x2="25" y1="15" y2="24"/>
    <line stroke-width="0.260204" x1="33" x2="25" y1="31" y2="24"/>
    <line stroke-width="0.260204" x1="25" x2="18" y1="24" y2="31"/>
   </symbol>
   <symbol id="transformer:shape1_1">
    <circle cx="26" cy="61" r="25"/>
    <line stroke-width="0.260204" x1="25" x2="25" y1="57" y2="66"/>
    <line stroke-width="0.260204" x1="33" x2="25" y1="73" y2="66"/>
    <line stroke-width="0.260204" x1="25" x2="18" y1="66" y2="73"/>
   </symbol>
   <symbol id="transformer:shape1-2">
    <circle cx="56" cy="45" r="25"/>
    <line stroke-width="0.260204" x1="57" x2="57" y1="54" y2="37"/>
    <line stroke-width="0.260204" x1="72" x2="57" y1="45" y2="37"/>
    <line stroke-width="0.260204" x1="72" x2="57" y1="45" y2="54"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" rx="13" ry="12.5"/>
    <line stroke-width="0.132653" x1="9" x2="13" y1="40" y2="36"/>
    <line stroke-width="0.132653" x1="13" x2="17" y1="36" y2="40"/>
    <line stroke-width="0.132653" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" r="13"/>
    <line stroke-width="0.132653" x1="9" x2="13" y1="20" y2="16"/>
    <line stroke-width="0.132653" x1="13" x2="17" y1="16" y2="20"/>
    <line stroke-width="0.132653" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="voltageTransformer:shape6">
    <circle cx="18" cy="15" r="6"/>
    <circle cx="11" cy="11" r="6"/>
    <circle cx="18" cy="7" r="6"/>
   </symbol>
   <symbol id="voltageTransformer:shape11">
    <line stroke-width="0.36322" x1="18" x2="15" y1="11" y2="9"/>
    <line stroke-width="0.36322" x1="13" x2="15" y1="11" y2="9"/>
    <circle cx="15" cy="19" r="6"/>
    <circle cx="24" cy="16" r="6"/>
    <ellipse cx="15" cy="11" rx="6.5" ry="6"/>
    <circle cx="7" cy="16" r="6"/>
    <line stroke-width="0.36322" x1="22" x2="24" y1="18" y2="16"/>
    <line stroke-width="0.363235" x1="25" x2="25" y1="16" y2="14"/>
    <line stroke-width="0.36322" x1="27" x2="24" y1="18" y2="16"/>
    <line stroke-width="0.36322" x1="13" x2="15" y1="22" y2="20"/>
    <line stroke-width="0.363235" x1="16" x2="16" y1="20" y2="18"/>
    <line stroke-width="0.36322" x1="18" x2="15" y1="22" y2="20"/>
    <line stroke-width="0.375009" x1="6" x2="9" y1="19" y2="15"/>
    <line stroke-width="0.373778" x1="6" x2="3" y1="19" y2="14"/>
    <line stroke-width="0.36323" x1="3" x2="9" y1="15" y2="15"/>
    <line stroke-width="0.363235" x1="16" x2="16" y1="9" y2="7"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    
   </symbol>
   <symbol id="Tag:shape27">
    
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="0.5" width="111" x="0" y="0"/>
    <line stroke="rgb(50,205,50)" stroke-width="1.5" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)"/>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(127,127,127);fill:none}
.BKBV-0KV { stroke:rgb(127,127,127);fill:rgb(127,127,127)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(0,72,216);fill:none}
.BKBV-10KV { stroke:rgb(0,72,216);fill:rgb(0,72,216)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(0,255,0);fill:none}
.BKBV-20KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(213,0,0);fill:none}
.BKBV-110KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-220KV { stroke:rgb(255,0,255);fill:none}
.BKBV-220KV { stroke:rgb(255,0,255);fill:rgb(255,0,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(255,0,0);fill:none}
.BKBV-500KV { stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.BV-750KV { stroke:rgb(255,0,0);fill:none}
.BKBV-750KV { stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.BV-22KV { stroke:rgb(255,255,255);fill:none}
.BKBV-22KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-380KV { stroke:rgb(255,255,255);fill:none}
.BKBV-380KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1253" width="2257" x="3020" y="-1245"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(255,0,0)" stroke-width="0.5" width="360" x="3118" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(255,0,0)" stroke-width="0.5" width="360" x="3117" y="-1080"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(255,0,0)" stroke-width="0.5" width="360" x="3117" y="-597"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(127,127,127)" stroke-width="0.208333" width="14" x="4346" y="-631"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(127,127,127)" stroke-width="0.208333" width="26" x="3958" y="-157"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4416.582328 -941.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43209">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4144.000000 -498.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7245" ObjectName="SW-CX_FJ.CX_FJ_3011SW"/>
     <cge:Meas_Ref ObjectId="43209"/>
    <cge:TPSR_Ref TObjectID="7245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43210">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4185.000000 -549.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7246" ObjectName="SW-CX_FJ.CX_FJ_30117SW"/>
     <cge:Meas_Ref ObjectId="43210"/>
    <cge:TPSR_Ref TObjectID="7246"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4144.000000 -419.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4186.000000 -398.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4144.000000 -306.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4186.000000 -286.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4186.000000 -348.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3604.000000 -49.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3605.000000 -166.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3605.000000 -269.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3949.000000 -33.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3971.000000 -75.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4034.000000 -76.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4081.000000 -33.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4104.000000 -75.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3950.000000 -136.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4337.000000 -418.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4380.000000 -398.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4379.000000 -350.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4337.000000 -305.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4379.000000 -288.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4552.000000 -56.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4511.000000 -150.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4502.000000 -122.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4499.000000 -185.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4498.000000 -249.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4145.000000 -815.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4145.000000 -930.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4509.582328 -941.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4605.582328 -943.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4652.000000 -60.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4612.000000 -670.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5147.000000 -879.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4798.000000 -845.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5148.000000 -753.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5148.000000 -628.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5148.000000 -333.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5149.000000 -207.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5149.000000 -82.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5147.000000 -465.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4796.000000 -430.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4337.000000 -497.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4325.000000 -598.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3605.000000 -464.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3606.000000 -581.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3606.000000 -684.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3605.000000 -783.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3887.000000 -429.000000)" xlink:href="#switch2:shape17-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3886.000000 -266.000000)" xlink:href="#switch2:shape17-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3605.000000 -933.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3606.000000 -1050.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3606.000000 -1153.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3889.000000 -936.000000)" xlink:href="#switch2:shape17-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3888.000000 -773.000000)" xlink:href="#switch2:shape17-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4186.000000 -860.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4188.000000 -917.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4187.000000 -988.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="46b9890">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4013.000000 -141.000000)" xlink:href="#voltageTransformer:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="47b9460">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -638.000000)" xlink:href="#voltageTransformer:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3802.000000 -21.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3802.000000 -21.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3802.000000 -138.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3802.000000 -138.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3801.000000 -241.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3801.000000 -241.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4445.000000 -878.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4445.000000 -878.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4538.000000 -879.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4538.000000 -879.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4634.000000 -880.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4634.000000 -880.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4952.000000 -907.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4952.000000 -907.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4953.000000 -781.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4953.000000 -781.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4953.000000 -656.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4953.000000 -656.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4953.000000 -361.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4953.000000 -361.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4954.000000 -235.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4954.000000 -235.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4954.000000 -110.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4954.000000 -110.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4952.000000 -493.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4952.000000 -493.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3803.000000 -436.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3803.000000 -436.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3803.000000 -553.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3803.000000 -553.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3802.000000 -656.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3802.000000 -656.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3801.000000 -755.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3801.000000 -755.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3803.000000 -905.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3803.000000 -905.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3803.000000 -1022.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3803.000000 -1022.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3802.000000 -1125.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3802.000000 -1125.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3510.500000 -62.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3511.500000 -179.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3511.500000 -282.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4460.500000 -1117.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4553.500000 -1117.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4649.500000 -1119.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5241.500000 -865.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5242.500000 -739.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5242.500000 -614.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5242.500000 -319.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5243.500000 -193.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5243.500000 -68.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5241.500000 -451.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3511.500000 -477.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3512.500000 -594.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3512.500000 -697.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3511.500000 -796.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3511.500000 -946.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3512.500000 -1063.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3512.500000 -1166.500000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="3eac8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-641 4159,-618 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="7244@1" Pin0InfoVect0LinkObjId="SW-43207_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-641 4159,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="4095530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4158,-576 4176,-576 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7244@x" ObjectIDND1="7245@x" ObjectIDZND0="7246@0" Pin0InfoVect0LinkObjId="SW-43210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43207_0" Pin1InfoVect1LinkObjId="SW-43209_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4158,-576 4176,-576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="43c8020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4212,-575 4222,-575 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7246@1" ObjectIDZND0="4785250@0" Pin0InfoVect0LinkObjId="4785250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43210_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4212,-575 4222,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="2fc0930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-591 4159,-576 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="7244@0" ObjectIDZND0="7246@x" ObjectIDZND1="7245@x" Pin0InfoVect0LinkObjId="SW-43210_0" Pin0InfoVect1LinkObjId="SW-43209_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43207_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-591 4159,-576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="2ae84a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-493 4159,-520 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="7245@0" Pin0InfoVect0LinkObjId="SW-43209_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-493 4159,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="27a6f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-556 4159,-576 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="7245@1" ObjectIDZND0="7246@x" ObjectIDZND1="7244@x" Pin0InfoVect0LinkObjId="SW-43210_0" Pin0InfoVect1LinkObjId="SW-43207_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43209_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-556 4159,-576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2b0ba40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-493 4159,-477 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-493 4159,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="476a7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-425 4177,-425 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-425 4177,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3e94160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4213,-424 4223,-424 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="47a3830@0" Pin0InfoVect0LinkObjId="47a3830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4213,-424 4223,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="41b30d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-425 4159,-413 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-425 4159,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3be6320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-441 4159,-425 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-441 4159,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="46b5790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-313 4177,-313 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="4001400@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="4001400_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-313 4177,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="49dbbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4213,-312 4223,-312 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="41535c0@0" Pin0InfoVect0LinkObjId="41535c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4213,-312 4223,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="49c1c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-328 4159,-313 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="4001400@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="4001400_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-328 4159,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3bb1e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-375 4177,-375 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-375 4177,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3e1a280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4213,-374 4223,-374 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="490ba30@0" Pin0InfoVect0LinkObjId="490ba30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4213,-374 4223,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="48f2230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-386 4159,-375 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-386 4159,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="460bad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-375 4159,-364 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-375 4159,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4600cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-287 4122,-287 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="4001400@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="4001400_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-287 4122,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="39645f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-313 4159,-287 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="4001400@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="4001400_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-313 4159,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="42c7cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-265 4122,-265 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="4001400@0" Pin0InfoVect0LinkObjId="4001400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-265 4122,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3eda330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-34 3854,-34 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="3c4ce30@0" Pin0InfoVect0LinkObjId="3c4ce30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3871,-34 3854,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="477dae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-34 3766,-54 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="445a8c0@0" Pin0InfoVect0LinkObjId="445a8c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-34 3766,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="41a3a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3679,-34 3679,-54 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="4685340@0" ObjectIDND1="0@x" ObjectIDZND0="2d88200@0" Pin0InfoVect0LinkObjId="2d88200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="4685340_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3679,-34 3679,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="49c4cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3701,-34 3679,-34 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4685340@1" ObjectIDZND0="2d88200@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="2d88200_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="4685340_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3701,-34 3679,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="47970b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3679,-34 3662,-34 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="4685340@0" ObjectIDND1="2d88200@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="4685340_0" Pin1InfoVect1LinkObjId="2d88200_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3679,-34 3662,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3ea4670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3626,-34 3614,-34 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="34ab050@0" Pin0InfoVect0LinkObjId="34ab050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3626,-34 3614,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3c97300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3575,-34 3563,-34 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="34ab050@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="34ab050_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3575,-34 3563,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="40e4940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-151 3855,-151 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="2f9e890@0" Pin0InfoVect0LinkObjId="2f9e890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3871,-151 3855,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3dcf840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-151 3766,-171 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="3e96990@0" Pin0InfoVect0LinkObjId="3e96990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-151 3766,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="32e3870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-151 3680,-171 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="4489f60@0" ObjectIDND1="0@x" ObjectIDZND0="422d1e0@0" Pin0InfoVect0LinkObjId="422d1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="4489f60_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-151 3680,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="494a0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3702,-151 3680,-151 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4489f60@1" ObjectIDZND0="422d1e0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="422d1e0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="4489f60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3702,-151 3680,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="465ddb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-151 3663,-151 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="4489f60@0" ObjectIDND1="422d1e0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="4489f60_0" Pin1InfoVect1LinkObjId="422d1e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-151 3663,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="335b010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-151 3615,-151 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="494ad10@0" Pin0InfoVect0LinkObjId="494ad10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-151 3615,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2d82ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3576,-151 3564,-151 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="494ad10@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="494ad10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3576,-151 3564,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2ff6d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-253 3855,-253 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="40015e0@0" Pin0InfoVect0LinkObjId="40015e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3871,-253 3855,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3467aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3765,-254 3765,-273 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="41a4000@0" Pin0InfoVect0LinkObjId="41a4000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3765,-254 3765,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="45e4680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-254 3680,-274 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="47b1620@0" ObjectIDND1="0@x" ObjectIDZND0="48efe20@0" Pin0InfoVect0LinkObjId="48efe20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="47b1620_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-254 3680,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2eb1b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3702,-254 3680,-254 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47b1620@1" ObjectIDZND0="48efe20@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="48efe20_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="47b1620_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3702,-254 3680,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="307c5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-254 3663,-254 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="47b1620@0" ObjectIDND1="48efe20@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="47b1620_0" Pin1InfoVect1LinkObjId="48efe20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-254 3663,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2fcd370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-254 3615,-254 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="45f9c00@0" Pin0InfoVect0LinkObjId="45f9c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-254 3615,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2c49ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3576,-254 3564,-254 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="45f9c00@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="45f9c00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3576,-254 3564,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2112cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-48 3891,-48 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3871,-48 3891,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2796240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-48 3944,-66 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-48 3944,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="26926d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3945,-102 3945,-112 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="418d6d0@0" Pin0InfoVect0LinkObjId="418d6d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3945,-102 3945,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4908280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3927,-48 3944,-48 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3927,-48 3944,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4632320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3963,-48 3944,-48 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3963,-48 3944,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2fe3890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-49 4007,-67 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-49 4007,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2f452b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4008,-103 4008,-113 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="418d8d0@0" Pin0InfoVect0LinkObjId="418d8d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4008,-103 4008,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4368960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3990,-48 4007,-48 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3990,-48 4007,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="434bf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-48 4023,-48 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-48 4023,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="43caf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-48 4077,-66 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="4001400@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="4001400_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-48 4077,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="35145c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-102 4078,-112 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="477d1b0@0" Pin0InfoVect0LinkObjId="477d1b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-102 4078,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="48ee340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4059,-48 4077,-48 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="4001400@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="4001400_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4059,-48 4077,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4941f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-151 3943,-173 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="46b9890@0" ObjectIDZND0="43ce750@0" Pin0InfoVect0LinkObjId="43ce750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="46b9890_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-151 3943,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="42cfb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3928,-151 3943,-151 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="46b9890@0" ObjectIDZND1="43ce750@0" Pin0InfoVect0LinkObjId="46b9890_0" Pin0InfoVect1LinkObjId="43ce750_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3928,-151 3943,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2fa01f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-151 4018,-151 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="0@x" ObjectIDND1="43ce750@0" ObjectIDZND0="46b9890@0" Pin0InfoVect0LinkObjId="46b9890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="43ce750_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-151 4018,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="47a9930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3892,-151 3871,-151 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3892,-151 3871,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2f54d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-493 4352,-476 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-493 4352,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2607cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-424 4371,-424 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-424 4371,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="411b810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4407,-424 4417,-424 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="48942a0@0" Pin0InfoVect0LinkObjId="48942a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4407,-424 4417,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="449b4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-440 4352,-424 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-440 4352,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="27b0ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-424 4352,-411 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-424 4352,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="46c1610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-376 4370,-376 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-376 4370,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="500f5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4406,-376 4416,-376 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="434c140@0" Pin0InfoVect0LinkObjId="434c140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4406,-376 4416,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4196a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-384 4352,-376 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-384 4352,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2ea3350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-376 4352,-363 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-376 4352,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="206c600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-314 4370,-314 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="40d4920@0" ObjectIDND2="4514b80@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="40d4920_0" Pin1InfoVect2LinkObjId="4514b80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-314 4370,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="40beb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4406,-314 4416,-314 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="40adf40@0" Pin0InfoVect0LinkObjId="40adf40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4406,-314 4416,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="43f0bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-327 4352,-314 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="40d4920@0" ObjectIDZND2="4514b80@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="40d4920_0" Pin0InfoVect2LinkObjId="4514b80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-327 4352,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3f70b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-288 4315,-288 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="40d4920@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="40d4920_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-288 4315,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="409b5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-314 4352,-288 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="40d4920@0" ObjectIDZND1="4514b80@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="40d4920_0" Pin0InfoVect1LinkObjId="4514b80_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-314 4352,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3be58b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-265 4315,-265 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="4514b80@0" ObjectIDZND0="40d4920@0" Pin0InfoVect0LinkObjId="40d4920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="4514b80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-265 4315,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4f04b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-288 4352,-265 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="40d4920@0" ObjectIDZND1="4514b80@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="40d4920_0" Pin0InfoVect1LinkObjId="4514b80_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-288 4352,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="419a7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-287 4159,-265 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="4001400@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="4001400_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-287 4159,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4dbac20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-48 4159,-48 4159,-265 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="4001400@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="4001400_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-48 4159,-48 4159,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4f01ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4475,-96 4465,-96 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="40bf080@0" Pin0InfoVect0LinkObjId="40bf080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4475,-96 4465,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4562a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-159 4508,-159 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-159 4508,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="49df2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4472,-159 4462,-159 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="409db50@0" Pin0InfoVect0LinkObjId="409db50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4472,-159 4462,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4485c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4471,-223 4461,-223 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="3eb0630@0" Pin0InfoVect0LinkObjId="3eb0630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4471,-223 4461,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4e3dbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-721 4160,-837 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-721 4160,-837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3e98280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-862 4453,-862 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="206d530@0" Pin0InfoVect0LinkObjId="206d530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-862 4453,-862 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="42c61c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-926 4432,-948 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="32a3230@1" ObjectIDZND0="4000c20@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="4000c20_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="32a3230_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-926 4432,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="462d420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-1052 4432,-1064 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="3dd6de0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3dd6de0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-1052 4432,-1064 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="42c52b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-961 4401,-948 4432,-948 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4000c20@0" ObjectIDZND0="32a3230@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="32a3230_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="4000c20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-961 4401,-948 4432,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="41a0f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-948 4432,-963 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="32a3230@0" ObjectIDND1="4000c20@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="32a3230_0" Pin1InfoVect1LinkObjId="4000c20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-948 4432,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="42c7a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-999 4432,-1013 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="3dd6de0@0" Pin0InfoVect0LinkObjId="3dd6de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-999 4432,-1013 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4368b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-863 4544,-863 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="3eac580@0" Pin0InfoVect0LinkObjId="3eac580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-863 4544,-863 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="47b8fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-926 4525,-948 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="41a0790@1" ObjectIDZND0="5021f50@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="5021f50_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="41a0790_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-926 4525,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="47b8d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-1052 4525,-1064 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="2f88be0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="2f88be0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-1052 4525,-1064 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="47b8b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4494,-961 4494,-948 4525,-948 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="5021f50@0" ObjectIDZND0="41a0790@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="41a0790_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="5021f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4494,-961 4494,-948 4525,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="47b8900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-948 4525,-963 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="5021f50@0" ObjectIDND1="41a0790@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="5021f50_0" Pin1InfoVect1LinkObjId="41a0790_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-948 4525,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="47b86c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-999 4525,-1013 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="2f88be0@0" Pin0InfoVect0LinkObjId="2f88be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-999 4525,-1013 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2893500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-864 4641,-864 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="210dc90@0" Pin0InfoVect0LinkObjId="210dc90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-864 4641,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3c98e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-928 4621,-950 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="24d68a0@1" ObjectIDZND0="291c190@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="291c190_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="24d68a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-928 4621,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="45fb700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-1054 4621,-1066 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="46c7cf0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="46c7cf0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-1054 4621,-1066 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="501ebe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4590,-963 4590,-950 4621,-950 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="291c190@0" ObjectIDZND0="24d68a0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="24d68a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="291c190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4590,-963 4590,-950 4621,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="46b2910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-950 4621,-965 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="291c190@0" ObjectIDND1="24d68a0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="291c190_0" Pin1InfoVect1LinkObjId="24d68a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-950 4621,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="448e270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-1001 4621,-1015 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="46c7cf0@0" Pin0InfoVect0LinkObjId="46c7cf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-1001 4621,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4624230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-34 3797,-34 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="3c4ce30@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3c4ce30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-34 3797,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="462b530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-34 3740,-34 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="4685340@0" Pin0InfoVect0LinkObjId="4685340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-34 3740,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="453f6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3810,-151 3797,-151 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="2f9e890@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="2f9e890_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3810,-151 3797,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4092c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-151 3741,-151 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="4489f60@0" Pin0InfoVect0LinkObjId="4489f60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-151 3741,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="44d8b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3810,-254 3796,-254 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="40015e0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="40015e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3810,-254 3796,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="501d950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3754,-254 3741,-254 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="47b1620@0" Pin0InfoVect0LinkObjId="47b1620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3754,-254 3741,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4360fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-818 4432,-831 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="4368790@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="4368790_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-818 4432,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2d05b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-873 4432,-887 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="32a3230@0" Pin0InfoVect0LinkObjId="32a3230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-873 4432,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="462c8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-818 4525,-832 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="351a6b0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="351a6b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-818 4525,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4546260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-874 4525,-887 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="41a0790@0" Pin0InfoVect0LinkObjId="41a0790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-874 4525,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="49788a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-820 4621,-833 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="48a5b80@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="48a5b80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-820 4621,-833 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="40061f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-875 4621,-889 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="24d68a0@0" Pin0InfoVect0LinkObjId="24d68a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-875 4621,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="46b06b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-240 4590,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="42c5980@0" ObjectIDND1="4099670@0" ObjectIDND2="4962c20@0" ObjectIDZND0="42c5ea0@0" Pin0InfoVect0LinkObjId="42c5ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="42c5980_0" Pin1InfoVect1LinkObjId="4099670_0" Pin1InfoVect2LinkObjId="4962c20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-240 4590,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="34b5ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-592 4589,-592 4589,-607 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="4099670@0" ObjectIDND1="42c5ea0@0" ObjectIDND2="4962c20@0" ObjectIDZND0="42c5980@0" Pin0InfoVect0LinkObjId="42c5980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="4099670_0" Pin1InfoVect1LinkObjId="42c5ea0_0" Pin1InfoVect2LinkObjId="4962c20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-592 4589,-592 4589,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2ed0830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4988,-915 4988,-895 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="4782670@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="4782670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4988,-915 4988,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2f488e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5072,-914 5072,-894 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="492e440@0" ObjectIDZND0="33299a0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="33299a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="492e440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5072,-914 5072,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="34b4290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5050,-894 5072,-894 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="33299a0@1" ObjectIDZND0="0@x" ObjectIDZND1="492e440@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="492e440_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="33299a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5050,-894 5072,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="49162b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5072,-894 5089,-894 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33299a0@0" ObjectIDND1="492e440@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="33299a0_0" Pin1InfoVect1LinkObjId="492e440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5072,-894 5089,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="490e1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5125,-894 5137,-894 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="4314960@0" Pin0InfoVect0LinkObjId="4314960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5125,-894 5137,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4069690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5176,-894 5188,-894 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="4314960@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="4314960_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5176,-894 5188,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4153d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-851 4737,-831 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="47a1bf0@0" ObjectIDZND0="4312270@0" ObjectIDZND1="3edf720@0" ObjectIDZND2="450ec70@0" Pin0InfoVect0LinkObjId="4312270_0" Pin0InfoVect1LinkObjId="3edf720_0" Pin0InfoVect2LinkObjId="450ec70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="47a1bf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-851 4737,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="405dba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4721,-240 4684,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="47a1bf0@0" ObjectIDND1="4312270@0" ObjectIDND2="3edf720@0" ObjectIDZND0="43ce030@0" Pin0InfoVect0LinkObjId="43ce030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="47a1bf0_0" Pin1InfoVect1LinkObjId="4312270_0" Pin1InfoVect2LinkObjId="3edf720_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4721,-240 4684,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="430dee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-830 4754,-830 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="47a1bf0@0" ObjectIDND1="3edf720@0" ObjectIDND2="450ec70@0" ObjectIDZND0="4312270@0" Pin0InfoVect0LinkObjId="4312270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="47a1bf0_0" Pin1InfoVect1LinkObjId="3edf720_0" Pin1InfoVect2LinkObjId="450ec70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-830 4754,-830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="44abfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4799,-830 4820,-830 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="4312270@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="4312270_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4799,-830 4820,-830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="479f250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4856,-830 4881,-830 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4856,-830 4881,-830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4313ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-895 4881,-895 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="49c6ed0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="49c6ed0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-895 4881,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="32acf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4942,-894 4957,-894 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="49c6ed0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="49c6ed0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4942,-894 4957,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="47bcd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4999,-894 5011,-894 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="33299a0@0" Pin0InfoVect0LinkObjId="33299a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4999,-894 5011,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2921280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4989,-789 4989,-769 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="2940ab0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="2940ab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4989,-789 4989,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="418e560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5073,-788 5073,-768 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4312c60@0" ObjectIDZND0="42e4040@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="42e4040_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="4312c60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5073,-788 5073,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="41444e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5051,-768 5073,-768 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="42e4040@1" ObjectIDZND0="4312c60@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="4312c60_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="42e4040_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5051,-768 5073,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3be4fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5073,-768 5090,-768 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="42e4040@0" ObjectIDND1="4312c60@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="42e4040_0" Pin1InfoVect1LinkObjId="4312c60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5073,-768 5090,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="42c80c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5126,-768 5138,-768 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="4195f00@0" Pin0InfoVect0LinkObjId="4195f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5126,-768 5138,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4788fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5177,-768 5189,-768 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="4195f00@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="4195f00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5177,-768 5189,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4000690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4898,-769 4881,-769 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="4782000@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="4782000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4898,-769 4881,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="43cb2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4943,-768 4958,-768 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="4782000@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="4782000_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4943,-768 4958,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="36dac10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5000,-768 5012,-768 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="42e4040@0" Pin0InfoVect0LinkObjId="42e4040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5000,-768 5012,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="42ca3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4989,-664 4989,-644 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="4307fb0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="4307fb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4989,-664 4989,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="422ed30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5073,-663 5073,-643 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3d723a0@0" ObjectIDZND0="47a9d90@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="47a9d90_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3d723a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5073,-663 5073,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="44a8e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5051,-643 5073,-643 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47a9d90@1" ObjectIDZND0="3d723a0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="3d723a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="47a9d90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5051,-643 5073,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="47b2b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5073,-643 5090,-643 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="47a9d90@0" ObjectIDND1="3d723a0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="47a9d90_0" Pin1InfoVect1LinkObjId="3d723a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5073,-643 5090,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="291eaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5126,-643 5138,-643 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="407ec20@0" Pin0InfoVect0LinkObjId="407ec20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5126,-643 5138,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2944f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5177,-643 5189,-643 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="407ec20@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="407ec20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5177,-643 5189,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3d72170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4898,-644 4881,-644 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="2115650@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="2115650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4898,-644 4881,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4d5d5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4943,-643 4958,-643 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="2115650@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="2115650_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4943,-643 4958,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3d07670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5000,-643 5012,-643 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="47a9d90@0" Pin0InfoVect0LinkObjId="47a9d90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5000,-643 5012,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3c9b8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4989,-369 4989,-349 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="454ee50@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="454ee50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4989,-369 4989,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="501d450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5073,-368 5073,-348 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="45983c0@0" ObjectIDZND0="46d05a0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="46d05a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="45983c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5073,-368 5073,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="42e8410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5051,-348 5073,-348 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="46d05a0@1" ObjectIDZND0="45983c0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="45983c0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="46d05a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5051,-348 5073,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4917f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5073,-348 5090,-348 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="46d05a0@0" ObjectIDND1="45983c0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="46d05a0_0" Pin1InfoVect1LinkObjId="45983c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5073,-348 5090,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="45a0570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5126,-348 5138,-348 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="49181e0@0" Pin0InfoVect0LinkObjId="49181e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5126,-348 5138,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4300690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5177,-348 5189,-348 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="49181e0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="49181e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5177,-348 5189,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4598190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4898,-349 4882,-349 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="5023870@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="5023870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4898,-349 4882,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3744130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4943,-348 4958,-348 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="5023870@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="5023870_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4943,-348 4958,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4186bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5000,-348 5012,-348 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="46d05a0@0" Pin0InfoVect0LinkObjId="46d05a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5000,-348 5012,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="48d3eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4990,-243 4990,-223 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="3bf8ee0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3bf8ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4990,-243 4990,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3f81ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-242 5074,-222 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="21356a0@0" ObjectIDZND0="37270d0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="37270d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="21356a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-242 5074,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4172840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5052,-222 5074,-222 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="37270d0@1" ObjectIDZND0="21356a0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="21356a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="37270d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5052,-222 5074,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="42c48e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-222 5091,-222 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="37270d0@0" ObjectIDND1="21356a0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="37270d0_0" Pin1InfoVect1LinkObjId="21356a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-222 5091,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2fb6510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5127,-222 5139,-222 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="42741f0@0" Pin0InfoVect0LinkObjId="42741f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5127,-222 5139,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2fb6770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5178,-222 5190,-222 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="42741f0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="42741f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5178,-222 5190,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2135440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4899,-223 4882,-223 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="36fcb50@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="36fcb50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4899,-223 4882,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="45f9df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4944,-222 4959,-222 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="36fcb50@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="36fcb50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4944,-222 4959,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="45fa050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5001,-222 5013,-222 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="37270d0@0" Pin0InfoVect0LinkObjId="37270d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5001,-222 5013,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="294c370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4990,-118 4990,-98 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="286e3a0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="286e3a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4990,-118 4990,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="502a850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-117 5074,-97 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4544ca0@0" ObjectIDZND0="48ec510@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="48ec510_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="4544ca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-117 5074,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="502aab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5052,-97 5074,-97 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="48ec510@1" ObjectIDZND0="4544ca0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="4544ca0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="48ec510_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5052,-97 5074,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="454f7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-97 5091,-97 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="48ec510@0" ObjectIDND1="4544ca0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="48ec510_0" Pin1InfoVect1LinkObjId="4544ca0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-97 5091,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="46aaa70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5127,-97 5139,-97 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="4db6650@0" Pin0InfoVect0LinkObjId="4db6650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5127,-97 5139,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="20a6ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5178,-97 5190,-97 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="4db6650@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="4db6650_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5178,-97 5190,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4544a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4899,-98 4882,-98 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="3e25b50@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3e25b50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4899,-98 4882,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="20ce430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4944,-97 4959,-97 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="3e25b50@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3e25b50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4944,-97 4959,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="411c490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5001,-97 5013,-97 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="48ec510@0" Pin0InfoVect0LinkObjId="48ec510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5001,-97 5013,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2590090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4988,-501 4988,-481 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="25902d0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="25902d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4988,-501 4988,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="212f700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5072,-500 5072,-480 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="453e250@0" ObjectIDZND0="27a6d50@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="27a6d50_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="453e250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5072,-500 5072,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="47b9830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5050,-480 5072,-480 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="27a6d50@1" ObjectIDZND0="453e250@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="453e250_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="27a6d50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5050,-480 5072,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3ee34c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5072,-480 5089,-480 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="27a6d50@0" ObjectIDND1="453e250@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="27a6d50_0" Pin1InfoVect1LinkObjId="453e250_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5072,-480 5089,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="350f940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5125,-480 5137,-480 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="29462e0@0" Pin0InfoVect0LinkObjId="29462e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5125,-480 5137,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="350fba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5176,-480 5188,-480 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="29462e0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="29462e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5176,-480 5188,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="453dff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-481 4882,-481 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="2df2560@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="2df2560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-481 4882,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="42c2f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4942,-480 4957,-480 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="2df2560@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="2df2560_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4942,-480 4957,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="42c3170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4999,-480 5011,-480 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="27a6d50@0" Pin0InfoVect0LinkObjId="27a6d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4999,-480 5011,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4547930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4720,-415 4720,-830 4737,-830 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="3edf720@0" ObjectIDND1="450ec70@0" ObjectIDND2="43ce030@0" ObjectIDZND0="47a1bf0@0" ObjectIDZND1="4312270@0" Pin0InfoVect0LinkObjId="47a1bf0_0" Pin0InfoVect1LinkObjId="4312270_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="3edf720_0" Pin1InfoVect1LinkObjId="450ec70_0" Pin1InfoVect2LinkObjId="43ce030_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4720,-415 4720,-830 4737,-830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="450e890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4736,-436 4736,-416 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="3edf720@0" ObjectIDZND0="47a1bf0@0" ObjectIDZND1="4312270@0" ObjectIDZND2="43ce030@0" Pin0InfoVect0LinkObjId="47a1bf0_0" Pin0InfoVect1LinkObjId="4312270_0" Pin0InfoVect2LinkObjId="43ce030_0" Pin0Num="1" Pin1InfoVect0LinkObjId="3edf720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4736,-436 4736,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="22b04f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4720,-415 4736,-415 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="47a1bf0@0" ObjectIDND1="4312270@0" ObjectIDND2="43ce030@0" ObjectIDZND0="3edf720@0" ObjectIDZND1="450ec70@0" Pin0InfoVect0LinkObjId="3edf720_0" Pin0InfoVect1LinkObjId="450ec70_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="47a1bf0_0" Pin1InfoVect1LinkObjId="4312270_0" Pin1InfoVect2LinkObjId="43ce030_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4720,-415 4736,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="367ce80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4736,-415 4753,-415 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3edf720@0" ObjectIDND1="47a1bf0@0" ObjectIDND2="4312270@0" ObjectIDZND0="450ec70@0" Pin0InfoVect0LinkObjId="450ec70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="3edf720_0" Pin1InfoVect1LinkObjId="47a1bf0_0" Pin1InfoVect2LinkObjId="4312270_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4736,-415 4753,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2e60790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4798,-415 4818,-415 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="450ec70@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="450ec70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4798,-415 4818,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2d7b8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4854,-415 4882,-415 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4854,-415 4882,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3db5500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-493 4352,-519 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-493 4352,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2fbfa20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-572 4334,-572 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="3014200@0" ObjectIDND2="47b9460@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="3014200_0" Pin1InfoVect2LinkObjId="47b9460_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-572 4334,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2fbfc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4298,-572 4288,-572 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="2e4fa80@0" Pin0InfoVect0LinkObjId="2e4fa80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4298,-572 4288,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2d07fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-555 4352,-572 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="3014200@0" ObjectIDZND2="47b9460@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="3014200_0" Pin0InfoVect2LinkObjId="47b9460_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-555 4352,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4949480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-592 4315,-592 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="47b9460@0" ObjectIDZND0="3014200@0" Pin0InfoVect0LinkObjId="3014200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="47b9460_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-592 4315,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4498f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-572 4352,-592 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="3014200@0" ObjectIDZND1="47b9460@0" Pin0InfoVect0LinkObjId="3014200_0" Pin0InfoVect1LinkObjId="47b9460_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-572 4352,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="47b9200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-642 4352,-592 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="47b9460@0" ObjectIDZND0="3014200@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="3014200_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="47b9460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-642 4352,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2825ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4510,-493 4510,-515 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="4675fa0@0" Pin0InfoVect0LinkObjId="4675fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4510,-493 4510,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2825d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4510,-560 4510,-596 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="4675fa0@1" ObjectIDZND0="3eacac0@1" Pin0InfoVect0LinkObjId="3eacac0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="4675fa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4510,-560 4510,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="43c7c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-449 3855,-449 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="42c0230@0" Pin0InfoVect0LinkObjId="42c0230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-449 3855,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="461f0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3767,-449 3767,-469 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="461f340@0" Pin0InfoVect0LinkObjId="461f340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3767,-449 3767,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="42dd990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-449 3680,-469 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="436df20@0" ObjectIDND1="0@x" ObjectIDZND0="48faef0@0" Pin0InfoVect0LinkObjId="48faef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="436df20_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-449 3680,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="42ddbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3702,-449 3680,-449 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="436df20@1" ObjectIDZND0="48faef0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="48faef0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="436df20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3702,-449 3680,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2d77c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-449 3663,-449 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="436df20@0" ObjectIDND1="48faef0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="436df20_0" Pin1InfoVect1LinkObjId="48faef0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-449 3663,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="20a7c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-449 3615,-449 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="2d77e70@0" Pin0InfoVect0LinkObjId="2d77e70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-449 3615,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="20a7e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3576,-449 3564,-449 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="2d77e70@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="2d77e70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3576,-449 3564,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="27bb7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-566 3856,-566 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="28069a0@0" Pin0InfoVect0LinkObjId="28069a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-566 3856,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="27bba00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3767,-566 3767,-586 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="29299a0@0" Pin0InfoVect0LinkObjId="29299a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3767,-566 3767,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4918820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-566 3681,-586 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="291bb00@0" ObjectIDND1="0@x" ObjectIDZND0="4918c80@0" Pin0InfoVect0LinkObjId="4918c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="291bb00_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-566 3681,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4918a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3703,-566 3681,-566 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="291bb00@1" ObjectIDZND0="4918c80@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="4918c80_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="291bb00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3703,-566 3681,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="216b020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-566 3664,-566 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="291bb00@0" ObjectIDND1="4918c80@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="291bb00_0" Pin1InfoVect1LinkObjId="4918c80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-566 3664,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="207a040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3628,-566 3616,-566 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="216b280@0" Pin0InfoVect0LinkObjId="216b280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3628,-566 3616,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="207a270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3577,-566 3565,-566 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="216b280@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="216b280_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3577,-566 3565,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="48f18d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-668 3856,-668 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="3dfe250@0" Pin0InfoVect0LinkObjId="3dfe250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-668 3856,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="48f1b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-669 3766,-688 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="48f1d30@0" Pin0InfoVect0LinkObjId="48f1d30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-669 3766,-688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="42ce7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-669 3681,-689 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="20b7880@0" ObjectIDND1="0@x" ObjectIDZND0="2cfa860@0" Pin0InfoVect0LinkObjId="2cfa860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="20b7880_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-669 3681,-689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="42cea00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3703,-669 3681,-669 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="20b7880@1" ObjectIDZND0="2cfa860@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="2cfa860_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="20b7880_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3703,-669 3681,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="477c940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-669 3664,-669 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="20b7880@0" ObjectIDND1="2cfa860@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="20b7880_0" Pin1InfoVect1LinkObjId="2cfa860_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-669 3664,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2075dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3628,-669 3616,-669 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="477cba0@0" Pin0InfoVect0LinkObjId="477cba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3628,-669 3616,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2076000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3577,-669 3565,-669 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="477cba0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="477cba0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3577,-669 3565,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="27adfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3810,-449 3798,-449 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="42c0230@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="42c0230_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3810,-449 3798,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="27ae210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3756,-449 3741,-449 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="436df20@0" Pin0InfoVect0LinkObjId="436df20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3756,-449 3741,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3db3720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3811,-566 3798,-566 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="28069a0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="28069a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3811,-566 3798,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3db3950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3756,-566 3742,-566 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="291bb00@0" Pin0InfoVect0LinkObjId="291bb00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3756,-566 3742,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2099c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3811,-669 3797,-669 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="3dfe250@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3dfe250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3811,-669 3797,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2099e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-669 3742,-669 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="20b7880@0" Pin0InfoVect0LinkObjId="20b7880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-669 3742,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3e4e3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-767 3855,-767 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="267e780@0" Pin0InfoVect0LinkObjId="267e780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-767 3855,-767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2810a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3765,-768 3765,-787 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="2810ca0@0" Pin0InfoVect0LinkObjId="2810ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3765,-768 3765,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3e50a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-768 3680,-788 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="225c6b0@0" ObjectIDND1="0@x" ObjectIDZND0="3e50ed0@0" Pin0InfoVect0LinkObjId="3e50ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="225c6b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-768 3680,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3e50c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3702,-768 3680,-768 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="225c6b0@1" ObjectIDZND0="3e50ed0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="3e50ed0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="225c6b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3702,-768 3680,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="210fc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-768 3663,-768 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="225c6b0@0" ObjectIDND1="3e50ed0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="225c6b0_0" Pin1InfoVect1LinkObjId="3e50ed0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-768 3663,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="22b14a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-768 3615,-768 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="210fe70@0" Pin0InfoVect0LinkObjId="210fe70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-768 3615,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="22b16d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3576,-768 3564,-768 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="210fe70@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="210fe70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3576,-768 3564,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2140d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3810,-768 3796,-768 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="267e780@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="267e780_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3810,-768 3796,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2140f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3754,-768 3741,-768 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="225c6b0@0" Pin0InfoVect0LinkObjId="225c6b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3754,-768 3741,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="267d460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-434 3892,-434 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-434 3892,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3f5cea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3934,-434 3947,-434 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="267d6c0@1" Pin0InfoVect0LinkObjId="267d6c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3934,-434 3947,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3f5d100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-434 4000,-400 3983,-400 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="267d6c0@0" ObjectIDND1="2df16f0@0" ObjectIDND2="3d0be80@0" ObjectIDZND0="211a280@0" Pin0InfoVect0LinkObjId="211a280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="267d6c0_0" Pin1InfoVect1LinkObjId="2df16f0_0" Pin1InfoVect2LinkObjId="3d0be80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-434 4000,-400 3983,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="206ffa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-434 4000,-434 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="267d6c0@0" ObjectIDZND0="211a280@0" ObjectIDZND1="2df16f0@0" ObjectIDZND2="3d0be80@0" Pin0InfoVect0LinkObjId="211a280_0" Pin0InfoVect1LinkObjId="2df16f0_0" Pin0InfoVect2LinkObjId="3d0be80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="267d6c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-434 4000,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2df1490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-271 3891,-271 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3871,-271 3891,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4947d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-271 3945,-271 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="2df16f0@1" Pin0InfoVect0LinkObjId="2df16f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-271 3945,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2692a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4001,-271 4001,-237 3984,-237 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="2df16f0@0" ObjectIDND1="267d6c0@0" ObjectIDND2="211a280@0" ObjectIDZND0="3d0be80@0" Pin0InfoVect0LinkObjId="3d0be80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="2df16f0_0" Pin1InfoVect1LinkObjId="267d6c0_0" Pin1InfoVect2LinkObjId="211a280_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4001,-271 4001,-237 3984,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3d0b9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3990,-271 4001,-271 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="2df16f0@0" ObjectIDZND0="267d6c0@0" ObjectIDZND1="211a280@0" ObjectIDZND2="3d0be80@0" Pin0InfoVect0LinkObjId="267d6c0_0" Pin0InfoVect1LinkObjId="211a280_0" Pin0InfoVect2LinkObjId="3d0be80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="2df16f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3990,-271 4001,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3d0bc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4001,-271 4016,-271 4016,-434 4000,-434 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="2df16f0@0" ObjectIDND1="3d0be80@0" ObjectIDZND0="267d6c0@0" ObjectIDZND1="211a280@0" Pin0InfoVect0LinkObjId="267d6c0_0" Pin0InfoVect1LinkObjId="211a280_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="2df16f0_0" Pin1InfoVect1LinkObjId="3d0be80_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4001,-271 4016,-271 4016,-434 4000,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="26510e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-918 3855,-918 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="457e3a0@0" Pin0InfoVect0LinkObjId="457e3a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-918 3855,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2651340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3767,-918 3767,-938 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="26515a0@0" Pin0InfoVect0LinkObjId="26515a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3767,-918 3767,-938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2954260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-918 3680,-938 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="291ce60@0" ObjectIDND1="0@x" ObjectIDZND0="29546f0@0" Pin0InfoVect0LinkObjId="29546f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="291ce60_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-918 3680,-938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2954490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3702,-918 3680,-918 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="291ce60@1" ObjectIDZND0="29546f0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="29546f0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="291ce60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3702,-918 3680,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="20e6e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-918 3663,-918 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="291ce60@0" ObjectIDND1="29546f0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="291ce60_0" Pin1InfoVect1LinkObjId="29546f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-918 3663,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2258540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-918 3615,-918 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="20e7090@0" Pin0InfoVect0LinkObjId="20e7090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-918 3615,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="22587a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3576,-918 3564,-918 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="20e7090@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="20e7090_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3576,-918 3564,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2247f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-1035 3856,-1035 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="36df0a0@0" Pin0InfoVect0LinkObjId="36df0a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-1035 3856,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="22481a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3767,-1035 3767,-1055 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="20b8050@0" Pin0InfoVect0LinkObjId="20b8050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3767,-1035 3767,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="453f100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-1035 3681,-1055 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="20b88a0@0" ObjectIDND1="0@x" ObjectIDZND0="3f70fd0@0" Pin0InfoVect0LinkObjId="3f70fd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="20b88a0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-1035 3681,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3f70d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3703,-1035 3681,-1035 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="20b88a0@1" ObjectIDZND0="3f70fd0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="3f70fd0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="20b88a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3703,-1035 3681,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2874ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-1035 3664,-1035 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="20b88a0@0" ObjectIDND1="3f70fd0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="20b88a0_0" Pin1InfoVect1LinkObjId="3f70fd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-1035 3664,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="20a43e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3628,-1035 3616,-1035 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="3f60770@0" Pin0InfoVect0LinkObjId="3f60770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3628,-1035 3616,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="20a4640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3577,-1035 3565,-1035 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="3f60770@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3f60770_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3577,-1035 3565,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4313a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-1137 3856,-1137 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="43131a0@0" Pin0InfoVect0LinkObjId="43131a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-1137 3856,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="20e3eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3766,-1138 3766,-1157 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="20e40e0@0" Pin0InfoVect0LinkObjId="20e40e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3766,-1138 3766,-1157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3f7f630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-1138 3681,-1158 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="3f7ec80@0" ObjectIDND1="0@x" ObjectIDZND0="43f0170@0" Pin0InfoVect0LinkObjId="43f0170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="3f7ec80_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-1138 3681,-1158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="43eff40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3703,-1138 3681,-1138 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3f7ec80@1" ObjectIDZND0="43f0170@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="43f0170_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3f7ec80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3703,-1138 3681,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2e17210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-1138 3664,-1138 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="3f7ec80@0" ObjectIDND1="43f0170@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="3f7ec80_0" Pin1InfoVect1LinkObjId="43f0170_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-1138 3664,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="49ebb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3628,-1138 3616,-1138 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="2e17470@0" Pin0InfoVect0LinkObjId="2e17470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3628,-1138 3616,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="49ebda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3577,-1138 3565,-1138 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="2e17470@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="2e17470_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3577,-1138 3565,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="260b4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3810,-918 3798,-918 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="457e3a0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="457e3a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3810,-918 3798,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="260b750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3756,-918 3741,-918 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="291ce60@0" Pin0InfoVect0LinkObjId="291ce60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3756,-918 3741,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="48f2980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3811,-1035 3798,-1035 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="36df0a0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="36df0a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3811,-1035 3798,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="48f2be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3756,-1035 3742,-1035 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="20b88a0@0" Pin0InfoVect0LinkObjId="20b88a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3756,-1035 3742,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="20771e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3811,-1138 3797,-1138 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="43131a0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="43131a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3811,-1138 3797,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4366750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-1138 3742,-1138 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="3f7ec80@0" Pin0InfoVect0LinkObjId="3f7ec80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-1138 3742,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4db6280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-941 3894,-941 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-941 3894,-941 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3db4570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3936,-941 3949,-941 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="3db3bf0@1" Pin0InfoVect0LinkObjId="3db3bf0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3936,-941 3949,-941 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3db47d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4002,-941 4002,-907 3985,-907 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3db3bf0@0" ObjectIDND1="20a0270@0" ObjectIDND2="28761d0@0" ObjectIDZND0="28752b0@0" Pin0InfoVect0LinkObjId="28752b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="3db3bf0_0" Pin1InfoVect1LinkObjId="20a0270_0" Pin1InfoVect2LinkObjId="28761d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4002,-941 4002,-907 3985,-907 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2875050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3994,-941 4002,-941 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="3db3bf0@0" ObjectIDZND0="28752b0@0" ObjectIDZND1="20a0270@0" ObjectIDZND2="28761d0@0" Pin0InfoVect0LinkObjId="28752b0_0" Pin0InfoVect1LinkObjId="20a0270_0" Pin0InfoVect2LinkObjId="28761d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="3db3bf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3994,-941 4002,-941 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="20e6820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-778 3893,-778 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-778 3893,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2876b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3935,-778 3947,-778 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="28761d0@1" Pin0InfoVect0LinkObjId="28761d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3935,-778 3947,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2876d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4003,-778 4003,-744 3986,-744 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28761d0@0" ObjectIDND1="28752b0@0" ObjectIDND2="3db3bf0@0" ObjectIDZND0="20a0270@0" Pin0InfoVect0LinkObjId="20a0270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="28761d0_0" Pin1InfoVect1LinkObjId="28752b0_0" Pin1InfoVect2LinkObjId="3db3bf0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4003,-778 4003,-744 3986,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="209fdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-778 4003,-778 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28761d0@0" ObjectIDZND0="20a0270@0" ObjectIDZND1="28752b0@0" ObjectIDZND2="3db3bf0@0" Pin0InfoVect0LinkObjId="20a0270_0" Pin0InfoVect1LinkObjId="28752b0_0" Pin0InfoVect2LinkObjId="3db3bf0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="28761d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-778 4003,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="20a0020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4003,-778 4018,-778 4018,-941 4002,-941 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="20a0270@0" ObjectIDND1="28761d0@0" ObjectIDZND0="28752b0@0" ObjectIDZND1="3db3bf0@0" Pin0InfoVect0LinkObjId="28752b0_0" Pin0InfoVect1LinkObjId="3db3bf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="20a0270_0" Pin1InfoVect1LinkObjId="28761d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4003,-778 4018,-778 4018,-941 4002,-941 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="43dc610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-592 4626,-626 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="42c5980@0" ObjectIDND1="42c5ea0@0" ObjectIDND2="4962c20@0" ObjectIDZND0="4099670@0" Pin0InfoVect0LinkObjId="4099670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="42c5980_0" Pin1InfoVect1LinkObjId="42c5ea0_0" Pin1InfoVect2LinkObjId="4962c20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-592 4626,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="25905d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4627,-671 4627,-692 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="4099670@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="4099670_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4627,-671 4627,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="25907c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4627,-728 4627,-754 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4627,-728 4627,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2590ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4431,-773 4431,-754 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="4368790@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="4368790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4431,-773 4431,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2620a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4524,-773 4524,-754 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="351a6b0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="351a6b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4524,-773 4524,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2621050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-775 4620,-754 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="48a5b80@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="48a5b80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-775 4620,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4620680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4488,-240 4526,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="4514b80@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="40d4920@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="40d4920_0" Pin0Num="1" Pin1InfoVect0LinkObjId="4514b80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4488,-240 4526,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4620870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-240 4526,-254 4352,-254 4352,-265 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="4514b80@0" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="40d4920@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="40d4920_0" Pin0Num="2" Pin1InfoVect0LinkObjId="4514b80_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-240 4526,-254 4352,-254 4352,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4620a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-208 4526,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="4514b80@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="4514b80_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-208 4526,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="42cd5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-159 4526,-172 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-159 4526,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="42cd830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-142 4526,-159 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-142 4526,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="42cda90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4511,-96 4526,-96 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4511,-96 4526,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="453fd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-96 4526,-115 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-96 4526,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="453ffb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-30 4526,-47 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-30 4526,-47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4540210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4526,-83 4526,-96 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4526,-83 4526,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="295aa60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-592 4626,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="42c5980@0" ObjectIDND1="4099670@0" ObjectIDZND0="42c5ea0@0" ObjectIDZND1="4962c20@0" Pin0InfoVect0LinkObjId="42c5ea0_0" Pin0InfoVect1LinkObjId="4962c20_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="42c5980_0" Pin1InfoVect1LinkObjId="4099670_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-592 4626,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="295b060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-51 4626,-30 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-51 4626,-30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="295b2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-240 4626,-159 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="42c5ea0@0" ObjectIDND1="42c5980@0" ObjectIDND2="4099670@0" ObjectIDZND0="4962c20@0" Pin0InfoVect0LinkObjId="4962c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="42c5ea0_0" Pin1InfoVect1LinkObjId="42c5980_0" Pin1InfoVect2LinkObjId="4099670_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-240 4626,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="43f23e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-114 4626,-87 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="4962c20@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="4962c20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-114 4626,-87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="43f2e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4720,-240 4720,-415 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="busSection" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="43ce030@0" ObjectIDND1="0@0" ObjectIDZND0="47a1bf0@0" ObjectIDZND1="4312270@0" ObjectIDZND2="3edf720@0" Pin0InfoVect0LinkObjId="47a1bf0_0" Pin0InfoVect1LinkObjId="4312270_0" Pin0InfoVect2LinkObjId="3edf720_0" Pin0Num="2" Pin1InfoVect0LinkObjId="43ce030_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4720,-240 4720,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="20e4b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4720,-30 4720,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="43ce030@0" ObjectIDZND1="47a1bf0@0" ObjectIDZND2="4312270@0" Pin0InfoVect0LinkObjId="43ce030_0" Pin0InfoVect1LinkObjId="47a1bf0_0" Pin0InfoVect2LinkObjId="4312270_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4720,-30 4720,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4952720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-873 4160,-886 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-873 4160,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="49bd4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-886 4160,-899 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-886 4160,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3379390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-886 4177,-886 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-886 4177,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="50279e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4213,-886 4227,-886 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="437c630@0" Pin0InfoVect0LinkObjId="437c630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4213,-886 4227,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2f60950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4162,-943 4179,-943 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4162,-943 4179,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="27a0d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4215,-943 4229,-943 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="4817470@0" Pin0InfoVect0LinkObjId="4817470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4215,-943 4229,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3022b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-926 4160,-943 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-926 4160,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="46093a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-943 4160,-952 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-943 4160,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2efc5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-1014 4178,-1014 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-1014 4178,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="33a4550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4214,-1014 4228,-1014 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="2d7cdf0@0" Pin0InfoVect0LinkObjId="2d7cdf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4214,-1014 4228,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2e92160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-988 4160,-1014 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-988 4160,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3dd72d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-1151 4270,-1151 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-1151 4270,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3e1b1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-1151 4160,-1184 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-1151 4160,-1184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="491be30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-1085 4220,-1085 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-1085 4220,-1085 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4487390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-1014 4160,-1085 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-1014 4160,-1085 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="501bed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-1085 4160,-1151 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-1085 4160,-1151 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3119.000000 -1155.000000) translate(0,17)">加南网标志（288＊90）：福嘉升压站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3030.000000 -1139.000000) translate(0,12)">0.1h</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3025.000000 -925.000000) translate(0,12)">0.4h</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3032.000000 -500.000000) translate(0,12)">0.5h</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3167.000000 -1120.000000) translate(0,12)">系统时间（180＊36）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3117.000000 -977.000000) translate(0,17)">频率</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3117.000000 -977.000000) translate(0,38)">全站有功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3117.000000 -977.000000) translate(0,59)">全站无功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3117.000000 -977.000000) translate(0,80)">并网联络点的电压和交换功率</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3118.000000 -589.000000) translate(0,17)">危险点说明</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3118.000000 -589.000000) translate(0,38)">联系方式</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3289.000000 -1231.000000) translate(0,12)">0.3h</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4075.000000 -689.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4410.000000 -1149.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4410.000000 -1149.000000) translate(0,27)">630kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4280.000000 -764.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3521.000000 -94.000000) translate(0,12)">3号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3521.000000 -94.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3522.000000 -211.000000) translate(0,12)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3522.000000 -211.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3522.000000 -314.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3522.000000 -314.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3489.000000 -196.000000) translate(0,12)">麻</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3489.000000 -196.000000) translate(0,27)">旺</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3489.000000 -196.000000) translate(0,42)">河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3489.000000 -196.000000) translate(0,57)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3489.000000 -196.000000) translate(0,72)">级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3489.000000 -196.000000) translate(0,87)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3489.000000 -196.000000) translate(0,102)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4503.000000 -1149.000000) translate(0,12)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4503.000000 -1149.000000) translate(0,27)">630kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4599.000000 -1151.000000) translate(0,12)">3号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4599.000000 -1151.000000) translate(0,27)">630kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5183.000000 -956.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5183.000000 -956.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4928.000000 -928.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5184.000000 -830.000000) translate(0,12)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5184.000000 -830.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4929.000000 -802.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5184.000000 -705.000000) translate(0,12)">3号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5184.000000 -705.000000) translate(0,27)">500kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4929.000000 -677.000000) translate(0,12)">3号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5184.000000 -410.000000) translate(0,12)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5184.000000 -410.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4929.000000 -382.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5185.000000 -284.000000) translate(0,12)">3号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5185.000000 -284.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4930.000000 -256.000000) translate(0,12)">3号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5185.000000 -159.000000) translate(0,12)">4号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5185.000000 -159.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4930.000000 -131.000000) translate(0,12)">4号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5183.000000 -542.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5183.000000 -542.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4928.000000 -514.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3522.000000 -509.000000) translate(0,12)">4号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3522.000000 -509.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3523.000000 -626.000000) translate(0,12)">3号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3523.000000 -626.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3523.000000 -729.000000) translate(0,12)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3523.000000 -729.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3486.000000 -679.000000) translate(0,12)">麻</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3486.000000 -679.000000) translate(0,27)">旺</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3486.000000 -679.000000) translate(0,42)">河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3486.000000 -679.000000) translate(0,57)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3486.000000 -679.000000) translate(0,72)">级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3486.000000 -679.000000) translate(0,87)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3486.000000 -679.000000) translate(0,102)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3522.000000 -828.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3522.000000 -828.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3522.000000 -978.000000) translate(0,12)">3号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3522.000000 -978.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3523.000000 -1095.000000) translate(0,12)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3523.000000 -1095.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3523.000000 -1198.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3523.000000 -1198.000000) translate(0,27)">800kw</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3490.000000 -1093.000000) translate(0,12)">麻</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3490.000000 -1093.000000) translate(0,27)">旺</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3490.000000 -1093.000000) translate(0,42)">河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3490.000000 -1093.000000) translate(0,57)">一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3490.000000 -1093.000000) translate(0,72)">级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3490.000000 -1093.000000) translate(0,87)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3490.000000 -1093.000000) translate(0,102)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3776.000000 -64.000000) translate(0,12)">3号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3776.000000 -181.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3776.000000 -283.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3840.000000 -312.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3774.000000 -479.000000) translate(0,12)">4号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3775.000000 -595.000000) translate(0,12)">3号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3776.000000 -701.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3774.000000 -798.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3773.000000 -948.000000) translate(0,12)">3号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3775.000000 -1066.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3774.000000 -1168.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4031.000000 -912.000000) translate(0,12)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4031.000000 -912.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4031.000000 -912.000000) translate(0,42)">麻</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4031.000000 -912.000000) translate(0,57)">一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4031.000000 -912.000000) translate(0,72)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4031.000000 -912.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4033.000000 -402.000000) translate(0,12)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4033.000000 -402.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4033.000000 -402.000000) translate(0,42)">麻</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4033.000000 -402.000000) translate(0,57)">一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4033.000000 -402.000000) translate(0,72)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4033.000000 -402.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3843.000000 -818.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3843.000000 -894.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4490.000000 -1179.000000) translate(0,12)">班果河一级站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4852.000000 -603.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4364.000000 -850.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4459.000000 -850.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4556.000000 -849.000000) translate(0,12)">3号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5256.000000 -811.000000) translate(0,12)">纳</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5256.000000 -811.000000) translate(0,27)">嫩</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5256.000000 -811.000000) translate(0,42)">河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5256.000000 -811.000000) translate(0,57)">一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5256.000000 -811.000000) translate(0,72)">级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5256.000000 -811.000000) translate(0,87)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5256.000000 -811.000000) translate(0,102)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5257.000000 -344.000000) translate(0,12)">纳</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5257.000000 -344.000000) translate(0,27)">嫩</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5257.000000 -344.000000) translate(0,42)">河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5257.000000 -344.000000) translate(0,57)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5257.000000 -344.000000) translate(0,72)">级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5257.000000 -344.000000) translate(0,87)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5257.000000 -344.000000) translate(0,102)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4851.000000 -525.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4754.000000 -401.000000) translate(0,12)">35kV纳三支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4689.000000 -466.000000) translate(0,12)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4689.000000 -466.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4689.000000 -466.000000) translate(0,42)">班</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4689.000000 -466.000000) translate(0,57)">纳</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4689.000000 -466.000000) translate(0,72)">一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4689.000000 -466.000000) translate(0,87)">级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4689.000000 -466.000000) translate(0,102)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4593.000000 -467.000000) translate(0,12)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4593.000000 -467.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4593.000000 -467.000000) translate(0,42)">班</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4593.000000 -467.000000) translate(0,57)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4593.000000 -467.000000) translate(0,72)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4593.000000 -467.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4436.000000 -274.000000) translate(0,12)">35kV班福线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4572.000000 -22.000000) translate(0,12)">班果河三级站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4128.000000 -180.000000) translate(0,12)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4128.000000 -180.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4128.000000 -180.000000) translate(0,42)">麻</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4128.000000 -180.000000) translate(0,57)">福</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4128.000000 -180.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4478.000000 -643.000000) translate(0,12)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4478.000000 -643.000000) translate(0,27)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4478.000000 -643.000000) translate(0,42)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4478.000000 -643.000000) translate(0,57)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4478.000000 -643.000000) translate(0,72)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4113.000000 -1169.000000) translate(0,12)">110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4113.000000 -1169.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4113.000000 -1169.000000) translate(0,42)">福</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4113.000000 -1169.000000) translate(0,57)">嘉</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4113.000000 -1169.000000) translate(0,72)">T</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4113.000000 -1169.000000) translate(0,87)">接</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4113.000000 -1169.000000) translate(0,102)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4066.000000 -1185.000000) translate(0,12)">110kV鄂西线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4120.000000 -612.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4112.000000 -545.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4174.000000 -601.000000) translate(0,12)">30117</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-43207">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4150.000000 -583.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7244" ObjectName="SW-CX_FJ.CX_FJ_301BK"/>
     <cge:Meas_Ref ObjectId="43207"/>
    <cge:TPSR_Ref TObjectID="7244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4150.000000 -378.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3999.000000 -39.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4343.000000 -376.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4517.000000 -107.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4151.000000 -891.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4349,-754 4676,-754 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4349,-754 4676,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-493 4559,-493 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4080,-493 4559,-493 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-288 3871,-17 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3871,-288 3871,-17 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4394,-30 4851,-30 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4394,-30 4851,-30 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4881,-969 4881,-608 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4881,-969 4881,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4882,-506 4882,-33 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4882,-506 4882,-33 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-799 3872,-409 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3872,-799 3872,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-1172 3872,-901 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3872,-1172 3872,-901 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="4001400">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4077.000000 -258.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3c4ce30">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3804.000000 -40.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4685340">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3745.500000 -24.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="2d88200">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3671.500000 -98.500000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="34ab050">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3619.500000 -24.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="2f9e890">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3805.000000 -156.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4489f60">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3746.500000 -141.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="422d1e0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3672.500000 -215.500000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="494ad10">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3620.500000 -141.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="40015e0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3805.000000 -259.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="47b1620">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3746.500000 -244.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="48efe20">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3672.500000 -318.500000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="45f9c00">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3620.500000 -244.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="43ce750">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3935.500000 -217.500000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="40d4920">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4271.000000 -258.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4514b80">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4444.000000 -233.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4368790">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4437.000000 -823.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="32a3230">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4422.500000 -882.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4000c20">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4394.000000 -1006.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3dd6de0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4422.500000 -1008.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="351a6b0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4530.000000 -823.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="41a0790">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4515.500000 -882.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="5021f50">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4487.000000 -1006.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="2f88be0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4515.500000 -1008.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="48a5b80">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4626.000000 -825.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="24d68a0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4611.500000 -884.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="291c190">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4583.000000 -1008.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="46c7cf0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4611.500000 -1010.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4962c20">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4621.000000 -109.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="42c5ea0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4546.000000 -233.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4099670">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4632.000000 -676.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="42c5980">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4582.000000 -652.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="49c6ed0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4947.000000 -889.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="33299a0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5006.500000 -904.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="47a1bf0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4729.500000 -895.500000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4314960">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5132.500000 -904.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4312270">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4804.000000 -825.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="43ce030">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4640.000000 -233.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="492e440">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5064.500000 -958.500000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4782000">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4948.000000 -763.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="42e4040">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5007.500000 -778.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4195f00">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5133.500000 -778.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4312c60">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5065.500000 -832.500000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="2115650">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4948.000000 -638.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="47a9d90">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5007.500000 -653.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="407ec20">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5133.500000 -653.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3d723a0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5065.500000 -707.500000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="5023870">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4948.000000 -343.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="46d05a0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5007.500000 -358.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="49181e0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5133.500000 -358.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="45983c0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5065.500000 -412.500000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="36fcb50">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4949.000000 -217.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="37270d0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5008.500000 -232.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="42741f0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5134.500000 -232.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="21356a0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5066.500000 -286.500000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3e25b50">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4949.000000 -92.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="48ec510">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5008.500000 -107.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4db6650">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5134.500000 -107.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4544ca0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5066.500000 -161.500000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="2df2560">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4947.000000 -475.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="27a6d50">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5006.500000 -490.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="29462e0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5132.500000 -490.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="453e250">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5064.500000 -544.500000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3edf720">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4728.500000 -480.500000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="450ec70">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4803.000000 -410.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3014200">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4271.000000 -585.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4675fa0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4515.000000 -565.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3eacac0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4498.000000 -593.000000)" xlink:href="#lightningRod:shape113"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="42c0230">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3805.000000 -454.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="436df20">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3746.500000 -439.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="48faef0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3672.500000 -513.500000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="2d77e70">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3620.500000 -439.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="28069a0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3806.000000 -571.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="291bb00">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3747.500000 -556.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4918c80">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3673.500000 -630.500000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="216b280">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3621.500000 -556.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3dfe250">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3806.000000 -674.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="20b7880">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3747.500000 -659.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="2cfa860">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3673.500000 -733.500000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="477cba0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3621.500000 -659.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="267e780">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3805.000000 -773.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="225c6b0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3746.500000 -758.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3e50ed0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3672.500000 -832.500000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="210fe70">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3620.500000 -758.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="267d6c0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3942.000000 -439.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="211a280">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3938.000000 -393.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="2df16f0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3940.000000 -277.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3d0be80">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3939.000000 -230.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="457e3a0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3805.000000 -923.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="291ce60">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3746.500000 -908.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="29546f0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3672.500000 -982.500000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="20e7090">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3620.500000 -908.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="36df0a0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3806.000000 -1040.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="20b88a0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3747.500000 -1025.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3f70fd0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3673.500000 -1099.500000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3f60770">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3621.500000 -1025.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="43131a0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3806.000000 -1143.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3f7ec80">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3747.500000 -1128.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="43f0170">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3673.500000 -1202.500000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="2e17470">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3621.500000 -1128.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3db3bf0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3944.000000 -947.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="28752b0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3940.000000 -900.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="28761d0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3942.000000 -784.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="20a0270">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3941.000000 -737.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" stroke="rgb(255,0,0)" stroke-width="0.5" x1="3063" x2="3063" y1="-1202" y2="-2"/>
   <line DF8003:Layer="PUBLIC" stroke="rgb(255,0,0)" stroke-width="0.5" x1="3065" x2="3105" y1="-1201" y2="-1201"/>
   <line DF8003:Layer="PUBLIC" stroke="rgb(255,0,0)" stroke-width="0.5" x1="3060" x2="3108" y1="-1078" y2="-1078"/>
   <line DF8003:Layer="PUBLIC" stroke="rgb(255,0,0)" stroke-width="0.5" x1="3065" x2="3090" y1="-594" y2="-594"/>
   <line DF8003:Layer="PUBLIC" stroke="rgb(255,0,0)" stroke-width="0.5" x1="3120" x2="5267" y1="-1212" y2="-1212"/>
   <line DF8003:Layer="PUBLIC" stroke="rgb(255,0,0)" stroke-width="0.5" x1="3118" x2="3118" y1="-1240" y2="-1213"/>
   <line DF8003:Layer="PUBLIC" stroke="rgb(255,0,0)" stroke-width="0.5" x1="3478" x2="3478" y1="-1237" y2="-1213"/>
   <line DF8003:Layer="PUBLIC" stroke="rgb(255,0,0)" stroke-width="0.5" x1="5268" x2="5268" y1="-1236" y2="-1210"/>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="4785250">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4217.000000 -565.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="47a3830">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4218.000000 -414.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="41535c0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4218.000000 -302.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="490ba30">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4218.000000 -364.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="445a8c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 -49.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3e96990">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 -166.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="41a4000">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3756.000000 -268.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="418d6d0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3955.000000 -107.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="418d8d0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4018.000000 -108.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="477d1b0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4088.000000 -107.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="48942a0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4412.000000 -414.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="434c140">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4411.000000 -366.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="40adf40">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4411.000000 -304.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="40bf080">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4470.000000 -106.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="409db50">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4467.000000 -169.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3eb0630">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4466.000000 -233.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="206d530">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4448.000000 -871.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3eac580">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4539.000000 -872.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="210dc90">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4636.000000 -873.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4782670">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4979.000000 -910.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="2940ab0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4980.000000 -784.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4307fb0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4980.000000 -659.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="454ee50">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4980.000000 -364.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3bf8ee0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4981.000000 -238.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="286e3a0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4981.000000 -113.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="25902d0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4979.000000 -496.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="2e4fa80">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4293.000000 -582.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="461f340">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 -464.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="29299a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 -581.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="48f1d30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 -683.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="2810ca0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3756.000000 -782.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="26515a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 -933.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="20b8050">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 -1050.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="20e40e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 -1152.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="437c630">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4222.000000 -876.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4817470">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4224.000000 -933.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="2d7cdf0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4223.000000 -1004.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4134.000000 -636.000000)" xlink:href="#transformer:shape1_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4134.000000 -636.000000)" xlink:href="#transformer:shape1_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4134.000000 -636.000000)" xlink:href="#transformer:shape1-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer"/><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4"/>
</svg>