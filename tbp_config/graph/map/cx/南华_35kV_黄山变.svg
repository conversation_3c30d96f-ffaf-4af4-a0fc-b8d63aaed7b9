<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-184" aopId="3965442" id="thSvg" product="E8000V2" version="1.0" viewBox="1345 -1451 2235 1236">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape29">
    <polyline arcFlag="1" points="26,105 24,105 22,104 21,104 19,103 18,102 16,101 15,99 14,97 14,96 13,94 13,92 13,90 14,88 14,87 15,85 16,84 18,82 19,81 21,80 22,80 24,79 26,79 28,79 30,80 31,80 33,81 34,82 36,84 37,85 38,87 38,88 39,90 39,92 " stroke-width="0.0972"/>
    <polyline arcFlag="1" points="43,30 44,30 45,30 45,30 46,31 46,31 47,31 47,32 48,32 48,33 48,34 48,34 49,35 49,36 49,36 48,37 48,38 48,38 48,39 47,39 47,40 46,40 46,40 45,41 45,41 44,41 43,41 " stroke-width="1"/>
    <rect height="24" stroke-width="0.398039" width="12" x="1" y="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="7" x2="7" y1="60" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="21" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="18" x2="33" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="17" x2="33" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="19" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="42" x2="42" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="9" x2="9" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="7" x2="43" y1="60" y2="60"/>
    <rect height="23" stroke-width="0.369608" width="12" x="20" y="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="26" x2="43" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="26" x2="26" y1="92" y2="26"/>
    <polyline arcFlag="1" points="43,41 44,41 45,41 45,42 46,42 46,42 47,43 47,43 48,44 48,44 48,45 48,45 49,46 49,47 49,47 48,48 48,49 48,49 48,50 47,50 47,51 46,51 46,52 45,52 45,52 44,52 43,52 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,19 44,19 45,19 45,19 46,19 46,20 47,20 47,21 48,21 48,22 48,22 48,23 49,24 49,24 49,25 48,26 48,26 48,27 48,27 47,28 47,28 46,29 46,29 45,29 45,30 44,30 43,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="60" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="105" y2="116"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="39" x2="26" y1="92" y2="92"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape74">
    <circle cx="39" cy="14" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="19" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="19" y2="19"/>
    <circle cx="30" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="30" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="71" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="31" y1="71" y2="71"/>
    <rect height="27" stroke-width="0.416667" width="14" x="24" y="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="82" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0587025" x1="31" x2="34" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173913" x1="30" x2="30" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.108974" x1="30" x2="27" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0587025" x1="31" x2="34" y1="9" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173913" x1="30" x2="30" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.108974" x1="30" x2="27" y1="8" y2="11"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,17 39,15 45,15 43,18 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape59">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="72" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="23" y2="23"/>
    <circle cx="9" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="63" y2="63"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,61 9,39 9,30 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="48"/>
   </symbol>
   <symbol id="lightningRod:shape178">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="131" y2="123"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="107"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="9,120 31,98 31,86 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="28" y1="122" y2="122"/>
    <circle cx="30" cy="71" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="31" y1="71" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="27" y1="71" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="77" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="32" y1="43" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="18" y2="18"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,49 6,49 6,20 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,6 26,19 39,19 32,6 32,7 32,6 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="34" y2="0"/>
    <circle cx="30" cy="49" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="49" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="29" y1="49" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="49" y2="54"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="load:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="5" y2="29"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,19 9,31 17,19 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape8_0">
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape8_1">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="22" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape39_0">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline points="64,96 64,89 " stroke-width="1.1"/>
    <polyline points="58,96 64,96 " stroke-width="1.1"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="transformer2:shape39_1">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,96 1,33 " stroke-width="1.1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="38" y1="64" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="38" y1="64" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="28" y1="71" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="28" y1="71" y2="64"/>
   </symbol>
   <symbol id="voltageTransformer:shape112">
    <circle cx="32" cy="16" fillStyle="0" r="8" stroke-width="0.570276"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="5" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="9" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="32" x2="35" y1="16" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="29" x2="32" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="32" x2="32" y1="16" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="20" x2="23" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="17" x2="20" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="20" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="17" x2="20" y1="26" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="20" x2="23" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="20" x2="20" y1="23" y2="20"/>
    <circle cx="8" cy="16" fillStyle="0" r="7.5" stroke-width="0.536731"/>
    <circle cx="20" cy="9" fillStyle="0" r="8" stroke-width="0.570276"/>
    <circle cx="20" cy="23" fillStyle="0" r="8" stroke-width="0.570276"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3acb940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f031c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3acd4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3acde00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3acec40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3acf750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3ad00b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3ad0ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3acc580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3acc580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3ad3a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3ad3a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3ad57d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3ad57d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_3ad67f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3ad83f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3ad8fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3ad9da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3ada6e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3adbd90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3adc960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3add220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3add9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3adeac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3adf440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3adff30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3ae08f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3ae1db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3ae28e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3ae3910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3ae4550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3af2d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3ae5e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3ae7430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3ae8960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1246" width="2245" x="1340" y="-1456"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-129696">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2260.130412 -987.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23800" ObjectName="SW-NH_HS.NH_HS_302BK"/>
     <cge:Meas_Ref ObjectId="129696"/>
    <cge:TPSR_Ref TObjectID="23800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129555">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2251.263746 -1175.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23730" ObjectName="SW-NH_HS.NH_HS_371BK"/>
     <cge:Meas_Ref ObjectId="129555"/>
    <cge:TPSR_Ref TObjectID="23730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129627">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2553.000000 -802.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23766" ObjectName="SW-NH_HS.NH_HS_012BK"/>
     <cge:Meas_Ref ObjectId="129627"/>
    <cge:TPSR_Ref TObjectID="23766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129701">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2258.930412 -773.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23802" ObjectName="SW-NH_HS.NH_HS_002BK"/>
     <cge:Meas_Ref ObjectId="129701"/>
    <cge:TPSR_Ref TObjectID="23802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129674">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2879.858935 -985.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23794" ObjectName="SW-NH_HS.NH_HS_301BK"/>
     <cge:Meas_Ref ObjectId="129674"/>
    <cge:TPSR_Ref TObjectID="23794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129679">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2878.658935 -772.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23796" ObjectName="SW-NH_HS.NH_HS_001BK"/>
     <cge:Meas_Ref ObjectId="129679"/>
    <cge:TPSR_Ref TObjectID="23796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129566">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2544.228522 -1175.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23734" ObjectName="SW-NH_HS.NH_HS_372BK"/>
     <cge:Meas_Ref ObjectId="129566"/>
    <cge:TPSR_Ref TObjectID="23734"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129577">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2824.763746 -1175.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23738" ObjectName="SW-NH_HS.NH_HS_373BK"/>
     <cge:Meas_Ref ObjectId="129577"/>
    <cge:TPSR_Ref TObjectID="23738"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129588">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.846154 -0.000000 0.000000 -1.000000 3103.579368 -1175.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23742" ObjectName="SW-NH_HS.NH_HS_374BK"/>
     <cge:Meas_Ref ObjectId="129588"/>
    <cge:TPSR_Ref TObjectID="23742"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129599">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1946.000000 -580.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23746" ObjectName="SW-NH_HS.NH_HS_058BK"/>
     <cge:Meas_Ref ObjectId="129599"/>
    <cge:TPSR_Ref TObjectID="23746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129606">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2072.000000 -581.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23751" ObjectName="SW-NH_HS.NH_HS_057BK"/>
     <cge:Meas_Ref ObjectId="129606"/>
    <cge:TPSR_Ref TObjectID="23751"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129613">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2248.000000 -582.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23756" ObjectName="SW-NH_HS.NH_HS_056BK"/>
     <cge:Meas_Ref ObjectId="129613"/>
    <cge:TPSR_Ref TObjectID="23756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129620">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2424.000000 -582.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23761" ObjectName="SW-NH_HS.NH_HS_055BK"/>
     <cge:Meas_Ref ObjectId="129620"/>
    <cge:TPSR_Ref TObjectID="23761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129640">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2658.000000 -581.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23770" ObjectName="SW-NH_HS.NH_HS_053BK"/>
     <cge:Meas_Ref ObjectId="129640"/>
    <cge:TPSR_Ref TObjectID="23770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129647">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2802.250000 -581.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23775" ObjectName="SW-NH_HS.NH_HS_052BK"/>
     <cge:Meas_Ref ObjectId="129647"/>
    <cge:TPSR_Ref TObjectID="23775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129654">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2946.500000 -582.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23780" ObjectName="SW-NH_HS.NH_HS_051BK"/>
     <cge:Meas_Ref ObjectId="129654"/>
    <cge:TPSR_Ref TObjectID="23780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129661">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3090.750000 -580.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23785" ObjectName="SW-NH_HS.NH_HS_054BK"/>
     <cge:Meas_Ref ObjectId="129661"/>
    <cge:TPSR_Ref TObjectID="23785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129668">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3235.000000 -584.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23790" ObjectName="SW-NH_HS.NH_HS_059BK"/>
     <cge:Meas_Ref ObjectId="129668"/>
    <cge:TPSR_Ref TObjectID="23790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191686">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3378.500000 -584.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28972" ObjectName="SW-NH_HS.NH_HS_050BK"/>
     <cge:Meas_Ref ObjectId="191686"/>
    <cge:TPSR_Ref TObjectID="28972"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2bf1a10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1887.000000 -895.000000)" xlink:href="#voltageTransformer:shape112"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c04d20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3121.000000 -899.000000)" xlink:href="#voltageTransformer:shape112"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_LJT" endPointId="0" endStationName="NH_HS" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_luohuang" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3111,-1346 3111,-1377 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38061" ObjectName="AC-35kV.LN_luohuang"/>
    <cge:TPSR_Ref TObjectID="38061_SS-184"/></metadata>
   <polyline fill="none" opacity="0" points="3111,-1346 3111,-1377 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="NH_HS" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_huangxitaiHS" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2833,-1349 2833,-1386 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38082" ObjectName="AC-35kV.LN_huangxitaiHS"/>
    <cge:TPSR_Ref TObjectID="38082_SS-184"/></metadata>
   <polyline fill="none" opacity="0" points="2833,-1349 2833,-1386 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="NH_HS" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_nanhuangdaHS" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2260,-1338 2260,-1371 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38071" ObjectName="AC-35kV.LN_nanhuangdaHS"/>
    <cge:TPSR_Ref TObjectID="38071_SS-184"/></metadata>
   <polyline fill="none" opacity="0" points="2260,-1338 2260,-1371 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-NH_HS.058Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1946.000000 -319.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33910" ObjectName="EC-NH_HS.058Ld"/>
    <cge:TPSR_Ref TObjectID="33910"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_HS.057Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2072.000000 -320.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33911" ObjectName="EC-NH_HS.057Ld"/>
    <cge:TPSR_Ref TObjectID="33911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_HS.056Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2248.000000 -321.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33913" ObjectName="EC-NH_HS.056Ld"/>
    <cge:TPSR_Ref TObjectID="33913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_HS.055Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2424.000000 -321.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33914" ObjectName="EC-NH_HS.055Ld"/>
    <cge:TPSR_Ref TObjectID="33914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_HS.053Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2658.000000 -320.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33915" ObjectName="EC-NH_HS.053Ld"/>
    <cge:TPSR_Ref TObjectID="33915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_HS.052Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2802.250000 -320.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33916" ObjectName="EC-NH_HS.052Ld"/>
    <cge:TPSR_Ref TObjectID="33916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_HS.051Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2947.500000 -321.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33917" ObjectName="EC-NH_HS.051Ld"/>
    <cge:TPSR_Ref TObjectID="33917"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_HS.054Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3090.750000 -319.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33918" ObjectName="EC-NH_HS.054Ld"/>
    <cge:TPSR_Ref TObjectID="33918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_HS.050Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3379.500000 -323.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33919" ObjectName="EC-NH_HS.050Ld"/>
    <cge:TPSR_Ref TObjectID="33919"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_HS.372Ld">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2544.000000 -1346.000000)" xlink:href="#load:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41853" ObjectName="EC-NH_HS.372Ld"/>
    <cge:TPSR_Ref TObjectID="41853"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2ce2120" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1985.000000 -787.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ce2890" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2335.930412 -1280.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d053c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2342.930412 -766.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d05b70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3208.000000 -788.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c9dd50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2960.658935 -767.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d8b450" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2628.895189 -1284.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d15180" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2909.430412 -1284.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d52de0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.846154 -0.000000 0.000000 -1.000000 3176.220394 -1280.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d1bc50" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.846154 -0.000000 -0.000000 1.000000 2470.923077 -807.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cc1ab0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2023.086957 -638.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c7d950" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2149.086957 -639.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c8e6e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2325.086957 -640.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c6d4f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2501.086957 -640.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ca0e70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2735.086957 -639.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cb0710" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2878.336957 -639.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c3c810" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3024.586957 -640.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e2cfb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3168.836957 -638.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e3c850" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3312.086957 -642.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c57fa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3456.586957 -642.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_2d814e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1858,-843 1858,-817 1907,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2a1adf0@0" ObjectIDZND0="g_2d69fa0@0" ObjectIDZND1="23833@x" ObjectIDZND2="23832@x" Pin0InfoVect0LinkObjId="g_2d69fa0_0" Pin0InfoVect1LinkObjId="SW-130039_0" Pin0InfoVect2LinkObjId="SW-129727_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a1adf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1858,-843 1858,-817 1907,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d994b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1989,-793 1968,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2ce2120@0" ObjectIDZND0="23833@1" Pin0InfoVect0LinkObjId="SW-130039_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ce2120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1989,-793 1968,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d03870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2340,-1286 2317,-1286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2ce2890@0" ObjectIDZND0="23733@1" Pin0InfoVect0LinkObjId="SW-129558_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ce2890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2340,-1286 2317,-1286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2db6af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2347,-772 2329,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2d053c0@0" ObjectIDZND0="23804@1" Pin0InfoVect0LinkObjId="SW-129704_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d053c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2347,-772 2329,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dda850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2535,-750 2535,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23767@0" ObjectIDZND0="23835@0" Pin0InfoVect0LinkObjId="g_2c158c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129628_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2535,-750 2535,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ddaa40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2614,-752 2614,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23769@0" ObjectIDZND0="23830@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2614,-752 2614,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ddac30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2614,-788 2614,-812 2589,-812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23769@1" ObjectIDZND0="23766@0" Pin0InfoVect0LinkObjId="SW-129627_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129630_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2614,-788 2614,-812 2589,-812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ce1f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2269,-995 2269,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="23800@0" ObjectIDZND0="23812@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129696_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2269,-995 2269,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d06320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1907,-837 1907,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2d69fa0@1" ObjectIDZND0="g_2a1adf0@0" ObjectIDZND1="23833@x" ObjectIDZND2="23832@x" Pin0InfoVect0LinkObjId="g_2a1adf0_0" Pin0InfoVect1LinkObjId="SW-130039_0" Pin0InfoVect2LinkObjId="SW-129727_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d69fa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1907,-837 1907,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d021a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2269,-1040 2269,-1022 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23801@0" ObjectIDZND0="23800@1" Pin0InfoVect0LinkObjId="SW-129696_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129698_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2269,-1040 2269,-1022 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d02a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2268,-902 2268,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="23812@0" ObjectIDZND0="g_2d02390@1" Pin0InfoVect0LinkObjId="g_2d02390_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ce1f30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2268,-902 2268,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ced580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2268,-858 2268,-867 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23803@1" ObjectIDZND0="g_2d02390@0" Pin0InfoVect0LinkObjId="g_2d02390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129703_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2268,-858 2268,-867 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ceedf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2268,-808 2268,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23802@1" ObjectIDZND0="23803@0" Pin0InfoVect0LinkObjId="SW-129703_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129701_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2268,-808 2268,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dcbe90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2268,-764 2268,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23805@1" ObjectIDZND0="23802@x" ObjectIDZND1="23804@x" Pin0InfoVect0LinkObjId="SW-129701_0" Pin0InfoVect1LinkObjId="SW-129704_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129705_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2268,-764 2268,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dcc0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2268,-772 2268,-781 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23805@x" ObjectIDND1="23804@x" ObjectIDZND0="23802@0" Pin0InfoVect0LinkObjId="SW-129701_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129705_0" Pin1InfoVect1LinkObjId="SW-129704_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2268,-772 2268,-781 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c9d910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2965,-773 2950,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2c9dd50@0" ObjectIDZND0="23798@1" Pin0InfoVect0LinkObjId="SW-129682_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c9dd50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2965,-773 2950,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c9db30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2889,-993 2889,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="23794@0" ObjectIDZND0="23811@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129674_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2889,-993 2889,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ccfd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2889,-1038 2889,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23795@0" ObjectIDZND0="23794@1" Pin0InfoVect0LinkObjId="SW-129674_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129676_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2889,-1038 2889,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cd08c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2888,-900 2888,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="23811@0" ObjectIDZND0="g_2ccffa0@1" Pin0InfoVect0LinkObjId="g_2ccffa0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c9db30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2888,-900 2888,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d58130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2888,-854 2888,-865 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23797@1" ObjectIDZND0="g_2ccffa0@0" Pin0InfoVect0LinkObjId="g_2ccffa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129681_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2888,-854 2888,-865 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5a010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2888,-807 2888,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23796@1" ObjectIDZND0="23797@0" Pin0InfoVect0LinkObjId="SW-129681_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129679_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2888,-807 2888,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d69d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2293,-772 2268,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23804@0" ObjectIDZND0="23805@x" ObjectIDZND1="23802@x" Pin0InfoVect0LinkObjId="SW-129705_0" Pin0InfoVect1LinkObjId="SW-129701_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129704_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2293,-772 2268,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d6a850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1907,-869 1907,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2d69fa0@0" ObjectIDZND0="g_2bf1a10@0" Pin0InfoVect0LinkObjId="g_2bf1a10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d69fa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1907,-869 1907,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dbbcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3092,-848 3092,-820 3141,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2d6cc90@0" ObjectIDZND0="g_2dbc4b0@0" ObjectIDZND1="23834@x" ObjectIDZND2="23809@x" Pin0InfoVect0LinkObjId="g_2dbc4b0_0" Pin0InfoVect1LinkObjId="SW-130038_0" Pin0InfoVect2LinkObjId="SW-129724_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d6cc90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3092,-848 3092,-820 3141,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dbc2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3141,-841 3141,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2dbc4b0@1" ObjectIDZND0="g_2d6cc90@0" ObjectIDZND1="23834@x" ObjectIDZND2="23809@x" Pin0InfoVect0LinkObjId="g_2d6cc90_0" Pin0InfoVect1LinkObjId="SW-130038_0" Pin0InfoVect2LinkObjId="SW-129724_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dbc4b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3141,-841 3141,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dbcba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3141,-875 3141,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2dbc4b0@0" ObjectIDZND0="g_2c04d20@0" Pin0InfoVect0LinkObjId="g_2c04d20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dbc4b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3141,-875 3141,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0b4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2694,-1000 2694,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2a50580@0" ObjectIDZND0="23828@0" Pin0InfoVect0LinkObjId="SW-129720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a50580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2694,-1000 2694,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0da90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2281,-1286 2260,-1286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="23733@0" ObjectIDZND0="23731@x" ObjectIDZND1="g_2cd7b70@0" ObjectIDZND2="g_2cbc1a0@0" Pin0InfoVect0LinkObjId="SW-129556_0" Pin0InfoVect1LinkObjId="g_2cd7b70_0" Pin0InfoVect2LinkObjId="g_2cbc1a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129558_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2281,-1286 2260,-1286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0dcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2260,-1286 2260,-1270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23733@x" ObjectIDND1="g_2cd7b70@0" ObjectIDND2="g_2cbc1a0@0" ObjectIDZND0="23731@1" Pin0InfoVect0LinkObjId="SW-129556_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-129558_0" Pin1InfoVect1LinkObjId="g_2cd7b70_0" Pin1InfoVect2LinkObjId="g_2cbc1a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2260,-1286 2260,-1270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d8b230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2633,-1290 2610,-1290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2d8b450@0" ObjectIDZND0="23737@1" Pin0InfoVect0LinkObjId="SW-129569_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d8b450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2633,-1290 2610,-1290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2da6830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2553,-1148 2553,-1183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23736@1" ObjectIDZND0="23734@0" Pin0InfoVect0LinkObjId="SW-129566_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129568_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2553,-1148 2553,-1183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2da6a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2553,-1210 2553,-1238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23734@1" ObjectIDZND0="23735@0" Pin0InfoVect0LinkObjId="SW-129567_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129566_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2553,-1210 2553,-1238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2da6c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2574,-1290 2553,-1290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="23737@0" ObjectIDZND0="23735@x" ObjectIDZND1="g_2d72be0@0" ObjectIDZND2="g_2cbaeb0@0" Pin0InfoVect0LinkObjId="SW-129567_0" Pin0InfoVect1LinkObjId="g_2d72be0_0" Pin0InfoVect2LinkObjId="g_2cbaeb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129569_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2574,-1290 2553,-1290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2da6e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2553,-1290 2553,-1274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23737@x" ObjectIDND1="g_2d72be0@0" ObjectIDND2="g_2cbaeb0@0" ObjectIDZND0="23735@1" Pin0InfoVect0LinkObjId="SW-129567_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-129569_0" Pin1InfoVect1LinkObjId="g_2d72be0_0" Pin1InfoVect2LinkObjId="g_2cbaeb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2553,-1290 2553,-1274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d14820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-1290 2890,-1290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2d15180@0" ObjectIDZND0="23741@1" Pin0InfoVect0LinkObjId="SW-129580_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d15180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-1290 2890,-1290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d15ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2854,-1290 2833,-1290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="23741@0" ObjectIDZND0="23739@x" ObjectIDZND1="g_2d74a60@0" ObjectIDZND2="g_2cb9e00@0" Pin0InfoVect0LinkObjId="SW-129578_0" Pin0InfoVect1LinkObjId="g_2d74a60_0" Pin0InfoVect2LinkObjId="g_2cb9e00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2854,-1290 2833,-1290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d15f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2833,-1290 2833,-1274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23741@x" ObjectIDND1="g_2d74a60@0" ObjectIDND2="g_2cb9e00@0" ObjectIDZND0="23739@1" Pin0InfoVect0LinkObjId="SW-129578_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-129580_0" Pin1InfoVect1LinkObjId="g_2d74a60_0" Pin1InfoVect2LinkObjId="g_2cb9e00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2833,-1290 2833,-1274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d52690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3179,-1286 3160,-1286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2d52de0@0" ObjectIDZND0="23744@1" Pin0InfoVect0LinkObjId="SW-129590_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d52de0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3179,-1286 3160,-1286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d53620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3129,-1286 3111,-1286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="23744@0" ObjectIDZND0="g_2d769e0@0" ObjectIDZND1="g_2d1eac0@0" ObjectIDZND2="38061@1" Pin0InfoVect0LinkObjId="g_2d769e0_0" Pin0InfoVect1LinkObjId="g_2d1eac0_0" Pin0InfoVect2LinkObjId="g_2d1e860_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3129,-1286 3111,-1286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d1b9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2467,-801 2486,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2d1bc50@0" ObjectIDZND0="23768@1" Pin0InfoVect0LinkObjId="SW-129629_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d1bc50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2467,-801 2486,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d1c6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2516,-801 2534,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23768@0" ObjectIDZND0="23766@x" ObjectIDZND1="23767@x" Pin0InfoVect0LinkObjId="SW-129627_0" Pin0InfoVect1LinkObjId="SW-129628_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129629_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2516,-801 2534,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d1c940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2562,-812 2535,-812 2535,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="23766@1" ObjectIDZND0="23768@x" ObjectIDZND1="23767@x" Pin0InfoVect0LinkObjId="SW-129629_0" Pin0InfoVect1LinkObjId="SW-129628_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129627_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2562,-812 2535,-812 2535,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d1cba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2535,-801 2535,-786 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23768@x" ObjectIDND1="23766@x" ObjectIDZND0="23767@1" Pin0InfoVect0LinkObjId="SW-129628_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129629_0" Pin1InfoVect1LinkObjId="SW-129627_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2535,-801 2535,-786 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cd7910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2535,-1009 2535,-988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2cd7030@1" ObjectIDZND0="23813@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cd7030_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2535,-1009 2535,-988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d71a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2308,-1326 2260,-1326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2cd7b70@0" ObjectIDZND0="23733@x" ObjectIDZND1="23731@x" ObjectIDZND2="g_2cbc1a0@0" Pin0InfoVect0LinkObjId="SW-129558_0" Pin0InfoVect1LinkObjId="SW-129556_0" Pin0InfoVect2LinkObjId="g_2cbc1a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cd7b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2308,-1326 2260,-1326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d72720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2260,-1286 2260,-1326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="23733@x" ObjectIDND1="23731@x" ObjectIDZND0="g_2cd7b70@0" ObjectIDZND1="g_2cbc1a0@0" ObjectIDZND2="38071@1" Pin0InfoVect0LinkObjId="g_2cd7b70_0" Pin0InfoVect1LinkObjId="g_2cbc1a0_0" Pin0InfoVect2LinkObjId="g_2d72980_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129558_0" Pin1InfoVect1LinkObjId="SW-129556_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2260,-1286 2260,-1326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d72980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2260,-1326 2260,-1339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2cd7b70@0" ObjectIDND1="23733@x" ObjectIDND2="23731@x" ObjectIDZND0="38071@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cd7b70_0" Pin1InfoVect1LinkObjId="SW-129558_0" Pin1InfoVect2LinkObjId="SW-129556_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2260,-1326 2260,-1339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d73850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2602,-1329 2554,-1329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2d72be0@0" ObjectIDZND0="23737@x" ObjectIDZND1="23735@x" ObjectIDZND2="g_2cbaeb0@0" Pin0InfoVect0LinkObjId="SW-129569_0" Pin0InfoVect1LinkObjId="SW-129567_0" Pin0InfoVect2LinkObjId="g_2cbaeb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d72be0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2602,-1329 2554,-1329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d745a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2553,-1290 2553,-1329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="23737@x" ObjectIDND1="23735@x" ObjectIDZND0="g_2d72be0@0" ObjectIDZND1="g_2cbaeb0@0" ObjectIDZND2="41853@x" Pin0InfoVect0LinkObjId="g_2d72be0_0" Pin0InfoVect1LinkObjId="g_2cbaeb0_0" Pin0InfoVect2LinkObjId="EC-NH_HS.372Ld_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129569_0" Pin1InfoVect1LinkObjId="SW-129567_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2553,-1290 2553,-1329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d74800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2553,-1329 2553,-1351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_2d72be0@0" ObjectIDND1="23737@x" ObjectIDND2="23735@x" ObjectIDZND0="41853@0" Pin0InfoVect0LinkObjId="EC-NH_HS.372Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d72be0_0" Pin1InfoVect1LinkObjId="SW-129569_0" Pin1InfoVect2LinkObjId="SW-129567_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2553,-1329 2553,-1351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d757d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2881,-1328 2833,-1328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2d74a60@0" ObjectIDZND0="23741@x" ObjectIDZND1="23739@x" ObjectIDZND2="g_2cb9e00@0" Pin0InfoVect0LinkObjId="SW-129580_0" Pin0InfoVect1LinkObjId="SW-129578_0" Pin0InfoVect2LinkObjId="g_2cb9e00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d74a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2881,-1328 2833,-1328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d76520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2833,-1290 2833,-1328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="23741@x" ObjectIDND1="23739@x" ObjectIDZND0="g_2d74a60@0" ObjectIDZND1="g_2cb9e00@0" ObjectIDZND2="38082@1" Pin0InfoVect0LinkObjId="g_2d74a60_0" Pin0InfoVect1LinkObjId="g_2cb9e00_0" Pin0InfoVect2LinkObjId="g_2d76780_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129580_0" Pin1InfoVect1LinkObjId="SW-129578_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2833,-1290 2833,-1328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d76780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2833,-1328 2833,-1350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2d74a60@0" ObjectIDND1="23741@x" ObjectIDND2="23739@x" ObjectIDZND0="38082@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d74a60_0" Pin1InfoVect1LinkObjId="SW-129580_0" Pin1InfoVect2LinkObjId="SW-129578_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2833,-1328 2833,-1350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1e600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3111,-1286 3111,-1327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="23743@x" ObjectIDND1="23744@x" ObjectIDZND0="g_2d769e0@0" ObjectIDZND1="g_2d1eac0@0" ObjectIDZND2="38061@1" Pin0InfoVect0LinkObjId="g_2d769e0_0" Pin0InfoVect1LinkObjId="g_2d1eac0_0" Pin0InfoVect2LinkObjId="g_2d1e860_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129589_0" Pin1InfoVect1LinkObjId="SW-129590_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3111,-1286 3111,-1327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1e860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3111,-1327 3111,-1346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2d769e0@0" ObjectIDND1="23743@x" ObjectIDND2="23744@x" ObjectIDZND0="38061@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d769e0_0" Pin1InfoVect1LinkObjId="SW-129589_0" Pin1InfoVect2LinkObjId="SW-129590_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3111,-1327 3111,-1346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d20850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3111,-1327 3159,-1327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="23743@x" ObjectIDND1="23744@x" ObjectIDND2="g_2d1eac0@0" ObjectIDZND0="g_2d769e0@0" Pin0InfoVect0LinkObjId="g_2d769e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-129589_0" Pin1InfoVect1LinkObjId="SW-129590_0" Pin1InfoVect2LinkObjId="g_2d1eac0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3111,-1327 3159,-1327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d231b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3111,-1267 3111,-1286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="23743@1" ObjectIDZND0="g_2d769e0@0" ObjectIDZND1="g_2d1eac0@0" ObjectIDZND2="38061@1" Pin0InfoVect0LinkObjId="g_2d769e0_0" Pin0InfoVect1LinkObjId="g_2d1eac0_0" Pin0InfoVect2LinkObjId="g_2d1e860_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129589_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3111,-1267 3111,-1286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d68ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2260,-1144 2260,-1183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23732@1" ObjectIDZND0="23730@0" Pin0InfoVect0LinkObjId="SW-129555_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129557_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2260,-1144 2260,-1183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d690e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2260,-1210 2260,-1234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23730@1" ObjectIDZND0="23731@0" Pin0InfoVect0LinkObjId="SW-129556_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129555_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2260,-1210 2260,-1234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d692d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2833,-1148 2833,-1183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23740@1" ObjectIDZND0="23738@0" Pin0InfoVect0LinkObjId="SW-129577_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129579_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2833,-1148 2833,-1183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d69500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2833,-1210 2833,-1238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23738@1" ObjectIDZND0="23739@0" Pin0InfoVect0LinkObjId="SW-129578_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129577_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2833,-1210 2833,-1238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d69730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3111,-1143 3111,-1183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23745@1" ObjectIDZND0="23742@0" Pin0InfoVect0LinkObjId="SW-129588_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129591_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3111,-1143 3111,-1183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d69960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3111,-1210 3111,-1241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23742@1" ObjectIDZND0="23743@0" Pin0InfoVect0LinkObjId="SW-129589_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129588_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3111,-1210 3111,-1241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cb9c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3111,-1327 3051,-1327 3051,-1306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2d769e0@0" ObjectIDND1="23743@x" ObjectIDND2="23744@x" ObjectIDZND0="g_2d1eac0@0" Pin0InfoVect0LinkObjId="g_2d1eac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d769e0_0" Pin1InfoVect1LinkObjId="SW-129589_0" Pin1InfoVect2LinkObjId="SW-129590_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3111,-1327 3051,-1327 3051,-1306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cbac50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2835,-1329 2776,-1329 2776,-1308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2d74a60@0" ObjectIDND1="23741@x" ObjectIDND2="23739@x" ObjectIDZND0="g_2cb9e00@0" Pin0InfoVect0LinkObjId="g_2cb9e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d74a60_0" Pin1InfoVect1LinkObjId="SW-129580_0" Pin1InfoVect2LinkObjId="SW-129578_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2835,-1329 2776,-1329 2776,-1308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cbbf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2553,-1329 2494,-1329 2494,-1308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2d72be0@0" ObjectIDND1="23737@x" ObjectIDND2="23735@x" ObjectIDZND0="g_2cbaeb0@0" Pin0InfoVect0LinkObjId="g_2cbaeb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d72be0_0" Pin1InfoVect1LinkObjId="SW-129569_0" Pin1InfoVect2LinkObjId="SW-129567_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2553,-1329 2494,-1329 2494,-1308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cbd230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2260,-1326 2201,-1326 2201,-1305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2cd7b70@0" ObjectIDND1="23733@x" ObjectIDND2="23731@x" ObjectIDZND0="g_2cbc1a0@0" Pin0InfoVect0LinkObjId="g_2cbc1a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cd7b70_0" Pin1InfoVect1LinkObjId="SW-129558_0" Pin1InfoVect2LinkObjId="SW-129556_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2260,-1326 2201,-1326 2201,-1305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cf18d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1907,-793 1932,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23832@x" ObjectIDND1="g_2a1adf0@0" ObjectIDND2="g_2d69fa0@0" ObjectIDZND0="23833@0" Pin0InfoVect0LinkObjId="SW-130039_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-129727_0" Pin1InfoVect1LinkObjId="g_2a1adf0_0" Pin1InfoVect2LinkObjId="g_2d69fa0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1907,-793 1932,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cf82d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1955,-720 1955,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23835@0" ObjectIDZND0="23748@1" Pin0InfoVect0LinkObjId="SW-129601_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dda850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1955,-720 1955,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ccad00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1955,-512 1955,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23747@0" ObjectIDZND0="g_2cca2e0@1" Pin0InfoVect0LinkObjId="g_2cca2e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1955,-512 1955,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ccaf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1955,-433 1955,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2cca2e0@0" ObjectIDZND0="23750@1" Pin0InfoVect0LinkObjId="SW-129603_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cca2e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1955,-433 1955,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ccdc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1955,-366 1955,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2cc9570@0" ObjectIDND1="23750@x" ObjectIDZND0="33910@0" Pin0InfoVect0LinkObjId="EC-NH_HS.058Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2cc9570_0" Pin1InfoVect1LinkObjId="SW-129603_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1955,-366 1955,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ccded0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1999,-366 1955,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2cc9570@0" ObjectIDZND0="33910@x" ObjectIDZND1="23750@x" Pin0InfoVect0LinkObjId="EC-NH_HS.058Ld_0" Pin0InfoVect1LinkObjId="SW-129603_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cc9570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1999,-366 1955,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cce130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1955,-366 1955,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33910@x" ObjectIDND1="g_2cc9570@0" ObjectIDZND0="23750@0" Pin0InfoVect0LinkObjId="SW-129603_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-NH_HS.058Ld_0" Pin1InfoVect1LinkObjId="g_2cc9570_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1955,-366 1955,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ccf340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2081,-720 2081,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23835@0" ObjectIDZND0="23753@1" Pin0InfoVect0LinkObjId="SW-129608_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dda850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2081,-720 2081,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c86c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2081,-513 2081,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23752@0" ObjectIDZND0="g_2c86220@1" Pin0InfoVect0LinkObjId="g_2c86220_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129607_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2081,-513 2081,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c86ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2081,-434 2081,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c86220@0" ObjectIDZND0="23755@1" Pin0InfoVect0LinkObjId="SW-129610_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c86220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2081,-434 2081,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c89bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2081,-367 2081,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2c854b0@0" ObjectIDND1="23755@x" ObjectIDZND0="33911@0" Pin0InfoVect0LinkObjId="EC-NH_HS.057Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c854b0_0" Pin1InfoVect1LinkObjId="SW-129610_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2081,-367 2081,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c89e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2125,-367 2081,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2c854b0@0" ObjectIDZND0="33911@x" ObjectIDZND1="23755@x" Pin0InfoVect0LinkObjId="EC-NH_HS.057Ld_0" Pin0InfoVect1LinkObjId="SW-129610_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c854b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2125,-367 2081,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c8a070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2081,-367 2081,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2c854b0@0" ObjectIDND1="33911@x" ObjectIDZND0="23755@0" Pin0InfoVect0LinkObjId="SW-129610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c854b0_0" Pin1InfoVect1LinkObjId="EC-NH_HS.057Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2081,-367 2081,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c979d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2257,-514 2257,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23757@0" ObjectIDZND0="g_2c96fb0@1" Pin0InfoVect0LinkObjId="g_2c96fb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129614_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2257,-514 2257,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c97c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2257,-435 2257,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c96fb0@0" ObjectIDZND0="23760@1" Pin0InfoVect0LinkObjId="SW-129617_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c96fb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2257,-435 2257,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c9a940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2257,-368 2257,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_2c96240@0" ObjectIDND1="g_2f0e460@0" ObjectIDND2="23760@x" ObjectIDZND0="33913@0" Pin0InfoVect0LinkObjId="EC-NH_HS.056Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c96240_0" Pin1InfoVect1LinkObjId="g_2f0e460_0" Pin1InfoVect2LinkObjId="SW-129617_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2257,-368 2257,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c9aba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2301,-368 2257,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2c96240@0" ObjectIDZND0="33913@x" ObjectIDZND1="g_2f0e460@0" ObjectIDZND2="23760@x" Pin0InfoVect0LinkObjId="EC-NH_HS.056Ld_0" Pin0InfoVect1LinkObjId="g_2f0e460_0" Pin0InfoVect2LinkObjId="SW-129617_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c96240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2301,-368 2257,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c6ac50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2433,-720 2433,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23835@0" ObjectIDZND0="23763@1" Pin0InfoVect0LinkObjId="SW-129622_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dda850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2433,-720 2433,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c767e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2433,-514 2433,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23762@0" ObjectIDZND0="g_2c75dc0@1" Pin0InfoVect0LinkObjId="g_2c75dc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129621_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2433,-514 2433,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c76a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2433,-435 2433,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c75dc0@0" ObjectIDZND0="23765@1" Pin0InfoVect0LinkObjId="SW-129624_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c75dc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2433,-435 2433,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c79750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2433,-368 2433,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2c75050@0" ObjectIDND1="23765@x" ObjectIDZND0="33914@0" Pin0InfoVect0LinkObjId="EC-NH_HS.055Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c75050_0" Pin1InfoVect1LinkObjId="SW-129624_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2433,-368 2433,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c799b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2477,-368 2433,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2c75050@0" ObjectIDZND0="33914@x" ObjectIDZND1="23765@x" Pin0InfoVect0LinkObjId="EC-NH_HS.055Ld_0" Pin0InfoVect1LinkObjId="SW-129624_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c75050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2477,-368 2433,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c79c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2433,-368 2433,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2c75050@0" ObjectIDND1="33914@x" ObjectIDZND0="23765@0" Pin0InfoVect0LinkObjId="SW-129624_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c75050_0" Pin1InfoVect1LinkObjId="EC-NH_HS.055Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2433,-368 2433,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c9e550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2667,-720 2667,-689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23830@0" ObjectIDZND0="23772@1" Pin0InfoVect0LinkObjId="SW-129642_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ddaa40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2667,-720 2667,-689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2caa160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2667,-513 2667,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23771@0" ObjectIDZND0="g_2ca9740@1" Pin0InfoVect0LinkObjId="g_2ca9740_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129641_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2667,-513 2667,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2caa3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2667,-434 2667,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2ca9740@0" ObjectIDZND0="23774@1" Pin0InfoVect0LinkObjId="SW-129644_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ca9740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2667,-434 2667,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cad0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2667,-367 2667,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="23774@x" ObjectIDND1="g_2ca89d0@0" ObjectIDZND0="33915@0" Pin0InfoVect0LinkObjId="EC-NH_HS.053Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129644_0" Pin1InfoVect1LinkObjId="g_2ca89d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2667,-367 2667,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cad330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2711,-367 2667,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2ca89d0@0" ObjectIDZND0="23774@x" ObjectIDZND1="33915@x" Pin0InfoVect0LinkObjId="SW-129644_0" Pin0InfoVect1LinkObjId="EC-NH_HS.053Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ca89d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2711,-367 2667,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cad590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2667,-367 2667,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2ca89d0@0" ObjectIDND1="33915@x" ObjectIDZND0="23774@0" Pin0InfoVect0LinkObjId="SW-129644_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ca89d0_0" Pin1InfoVect1LinkObjId="EC-NH_HS.053Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2667,-367 2667,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c359d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2811,-513 2811,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23776@0" ObjectIDZND0="g_2c34fb0@1" Pin0InfoVect0LinkObjId="g_2c34fb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129648_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2811,-513 2811,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c35c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2811,-434 2811,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c34fb0@0" ObjectIDZND0="23779@1" Pin0InfoVect0LinkObjId="SW-129651_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c34fb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2811,-434 2811,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c38940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2811,-367 2811,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2c34240@0" ObjectIDND1="23779@x" ObjectIDZND0="33916@0" Pin0InfoVect0LinkObjId="EC-NH_HS.052Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c34240_0" Pin1InfoVect1LinkObjId="SW-129651_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2811,-367 2811,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c38ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2855,-367 2811,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2c34240@0" ObjectIDZND0="33916@x" ObjectIDZND1="23779@x" Pin0InfoVect0LinkObjId="EC-NH_HS.052Ld_0" Pin0InfoVect1LinkObjId="SW-129651_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c34240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2855,-367 2811,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c38e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2811,-367 2811,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2c34240@0" ObjectIDND1="33916@x" ObjectIDZND0="23779@0" Pin0InfoVect0LinkObjId="SW-129651_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c34240_0" Pin1InfoVect1LinkObjId="EC-NH_HS.052Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2811,-367 2811,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c45b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2956,-514 2956,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23781@0" ObjectIDZND0="g_2c450e0@1" Pin0InfoVect0LinkObjId="g_2c450e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129655_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2956,-514 2956,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c45d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2956,-435 2956,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c450e0@0" ObjectIDZND0="23784@1" Pin0InfoVect0LinkObjId="SW-129658_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c450e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2956,-435 2956,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e29710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2956,-368 2956,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="23784@x" ObjectIDND1="g_2c44370@0" ObjectIDZND0="33917@0" Pin0InfoVect0LinkObjId="EC-NH_HS.051Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129658_0" Pin1InfoVect1LinkObjId="g_2c44370_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2956,-368 2956,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e29970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3000,-368 2956,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2c44370@0" ObjectIDZND0="23784@x" ObjectIDZND1="33917@x" Pin0InfoVect0LinkObjId="SW-129658_0" Pin0InfoVect1LinkObjId="EC-NH_HS.051Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c44370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3000,-368 2956,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e29bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2956,-368 2956,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2c44370@0" ObjectIDND1="33917@x" ObjectIDZND0="23784@0" Pin0InfoVect0LinkObjId="SW-129658_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c44370_0" Pin1InfoVect1LinkObjId="EC-NH_HS.051Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2956,-368 2956,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e2a550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3100,-720 3100,-691 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23830@0" ObjectIDZND0="23787@1" Pin0InfoVect0LinkObjId="SW-129663_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ddaa40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3100,-720 3100,-691 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e362a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3100,-512 3100,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23786@0" ObjectIDZND0="g_2e35880@1" Pin0InfoVect0LinkObjId="g_2e35880_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129662_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3100,-512 3100,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e36500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3100,-433 3100,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2e35880@0" ObjectIDZND0="23789@1" Pin0InfoVect0LinkObjId="SW-129665_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e35880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3100,-433 3100,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e39210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3100,-366 3100,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="23789@x" ObjectIDND1="g_2e34b10@0" ObjectIDZND0="33918@0" Pin0InfoVect0LinkObjId="EC-NH_HS.054Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129665_0" Pin1InfoVect1LinkObjId="g_2e34b10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3100,-366 3100,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e39470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3144,-366 3100,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2e34b10@0" ObjectIDZND0="23789@x" ObjectIDZND1="33918@x" Pin0InfoVect0LinkObjId="SW-129665_0" Pin0InfoVect1LinkObjId="EC-NH_HS.054Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e34b10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3144,-366 3100,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e396d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3100,-366 3100,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2e34b10@0" ObjectIDND1="33918@x" ObjectIDZND0="23789@0" Pin0InfoVect0LinkObjId="SW-129665_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e34b10_0" Pin1InfoVect1LinkObjId="EC-NH_HS.054Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3100,-366 3100,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c51240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3244,-464 3244,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_2c4dd70@0" ObjectIDZND0="39833@1" Pin0InfoVect0LinkObjId="CB-NH_HS.NH_HS_Cb1_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c4dd70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3244,-464 3244,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c52c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2956,-720 2956,-690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23830@0" ObjectIDZND0="23782@1" Pin0InfoVect0LinkObjId="SW-129656_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ddaa40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2956,-720 2956,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c52e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2889,-720 2888,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23830@0" ObjectIDZND0="23799@0" Pin0InfoVect0LinkObjId="SW-129683_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ddaa40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2889,-720 2888,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c5eaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3388,-516 3388,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="28973@0" ObjectIDZND0="g_2c5e080@1" Pin0InfoVect0LinkObjId="g_2c5e080_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3388,-516 3388,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c617b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3388,-370 3388,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="g_2c5d310@0" ObjectIDND1="g_2c5e080@0" ObjectIDZND0="33919@0" Pin0InfoVect0LinkObjId="EC-NH_HS.050Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c5d310_0" Pin1InfoVect1LinkObjId="g_2c5e080_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3388,-370 3388,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c61a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3432,-370 3388,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="g_2c5d310@0" ObjectIDZND0="33919@x" ObjectIDZND1="g_2c5e080@0" Pin0InfoVect0LinkObjId="EC-NH_HS.050Ld_0" Pin0InfoVect1LinkObjId="g_2c5e080_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5d310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3432,-370 3388,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c63020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3388,-720 3388,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23830@0" ObjectIDZND0="28974@1" Pin0InfoVect0LinkObjId="SW-191688_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ddaa40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3388,-720 3388,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c63990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3388,-437 3388,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="g_2c5e080@0" ObjectIDZND0="33919@x" ObjectIDZND1="g_2c5d310@0" Pin0InfoVect0LinkObjId="EC-NH_HS.050Ld_0" Pin0InfoVect1LinkObjId="g_2c5d310_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c5e080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3388,-437 3388,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf37e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1907,-720 1907,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23835@0" ObjectIDZND0="23832@0" Pin0InfoVect0LinkObjId="SW-129727_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dda850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1907,-720 1907,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf42d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1907,-768 1907,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="23832@1" ObjectIDZND0="23833@x" ObjectIDZND1="g_2a1adf0@0" ObjectIDZND2="g_2d69fa0@0" Pin0InfoVect0LinkObjId="SW-130039_0" Pin0InfoVect1LinkObjId="g_2a1adf0_0" Pin0InfoVect2LinkObjId="g_2d69fa0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129727_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1907,-768 1907,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf4530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1907,-793 1907,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="23833@x" ObjectIDND1="23832@x" ObjectIDZND0="g_2a1adf0@0" ObjectIDZND1="g_2d69fa0@0" Pin0InfoVect0LinkObjId="g_2a1adf0_0" Pin0InfoVect1LinkObjId="g_2d69fa0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-130039_0" Pin1InfoVect1LinkObjId="SW-129727_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1907,-793 1907,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf4790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1955,-566 1923,-566 1923,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="23747@x" ObjectIDND1="23746@x" ObjectIDZND0="g_2bf54e0@0" Pin0InfoVect0LinkObjId="g_2bf54e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129600_0" Pin1InfoVect1LinkObjId="SW-129599_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1955,-566 1923,-566 1923,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf5280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1955,-566 1955,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2bf54e0@0" ObjectIDND1="23746@x" ObjectIDZND0="23747@1" Pin0InfoVect0LinkObjId="SW-129600_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bf54e0_0" Pin1InfoVect1LinkObjId="SW-129599_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1955,-566 1955,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf6f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2080,-570 2048,-570 2048,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="23752@x" ObjectIDND1="23751@x" ObjectIDZND0="g_2bf6210@0" Pin0InfoVect0LinkObjId="g_2bf6210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129607_0" Pin1InfoVect1LinkObjId="SW-129606_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2080,-570 2048,-570 2048,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf7ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2433,-575 2401,-575 2401,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="23762@x" ObjectIDND1="23761@x" ObjectIDZND0="g_2bf71a0@0" Pin0InfoVect0LinkObjId="g_2bf71a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129621_0" Pin1InfoVect1LinkObjId="SW-129620_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2433,-575 2401,-575 2401,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf89c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2081,-570 2081,-549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2bf6210@0" ObjectIDND1="23751@x" ObjectIDZND0="23752@1" Pin0InfoVect0LinkObjId="SW-129607_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bf6210_0" Pin1InfoVect1LinkObjId="SW-129606_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2081,-570 2081,-549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf9950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2256,-573 2224,-573 2224,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="23757@x" ObjectIDND1="23756@x" ObjectIDZND0="g_2bf8c20@0" Pin0InfoVect0LinkObjId="g_2bf8c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129614_0" Pin1InfoVect1LinkObjId="SW-129613_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2256,-573 2224,-573 2224,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bfa440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2257,-573 2257,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2bf8c20@0" ObjectIDND1="23756@x" ObjectIDZND0="23757@1" Pin0InfoVect0LinkObjId="SW-129614_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bf8c20_0" Pin1InfoVect1LinkObjId="SW-129613_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2257,-573 2257,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bfb3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2666,-569 2634,-569 2634,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="23771@x" ObjectIDND1="23770@x" ObjectIDZND0="g_2bfa6a0@0" Pin0InfoVect0LinkObjId="g_2bfa6a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129641_0" Pin1InfoVect1LinkObjId="SW-129640_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2666,-569 2634,-569 2634,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bfc360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2811,-571 2779,-571 2779,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="23776@x" ObjectIDND1="23775@x" ObjectIDZND0="g_2bfb630@0" Pin0InfoVect0LinkObjId="g_2bfb630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129648_0" Pin1InfoVect1LinkObjId="SW-129647_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2811,-571 2779,-571 2779,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bfce50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2433,-575 2433,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2bf71a0@0" ObjectIDND1="23761@x" ObjectIDZND0="23762@1" Pin0InfoVect0LinkObjId="SW-129621_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bf71a0_0" Pin1InfoVect1LinkObjId="SW-129620_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2433,-575 2433,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bfd940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2667,-569 2667,-549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2bfa6a0@0" ObjectIDND1="23770@x" ObjectIDZND0="23771@1" Pin0InfoVect0LinkObjId="SW-129641_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bfa6a0_0" Pin1InfoVect1LinkObjId="SW-129640_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2667,-569 2667,-549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bfe430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2811,-571 2811,-549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2bfb630@0" ObjectIDND1="23775@x" ObjectIDZND0="23776@1" Pin0InfoVect0LinkObjId="SW-129648_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bfb630_0" Pin1InfoVect1LinkObjId="SW-129647_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2811,-571 2811,-549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bff3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2955,-569 2923,-569 2923,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="23781@x" ObjectIDND1="23780@x" ObjectIDZND0="g_2bfe690@0" Pin0InfoVect0LinkObjId="g_2bfe690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129655_0" Pin1InfoVect1LinkObjId="SW-129654_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2955,-569 2923,-569 2923,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bffeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2956,-569 2956,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2bfe690@0" ObjectIDND1="23780@x" ObjectIDZND0="23781@1" Pin0InfoVect0LinkObjId="SW-129655_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bfe690_0" Pin1InfoVect1LinkObjId="SW-129654_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2956,-569 2956,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c00e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3100,-567 3068,-567 3068,-549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="23786@x" ObjectIDND1="23785@x" ObjectIDZND0="g_2c00110@0" Pin0InfoVect0LinkObjId="g_2c00110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129662_0" Pin1InfoVect1LinkObjId="SW-129661_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3100,-567 3068,-567 3068,-549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c01930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3100,-567 3100,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2c00110@0" ObjectIDND1="23785@x" ObjectIDZND0="23786@1" Pin0InfoVect0LinkObjId="SW-129662_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c00110_0" Pin1InfoVect1LinkObjId="SW-129661_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3100,-567 3100,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c01b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3244,-517 3244,-531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c4dd70@1" ObjectIDZND0="23791@0" Pin0InfoVect0LinkObjId="SW-129669_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c4dd70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3244,-517 3244,-531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c02b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3244,-578 3212,-578 3212,-560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="23791@x" ObjectIDND1="23790@x" ObjectIDZND0="g_2c01df0@0" Pin0InfoVect0LinkObjId="g_2c01df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129669_0" Pin1InfoVect1LinkObjId="SW-129668_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3244,-578 3212,-578 3212,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c03610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3244,-567 3244,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="23791@1" ObjectIDZND0="g_2c01df0@0" ObjectIDZND1="23790@x" Pin0InfoVect0LinkObjId="g_2c01df0_0" Pin0InfoVect1LinkObjId="SW-129668_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129669_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3244,-567 3244,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c03870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3141,-720 3141,-733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23830@0" ObjectIDZND0="23809@0" Pin0InfoVect0LinkObjId="SW-129724_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ddaa40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3141,-720 3141,-733 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c03ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3212,-794 3191,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2d05b70@0" ObjectIDZND0="23834@1" Pin0InfoVect0LinkObjId="SW-130038_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d05b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3212,-794 3191,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c03e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3155,-794 3141,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="23834@0" ObjectIDZND0="23809@x" ObjectIDZND1="g_2dbc4b0@0" ObjectIDZND2="g_2d6cc90@0" Pin0InfoVect0LinkObjId="SW-129724_0" Pin0InfoVect1LinkObjId="g_2dbc4b0_0" Pin0InfoVect2LinkObjId="g_2d6cc90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130038_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3155,-794 3141,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c04860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3141,-769 3141,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="23809@1" ObjectIDZND0="23834@x" ObjectIDZND1="g_2dbc4b0@0" ObjectIDZND2="g_2d6cc90@0" Pin0InfoVect0LinkObjId="SW-130038_0" Pin0InfoVect1LinkObjId="g_2dbc4b0_0" Pin0InfoVect2LinkObjId="g_2d6cc90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129724_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3141,-769 3141,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c04ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3141,-794 3141,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="23834@x" ObjectIDND1="23809@x" ObjectIDZND0="g_2dbc4b0@0" ObjectIDZND1="g_2d6cc90@0" Pin0InfoVect0LinkObjId="g_2dbc4b0_0" Pin0InfoVect1LinkObjId="g_2d6cc90_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-130038_0" Pin1InfoVect1LinkObjId="SW-129724_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3141,-794 3141,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c06e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1955,-588 1955,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="23746@0" ObjectIDZND0="23747@x" ObjectIDZND1="g_2bf54e0@0" Pin0InfoVect0LinkObjId="SW-129600_0" Pin0InfoVect1LinkObjId="g_2bf54e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129599_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1955,-588 1955,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c070f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2081,-589 2081,-570 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="23751@0" ObjectIDZND0="g_2bf6210@0" ObjectIDZND1="23752@x" Pin0InfoVect0LinkObjId="g_2bf6210_0" Pin0InfoVect1LinkObjId="SW-129607_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129606_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2081,-589 2081,-570 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c07be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2081,-616 2081,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="23751@1" ObjectIDZND0="23753@x" ObjectIDZND1="23754@x" Pin0InfoVect0LinkObjId="SW-129608_0" Pin0InfoVect1LinkObjId="SW-129609_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129606_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2081,-616 2081,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c07e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2081,-645 2081,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="23751@x" ObjectIDND1="23754@x" ObjectIDZND0="23753@0" Pin0InfoVect0LinkObjId="SW-129608_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129606_0" Pin1InfoVect1LinkObjId="SW-129609_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2081,-645 2081,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c080a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2154,-645 2136,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2c7d950@0" ObjectIDZND0="23754@1" Pin0InfoVect0LinkObjId="SW-129609_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c7d950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2154,-645 2136,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c08300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2100,-645 2081,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23754@0" ObjectIDZND0="23751@x" ObjectIDZND1="23753@x" Pin0InfoVect0LinkObjId="SW-129606_0" Pin0InfoVect1LinkObjId="SW-129608_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129609_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2100,-645 2081,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c08df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1955,-615 1955,-644 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="23746@1" ObjectIDZND0="23748@x" ObjectIDZND1="23749@x" Pin0InfoVect0LinkObjId="SW-129601_0" Pin0InfoVect1LinkObjId="SW-129602_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129599_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1955,-615 1955,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c09050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1955,-644 1955,-653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="23746@x" ObjectIDND1="23749@x" ObjectIDZND0="23748@0" Pin0InfoVect0LinkObjId="SW-129601_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129599_0" Pin1InfoVect1LinkObjId="SW-129602_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1955,-644 1955,-653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c092b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2028,-644 2011,-644 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2cc1ab0@0" ObjectIDZND0="23749@1" Pin0InfoVect0LinkObjId="SW-129602_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cc1ab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2028,-644 2011,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c09510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1975,-644 1955,-644 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23749@0" ObjectIDZND0="23746@x" ObjectIDZND1="23748@x" Pin0InfoVect0LinkObjId="SW-129599_0" Pin0InfoVect1LinkObjId="SW-129601_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129602_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1975,-644 1955,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c09770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2257,-720 2257,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23835@0" ObjectIDZND0="23758@1" Pin0InfoVect0LinkObjId="SW-129615_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dda850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2257,-720 2257,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c099d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2257,-590 2257,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="23756@0" ObjectIDZND0="g_2bf8c20@0" ObjectIDZND1="23757@x" Pin0InfoVect0LinkObjId="g_2bf8c20_0" Pin0InfoVect1LinkObjId="SW-129614_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129613_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2257,-590 2257,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0a4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2257,-661 2257,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23758@0" ObjectIDZND0="23756@x" ObjectIDZND1="23759@x" Pin0InfoVect0LinkObjId="SW-129613_0" Pin0InfoVect1LinkObjId="SW-129616_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129615_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2257,-661 2257,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0a720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2257,-646 2257,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23758@x" ObjectIDND1="23759@x" ObjectIDZND0="23756@1" Pin0InfoVect0LinkObjId="SW-129613_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129615_0" Pin1InfoVect1LinkObjId="SW-129616_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2257,-646 2257,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0a980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2330,-646 2312,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2c8e6e0@0" ObjectIDZND0="23759@1" Pin0InfoVect0LinkObjId="SW-129616_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c8e6e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2330,-646 2312,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0abe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2276,-646 2257,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23759@0" ObjectIDZND0="23758@x" ObjectIDZND1="23756@x" Pin0InfoVect0LinkObjId="SW-129615_0" Pin0InfoVect1LinkObjId="SW-129613_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129616_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2276,-646 2257,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0ae40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2433,-590 2433,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="23761@0" ObjectIDZND0="g_2bf71a0@0" ObjectIDZND1="23762@x" Pin0InfoVect0LinkObjId="g_2bf71a0_0" Pin0InfoVect1LinkObjId="SW-129621_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2433,-590 2433,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0b930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2433,-655 2433,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23763@0" ObjectIDZND0="23761@x" ObjectIDZND1="23764@x" Pin0InfoVect0LinkObjId="SW-129620_0" Pin0InfoVect1LinkObjId="SW-129623_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129622_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2433,-655 2433,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0bb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2433,-646 2433,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23763@x" ObjectIDND1="23764@x" ObjectIDZND0="23761@1" Pin0InfoVect0LinkObjId="SW-129620_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129622_0" Pin1InfoVect1LinkObjId="SW-129623_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2433,-646 2433,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0bdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2506,-646 2488,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2c6d4f0@0" ObjectIDZND0="23764@1" Pin0InfoVect0LinkObjId="SW-129623_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c6d4f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2506,-646 2488,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0c050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2452,-646 2433,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23764@0" ObjectIDZND0="23763@x" ObjectIDZND1="23761@x" Pin0InfoVect0LinkObjId="SW-129622_0" Pin0InfoVect1LinkObjId="SW-129620_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129623_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2452,-646 2433,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0c2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2667,-589 2667,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="23770@0" ObjectIDZND0="g_2bfa6a0@0" ObjectIDZND1="23771@x" Pin0InfoVect0LinkObjId="g_2bfa6a0_0" Pin0InfoVect1LinkObjId="SW-129641_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2667,-589 2667,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0cda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2667,-654 2667,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23772@0" ObjectIDZND0="23770@x" ObjectIDZND1="23773@x" Pin0InfoVect0LinkObjId="SW-129640_0" Pin0InfoVect1LinkObjId="SW-129643_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129642_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2667,-654 2667,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0d000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2667,-645 2667,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23772@x" ObjectIDND1="23773@x" ObjectIDZND0="23770@1" Pin0InfoVect0LinkObjId="SW-129640_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129642_0" Pin1InfoVect1LinkObjId="SW-129643_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2667,-645 2667,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0d260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2740,-645 2722,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2ca0e70@0" ObjectIDZND0="23773@1" Pin0InfoVect0LinkObjId="SW-129643_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ca0e70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2740,-645 2722,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0d4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2686,-645 2668,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23773@0" ObjectIDZND0="23772@x" ObjectIDZND1="23770@x" Pin0InfoVect0LinkObjId="SW-129642_0" Pin0InfoVect1LinkObjId="SW-129640_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129643_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2686,-645 2668,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0d720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2811,-589 2811,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="23775@0" ObjectIDZND0="g_2bfb630@0" ObjectIDZND1="23776@x" Pin0InfoVect0LinkObjId="g_2bfb630_0" Pin0InfoVect1LinkObjId="SW-129648_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129647_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2811,-589 2811,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0df50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2811,-719 2811,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23830@0" ObjectIDZND0="23777@1" Pin0InfoVect0LinkObjId="SW-129649_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ddaa40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2811,-719 2811,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0ea40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2811,-658 2811,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23777@0" ObjectIDZND0="23775@x" ObjectIDZND1="23778@x" Pin0InfoVect0LinkObjId="SW-129647_0" Pin0InfoVect1LinkObjId="SW-129650_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129649_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2811,-658 2811,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0eca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2811,-645 2811,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23777@x" ObjectIDND1="23778@x" ObjectIDZND0="23775@1" Pin0InfoVect0LinkObjId="SW-129647_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129649_0" Pin1InfoVect1LinkObjId="SW-129650_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2811,-645 2811,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0ef00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2883,-645 2865,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2cb0710@0" ObjectIDZND0="23778@1" Pin0InfoVect0LinkObjId="SW-129650_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cb0710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2883,-645 2865,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0f160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2829,-645 2811,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23778@0" ObjectIDZND0="23777@x" ObjectIDZND1="23775@x" Pin0InfoVect0LinkObjId="SW-129649_0" Pin0InfoVect1LinkObjId="SW-129647_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2829,-645 2811,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0f3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2956,-590 2956,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="23780@0" ObjectIDZND0="g_2bfe690@0" ObjectIDZND1="23781@x" Pin0InfoVect0LinkObjId="g_2bfe690_0" Pin0InfoVect1LinkObjId="SW-129655_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129654_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2956,-590 2956,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0feb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2956,-655 2956,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23782@0" ObjectIDZND0="23780@x" ObjectIDZND1="23783@x" Pin0InfoVect0LinkObjId="SW-129654_0" Pin0InfoVect1LinkObjId="SW-129657_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129656_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2956,-655 2956,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c10110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2956,-646 2956,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23782@x" ObjectIDND1="23783@x" ObjectIDZND0="23780@1" Pin0InfoVect0LinkObjId="SW-129654_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129656_0" Pin1InfoVect1LinkObjId="SW-129657_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2956,-646 2956,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c10370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3029,-646 3011,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2c3c810@0" ObjectIDZND0="23783@1" Pin0InfoVect0LinkObjId="SW-129657_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c3c810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3029,-646 3011,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c105d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2975,-646 2956,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23783@0" ObjectIDZND0="23782@x" ObjectIDZND1="23780@x" Pin0InfoVect0LinkObjId="SW-129656_0" Pin0InfoVect1LinkObjId="SW-129654_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129657_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2975,-646 2956,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c10830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3100,-588 3100,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="23785@0" ObjectIDZND0="g_2c00110@0" ObjectIDZND1="23786@x" Pin0InfoVect0LinkObjId="g_2c00110_0" Pin0InfoVect1LinkObjId="SW-129662_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129661_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3100,-588 3100,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c11320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3100,-653 3100,-644 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23787@0" ObjectIDZND0="23785@x" ObjectIDZND1="23788@x" Pin0InfoVect0LinkObjId="SW-129661_0" Pin0InfoVect1LinkObjId="SW-129664_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129663_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3100,-653 3100,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c11580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3100,-644 3100,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23787@x" ObjectIDND1="23788@x" ObjectIDZND0="23785@1" Pin0InfoVect0LinkObjId="SW-129661_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129663_0" Pin1InfoVect1LinkObjId="SW-129664_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3100,-644 3100,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c117e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3173,-644 3155,-644 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2e2cfb0@0" ObjectIDZND0="23788@1" Pin0InfoVect0LinkObjId="SW-129664_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e2cfb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3173,-644 3155,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c11a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3119,-644 3100,-644 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23788@0" ObjectIDZND0="23787@x" ObjectIDZND1="23785@x" Pin0InfoVect0LinkObjId="SW-129663_0" Pin0InfoVect1LinkObjId="SW-129661_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129664_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3119,-644 3100,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c11ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3244,-578 3244,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_2c01df0@0" ObjectIDND1="23791@x" ObjectIDZND0="23790@0" Pin0InfoVect0LinkObjId="SW-129668_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c01df0_0" Pin1InfoVect1LinkObjId="SW-129669_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3244,-578 3244,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c11f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3244,-720 3244,-693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23830@0" ObjectIDZND0="23792@1" Pin0InfoVect0LinkObjId="SW-129670_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ddaa40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3244,-720 3244,-693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c12fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3244,-619 3244,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="23790@1" ObjectIDZND0="23792@x" ObjectIDZND1="23793@x" Pin0InfoVect0LinkObjId="SW-129670_0" Pin0InfoVect1LinkObjId="SW-129671_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129668_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3244,-619 3244,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c13220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3244,-648 3244,-657 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="23790@x" ObjectIDND1="23793@x" ObjectIDZND0="23792@0" Pin0InfoVect0LinkObjId="SW-129670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129668_0" Pin1InfoVect1LinkObjId="SW-129671_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3244,-648 3244,-657 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c13480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3317,-648 3299,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2e3c850@0" ObjectIDZND0="23793@1" Pin0InfoVect0LinkObjId="SW-129671_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e3c850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3317,-648 3299,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c136e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3263,-648 3244,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23793@0" ObjectIDZND0="23790@x" ObjectIDZND1="23792@x" Pin0InfoVect0LinkObjId="SW-129668_0" Pin0InfoVect1LinkObjId="SW-129670_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129671_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3263,-648 3244,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c13940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3388,-592 3388,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28972@0" ObjectIDZND0="28973@1" Pin0InfoVect0LinkObjId="SW-191690_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191686_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3388,-592 3388,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c14430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3388,-657 3388,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="28974@0" ObjectIDZND0="28972@x" ObjectIDZND1="28975@x" Pin0InfoVect0LinkObjId="SW-191686_0" Pin0InfoVect1LinkObjId="SW-191689_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191688_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3388,-657 3388,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c14690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3388,-648 3388,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="28974@x" ObjectIDND1="28975@x" ObjectIDZND0="28972@1" Pin0InfoVect0LinkObjId="SW-191686_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-191688_0" Pin1InfoVect1LinkObjId="SW-191689_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3388,-648 3388,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c148f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3461,-648 3443,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2c57fa0@0" ObjectIDZND0="28975@1" Pin0InfoVect0LinkObjId="SW-191689_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c57fa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3461,-648 3443,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c14b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3407,-648 3388,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28975@0" ObjectIDZND0="28974@x" ObjectIDZND1="28972@x" Pin0InfoVect0LinkObjId="SW-191688_0" Pin0InfoVect1LinkObjId="SW-191686_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191689_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3407,-648 3388,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c158c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2268,-728 2268,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23805@0" ObjectIDZND0="23835@0" Pin0InfoVect0LinkObjId="g_2dda850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129705_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2268,-728 2268,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c188b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2260,-1108 2260,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23732@0" ObjectIDZND0="23829@0" Pin0InfoVect0LinkObjId="g_2c190e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129557_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2260,-1108 2260,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c190e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2269,-1076 2269,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23801@1" ObjectIDZND0="23829@0" Pin0InfoVect0LinkObjId="g_2c188b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129698_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2269,-1076 2269,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c19910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2553,-1112 2553,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23736@0" ObjectIDZND0="23829@0" Pin0InfoVect0LinkObjId="g_2c188b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129568_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2553,-1112 2553,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c1a140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2536,-1054 2536,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_2cd7030@0" ObjectIDZND0="23829@0" Pin0InfoVect0LinkObjId="g_2c188b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cd7030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2536,-1054 2536,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c1a970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2833,-1112 2833,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23740@0" ObjectIDZND0="23829@0" Pin0InfoVect0LinkObjId="g_2c188b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129579_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2833,-1112 2833,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c1b1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2694,-1076 2694,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23828@1" ObjectIDZND0="23829@0" Pin0InfoVect0LinkObjId="g_2c188b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129720_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2694,-1076 2694,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c1b9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3111,-1107 3111,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23745@0" ObjectIDZND0="23829@0" Pin0InfoVect0LinkObjId="g_2c188b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129591_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3111,-1107 3111,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c1c200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2889,-1074 2889,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23795@1" ObjectIDZND0="23829@0" Pin0InfoVect0LinkObjId="g_2c188b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129676_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2889,-1074 2889,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c1e550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2914,-773 2888,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23798@0" ObjectIDZND0="23796@x" ObjectIDZND1="23799@x" Pin0InfoVect0LinkObjId="SW-129679_0" Pin0InfoVect1LinkObjId="SW-129683_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129682_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2914,-773 2888,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c1eec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2888,-780 2888,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="23796@0" ObjectIDZND0="23798@x" ObjectIDZND1="23799@x" Pin0InfoVect0LinkObjId="SW-129682_0" Pin0InfoVect1LinkObjId="SW-129683_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129679_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2888,-780 2888,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c1f0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2888,-773 2888,-767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23798@x" ObjectIDND1="23796@x" ObjectIDZND0="23799@1" Pin0InfoVect0LinkObjId="SW-129683_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-129682_0" Pin1InfoVect1LinkObjId="SW-129679_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2888,-773 2888,-767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f102d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2201,-362 2201,-375 2257,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_2f0e460@0" ObjectIDZND0="g_2c96240@0" ObjectIDZND1="33913@x" ObjectIDZND2="23760@x" Pin0InfoVect0LinkObjId="g_2c96240_0" Pin0InfoVect1LinkObjId="EC-NH_HS.056Ld_0" Pin0InfoVect2LinkObjId="SW-129617_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f0e460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2201,-362 2201,-375 2257,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f10dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2257,-368 2257,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2c96240@0" ObjectIDND1="33913@x" ObjectIDZND0="g_2f0e460@0" ObjectIDZND1="23760@x" Pin0InfoVect0LinkObjId="g_2f0e460_0" Pin0InfoVect1LinkObjId="SW-129617_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c96240_0" Pin1InfoVect1LinkObjId="EC-NH_HS.056Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2257,-368 2257,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f11020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2257,-375 2257,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_2f0e460@0" ObjectIDND1="g_2c96240@0" ObjectIDND2="33913@x" ObjectIDZND0="23760@0" Pin0InfoVect0LinkObjId="SW-129617_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f0e460_0" Pin1InfoVect1LinkObjId="g_2c96240_0" Pin1InfoVect2LinkObjId="EC-NH_HS.056Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2257,-375 2257,-388 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="23835" cx="2535" cy="-720" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23835" cx="2081" cy="-720" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23835" cx="2433" cy="-720" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23830" cx="2614" cy="-720" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23830" cx="2667" cy="-720" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23830" cx="3100" cy="-720" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23830" cx="2956" cy="-720" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23830" cx="3388" cy="-720" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23830" cx="2889" cy="-720" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23835" cx="1955" cy="-720" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23830" cx="2811" cy="-720" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23830" cx="3244" cy="-720" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23835" cx="2257" cy="-720" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23835" cx="2268" cy="-720" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23829" cx="2260" cy="-1092" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23829" cx="2269" cy="-1092" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23829" cx="2553" cy="-1092" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23829" cx="2536" cy="-1092" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23829" cx="2833" cy="-1092" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23829" cx="2694" cy="-1092" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23829" cx="3111" cy="-1092" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23829" cx="2889" cy="-1092" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-129227" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1646.500000 -1293.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23692" ObjectName="DYN-NH_HS"/>
     <cge:Meas_Ref ObjectId="129227"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dc6570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2486.000000 -892.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b11420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1861.000000 -960.000000) translate(0,15)">10kVII段母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a633b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2234.000000 -1403.000000) translate(0,15)">南</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a633b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2234.000000 -1403.000000) translate(0,33)">黄</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a633b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2234.000000 -1403.000000) translate(0,51)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a633b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2234.000000 -1403.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d03a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1932.000000 -816.000000) translate(0,12)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2db4a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3153.000000 -788.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2db6ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1916.000000 -756.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2db6ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2db6ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2db6ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2db6ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2db6ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2db6ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2db6ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2db6ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2db6ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d4c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ddae20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2490.000000 -776.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2db70d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2621.000000 -777.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d06510" transform="matrix(1.000000 -0.000000 -0.000000 0.936508 2055.000000 -313.761905) translate(0,15)">小木线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d01b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2248.000000 -309.000000) translate(0,15)">比厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d01d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2769.000000 -314.000000) translate(0,15)">西城II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d01f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3053.000000 -314.000000) translate(0,15)">西城IV回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dbbef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -756.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dbcdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3085.000000 -962.000000) translate(0,15)">10kVI段母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dbd1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1767.000000 -707.000000) translate(0,15)">10kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dbd3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3472.000000 -697.000000) translate(0,15)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d0b690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2644.000000 -913.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0ded0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2277.000000 -1135.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d88b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2516.000000 -1388.000000) translate(0,15)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d88b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2516.000000 -1388.000000) translate(0,33)">苴</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d88b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2516.000000 -1388.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2da70b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2569.000000 -1135.000000) translate(0,12)">3721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d14a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2850.430412 -1283.000000) translate(0,12)">37367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d14f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2850.430412 -1258.000000) translate(0,12)">3736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d16180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2850.000000 -1135.000000) translate(0,12)">3731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d17be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2808.000000 -1403.000000) translate(0,15)">黄</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d17be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2808.000000 -1403.000000) translate(0,33)">西</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d17be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2808.000000 -1403.000000) translate(0,51)">太</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d17be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2808.000000 -1403.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d528f0" transform="matrix(0.846154 -0.000000 -0.000000 1.000000 3128.143471 -1283.000000) translate(0,12)">37467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d18160" transform="matrix(1.009901 -0.000000 -0.000000 1.000000 2980.326733 -1262.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d18a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2057.000000 -1078.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d1ce00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2481.000000 -819.000000) translate(0,12)">01227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimHei" font-size="20" graphid="g_2cd6400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1512.000000 -1374.000000) translate(0,16)">黄山变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d1fa80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -1385.000000) translate(0,15)">罗</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d1fa80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -1385.000000) translate(0,33)">黄</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d1fa80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -1385.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d20390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3128.000000 -1135.000000) translate(0,12)">3741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d20610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3128.000000 -1258.000000) translate(0,12)">3746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d22850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2277.000000 -1283.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d22d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2277.000000 -1258.000000) translate(0,12)">3716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d22f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2569.000000 -1258.000000) translate(0,12)">3726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d233a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2700.000000 -1063.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d686d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2569.000000 -1283.000000) translate(0,12)">37267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cf01e0" transform="matrix(1.009901 -0.000000 -0.000000 1.000000 2421.326733 -1262.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cf07c0" transform="matrix(1.009901 -0.000000 -0.000000 1.000000 2709.326733 -1262.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cf0a00" transform="matrix(1.009901 -0.000000 -0.000000 1.000000 2130.326733 -1262.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cf7a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1932.000000 -313.000000) translate(0,15)">小岔线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c514a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2412.000000 -314.000000) translate(0,15)">徐营线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c52140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2628.000000 -312.000000) translate(0,15)">西城Ⅲ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c52790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2911.000000 -314.000000) translate(0,15)">西城Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c62390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3331.000000 -317.000000) translate(0,15)">南华泵站Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c64c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1964.000000 -609.000000) translate(0,12)">058</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c655e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1962.000000 -678.000000) translate(0,12)">0581</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c65a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1978.000000 -641.000000) translate(0,12)">05817</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c65c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1962.000000 -537.000000) translate(0,12)">0582</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c65ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1962.000000 -400.000000) translate(0,12)">0586</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c66240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2090.000000 -610.000000) translate(0,12)">057</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c66550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2088.000000 -679.000000) translate(0,12)">0571</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c66790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2099.000000 -641.000000) translate(0,12)">05717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c669d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2088.000000 -538.000000) translate(0,12)">0572</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c66c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2088.000000 -401.000000) translate(0,12)">0576</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c66f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2266.000000 -611.000000) translate(0,12)">056</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c672a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2264.000000 -683.000000) translate(0,12)">0561</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c674e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2275.000000 -640.000000) translate(0,12)">05617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c67720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2264.000000 -539.000000) translate(0,12)">0562</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c67960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2264.000000 -402.000000) translate(0,12)">0566</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bde8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2442.000000 -611.000000) translate(0,12)">055</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bdebf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2440.000000 -680.000000) translate(0,12)">0551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bdee30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2456.000000 -640.000000) translate(0,12)">05517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bdf070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2440.000000 -539.000000) translate(0,12)">0552</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bdf2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2440.000000 -402.000000) translate(0,12)">0556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bdf630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2676.000000 -610.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bdf940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2674.000000 -679.000000) translate(0,12)">0531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bdfb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2690.000000 -639.000000) translate(0,12)">05317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bdfdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2674.000000 -538.000000) translate(0,12)">0532</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be0000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2674.000000 -401.000000) translate(0,12)">0536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be0380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2821.000000 -610.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be0690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2817.000000 -679.000000) translate(0,12)">0521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be08d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2833.000000 -640.000000) translate(0,12)">05217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be0b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2818.000000 -538.000000) translate(0,12)">0522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be0d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2818.000000 -401.000000) translate(0,12)">0526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be10d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2965.000000 -611.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be13e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2973.000000 -641.000000) translate(0,12)">05117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be1620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2963.000000 -679.000000) translate(0,12)">0511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be1860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2963.000000 -539.000000) translate(0,12)">0512</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be1aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2963.000000 -402.000000) translate(0,12)">0516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be1e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3109.000000 -609.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be2130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3107.000000 -678.000000) translate(0,12)">0541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be2370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -636.000000) translate(0,12)">05417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be25b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3107.000000 -537.000000) translate(0,12)">0542</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be27f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3107.000000 -400.000000) translate(0,12)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be2b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3253.000000 -613.000000) translate(0,12)">059</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be2e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3251.000000 -682.000000) translate(0,12)">0591</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be30c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3264.000000 -642.000000) translate(0,12)">05917</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be3300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3256.000000 -555.000000) translate(0,12)">0596</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be3680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3397.000000 -613.000000) translate(0,12)">050</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be3990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3395.000000 -681.000000) translate(0,12)">0501</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be3bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3409.000000 -642.000000) translate(0,12)">05017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be3e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3395.000000 -541.000000) translate(0,12)">0506</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be4190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2279.000000 -1016.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be44a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2276.000000 -1065.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be46e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2277.000000 -802.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be4920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2275.000000 -847.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be4b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2275.000000 -753.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be4da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2292.000000 -769.000000) translate(0,12)">00217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be4fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2898.000000 -1015.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be5220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2896.000000 -1064.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be5460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2895.000000 -844.000000) translate(0,12)">0012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be56a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2897.000000 -801.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be58e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2931.000000 -792.000000) translate(0,12)">00117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be5b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2895.000000 -756.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be5d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2895.000000 -756.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2be81b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1698.000000 -1363.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2bea750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1698.000000 -1400.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2beae30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2563.000000 -836.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2beb1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2270.000000 -1204.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2beb610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2563.000000 -1204.000000) translate(0,12)">372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2beb920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2843.000000 -1204.000000) translate(0,12)">373</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bebb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3120.000000 -1204.000000) translate(0,12)">374</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bec160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2326.000000 -931.000000) translate(0,12)">温度：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2becda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2327.000000 -947.000000) translate(0,12)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bed760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2952.000000 -946.000000) translate(0,12)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2beda80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2951.000000 -930.000000) translate(0,12)">温度：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2beec60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2117.000000 -929.000000) translate(0,15)">SZ9-5000KVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2beec60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2117.000000 -929.000000) translate(0,33)">Y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bf0a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2743.000000 -911.000000) translate(0,15)">SZ9-5000KVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bf0a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2743.000000 -911.000000) translate(0,33)">Y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c1ca30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1474.000000 -441.000000) translate(0,15)">7221538</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c1d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3204.000000 -233.000000) translate(0,15)">1号电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2c25a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1630.000000 -1380.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c264f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1351.000000 -760.000000) translate(0,12)">1、与修试所顾铨、罗诚核实0901、0902、0011、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c264f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1351.000000 -760.000000) translate(0,27)">05217辅助触点有缺陷，上送位置信号错误，</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c264f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1351.000000 -760.000000) translate(0,42)">需报缺陷待停电后处理。请当值调控员根据</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c264f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1351.000000 -760.000000) translate(0,57)">现场一次设备状态变化进行置位。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c264f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1351.000000 -760.000000) translate(0,72)">2、更换远动后与现场罗诚核实主变调档器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c264f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1351.000000 -760.000000) translate(0,87)">检查无问题，在不停电情况下未查出主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c264f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1351.000000 -760.000000) translate(0,102)">不能调档原因，需报缺陷待停电后处理</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2f02650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1345.000000 -409.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2f02650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1345.000000 -409.000000) translate(0,38)">心变运一班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2f04ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1474.000000 -391.500000) translate(0,16)">13908784302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f093a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2789.000000 -944.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f09ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2174.000000 -958.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_2f0cac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1359.000000 -1022.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f11280" transform="matrix(1.000000 -0.000000 -0.000000 0.936508 2168.000000 -262.761905) translate(0,15)">10kV2号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f11280" transform="matrix(1.000000 -0.000000 -0.000000 0.936508 2168.000000 -262.761905) translate(0,33)">站用变</text>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-129727">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 1899.000000 -727.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23832" ObjectName="SW-NH_HS.NH_HS_0902SW"/>
     <cge:Meas_Ref ObjectId="129727"/>
    <cge:TPSR_Ref TObjectID="23832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129698">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2260.930412 -1035.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23801" ObjectName="SW-NH_HS.NH_HS_3021SW"/>
     <cge:Meas_Ref ObjectId="129698"/>
    <cge:TPSR_Ref TObjectID="23801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129556">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2251.930412 -1229.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23731" ObjectName="SW-NH_HS.NH_HS_3716SW"/>
     <cge:Meas_Ref ObjectId="129556"/>
    <cge:TPSR_Ref TObjectID="23731"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130039">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1941.000000 -767.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23833" ObjectName="SW-NH_HS.NH_HS_09027SW"/>
     <cge:Meas_Ref ObjectId="130039"/>
    <cge:TPSR_Ref TObjectID="23833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129558">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2289.930412 -1260.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23733" ObjectName="SW-NH_HS.NH_HS_37167SW"/>
     <cge:Meas_Ref ObjectId="129558"/>
    <cge:TPSR_Ref TObjectID="23733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130038">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3164.000000 -768.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23834" ObjectName="SW-NH_HS.NH_HS_09017SW"/>
     <cge:Meas_Ref ObjectId="130038"/>
    <cge:TPSR_Ref TObjectID="23834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129704">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2301.930412 -746.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23804" ObjectName="SW-NH_HS.NH_HS_00217SW"/>
     <cge:Meas_Ref ObjectId="129704"/>
    <cge:TPSR_Ref TObjectID="23804"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129628">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2526.000000 -745.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23767" ObjectName="SW-NH_HS.NH_HS_0122SW"/>
     <cge:Meas_Ref ObjectId="129628"/>
    <cge:TPSR_Ref TObjectID="23767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129630">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2605.000000 -747.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23769" ObjectName="SW-NH_HS.NH_HS_0121SW"/>
     <cge:Meas_Ref ObjectId="129630"/>
    <cge:TPSR_Ref TObjectID="23769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129703">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2258.930412 -817.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23803" ObjectName="SW-NH_HS.NH_HS_0022SW"/>
     <cge:Meas_Ref ObjectId="129703"/>
    <cge:TPSR_Ref TObjectID="23803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129705">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2258.930412 -723.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23805" ObjectName="SW-NH_HS.NH_HS_0021SW"/>
     <cge:Meas_Ref ObjectId="129705"/>
    <cge:TPSR_Ref TObjectID="23805"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129676">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2880.658935 -1033.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23795" ObjectName="SW-NH_HS.NH_HS_3011SW"/>
     <cge:Meas_Ref ObjectId="129676"/>
    <cge:TPSR_Ref TObjectID="23795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129682">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2922.658935 -747.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23798" ObjectName="SW-NH_HS.NH_HS_00117SW"/>
     <cge:Meas_Ref ObjectId="129682"/>
    <cge:TPSR_Ref TObjectID="23798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129681">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2878.658935 -813.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23797" ObjectName="SW-NH_HS.NH_HS_0012SW"/>
     <cge:Meas_Ref ObjectId="129681"/>
    <cge:TPSR_Ref TObjectID="23797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129683">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2879.000000 -726.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23799" ObjectName="SW-NH_HS.NH_HS_0011SW"/>
     <cge:Meas_Ref ObjectId="129683"/>
    <cge:TPSR_Ref TObjectID="23799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129724">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3133.000000 -728.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23809" ObjectName="SW-NH_HS.NH_HS_0901SW"/>
     <cge:Meas_Ref ObjectId="129724"/>
    <cge:TPSR_Ref TObjectID="23809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129720">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2684.966495 -1035.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23828" ObjectName="SW-NH_HS.NH_HS_3901SW"/>
     <cge:Meas_Ref ObjectId="129720"/>
    <cge:TPSR_Ref TObjectID="23828"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129557">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2250.930412 -1103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23732" ObjectName="SW-NH_HS.NH_HS_3711SW"/>
     <cge:Meas_Ref ObjectId="129557"/>
    <cge:TPSR_Ref TObjectID="23732"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129569">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2582.895189 -1264.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23737" ObjectName="SW-NH_HS.NH_HS_37267SW"/>
     <cge:Meas_Ref ObjectId="129569"/>
    <cge:TPSR_Ref TObjectID="23737"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129568">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2543.895189 -1107.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23736" ObjectName="SW-NH_HS.NH_HS_3721SW"/>
     <cge:Meas_Ref ObjectId="129568"/>
    <cge:TPSR_Ref TObjectID="23736"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129567">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2544.895189 -1233.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23735" ObjectName="SW-NH_HS.NH_HS_3726SW"/>
     <cge:Meas_Ref ObjectId="129567"/>
    <cge:TPSR_Ref TObjectID="23735"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129580">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2863.430412 -1264.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23741" ObjectName="SW-NH_HS.NH_HS_37367SW"/>
     <cge:Meas_Ref ObjectId="129580"/>
    <cge:TPSR_Ref TObjectID="23741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129579">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2824.430412 -1107.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23740" ObjectName="SW-NH_HS.NH_HS_3731SW"/>
     <cge:Meas_Ref ObjectId="129579"/>
    <cge:TPSR_Ref TObjectID="23740"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129578">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 2825.430412 -1233.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23739" ObjectName="SW-NH_HS.NH_HS_3736SW"/>
     <cge:Meas_Ref ObjectId="129578"/>
    <cge:TPSR_Ref TObjectID="23739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129590">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.846154 -0.000000 0.000000 -1.000000 3137.297317 -1260.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23744" ObjectName="SW-NH_HS.NH_HS_37467SW"/>
     <cge:Meas_Ref ObjectId="129590"/>
    <cge:TPSR_Ref TObjectID="23744"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129589">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.642857 -0.000000 0.000000 -0.717391 3106.143471 -1238.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23743" ObjectName="SW-NH_HS.NH_HS_3746SW"/>
     <cge:Meas_Ref ObjectId="129589"/>
    <cge:TPSR_Ref TObjectID="23743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129629">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.846154 -0.000000 -0.000000 1.000000 2508.846154 -827.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23768" ObjectName="SW-NH_HS.NH_HS_01227SW"/>
     <cge:Meas_Ref ObjectId="129629"/>
    <cge:TPSR_Ref TObjectID="23768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3235.000000 -283.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129601">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1946.000000 -648.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23748" ObjectName="SW-NH_HS.NH_HS_0581SW"/>
     <cge:Meas_Ref ObjectId="129601"/>
    <cge:TPSR_Ref TObjectID="23748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129600">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1946.000000 -507.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23747" ObjectName="SW-NH_HS.NH_HS_0582SW"/>
     <cge:Meas_Ref ObjectId="129600"/>
    <cge:TPSR_Ref TObjectID="23747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129603">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.936508 1946.000000 -370.761905)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23750" ObjectName="SW-NH_HS.NH_HS_0586SW"/>
     <cge:Meas_Ref ObjectId="129603"/>
    <cge:TPSR_Ref TObjectID="23750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129602">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1983.913043 -618.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23749" ObjectName="SW-NH_HS.NH_HS_05817SW"/>
     <cge:Meas_Ref ObjectId="129602"/>
    <cge:TPSR_Ref TObjectID="23749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129608">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2072.000000 -649.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23753" ObjectName="SW-NH_HS.NH_HS_0571SW"/>
     <cge:Meas_Ref ObjectId="129608"/>
    <cge:TPSR_Ref TObjectID="23753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129607">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2072.000000 -508.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23752" ObjectName="SW-NH_HS.NH_HS_0572SW"/>
     <cge:Meas_Ref ObjectId="129607"/>
    <cge:TPSR_Ref TObjectID="23752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129610">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.936508 2072.000000 -371.761905)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23755" ObjectName="SW-NH_HS.NH_HS_0576SW"/>
     <cge:Meas_Ref ObjectId="129610"/>
    <cge:TPSR_Ref TObjectID="23755"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129609">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2108.913043 -619.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23754" ObjectName="SW-NH_HS.NH_HS_05717SW"/>
     <cge:Meas_Ref ObjectId="129609"/>
    <cge:TPSR_Ref TObjectID="23754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129615">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2248.000000 -656.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23758" ObjectName="SW-NH_HS.NH_HS_0561SW"/>
     <cge:Meas_Ref ObjectId="129615"/>
    <cge:TPSR_Ref TObjectID="23758"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129614">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2248.000000 -509.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23757" ObjectName="SW-NH_HS.NH_HS_0562SW"/>
     <cge:Meas_Ref ObjectId="129614"/>
    <cge:TPSR_Ref TObjectID="23757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129617">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.936508 2248.000000 -383.761905)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23760" ObjectName="SW-NH_HS.NH_HS_0566SW"/>
     <cge:Meas_Ref ObjectId="129617"/>
    <cge:TPSR_Ref TObjectID="23760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129616">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2284.913043 -620.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23759" ObjectName="SW-NH_HS.NH_HS_05617SW"/>
     <cge:Meas_Ref ObjectId="129616"/>
    <cge:TPSR_Ref TObjectID="23759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129622">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2424.000000 -650.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23763" ObjectName="SW-NH_HS.NH_HS_0551SW"/>
     <cge:Meas_Ref ObjectId="129622"/>
    <cge:TPSR_Ref TObjectID="23763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129621">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2424.000000 -509.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23762" ObjectName="SW-NH_HS.NH_HS_0552SW"/>
     <cge:Meas_Ref ObjectId="129621"/>
    <cge:TPSR_Ref TObjectID="23762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129624">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.936508 2424.000000 -372.761905)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23765" ObjectName="SW-NH_HS.NH_HS_0556SW"/>
     <cge:Meas_Ref ObjectId="129624"/>
    <cge:TPSR_Ref TObjectID="23765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129623">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2460.913043 -620.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23764" ObjectName="SW-NH_HS.NH_HS_05517SW"/>
     <cge:Meas_Ref ObjectId="129623"/>
    <cge:TPSR_Ref TObjectID="23764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129642">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2658.000000 -649.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23772" ObjectName="SW-NH_HS.NH_HS_0531SW"/>
     <cge:Meas_Ref ObjectId="129642"/>
    <cge:TPSR_Ref TObjectID="23772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129641">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2658.000000 -508.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23771" ObjectName="SW-NH_HS.NH_HS_0532SW"/>
     <cge:Meas_Ref ObjectId="129641"/>
    <cge:TPSR_Ref TObjectID="23771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129644">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.936508 2658.000000 -371.761905)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23774" ObjectName="SW-NH_HS.NH_HS_0536SW"/>
     <cge:Meas_Ref ObjectId="129644"/>
    <cge:TPSR_Ref TObjectID="23774"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129643">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2694.913043 -619.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23773" ObjectName="SW-NH_HS.NH_HS_05317SW"/>
     <cge:Meas_Ref ObjectId="129643"/>
    <cge:TPSR_Ref TObjectID="23773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129649">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2802.250000 -653.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23777" ObjectName="SW-NH_HS.NH_HS_0521SW"/>
     <cge:Meas_Ref ObjectId="129649"/>
    <cge:TPSR_Ref TObjectID="23777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129648">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2802.250000 -508.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23776" ObjectName="SW-NH_HS.NH_HS_0522SW"/>
     <cge:Meas_Ref ObjectId="129648"/>
    <cge:TPSR_Ref TObjectID="23776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129651">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.936508 2802.250000 -371.761905)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23779" ObjectName="SW-NH_HS.NH_HS_0526SW"/>
     <cge:Meas_Ref ObjectId="129651"/>
    <cge:TPSR_Ref TObjectID="23779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129650">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2838.163043 -619.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23778" ObjectName="SW-NH_HS.NH_HS_05217SW"/>
     <cge:Meas_Ref ObjectId="129650"/>
    <cge:TPSR_Ref TObjectID="23778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129656">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2947.500000 -649.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23782" ObjectName="SW-NH_HS.NH_HS_0511SW"/>
     <cge:Meas_Ref ObjectId="129656"/>
    <cge:TPSR_Ref TObjectID="23782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129655">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.923077 -0.000000 0.000000 -1.000000 2947.538462 -509.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23781" ObjectName="SW-NH_HS.NH_HS_0512SW"/>
     <cge:Meas_Ref ObjectId="129655"/>
    <cge:TPSR_Ref TObjectID="23781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129658">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.936508 2947.500000 -372.761905)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23784" ObjectName="SW-NH_HS.NH_HS_0516SW"/>
     <cge:Meas_Ref ObjectId="129658"/>
    <cge:TPSR_Ref TObjectID="23784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129657">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2984.413043 -620.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23783" ObjectName="SW-NH_HS.NH_HS_05117SW"/>
     <cge:Meas_Ref ObjectId="129657"/>
    <cge:TPSR_Ref TObjectID="23783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129663">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3090.750000 -648.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23787" ObjectName="SW-NH_HS.NH_HS_0541SW"/>
     <cge:Meas_Ref ObjectId="129663"/>
    <cge:TPSR_Ref TObjectID="23787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129662">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3090.750000 -507.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23786" ObjectName="SW-NH_HS.NH_HS_0542SW"/>
     <cge:Meas_Ref ObjectId="129662"/>
    <cge:TPSR_Ref TObjectID="23786"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129665">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.936508 3090.750000 -370.761905)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23789" ObjectName="SW-NH_HS.NH_HS_0546SW"/>
     <cge:Meas_Ref ObjectId="129665"/>
    <cge:TPSR_Ref TObjectID="23789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129664">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3127.663043 -618.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23788" ObjectName="SW-NH_HS.NH_HS_05417SW"/>
     <cge:Meas_Ref ObjectId="129664"/>
    <cge:TPSR_Ref TObjectID="23788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129670">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3235.000000 -652.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23792" ObjectName="SW-NH_HS.NH_HS_0591SW"/>
     <cge:Meas_Ref ObjectId="129670"/>
    <cge:TPSR_Ref TObjectID="23792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129669">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3235.000000 -526.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23791" ObjectName="SW-NH_HS.NH_HS_0596SW"/>
     <cge:Meas_Ref ObjectId="129669"/>
    <cge:TPSR_Ref TObjectID="23791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129671">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3271.913043 -622.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23793" ObjectName="SW-NH_HS.NH_HS_05917SW"/>
     <cge:Meas_Ref ObjectId="129671"/>
    <cge:TPSR_Ref TObjectID="23793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191688">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3379.500000 -651.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28974" ObjectName="SW-NH_HS.NH_HS_0501SW"/>
     <cge:Meas_Ref ObjectId="191688"/>
    <cge:TPSR_Ref TObjectID="28974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191690">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.923077 -0.000000 0.000000 -1.000000 3379.538462 -511.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28973" ObjectName="SW-NH_HS.NH_HS_0506SW"/>
     <cge:Meas_Ref ObjectId="191690"/>
    <cge:TPSR_Ref TObjectID="28973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191689">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3416.413043 -622.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28975" ObjectName="SW-NH_HS.NH_HS_05017SW"/>
     <cge:Meas_Ref ObjectId="191689"/>
    <cge:TPSR_Ref TObjectID="28975"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129591">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3102.143471 -1102.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23745" ObjectName="SW-NH_HS.NH_HS_3741SW"/>
     <cge:Meas_Ref ObjectId="129591"/>
    <cge:TPSR_Ref TObjectID="23745"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-NH_HS.NH_HS_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3218.000000 -337.000000)" xlink:href="#capacitor:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39833" ObjectName="CB-NH_HS.NH_HS_Cb1"/>
    <cge:TPSR_Ref TObjectID="39833"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2a1adf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1850.666667 -897.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a50580">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2724.966495 -919.000000)" xlink:href="#lightningRod:shape74"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d02390">
    <use class="BV-10KV" transform="matrix(0.636364 -0.000000 0.000000 -0.492063 2264.930412 -865.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ccffa0">
    <use class="BV-10KV" transform="matrix(0.636364 -0.000000 0.000000 -0.492063 2884.658935 -860.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d69fa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1898.000000 -832.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d6cc90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.047619 3084.666667 -905.095238)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dbc4b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.047619 3132.000000 -836.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cd7030">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 2529.962199 -1004.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cd7b70">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 2361.930412 -1333.333333)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d72be0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 2655.895189 -1336.333333)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d74a60">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 2935.430412 -1335.333333)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d769e0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 3213.143471 -1334.333333)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d1eac0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3041.143471 -1234.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cb9e00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2766.430412 -1236.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cbaeb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2483.895189 -1236.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cbc1a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2190.930412 -1233.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cc9570">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2005.377778 -312.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cca2e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1950.000000 -428.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c854b0">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2131.377778 -313.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c86220">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2076.000000 -429.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c96240">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2307.377778 -314.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c96fb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2252.000000 -430.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c75050">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2483.377778 -314.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c75dc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2428.000000 -430.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ca89d0">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2717.377778 -313.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ca9740">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2662.000000 -429.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c34240">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 2861.627778 -313.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c34fb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2806.250000 -429.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c44370">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 3006.877778 -314.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c450e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2951.500000 -430.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e34b10">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 3151.127778 -312.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e35880">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3094.750000 -428.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c4dd70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3239.000000 -459.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c5d310">
    <use class="BV-10KV" transform="matrix(-0.933333 -0.000000 0.000000 -1.000000 3438.877778 -316.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c5e080">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3383.500000 -432.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bf54e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1916.000000 -494.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bf6210">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2041.000000 -498.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bf71a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2394.000000 -503.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bf8c20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2217.000000 -501.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bfa6a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2627.000000 -497.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bfb630">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2772.000000 -499.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bfe690">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2916.000000 -497.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c00110">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3061.000000 -495.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c01df0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3205.000000 -506.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f0e460">
    <use class="BV-10KV" transform="matrix(0.760870 -0.000000 0.000000 -0.742647 2177.000000 -265.000000)" xlink:href="#lightningRod:shape178"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-129423" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2260.930412 -1450.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129423" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23730"/>
     <cge:Term_Ref ObjectID="13141"/>
    <cge:TPSR_Ref TObjectID="23730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-129424" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2260.930412 -1450.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129424" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23730"/>
     <cge:Term_Ref ObjectID="13141"/>
    <cge:TPSR_Ref TObjectID="23730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-129421" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2260.930412 -1450.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129421" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23730"/>
     <cge:Term_Ref ObjectID="13141"/>
    <cge:TPSR_Ref TObjectID="23730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-129428" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2557.895189 -1446.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129428" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23734"/>
     <cge:Term_Ref ObjectID="21079"/>
    <cge:TPSR_Ref TObjectID="23734"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-129429" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2557.895189 -1446.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129429" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23734"/>
     <cge:Term_Ref ObjectID="21079"/>
    <cge:TPSR_Ref TObjectID="23734"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-129426" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2557.895189 -1446.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129426" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23734"/>
     <cge:Term_Ref ObjectID="21079"/>
    <cge:TPSR_Ref TObjectID="23734"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-129433" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2826.430412 -1450.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129433" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23738"/>
     <cge:Term_Ref ObjectID="33448"/>
    <cge:TPSR_Ref TObjectID="23738"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-129434" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2826.430412 -1450.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129434" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23738"/>
     <cge:Term_Ref ObjectID="33448"/>
    <cge:TPSR_Ref TObjectID="23738"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-129431" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2826.430412 -1450.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129431" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23738"/>
     <cge:Term_Ref ObjectID="33448"/>
    <cge:TPSR_Ref TObjectID="23738"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-129438" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3111.143471 -1433.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129438" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23742"/>
     <cge:Term_Ref ObjectID="33456"/>
    <cge:TPSR_Ref TObjectID="23742"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-129439" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3111.143471 -1433.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129439" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23742"/>
     <cge:Term_Ref ObjectID="33456"/>
    <cge:TPSR_Ref TObjectID="23742"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-129436" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3111.143471 -1433.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129436" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23742"/>
     <cge:Term_Ref ObjectID="33456"/>
    <cge:TPSR_Ref TObjectID="23742"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-129506" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2393.930412 -1029.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129506" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23800"/>
     <cge:Term_Ref ObjectID="33572"/>
    <cge:TPSR_Ref TObjectID="23800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-129507" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2393.930412 -1029.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129507" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23800"/>
     <cge:Term_Ref ObjectID="33572"/>
    <cge:TPSR_Ref TObjectID="23800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-129503" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2393.930412 -1029.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129503" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23800"/>
     <cge:Term_Ref ObjectID="33572"/>
    <cge:TPSR_Ref TObjectID="23800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-129494" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3018.658935 -1029.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129494" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23794"/>
     <cge:Term_Ref ObjectID="33560"/>
    <cge:TPSR_Ref TObjectID="23794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-129495" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3018.658935 -1029.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129495" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23794"/>
     <cge:Term_Ref ObjectID="33560"/>
    <cge:TPSR_Ref TObjectID="23794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-129491" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3018.658935 -1029.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129491" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23794"/>
     <cge:Term_Ref ObjectID="33560"/>
    <cge:TPSR_Ref TObjectID="23794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-129512" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2203.930412 -822.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129512" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23802"/>
     <cge:Term_Ref ObjectID="33576"/>
    <cge:TPSR_Ref TObjectID="23802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-129513" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2203.930412 -822.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129513" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23802"/>
     <cge:Term_Ref ObjectID="33576"/>
    <cge:TPSR_Ref TObjectID="23802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-129509" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2203.930412 -822.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129509" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23802"/>
     <cge:Term_Ref ObjectID="33576"/>
    <cge:TPSR_Ref TObjectID="23802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-129500" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2824.658935 -815.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129500" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23796"/>
     <cge:Term_Ref ObjectID="33564"/>
    <cge:TPSR_Ref TObjectID="23796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-129501" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2824.658935 -815.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129501" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23796"/>
     <cge:Term_Ref ObjectID="33564"/>
    <cge:TPSR_Ref TObjectID="23796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-129497" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2824.658935 -815.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129497" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23796"/>
     <cge:Term_Ref ObjectID="33564"/>
    <cge:TPSR_Ref TObjectID="23796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-129463" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2564.000000 -800.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129463" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23766"/>
     <cge:Term_Ref ObjectID="33504"/>
    <cge:TPSR_Ref TObjectID="23766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-129464" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2564.000000 -800.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129464" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23766"/>
     <cge:Term_Ref ObjectID="33504"/>
    <cge:TPSR_Ref TObjectID="23766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-129461" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2564.000000 -800.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129461" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23766"/>
     <cge:Term_Ref ObjectID="33504"/>
    <cge:TPSR_Ref TObjectID="23766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-129448" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2085.000000 -288.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129448" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23751"/>
     <cge:Term_Ref ObjectID="33474"/>
    <cge:TPSR_Ref TObjectID="23751"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-129449" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2085.000000 -288.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129449" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23751"/>
     <cge:Term_Ref ObjectID="33474"/>
    <cge:TPSR_Ref TObjectID="23751"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-129446" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2085.000000 -288.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129446" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23751"/>
     <cge:Term_Ref ObjectID="33474"/>
    <cge:TPSR_Ref TObjectID="23751"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-129453" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2314.000000 -284.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129453" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23756"/>
     <cge:Term_Ref ObjectID="33484"/>
    <cge:TPSR_Ref TObjectID="23756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-129454" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2314.000000 -284.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129454" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23756"/>
     <cge:Term_Ref ObjectID="33484"/>
    <cge:TPSR_Ref TObjectID="23756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-129451" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2314.000000 -284.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129451" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23756"/>
     <cge:Term_Ref ObjectID="33484"/>
    <cge:TPSR_Ref TObjectID="23756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-129458" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2461.000000 -286.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129458" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23761"/>
     <cge:Term_Ref ObjectID="33494"/>
    <cge:TPSR_Ref TObjectID="23761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-129459" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2461.000000 -286.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129459" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23761"/>
     <cge:Term_Ref ObjectID="33494"/>
    <cge:TPSR_Ref TObjectID="23761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-129456" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2461.000000 -286.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129456" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23761"/>
     <cge:Term_Ref ObjectID="33494"/>
    <cge:TPSR_Ref TObjectID="23761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-129468" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2675.000000 -286.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129468" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23770"/>
     <cge:Term_Ref ObjectID="33512"/>
    <cge:TPSR_Ref TObjectID="23770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-129469" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2675.000000 -286.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129469" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23770"/>
     <cge:Term_Ref ObjectID="33512"/>
    <cge:TPSR_Ref TObjectID="23770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-129466" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2675.000000 -286.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129466" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23770"/>
     <cge:Term_Ref ObjectID="33512"/>
    <cge:TPSR_Ref TObjectID="23770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-129473" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2815.000000 -286.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129473" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23775"/>
     <cge:Term_Ref ObjectID="33522"/>
    <cge:TPSR_Ref TObjectID="23775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-129474" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2815.000000 -286.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129474" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23775"/>
     <cge:Term_Ref ObjectID="33522"/>
    <cge:TPSR_Ref TObjectID="23775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-129471" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2815.000000 -286.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129471" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23775"/>
     <cge:Term_Ref ObjectID="33522"/>
    <cge:TPSR_Ref TObjectID="23775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-129483" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3107.000000 -290.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129483" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23785"/>
     <cge:Term_Ref ObjectID="33542"/>
    <cge:TPSR_Ref TObjectID="23785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-129484" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3107.000000 -290.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129484" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23785"/>
     <cge:Term_Ref ObjectID="33542"/>
    <cge:TPSR_Ref TObjectID="23785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-129481" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3107.000000 -290.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129481" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23785"/>
     <cge:Term_Ref ObjectID="33542"/>
    <cge:TPSR_Ref TObjectID="23785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-129488" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3251.000000 -283.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129488" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23790"/>
     <cge:Term_Ref ObjectID="33552"/>
    <cge:TPSR_Ref TObjectID="23790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-129489" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3251.000000 -283.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129489" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23790"/>
     <cge:Term_Ref ObjectID="33552"/>
    <cge:TPSR_Ref TObjectID="23790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-129486" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3251.000000 -283.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23790"/>
     <cge:Term_Ref ObjectID="33552"/>
    <cge:TPSR_Ref TObjectID="23790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-129515" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2139.000000 -1175.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129515" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23829"/>
     <cge:Term_Ref ObjectID="33610"/>
    <cge:TPSR_Ref TObjectID="23829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-129516" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2139.000000 -1175.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129516" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23829"/>
     <cge:Term_Ref ObjectID="33610"/>
    <cge:TPSR_Ref TObjectID="23829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-129517" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2139.000000 -1175.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129517" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23829"/>
     <cge:Term_Ref ObjectID="33610"/>
    <cge:TPSR_Ref TObjectID="23829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-130015" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2139.000000 -1175.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130015" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23829"/>
     <cge:Term_Ref ObjectID="33610"/>
    <cge:TPSR_Ref TObjectID="23829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-129518" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2139.000000 -1175.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129518" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23829"/>
     <cge:Term_Ref ObjectID="33610"/>
    <cge:TPSR_Ref TObjectID="23829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-129520" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3530.000000 -826.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129520" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23830"/>
     <cge:Term_Ref ObjectID="33609"/>
    <cge:TPSR_Ref TObjectID="23830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-129521" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3530.000000 -826.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129521" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23830"/>
     <cge:Term_Ref ObjectID="33609"/>
    <cge:TPSR_Ref TObjectID="23830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-129522" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3530.000000 -826.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129522" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23830"/>
     <cge:Term_Ref ObjectID="33609"/>
    <cge:TPSR_Ref TObjectID="23830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-130016" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3530.000000 -826.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130016" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23830"/>
     <cge:Term_Ref ObjectID="33609"/>
    <cge:TPSR_Ref TObjectID="23830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-129523" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3530.000000 -826.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129523" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23830"/>
     <cge:Term_Ref ObjectID="33609"/>
    <cge:TPSR_Ref TObjectID="23830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-129524" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3530.000000 -826.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129524" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23830"/>
     <cge:Term_Ref ObjectID="33609"/>
    <cge:TPSR_Ref TObjectID="23830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-129541" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2368.930412 -946.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129541" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23812"/>
     <cge:Term_Ref ObjectID="33601"/>
    <cge:TPSR_Ref TObjectID="23812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-129543" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2368.930412 -946.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129543" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23812"/>
     <cge:Term_Ref ObjectID="33601"/>
    <cge:TPSR_Ref TObjectID="23812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-129540" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2991.658935 -945.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129540" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23811"/>
     <cge:Term_Ref ObjectID="33597"/>
    <cge:TPSR_Ref TObjectID="23811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-129542" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2991.658935 -945.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129542" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23811"/>
     <cge:Term_Ref ObjectID="33597"/>
    <cge:TPSR_Ref TObjectID="23811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-129443" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1941.000000 -286.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129443" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23746"/>
     <cge:Term_Ref ObjectID="33464"/>
    <cge:TPSR_Ref TObjectID="23746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-129444" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1941.000000 -286.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129444" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23746"/>
     <cge:Term_Ref ObjectID="33464"/>
    <cge:TPSR_Ref TObjectID="23746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-129441" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1941.000000 -286.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129441" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23746"/>
     <cge:Term_Ref ObjectID="33464"/>
    <cge:TPSR_Ref TObjectID="23746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-191701" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3395.000000 -283.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="191701" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28972"/>
     <cge:Term_Ref ObjectID="41301"/>
    <cge:TPSR_Ref TObjectID="28972"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-129479" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3395.000000 -283.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129479" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28972"/>
     <cge:Term_Ref ObjectID="41301"/>
    <cge:TPSR_Ref TObjectID="28972"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-191699" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3395.000000 -283.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="191699" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28972"/>
     <cge:Term_Ref ObjectID="41301"/>
    <cge:TPSR_Ref TObjectID="28972"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-129478" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2968.000000 -286.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129478" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23780"/>
     <cge:Term_Ref ObjectID="33532"/>
    <cge:TPSR_Ref TObjectID="23780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-239163" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2968.000000 -286.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="239163" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23780"/>
     <cge:Term_Ref ObjectID="33532"/>
    <cge:TPSR_Ref TObjectID="23780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-129476" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2968.000000 -286.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="129476" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23780"/>
     <cge:Term_Ref ObjectID="33532"/>
    <cge:TPSR_Ref TObjectID="23780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-296441" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1816.000000 -817.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="296441" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23835"/>
     <cge:Term_Ref ObjectID="33608"/>
    <cge:TPSR_Ref TObjectID="23835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-296442" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1816.000000 -817.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="296442" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23835"/>
     <cge:Term_Ref ObjectID="33608"/>
    <cge:TPSR_Ref TObjectID="23835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-296443" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1816.000000 -817.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="296443" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23835"/>
     <cge:Term_Ref ObjectID="33608"/>
    <cge:TPSR_Ref TObjectID="23835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-296448" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1816.000000 -817.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="296448" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23835"/>
     <cge:Term_Ref ObjectID="33608"/>
    <cge:TPSR_Ref TObjectID="23835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-296444" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1816.000000 -817.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="296444" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23835"/>
     <cge:Term_Ref ObjectID="33608"/>
    <cge:TPSR_Ref TObjectID="23835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-296445" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1816.000000 -817.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="296445" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23835"/>
     <cge:Term_Ref ObjectID="33608"/>
    <cge:TPSR_Ref TObjectID="23835"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="nh_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="1467" y="-1385"/></g>
   <g href="nh_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="1416" y="-1405"/></g>
   <g href="nh_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="1414" y="-1408"/></g>
   <g href="35kV黄山变10kV小岔线058间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1964" y="-609"/></g>
   <g href="35kV黄山变10kV小木线057间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2090" y="-610"/></g>
   <g href="35kV黄山变10kV比厂线056间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2266" y="-611"/></g>
   <g href="35kV黄山变10kV徐营线055间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2442" y="-611"/></g>
   <g href="35kV黄山变10kV西城Ⅲ回线053间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2676" y="-610"/></g>
   <g href="35kV黄山变10kV西城Ⅱ回线052间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2821" y="-610"/></g>
   <g href="35kV黄山变10kV西城Ⅰ回线051间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2965" y="-611"/></g>
   <g href="35kV黄山变10kV西城Ⅳ回线054间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3109" y="-609"/></g>
   <g href="35kV黄山变10kV电容器059间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3253" y="-613"/></g>
   <g href="35kV黄山变10kV南华泵站Ⅰ回线050间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3397" y="-613"/></g>
   <g href="cx_配调_配网接线图35_南华.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1687" y="-1370"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1687" y="-1409"/></g>
   <g href="35kV黄山变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="2563" y="-836"/></g>
   <g href="35kV黄山变35kV南黄大线371间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2270" y="-1204"/></g>
   <g href="35kV黄山变35kV大苴线372间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2563" y="-1204"/></g>
   <g href="35kV黄山变35kV黄西太线373间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2843" y="-1204"/></g>
   <g href="35kV黄山变35kV罗黄线374间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3120" y="-1204"/></g>
   <g href="AVC黄山站.svg" style="fill-opacity:0"><rect height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="1626" y="-1394"/></g>
   <g href="35kV黄山变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="2789" y="-944"/></g>
   <g href="35kV黄山变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="2174" y="-958"/></g>
   <g href="35kV黄山变GG虚设备间隔间隔接线图_1.svg" style="fill-opacity:0"><rect height="23" qtmmishow="hidden" width="81" x="1354" y="-1025"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cb4a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2073.000000 1167.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cb5f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2073.000000 1153.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cb64a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2073.000000 1139.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cb6a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2079.000000 1124.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cb6c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2065.000000 1109.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cb6fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2210.000000 1450.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cb7b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2199.000000 1435.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cb83b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2224.000000 1420.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cb8d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1885.000000 287.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cb8fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1874.000000 272.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cb9200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1899.000000 257.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cb9530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2339.000000 1029.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cb9790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2328.000000 1014.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cb99d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2353.000000 999.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cf0d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2146.000000 824.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cf0f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2135.000000 809.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cf11d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2160.000000 794.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2beddb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2962.000000 1030.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bee010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2951.000000 1015.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bee250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2976.000000 1000.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bee580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2765.000000 817.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bee7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2754.000000 802.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2beea20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2779.000000 787.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1fc10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2503.000000 1450.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c1fe70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2492.000000 1435.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c200b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2517.000000 1420.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c203e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2773.000000 1451.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c20640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2762.000000 1436.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c20880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2787.000000 1421.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c20bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3057.000000 1434.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c20e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3046.000000 1419.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c21050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3071.000000 1404.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c21380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2031.000000 289.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c215e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2020.000000 274.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c21820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2045.000000 259.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c21b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2258.000000 285.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c21db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2247.000000 270.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c21ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2272.000000 255.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c22320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2407.000000 286.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c22580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2396.000000 271.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c227c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2421.000000 256.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c22af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2622.000000 287.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c22d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2611.000000 272.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c22f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2636.000000 257.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c232c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2762.000000 287.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c23520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2751.000000 272.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c23760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2776.000000 257.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c23a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2916.000000 287.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c23cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2905.000000 272.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c23f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2930.000000 257.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c24260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3054.000000 290.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c244c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3043.000000 275.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c24700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3068.000000 260.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c24a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3197.000000 285.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c24c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3186.000000 270.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c24ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3211.000000 255.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c25200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3341.000000 284.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c25460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3330.000000 269.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c256a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3355.000000 254.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0a450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3448.000000 814.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0b100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3448.000000 828.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0b310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3447.000000 784.000000) translate(0,12)">3U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0b550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3448.000000 799.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0b790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3440.000000 769.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0b9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3440.000000 753.000000) translate(0,12)">Ubc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0bd00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1731.000000 802.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0bf80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1731.000000 816.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0c1c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1730.000000 772.000000) translate(0,12)">3U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0c400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1731.000000 787.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0c640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1723.000000 757.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f0c880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1723.000000 741.000000) translate(0,12)">Ubc（kV）：</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-NH_HS.NH_HS_Zyb1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="33604"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 2549.962199 -993.000000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 2549.962199 -993.000000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="23813" ObjectName="TF-NH_HS.NH_HS_Zyb1"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-NH_HS.NH_HS_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="33596"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.918367 2854.000000 -896.000000)" xlink:href="#transformer2:shape39_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.918367 2854.000000 -896.000000)" xlink:href="#transformer2:shape39_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="23811" ObjectName="TF-NH_HS.NH_HS_1T"/>
    <cge:TPSR_Ref TObjectID="23811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-NH_HS.NH_HS_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="33600"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.918367 2234.000000 -898.000000)" xlink:href="#transformer2:shape39_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.918367 2234.000000 -898.000000)" xlink:href="#transformer2:shape39_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="23812" ObjectName="TF-NH_HS.NH_HS_2T"/>
    <cge:TPSR_Ref TObjectID="23812"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="1467" y="-1385"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="1467" y="-1385"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="1416" y="-1405"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="1416" y="-1405"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="1414" y="-1408"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="1414" y="-1408"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1964" y="-609"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1964" y="-609"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2090" y="-610"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2090" y="-610"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2266" y="-611"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2266" y="-611"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2442" y="-611"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2442" y="-611"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2676" y="-610"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2676" y="-610"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2821" y="-610"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2821" y="-610"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2965" y="-611"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2965" y="-611"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3109" y="-609"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3109" y="-609"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3253" y="-613"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3253" y="-613"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3397" y="-613"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3397" y="-613"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1687" y="-1370"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1687" y="-1370"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1687" y="-1409"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1687" y="-1409"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="2563" y="-836"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="2563" y="-836"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2270" y="-1204"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2270" y="-1204"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2563" y="-1204"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2563" y="-1204"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2843" y="-1204"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2843" y="-1204"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3120" y="-1204"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3120" y="-1204"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="1626,-1394 1623,-1397 1623,-1344 1626,-1347 1626,-1394" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="1626,-1394 1623,-1397 1674,-1397 1671,-1394 1626,-1394" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="1626,-1347 1623,-1344 1674,-1344 1671,-1347 1626,-1347" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="1671,-1394 1674,-1397 1674,-1344 1671,-1347 1671,-1394" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="47" stroke="rgb(255,255,255)" width="45" x="1626" y="-1394"/>
     <rect fill="none" height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="1626" y="-1394"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="2789" y="-944"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="2789" y="-944"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="2174" y="-958"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="2174" y="-958"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="23" qtmmishow="hidden" width="81" x="1354" y="-1025"/>
    </a>
   <metadata/><rect fill="white" height="23" opacity="0" stroke="white" transform="" width="81" x="1354" y="-1025"/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NH_HS.NH_HS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2119,-1092 3248,-1092 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23829" ObjectName="BS-NH_HS.NH_HS_3IM"/>
    <cge:TPSR_Ref TObjectID="23829"/></metadata>
   <polyline fill="none" opacity="0" points="2119,-1092 3248,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NH_HS.NH_HS_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2600,-720 3579,-720 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23830" ObjectName="BS-NH_HS.NH_HS_9IM"/>
    <cge:TPSR_Ref TObjectID="23830"/></metadata>
   <polyline fill="none" opacity="0" points="2600,-720 3579,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NH_HS.NH_HS_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1743,-720 2558,-720 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23835" ObjectName="BS-NH_HS.NH_HS_9IIM"/>
    <cge:TPSR_Ref TObjectID="23835"/></metadata>
   <polyline fill="none" opacity="0" points="1743,-720 2558,-720 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 1448.500000 -1327.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217891" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1473.000000 -1231.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217891" ObjectName="NH_HS:NH_HS_sumP1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217891" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1473.000000 -1191.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217891" ObjectName="NH_HS:NH_HS_sumP1"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="NH_HS"/>
</svg>