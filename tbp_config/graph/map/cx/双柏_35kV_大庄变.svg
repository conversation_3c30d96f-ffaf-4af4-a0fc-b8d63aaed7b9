<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-160" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="732 -1879 2320 1448">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape21">
    <polyline points="37,108 39,108 41,107 42,107 44,106 45,105 47,104 48,102 49,100 49,99 50,97 50,95 50,93 49,91 49,90 48,88 47,87 45,85 44,84 42,83 41,83 39,82 37,82 35,82 33,83 32,83 30,84 29,85 27,87 26,88 25,90 25,91 24,93 24,95 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="24" x2="36" y1="95" y2="95"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.774005" x1="37" x2="37" y1="95" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="26" y2="17"/>
    <polyline arcFlag="1" points="13,26 12,26 12,26 11,26 10,26 10,27 9,27 8,28 8,28 8,29 7,30 7,30 7,31 7,32 7,33 7,33 8,34 8,35 8,35 9,36 10,36 10,37 11,37 12,37 12,37 13,37 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,37 12,37 12,37 11,37 10,37 10,38 9,38 8,39 8,39 8,40 7,41 7,41 7,42 7,43 7,44 7,44 8,45 8,46 8,46 9,47 10,47 10,48 11,48 12,48 12,48 13,48 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,48 12,48 12,48 11,48 10,48 10,49 9,49 8,50 8,50 8,51 7,52 7,52 7,53 7,54 7,55 7,55 8,56 8,57 8,57 9,58 10,58 10,59 11,59 12,59 12,59 13,59 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="68" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="68" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.393258" x1="21" x2="56" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="16" y2="16"/>
    <rect height="26" stroke-width="0.398039" width="12" x="31" y="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="20" x2="20" y1="5" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="2" x2="2" y1="58" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="57" x2="57" y1="5" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="37" x2="37" y1="19" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="37" x2="37" y1="108" y2="116"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape201">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.485753" x1="3" x2="15" y1="27" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.786486" x1="15" x2="9" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="6" x2="6" y1="90" y2="96"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.532624" x1="5" x2="8" y1="100" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="3" x2="10" y1="98" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="0" x2="13" y1="96" y2="96"/>
    <polyline arcFlag="1" points="6,79 7,79 7,79 8,79 9,79 9,78 10,78 11,77 11,77 11,76 12,75 12,75 12,74 12,73 12,72 12,72 11,71 11,70 11,70 10,69 9,69 9,68 8,68 7,68 7,68 6,68 " stroke-width="0.815047"/>
    <polyline arcFlag="1" points="6,68 7,68 7,68 8,68 9,68 9,67 10,67 11,66 11,66 11,65 12,64 12,64 12,63 12,62 12,61 12,61 11,60 11,59 11,59 10,58 9,58 9,57 8,57 7,57 7,57 6,57 " stroke-width="0.815047"/>
    <polyline arcFlag="1" points="6,57 7,57 7,57 8,57 9,57 9,56 10,56 11,55 11,55 11,54 12,53 12,53 12,52 12,51 12,50 12,50 11,49 11,48 11,48 10,47 9,47 9,46 8,46 7,46 7,46 6,46 " stroke-width="0.815047"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="6" x2="6" y1="4" y2="13"/>
    <polyline arcFlag="1" points="6,46 7,46 7,46 8,46 9,46 9,45 10,45 11,44 11,44 11,43 12,42 12,42 12,41 12,40 12,39 12,39 11,38 11,37 11,37 10,36 9,36 9,35 8,35 7,35 7,35 6,35 " stroke-width="0.815047"/>
    <polyline arcFlag="1" points="6,35 7,35 7,35 8,35 9,35 9,34 10,34 11,33 11,33 11,32 12,31 12,31 12,30 12,29 12,28 12,28 11,27 11,26 11,26 10,25 9,25 9,24 8,24 7,24 7,24 6,24 " stroke-width="0.815047"/>
    <polyline arcFlag="1" points="6,90 7,90 7,90 8,90 9,90 9,89 10,89 11,88 11,88 11,87 12,86 12,86 12,85 12,84 12,83 12,83 11,82 11,81 11,81 10,80 9,80 9,79 8,79 7,79 7,79 6,79 " stroke-width="0.815047"/>
    <polyline arcFlag="1" points="6,24 7,24 7,24 8,24 9,24 9,23 10,23 11,22 11,22 11,21 12,20 12,20 12,19 12,18 12,17 12,17 11,16 11,15 11,15 10,14 9,14 9,13 8,13 7,13 7,13 6,13 " stroke-width="0.815047"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.71892" x1="15" x2="15" y1="8" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="7" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="24" y1="19" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="15" y1="24" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="24" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="17" y2="24"/>
    <circle cx="17" cy="17" fillStyle="0" r="16" stroke-width="1.0625"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="17" y1="7" y2="7"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
   </symbol>
   <symbol id="switch2:shape25_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape4_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="14" x2="39" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="5" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape4_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="3" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="3" x2="43" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape4-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="11" x2="11" y1="3" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="43" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape4-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="11" x2="11" y1="3" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="43" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape40_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape40_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <polyline DF8003:Layer="PUBLIC" points="30,87 26,78 36,78 30,87 "/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape48_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape48_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
   </symbol>
   <symbol id="transformer2:shape54_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <rect height="28" stroke-width="1" width="14" x="90" y="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="95" x2="98" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="93" x2="101" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="97" x2="97" y1="28" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="103" x2="91" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="97" x2="97" y1="75" y2="40"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="39" y1="75" y2="75"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape54_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape64">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="29" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="6" y1="69" y2="69"/>
    <ellipse cx="8" cy="59" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="67" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="38" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="8" y1="16" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="17" y1="24" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="37" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="10" y1="17" y2="30"/>
   </symbol>
   <symbol id="voltageTransformer:shape65">
    <ellipse cx="19" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="46" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="9" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="8" x2="8" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="11" x2="8" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="5" x2="8" y1="18" y2="20"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="5" x2="9" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="5" x2="9" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="9" x2="9" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="41" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="30" y2="38"/>
    <rect height="13" stroke-width="1" width="7" x="32" y="17"/>
    <ellipse cx="8" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <polyline points="27,8 35,8 35,18 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="38" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="40" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="18" y2="20"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22e3680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22e4030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22e49e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22e5750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22e69a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22e75c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22e7d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_22e85e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1c62630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1c62630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22eb110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22eb110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22ec5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22ec5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_22ed2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22eeec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22efab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22f0870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22f0f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22f2550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22f2d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22f3440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22f3bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22f4ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22f5520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22f6010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22f69d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22f8050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22f8a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22f99c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22fa540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2308b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2300c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2301930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_22fc3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1458" width="2330" x="727" y="-1884"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_280e850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1433.000000 1520.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bdd130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1433.000000 1506.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2976780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1433.000000 1492.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ee6df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1439.000000 1477.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bbc740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1425.000000 1462.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2869320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1657.000000 1879.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e86aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1646.000000 1864.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29a8b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1671.000000 1849.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ef2590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1733.000000 1275.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c46e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1722.000000 1260.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ee8b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1747.000000 1245.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ee9a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1735.000000 1135.000000) translate(0,12)">档位(档):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_280e520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1735.000000 1117.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d37860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1731.000000 1078.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ee7ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1720.000000 1063.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3378370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1745.000000 1048.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27c9550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1125.000000 1017.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3194c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1125.000000 1003.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc5840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1125.000000 989.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6f860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1131.000000 974.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bbca60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1117.000000 959.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16601d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1203.000000 476.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27c5340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1192.000000 461.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29b91f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1217.000000 446.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cbf710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2530.000000 1256.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cbf940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2519.000000 1241.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cbfb80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2544.000000 1226.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cbfeb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2557.000000 1145.000000) translate(0,12)">档位(档):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cce3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2557.000000 1127.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cce6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2542.000000 1059.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cce920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2531.000000 1044.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cceb60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2556.000000 1029.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_260d6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2957.000000 1012.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fec590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2957.000000 998.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fec7a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2957.000000 984.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fec9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2963.000000 969.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fecc20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2949.000000 954.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2210070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1383.000000 1031.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293cef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1372.000000 1016.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33a04b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1397.000000 1001.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2954bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2633.000000 1181.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="2640" cy="1174" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2162f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2531.000000 507.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="2538" cy="500" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="64" stroke="rgb(0,255,0)" stroke-width="1" width="37" x="1271" y="-1317"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="31" stroke="rgb(0,255,0)" stroke-width="1" width="34" x="1314" y="-1313"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="92" stroke="rgb(0,255,0)" stroke-width="1" width="20" x="1219" y="-1342"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="16" stroke="rgb(0,255,0)" stroke-width="1" width="6" x="1227" y="-1307"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(50,205,50)" stroke-width="0.424575" width="29" x="1214" y="-1185"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-108241">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1736.000000 -1582.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21015" ObjectName="SW-SB_DZ.SB_DZ_3616SW"/>
     <cge:Meas_Ref ObjectId="108241"/>
    <cge:TPSR_Ref TObjectID="21015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108240">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1736.000000 -1426.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21014" ObjectName="SW-SB_DZ.SB_DZ_3611SW"/>
     <cge:Meas_Ref ObjectId="108240"/>
    <cge:TPSR_Ref TObjectID="21014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108244">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1640.000000 -1602.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21018" ObjectName="SW-SB_DZ.SB_DZ_36167SW"/>
     <cge:Meas_Ref ObjectId="108244"/>
    <cge:TPSR_Ref TObjectID="21018"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108242">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1831.000000 -1481.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21016" ObjectName="SW-SB_DZ.SB_DZ_36117SW"/>
     <cge:Meas_Ref ObjectId="108242"/>
    <cge:TPSR_Ref TObjectID="21016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108243">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1832.000000 -1572.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21017" ObjectName="SW-SB_DZ.SB_DZ_36160SW"/>
     <cge:Meas_Ref ObjectId="108243"/>
    <cge:TPSR_Ref TObjectID="21017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107721">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2200.000000 -1438.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20982" ObjectName="SW-SB_DZ.SB_DZ_39010SW"/>
     <cge:Meas_Ref ObjectId="107721"/>
    <cge:TPSR_Ref TObjectID="20982"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107716">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2121.000000 -1455.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20977" ObjectName="SW-SB_DZ.SB_DZ_3901SW"/>
     <cge:Meas_Ref ObjectId="107716"/>
    <cge:TPSR_Ref TObjectID="20977"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107720">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2037.000000 -1470.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20981" ObjectName="SW-SB_DZ.SB_DZ_39017SW"/>
     <cge:Meas_Ref ObjectId="107720"/>
    <cge:TPSR_Ref TObjectID="20981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107717">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1667.000000 -1322.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20978" ObjectName="SW-SB_DZ.SB_DZ_3011SW"/>
     <cge:Meas_Ref ObjectId="107717"/>
    <cge:TPSR_Ref TObjectID="20978"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107719">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1568.000000 -1254.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20980" ObjectName="SW-SB_DZ.SB_DZ_30117SW"/>
     <cge:Meas_Ref ObjectId="107719"/>
    <cge:TPSR_Ref TObjectID="20980"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107718">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1667.000000 -970.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20979" ObjectName="SW-SB_DZ.SB_DZ_0011SW"/>
     <cge:Meas_Ref ObjectId="107718"/>
    <cge:TPSR_Ref TObjectID="20979"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107715">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1986.000000 -948.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20976" ObjectName="SW-SB_DZ.SB_DZ_0901SW"/>
     <cge:Meas_Ref ObjectId="107715"/>
    <cge:TPSR_Ref TObjectID="20976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1988.000000 -1046.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107835">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1472.000000 -667.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20992" ObjectName="SW-SB_DZ.SB_DZ_0626SW"/>
     <cge:Meas_Ref ObjectId="107835"/>
    <cge:TPSR_Ref TObjectID="20992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107836">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1472.000000 -851.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20993" ObjectName="SW-SB_DZ.SB_DZ_0621SW"/>
     <cge:Meas_Ref ObjectId="107836"/>
    <cge:TPSR_Ref TObjectID="20993"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107804">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1643.000000 -670.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20989" ObjectName="SW-SB_DZ.SB_DZ_0636SW"/>
     <cge:Meas_Ref ObjectId="107804"/>
    <cge:TPSR_Ref TObjectID="20989"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107805">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1643.000000 -844.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20990" ObjectName="SW-SB_DZ.SB_DZ_0631SW"/>
     <cge:Meas_Ref ObjectId="107805"/>
    <cge:TPSR_Ref TObjectID="20990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107897">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1796.000000 -728.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20998" ObjectName="SW-SB_DZ.SB_DZ_0646SW"/>
     <cge:Meas_Ref ObjectId="107897"/>
    <cge:TPSR_Ref TObjectID="20998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107898">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1796.000000 -846.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20999" ObjectName="SW-SB_DZ.SB_DZ_0641SW"/>
     <cge:Meas_Ref ObjectId="107898"/>
    <cge:TPSR_Ref TObjectID="20999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107866">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2022.000000 -671.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20995" ObjectName="SW-SB_DZ.SB_DZ_0656SW"/>
     <cge:Meas_Ref ObjectId="107866"/>
    <cge:TPSR_Ref TObjectID="20995"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107867">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2022.000000 -845.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20996" ObjectName="SW-SB_DZ.SB_DZ_0651SW"/>
     <cge:Meas_Ref ObjectId="107867"/>
    <cge:TPSR_Ref TObjectID="20996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107772">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2323.000000 -667.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20986" ObjectName="SW-SB_DZ.SB_DZ_0666SW"/>
     <cge:Meas_Ref ObjectId="107772"/>
    <cge:TPSR_Ref TObjectID="20986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107928">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2475.000000 -665.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21001" ObjectName="SW-SB_DZ.SB_DZ_0676SW"/>
     <cge:Meas_Ref ObjectId="107928"/>
    <cge:TPSR_Ref TObjectID="21001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2124.000000 -1303.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107711">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2510.000000 -1590.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20972" ObjectName="SW-SB_DZ.SB_DZ_3626SW"/>
     <cge:Meas_Ref ObjectId="107711"/>
    <cge:TPSR_Ref TObjectID="20972"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107710">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2510.000000 -1434.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20971" ObjectName="SW-SB_DZ.SB_DZ_3621SW"/>
     <cge:Meas_Ref ObjectId="107710"/>
    <cge:TPSR_Ref TObjectID="20971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107714">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2414.000000 -1610.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20975" ObjectName="SW-SB_DZ.SB_DZ_36267SW"/>
     <cge:Meas_Ref ObjectId="107714"/>
    <cge:TPSR_Ref TObjectID="20975"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107712">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2605.000000 -1489.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20973" ObjectName="SW-SB_DZ.SB_DZ_36217SW"/>
     <cge:Meas_Ref ObjectId="107712"/>
    <cge:TPSR_Ref TObjectID="20973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107713">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2606.000000 -1580.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20974" ObjectName="SW-SB_DZ.SB_DZ_36260SW"/>
     <cge:Meas_Ref ObjectId="107713"/>
    <cge:TPSR_Ref TObjectID="20974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107960">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1308.000000 -728.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21005" ObjectName="SW-SB_DZ.SB_DZ_0616SW"/>
     <cge:Meas_Ref ObjectId="107960"/>
    <cge:TPSR_Ref TObjectID="21005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107959">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1308.000000 -851.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21004" ObjectName="SW-SB_DZ.SB_DZ_0611SW"/>
     <cge:Meas_Ref ObjectId="107959"/>
    <cge:TPSR_Ref TObjectID="21004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107981">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1353.000000 -556.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21006" ObjectName="SW-SB_DZ.SB_DZ_06100SW"/>
     <cge:Meas_Ref ObjectId="107981"/>
    <cge:TPSR_Ref TObjectID="21006"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107968">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1356.000000 -712.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21013" ObjectName="SW-SB_DZ.SB_DZ_06167SW"/>
     <cge:Meas_Ref ObjectId="107968"/>
    <cge:TPSR_Ref TObjectID="21013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233132">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1307.000000 -1034.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38888" ObjectName="SW-SB_DZ.SB_DZ_0686SW"/>
     <cge:Meas_Ref ObjectId="233132"/>
    <cge:TPSR_Ref TObjectID="38888"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233131">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1307.000000 -942.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38887" ObjectName="SW-SB_DZ.SB_DZ_0681SW"/>
     <cge:Meas_Ref ObjectId="233131"/>
    <cge:TPSR_Ref TObjectID="38887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233133">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1270.000000 -1231.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38889" ObjectName="SW-SB_DZ.SB_DZ_0030SW"/>
     <cge:Meas_Ref ObjectId="233133"/>
    <cge:TPSR_Ref TObjectID="38889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244003">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2662.000000 -672.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41026" ObjectName="SW-SB_DZ.SB_DZ_0696SW"/>
     <cge:Meas_Ref ObjectId="244003"/>
    <cge:TPSR_Ref TObjectID="41026"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-262184">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2662.000000 -846.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42829" ObjectName="SW-SB_DZ.SB_DZ_0692SW"/>
     <cge:Meas_Ref ObjectId="262184"/>
    <cge:TPSR_Ref TObjectID="42829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244048">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2905.000000 -667.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41029" ObjectName="SW-SB_DZ.SB_DZ_0716SW"/>
     <cge:Meas_Ref ObjectId="244048"/>
    <cge:TPSR_Ref TObjectID="41029"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-262221">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2905.000000 -841.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42830" ObjectName="SW-SB_DZ.SB_DZ_0712SW"/>
     <cge:Meas_Ref ObjectId="262221"/>
    <cge:TPSR_Ref TObjectID="42830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261809">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2464.000000 -1319.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42820" ObjectName="SW-SB_DZ.SB_DZ_3021SW"/>
     <cge:Meas_Ref ObjectId="261809"/>
    <cge:TPSR_Ref TObjectID="42820"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261810">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2365.000000 -1253.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42821" ObjectName="SW-SB_DZ.SB_DZ_30217SW"/>
     <cge:Meas_Ref ObjectId="261810"/>
    <cge:TPSR_Ref TObjectID="42821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261811">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2464.000000 -961.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42822" ObjectName="SW-SB_DZ.SB_DZ_0022SW"/>
     <cge:Meas_Ref ObjectId="261811"/>
    <cge:TPSR_Ref TObjectID="42822"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261895">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2776.000000 -952.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42824" ObjectName="SW-SB_DZ.SB_DZ_0902SW"/>
     <cge:Meas_Ref ObjectId="261895"/>
    <cge:TPSR_Ref TObjectID="42824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261901">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2183.090909 -985.000000)" xlink:href="#switch2:shape4_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42826" ObjectName="SW-SB_DZ.SB_DZ_0121SW"/>
     <cge:Meas_Ref ObjectId="261901"/>
    <cge:TPSR_Ref TObjectID="42826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107773">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2323.000000 -841.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42827" ObjectName="SW-SB_DZ.SB_DZ_0662SW"/>
     <cge:Meas_Ref ObjectId="107773"/>
    <cge:TPSR_Ref TObjectID="42827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-262107">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2475.000000 -845.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42828" ObjectName="SW-SB_DZ.SB_DZ_0672SW"/>
     <cge:Meas_Ref ObjectId="262107"/>
    <cge:TPSR_Ref TObjectID="42828"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-SB_DZ.SB_DZ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1377,-1406 2904,-1406 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20966" ObjectName="BS-SB_DZ.SB_DZ_3IM"/>
    <cge:TPSR_Ref TObjectID="20966"/></metadata>
   <polyline fill="none" opacity="0" points="1377,-1406 2904,-1406 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-SB_DZ.SB_DZ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1194,-928 2103,-928 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20967" ObjectName="BS-SB_DZ.SB_DZ_9IM"/>
    <cge:TPSR_Ref TObjectID="20967"/></metadata>
   <polyline fill="none" opacity="0" points="1194,-928 2103,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-SB_DZ.SB_DZ_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2294,-928 3019,-928 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20968" ObjectName="BS-SB_DZ.SB_DZ_9IIM"/>
    <cge:TPSR_Ref TObjectID="20968"/></metadata>
   <polyline fill="none" opacity="0" points="2294,-928 3019,-928 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-SB_DZ.SB_DZ_Cb1">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1354.000000 -539.000000)" xlink:href="#capacitor:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40826" ObjectName="CB-SB_DZ.SB_DZ_Cb1"/>
    <cge:TPSR_Ref TObjectID="40826"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-SB_DZ.SB_DZ_Zyb1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2101.000000 -1196.000000)" xlink:href="#transformer2:shape40_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2101.000000 -1196.000000)" xlink:href="#transformer2:shape40_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21011" ObjectName="TF-SB_DZ.SB_DZ_Zyb1"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-SB_DZ.SB_DZ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.069444 -0.000000 0.000000 -1.088889 1636.000000 -1099.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.069444 -0.000000 0.000000 -1.088889 1636.000000 -1099.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21009" ObjectName="TF-SB_DZ.SB_DZ_1T"/>
    <cge:TPSR_Ref TObjectID="21009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-SB_DZ.SB_DZ_Zyb2">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.720000 -0.000000 0.000000 -0.777778 1860.000000 -602.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.720000 -0.000000 0.000000 -0.777778 1860.000000 -602.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21012" ObjectName="TF-SB_DZ.SB_DZ_Zyb2"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-SB_DZ.SB_DZ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="18688"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2434.000000 -1095.000000)" xlink:href="#transformer2:shape54_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2434.000000 -1095.000000)" xlink:href="#transformer2:shape54_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="42816" ObjectName="TF-SB_DZ.SB_DZ_2T"/>
    <cge:TPSR_Ref TObjectID="42816"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3176df0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1765.000000 -1693.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33d7040">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2121.000000 -1547.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f9b960">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2164.000000 -1527.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27567c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1925.000000 -978.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33d7670">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1430.000000 -595.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a180f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1601.000000 -588.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31816b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1754.000000 -651.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ec4f60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1980.000000 -589.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d55b60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2281.000000 -585.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3188bd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2433.000000 -583.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bd31b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2539.000000 -1701.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_243a7a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1471.000000 -581.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28de4b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1642.000000 -577.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33a2950">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1795.000000 -597.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33e1400">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1310.648489 -1089.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31a0ad0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -0.626634 1273.000000 -1262.000000)" xlink:href="#lightningRod:shape201"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d27750">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1299.000000 -1196.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d4f9a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1355.000000 -1197.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d25650">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2620.000000 -590.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c4b5b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2863.000000 -585.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28dcf80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1256.000000 -642.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_261e120">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2715.000000 -982.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2749c30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2776.000000 -1060.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2958750">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2474.000000 -574.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3137360">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2661.000000 -573.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23eb3b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2904.000000 -573.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 850.000000 -1581.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-262848" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 882.000000 -1400.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="262848" ObjectName="SB_DZ:SB_DZ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-262849" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 882.000000 -1360.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="262849" ObjectName="SB_DZ:SB_DZ_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-262848" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 879.000000 -1485.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="262848" ObjectName="SB_DZ:SB_DZ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-262848" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 881.000000 -1445.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="262848" ObjectName="SB_DZ:SB_DZ_sumP"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-108058" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1730.000000 -1879.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108058" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21008"/>
     <cge:Term_Ref ObjectID="29261"/>
    <cge:TPSR_Ref TObjectID="21008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-108059" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1730.000000 -1879.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108059" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21008"/>
     <cge:Term_Ref ObjectID="29261"/>
    <cge:TPSR_Ref TObjectID="21008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-108060" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1730.000000 -1879.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108060" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21008"/>
     <cge:Term_Ref ObjectID="29261"/>
    <cge:TPSR_Ref TObjectID="21008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-108072" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2503.000000 -1879.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108072" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21007"/>
     <cge:Term_Ref ObjectID="29259"/>
    <cge:TPSR_Ref TObjectID="21007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-108073" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2503.000000 -1879.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108073" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21007"/>
     <cge:Term_Ref ObjectID="29259"/>
    <cge:TPSR_Ref TObjectID="21007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-108074" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2503.000000 -1879.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108074" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21007"/>
     <cge:Term_Ref ObjectID="29259"/>
    <cge:TPSR_Ref TObjectID="21007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107532" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1788.000000 -1274.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107532" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20983"/>
     <cge:Term_Ref ObjectID="29211"/>
    <cge:TPSR_Ref TObjectID="20983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107533" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1788.000000 -1274.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107533" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20983"/>
     <cge:Term_Ref ObjectID="29211"/>
    <cge:TPSR_Ref TObjectID="20983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107534" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1788.000000 -1274.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107534" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20983"/>
     <cge:Term_Ref ObjectID="29211"/>
    <cge:TPSR_Ref TObjectID="20983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107557" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2584.000000 -1254.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107557" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42818"/>
     <cge:Term_Ref ObjectID="18690"/>
    <cge:TPSR_Ref TObjectID="42818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107558" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2584.000000 -1254.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107558" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42818"/>
     <cge:Term_Ref ObjectID="18690"/>
    <cge:TPSR_Ref TObjectID="42818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107559" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2584.000000 -1254.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42818"/>
     <cge:Term_Ref ObjectID="18690"/>
    <cge:TPSR_Ref TObjectID="42818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-108084" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1785.000000 -1077.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108084" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20984"/>
     <cge:Term_Ref ObjectID="29213"/>
    <cge:TPSR_Ref TObjectID="20984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107544" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1785.000000 -1077.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107544" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20984"/>
     <cge:Term_Ref ObjectID="29213"/>
    <cge:TPSR_Ref TObjectID="20984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107545" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1785.000000 -1077.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107545" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20984"/>
     <cge:Term_Ref ObjectID="29213"/>
    <cge:TPSR_Ref TObjectID="20984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107569" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2597.000000 -1059.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107569" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42819"/>
     <cge:Term_Ref ObjectID="18692"/>
    <cge:TPSR_Ref TObjectID="42819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107570" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2597.000000 -1059.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107570" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42819"/>
     <cge:Term_Ref ObjectID="18692"/>
    <cge:TPSR_Ref TObjectID="42819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107571" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2597.000000 -1059.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107571" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42819"/>
     <cge:Term_Ref ObjectID="18692"/>
    <cge:TPSR_Ref TObjectID="42819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107647" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1439.000000 -1029.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107647" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38886"/>
     <cge:Term_Ref ObjectID="58340"/>
    <cge:TPSR_Ref TObjectID="38886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107648" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1439.000000 -1029.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107648" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38886"/>
     <cge:Term_Ref ObjectID="58340"/>
    <cge:TPSR_Ref TObjectID="38886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107649" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1439.000000 -1029.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107649" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38886"/>
     <cge:Term_Ref ObjectID="58340"/>
    <cge:TPSR_Ref TObjectID="38886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107599" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1290.000000 -461.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107599" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21003"/>
     <cge:Term_Ref ObjectID="29251"/>
    <cge:TPSR_Ref TObjectID="21003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107600" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1290.000000 -461.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107600" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21003"/>
     <cge:Term_Ref ObjectID="29251"/>
    <cge:TPSR_Ref TObjectID="21003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107605" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1452.000000 -476.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107605" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20991"/>
     <cge:Term_Ref ObjectID="29227"/>
    <cge:TPSR_Ref TObjectID="20991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107606" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1452.000000 -476.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107606" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20991"/>
     <cge:Term_Ref ObjectID="29227"/>
    <cge:TPSR_Ref TObjectID="20991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107607" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1452.000000 -476.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107607" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20991"/>
     <cge:Term_Ref ObjectID="29227"/>
    <cge:TPSR_Ref TObjectID="20991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107612" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1618.000000 -476.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107612" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20988"/>
     <cge:Term_Ref ObjectID="29221"/>
    <cge:TPSR_Ref TObjectID="20988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107613" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1618.000000 -476.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107613" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20988"/>
     <cge:Term_Ref ObjectID="29221"/>
    <cge:TPSR_Ref TObjectID="20988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107614" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1618.000000 -476.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107614" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20988"/>
     <cge:Term_Ref ObjectID="29221"/>
    <cge:TPSR_Ref TObjectID="20988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107619" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1792.000000 -476.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107619" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20997"/>
     <cge:Term_Ref ObjectID="29239"/>
    <cge:TPSR_Ref TObjectID="20997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107620" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1792.000000 -476.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107620" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20997"/>
     <cge:Term_Ref ObjectID="29239"/>
    <cge:TPSR_Ref TObjectID="20997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107621" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1792.000000 -476.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107621" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20997"/>
     <cge:Term_Ref ObjectID="29239"/>
    <cge:TPSR_Ref TObjectID="20997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107626" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2007.000000 -476.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107626" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20994"/>
     <cge:Term_Ref ObjectID="29233"/>
    <cge:TPSR_Ref TObjectID="20994"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107627" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2007.000000 -476.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107627" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20994"/>
     <cge:Term_Ref ObjectID="29233"/>
    <cge:TPSR_Ref TObjectID="20994"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107628" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2007.000000 -476.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107628" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20994"/>
     <cge:Term_Ref ObjectID="29233"/>
    <cge:TPSR_Ref TObjectID="20994"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107633" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2312.000000 -476.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107633" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20985"/>
     <cge:Term_Ref ObjectID="29215"/>
    <cge:TPSR_Ref TObjectID="20985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107634" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2312.000000 -476.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107634" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20985"/>
     <cge:Term_Ref ObjectID="29215"/>
    <cge:TPSR_Ref TObjectID="20985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107635" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2312.000000 -476.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107635" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20985"/>
     <cge:Term_Ref ObjectID="29215"/>
    <cge:TPSR_Ref TObjectID="20985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107640" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2468.000000 -476.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107640" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21000"/>
     <cge:Term_Ref ObjectID="29245"/>
    <cge:TPSR_Ref TObjectID="21000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107641" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2468.000000 -476.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107641" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21000"/>
     <cge:Term_Ref ObjectID="29245"/>
    <cge:TPSR_Ref TObjectID="21000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107642" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2468.000000 -476.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107642" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21000"/>
     <cge:Term_Ref ObjectID="29245"/>
    <cge:TPSR_Ref TObjectID="21000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107654" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2656.000000 -476.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107654" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41024"/>
     <cge:Term_Ref ObjectID="62155"/>
    <cge:TPSR_Ref TObjectID="41024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107655" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2656.000000 -476.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107655" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41024"/>
     <cge:Term_Ref ObjectID="62155"/>
    <cge:TPSR_Ref TObjectID="41024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107656" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2656.000000 -476.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107656" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41024"/>
     <cge:Term_Ref ObjectID="62155"/>
    <cge:TPSR_Ref TObjectID="41024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-107661" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2887.000000 -476.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107661" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41025"/>
     <cge:Term_Ref ObjectID="62157"/>
    <cge:TPSR_Ref TObjectID="41025"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-107662" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2887.000000 -476.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107662" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41025"/>
     <cge:Term_Ref ObjectID="62157"/>
    <cge:TPSR_Ref TObjectID="41025"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-107663" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2887.000000 -476.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107663" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41025"/>
     <cge:Term_Ref ObjectID="62157"/>
    <cge:TPSR_Ref TObjectID="41025"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-107524" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1487.000000 -1522.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107524" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20966"/>
     <cge:Term_Ref ObjectID="29180"/>
    <cge:TPSR_Ref TObjectID="20966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-107525" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1487.000000 -1522.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107525" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20966"/>
     <cge:Term_Ref ObjectID="29180"/>
    <cge:TPSR_Ref TObjectID="20966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-107526" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1487.000000 -1522.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107526" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20966"/>
     <cge:Term_Ref ObjectID="29180"/>
    <cge:TPSR_Ref TObjectID="20966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-107530" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1487.000000 -1522.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107530" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20966"/>
     <cge:Term_Ref ObjectID="29180"/>
    <cge:TPSR_Ref TObjectID="20966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-107527" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1487.000000 -1522.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107527" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20966"/>
     <cge:Term_Ref ObjectID="29180"/>
    <cge:TPSR_Ref TObjectID="20966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-107583" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 -1016.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107583" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20967"/>
     <cge:Term_Ref ObjectID="29181"/>
    <cge:TPSR_Ref TObjectID="20967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-107584" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 -1016.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107584" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20967"/>
     <cge:Term_Ref ObjectID="29181"/>
    <cge:TPSR_Ref TObjectID="20967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-107585" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 -1016.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107585" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20967"/>
     <cge:Term_Ref ObjectID="29181"/>
    <cge:TPSR_Ref TObjectID="20967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-107589" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 -1016.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107589" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20967"/>
     <cge:Term_Ref ObjectID="29181"/>
    <cge:TPSR_Ref TObjectID="20967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-107586" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 -1016.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107586" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20967"/>
     <cge:Term_Ref ObjectID="29181"/>
    <cge:TPSR_Ref TObjectID="20967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-107591" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3011.000000 -1011.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107591" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20968"/>
     <cge:Term_Ref ObjectID="29182"/>
    <cge:TPSR_Ref TObjectID="20968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-107592" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3011.000000 -1011.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107592" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20968"/>
     <cge:Term_Ref ObjectID="29182"/>
    <cge:TPSR_Ref TObjectID="20968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-107593" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3011.000000 -1011.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107593" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20968"/>
     <cge:Term_Ref ObjectID="29182"/>
    <cge:TPSR_Ref TObjectID="20968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-107597" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3011.000000 -1011.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107597" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20968"/>
     <cge:Term_Ref ObjectID="29182"/>
    <cge:TPSR_Ref TObjectID="20968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-107594" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3011.000000 -1011.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107594" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20968"/>
     <cge:Term_Ref ObjectID="29182"/>
    <cge:TPSR_Ref TObjectID="20968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-107555" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1806.000000 -1133.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21009"/>
     <cge:Term_Ref ObjectID="29266"/>
    <cge:TPSR_Ref TObjectID="21009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-107556" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1806.000000 -1133.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107556" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21009"/>
     <cge:Term_Ref ObjectID="29266"/>
    <cge:TPSR_Ref TObjectID="21009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-107581" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2629.000000 -1143.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107581" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42816"/>
     <cge:Term_Ref ObjectID="18689"/>
    <cge:TPSR_Ref TObjectID="42816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-107582" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2629.000000 -1143.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="107582" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42816"/>
     <cge:Term_Ref ObjectID="18689"/>
    <cge:TPSR_Ref TObjectID="42816"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="861" y="-1640"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="861" y="-1640"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="813" y="-1657"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="813" y="-1657"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1489" y="-820"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1489" y="-820"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1660" y="-819"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1660" y="-819"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2040" y="-822"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2040" y="-822"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2341" y="-822"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2341" y="-822"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2493" y="-822"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2493" y="-822"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1325" y="-821"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1325" y="-821"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1754" y="-1511"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1754" y="-1511"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2528" y="-1519"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2528" y="-1519"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1815" y="-823"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1815" y="-823"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1026" y="-1628"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1026" y="-1628"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1026" y="-1663"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1026" y="-1663"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1323" y="-1017"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1323" y="-1017"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="29" qtmmishow="hidden" width="67" x="1747" y="-1192"/>
    </a>
   <metadata/><rect fill="white" height="29" opacity="0" stroke="white" transform="" width="67" x="1747" y="-1192"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="1098,-1514 1095,-1517 1095,-1463 1098,-1466 1098,-1514" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="1098,-1514 1095,-1517 1159,-1517 1156,-1514 1098,-1514" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="1098,-1466 1095,-1463 1159,-1463 1156,-1466 1098,-1466" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="1156,-1514 1159,-1517 1159,-1463 1156,-1466 1156,-1514" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="48" stroke="rgb(255,255,255)" width="58" x="1098" y="-1514"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(0,0,0)" width="58" x="1098" y="-1514"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2923" y="-822"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2923" y="-822"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2680" y="-822"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2680" y="-822"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="80" x="874" y="-1256"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="80" x="874" y="-1256"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="69" x="2544" y="-1183"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="69" x="2544" y="-1183"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="861" y="-1640"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="813" y="-1657"/></g>
   <g href="35kV大庄变10kV洒利黑线062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1489" y="-820"/></g>
   <g href="35kV大庄变10kV水泥厂线063间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1660" y="-819"/></g>
   <g href="35kV大庄变10kV干海资线065间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2040" y="-822"/></g>
   <g href="35kV大庄变10kV桃园线066间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2341" y="-822"/></g>
   <g href="35kV大庄变10kV城街线067间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2493" y="-822"/></g>
   <g href="35kV大庄变10kV1号电容器组061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1325" y="-821"/></g>
   <g href="35kV大庄变35kV桐大线361间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1754" y="-1511"/></g>
   <g href="35kV大庄变35kV双妥大线362间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2528" y="-1519"/></g>
   <g href="35kV大庄变10kV马街子线064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1815" y="-823"/></g>
   <g href="cx_配调_配网接线图35_双柏.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1026" y="-1628"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1026" y="-1663"/></g>
   <g href="35kV大庄变10kV1号接地变及消弧线圈068间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1323" y="-1017"/></g>
   <g href="35kV大庄变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="29" qtmmishow="hidden" width="67" x="1747" y="-1192"/></g>
   <g href="AVC大庄站.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(0,0,0)" width="58" x="1098" y="-1514"/></g>
   <g href="35kV大庄变10kV木章郎Ⅰ回线071间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2923" y="-822"/></g>
   <g href="35kV大庄变10kV尹代箐线069间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2680" y="-822"/></g>
   <g href="35kV大庄变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="80" x="874" y="-1256"/></g>
   <g href="35kV大庄变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="69" x="2544" y="-1183"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.637931" x1="1291" x2="1291" y1="-1265" y2="-1281"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.637931" x1="1291" x2="1291" y1="-1288" y2="-1304"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1300" x2="1335" y1="-1304" y2="-1304"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1300" x2="1333" y1="-1289" y2="-1289"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1300" x2="1305" y1="-1281" y2="-1281"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1300" x2="1305" y1="-1266" y2="-1266"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.394631" x1="1234" x2="1226" y1="-1351" y2="-1351"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.394631" x1="1232" x2="1228" y1="-1353" y2="-1353"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.339777" x1="1230" x2="1229" y1="-1354" y2="-1354"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.533936" x1="1230" x2="1230" y1="-1338" y2="-1350"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1279" x2="1279" y1="-1226" y2="-1269"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1230" x2="1230" y1="-1235" y2="-1256"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1279" x2="1230" y1="-1235" y2="-1235"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1230" x2="1230" y1="-1255" y2="-1264"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1229" x2="1231" y1="-1264" y2="-1264"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1230" x2="1222" y1="-1282" y2="-1265"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1230" x2="1230" y1="-1282" y2="-1291"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1230" x2="1230" y1="-1307" y2="-1316"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.25" x1="1201" x2="1201" y1="-1180" y2="-1177"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.39375" x1="1203" x2="1203" y1="-1182" y2="-1175"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.611465" x1="1205" x2="1205" y1="-1173" y2="-1184"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="1205" x2="1214" y1="-1179" y2="-1179"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="1316" x2="1220" y1="-1178" y2="-1178"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1279" x2="1279" y1="-1177" y2="-1190"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_31896f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1636.000000 -1674.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2033ac0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2110.000000 -1621.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33082b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1975.000000 -1124.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c168b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2410.000000 -1682.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2839dd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2765.000000 -1128.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-SB_DZ.062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1476.000000 -525.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34049" ObjectName="EC-SB_DZ.062Ld"/>
    <cge:TPSR_Ref TObjectID="34049"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_DZ.063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1647.000000 -528.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34050" ObjectName="EC-SB_DZ.063Ld"/>
    <cge:TPSR_Ref TObjectID="34050"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_DZ.064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1800.000000 -530.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34051" ObjectName="EC-SB_DZ.064Ld"/>
    <cge:TPSR_Ref TObjectID="34051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_DZ.065Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2026.000000 -529.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34052" ObjectName="EC-SB_DZ.065Ld"/>
    <cge:TPSR_Ref TObjectID="34052"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_DZ.066Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2327.000000 -525.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34053" ObjectName="EC-SB_DZ.066Ld"/>
    <cge:TPSR_Ref TObjectID="34053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_DZ.067Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2479.000000 -523.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42842" ObjectName="EC-SB_DZ.067Ld"/>
    <cge:TPSR_Ref TObjectID="42842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_DZ.069Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2666.000000 -530.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42843" ObjectName="EC-SB_DZ.069Ld"/>
    <cge:TPSR_Ref TObjectID="42843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_DZ.071Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2909.000000 -525.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42844" ObjectName="EC-SB_DZ.071Ld"/>
    <cge:TPSR_Ref TObjectID="42844"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2054290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1745,-1406 1745,-1431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20966@0" ObjectIDZND0="21014@0" Pin0InfoVect0LinkObjId="SW-108240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ea4930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1745,-1406 1745,-1431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d55920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1745,-1467 1745,-1477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21014@1" ObjectIDZND0="21008@x" ObjectIDZND1="21016@x" Pin0InfoVect0LinkObjId="SW-108239_0" Pin0InfoVect1LinkObjId="SW-108242_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108240_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1745,-1467 1745,-1477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34278d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1745,-1477 1745,-1490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21014@x" ObjectIDND1="21016@x" ObjectIDZND0="21008@0" Pin0InfoVect0LinkObjId="SW-108239_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108240_0" Pin1InfoVect1LinkObjId="SW-108242_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1745,-1477 1745,-1490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33f8660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1840,-1535 1840,-1522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2ef7b40@0" ObjectIDZND0="21016@1" Pin0InfoVect0LinkObjId="SW-108242_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ef7b40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1840,-1535 1840,-1522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3251c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1840,-1486 1840,-1477 1745,-1477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21016@0" ObjectIDZND0="21008@x" ObjectIDZND1="21014@x" Pin0InfoVect0LinkObjId="SW-108239_0" Pin0InfoVect1LinkObjId="SW-108240_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108242_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1840,-1486 1840,-1477 1745,-1477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33eac30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1841,-1577 1841,-1568 1746,-1568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21017@0" ObjectIDZND0="21008@x" ObjectIDZND1="21015@x" Pin0InfoVect0LinkObjId="SW-108239_0" Pin0InfoVect1LinkObjId="SW-108241_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108243_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1841,-1577 1841,-1568 1746,-1568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c07840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1841,-1626 1841,-1613 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_15e81f0@0" ObjectIDZND0="21017@1" Pin0InfoVect0LinkObjId="SW-108243_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15e81f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1841,-1626 1841,-1613 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2717160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1745,-1517 1745,-1568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21008@1" ObjectIDZND0="21017@x" ObjectIDZND1="21015@x" Pin0InfoVect0LinkObjId="SW-108243_0" Pin0InfoVect1LinkObjId="SW-108241_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108239_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1745,-1517 1745,-1568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3188750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1745,-1568 1745,-1587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21008@x" ObjectIDND1="21017@x" ObjectIDZND0="21015@0" Pin0InfoVect0LinkObjId="SW-108241_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108239_0" Pin1InfoVect1LinkObjId="SW-108243_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1745,-1568 1745,-1587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3185130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1745,-1623 1745,-1654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="21015@1" ObjectIDZND0="21018@x" ObjectIDZND1="g_31896f0@0" ObjectIDZND2="g_3176df0@0" Pin0InfoVect0LinkObjId="SW-108244_0" Pin0InfoVect1LinkObjId="g_31896f0_0" Pin0InfoVect2LinkObjId="g_3176df0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108241_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1745,-1623 1745,-1654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_227c1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1745,-1654 1649,-1654 1649,-1643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="21015@x" ObjectIDND1="g_31896f0@0" ObjectIDND2="g_3176df0@0" ObjectIDZND0="21018@1" Pin0InfoVect0LinkObjId="SW-108244_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-108241_0" Pin1InfoVect1LinkObjId="g_31896f0_0" Pin1InfoVect2LinkObjId="g_3176df0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1745,-1654 1649,-1654 1649,-1643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_340b6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1649,-1607 1649,-1588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21018@0" ObjectIDZND0="g_27bf990@0" Pin0InfoVect0LinkObjId="g_27bf990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108244_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1649,-1607 1649,-1588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_341b8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1745,-1679 1644,-1679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="21018@x" ObjectIDND1="21015@x" ObjectIDND2="g_3176df0@0" ObjectIDZND0="g_31896f0@0" Pin0InfoVect0LinkObjId="g_31896f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-108244_0" Pin1InfoVect1LinkObjId="SW-108241_0" Pin1InfoVect2LinkObjId="g_3176df0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1745,-1679 1644,-1679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff9520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1745,-1654 1745,-1679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="21018@x" ObjectIDND1="21015@x" ObjectIDZND0="g_31896f0@0" ObjectIDZND1="g_3176df0@0" ObjectIDZND2="37768@1" Pin0InfoVect0LinkObjId="g_31896f0_0" Pin0InfoVect1LinkObjId="g_3176df0_0" Pin0InfoVect2LinkObjId="g_2f2b550_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108244_0" Pin1InfoVect1LinkObjId="SW-108241_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1745,-1654 1745,-1679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2168ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1745,-1697 1771,-1697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="21018@x" ObjectIDND1="21015@x" ObjectIDND2="g_31896f0@0" ObjectIDZND0="g_3176df0@0" Pin0InfoVect0LinkObjId="g_3176df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-108244_0" Pin1InfoVect1LinkObjId="SW-108241_0" Pin1InfoVect2LinkObjId="g_31896f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1745,-1697 1771,-1697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cbf260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1745,-1679 1745,-1697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="21018@x" ObjectIDND1="21015@x" ObjectIDND2="g_31896f0@0" ObjectIDZND0="g_3176df0@0" ObjectIDZND1="37768@1" Pin0InfoVect0LinkObjId="g_3176df0_0" Pin0InfoVect1LinkObjId="g_2f2b550_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-108244_0" Pin1InfoVect1LinkObjId="SW-108241_0" Pin1InfoVect2LinkObjId="g_31896f0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1745,-1679 1745,-1697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f2b550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1745,-1697 1745,-1741 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="powerLine" ObjectIDND0="21018@x" ObjectIDND1="21015@x" ObjectIDND2="g_31896f0@0" ObjectIDZND0="37768@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-108244_0" Pin1InfoVect1LinkObjId="SW-108241_0" Pin1InfoVect2LinkObjId="g_31896f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1745,-1697 1745,-1741 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d37dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2209,-1479 2209,-1494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20982@1" ObjectIDZND0="g_291f090@0" Pin0InfoVect0LinkObjId="g_291f090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107721_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2209,-1479 2209,-1494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d38010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-1496 2130,-1518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="20977@1" ObjectIDZND0="20981@x" ObjectIDZND1="g_1f9b960@0" ObjectIDZND2="g_33d7040@0" Pin0InfoVect0LinkObjId="SW-107720_0" Pin0InfoVect1LinkObjId="g_1f9b960_0" Pin0InfoVect2LinkObjId="g_33d7040_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107716_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-1496 2130,-1518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d38490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-1518 2046,-1518 2046,-1511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="20977@x" ObjectIDND1="g_1f9b960@0" ObjectIDND2="g_33d7040@0" ObjectIDZND0="20981@1" Pin0InfoVect0LinkObjId="SW-107720_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-107716_0" Pin1InfoVect1LinkObjId="g_1f9b960_0" Pin1InfoVect2LinkObjId="g_33d7040_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-1518 2046,-1518 2046,-1511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d386d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2046,-1475 2046,-1462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20981@0" ObjectIDZND0="g_2615640@0" Pin0InfoVect0LinkObjId="g_2615640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2046,-1475 2046,-1462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a2ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-1583 2130,-1623 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_33d7040@0" ObjectIDZND0="g_2033ac0@0" Pin0InfoVect0LinkObjId="g_2033ac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33d7040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-1583 2130,-1623 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d38910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-1531 2170,-1531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="20981@x" ObjectIDND1="20977@x" ObjectIDND2="g_33d7040@0" ObjectIDZND0="g_1f9b960@0" Pin0InfoVect0LinkObjId="g_1f9b960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-107720_0" Pin1InfoVect1LinkObjId="SW-107716_0" Pin1InfoVect2LinkObjId="g_33d7040_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-1531 2170,-1531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a24b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-1518 2130,-1531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="20981@x" ObjectIDND1="20977@x" ObjectIDZND0="g_1f9b960@0" ObjectIDZND1="g_33d7040@0" Pin0InfoVect0LinkObjId="g_1f9b960_0" Pin0InfoVect1LinkObjId="g_33d7040_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107720_0" Pin1InfoVect1LinkObjId="SW-107716_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-1518 2130,-1531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a3110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-1531 2130,-1552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="20981@x" ObjectIDND1="20977@x" ObjectIDND2="g_1f9b960@0" ObjectIDZND0="g_33d7040@1" Pin0InfoVect0LinkObjId="g_33d7040_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-107720_0" Pin1InfoVect1LinkObjId="SW-107716_0" Pin1InfoVect2LinkObjId="g_1f9b960_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-1531 2130,-1552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a3b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1676,-1191 1676,-1254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="21009@0" ObjectIDZND0="20983@0" Pin0InfoVect0LinkObjId="SW-107763_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2583a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1676,-1191 1676,-1254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a5980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1577,-1259 1577,-1238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20980@0" ObjectIDZND0="g_33ab500@0" Pin0InfoVect0LinkObjId="g_33ab500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107719_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1577,-1259 1577,-1238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31884f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1995,-1036 1932,-1036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="20976@x" ObjectIDZND0="g_27567c0@0" Pin0InfoVect0LinkObjId="g_27567c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-107715_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1995,-1036 1932,-1036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1befc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1481,-653 1437,-653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_243a7a0@0" ObjectIDND1="20992@x" ObjectIDZND0="g_33d7670@0" Pin0InfoVect0LinkObjId="g_33d7670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_243a7a0_0" Pin1InfoVect1LinkObjId="SW-107835_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1481,-653 1437,-653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_317f990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1652,-646 1608,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="20989@x" ObjectIDND1="g_28de4b0@0" ObjectIDZND0="g_2a180f0@0" Pin0InfoVect0LinkObjId="g_2a180f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107804_0" Pin1InfoVect1LinkObjId="g_28de4b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1652,-646 1608,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_317f2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1652,-675 1652,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="20989@0" ObjectIDZND0="g_2a180f0@0" ObjectIDZND1="g_28de4b0@0" Pin0InfoVect0LinkObjId="g_2a180f0_0" Pin0InfoVect1LinkObjId="g_28de4b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107804_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1652,-675 1652,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23da2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2031,-647 1987,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="20995@x" ObjectIDND1="34052@x" ObjectIDZND0="g_2ec4f60@0" Pin0InfoVect0LinkObjId="g_2ec4f60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107866_0" Pin1InfoVect1LinkObjId="EC-SB_DZ.065Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2031,-647 1987,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b90060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2031,-676 2031,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="20995@0" ObjectIDZND0="g_2ec4f60@0" ObjectIDZND1="34052@x" Pin0InfoVect0LinkObjId="g_2ec4f60_0" Pin0InfoVect1LinkObjId="EC-SB_DZ.065Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107866_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2031,-676 2031,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_269a2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2031,-647 2031,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="20995@x" ObjectIDND1="g_2ec4f60@0" ObjectIDZND0="34052@0" Pin0InfoVect0LinkObjId="EC-SB_DZ.065Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107866_0" Pin1InfoVect1LinkObjId="g_2ec4f60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2031,-647 2031,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d65ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2332,-643 2288,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="20986@x" ObjectIDND1="34053@x" ObjectIDZND0="g_1d55b60@0" Pin0InfoVect0LinkObjId="g_1d55b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107772_0" Pin1InfoVect1LinkObjId="EC-SB_DZ.066Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2332,-643 2288,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fc82d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2332,-672 2332,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="20986@0" ObjectIDZND0="g_1d55b60@0" ObjectIDZND1="34053@x" Pin0InfoVect0LinkObjId="g_1d55b60_0" Pin0InfoVect1LinkObjId="EC-SB_DZ.066Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107772_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2332,-672 2332,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ef6360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2332,-643 2332,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="20986@x" ObjectIDND1="g_1d55b60@0" ObjectIDZND0="34053@0" Pin0InfoVect0LinkObjId="EC-SB_DZ.066Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107772_0" Pin1InfoVect1LinkObjId="g_1d55b60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2332,-643 2332,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_318bf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2484,-641 2440,-641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="21001@x" ObjectIDND1="g_2958750@0" ObjectIDZND0="g_3188bd0@0" Pin0InfoVect0LinkObjId="g_3188bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107928_0" Pin1InfoVect1LinkObjId="g_2958750_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2484,-641 2440,-641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15e8b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2132,-1308 2132,-1289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="21011@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2132,-1308 2132,-1289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ea5470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-1406 2130,-1431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20966@0" ObjectIDZND0="20977@0" ObjectIDZND1="20982@x" Pin0InfoVect0LinkObjId="SW-107716_0" Pin0InfoVect1LinkObjId="SW-107721_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ea4930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-1406 2130,-1431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ea4930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-1460 2130,-1431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="20977@0" ObjectIDZND0="20966@0" ObjectIDZND1="20982@x" Pin0InfoVect0LinkObjId="g_3253350_0" Pin0InfoVect1LinkObjId="SW-107721_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107716_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-1460 2130,-1431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ea6670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2130,-1431 2209,-1431 2209,-1443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="20977@0" ObjectIDND1="20966@0" ObjectIDZND0="20982@0" Pin0InfoVect0LinkObjId="SW-107721_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107716_0" Pin1InfoVect1LinkObjId="g_2ea4930_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2130,-1431 2209,-1431 2209,-1443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33bab60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2519,-1475 2519,-1485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20971@1" ObjectIDZND0="21007@x" ObjectIDZND1="20973@x" Pin0InfoVect0LinkObjId="SW-107989_0" Pin0InfoVect1LinkObjId="SW-107712_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107710_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2519,-1475 2519,-1485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33b56b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2519,-1485 2519,-1498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="20971@x" ObjectIDND1="20973@x" ObjectIDZND0="21007@0" Pin0InfoVect0LinkObjId="SW-107989_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107710_0" Pin1InfoVect1LinkObjId="SW-107712_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2519,-1485 2519,-1498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33b5120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2614,-1543 2614,-1530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2c0a4e0@0" ObjectIDZND0="20973@1" Pin0InfoVect0LinkObjId="SW-107712_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c0a4e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2614,-1543 2614,-1530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33b5c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2614,-1494 2614,-1485 2519,-1485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20973@0" ObjectIDZND0="21007@x" ObjectIDZND1="20971@x" Pin0InfoVect0LinkObjId="SW-107989_0" Pin0InfoVect1LinkObjId="SW-107710_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107712_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2614,-1494 2614,-1485 2519,-1485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c19840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2615,-1585 2615,-1576 2520,-1576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20974@0" ObjectIDZND0="21007@x" ObjectIDZND1="20972@x" Pin0InfoVect0LinkObjId="SW-107989_0" Pin0InfoVect1LinkObjId="SW-107711_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107713_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2615,-1585 2615,-1576 2520,-1576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c14170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2615,-1634 2615,-1621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_33e0e40@0" ObjectIDZND0="20974@1" Pin0InfoVect0LinkObjId="SW-107713_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33e0e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2615,-1634 2615,-1621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c10570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2519,-1525 2519,-1576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21007@1" ObjectIDZND0="20974@x" ObjectIDZND1="20972@x" Pin0InfoVect0LinkObjId="SW-107713_0" Pin0InfoVect1LinkObjId="SW-107711_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107989_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2519,-1525 2519,-1576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ef9ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2519,-1576 2519,-1595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21007@x" ObjectIDND1="20974@x" ObjectIDZND0="20972@0" Pin0InfoVect0LinkObjId="SW-107711_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107989_0" Pin1InfoVect1LinkObjId="SW-107713_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2519,-1576 2519,-1595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2699aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2519,-1631 2519,-1662 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="20972@1" ObjectIDZND0="20975@x" ObjectIDZND1="g_2c168b0@0" ObjectIDZND2="g_2bd31b0@0" Pin0InfoVect0LinkObjId="SW-107714_0" Pin0InfoVect1LinkObjId="g_2c168b0_0" Pin0InfoVect2LinkObjId="g_2bd31b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107711_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2519,-1631 2519,-1662 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33b5f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2519,-1662 2423,-1662 2423,-1651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="20972@x" ObjectIDND1="g_2c168b0@0" ObjectIDND2="g_2bd31b0@0" ObjectIDZND0="20975@1" Pin0InfoVect0LinkObjId="SW-107714_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-107711_0" Pin1InfoVect1LinkObjId="g_2c168b0_0" Pin1InfoVect2LinkObjId="g_2bd31b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2519,-1662 2423,-1662 2423,-1651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33b53e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2423,-1615 2423,-1596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20975@0" ObjectIDZND0="g_26dafc0@0" Pin0InfoVect0LinkObjId="g_26dafc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107714_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2423,-1615 2423,-1596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33b4e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2519,-1687 2418,-1687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="20975@x" ObjectIDND1="20972@x" ObjectIDND2="g_2bd31b0@0" ObjectIDZND0="g_2c168b0@0" Pin0InfoVect0LinkObjId="g_2c168b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-107714_0" Pin1InfoVect1LinkObjId="SW-107711_0" Pin1InfoVect2LinkObjId="g_2bd31b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2519,-1687 2418,-1687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33b5970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2519,-1662 2519,-1687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="20975@x" ObjectIDND1="20972@x" ObjectIDZND0="g_2c168b0@0" ObjectIDZND1="g_2bd31b0@0" ObjectIDZND2="34567@1" Pin0InfoVect0LinkObjId="g_2c168b0_0" Pin0InfoVect1LinkObjId="g_2bd31b0_0" Pin0InfoVect2LinkObjId="g_32527f0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107714_0" Pin1InfoVect1LinkObjId="SW-107711_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2519,-1662 2519,-1687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_280c950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2519,-1705 2545,-1705 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="20975@x" ObjectIDND1="20972@x" ObjectIDND2="g_2c168b0@0" ObjectIDZND0="g_2bd31b0@0" Pin0InfoVect0LinkObjId="g_2bd31b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-107714_0" Pin1InfoVect1LinkObjId="SW-107711_0" Pin1InfoVect2LinkObjId="g_2c168b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2519,-1705 2545,-1705 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_275b890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2519,-1687 2519,-1705 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="20975@x" ObjectIDND1="20972@x" ObjectIDND2="g_2c168b0@0" ObjectIDZND0="g_2bd31b0@0" ObjectIDZND1="34567@1" Pin0InfoVect0LinkObjId="g_2bd31b0_0" Pin0InfoVect1LinkObjId="g_32527f0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-107714_0" Pin1InfoVect1LinkObjId="SW-107711_0" Pin1InfoVect2LinkObjId="g_2c168b0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2519,-1687 2519,-1705 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32527f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2519,-1705 2519,-1735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="powerLine" ObjectIDND0="20975@x" ObjectIDND1="20972@x" ObjectIDND2="g_2c168b0@0" ObjectIDZND0="34567@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-107714_0" Pin1InfoVect1LinkObjId="SW-107711_0" Pin1InfoVect2LinkObjId="g_2c168b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2519,-1705 2519,-1735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3253350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2519,-1439 2519,-1406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20971@0" ObjectIDZND0="20966@0" Pin0InfoVect0LinkObjId="g_2ea4930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2519,-1439 2519,-1406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ee2fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1995,-1103 1995,-1126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_33082b0@0" Pin0InfoVect0LinkObjId="g_33082b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1995,-1103 1995,-1126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_285b8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1995,-1036 1995,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_27567c0@0" ObjectIDND1="20976@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_27567c0_0" Pin1InfoVect1LinkObjId="SW-107715_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1995,-1036 1995,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a768c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1995,-989 1995,-1036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="20976@1" ObjectIDZND0="0@x" ObjectIDZND1="g_27567c0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_27567c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107715_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1995,-989 1995,-1036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29aa220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1805,-733 1805,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" EndDevType2="lightningRod" ObjectIDND0="20998@0" ObjectIDZND0="g_31816b0@0" ObjectIDZND1="21012@x" ObjectIDZND2="g_33a2950@0" Pin0InfoVect0LinkObjId="g_31816b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="g_33a2950_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107897_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1805,-733 1805,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bf5460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1761,-709 1805,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" EndDevType2="lightningRod" ObjectIDND0="g_31816b0@0" ObjectIDZND0="20998@x" ObjectIDZND1="21012@x" ObjectIDZND2="g_33a2950@0" Pin0InfoVect0LinkObjId="SW-107897_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="g_33a2950_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31816b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1761,-709 1805,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b7bcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1365,-671 1365,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21013@1" ObjectIDZND0="g_29acde0@0" Pin0InfoVect0LinkObjId="g_29acde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107968_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1365,-671 1365,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33bb050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1481,-653 1481,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_33d7670@0" ObjectIDND1="20992@x" ObjectIDZND0="g_243a7a0@1" Pin0InfoVect0LinkObjId="g_243a7a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33d7670_0" Pin1InfoVect1LinkObjId="SW-107835_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1481,-653 1481,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28de280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1481,-586 1481,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_243a7a0@0" ObjectIDZND0="34049@0" Pin0InfoVect0LinkObjId="EC-SB_DZ.062Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_243a7a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1481,-586 1481,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33a4140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1652,-646 1652,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="20989@x" ObjectIDND1="g_2a180f0@0" ObjectIDZND0="g_28de4b0@1" Pin0InfoVect0LinkObjId="g_28de4b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107804_0" Pin1InfoVect1LinkObjId="g_2a180f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1652,-646 1652,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33a26f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1652,-582 1652,-549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_28de4b0@0" ObjectIDZND0="34050@0" Pin0InfoVect0LinkObjId="EC-SB_DZ.063Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28de4b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1652,-582 1652,-549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d27f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1805,-709 1805,-641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="transformer2" EndDevType0="lightningRod" ObjectIDND0="g_31816b0@0" ObjectIDND1="20998@x" ObjectIDND2="21012@x" ObjectIDZND0="g_33a2950@1" Pin0InfoVect0LinkObjId="g_33a2950_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_31816b0_0" Pin1InfoVect1LinkObjId="SW-107897_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1805,-709 1805,-641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b1dd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1805,-602 1805,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_33a2950@0" ObjectIDZND0="34051@0" Pin0InfoVect0LinkObjId="EC-SB_DZ.064Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33a2950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1805,-602 1805,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b4b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1316,-1023 1316,-1039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38886@1" ObjectIDZND0="38888@0" Pin0InfoVect0LinkObjId="SW-233132_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233130_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1316,-1023 1316,-1039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b4e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1316,-983 1316,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38887@1" ObjectIDZND0="38886@0" Pin0InfoVect0LinkObjId="SW-233130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233131_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1316,-983 1316,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d28380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2671,-648 2627,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="41026@x" ObjectIDND1="g_3137360@0" ObjectIDZND0="g_2d25650@0" Pin0InfoVect0LinkObjId="g_2d25650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-244003_0" Pin1InfoVect1LinkObjId="g_3137360_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2671,-648 2627,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d285b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2671,-677 2671,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="41026@0" ObjectIDZND0="g_2d25650@0" ObjectIDZND1="g_3137360@0" Pin0InfoVect0LinkObjId="g_2d25650_0" Pin0InfoVect1LinkObjId="g_3137360_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244003_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2671,-677 2671,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32703c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2914,-643 2870,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="41029@x" ObjectIDND1="g_23eb3b0@0" ObjectIDZND0="g_2c4b5b0@0" Pin0InfoVect0LinkObjId="g_2c4b5b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-244048_0" Pin1InfoVect1LinkObjId="g_23eb3b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2914,-643 2870,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3270600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2914,-672 2914,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="41029@0" ObjectIDZND0="g_2c4b5b0@0" ObjectIDZND1="g_23eb3b0@0" Pin0InfoVect0LinkObjId="g_2c4b5b0_0" Pin0InfoVect1LinkObjId="g_23eb3b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244048_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2914,-672 2914,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2956410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2473,-1105 2473,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="42816@0" ObjectIDZND0="42819@1" Pin0InfoVect0LinkObjId="SW-261803_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2473,-1105 2473,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33aa740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2473,-1026 2473,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42819@0" ObjectIDZND0="42822@1" Pin0InfoVect0LinkObjId="SW-261811_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261803_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2473,-1026 2473,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33aa9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2473,-1192 2473,-1254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="42816@1" ObjectIDZND0="42818@0" Pin0InfoVect0LinkObjId="SW-261802_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2473,-1192 2473,-1254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2987d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2473,-1281 2473,-1308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="42818@1" ObjectIDZND0="42820@x" ObjectIDZND1="42821@x" Pin0InfoVect0LinkObjId="SW-261809_0" Pin0InfoVect1LinkObjId="SW-261810_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261802_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2473,-1281 2473,-1308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2987f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2473,-1308 2473,-1324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="42818@x" ObjectIDND1="42821@x" ObjectIDZND0="42820@0" Pin0InfoVect0LinkObjId="SW-261809_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-261802_0" Pin1InfoVect1LinkObjId="SW-261810_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2473,-1308 2473,-1324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ea73f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2473,-1308 2374,-1308 2374,-1294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="42820@x" ObjectIDND1="42818@x" ObjectIDZND0="42821@1" Pin0InfoVect0LinkObjId="SW-261810_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-261809_0" Pin1InfoVect1LinkObjId="SW-261802_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2473,-1308 2374,-1308 2374,-1294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ea7650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2374,-1258 2374,-1237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="42821@0" ObjectIDZND0="g_33aac00@0" Pin0InfoVect0LinkObjId="g_33aac00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2374,-1258 2374,-1237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_327a6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1263,-700 1263,-717 1365,-717 1365,-707 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_28dcf80@0" ObjectIDZND0="21013@0" Pin0InfoVect0LinkObjId="SW-107968_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28dcf80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1263,-700 1263,-717 1365,-717 1365,-707 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29743c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2188,-990 2079,-990 2079,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42826@0" ObjectIDZND0="20967@0" Pin0InfoVect0LinkObjId="g_3230500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261901_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2188,-990 2079,-990 2079,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29745f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2225,-991 2321,-991 2321,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42826@1" ObjectIDZND0="20968@0" Pin0InfoVect0LinkObjId="g_2c4c550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261901_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2225,-991 2321,-991 2321,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_293c580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1676,-1319 1676,-1309 1577,-1309 1577,-1295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20983@x" ObjectIDND1="20978@x" ObjectIDZND0="20980@1" Pin0InfoVect0LinkObjId="SW-107719_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107763_0" Pin1InfoVect1LinkObjId="SW-107717_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1676,-1319 1676,-1309 1577,-1309 1577,-1295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_293c7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1316,-1163 1316,-1147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2d27750@0" ObjectIDZND0="g_33e1400@0" Pin0InfoVect0LinkObjId="g_33e1400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d27750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1316,-1163 1316,-1147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_293ca30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1362,-1139 1362,-1084 1316,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1d4f9a0@0" ObjectIDZND0="g_33e1400@0" ObjectIDZND1="38888@x" Pin0InfoVect0LinkObjId="g_33e1400_0" Pin0InfoVect1LinkObjId="SW-233132_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d4f9a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1362,-1139 1362,-1084 1316,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_293cc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1316,-1094 1316,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_33e1400@1" ObjectIDZND0="g_1d4f9a0@0" ObjectIDZND1="38888@x" Pin0InfoVect0LinkObjId="g_1d4f9a0_0" Pin0InfoVect1LinkObjId="SW-233132_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33e1400_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1316,-1094 1316,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_339fb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1316,-1084 1316,-1075 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_33e1400@0" ObjectIDND1="g_1d4f9a0@0" ObjectIDZND0="38888@1" Pin0InfoVect0LinkObjId="SW-233132_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33e1400_0" Pin1InfoVect1LinkObjId="g_1d4f9a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1316,-1084 1316,-1075 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_339fd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1805,-769 1805,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20998@1" ObjectIDZND0="20997@0" Pin0InfoVect0LinkObjId="SW-107896_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107897_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1805,-769 1805,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_339fff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1805,-826 1805,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20997@1" ObjectIDZND0="20999@0" Pin0InfoVect0LinkObjId="SW-107898_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107896_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1805,-826 1805,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33a0250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1481,-826 1481,-856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20991@1" ObjectIDZND0="20993@0" Pin0InfoVect0LinkObjId="SW-107836_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107834_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1481,-826 1481,-856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b3c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2031,-712 2031,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20995@1" ObjectIDZND0="20994@0" Pin0InfoVect0LinkObjId="SW-107865_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107866_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2031,-712 2031,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b3ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2031,-826 2031,-850 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20994@1" ObjectIDZND0="20996@0" Pin0InfoVect0LinkObjId="SW-107867_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107865_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2031,-826 2031,-850 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b4120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1652,-711 1652,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20989@1" ObjectIDZND0="20988@0" Pin0InfoVect0LinkObjId="SW-107803_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107804_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1652,-711 1652,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b4380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1652,-826 1652,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20988@1" ObjectIDZND0="20990@0" Pin0InfoVect0LinkObjId="SW-107805_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107803_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1652,-826 1652,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f2c130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2332,-708 2332,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20986@1" ObjectIDZND0="20985@0" Pin0InfoVect0LinkObjId="SW-107771_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107772_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2332,-708 2332,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f2c390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2332,-826 2332,-846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20985@1" ObjectIDZND0="42827@0" Pin0InfoVect0LinkObjId="SW-107773_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107771_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2332,-826 2332,-846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f2c5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2671,-713 2671,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41026@1" ObjectIDZND0="41024@0" Pin0InfoVect0LinkObjId="SW-244016_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244003_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2671,-713 2671,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f2c850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2671,-826 2671,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41024@1" ObjectIDZND0="42829@0" Pin0InfoVect0LinkObjId="SW-262184_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244016_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2671,-826 2671,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_159c9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2914,-708 2914,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41029@1" ObjectIDZND0="41025@0" Pin0InfoVect0LinkObjId="SW-244045_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244048_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2914,-708 2914,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_159cc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2914,-826 2914,-846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41025@1" ObjectIDZND0="42830@0" Pin0InfoVect0LinkObjId="SW-262221_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244045_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2914,-826 2914,-846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_159cea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1481,-653 1481,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_243a7a0@0" ObjectIDND1="g_33d7670@0" ObjectIDZND0="20992@0" Pin0InfoVect0LinkObjId="SW-107835_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_243a7a0_0" Pin1InfoVect1LinkObjId="g_33d7670_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1481,-653 1481,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_159d100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1481,-708 1481,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20992@1" ObjectIDZND0="20991@0" Pin0InfoVect0LinkObjId="SW-107834_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107835_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1481,-708 1481,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3230500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1316,-947 1316,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38887@0" ObjectIDZND0="20967@0" Pin0InfoVect0LinkObjId="g_29743c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233131_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1316,-947 1316,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32306f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1317,-892 1317,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21004@1" ObjectIDZND0="20967@0" Pin0InfoVect0LinkObjId="g_29743c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107959_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1317,-892 1317,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32308e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1481,-892 1481,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20993@1" ObjectIDZND0="20967@0" Pin0InfoVect0LinkObjId="g_29743c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107836_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1481,-892 1481,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3230ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1652,-885 1652,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20990@1" ObjectIDZND0="20967@0" Pin0InfoVect0LinkObjId="g_29743c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107805_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1652,-885 1652,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3230d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1676,-975 1676,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20979@0" ObjectIDZND0="20967@0" Pin0InfoVect0LinkObjId="g_29743c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107718_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1676,-975 1676,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c4bec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1995,-953 1995,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20976@0" ObjectIDZND0="20967@0" Pin0InfoVect0LinkObjId="g_29743c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107715_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1995,-953 1995,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c4c0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2031,-886 2031,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20996@1" ObjectIDZND0="20967@0" Pin0InfoVect0LinkObjId="g_29743c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107867_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2031,-886 2031,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c4c320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1805,-887 1805,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20999@1" ObjectIDZND0="20967@0" Pin0InfoVect0LinkObjId="g_29743c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107898_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1805,-887 1805,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c4c550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2332,-882 2332,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42827@1" ObjectIDZND0="20968@0" Pin0InfoVect0LinkObjId="g_29745f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107773_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2332,-882 2332,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c4c780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2473,-966 2473,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42822@0" ObjectIDZND0="20968@0" Pin0InfoVect0LinkObjId="g_29745f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261811_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2473,-966 2473,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2749510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2671,-887 2671,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42829@1" ObjectIDZND0="20968@0" Pin0InfoVect0LinkObjId="g_29745f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-262184_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2671,-887 2671,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2749770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2914,-882 2914,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42830@1" ObjectIDZND0="20968@0" Pin0InfoVect0LinkObjId="g_29745f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-262221_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2914,-882 2914,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27499d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2722,-1040 2785,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_261e120@0" ObjectIDZND0="g_2749c30@0" ObjectIDZND1="42824@x" Pin0InfoVect0LinkObjId="g_2749c30_0" Pin0InfoVect1LinkObjId="SW-261895_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_261e120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2722,-1040 2785,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3004cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2785,-1130 2785,-1096 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2839dd0@0" ObjectIDZND0="g_2749c30@0" Pin0InfoVect0LinkObjId="g_2749c30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2839dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2785,-1130 2785,-1096 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3004f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2785,-1065 2785,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2749c30@1" ObjectIDZND0="g_261e120@0" ObjectIDZND1="42824@x" Pin0InfoVect0LinkObjId="g_261e120_0" Pin0InfoVect1LinkObjId="SW-261895_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2749c30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2785,-1065 2785,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25838a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1676,-1011 1676,-1026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20979@1" ObjectIDZND0="20984@0" Pin0InfoVect0LinkObjId="SW-107764_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107718_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1676,-1011 1676,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2583a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1676,-1053 1676,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="20984@1" ObjectIDZND0="21009@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107764_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1676,-1053 1676,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c13a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1676,-1363 1676,-1406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20978@1" ObjectIDZND0="20966@0" Pin0InfoVect0LinkObjId="g_2ea4930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107717_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1676,-1363 1676,-1406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c1590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1676,-1281 1676,-1319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20983@1" ObjectIDZND0="20980@x" ObjectIDZND1="20978@x" Pin0InfoVect0LinkObjId="SW-107719_0" Pin0InfoVect1LinkObjId="SW-107717_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107763_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1676,-1281 1676,-1319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c1780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1676,-1319 1676,-1327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20983@x" ObjectIDND1="20980@x" ObjectIDZND0="20978@0" Pin0InfoVect0LinkObjId="SW-107717_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-107763_0" Pin1InfoVect1LinkObjId="SW-107719_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1676,-1319 1676,-1327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c1970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2131,-1360 2131,-1406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="20966@0" Pin0InfoVect0LinkObjId="g_2ea4930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2131,-1360 2131,-1406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c1b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2473,-1360 2473,-1406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42820@1" ObjectIDZND0="20966@0" Pin0InfoVect0LinkObjId="g_2ea4930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261809_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2473,-1360 2473,-1406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b2910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1317,-856 1317,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21004@0" ObjectIDZND0="21003@1" Pin0InfoVect0LinkObjId="SW-107958_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107959_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1317,-856 1317,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b2b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1317,-799 1317,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21003@0" ObjectIDZND0="21005@1" Pin0InfoVect0LinkObjId="SW-107960_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107958_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1317,-799 1317,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b3250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1878,-668 1878,-709 1805,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="21012@1" ObjectIDZND0="g_31816b0@0" ObjectIDZND1="20998@x" ObjectIDZND2="g_33a2950@0" Pin0InfoVect0LinkObjId="g_31816b0_0" Pin0InfoVect1LinkObjId="SW-107897_0" Pin0InfoVect2LinkObjId="g_33a2950_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1878,-668 1878,-709 1805,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3241c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2785,-957 2785,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42824@0" ObjectIDZND0="20968@0" Pin0InfoVect0LinkObjId="g_29745f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261895_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2785,-957 2785,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3241e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2785,-993 2785,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="42824@1" ObjectIDZND0="g_2749c30@0" ObjectIDZND1="g_261e120@0" Pin0InfoVect0LinkObjId="g_2749c30_0" Pin0InfoVect1LinkObjId="g_261e120_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-261895_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2785,-993 2785,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3242060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2484,-670 2484,-641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="21001@0" ObjectIDZND0="g_3188bd0@0" ObjectIDZND1="g_2958750@0" Pin0InfoVect0LinkObjId="g_3188bd0_0" Pin0InfoVect1LinkObjId="g_2958750_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107928_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2484,-670 2484,-641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ccd0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2484,-799 2484,-706 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21000@0" ObjectIDZND0="21001@1" Pin0InfoVect0LinkObjId="SW-107928_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107927_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2484,-799 2484,-706 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ccd290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2484,-928 2484,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="20968@0" ObjectIDZND0="42828@1" Pin0InfoVect0LinkObjId="SW-262107_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29745f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2484,-928 2484,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ccd480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2484,-850 2484,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42828@0" ObjectIDZND0="21000@1" Pin0InfoVect0LinkObjId="SW-107927_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-262107_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2484,-850 2484,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27d3d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1407,-551 1394,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2620da0@0" ObjectIDZND0="21006@1" Pin0InfoVect0LinkObjId="SW-107981_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2620da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1407,-551 1394,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d028b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2484,-641 2484,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3188bd0@0" ObjectIDND1="21001@x" ObjectIDZND0="g_2958750@1" Pin0InfoVect0LinkObjId="g_2958750_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3188bd0_0" Pin1InfoVect1LinkObjId="SW-107928_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2484,-641 2484,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d10aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2484,-579 2484,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2958750@0" ObjectIDZND0="42842@0" Pin0InfoVect0LinkObjId="EC-SB_DZ.067Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2958750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2484,-579 2484,-544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23eaef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2671,-648 2671,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="41026@x" ObjectIDND1="g_2d25650@0" ObjectIDZND0="g_3137360@1" Pin0InfoVect0LinkObjId="g_3137360_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-244003_0" Pin1InfoVect1LinkObjId="g_2d25650_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2671,-648 2671,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23eb150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2671,-578 2671,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_3137360@0" ObjectIDZND0="42843@0" Pin0InfoVect0LinkObjId="EC-SB_DZ.069Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3137360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2671,-578 2671,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_335f4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2914,-643 2914,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="41029@x" ObjectIDND1="g_2c4b5b0@0" ObjectIDZND0="g_23eb3b0@1" Pin0InfoVect0LinkObjId="g_23eb3b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-244048_0" Pin1InfoVect1LinkObjId="g_2c4b5b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2914,-643 2914,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_335f740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2914,-578 2914,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_23eb3b0@0" ObjectIDZND0="42844@0" Pin0InfoVect0LinkObjId="EC-SB_DZ.071Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23eb3b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2914,-578 2914,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_335f9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1317,-733 1317,-655 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="21005@0" ObjectIDZND0="40826@1" Pin0InfoVect0LinkObjId="CB-SB_DZ.SB_DZ_Cb1_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1317,-733 1317,-655 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2954870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-551 1317,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="21006@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-107981_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-551 1317,-551 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-107506" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1040.000000 -1557.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20951" ObjectName="DYN-SB_DZ"/>
     <cge:Meas_Ref ObjectId="107506"/>
    </metadata>
   </g>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1300,-1273 1299,-1273 1299,-1273 1298,-1273 1298,-1273 1297,-1273 1297,-1272 1297,-1272 1297,-1272 1296,-1271 1296,-1271 1296,-1270 1296,-1270 1296,-1269 1296,-1269 1296,-1268 1296,-1268 1297,-1267 1297,-1267 1297,-1266 1297,-1266 1298,-1266 1298,-1266 1299,-1265 1299,-1265 1300,-1265 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1300,-1281 1299,-1281 1299,-1281 1298,-1281 1298,-1281 1297,-1280 1297,-1280 1297,-1280 1297,-1279 1296,-1279 1296,-1278 1296,-1278 1296,-1277 1296,-1277 1296,-1276 1296,-1276 1296,-1275 1297,-1275 1297,-1275 1297,-1274 1297,-1274 1298,-1274 1298,-1273 1299,-1273 1299,-1273 1300,-1273 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1300,-1297 1299,-1297 1299,-1296 1298,-1296 1298,-1296 1297,-1296 1297,-1295 1297,-1295 1297,-1295 1296,-1294 1296,-1294 1296,-1293 1296,-1293 1296,-1292 1296,-1292 1296,-1291 1296,-1291 1297,-1290 1297,-1290 1297,-1290 1297,-1289 1298,-1289 1298,-1289 1299,-1289 1299,-1288 1300,-1288 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1300,-1304 1299,-1304 1299,-1304 1298,-1304 1298,-1304 1297,-1303 1297,-1303 1297,-1303 1297,-1302 1296,-1302 1296,-1301 1296,-1301 1296,-1300 1296,-1300 1296,-1299 1296,-1299 1296,-1298 1297,-1298 1297,-1298 1297,-1297 1297,-1297 1298,-1297 1298,-1296 1299,-1296 1299,-1296 1300,-1296 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1230,-1327 1229,-1327 1229,-1327 1228,-1327 1227,-1327 1227,-1326 1226,-1326 1225,-1325 1225,-1325 1225,-1324 1224,-1323 1224,-1323 1224,-1322 1224,-1321 1224,-1320 1224,-1320 1225,-1319 1225,-1318 1225,-1318 1226,-1317 1227,-1317 1227,-1316 1228,-1316 1229,-1316 1229,-1316 1230,-1316 " stroke="rgb(0,255,0)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1230,-1338 1229,-1338 1229,-1338 1228,-1338 1227,-1338 1227,-1337 1226,-1337 1225,-1336 1225,-1336 1225,-1335 1224,-1334 1224,-1334 1224,-1333 1224,-1332 1224,-1331 1224,-1331 1225,-1330 1225,-1329 1225,-1329 1226,-1328 1227,-1328 1227,-1327 1228,-1327 1229,-1327 1229,-1327 1230,-1327 " stroke="rgb(0,255,0)" stroke-width="0.171589"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="SB_DZ" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_shuangtuodaTdz" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2519,-1783 2519,-1734 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34567" ObjectName="AC-35kV.LN_shuangtuodaTdz"/>
    <cge:TPSR_Ref TObjectID="34567_SS-160"/></metadata>
   <polyline fill="none" opacity="0" points="2519,-1783 2519,-1734 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="SB_DZ" endPointId="0" endStationName="CX_DZ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_tongda_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1745,-1789 1745,-1740 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37768" ObjectName="AC-35kV.LN_tongda_line"/>
    <cge:TPSR_Ref TObjectID="37768_SS-160"/></metadata>
   <polyline fill="none" opacity="0" points="1745,-1789 1745,-1740 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="20966" cx="1745" cy="-1406" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20966" cx="2130" cy="-1406" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20966" cx="2519" cy="-1406" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20967" cx="2079" cy="-928" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20968" cx="2321" cy="-928" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20967" cx="1316" cy="-928" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20967" cx="1317" cy="-928" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20967" cx="1481" cy="-928" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20967" cx="1652" cy="-928" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20967" cx="1676" cy="-928" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20967" cx="1995" cy="-928" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20967" cx="2031" cy="-928" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20967" cx="1805" cy="-928" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20968" cx="2332" cy="-928" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20968" cx="2473" cy="-928" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20968" cx="2671" cy="-928" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20968" cx="2914" cy="-928" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20966" cx="1676" cy="-1406" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20966" cx="2131" cy="-1406" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20966" cx="2473" cy="-1406" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20968" cx="2785" cy="-928" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20968" cx="2484" cy="-928" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-108239">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1736.000000 -1482.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21008" ObjectName="SW-SB_DZ.SB_DZ_361BK"/>
     <cge:Meas_Ref ObjectId="108239"/>
    <cge:TPSR_Ref TObjectID="21008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107763">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1667.000000 -1246.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20983" ObjectName="SW-SB_DZ.SB_DZ_301BK"/>
     <cge:Meas_Ref ObjectId="107763"/>
    <cge:TPSR_Ref TObjectID="20983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107764">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1667.000000 -1018.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20984" ObjectName="SW-SB_DZ.SB_DZ_001BK"/>
     <cge:Meas_Ref ObjectId="107764"/>
    <cge:TPSR_Ref TObjectID="20984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107834">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1472.000000 -791.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20991" ObjectName="SW-SB_DZ.SB_DZ_062BK"/>
     <cge:Meas_Ref ObjectId="107834"/>
    <cge:TPSR_Ref TObjectID="20991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107803">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1643.000000 -791.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20988" ObjectName="SW-SB_DZ.SB_DZ_063BK"/>
     <cge:Meas_Ref ObjectId="107803"/>
    <cge:TPSR_Ref TObjectID="20988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107896">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1796.000000 -791.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20997" ObjectName="SW-SB_DZ.SB_DZ_064BK"/>
     <cge:Meas_Ref ObjectId="107896"/>
    <cge:TPSR_Ref TObjectID="20997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107865">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2022.000000 -791.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20994" ObjectName="SW-SB_DZ.SB_DZ_065BK"/>
     <cge:Meas_Ref ObjectId="107865"/>
    <cge:TPSR_Ref TObjectID="20994"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107771">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2323.000000 -791.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20985" ObjectName="SW-SB_DZ.SB_DZ_066BK"/>
     <cge:Meas_Ref ObjectId="107771"/>
    <cge:TPSR_Ref TObjectID="20985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107927">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2475.000000 -791.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21000" ObjectName="SW-SB_DZ.SB_DZ_067BK"/>
     <cge:Meas_Ref ObjectId="107927"/>
    <cge:TPSR_Ref TObjectID="21000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107989">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2510.000000 -1490.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21007" ObjectName="SW-SB_DZ.SB_DZ_362BK"/>
     <cge:Meas_Ref ObjectId="107989"/>
    <cge:TPSR_Ref TObjectID="21007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-107958">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1308.000000 -791.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21003" ObjectName="SW-SB_DZ.SB_DZ_061BK"/>
     <cge:Meas_Ref ObjectId="107958"/>
    <cge:TPSR_Ref TObjectID="21003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233130">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1307.000000 -988.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38886" ObjectName="SW-SB_DZ.SB_DZ_068BK"/>
     <cge:Meas_Ref ObjectId="233130"/>
    <cge:TPSR_Ref TObjectID="38886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244016">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2662.000000 -791.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41024" ObjectName="SW-SB_DZ.SB_DZ_069BK"/>
     <cge:Meas_Ref ObjectId="244016"/>
    <cge:TPSR_Ref TObjectID="41024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244045">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2905.000000 -791.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41025" ObjectName="SW-SB_DZ.SB_DZ_071BK"/>
     <cge:Meas_Ref ObjectId="244045"/>
    <cge:TPSR_Ref TObjectID="41025"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261802">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2464.000000 -1246.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42818" ObjectName="SW-SB_DZ.SB_DZ_302BK"/>
     <cge:Meas_Ref ObjectId="261802"/>
    <cge:TPSR_Ref TObjectID="42818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-261803">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2464.000000 -1018.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42819" ObjectName="SW-SB_DZ.SB_DZ_002BK"/>
     <cge:Meas_Ref ObjectId="261803"/>
    <cge:TPSR_Ref TObjectID="42819"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cb0220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1050.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cb0220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1050.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cb0220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1050.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cb0220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1050.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cb0220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1050.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cb0220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1050.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cb0220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1050.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cb0220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1050.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cb0220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1050.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cb0220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1050.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cb0220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1050.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cb0220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1050.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cb0220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1050.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cb0220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1050.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cb0220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1050.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cb0220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1050.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cb0220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1050.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cb0220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1050.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_222e5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 897.000000 -1629.500000) translate(0,16)">大庄变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2f663e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1714.000000 -1821.000000) translate(0,18)">桐大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eb06c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1377.000000 -1429.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a17a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1163.000000 -897.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_25fd3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2051.000000 -1697.000000) translate(0,18)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_341d660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1919.000000 -1220.000000) translate(0,18)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_341d660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1919.000000 -1220.000000) translate(0,40)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_27c44e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2455.000000 -512.000000) translate(0,18)">城街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_28b2e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2482.000000 -1821.000000) translate(0,18)">双妥大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e875d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2528.000000 -1519.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22789a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2526.000000 -1620.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29b84b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2526.000000 -1464.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b68a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2621.000000 -1519.000000) translate(0,12)">36217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2678be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2622.000000 -1610.000000) translate(0,12)">36260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_288cfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2430.000000 -1640.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc4fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2216.000000 -1468.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ee3b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2137.000000 -1485.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eacda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2053.000000 -1500.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2868fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1685.000000 -1270.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28b4f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1683.000000 -1345.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e94f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1584.000000 -1292.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25da9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1685.000000 -1056.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d55850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1683.000000 -1018.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_317efc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2491.000000 -695.000000) translate(0,12)">0676</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d65c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2491.000000 -869.000000) translate(0,12)">0672</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fdc6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2339.000000 -871.000000) translate(0,12)">0662</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26210a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2339.000000 -697.000000) translate(0,12)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26774e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2038.000000 -701.000000) translate(0,12)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e87020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2038.000000 -875.000000) translate(0,12)">0651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c10840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1812.000000 -876.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33786f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1812.000000 -758.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d9fbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1659.000000 -874.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f10320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1659.000000 -700.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25bb090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1490.000000 -820.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2859110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1488.000000 -881.000000) translate(0,12)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b69d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1488.000000 -707.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31a0f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2002.000000 -978.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ad5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1754.000000 -1511.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2900a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1752.000000 -1612.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b91bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1656.000000 -1632.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b3c710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1851.000000 -1602.000000) translate(0,12)">36160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c12f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1852.000000 -1509.000000) translate(0,12)">36117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6fe00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1752.000000 -1456.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2869640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 875.000000 -693.000000) translate(0,17)">7811107</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b7bf40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1333.000000 -852.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3205cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1371.000000 -692.000000) translate(0,12)">06167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3205ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1367.000000 -575.000000) translate(0,12)">06100</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3232250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1324.000000 -759.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205f580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1661.000000 -820.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bf29e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2040.000000 -820.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_229fa70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2341.000000 -822.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_229fcb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2493.000000 -822.000000) translate(0,12)">067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b2f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1326.000000 -820.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b14f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1814.000000 -820.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34458c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1455.000000 -1218.000000) translate(0,15)">1号主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34458c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1455.000000 -1218.000000) translate(0,33)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34458c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1455.000000 -1218.000000) translate(0,51)">35000/10500V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34458c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1455.000000 -1218.000000) translate(0,69)">标准档电流82.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34458c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1455.000000 -1218.000000) translate(0,87)">274.9 Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34458c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1455.000000 -1218.000000) translate(0,105)">Ud%=7.55%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_33c0350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1037.000000 -1620.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_33be560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1037.000000 -1655.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29bb0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1244.000000 -1382.000000) translate(0,15)">10kV1号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29bb0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1244.000000 -1382.000000) translate(0,33)">及消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c09b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1325.000000 -1016.000000) translate(0,12)">068</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c0a050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1323.000000 -1063.000000) translate(0,12)">0686</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b8910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1323.000000 -970.000000) translate(0,12)">0681</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b7cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1291.000000 -1216.000000) translate(0,12)">0030</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_215bb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1434.000000 -510.000000) translate(0,18)">洒利黑线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_215be30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1605.000000 -512.000000) translate(0,18)">水泥厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_33b3e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1759.000000 -518.000000) translate(0,18)">马街子线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_33b40f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1983.000000 -514.000000) translate(0,18)">干海资线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_33b4360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2304.000000 -509.000000) translate(0,18)">桃园线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_33bf760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 746.000000 -655.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_33bf760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 746.000000 -655.000000) translate(0,38)">心变运三班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_33bfa00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 873.000000 -665.500000) translate(0,17)">18787878955</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_33bfa00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 873.000000 -665.500000) translate(0,38)">18787878953</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_33bfa00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 873.000000 -665.500000) translate(0,59)">18787878979</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_33bb6c0" transform="matrix(0.950820 -0.000000 -0.000000 0.827586 1106.557377 -1497.448276) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2d0a620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2628.000000 -512.000000) translate(0,18)">尹代箐线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3249650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2871.000000 -507.000000) translate(0,18)">木章郎Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_327c7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2680.000000 -822.000000) translate(0,12)">069</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_327cb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2678.000000 -876.000000) translate(0,12)">0692</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ce2c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2678.000000 -702.000000) translate(0,12)">0696</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ce2eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2923.000000 -822.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ce30f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2921.000000 -871.000000) translate(0,12)">0712</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ce3330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2921.000000 -697.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea78b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2482.000000 -1262.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ceeb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2480.000000 -1344.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ceedc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2381.000000 -1283.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cef000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2482.000000 -1047.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cef240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2480.000000 -991.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28dcbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2246.000000 -1214.000000) translate(0,15)">2号主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28dcbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2246.000000 -1214.000000) translate(0,33)">SZ11-8000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28dcbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2246.000000 -1214.000000) translate(0,51)">35±3×2.5kV/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28dcbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2246.000000 -1214.000000) translate(0,69)">高压额定电流：131.97A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28dcbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2246.000000 -1214.000000) translate(0,87)">低压额定电流：439.89A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28dcbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2246.000000 -1214.000000) translate(0,105)">联接组方式：YNd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28dcbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2246.000000 -1214.000000) translate(0,123)">冷却方式：0NAN</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28dcbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2246.000000 -1214.000000) translate(0,141)">Ud%=7.4%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_260cf00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2729.000000 -1217.000000) translate(0,18)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_260cf00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2729.000000 -1217.000000) translate(0,40)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_260d3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2792.000000 -982.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2974820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2962.000000 -910.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_29b3ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1749.000000 -1183.000000) translate(0,16)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3005190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1555.000000 -1716.000000) translate(0,18)">A,B相</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2f2cab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2338.000000 -1726.000000) translate(0,18)">A,B相</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3005640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1238.000000 -516.000000) translate(0,18)">10kV1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2583140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2544.000000 -1183.000000) translate(0,16)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_25834d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2085.000000 -1173.000000) translate(0,16)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33c1db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1839.000000 -582.000000) translate(0,16)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29b2cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2191.000000 -1016.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3241730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 874.000000 -1256.000000) translate(0,16)">公共信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2566890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1482.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2566890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1482.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2566890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1482.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2566890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1482.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2566890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1482.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2566890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1482.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2566890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1482.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2566890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1482.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2566890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1482.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_2abf440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 732.000000 -1002.000000) translate(0,15)">定值切区仅可在104调度数据网通道操作;</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_2abf440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 732.000000 -1002.000000) translate(0,33)">101数字租用通道不具备定值切区功能。</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_27bf990" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1643.000000 -1570.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ef7b40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1834.000000 -1530.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15e81f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1835.000000 -1621.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_291f090" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2203.000000 -1489.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2615640" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2040.000000 -1444.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33ab500" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1571.000000 -1220.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26dafc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2417.000000 -1578.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c0a4e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2608.000000 -1538.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33e0e40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2609.000000 -1629.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29acde0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1359.000000 -661.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33aac00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2368.000000 -1219.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2620da0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1402.148148 -545.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="SB_DZ"/>
</svg>