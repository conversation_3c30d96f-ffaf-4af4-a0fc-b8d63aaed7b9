<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-51" aopId="0" id="thSvg" viewBox="3115 -1197 1803 1246">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.890909" x1="29" x2="29" y1="6" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.583333" x1="26" x2="26" y1="4" y2="13"/>
   </symbol>
   <symbol id="lightningRod:shape101">
    <ellipse cx="12" cy="33" rx="11.5" ry="12" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="9" x2="15" y1="13" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="15" x2="15" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="9" x2="15" y1="13" y2="18"/>
    <ellipse cx="12" cy="13" rx="11.5" ry="12.5" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="17" x2="12" y1="36" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="12" y1="33" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="8" x2="12" y1="36" y2="33"/>
   </symbol>
   <symbol id="lightningRod:shape164">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape159">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="9" x2="13" y1="17" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="13" x2="13" y1="13" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="17" x2="13" y1="17" y2="13"/>
    <ellipse cx="12" cy="13" rx="11" ry="12.5" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="8" x2="12" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="12" y1="33" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="16" x2="12" y1="37" y2="33"/>
    <ellipse cx="12" cy="33" rx="11" ry="12" stroke-width="1.22172"/>
   </symbol>
   <symbol id="lightningRod:shape158">
    <polyline fill="none" points="7,8 12,1 17,8 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="7" y1="41" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="7" x2="17" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="17" y1="41" y2="35"/>
    <ellipse cx="12" cy="58" rx="11" ry="12" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="16" x2="12" y1="62" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="12" y1="58" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="8" x2="12" y1="62" y2="58"/>
    <ellipse cx="12" cy="38" rx="11" ry="12.5" stroke-width="1.22172"/>
    <polyline fill="none" points="12,25 12,2 "/>
   </symbol>
   <symbol id="lightningRod:shape160">
    <polyline fill="none" points="1,54 1,8 54,8 54,53 "/>
    <polyline fill="none" points="43,54 44,53 43,53 43,52 43,51 42,50 42,49 41,48 40,48 40,47 39,47 38,46 37,46 36,46 35,46 34,46 33,47 32,47 31,48 30,48 30,49 29,50 29,51 28,52 28,53 28,53 28,54 "/>
    <ellipse cx="27" cy="53" rx="24" ry="23.5" stroke-width="0.5"/>
    <polyline fill="none" points="28,54 28,55 28,56 28,57 27,58 27,59 26,60 26,60 25,61 24,61 23,62 22,62 21,62 20,63 19,62 18,62 17,62 16,61 16,61 15,60 14,60 14,59 13,58 13,57 13,56 12,55 13,54 "/>
    <polyline fill="none" points="28,30 28,1 "/>
   </symbol>
   <symbol id="lightningRod:shape161">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="32" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="32" y2="30"/>
    <circle cx="15" cy="40" r="6" stroke-width="0.431185"/>
    <circle cx="24" cy="37" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="32" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="7" cy="37" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="39" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="39" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="43" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="41" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="43" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="40" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="30" y2="28"/>
    <rect height="29" stroke-width="2" width="15" x="56" y="41"/>
    <polyline fill="none" points="63,52 63,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="84" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="84" y2="94"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="63" x2="63" y1="42" y2="17"/>
    <rect height="27" stroke-width="0.416667" width="14" x="8" y="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="62" x2="15" y1="84" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="63" x2="17" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="33" x2="45" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="39" x2="39" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="43" x2="35" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="26" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00003" x1="64" x2="61" y1="50" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00003" x1="63" x2="66" y1="50" y2="57"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="8" x2="8" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="transformer2:shape15_0">
    <ellipse cx="25" cy="29" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape15_1">
    <circle cx="25" cy="61" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="67" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="59" y2="67"/>
   </symbol>
   <symbol id="transformer2:shape3_0">
    <ellipse cx="13" cy="34" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape3_1">
    <circle cx="13" cy="18" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="32" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(0.512795 -0.000000 0.000000 -1.035714 2.846957 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1256" width="1813" x="3110" y="-1202"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3884.000000 1050.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3873.000000 1035.000000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3896.000000 1020.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3900.000000 1005.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3736.000000 704.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3725.000000 689.000000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3748.000000 674.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3752.000000 659.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4367.000000 253.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4356.000000 238.000000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4379.000000 223.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4383.000000 208.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3915.000000 248.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3904.000000 233.000000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3927.000000 218.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3931.000000 203.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3681.000000 769.000000) translate(0,12)">Uab(KV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4631.000000 704.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4620.000000 688.250000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.000000 671.500000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4649.000000 639.000000) translate(0,12)">Uab:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.000000 655.750000) translate(0,12)">Ic(A):</text>
   <metadata/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="22" stroke="rgb(255,255,255)" stroke-width="1" width="101" x="3913" y="-170"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="22" stroke="rgb(255,255,255)" stroke-width="1" width="101" x="4361" y="-170"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1076"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-1196"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-596"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-38KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3867.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11293" ObjectName="SW-CX_JLD.CX_JLD_4011SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42808">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4351.000000 -670.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7128" ObjectName="SW-CX_JLD.CX_JLD_09017SW"/>
     <cge:Meas_Ref ObjectId="42808"/>
    <cge:TPSR_Ref TObjectID="7128"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4769.000000 -360.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11302" ObjectName="SW-CX_JLD.CX_JLD_4021SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42796">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3915.000000 -683.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7123" ObjectName="SW-CX_JLD.CX_JLD_00117SW"/>
     <cge:Meas_Ref ObjectId="42796"/>
    <cge:TPSR_Ref TObjectID="7123"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42711">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3888.000000 -828.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7102" ObjectName="SW-CX_JLD.CX_JLD_08117SW"/>
     <cge:Meas_Ref ObjectId="42711"/>
    <cge:TPSR_Ref TObjectID="7102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3841.000000 -1026.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11297" ObjectName="SW-CX_JLD.CX_JLD_0816SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-38KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3839.000000 -213.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11294" ObjectName="SW-CX_JLD.CX_JLD_4111SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42728">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4099.000000 -830.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7105" ObjectName="SW-CX_JLD.CX_JLD_08217SW"/>
     <cge:Meas_Ref ObjectId="42728"/>
    <cge:TPSR_Ref TObjectID="7105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4052.000000 -1029.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11298" ObjectName="SW-CX_JLD.CX_JLD_0826SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42774">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4337.000000 -833.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7118" ObjectName="SW-CX_JLD.CX_JLD_08317SW"/>
     <cge:Meas_Ref ObjectId="42774"/>
    <cge:TPSR_Ref TObjectID="7118"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4290.000000 -1029.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11299" ObjectName="SW-CX_JLD.CX_JLD_0836SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42743">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4593.000000 -834.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7110" ObjectName="SW-CX_JLD.CX_JLD_08417SW"/>
     <cge:Meas_Ref ObjectId="42743"/>
    <cge:TPSR_Ref TObjectID="7110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4546.000000 -1030.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11300" ObjectName="SW-CX_JLD.CX_JLD_0846SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42758">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4816.000000 -829.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7114" ObjectName="SW-CX_JLD.CX_JLD_08517SW"/>
     <cge:Meas_Ref ObjectId="42758"/>
    <cge:TPSR_Ref TObjectID="7114"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4769.000000 -1029.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11301" ObjectName="SW-CX_JLD.CX_JLD_0856SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-38KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4288.000000 -211.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11295" ObjectName="SW-CX_JLD.CX_JLD_4121SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42806">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4816.000000 -667.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7126" ObjectName="SW-CX_JLD.CX_JLD_00217SW"/>
     <cge:Meas_Ref ObjectId="42806"/>
    <cge:TPSR_Ref TObjectID="7126"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42712">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3841.000000 -913.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7103" ObjectName="SW-CX_JLD.CX_JLD_0812SW"/>
     <cge:Meas_Ref ObjectId="42712"/>
    <cge:TPSR_Ref TObjectID="7103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42710">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3841.000000 -782.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7101" ObjectName="SW-CX_JLD.CX_JLD_0811SW"/>
     <cge:Meas_Ref ObjectId="42710"/>
    <cge:TPSR_Ref TObjectID="7101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42729">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4052.000000 -915.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7106" ObjectName="SW-CX_JLD.CX_JLD_0822SW"/>
     <cge:Meas_Ref ObjectId="42729"/>
    <cge:TPSR_Ref TObjectID="7106"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42730">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4052.000000 -784.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7107" ObjectName="SW-CX_JLD.CX_JLD_0821SW"/>
     <cge:Meas_Ref ObjectId="42730"/>
    <cge:TPSR_Ref TObjectID="7107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42775">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4290.000000 -914.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7119" ObjectName="SW-CX_JLD.CX_JLD_0832SW"/>
     <cge:Meas_Ref ObjectId="42775"/>
    <cge:TPSR_Ref TObjectID="7119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42773">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4290.000000 -787.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7117" ObjectName="SW-CX_JLD.CX_JLD_0831SW"/>
     <cge:Meas_Ref ObjectId="42773"/>
    <cge:TPSR_Ref TObjectID="7117"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42744">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4546.000000 -919.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7111" ObjectName="SW-CX_JLD.CX_JLD_0842SW"/>
     <cge:Meas_Ref ObjectId="42744"/>
    <cge:TPSR_Ref TObjectID="7111"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42742">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4546.000000 -788.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7109" ObjectName="SW-CX_JLD.CX_JLD_0841SW"/>
     <cge:Meas_Ref ObjectId="42742"/>
    <cge:TPSR_Ref TObjectID="7109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42759">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4769.000000 -914.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7115" ObjectName="SW-CX_JLD.CX_JLD_0852SW"/>
     <cge:Meas_Ref ObjectId="42759"/>
    <cge:TPSR_Ref TObjectID="7115"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42757">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4769.000000 -783.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7113" ObjectName="SW-CX_JLD.CX_JLD_0851SW"/>
     <cge:Meas_Ref ObjectId="42757"/>
    <cge:TPSR_Ref TObjectID="7113"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42805">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4769.000000 -694.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7125" ObjectName="SW-CX_JLD.CX_JLD_0021SW"/>
     <cge:Meas_Ref ObjectId="42805"/>
    <cge:TPSR_Ref TObjectID="7125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42795">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3868.000000 -703.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7122" ObjectName="SW-CX_JLD.CX_JLD_0011SW"/>
     <cge:Meas_Ref ObjectId="42795"/>
    <cge:TPSR_Ref TObjectID="7122"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42807">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4304.000000 -706.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7127" ObjectName="SW-CX_JLD.CX_JLD_0901SW"/>
     <cge:Meas_Ref ObjectId="42807"/>
    <cge:TPSR_Ref TObjectID="7127"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42797">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3868.000000 -574.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7124" ObjectName="SW-CX_JLD.CX_JLD_0016SW"/>
     <cge:Meas_Ref ObjectId="42797"/>
    <cge:TPSR_Ref TObjectID="7124"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-1116 3856,-1149 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3856,-1116 3856,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1115 4067,-1148 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4067,-1115 4067,-1148 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-1120 4305,-1153 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4305,-1120 4305,-1153 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-1118 4561,-1151 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4561,-1118 4561,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-1117 4784,-1150 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4784,-1117 4784,-1150 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2812530">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4412.000000 -687.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28267b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3976.000000 -700.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28319a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4160.000000 -847.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28406d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4654.000000 -851.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28489c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.000000 -846.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2854070">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.000000 -684.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2857b50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4839.000000 -563.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2878f50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4398.000000 -850.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_299a300">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3949.000000 -845.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_27d27d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-783 3856,-804 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10971@0" ObjectIDZND0="7101@0" Pin0InfoVect0LinkObjId="SW-42710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28a3a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-783 3856,-804 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_281ea10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4320,-696 4342,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2996c90@0" ObjectIDND1="7127@x" ObjectIDZND0="7128@0" Pin0InfoVect0LinkObjId="SW-42808_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2996c90_0" Pin1InfoVect1LinkObjId="SW-42807_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4320,-696 4342,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2820f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4378,-696 4417,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7128@1" ObjectIDZND0="g_2812530@0" Pin0InfoVect0LinkObjId="g_2812530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42808_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4378,-696 4417,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28227b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-783 4784,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10971@0" ObjectIDZND0="7125@1" Pin0InfoVect0LinkObjId="SW-42805_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28a3a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-783 4784,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28263d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3884,-709 3906,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7122@x" ObjectIDND1="7121@x" ObjectIDZND0="7123@0" Pin0InfoVect0LinkObjId="SW-42796_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42795_0" Pin1InfoVect1LinkObjId="SW-42794_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3884,-709 3906,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28265c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-709 3981,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7123@1" ObjectIDZND0="g_28267b0@0" Pin0InfoVect0LinkObjId="g_28267b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42796_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-709 3981,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2826f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3883,-709 3883,-725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7123@x" ObjectIDND1="7121@x" ObjectIDZND0="7122@0" Pin0InfoVect0LinkObjId="SW-42795_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42796_0" Pin1InfoVect1LinkObjId="SW-42794_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3883,-709 3883,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2827150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3883,-690 3883,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="7121@1" ObjectIDZND0="7123@x" ObjectIDZND1="7122@x" Pin0InfoVect0LinkObjId="SW-42796_0" Pin0InfoVect1LinkObjId="SW-42795_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42794_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3883,-690 3883,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2827340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3883,-644 3939,-644 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="7121@x" ObjectIDND1="7124@x" ObjectIDZND0="g_299bb00@0" Pin0InfoVect0LinkObjId="g_299bb00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42794_0" Pin1InfoVect1LinkObjId="SW-42797_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3883,-644 3939,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2827530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3883,-644 3883,-663 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_299bb00@0" ObjectIDND1="7124@x" ObjectIDZND0="7121@0" Pin0InfoVect0LinkObjId="SW-42794_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_299bb00_0" Pin1InfoVect1LinkObjId="SW-42797_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3883,-644 3883,-663 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2829510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-854 3879,-854 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7101@x" ObjectIDND1="7100@x" ObjectIDZND0="7102@0" Pin0InfoVect0LinkObjId="SW-42711_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42710_0" Pin1InfoVect1LinkObjId="SW-42709_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-854 3879,-854 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2829700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3915,-854 3954,-854 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7102@1" ObjectIDZND0="g_299a300@0" Pin0InfoVect0LinkObjId="g_299a300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42711_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3915,-854 3954,-854 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28298f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-840 3856,-854 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="7101@1" ObjectIDZND0="7102@x" ObjectIDZND1="7100@x" Pin0InfoVect0LinkObjId="SW-42711_0" Pin0InfoVect1LinkObjId="SW-42709_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42710_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-840 3856,-854 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2829ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-854 3856,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7102@x" ObjectIDND1="7101@x" ObjectIDZND0="7100@0" Pin0InfoVect0LinkObjId="SW-42709_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42711_0" Pin1InfoVect1LinkObjId="SW-42710_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-854 3856,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2829cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-930 3913,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2878230@0" Pin0InfoVect0LinkObjId="g_2878230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-930 3913,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_282d6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-288 3854,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10972@0" ObjectIDZND0="11294@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_284cf30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-288 3854,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_282d890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-235 3854,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11294@0" ObjectIDZND0="10872@1" Pin0InfoVect0LinkObjId="SW-58143_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-235 3854,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_282da80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-143 3921,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="10872@x" ObjectIDND1="g_2994b10@0" ObjectIDZND0="g_298d0b0@0" ObjectIDZND1="g_298ddb0@0" Pin0InfoVect0LinkObjId="g_298d0b0_0" Pin0InfoVect1LinkObjId="g_298ddb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58143_0" Pin1InfoVect1LinkObjId="g_2994b10_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-143 3921,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_282dc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-783 4067,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10971@0" ObjectIDZND0="7107@0" Pin0InfoVect0LinkObjId="SW-42730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28a3a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-783 4067,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2831560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-856 4090,-856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7107@x" ObjectIDND1="7104@x" ObjectIDZND0="7105@0" Pin0InfoVect0LinkObjId="SW-42728_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42730_0" Pin1InfoVect1LinkObjId="SW-42727_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-856 4090,-856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2831780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4126,-856 4165,-856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7105@1" ObjectIDZND0="g_28319a0@0" Pin0InfoVect0LinkObjId="g_28319a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42728_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4126,-856 4165,-856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28322d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-842 4067,-856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7107@1" ObjectIDZND0="7104@x" ObjectIDZND1="7105@x" Pin0InfoVect0LinkObjId="SW-42727_0" Pin0InfoVect1LinkObjId="SW-42728_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42730_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-842 4067,-856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28324f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-856 4067,-874 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7107@x" ObjectIDND1="7105@x" ObjectIDZND0="7104@0" Pin0InfoVect0LinkObjId="SW-42727_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42730_0" Pin1InfoVect1LinkObjId="SW-42728_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-856 4067,-874 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2832710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-932 4124,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_299ad90@0" Pin0InfoVect0LinkObjId="g_299ad90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-932 4124,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2834bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-783 4305,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10971@0" ObjectIDZND0="7117@0" Pin0InfoVect0LinkObjId="SW-42773_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28a3a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-783 4305,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2839190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-859 4328,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7117@x" ObjectIDND1="7116@x" ObjectIDZND0="7118@0" Pin0InfoVect0LinkObjId="SW-42774_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42773_0" Pin1InfoVect1LinkObjId="SW-42772_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-859 4328,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28393b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-845 4305,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7117@1" ObjectIDZND0="7116@x" ObjectIDZND1="7118@x" Pin0InfoVect0LinkObjId="SW-42772_0" Pin0InfoVect1LinkObjId="SW-42774_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42773_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-845 4305,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28395d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-859 4305,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7117@x" ObjectIDND1="7118@x" ObjectIDZND0="7116@0" Pin0InfoVect0LinkObjId="SW-42772_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42773_0" Pin1InfoVect1LinkObjId="SW-42774_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-859 4305,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28397f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-932 4362,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_28797f0@0" Pin0InfoVect0LinkObjId="g_28797f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-932 4362,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_283bc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-783 4561,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10971@0" ObjectIDZND0="7109@0" Pin0InfoVect0LinkObjId="SW-42742_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28a3a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-783 4561,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2840270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-860 4584,-860 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7109@x" ObjectIDND1="7108@x" ObjectIDZND0="7110@0" Pin0InfoVect0LinkObjId="SW-42743_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42742_0" Pin1InfoVect1LinkObjId="SW-42741_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-860 4584,-860 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2840490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-860 4659,-860 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7110@1" ObjectIDZND0="g_28406d0@0" Pin0InfoVect0LinkObjId="g_28406d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42743_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-860 4659,-860 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2841100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-846 4561,-860 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7109@1" ObjectIDZND0="7108@x" ObjectIDZND1="7110@x" Pin0InfoVect0LinkObjId="SW-42741_0" Pin0InfoVect1LinkObjId="SW-42743_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42742_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-846 4561,-860 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2841360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-860 4561,-878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7109@x" ObjectIDND1="7110@x" ObjectIDZND0="7108@0" Pin0InfoVect0LinkObjId="SW-42741_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42742_0" Pin1InfoVect1LinkObjId="SW-42743_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-860 4561,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28415c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-937 4618,-937 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_287a480@0" Pin0InfoVect0LinkObjId="g_287a480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-937 4618,-937 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2843d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-783 4784,-804 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10971@0" ObjectIDZND0="7113@0" Pin0InfoVect0LinkObjId="SW-42757_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28a3a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-783 4784,-804 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2848500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-855 4807,-855 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="7113@x" ObjectIDND1="7112@x" ObjectIDZND0="7114@0" Pin0InfoVect0LinkObjId="SW-42758_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42757_0" Pin1InfoVect1LinkObjId="SW-42756_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-855 4807,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2848760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4843,-855 4882,-855 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7114@1" ObjectIDZND0="g_28489c0@0" Pin0InfoVect0LinkObjId="g_28489c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42758_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4843,-855 4882,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28493f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-841 4784,-855 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7113@1" ObjectIDZND0="7112@x" ObjectIDZND1="7114@x" Pin0InfoVect0LinkObjId="SW-42756_0" Pin0InfoVect1LinkObjId="SW-42758_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42757_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-841 4784,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2849650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-855 4784,-873 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7113@x" ObjectIDND1="7114@x" ObjectIDZND0="7112@0" Pin0InfoVect0LinkObjId="SW-42756_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42757_0" Pin1InfoVect1LinkObjId="SW-42758_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-855 4784,-873 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28498b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-930 4841,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="7115@x" ObjectIDND1="7112@x" ObjectIDZND0="g_287b170@0" Pin0InfoVect0LinkObjId="g_287b170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42759_0" Pin1InfoVect1LinkObjId="SW-42756_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-930 4841,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_284cf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-261 4159,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_299ce40@0" ObjectIDZND0="10972@0" Pin0InfoVect0LinkObjId="g_28a3ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_299ce40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-261 4159,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_284ef50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,-142 4303,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="10873@x" ObjectIDND1="g_298f910@0" ObjectIDND2="g_29903f0@0" ObjectIDZND0="g_2995bd0@1" Pin0InfoVect0LinkObjId="g_2995bd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-58151_0" Pin1InfoVect1LinkObjId="g_298f910_0" Pin1InfoVect2LinkObjId="g_29903f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4303,-142 4303,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_28519e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,-186 4303,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="10873@0" ObjectIDZND0="g_298f910@0" ObjectIDZND1="g_29903f0@0" ObjectIDZND2="g_2995bd0@0" Pin0InfoVect0LinkObjId="g_298f910_0" Pin0InfoVect1LinkObjId="g_29903f0_0" Pin0InfoVect2LinkObjId="g_2995bd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58151_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4303,-186 4303,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_2851c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,-142 4370,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="10873@x" ObjectIDND1="g_2995bd0@0" ObjectIDZND0="g_298f910@0" ObjectIDZND1="g_29903f0@0" Pin0InfoVect0LinkObjId="g_298f910_0" Pin0InfoVect1LinkObjId="g_29903f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58151_0" Pin1InfoVect1LinkObjId="g_2995bd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4303,-142 4370,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_2852e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,-288 4303,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10972@0" ObjectIDZND0="11295@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_284cf30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4303,-288 4303,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_2853080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,-233 4303,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11295@0" ObjectIDZND0="10873@1" Pin0InfoVect0LinkObjId="SW-58151_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4303,-233 4303,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2853c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4319,-696 4319,-663 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="7128@x" ObjectIDND1="7127@x" ObjectIDZND0="g_2996c90@0" Pin0InfoVect0LinkObjId="g_2996c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42808_0" Pin1InfoVect1LinkObjId="SW-42807_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4319,-696 4319,-663 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2853e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-693 4806,-693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="7125@x" ObjectIDND1="g_28898e0@0" ObjectIDZND0="7126@0" Pin0InfoVect0LinkObjId="SW-42806_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42805_0" Pin1InfoVect1LinkObjId="g_28898e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-693 4806,-693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2854950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4843,-693 4882,-693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7126@1" ObjectIDZND0="g_2854070@0" Pin0InfoVect0LinkObjId="g_2854070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42806_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4843,-693 4882,-693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2854b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-693 4784,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="7126@x" ObjectIDND1="g_28898e0@0" ObjectIDZND0="7125@0" Pin0InfoVect0LinkObjId="SW-42805_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-42806_0" Pin1InfoVect1LinkObjId="g_28898e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-693 4784,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2857470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-337 4784,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10972@0" ObjectIDZND0="11302@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_284cf30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-337 4784,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28578f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4787,-572 4844,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDZND0="g_2857b50@0" Pin0InfoVect0LinkObjId="g_2857b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2812530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4787,-572 4844,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2878cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4364,-859 4403,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7118@1" ObjectIDZND0="g_2878f50@0" Pin0InfoVect0LinkObjId="g_2878f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42774_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4364,-859 4403,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_287be60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-1104 3916,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDND1="11297@x" ObjectIDZND0="g_287d0e0@0" Pin0InfoVect0LinkObjId="g_287d0e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2812530_1" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-1104 3916,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_287c0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-1104 3856,-1116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="powerLine" ObjectIDND0="11297@x" ObjectIDND1="g_287d0e0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2812530_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_287d0e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-1104 3856,-1116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_287c560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1103 4127,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="lightningRod" ObjectIDND0="11298@x" ObjectIDND1="0@1" ObjectIDZND0="g_287ddd0@0" Pin0InfoVect0LinkObjId="g_287ddd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2812530_1" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1103 4127,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_287c7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1103 4067,-1115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="powerLine" ObjectIDND0="11298@x" ObjectIDND1="g_287ddd0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2812530_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_287ddd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1103 4067,-1115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2883700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4319,-728 4319,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="7127@0" ObjectIDZND0="g_2996c90@0" ObjectIDZND1="7128@x" Pin0InfoVect0LinkObjId="g_2996c90_0" Pin0InfoVect1LinkObjId="SW-42808_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42807_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4319,-728 4319,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2888610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3883,-632 3883,-644 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="7124@1" ObjectIDZND0="g_299bb00@0" ObjectIDZND1="7121@x" Pin0InfoVect0LinkObjId="g_299bb00_0" Pin0InfoVect1LinkObjId="SW-42794_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42797_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3883,-632 3883,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2889eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-683 4784,-693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_28898e0@1" ObjectIDZND0="7126@x" ObjectIDZND1="7125@x" Pin0InfoVect0LinkObjId="SW-42806_0" Pin0InfoVect1LinkObjId="SW-42805_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28898e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-683 4784,-693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_288a110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-142 4370,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="10873@x" ObjectIDND1="g_2995bd0@0" ObjectIDND2="g_29903f0@0" ObjectIDZND0="g_298f910@1" Pin0InfoVect0LinkObjId="g_298f910_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-58151_0" Pin1InfoVect1LinkObjId="g_2995bd0_0" Pin1InfoVect2LinkObjId="g_29903f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-142 4370,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_288a370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-118 4432,-142 4370,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_29903f0@1" ObjectIDZND0="10873@x" ObjectIDZND1="g_2995bd0@0" ObjectIDZND2="g_298f910@0" Pin0InfoVect0LinkObjId="SW-58151_0" Pin0InfoVect1LinkObjId="g_2995bd0_0" Pin0InfoVect2LinkObjId="g_298f910_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29903f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-118 4432,-142 4370,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_288adb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-288 4078,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="10972@0" ObjectIDZND0="g_288a5d0@1" Pin0InfoVect0LinkObjId="g_288a5d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_284cf30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-288 4078,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_288b010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-219 4078,-182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_288a5d0@0" ObjectIDZND0="g_284c070@0" Pin0InfoVect0LinkObjId="g_284c070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_288a5d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-219 4078,-182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_288c370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-628 4784,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_28898e0@0" Pin0InfoVect0LinkObjId="g_28898e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2812530_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-628 4784,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_288ee00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3882,-426 3882,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="7129@0" Pin0InfoVect0LinkObjId="g_288f1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3882,-426 3882,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_288eff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3883,-596 3883,-583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="7124@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42797_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3883,-596 3883,-583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_288f1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3883,-546 3883,-525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="7129@1" Pin0InfoVect0LinkObjId="g_288ee00_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3883,-546 3883,-525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2894120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-971 3856,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7103@1" ObjectIDZND0="g_28933d0@1" Pin0InfoVect0LinkObjId="g_28933d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42712_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-971 3856,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2894380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-973 4067,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7106@1" ObjectIDZND0="g_2891930@1" Pin0InfoVect0LinkObjId="g_2891930_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42729_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-973 4067,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28945e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-972 4305,-979 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7119@1" ObjectIDZND0="g_2892680@1" Pin0InfoVect0LinkObjId="g_2892680_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42775_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-972 4305,-979 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2894840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-977 4561,-985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7111@1" ObjectIDZND0="g_288fe90@1" Pin0InfoVect0LinkObjId="g_288fe90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42744_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-977 4561,-985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2894aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-972 4784,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7115@1" ObjectIDZND0="g_288f3f0@1" Pin0InfoVect0LinkObjId="g_288f3f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42759_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-972 4784,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2894d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-418 4784,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="11302@1" ObjectIDZND0="g_2890be0@1" Pin0InfoVect0LinkObjId="g_2890be0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-418 4784,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2894f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-510 4784,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2890be0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2812530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2890be0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-510 4784,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a15d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-1033 3856,-1048 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_28933d0@0" ObjectIDZND0="11297@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28933d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-1033 3856,-1048 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a17c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-1084 3856,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="lightningRod" ObjectIDND0="11297@1" ObjectIDZND0="0@1" ObjectIDZND1="g_287d0e0@0" Pin0InfoVect0LinkObjId="g_2812530_1" Pin0InfoVect1LinkObjId="g_287d0e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-1084 3856,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a19b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1035 4067,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2891930@0" ObjectIDZND0="11298@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2891930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1035 4067,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a1be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1087 4067,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="lightningRod" ObjectIDND0="11298@1" ObjectIDZND0="0@1" ObjectIDZND1="g_287ddd0@0" Pin0InfoVect0LinkObjId="g_2812530_1" Pin0InfoVect1LinkObjId="g_287ddd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1087 4067,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a1e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-1120 4305,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="11299@x" ObjectIDZND1="g_287eac0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_287eac0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2812530_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-1120 4305,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a2040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-1108 4365,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDND1="11299@x" ObjectIDZND0="g_287eac0@0" Pin0InfoVect0LinkObjId="g_287eac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2812530_1" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-1108 4365,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a2270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-1032 4305,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2892680@0" ObjectIDZND0="11299@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2892680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-1032 4305,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a24a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-1087 4305,-1109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="lightningRod" ObjectIDND0="11299@1" ObjectIDZND0="0@1" ObjectIDZND1="g_287eac0@0" Pin0InfoVect0LinkObjId="g_2812530_1" Pin0InfoVect1LinkObjId="g_287eac0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-1087 4305,-1109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a2700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-1118 4561,-1106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="11300@x" ObjectIDZND1="g_28804a0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_28804a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2812530_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-1118 4561,-1106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a2960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-1106 4621,-1106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDND1="11300@x" ObjectIDZND0="g_28804a0@0" Pin0InfoVect0LinkObjId="g_28804a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2812530_1" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-1106 4621,-1106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a2bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-1038 4561,-1052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_288fe90@0" ObjectIDZND0="11300@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_288fe90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-1038 4561,-1052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a2e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-1088 4561,-1106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="lightningRod" ObjectIDND0="11300@1" ObjectIDZND0="0@1" ObjectIDZND1="g_28804a0@0" Pin0InfoVect0LinkObjId="g_2812530_1" Pin0InfoVect1LinkObjId="g_28804a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-1088 4561,-1106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a3080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-1117 4784,-1105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="11301@x" ObjectIDZND1="g_287f7b0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_287f7b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2812530_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-1117 4784,-1105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a32e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-1105 4844,-1105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDND1="11301@x" ObjectIDZND0="g_287f7b0@0" Pin0InfoVect0LinkObjId="g_287f7b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2812530_1" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-1105 4844,-1105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a3540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-1033 4784,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_288f3f0@0" ObjectIDZND0="11301@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_288f3f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-1033 4784,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a37a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-1087 4784,-1105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="lightningRod" ObjectIDND0="11301@1" ObjectIDZND0="0@1" ObjectIDZND1="g_287f7b0@0" Pin0InfoVect0LinkObjId="g_2812530_1" Pin0InfoVect1LinkObjId="g_287f7b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-1087 4784,-1105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a3a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4319,-764 4319,-783 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7127@1" ObjectIDZND0="10971@0" Pin0InfoVect0LinkObjId="g_28a3c60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42807_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4319,-764 4319,-783 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a3c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3883,-761 3883,-783 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7122@1" ObjectIDZND0="10971@0" Pin0InfoVect0LinkObjId="g_28a3a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42795_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3883,-761 3883,-783 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_28a3ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3882,-316 3882,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11293@0" ObjectIDZND0="10972@0" Pin0InfoVect0LinkObjId="g_284cf30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3882,-316 3882,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_298c260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-187 3854,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="10872@0" ObjectIDZND0="g_298d0b0@0" ObjectIDZND1="g_298ddb0@0" ObjectIDZND2="g_2994b10@0" Pin0InfoVect0LinkObjId="g_298d0b0_0" Pin0InfoVect1LinkObjId="g_298ddb0_0" Pin0InfoVect2LinkObjId="g_2994b10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58143_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-187 3854,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_298cc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-92 3854,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="g_2994b10@1" ObjectIDZND0="g_298d0b0@0" ObjectIDZND1="g_298ddb0@0" ObjectIDZND2="10872@x" Pin0InfoVect0LinkObjId="g_298d0b0_0" Pin0InfoVect1LinkObjId="g_298ddb0_0" Pin0InfoVect2LinkObjId="SW-58143_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2994b10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-92 3854,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_298ce50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-143 3983,-143 3983,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="10872@x" ObjectIDND1="g_2994b10@0" ObjectIDND2="g_298d0b0@0" ObjectIDZND0="g_298ddb0@1" Pin0InfoVect0LinkObjId="g_298ddb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-58143_0" Pin1InfoVect1LinkObjId="g_2994b10_0" Pin1InfoVect2LinkObjId="g_298d0b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-143 3983,-143 3983,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_298d8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-143 3921,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="10872@x" ObjectIDND1="g_2994b10@0" ObjectIDND2="g_298ddb0@0" ObjectIDZND0="g_298d0b0@1" Pin0InfoVect0LinkObjId="g_298d0b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-58143_0" Pin1InfoVect1LinkObjId="g_2994b10_0" Pin1InfoVect2LinkObjId="g_298ddb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-143 3921,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_298db50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-96 3921,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_298d0b0@0" ObjectIDZND0="g_2991e10@0" Pin0InfoVect0LinkObjId="g_2991e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_298d0b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-96 3921,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_298f6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3983,-92 3983,-74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_298ddb0@0" ObjectIDZND0="g_298e630@0" Pin0InfoVect0LinkObjId="g_298e630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_298ddb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3983,-92 3983,-74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_2990190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-68 4370,-53 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_298f910@0" ObjectIDZND0="g_2993490@0" Pin0InfoVect0LinkObjId="g_2993490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_298f910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-68 4370,-53 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_2991bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-87 4432,-69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_29903f0@0" ObjectIDZND0="g_2990c70@0" Pin0InfoVect0LinkObjId="g_2990c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29903f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-87 4432,-69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a05b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-936 4784,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="7115@0" ObjectIDZND0="g_287b170@0" ObjectIDZND1="7112@x" Pin0InfoVect0LinkObjId="g_287b170_0" Pin0InfoVect1LinkObjId="SW-42756_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42759_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-936 4784,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a0810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-900 4784,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="7112@1" ObjectIDZND0="g_287b170@0" ObjectIDZND1="7115@x" Pin0InfoVect0LinkObjId="g_287b170_0" Pin0InfoVect1LinkObjId="SW-42759_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42756_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-900 4784,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a4d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-899 3856,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7100@1" ObjectIDZND0="7103@0" Pin0InfoVect0LinkObjId="SW-42712_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42709_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-899 3856,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a4fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-901 4067,-937 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7104@1" ObjectIDZND0="7106@0" Pin0InfoVect0LinkObjId="SW-42729_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42727_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-901 4067,-937 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a5210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-904 4305,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7116@1" ObjectIDZND0="7119@0" Pin0InfoVect0LinkObjId="SW-42775_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42772_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-904 4305,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29a5470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-905 4561,-941 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7108@1" ObjectIDZND0="7111@0" Pin0InfoVect0LinkObjId="SW-42744_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-42741_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-905 4561,-941 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_29a6ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3791,-364 3791,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_29a6150@0" ObjectIDZND0="11293@x" ObjectIDZND1="g_29a8e30@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_29a8e30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29a6150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3791,-364 3791,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_29a79b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3791,-373 3882,-373 3882,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_29a6150@0" ObjectIDND1="g_29a8e30@0" ObjectIDZND0="11293@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29a6150_0" Pin1InfoVect1LinkObjId="g_29a8e30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3791,-373 3882,-373 3882,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_29a9800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-339 3714,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_29a8e30@1" Pin0InfoVect0LinkObjId="g_29a8e30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2812530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-339 3714,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_29a9a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-366 3714,-373 3791,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_29a8e30@0" ObjectIDZND0="g_29a6150@0" ObjectIDZND1="11293@x" Pin0InfoVect0LinkObjId="g_29a6150_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29a8e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-366 3714,-373 3791,-373 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer"/><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" id="DYN-37320" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3405.000000 -1084.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5902" ObjectName="DYN-CX_JLD"/>
     <cge:Meas_Ref ObjectId="37320"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3753.000000 -4.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3753.000000 -4.000000) translate(0,27)">320kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4350.000000 -583.000000) translate(0,12)">母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3834.000000 -1175.000000) translate(0,12)">吕九线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3920.000000 -164.000000) translate(0,12)">微机综合保护</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3799.000000 -208.000000) translate(0,12)">同期点</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4832.000000 -640.000000) translate(0,12)">厂用变压器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4045.000000 -1175.000000) translate(0,12)">干田线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4285.000000 -1175.000000) translate(0,12)">备用线1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4540.000000 -1175.000000) translate(0,12)">近区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4761.000000 -1175.000000) translate(0,12)">辛庄线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4220.000000 -5.000000) translate(0,12)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4220.000000 -5.000000) translate(0,27)">320kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4247.000000 -207.000000) translate(0,12)">同期点</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4044.000000 -131.000000) translate(0,12)">JSJ-04./0.1kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4120.000000 -199.000000) translate(0,12)">Y1.5W-0.5G</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4745.000000 -321.000000) translate(0,12)">0.4kV厂用系统</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4368.000000 -165.000000) translate(0,12)">微机综合保护</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3684.000000 -804.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3865.000000 -893.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3863.000000 -829.000000) translate(0,12)">0811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3863.000000 -960.000000) translate(0,12)">0812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3877.000000 -880.000000) translate(0,12)">08117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4076.000000 -895.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4074.000000 -962.000000) translate(0,12)">0822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4074.000000 -831.000000) translate(0,12)">0821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4088.000000 -882.000000) translate(0,12)">08217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4314.000000 -898.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4312.000000 -959.000000) translate(0,12)">0832</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4312.000000 -834.000000) translate(0,12)">0831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4326.000000 -885.000000) translate(0,12)">08317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4570.000000 -899.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4568.000000 -966.000000) translate(0,12)">0842</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4568.000000 -835.000000) translate(0,12)">0841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4582.000000 -886.000000) translate(0,12)">08417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4793.000000 -894.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4791.000000 -961.000000) translate(0,12)">0852</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4791.000000 -830.000000) translate(0,12)">0851</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4805.000000 -881.000000) translate(0,12)">08517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3892.000000 -684.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3890.000000 -750.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3904.000000 -735.000000) translate(0,12)">00117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3890.000000 -621.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3909.000000 -492.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4327.000000 -759.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4340.000000 -722.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4792.000000 -744.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4811.000000 -718.000000) translate(0,12)">00217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3136.000000 -586.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3136.000000 -586.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3136.000000 -586.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3136.000000 -586.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3136.000000 -586.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3136.000000 -586.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3136.000000 -586.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3136.000000 -586.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3136.000000 -586.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3136.000000 -586.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3136.000000 -586.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3136.000000 -586.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3136.000000 -586.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3136.000000 -586.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3136.000000 -586.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3136.000000 -586.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3136.000000 -586.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3136.000000 -586.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -1024.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -1024.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -1024.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -1024.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -1024.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -1024.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -1024.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3260.500000 -1166.500000) translate(0,16)">九龙甸水电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3865.000000 -209.000000) translate(0,12)">411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4317.000000 -208.000000) translate(0,12)">412</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3725.000000 -308.000000) translate(0,12)">0.4kV发电机母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3898.000000 -345.000000) translate(0,12)">4011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3861.000000 -260.000000) translate(0,12)">4111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4312.000000 -257.000000) translate(0,12)">4121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3863.000000 -1073.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4074.000000 -1076.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4312.000000 -1076.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4568.000000 -1077.000000) translate(0,12)">0846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4791.000000 -1076.000000) translate(0,12)">0856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4791.000000 -407.000000) translate(0,12)">4021</text>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4871" x2="4871" y1="-918" y2="-909"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4874" x2="4874" y1="-916" y2="-910"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4877" x2="4877" y1="-915" y2="-910"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4871" x2="4846" y1="-912" y2="-912"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4835" x2="4844" y1="-917" y2="-907"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4834" x2="4844" y1="-906" y2="-916"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4832" x2="4810" y1="-910" y2="-910"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4809" x2="4809" y1="-913" y2="-906"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4805" x2="4805" y1="-913" y2="-906"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4805" x2="4785" y1="-909" y2="-909"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4647" x2="4647" y1="-923" y2="-914"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4650" x2="4650" y1="-921" y2="-915"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4653" x2="4653" y1="-920" y2="-915"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4647" x2="4622" y1="-917" y2="-917"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4611" x2="4620" y1="-922" y2="-912"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4610" x2="4620" y1="-911" y2="-921"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4608" x2="4586" y1="-915" y2="-915"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4585" x2="4585" y1="-918" y2="-911"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4581" x2="4581" y1="-918" y2="-911"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4581" x2="4561" y1="-914" y2="-914"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4391" x2="4391" y1="-921" y2="-912"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4394" x2="4394" y1="-919" y2="-913"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4397" x2="4397" y1="-918" y2="-913"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4391" x2="4366" y1="-915" y2="-915"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4355" x2="4364" y1="-920" y2="-910"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4354" x2="4364" y1="-909" y2="-919"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4352" x2="4330" y1="-913" y2="-913"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4329" x2="4329" y1="-916" y2="-909"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4325" x2="4325" y1="-916" y2="-909"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4325" x2="4305" y1="-912" y2="-912"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4153" x2="4153" y1="-919" y2="-910"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4156" x2="4156" y1="-917" y2="-911"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4159" x2="4159" y1="-916" y2="-911"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4153" x2="4128" y1="-913" y2="-913"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4117" x2="4126" y1="-918" y2="-908"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4116" x2="4126" y1="-907" y2="-917"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4114" x2="4092" y1="-911" y2="-911"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4091" x2="4091" y1="-914" y2="-907"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4087" x2="4087" y1="-914" y2="-907"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4087" x2="4067" y1="-910" y2="-910"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3942" x2="3942" y1="-918" y2="-909"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3945" x2="3945" y1="-916" y2="-910"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3948" x2="3948" y1="-915" y2="-910"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3942" x2="3917" y1="-912" y2="-912"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3906" x2="3915" y1="-917" y2="-907"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3905" x2="3915" y1="-906" y2="-916"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3903" x2="3881" y1="-910" y2="-910"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3880" x2="3880" y1="-913" y2="-906"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3876" x2="3876" y1="-913" y2="-906"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3876" x2="3856" y1="-909" y2="-909"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3874" x2="3892" y1="-585" y2="-574"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3891" x2="3874" y1="-587" y2="-577"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3875" x2="3890" y1="-555" y2="-540"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3893" x2="3878" y1="-555" y2="-540"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3870" x2="3892" y1="-433" y2="-420"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3891" x2="3871" y1="-431" y2="-419"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3874" x2="3887" y1="-379" y2="-371"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3893" x2="3877" y1="-384" y2="-368"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="AC-CX_JLD.CX_JLD_4ⅠM">
    <g class="BV-38KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3726,-288 4475,-288 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10972" ObjectName="BS-CX_JLD.CX_JLD_4ⅠM"/>
    <cge:TPSR_Ref TObjectID="10972"/></metadata>
   <polyline fill="none" opacity="0" points="3726,-288 4475,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_JLD.CX_JLD_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3677,-783 4918,-783 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10971" ObjectName="BS-CX_JLD.CX_JLD_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="10971"/></metadata>
   <polyline fill="none" opacity="0" points="3677,-783 4918,-783 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_JLD.CX_JLD_4ⅠM">
    <g class="BV-38KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4667,-337 4897,-337 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10972" ObjectName="BS-CX_JLD.CX_JLD_4ⅠM"/>
    <cge:TPSR_Ref TObjectID="10972"/></metadata>
   <polyline fill="none" opacity="0" points="4667,-337 4897,-337 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_284c070">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4066.000000 -137.000000)" xlink:href="#lightningRod:shape101"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2878230">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.866667 3909.000000 -923.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28797f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.866667 4358.000000 -925.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_287a480">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.866667 4614.000000 -930.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_287b170">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.866667 4837.000000 -923.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_287d0e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.866667 3912.000000 -1097.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_287ddd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.866667 4123.000000 -1096.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_287eac0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.866667 4361.000000 -1101.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_287f7b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.866667 4840.000000 -1098.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28804a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.866667 4617.000000 -1099.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28898e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4775.000000 -647.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_288a5d0">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4069.000000 -214.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_288f3f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4779.000000 -975.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_288fe90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4556.000000 -980.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2890be0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4779.000000 -452.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2891930">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4062.000000 -977.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2892680">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4300.000000 -974.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28933d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3851.000000 -975.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_298d0b0">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -91.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_298ddb0">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3974.000000 -87.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_298e630">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3971.000000 -29.000000)" xlink:href="#lightningRod:shape159"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_298f910">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4361.000000 -63.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29903f0">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4423.000000 -82.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2990c70">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4420.000000 -24.000000)" xlink:href="#lightningRod:shape159"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2991e10">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3909.000000 -11.000000)" xlink:href="#lightningRod:shape158"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2993490">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4358.000000 17.000000)" xlink:href="#lightningRod:shape158"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2994b10">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3827.000000 -14.000000)" xlink:href="#lightningRod:shape160"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2995bd0">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4276.000000 -8.000000)" xlink:href="#lightningRod:shape160"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2996c90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4277.000000 -569.000000)" xlink:href="#lightningRod:shape161"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_299ad90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -924.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_299bb00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3935.000000 -636.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_299ce40">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.000000 -203.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_299dbb0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3848.000000 42.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_299e920">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4297.000000 49.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29a6150">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3784.000000 -306.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29a8e30">
    <use class="BV-38KV" transform="matrix(0.722222 -0.000000 0.000000 -0.642857 3708.000000 -343.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42675" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3938.000000 -1050.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42675" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7100"/>
     <cge:Term_Ref ObjectID="10311"/>
    <cge:TPSR_Ref TObjectID="7100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42676" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3938.000000 -1050.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42676" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7100"/>
     <cge:Term_Ref ObjectID="10311"/>
    <cge:TPSR_Ref TObjectID="7100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42672" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3938.000000 -1050.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42672" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7100"/>
     <cge:Term_Ref ObjectID="10311"/>
    <cge:TPSR_Ref TObjectID="7100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-42674" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3938.000000 -1050.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42674" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7100"/>
     <cge:Term_Ref ObjectID="10311"/>
    <cge:TPSR_Ref TObjectID="7100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42680" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4089.000000 -1051.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42680" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7104"/>
     <cge:Term_Ref ObjectID="10319"/>
    <cge:TPSR_Ref TObjectID="7104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42681" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4089.000000 -1051.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42681" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7104"/>
     <cge:Term_Ref ObjectID="10319"/>
    <cge:TPSR_Ref TObjectID="7104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42677" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4089.000000 -1051.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42677" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7104"/>
     <cge:Term_Ref ObjectID="10319"/>
    <cge:TPSR_Ref TObjectID="7104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-42679" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4089.000000 -1051.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42679" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7104"/>
     <cge:Term_Ref ObjectID="10319"/>
    <cge:TPSR_Ref TObjectID="7104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42694" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4326.000000 -1049.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42694" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7116"/>
     <cge:Term_Ref ObjectID="10343"/>
    <cge:TPSR_Ref TObjectID="7116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42695" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4326.000000 -1049.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42695" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7116"/>
     <cge:Term_Ref ObjectID="10343"/>
    <cge:TPSR_Ref TObjectID="7116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42692" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4326.000000 -1049.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42692" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7116"/>
     <cge:Term_Ref ObjectID="10343"/>
    <cge:TPSR_Ref TObjectID="7116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-42696" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4326.000000 -1049.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42696" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7116"/>
     <cge:Term_Ref ObjectID="10343"/>
    <cge:TPSR_Ref TObjectID="7116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42685" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4585.000000 -1052.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42685" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7108"/>
     <cge:Term_Ref ObjectID="10327"/>
    <cge:TPSR_Ref TObjectID="7108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42686" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4585.000000 -1052.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42686" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7108"/>
     <cge:Term_Ref ObjectID="10327"/>
    <cge:TPSR_Ref TObjectID="7108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42682" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4585.000000 -1052.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42682" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7108"/>
     <cge:Term_Ref ObjectID="10327"/>
    <cge:TPSR_Ref TObjectID="7108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-42684" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4585.000000 -1052.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42684" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7108"/>
     <cge:Term_Ref ObjectID="10327"/>
    <cge:TPSR_Ref TObjectID="7108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42690" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4808.000000 -1049.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42690" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7112"/>
     <cge:Term_Ref ObjectID="10335"/>
    <cge:TPSR_Ref TObjectID="7112"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42691" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4808.000000 -1049.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42691" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7112"/>
     <cge:Term_Ref ObjectID="10335"/>
    <cge:TPSR_Ref TObjectID="7112"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42687" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4808.000000 -1049.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42687" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7112"/>
     <cge:Term_Ref ObjectID="10335"/>
    <cge:TPSR_Ref TObjectID="7112"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-42689" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4808.000000 -1049.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42689" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7112"/>
     <cge:Term_Ref ObjectID="10335"/>
    <cge:TPSR_Ref TObjectID="7112"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42704" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3793.000000 -703.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42704" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7121"/>
     <cge:Term_Ref ObjectID="10353"/>
    <cge:TPSR_Ref TObjectID="7121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42705" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3793.000000 -703.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42705" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7121"/>
     <cge:Term_Ref ObjectID="10353"/>
    <cge:TPSR_Ref TObjectID="7121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42701" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3793.000000 -703.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42701" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7121"/>
     <cge:Term_Ref ObjectID="10353"/>
    <cge:TPSR_Ref TObjectID="7121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-42703" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3793.000000 -703.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42703" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7121"/>
     <cge:Term_Ref ObjectID="10353"/>
    <cge:TPSR_Ref TObjectID="7121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-58369" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4424.000000 -251.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58369" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10873"/>
     <cge:Term_Ref ObjectID="15162"/>
    <cge:TPSR_Ref TObjectID="10873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-58370" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4424.000000 -251.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58370" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10873"/>
     <cge:Term_Ref ObjectID="15162"/>
    <cge:TPSR_Ref TObjectID="10873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-58367" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4424.000000 -251.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58367" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10873"/>
     <cge:Term_Ref ObjectID="15162"/>
    <cge:TPSR_Ref TObjectID="10873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-58368" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4424.000000 -251.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58368" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10873"/>
     <cge:Term_Ref ObjectID="15162"/>
    <cge:TPSR_Ref TObjectID="10873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-42699" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3975.000000 -246.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42699" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10872"/>
     <cge:Term_Ref ObjectID="15160"/>
    <cge:TPSR_Ref TObjectID="10872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-42700" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3975.000000 -246.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42700" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10872"/>
     <cge:Term_Ref ObjectID="15160"/>
    <cge:TPSR_Ref TObjectID="10872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-42697" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3975.000000 -246.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42697" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10872"/>
     <cge:Term_Ref ObjectID="15160"/>
    <cge:TPSR_Ref TObjectID="10872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-42698" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3975.000000 -246.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42698" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10872"/>
     <cge:Term_Ref ObjectID="15160"/>
    <cge:TPSR_Ref TObjectID="10872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-42706" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3748.000000 -769.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="42706" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10971"/>
     <cge:Term_Ref ObjectID="15293"/>
    <cge:TPSR_Ref TObjectID="10971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-58374" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4692.000000 -705.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58374" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7125"/>
     <cge:Term_Ref ObjectID="10361"/>
    <cge:TPSR_Ref TObjectID="7125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-58375" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4692.000000 -705.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58375" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7125"/>
     <cge:Term_Ref ObjectID="10361"/>
    <cge:TPSR_Ref TObjectID="7125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-58372" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4692.000000 -705.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58372" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7125"/>
     <cge:Term_Ref ObjectID="10361"/>
    <cge:TPSR_Ref TObjectID="7125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-58373" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4692.000000 -705.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58373" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7125"/>
     <cge:Term_Ref ObjectID="10361"/>
    <cge:TPSR_Ref TObjectID="7125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-58376" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4692.000000 -705.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58376" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7125"/>
     <cge:Term_Ref ObjectID="10361"/>
    <cge:TPSR_Ref TObjectID="7125"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图10.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3238" y="-1175"/></g>
   <g href="cx_配调_配网接线图10.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3190" y="-1192"/></g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="4839" cy="-911" fill="none" r="7" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4615" cy="-916" fill="none" r="7" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4359" cy="-914" fill="none" r="7" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4121" cy="-912" fill="none" r="7" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3910" cy="-911" fill="none" r="7" stroke="rgb(0,255,0)" stroke-width="1"/>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_JLD.CX_JLD_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="10371"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3857.000000 -440.000000)" xlink:href="#transformer2:shape15_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3857.000000 -440.000000)" xlink:href="#transformer2:shape15_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="7129" ObjectName="TF-CX_JLD.CX_JLD_1T"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4759.000000 -543.000000)" xlink:href="#transformer2:shape15_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4759.000000 -543.000000)" xlink:href="#transformer2:shape15_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-38KV" transform="matrix(0.884615 -0.000000 0.000000 -0.788462 3703.000000 -302.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.884615 -0.000000 0.000000 -0.788462 3703.000000 -302.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259" style="fill-opacity:0">
    <a>
     
     <rect height="41" qtmmishow="hidden" width="139" x="3238" y="-1175"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3238" y="-1175"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124" style="fill-opacity:0">
    <a>
     
     <rect height="69" qtmmishow="hidden" width="77" x="3190" y="-1192"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3190" y="-1192"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-42794">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3874.000000 -655.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7121" ObjectName="SW-CX_JLD.CX_JLD_001BK"/>
     <cge:Meas_Ref ObjectId="42794"/>
    <cge:TPSR_Ref TObjectID="7121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42709">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3847.000000 -864.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7100" ObjectName="SW-CX_JLD.CX_JLD_081BK"/>
     <cge:Meas_Ref ObjectId="42709"/>
    <cge:TPSR_Ref TObjectID="7100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58143">
    <use class="BV-38KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3845.000000 -179.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10872" ObjectName="SW-CX_JLD.CX_JLD_411BK"/>
     <cge:Meas_Ref ObjectId="58143"/>
    <cge:TPSR_Ref TObjectID="10872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42727">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4058.000000 -866.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7104" ObjectName="SW-CX_JLD.CX_JLD_082BK"/>
     <cge:Meas_Ref ObjectId="42727"/>
    <cge:TPSR_Ref TObjectID="7104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42772">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4296.000000 -869.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7116" ObjectName="SW-CX_JLD.CX_JLD_083BK"/>
     <cge:Meas_Ref ObjectId="42772"/>
    <cge:TPSR_Ref TObjectID="7116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42741">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4552.000000 -870.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7108" ObjectName="SW-CX_JLD.CX_JLD_084BK"/>
     <cge:Meas_Ref ObjectId="42741"/>
    <cge:TPSR_Ref TObjectID="7108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-42756">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4775.000000 -865.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7112" ObjectName="SW-CX_JLD.CX_JLD_085BK"/>
     <cge:Meas_Ref ObjectId="42756"/>
    <cge:TPSR_Ref TObjectID="7112"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58151">
    <use class="BV-38KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -178.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10873" ObjectName="SW-CX_JLD.CX_JLD_412BK"/>
     <cge:Meas_Ref ObjectId="58151"/>
    <cge:TPSR_Ref TObjectID="10873"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3226.500000 -1116.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4"/>
</svg>